<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>3DText文本使用示例</title>

    <link
      rel="stylesheet"
      href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <link
    rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/base16/dracula.min.css"
  />
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/go.min.js"></script>
    <script src="./index.js" type="module"></script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        right: 50%;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }
      .description {
        position: absolute;
        top: 20px;
        right: 20px;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv"></div>
    <div class="tools">
      <button onclick="loadOnClick()">加载</button>
      <button onclick="removeOnClick()">移除</button>
      <button onclick="removeItemClick()">移除指定id</button>
    </div>
    <div class="description">
      使用：
      <p>
        添加： const textLayer=load3DTextLayer({ view, data, fontSize = 20,
        fontColor = "white" })
      </p>
      <p>移除：view.map.remove(textLayer)</p>
      移除指定id的文本  
      <pre><code class="language-javascript">
        // textLayer:为创建的3d文本图层
        // 后面数组为需要移除的id数组
        ArcGisUtils.remove3DTextById(textLayer, [1, 2, 3, 4, 5]);
      </code></pre>
    </div>
  </body>

  <script>
    let textLayer;
    function loadOnClick() {
      textLayer = ArcGisUtils.load3DTextLayer({
        view,
        data: [
          {
            pos: [119.94315399169922, 29.5630503845215, 11000],
            text: "浦江县",
            id: 1,
          },
          {
            pos: [119.46214447021484, 29.31345558166504, 11000],
            text: "兰溪市",
            id: 2,
          },
          {
            pos: [119.5569204711914, 29.00677101135254, 11000],
            text: "婺城区",
            id: 3,
          },
          {
            pos: [119.8483056640625, 29.18855995178222711],
            text: "金东区",
            id: 4,
          },
          {
            pos: [120.08206787109375, 29.322123641967773, 11000],
            text: "义乌市",
            id: 5,
          },
          {
            pos: [119.7269204711914, 28.79677101135254, 11000],
            text: "武义县",
            id: 6,
          },
          {
            pos: [120.1469204711914, 28.97677101135254, 11000],
            text: "永康市",
            id: 7,
          },
          {
            pos: [120.4169204711914, 29.24677101135254, 11000],
            text: "东阳市",
            id: 8,
          },
          {
            pos: [120.6299204711914, 29.06677101135254, 11000],
            text: "磐安县",
            id: 9,
          },
        ],
        fontSize: 30,
        fontColor: "red",
      });
    }
    function removeOnClick() {
      view.map.remove(textLayer);
    }

    function removeItemClick() {
      ArcGisUtils.remove3DTextById(textLayer, [1, 2, 3, 4, 5]);
    }
  </script>
    <script>
      hljs.highlightAll();
    </script>
</html>
