<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>年度计划数弹窗</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <link rel="stylesheet" href="/static/css/viewCss/commonObjzhdd.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/Vue/vue-count-to.min.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/static/js/jslib/Emiter.js"></script>
    <script src="/elementui/js/index.js"></script>
    <style>

        .search-box {
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }

        ::-webkit-scrollbar {
             display: none;
         }


      ul,
      ul li {
        list-style: none;
      }

      .rwgz-tc {
        width: 1515px;
        height: 866px;
        background: url("/static/images/zhdd/dialogBg.png") no-repeat;
        background-size: 100% 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: column;
      }

      .rw-title {
        padding: 46px 3% 0;
        width: 95%;
        height: 60px;
        line-height: 60px;
      }

      .close {
        background: url("/static/images/zhdd/close.png") no-repeat;
        width: 34px;
        height: 34px;
      }

      .ql-indent-1 img {
        width: 100%;
      }

      img {
        width: 100%;
      }

      .content {
          height: 910px;
          margin-top: 10px;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
          flex-direction: column;
      }

      ::-webkit-scrollbar {
          width: 0;
          height: 0;
      }

      .scrollTab {
          width:928px;
          height:70px;
      }

      .table-container {
          height: 560px;
          overflow-y: scroll;
      }

        .tableContainer2 {
            height: 400px;
            overflow-y: scroll;
        }

      .table-line {
          width: 1429px;
          height: 80px;
          display: flex;
          justify-content: space-evenly;
          align-items: center;
          background: rgba(50,134,248,0.15);
      }

      .title-line {
          background: transparent !important;
      }

      .table-title {
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: bold;
          color: #FFFFFF;
          text-align: left;
      }

      .table-column {
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #FFFFFF;
          overflow: hidden;
          text-overflow: ellipsis;
      }
      .activeTableLine {
          background: rgba(50,134,248,0.25);
      }

        .el-input {
            /*position: absolute;*/
            /*top: 229px;*/
            /*left: 1630px;*/
            width: 537px;
            height: 72px;
        }

        .el-input__icon {
            height: 100%;
            width: 25px;
            text-align: center;
            -webkit-transition: all .3s;
            transition: all .3s;
            line-height: 75px;
        }

        .el-scrollbar {
            overflow: hidden;
            /*position: relative;*/
            height: 500px;
            background: #020b28;
            border-radius: 10px;
        }

        .el-autocomplete-suggestion__wrap {
            max-height: unset !important;
            padding: 10px 0;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
        }

        .el-input--suffix .el-input__inner {
            border: 1px solid #359CF8;
            border-radius: 8px;
            padding-right: 30px;
            height: 70px;
            font-family: MicrosoftYaHei;
            font-size: 28px;
            font-weight: normal;
            font-stretch: normal;
            letter-spacing: 1px;
            color: #bbe5fd !important;
            background: #020b28;
        }

        .el-input.is-active .el-input__inner,
        .el-input__inner:focus {
            border: 1px solid #bbe5fd;
            outline: 0;
        }

        .el-input__suffix-inner {
            pointer-events: all;
            font-size: 28px;
            margin: 15px 20px 0 0;
            color: #bbe5fd !important;
        }

        .el-autocomplete-suggestion li {
            padding: 0 20px;
            line-height: 34px;
            cursor: pointer;
            color: #bbe5fd;
            font-size: 28px;
            list-style: none;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin: 10px 0 25px 0;
        }

        .el-autocomplete-suggestion li:hover {
            background: unset !important;
        }

        .search {
            width: 100px;
            height: 65px;
            line-height: 65px;
            text-align: center;
            background: #0A619E;
            border: 1px solid #359CF8;
            border-radius: 8px;
            font-size: 28px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FEFEFE;
            margin-left: 10px;
            cursor: pointer;
        }

        .tab-con-active {
            background: url('/static/images/zfts/tab-active.png') no-repeat;
            background-size: 110% 100%;
        }

        .toolBar {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .tab-con {
            width: 351px;
            height: 70px;
            line-height: 70px;
            color: #fff;
            font-size: 32px;
        }

        .s-flex {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-evenly;
        }

        .tab-item {
            font-size: 36px;
            font-family: Source Han Sans CN;
            font-weight: 500;
            font-style: italic;
            color: rgba(171, 206, 239, 0.7);
            line-height: 59px;
        }

        .tabConActive {
            background: url('/static/images/zfts/tab-active.png') no-repeat;
            background-size: 110% 100%;
            font-family: Source Han Sans CN;
            font-weight: bold;
            font-size: 36px;
            color: #ffffff;
        }
    </style>
  </head>

  <body>
    <div id="ndjhs" class="rwgz-tc">
      <div class="rw-title flex-between">
        <div class="fs-44 text-mid-yellow" id="rwTitle" style="margin-left: 20px;">{{ dialogTitle }}</div>
        <div class="close cursor" @click="close" style="margin-right: 20px;"></div>
      </div>
      <div class="content">
        <div class="toolBar">
          <div class="tab-con s-flex" v-show="city == '金华市'">
            <span
              class="tab-item"
              v-for="(item,i) in btnList"
              :class="{tabConActive:btnActive==i}"
              @click="changeTab(i)"
            >{{item.name}}
            </span>
          </div>
          <div class="search-box">
            <el-autocomplete class="inline-input" suffix-icon="el-icon-search" v-model="searchInput"
                             :fetch-suggestions="querySearch" placeholder="请输入关键词" value-key="name" :trigger-on-focus="false"
                             @select="handleSelect" @change="handleChange"></el-autocomplete>
            <div class="search" @click="handleChange">搜索</div>
          </div>
        </div>
        <div class="table" v-show="dialogTitle == '未完成计划数'">
          <div class="table-line title-line">
            <div class="table-column table-title" style="flex: 1;margin-left: 30px">序号</div>
            <div class="table-column table-title" style="flex: 3" v-show="btnActive == 0">部门</div>
            <div class="table-column table-title" style="flex: 3" v-show="btnActive != 0">县市区</div>
            <div class="table-column table-title" style="flex: 2">合计</div>
            <div class="table-column table-title" style="flex: 2">在实施计划数</div>
            <div class="table-column table-title" style="flex: 2">待实施计划数</div>
          </div>
          <div class="table-container">
            <div class="table-line">
              <div class="table-column" style="flex: 1"></div>
              <div class="table-column" style="flex: 3;margin-left: 30px">合计</div>
              <div class="table-column" style="flex: 2">{{wwcjhszs.hjzs}}</div>
              <div class="table-column" style="flex: 2">{{wwcjhszs.zsszs}}</div>
              <div class="table-column" style="flex: 2">{{wwcjhszs.dsszs}}</div>
            </div>
            <div class="table-line" v-for="(item,i) in tableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
              <div class="table-column" style="flex: 1;margin-left: 30px">{{i + 1}}</div>
              <div class="table-column" style="flex: 3" :title="item.name">{{item.name}}</div>
              <div class="table-column" style="flex: 2">{{item.wwchj}}</div>
              <div class="table-column" style="flex: 2">{{item.zss}}</div>
              <div class="table-column" style="flex: 2">{{item.dss}}</div>
            </div>
          </div>
        </div>

        <div class="table" v-show="dialogTitle == '年度计划数'">
          <div class="table-line title-line">
            <div class="table-column table-title" style="flex: 1;margin-left: 30px">序号</div>
            <div class="table-column table-title" style="flex: 3" v-show="btnActive == 0">部门</div>
            <div class="table-column table-title" style="flex: 3" v-show="btnActive != 0">县市区</div>
            <div class="table-column table-title" style="flex: 2">年度计划数</div>
            <div class="table-column table-title" style="flex: 2">已完成计划数</div>
            <div class="table-column table-title" style="flex: 2">在实施计划数</div>
            <div class="table-column table-title" style="flex: 2">待实施计划数</div>
          </div>
          <div class="table-container">
            <div class="table-line">
              <div class="table-column" style="flex: 1;margin-left: 30px"></div>
              <div class="table-column" style="flex: 3">合计</div>
              <div class="table-column" style="flex: 2">{{ndjhszs.ndjhszs}}</div>
              <div class="table-column" style="flex: 2">{{ndjhszs.ywcjhszs}}</div>
              <div class="table-column" style="flex: 2">{{ndjhszs.zssjhszs}}</div>
              <div class="table-column" style="flex: 2">{{ndjhszs.dssjhszs}}</div>
            </div>
            <div class="table-line" v-for="(item,i) in tableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
              <div class="table-column" style="flex: 1;margin-left: 30px">{{i + 1}}</div>
              <div class="table-column" style="flex: 3" :title="item.name">{{item.name}}</div>
                            <div class="table-column" style="flex: 2">{{item.value}}</div>
                            <div class="table-column" style="flex: 2">{{item.ywc}}</div>
              <div class="table-column" style="flex: 2">{{item.zss}}</div>
              <div class="table-column" style="flex: 2">{{item.dss}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      let vm = new Vue({
        el: "#ndjhs",
        data: {
          dialogTitle:'',
          searchInput: '',
          tableData: [],
          departments: [
            {
              depart:"综合行政执法局"
            },
            {
              depart:"市场监管局"
            },
            {
              depart:"生态环境局"
            },
            {
              depart:"交通运输局"
            },
            {
              depart:"文广旅游局"
            },
            {
              depart:"农业农村局"
            },
            {
              depart:"应急管理局"
            },
            {
              depart:"卫健委"
            },
            {
              depart:"资规局"
            }
          ],
          btnActive: 0,
          btnList: [{name:"市直部门",value:"市直部门"}, {name:"县市区",value:"地区"}],
          wwcjhszs: {
            hjzs: 0,
            zsszs: 0,
            dsszs: 0
          },
          ndjhszs: {
            ndjhszs: 0,
            ywcjhszs: 0,
            zssjhszs:0,
            dssjhszs:0
          }
        },
        computed:{
          city() {
            return localStorage.getItem("city")
          }
        },
        mounted() {
          const that = this;
          window.addEventListener("message", function (event) {
            //子获取父消息
            let newData;
            if (typeof event.data == "object") {
              newData = event.data;
            } else {
              newData = JSON.parse(event.data.argument);
            }
            that.dialogTitle = newData.type;
          });

          window.parent.eventbus &&
          window.parent.eventbus.on("yearChange", (year) => {
            this.getDetail(this.searchInput,year);
          });
          this.getDetail(this.searchInput,localStorage.getItem("year"));
        },
        methods: {
          changeTab(i) {
            this.btnActive = i;
            this.getDetail(this.searchInput,localStorage.getItem("year"))
          },
          //获取数据
          getDetail(depart,year) {
            let params = {qxwd:this.city,ywwd1:depart,ywwd2:this.city == '金华市'?this.btnList[this.btnActive].value:"",ywwd3:"",sjwd2: year}
            $api("/csdn_yjyp56",params).then((res) => {
              console.log(this.ArrReduce("ywwd1", res),"csdn_yjyp56");
              // this.tableData = res.map(item => {return {
              //   name:item.ywwd1,
              //   value:item.tjz,
              //   ywc:item.ywwd3 == '已完成'?item.tjz:"暂无数据",
              //   zss:item.ywwd3 == '执行中'?item.tjz:"暂无数据",
              //   dss:item.ywwd3 == '待执行'?item.tjz:"暂无数据"
              // }})
              this.tableData = Object.values(this.ArrReduce("ywwd1",res)).map(item => {return {
                name:item[0].ywwd1,
                // value:item.find(item2 => item2.ywwd3 == "").tjz?item.find(item2 => item2.ywwd3 == "").tjz:"暂无数据",
                // ywc:item.find(item2 => item2.ywwd3 == "已完成").tjz?item.find(item2 => item2.ywwd3 == "已完成").tjz:"暂无数据",
                // zss:item.find(item2 => item2.ywwd3 == "执行中").tjz?item.find(item2 => item2.ywwd3 == "执行中").tjz:"暂无数据",
                // dss:item.find(item2 => item2.ywwd3 == "待执行").tjz?item.find(item2 => item2.ywwd3 == "待执行").tjz:"暂无数据"
                value:item.find(item2 => item2.ywwd3 == "")?item.find(item2 => item2.ywwd3 == "").tjz:"0",
                ywc:item.find(item2 => item2.ywwd3 == "已完成")?item.find(item2 => item2.ywwd3 == "已完成").tjz:"0",
                zss:item.find(item2 => item2.ywwd3 == "执行中")?item.find(item2 => item2.ywwd3 == "执行中").tjz:"0",
                dss:item.find(item2 => item2.ywwd3 == "待执行")?item.find(item2 => item2.ywwd3 == "待执行").tjz:"0"
              }})

              this.tableData.forEach((item,i) => {
                item.wwchj = Number(item.zss) + Number(item.dss)
              })

              this.wwcjhszs = {
                hjzs: 0,
                zsszs: 0,
                dsszs: 0
              };
              this.ndjhszs = {
                ndjhszs: 0,
                ywcjhszs: 0,
                zssjhszs:0,
                dssjhszs:0
              };

              console.log(this.tableData,"tpy");

              this.tableData.forEach((item,i) => {
                this.wwcjhszs.hjzs += Number(item.wwchj)
                this.wwcjhszs.zsszs += Number(item.zss)
                this.wwcjhszs.dsszs += Number(item.dss)

                this.ndjhszs.ndjhszs += Number(item.value)
                this.ndjhszs.ywcjhszs += Number(item.ywc)
                this.ndjhszs.zssjhszs += Number(item.zss)
                this.ndjhszs.dssjhszs += Number(item.dss)
              });
            });
          },
          //数组根据字段分类
          ArrReduce(str,arr) {
            let result = arr.reduce((a, b) => {
              if (a[b[str]]) {
                //根据old分类
                a[b[str]].push(b);
              } else {
                a[b[str]] = [b];
              }
              return a;
            }, {});
            return result;
          },
          close() {
            window.parent.lay.closeIframeByNames(["planDialog"]);
          },
          querySearch(queryString, cb) {
            var restaurants = this.tableData
            var results = queryString
              ? restaurants.filter(this.createFilter(queryString.replace(/\s*/g, '')))
              : restaurants
            // 调用 callback 返回建议列表的数据
            cb(results)
          },
          createFilter(queryString) {
            return (restaurant) => {
              return restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) !== -1
            }
          },
          handleSelect(item) {
            this.getDetail(item.name,localStorage.getItem("year"))
          },
          handleChange() {
            this.getDetail(this.searchInput)
          }
        },
      });
    </script>
  </body>
</html>
