<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>轨迹图层</title>

    <link
      rel="stylesheet"
      href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/base16/dracula.min.css"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/go.min.js"></script>
    <script src="./libs/three-r116.min.js"></script>
    <script src="./index.js" type="module"></script>
    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        left: 50%;
        width: 50%;
        height: 200px;
        display: flex;
      }

      .tools span {
        cursor: pointer;
        background-color: blue;
        width: 150px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        color: white;
      }

      #lineOfSight {
        width: 200px;
        height: 200px;
        position: absolute;
        bottom: 10px;
        right: 10px;
        z-index: 1;
      }

      .description {
        position: absolute;
        right: 10px;
        top: 10px;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv"></div>
    <div class="tools">
      <span onclick="add()">添加</span>
      <span onclick="remove()">移除</span>
    </div>
    <div class="description">
      添加：
      <pre><code class="language-javascript">
        const layer = ArcGisUtils.addRouteLayer({
          view,
          data: [
            [119.62630225735575, 29.074167143098993],
            [119.63410300212077, 29.07332444666024],
            [119.64194493333977, 29.073745573191637],
            [119.64264383051217, 29.070167910523008],
            [119.64274041821373, 29.066721267893424],
            [119.64314526703286, 29.062232490334598],
            [119.6369377961143, 29.061362026456308],
            [119.62747368868112, 29.05965010101974],
            [119.62670673024888, 29.066001421348204],
            [119.62630225735575, 29.074167143098993],
          ],
          isLock: true, // 是否锁定视角，默认false
          speed: 100, // 速度：单位,km/h，默认40
        });
      </code></pre>
      移除：
      <pre><code class="language-javascript">
        ArcGisUtils.removeRouteLayer(view);
      </code></pre>
    </div>
    <div id="lineOfSight"></div>
  </body>
  <script>
    let lineOfSightWidget;
    function add() {
      const layer = ArcGisUtils.addRouteLayer({
        view,
        data: [
          [119.62630225735575, 29.074167143098993],
          [119.63410300212077, 29.07332444666024],
          [119.64194493333977, 29.073745573191637],
          [119.64264383051217, 29.070167910523008],
          [119.64274041821373, 29.066721267893424],
          [119.64314526703286, 29.062232490334598],
          [119.6369377961143, 29.061362026456308],
          [119.62747368868112, 29.05965010101974],
          [119.62670673024888, 29.066001421348204],
          [119.62630225735575, 29.074167143098993],
        ],
        isLock: true, // 是否锁定视角，默认false
        speed: 100, // 速度：单位,km/h，默认40
      });
    }
    function remove() {
      ArcGisUtils.removeRouteLayer(view);
    }
  </script>
  <script>
    hljs.highlightAll();
  </script>
</html>
