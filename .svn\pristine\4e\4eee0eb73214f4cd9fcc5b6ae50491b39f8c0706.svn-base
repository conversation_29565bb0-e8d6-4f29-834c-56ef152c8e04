<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>指挥调度弹窗</title>
    <link rel="shortcut icon" href="#" />
    <script src="/Vue/vue.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <link rel="stylesheet" href="/elementui/css/elementui.css" />
    <script src="/elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="https://g.alicdn.com/gdt/jsapi/1.9.22/index.js"></script>
    <script src="/static/js/jslib/s.min.vue.js"></script>
    <link href="/static/js/layui/css/layui.css" rel="stylesheet" />
    <link href="/static/js/layui/css/formSelects-v4.css" rel="stylesheet" />
    <link href="/static/css/viewCss/commonObjzhdd.css" rel="stylesheet" />
    <script src="/static/js/jslib/turf.min.js"></script>
    <script src="/static/js/layui/layui.js"></script>
    <style type="text/css" scoped>
      [v-cloak] {
        display: none;
      }

      body {
        margin: 0;
        padding: 0;
      }

      #zhdddialog {
        width: 730px;
        height: 1350px;
        position: relative;
      }

      .close {
        width: 46px;
        height: 78px;
        font-size: 60px;
        color: #fff;
        font-weight: 600;
        cursor: pointer;
      }
      .content {
        position: absolute;
        top: 75px;
        left: 30px;
        width: 670px;
      }
      .icon-cir {
        background: url("/static/images/zhdd/icon_cir.png") no-repeat;
        width: 19px;
        height: 19px;
        margin-right: 8px;
      }
      .layui-input,
      .layui-select,
      .layui-textarea {
        height: 60px;
        line-height: 60px;
        background-color: #1c4d65;
        box-shadow: inset 0 0 12px 0 #4ea4d5;
        border-radius: 6px;
        box-sizing: border-box;
        border: none;
        color: #66c9f4;
      }
      .zhdd-item .layui-form-label {
        font-size: 26px;
      }

      .tl-item {
        width: 50px;
        height: 50px;
        margin: 16% auto;
        padding: 10px;
        box-sizing: border-box;
        border-radius: 10px;
        background-color: #00396f;
      }
      .tl-item-fw {
        margin: 0 0 0 10px;
        cursor: pointer;
      }
      .tl-box-poly {
        background: url("/static/images/zhdd/icon_polygon.png") no-repeat;
        width: 42px;
        height: 42px;
        background-size: 100% 100%;
        margin-left: -6px;
        margin-top: -6px;
      }
      .liActive .icon-tick {
        background-position-x: -26px;
      }
      .icon-tick {
        background: url("/static/images/zhdd/icon_tick.png") no-repeat;
        width: 26px;
        height: 26px;
        /* margin-right: 15px; */
        margin-left: 15px;
      }

      .icon-add {
        background: url("/static/images/zhdd/addGroup.png") no-repeat;
        background-size: 100% 100%;
        width: 158px;
        height: 46px;
        line-height: 46px;
        text-align: center;
        margin: 20px 0;
      }
      .zhdd-submit {
        margin: 120px auto 0;
        width: 240px;
        height: 60px;
        line-height: 60px;
        cursor: pointer;
      }
      /*elementui-tree*/
      .el-tree {
        background: unset;
      }
      .el-tree-node__content:hover {
        background-color: #1c4d65;
      }
      .el-tree-node__label {
        font-size: 30px;
      }
      .el-tree-node.is-current > .el-tree-node__content {
        background-color: #1c4d65 !important;
      }
      .el-tree-node:focus > .el-tree-node__content {
        background-color: #4a9de7 !important;
        color: #fff !important;
      }
      /*节点失焦时的背景颜色*/
      .el-tree--highlight-current
        .el-tree-node.is-current
        > .el-tree-node__content {
        background-color: #4a9de7 !important;
        /* color: #fff !important; */
      }
      .el-tree-node__content {
        height: 40px;
        color: #fff;
      }
      .el-checkbox__inner {
        width: 27px;
        height: 27px;
      }
      .el-checkbox__input.is-checked .el-checkbox__inner,
      .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        color: #ffc561;
        font-size: 24px;
        line-height: 27px;
        font-weight: 600;
        text-align: center;
        background-color: #252316;
        text-align: center;
        border: 1px solid #ffc561;
      }
      .el-checkbox__inner::after {
        -webkit-box-sizing: content-box;
        box-sizing: content-box;
        content: "";
        border: 1px solid #fff;
        border-left: 0;
        border-top: 0;
        height: 16px;
        left: 7px;
        width: 7px !important;
        position: absolute;
        top: 0px;
      }
      .el-tree > .el-tree-node > .el-tree-node__content .el-checkbox {
        display: none;
      }
      .setstyle {
        min-height: 200px;
        padding: 0 !important;
        margin: 0;
        overflow: auto;
        cursor: default !important;
      }
      .el-select-dropdown {
        background-color: rgba(2, 16, 30, 0.8) !important;
      }
      .el-select-dropdown__item.hover,
      .el-select-dropdown__item:hover {
        background-color: unset;
      }
      .el-input__inner {
        background-color: unset !important;
      }
      .el-select {
        width: 98%;
      }
      .el-input__inner {
        background-color: #1c4d65;
        color: #7ed8ff;
        height: 57px;
        border-radius: 8px;
        box-shadow: inset 0 0 40px 0 #2b85a9;
        border: unset;
        padding: 10px;
        font-size: 32px;
        padding-left: 20px;
      }
      .el-tag.el-tag--info {
        background-color: unset;
        border-color: #e9e9eb;
        color: #fff;
        font-size: 25px;
        height: 39px;
        line-height: 39px;
      }
      .el-select .el-tag__close.el-icon-close {
        background-color: unset;
        top: -3px;
        color: #fff;
      }
      .el-select .el-tag__close.el-icon-close::before {
        font-size: 25px;
        line-height: 26px;
      }
      .el-select .el-tag__close.el-icon-close {
        width: 25px;
        height: 25px;
      }
      .el-select .el-input .el-select__caret {
        font-size: 28px;
      }
      .el-input__icon {
        width: 32px !important;
      }
      .el-button {
        font-size: 22px;
        width: 100px;
        height: 40px;
        padding: 0;
        line-height: 40px;
      }
      .button-xz {
        left: 335px;
        top: 3px;
        position: absolute;
      }
      .el-upload-list {
        width: 100%;
        padding: 0 65px 0 226px;
        box-sizing: border-box;
      }
      .el-upload-list__item {
        font-size: 24px;
      }
      .el-upload-list__item-name {
        color: #fff;
      }
      .tl-box1 {
        background: url("/static/images/zhdd/icon_circle.png") no-repeat;
        width: 32px;
        height: 32px;
        background-size: 100% 100%;
      }
      .fw-item {
        display: flex;
        flex-direction: column;
      }
      .tl-box9 {
        background: url("/static/images/zhdd/icon9.png") no-repeat;
        width: 32px;
        height: 32px;
        background-size: 100% 100%;
      }
      .demo-slider {
        margin-top: 25px;
        width: 100%;
      }
      .dd-bg {
        background: url("/static/images/zhdd/bg_dd.png") no-repeat;
        width: 201px;
        height: 48px;
        line-height: 48px;
        text-align: center;
      }
      .ddActive {
        background: url("/static/images/zhdd/bg_dd_active.png") no-repeat;
      }
      .zzdCon {
        background: #224763;
        border: 1px solid #3778ee;
        width: 700px;
        height: 600px;
        box-shadow: 0 0 5px #0d3243;
        position: absolute;
        left: 93px;
        top: 550px;
        display: none;
      }
    </style>
  </head>

  <body>
    <div id="zhdddialog" v-cloak>
      <s-dialog
        title="一键调度"
        width="730px"
        height="1350px"
        top="52px"
      ></s-dialog>
      <div class="content">
        <div class="close" style="margin-left: 600px" @click="close()">×</div>
        <div class="text-mid-yellow" style="margin: 20px 0">任务信息</div>
        <form action="" class="layui-form" id="form3" lay-filter="form3">
          <div class="layui-form-item zhdd-item">
            <label class="layui-form-label text-mid-blue flex-align-center">
              <div class="icon-cir"></div>
              <span>现场地址</span>
            </label>
            <div class="layui-input-inline flex-between">
              <input
                autocomplete="off"
                class="layui-input"
                name="xcdz"
                style="width: 90%"
                type="text"
                id="yjddXcdz"
              />
              <div class="zhdd-search" id="zhddSearch"></div>
            </div>
          </div>
          <div class="layui-form-item zhdd-item">
            <label
              class="layui-form-label text-mid-blue flex-align-center"
              style="width: 30%"
            >
              <div class="icon-cir"></div>
              <span>范围半径(m)</span>
            </label>
            <div class="fw-item" style="width: 63%">
              <div class="layui-input-inline flex-between" style="width: 100%">
                <input
                  autocomplete="off"
                  class="layui-input"
                  max="10000"
                  min="0"
                  name="fwbj"
                  type="number"
                  readonly
                />
                <div class="tl-item tl-item-fw">
                  <div class="tl-box1" id="circleBtn3" title="圈选"></div>
                </div>
                <div class="tl-item tl-item-fw">
                  <div
                    class="tl-box9"
                    id="circleBtn3Clear"
                    title="清除圈"
                  ></div>
                </div>
              </div>
              <div class="demo-slider" id="slideTest3"></div>
            </div>
          </div>
          <div class="layui-form-item zhdd-item">
            <label class="layui-form-label text-mid-blue flex-align-center">
              <div class="icon-cir"></div>
              <span>内容描述</span>
            </label>
            <div class="layui-input-inline">
              <textarea
                class="layui-textarea scrollbar"
                name="nrms"
                placeholder=""
                style="height: 100px"
              ></textarea>
            </div>
          </div>
        </form>
        <div class="dd-tab flex-around" id="ddTab">
          <div class="dd-bg ddActive">资源调度</div>
          <div class="dd-bg">感知设备调度</div>
        </div>
        <div class="dd-menu">
          <ul class="menu-ul" id="menuUl"></ul>
        </div>
        <div class="flex-between" style="width: 50%">
          <div class="icon-add fs-28 cursor" id="addGroup">+&nbsp;添加</div>
          <!--<div class="icon-add fs-28 cursor" id="zzdxx">浙政钉消息</div>-->
          <!--<div class="icon-add fs-28 cursor" id="sphy">视频会议</div>-->
          <!--<div class="icon-add fs-28 cursor" id="yjhj">语音呼叫</div>-->
        </div>
        <div class="flex-align-center cursor" id="yjddSelectAll">
          <div class="icon-tick"></div>
          <div class="text-linear-blue">&nbsp;全选</div>
        </div>
        <div class="zzdCon" id="zzdCon">
          <div class="flex-between zzd-title">
            <div class="">消息</div>
            <div class="close cursor" id="zzClose">×</div>
          </div>
          <form action="" class="layui-form" id="formtz" lay-filter="formtz">
            <div class="layui-form-item zhdd-item">
              <label class="layui-form-label text-mid-blue flex-align-center">
                <span>通知人员</span>
              </label>
              <div class="layui-input-inline">
                <div
                  class="layui-textarea scrollbar"
                  id="tzry"
                  style="height: 80px"
                ></div>
                <!--<textarea placeholder=""  id="tzry" readonly class="layui-textarea scrollbar" name="tzry" style="height: 200px;"></textarea>-->
              </div>
            </div>
            <div class="layui-form-item zhdd-item">
              <label class="layui-form-label text-mid-blue flex-align-center">
                <span>消息</span>
              </label>
              <div class="layui-input-inline">
                <textarea
                  class="layui-textarea scrollbar"
                  id="xx"
                  name="xx"
                  placeholder="请填写消息"
                  style="height: 200px"
                ></textarea>
              </div>
            </div>
            <div
              class="icon-add flex-center cursor"
              id="tzSubmit"
              lay-filter="tzSubmit"
              lay-submit="tzSubmit"
              style="margin: 30px auto"
            >
              发送
            </div>
          </form>
          <div class="zzd-msg" id="fsMsg">发送成功</div>
        </div>
        <ul class="zhdw-ul scrollbar" id="zhdwUl"></ul>
        <div class="icon-add zhdd-submit fs-38" id="zhddSubmit">发起</div>
      </div>
    </div>
  </body>
  <script
    type="text/javascript"
    src="https://cdn.bootcss.com/animejs/2.2.0/anime.min.js"
  ></script>
  <script src="/static/citybrain/zhdd/zhdd_page/js/mapUtilZhdd.js"></script>
  <script src="/static/citybrain/zhdd/zhdd_page/js/layercfgZhdd_biz.js"></script>
  <script src="/static/citybrain/zhdd/zhdd_page/js/commonObjZhdd.js"></script>
  <script>
    var vm = new Vue({
      el: " #zhdddialog",
      data() {
        var this_ = this;
        return {
          sgId: null,
          sgLat: null,
          sgLon: null,
          yjddRadius: 0, // 一键调度半径
          yjddRadius_def: 0, // 一键调度半径(初始默认值)
          yjddCenter: [], // 一键调度中心点
          yjddLx: "", //一键调度类型
          currentYjddLx: "", // 一键调度当前选中类型
          yjddArr: [], //一键调度选中的队伍
          yjddArrAdd: [], //一键调度添加确定选中的队伍
          yjddXzArr: [], //一键调度选择队伍
          yxzArr: [], //一键调度展示队伍
          selectAll: [],
          currentSjkddzy: [], // 当前事件可调度资源
          lookZyFromYjdd: false, // 一键调度弹窗打开，查看资源，用于funloadPtByLx方法条件判断
          sgPolygonCenter: [], // 事件点多边形中心点
          geoserver_server_url:
            window.location.protocol + "//" + window.location.host, //线上
          yjddSjPolygon: [], // 事件多边形（圈）坐标
          zyFwInner: true, // 左下角图例范围，区域内：true; 全域：false
          zyFwCheckedLabel: "区域内",
          zyFwCircleCoords: [], // 左下角图例1km,2km,3km 圆坐标
          layForm: null,
          layer: null,
          yjddType: "", //判断是一键通知/指挥/调度
          laySlider: null,
          yjzhCoords: [], // 一键指挥圈选坐标集合
          yjddCirc_bear: 0, // 画圈方位角
          yjddCirc_labelRotate: 0, // 画圈文字标签旋转角度
          gridAttrs: [], // 已加载网格
          gridUrls: {
            婺城区: {
              url:
                this_.geoserver_server_url +
                "/egis-map-engine/data/services/wcqqkwlsjfw/wfs200/2.0.0/GetFeature",
              // url:  this_.geoserver_server_url + "/static/data/grid/wcq.json"
            },
            东阳市: {
              url:
                this_.geoserver_server_url +
                "/egis-map-engine/data/services/dysqkwlsjfw/wfs200/2.0.0/GetFeature",
              // url:  this_.geoserver_server_url + "/static/data/grid/dys.json"
            },
            "金义新区（金东区）": {
              url:
                this_.geoserver_server_url +
                "/egis-map-engine/data/services/jdqqkwlsjfw/wfs200/2.0.0/GetFeature",
              // url:  this_.geoserver_server_url + "/static/data/grid/jdq.json"
            },
            开发区: {
              url:
                this_.geoserver_server_url +
                "/egis-map-engine/data/services/jhkfqqkwlsjfw/wfs200/2.0.0/GetFeature",
              // url:  this_.geoserver_server_url + "/static/data/grid/kfq.json"
            },
            金华开发区: {
              url:
                this_.geoserver_server_url +
                "/egis-map-engine/data/services/jhkfqqkwlsjfw/wfs200/2.0.0/GetFeature",
              // url:  this_.geoserver_server_url + "/static/data/grid/kfq.json"
            },
            兰溪市: {
              url:
                this_.geoserver_server_url +
                "/egis-map-engine/data/services/lxsqkwlsjfw/wfs200/2.0.0/GetFeature",
              // url:  this_.geoserver_server_url + "/static/data/grid/lxs.json"
            },
            磐安县: {
              url:
                this_.geoserver_server_url +
                "/egis-map-engine/data/services/paxqkwlsjfw/wfs200/2.0.0/GetFeature",
              // url:  this_.geoserver_server_url + "/static/data/grid/pax.json"
            },
            浦江县: {
              url:
                this_.geoserver_server_url +
                "/egis-map-engine/data/services/pjxqkwlsjfw/wfs200/2.0.0/GetFeature",
              // url:  this_.geoserver_server_url + "/static/data/grid/pjx.json"
            },
            武义县: {
              url:
                this_.geoserver_server_url +
                "/egis-map-engine/data/services/wyxqkwlsjfw/wfs200/2.0.0/GetFeature",
              // url:  this_.geoserver_server_url + "/static/data/grid/wyx.json"
            },
            义乌市: {
              url:
                this_.geoserver_server_url +
                "/egis-map-engine/data/services/ywsqkwlsjfu/wfs200/2.0.0/GetFeature",
              // url:  this_.geoserver_server_url + "/static/data/grid/yws.json"
            },
            永康市: {
              url:
                this_.geoserver_server_url +
                "/egis-map-engine/data/services/yksqkwlsjfw/wfs200/2.0.0/GetFeature",
              // url:  this_.geoserver_server_url + "/static/data/grid/yks.json"
            },
          },
          jhGeojson: null,
        };
      },
      created() {},
      mounted() {
        this.initApi();
      },
      methods: {
        initApi() {
          this.sgId = window.parent.frames["zhdd_right"].zhddRightVm.sgId;
          this.sgLat = window.parent.frames["zhdd_right"].zhddRightVm.sgLat;
          this.sgLon = window.parent.frames["zhdd_right"].zhddRightVm.sgLon;
          let this_ = this;
          common.funSearchInfoOnline22(
            {
              url: "zhddzx/zhddzxMid016",
              argument: { sgid: this.sgId, type: "yjdd" },
            },
            function (res) {
              this_.yjddRadius = Number(res.fwbj) / 1000;
              this_.yjddRadius_def = Number(res.fwbj) / 1000;
              this_.funForm3(res);
              this_.yjddLx = res.lx;
              common.funSearchInfoOnline22(
                {
                  url: "zhddzx/zhddzxMid018",
                  argument: {
                    sgid: this_.sgId,
                    lx: res.lx,
                    type: $("#ddTab>.ddActive").text(),
                  },
                },
                this_.funZyddTab
              ); //资源调度
            }
          );
          $("#ddTab>div")
            .unbind("click")
            .bind("click", function () {
              let name = $(this).text();
              $(this).addClass("ddActive").siblings().removeClass("ddActive");
              common.funSearchInfoOnline22(
                {
                  url: "zhddzx/zhddzxMid018",
                  argument: {
                    sgid: this_.sgId,
                    lx: this_.yjddLx,
                    type: name,
                  },
                },
                this_.funZyddTab
              ); //资源调度
            });
          $("#yjddPanelClose").click(function () {
            window.parent.mapUtil.plotTool.close();
            window.parent.mapUtil.drawTool.clear();
            $("#yjddPanel").hide();
            $("#gfPanel").show();
            this.yjddArr = [];
            this.yjddArrAdd = [];
            $("#mainMid").hide();
            this.lookZyFromYjdd = false;
            this.selectAll = [];
            this.funCloseYjdd();
            this.yjddRadius = 0;
            this.yjddCenter = [];
          });
          // 添加
          $("#addGroup")
            .unbind("click")
            .bind("click", function () {
              this.funGetZyListByType({
                lx: $("#menuUl>.menuActive").data("id"),
                callback(_res) {
                  this.funXzdw2(_res.features, _res.fieldName);
                },
              });
            });
          $("#zzdxx")
            .unbind("click")
            .bind("click", function () {
              $("#zzdCon").show();
              let lis = [];
              this.yjddArr.map((item, index) => {
                lis.push(`<div class="name-con lf">${item.name}</div>`);
              });
              $("#tzry").html(lis.join(""));
            });
          $("#zzClose")
            .unbind("click")
            .bind("click", function () {
              $("#zzdCon").hide();
              $("#tzry").empty();
              $("#tzry").val("");
              $("#fsMsg").hide();
            });
          $("#tzSubmit")
            .unbind("click")
            .bind("click", function () {
              $("#zhdwUl>li").removeClass("liActive");
              $("#fsMsg").show();
              setTimeout(function () {
                $("#zzdCon").hide();
                $("#tzry").empty();
                $("#tzry").val("");
                $("#fsMsg").hide();
              }, 1000);
            });
          // 一键调度-现场地址按钮点击
          $("#zhddSearch")
            .unbind("click")
            .bind("click", function () {
              this.getLocationByJh({
                keyWord: $("#yjddXcdz").val(),
                callback(res) {
                  // 默认取第一个
                  if (res.data.length > 0) {
                    const poi = res.data[0];
                    this.funFlyToPt(poi.x + "," + poi.y);
                  }
                },
              });
            });
        },
        funFlyToPt(point, zoom = 13) {
          const points = point.split(",");
          window.parent.mapUtil.flyTo({
            destination: points,
            zoom,
          });
        },
        // 金华现场地址查询
        getLocationByJh(params = {}) {
          let { keyWord = "" } = params;
          const _url =
            baseURL.url + "/api2.0/solr-provider/api/data-sources/solr-search";
          const _argument = {
            geoType: "geojson",
            operator: "or",
            order: "",
            pageInfo: {
              current: 1,
              size: 10,
              totalSize: 0,
            },
            returnGeo: true,
            sortField: "",
            tableNames: "dmdz",
            text: keyWord, //地址关键字
          };

          this.funSearchInfoByUrl(
            { url: _url, argument: _argument, type: "post" },
            function (res) {
              if (params.callback) {
                console.log("金华地址查询结果：" + res.data.length);
                params.callback(res);
              }
            }
          );
        },
        funSearchInfoByUrl: function (params, callback) {
          let { url, argument, flag = true, type = "get", opts } = params;
          $.ajax({
            url: url,
            data: type === "get" ? argument : JSON.stringify(argument),
            contentType:
              type === "get"
                ? "application/x-www-form-urlencoded"
                : "application/json; charset=UTF-8",
            dataType: "json",
            type: type,
            async: flag,
            success: function (data) {
              callback(data);
            },
            error: function (e) {},
          });
        },
        /**
         * 计算两经纬度之间的距离，单位是m
         * approx distance between two points on earth ellipsoid
         */
        getFlatternDistance(lat11, lng12, lat21, lng22) {
          const lat1 = Number(lat11);
          const lng1 = Number(lng12);
          const lat2 = Number(lat21);
          const lng2 = Number(lng22);
          const PI = Math.PI;
          const EARTH_RADIUS = 6378137.0;

          function getRad(d) {
            return (d * PI) / 180.0;
          }

          let f = getRad((lat1 + lat2) / 2);
          let g = getRad((lat1 - lat2) / 2);
          let l = getRad((lng1 - lng2) / 2);
          let sg = Math.sin(g);
          let sl = Math.sin(l);
          let sf = Math.sin(f);

          let s, c, w, r, d, h1, h2;
          let a = EARTH_RADIUS;
          let fl = 1 / 298.257;

          sg = sg * sg;
          sl = sl * sl;
          sf = sf * sf;

          s = sg * (1 - sl) + (1 - sf) * sl;
          c = (1 - sg) * (1 - sl) + sf * sl;

          w = Math.atan(Math.sqrt(s / c));
          r = Math.sqrt(s * c) / w;
          d = 2 * w * a;
          h1 = (3 * r - 1) / 2 / c;
          h2 = (3 * r + 1) / 2 / s;

          return d * (1 + fl * (h1 * sf * (1 - sg) - h2 * (1 - sf) * sg));
        },
        // 添加-选择(借助geoserver服务查询)
        funXzdw2(data, fieldName = "name") {
          $("#groupPanel").show();
          let lis = [];
          let newArr = [];
          let yxzArr = this.yxzArr || []; // 在#zhdwUl下已展示的队伍，用于在添加时判断是否已经存在，如果存在不再添加
          //关闭
          $("#groupPanelClose")
            .unbind("click")
            .bind("click", function () {
              this.yjddXzArr = [];
              newArr = [];
              $("#xzdwUl>li").removeClass("liActive");
              $("#groupPanel").hide();
            });
          //确定
          $("#certainGroup")
            .unbind("click")
            .bind("click", function () {
              newArr.map((item) => {
                this.yjddXzArr.push(item);
              });

              let lis = [];
              this.yjddXzArr.map((item, index) => {
                const hasXz = yxzArr.includes(item.id);
                if (!hasXz) {
                  let _dis =
                    this.getFlatternDistance(
                      this.sgLat,
                      this.sgLon,
                      item.point[1],
                      item.point[0]
                    ) / 1000; // km
                  _dis = _dis.toFixed(2);
                  lis.push(`<li class="zhdw-li flex-align-center liActive" data-id="${item.id}" data-name="${item.name}">
                        <div class="icon-tick"></div>
                        <div class="text-linear-blue ellipsis zhdw-name2" title="${item.name}">${item.name}</div>
                        <div class="color-d6e7f9 text-right zhdw-km">${_dis} km</div>
                    </li>`);
                  yxzArr.push(item.id);
                }
              });
              yxzArr = [...new Set(yxzArr)];
              // 确定勾选，赋值到已选中队伍，并去重
              this.yjddArrAdd = [...this.yjddArrAdd, ...this.yjddXzArr];
              this.yjddArrAdd = [
                ...new Set(this.yjddArrAdd.map((v) => JSON.stringify(v))),
              ].map((s) => JSON.parse(s));
              this.yjddArr = [...this.yjddArr, ...this.yjddArrAdd];

              $("#zhdwUl").append(lis.join(""));
              // common.titleInfo("zhdw-name2");
              this.yjddXzArr = [];
              newArr = [];
              $("#xzdwUl>li").removeClass("liActive");

              $("#zhdwUl>li")
                .unbind("click")
                .bind("click", function () {
                  if ($(this).hasClass("liActive")) {
                    $(this).removeClass("liActive");
                    //去掉勾选并删除数组中的数据
                    this.yjddArr.splice(
                      this.yjddArr.findIndex(
                        (item) => item.id == $(this).data("id")
                      ),
                      1
                    );
                  } else {
                    $(this).addClass("liActive");
                    //选中的添加到数组
                    this.yjddArr.push({
                      id: $(this).data("id") + "",
                      name: $(this).data("name"),
                      type: this.currentYjddLx,
                    });
                  }
                });

              setTimeout(() => {
                $("#xzdwUl>li").removeClass("liActive");
                $("#groupPanel").hide();
              }, 1000);
            });
          //取消
          $("#cancelGroup")
            .unbind("click")
            .bind("click", function () {
              $("#xzdwUl>li").removeClass("liActive");
              this.yjddXzArr = [];
              newArr = [];
            });
          if (data.length > 0 || data != null) {
            // 资源补充距离字段， 并排序
            for (let i = 0; i < data.length; i++) {
              let _dis =
                this.getFlatternDistance(
                  Number(this.sgLat),
                  Number(this.sgLon),
                  data[i].geometry.coordinates[1],
                  data[i].geometry.coordinates[0]
                ) / 1000; // 点位到事故点距离 km
              _dis = _dis.toFixed(2);
              data[i].properties.distanceTosg = _dis;
            }
            data.sort((a, b) => {
              return a.properties.distanceTosg - b.properties.distanceTosg;
            }); //  升序
            if (this.currentYjddLx == "qtry") {
              let res = [
                { name: "专家", data: [] },
                { name: "避灾安置点", data: [] },
              ];
              data.map((item) => {
                if (item.properties.type == "yjzj") {
                  res[0].data.push({
                    name: item.properties[fieldName],
                    id: item.properties.id,
                    properties: item.properties,
                  });
                } else {
                  res[1].data.push({
                    name: item.properties[fieldName],
                    id: item.properties.id,
                    properties: item.properties,
                  });
                }
              });
              res.map((ite) => {
                let ul = "";
                ite.data.map((item, index) => {
                  ul += `<li class="zhdw-li flex-align-center" data-id="${item.id}" data-name="${item.name}">
                            <div class="icon-tick"></div>
                            <div class="text-linear-blue ellipsis  zhdw-name1" title="${item.name}" style="width: 50%;">${item.name}</div>
                            <div class="color-d6e7f9 text-right zhdw-km">${item.properties.distanceTosg} km</div>
                        </li>`;
                });
                lis.push(
                  `<li class="zhdw-warp-li"><div class="wrap-name">${ite.name}</div><ul class="sec-ul">${ul}</ul></li>`
                );
              });
              $("#xzdwUl").html(lis.join(""));
              // common.titleInfo("zhdw-name1");
              //点击名字 收缩下面的ul
              $("#xzdwUl>li>.wrap-name")
                .unbind("click")
                .bind("click", function () {
                  if ($(this).hasClass("text-linear-blue")) {
                    $(this).removeClass("text-linear-blue");
                    $(this).siblings().show();
                  } else {
                    $(this).addClass("text-linear-blue");
                    $(this).siblings().hide();
                  }
                });
              $("#xzdwUl>li>ul>li").click(function () {
                if ($(this).hasClass("liActive")) {
                  $(this).removeClass("liActive");
                  //去掉勾选并删除数组中的数据
                  newArr.splice(
                    newArr.findIndex((item) => item === $(this).data("id")),
                    1
                  );
                } else {
                  $(this).addClass("liActive");
                  //选中的添加到数组
                  // newArr.push($(this).data("id"));
                  newArr.push({
                    id: $(this).data("id") + "",
                    name: $(this).data("name"),
                    type: this.currentYjddLx,
                    point: data[$(this).index()].geometry.coordinates, // []
                  });
                }
              });
            } else {
              data.map((item, index) => {
                lis.push(`<li class="zhdw-li flex-align-center" data-id="${item.properties.id}" data-name="${item.properties[fieldName]}">
                            <div class="icon-tick"></div>
                            <div class="text-linear-blue ellipsis  zhdw-name1" title="${item.properties[fieldName]}" style="width: 50%;">${item.properties[fieldName]}</div>
                            <div class="color-d6e7f9 text-right zhdw-km">${item.properties.distanceTosg} km</div>
                        </li>`);
              });
              $("#xzdwUl").html(lis.join(""));
              // common.titleInfo("zhdw-name1");
              $("#xzdwUl>li").click(function () {
                if ($(this).hasClass("liActive")) {
                  $(this).removeClass("liActive");
                  //去掉勾选并删除数组中的数据
                  newArr.splice(
                    newArr.findIndex((item) => item === $(this).data("id")),
                    1
                  );
                } else {
                  $(this).addClass("liActive");
                  //选中的添加到数组
                  // newArr.push($(this).data("id"));
                  newArr.push({
                    id: $(this).data("id") + "",
                    name: $(this).data("name"),
                    type: this.currentYjddLx,
                    point: data[$(this).index()].geometry.coordinates, // []
                  });
                }
              });
            }
          }
        },
        // 获取资源列表(使用geoserver服务)
        funGetZyListByType(params = {}) {
          let poiName = "",
            fieldName = "name";
          switch (params.lx) {
            case "wgy":
              poiName = "poitest:wgry";
              fieldName = "contacts_n";
              break;
            case "yjzj":
              poiName = "poitest:yjzj";
              fieldName = "expert_nam";
              break;
            case "jydw":
              poiName = "poitest:yjdw";
              fieldName = "team_name";
              break;
            case "yjwz":
              poiName = "poitest:yjwz";
              fieldName = "material_n";
              break;
            case "bzcs":
              poiName = "poitest:bzazd";
              fieldName = "shelter_na";
              break;
            case "wlgz":
              poiName = "poitest:wlgz";
              fieldName = "name";
              break;
            case "zfry":
              poiName = "poitest:zfry";
              fieldName = "name";
              break;
            case "spjk":
              poiName = "poitest:video";
              fieldName = "CHN_NAME";
              break;
            case "yjgb":
              poiName = "poitest:yjgb";
              fieldName = "GBMC";
              break;
            case "zyz":
              poiName = "poitest:zyz";
              fieldName = "mc";
              break;
            case "qtry":
              poiName = "poitest:qtry";
              fieldName = "mc";
              break;
          }
          // let url = common.geoserver_server_url + "/geoserver/poitest/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=" + poiName + "&maxFeatures=500&outputFormat=application/json&bbox=119.22367661602989,28.52706653689894,120.77889703265598,29.68484864821428";
          // 默认取当前事故点外5km 资源
          const circle = turf.circle(this.sgPolygonCenter, 5, {
            steps: 64,
            units: "kilometers",
          });
          const coordinates = circle.geometry.coordinates[0].flat();
          let posStr = "";
          for (let i = 0; i < coordinates.length; i = i + 2) {
            posStr += coordinates[i] + "," + coordinates[i + 1] + " ";
          }
          const filter =
            `<Filter xmlns="http://www.opengis.net/ogc" xmlns:gml="http://www.opengis.net/gml"><Intersects><PropertyName>the_geom</PropertyName><gml:Polygon><gml:outerBoundaryIs><gml:LinearRing><gml:coordinates>` +
            posStr +
            `</gml:coordinates></gml:LinearRing></gml:outerBoundaryIs></gml:Polygon></Intersects></Filter>`;
          let url =
            this.geoserver_server_url +
            "/geoserver/poitest/ows?service=WFS&request=GetFeature&version=1.0.0&typeName=" +
            poiName +
            "&maxFeatures=5000&outputFormat=json&filter=" +
            filter;

          this.funSearchInfoByUrl({ url: url, type: "post" }, function (res) {
            const { totalFeatures, features } = res;
            if (params.callback) {
              params.callback({ totalFeatures, features, fieldName });
            }
          });
        },
        // 一键调度弹窗关闭
        funCloseYjdd() {
          // 清除点位
          this.currentSjkddzy.forEach((item) => {
            this.funRmPtByLx(item);
          });
          // 左下角图对应取消勾选
          $("#zyLegendCon div").each(function () {
            let name = $(this).data("name");
            name = name == "wgry" ? "name" : name;
            const isHas = this.currentSjkddzy.includes(name);
            if ($(this).hasClass("liActive") && isHas) {
              $(this).removeClass("liActive");
            }
          });
        },
        // 根据类型清除点位
        funRmPtByLx(lx = "jydw") {
          let pointId = "";
          switch (lx) {
            case "jydw":
              pointId = "zhdd_map_zydd_jydw_yjtz";
              break;
            case "yjzj":
              pointId = "zhdd_map_zydd_yjzj_yjtz";
              break;
            case "yjwz":
              pointId = "zhdd_map_zydd_yjwz_yjtz";
              break;
            case "wlgz":
              pointId = "zhdd_map_zydd_wlgz_yjtz";
              break;
            case "wgry":
              pointId = "zhdd_map_zydd_wgry_yjtz";
              break;
            case "wgy":
              pointId = "zhdd_map_zydd_wgry_yjtz";
              break;
            case "zfry":
              pointId = "zhdd_map_zydd_xzzfry_yjtz";
              break;
            case "spjk":
              pointId = "zhdd_map_zydd_video_yjtz";
              break;
            case "bzcs":
              pointId = "zhdd_map_zydd_bzazd_yjtz";
              break;
            case "yld":
              pointId = "zhdd_map_zydd_yld_yjtz";
              break;
            case "yjgb":
              pointId = "zhdd_map_zydd_yjgb_yjtz";
              break;
            case "bscg":
              pointId = "zhdd_map_zydd_bscg_yjtz";
              break;
            case "jdbg":
              pointId = "zhdd_map_zydd_jdbg_yjtz";
              break;
          }
          this.funClearPoint(pointId);
        },
        funClearPoint(id = "") {
          window.parent.mapUtil.removeLayer(id);
        },
        funZyddTab(data) {
          let lis = [];
          this.currentSjkddzy = [];
          let menuUlWidth = 0;
          data.map((item, index) => {
            menuUlWidth += item.name.length * 40;
            lis.push(
              `<li style="width: ${
                item.name.length * 40
              }px" class="menu-li lf" data-id="${item.id}">${item.name}</li>`
            );
            this.currentSjkddzy.push(item.id);
          });
          $("#menuUl").css("width", menuUlWidth + "px");
          $("#menuUl").html(lis.join(""));

          $("#menuUl>li").eq(0).addClass("menuActive"); //第一个默认选中

          // 一键调度当前选中类型
          this.currentYjddLx = $("#menuUl>.menuActive").data("id");
          this.funRmPtByLx(this.currentYjddLx);

          this.funloadPtByLx({
            lx: $("#menuUl>.menuActive").data("id"),
          });

          $("#menuUl>li")
            .unbind("click")
            .bind("click", function () {
              $("#yjddSelectAll").removeClass("liActive");
              $(this)
                .addClass("menuActive")
                .siblings()
                .removeClass("menuActive");
              this.funRmPtByLx(this.currentYjddLx);
              let id = $(this).data("id");
              this.currentYjddLx = id;
              this.selectAll.map((item, index) => {
                if ($(this).index() == item) {
                  $("#yjddSelectAll").addClass("liActive");
                }
              });
              this.funloadPtByLx({
                lx: id,
                showList: true,
                limit: true,
                isQueryCircle:
                  this.yjddRadius !== this.yjddRadius_def ? true : false,
                circleRadius: this.yjddRadius,
                circleCenter: this.yjddCenter,
              });
            });
        },
        /**
         * 根据类型查询资源并初始化列表（借助geoserver）
         * @param{Object} params
         * @param{String} params.lx: 资源类型
         * @param{Boolean} params.showList: 是否指定查询区域坐标
         * @param{Boolean} params.limit: 是否展示列表
         * @param{Boolean} params.isQueryCircle: 是否查询圆
         * @param{Number} params.circleRadius: 圆半径
         * @param{Array} params.circleCenter: 圆心
         */
        funloadPtByLx(params = {}) {
          const {
            lx = "jydw",
            showList = true,
            limit = true,
            isQueryCircle = false,
            circleRadius = 0,
            circleCenter = [],
          } = params;
          let _queryLimit = this.yjddSjPolygon.length === 0 ? false : true;
          if (!limit) {
            _queryLimit = false;
          }
          if (!this.lookZyFromYjdd) {
            _queryLimit = this.zyFwInner; // 根据勾选区域内 还是全域
          }
          let _coords = []; // 查询坐标
          if (
            this.zyFwCheckedLabel == "1" ||
            this.zyFwCheckedLabel == "2" ||
            this.zyFwCheckedLabel == "3"
          ) {
            _coords = this.zyFwCircleCoords;
            _queryLimit = true;
          } else {
            // 区域内/全域
            _coords = this.yjddSjPolygon;
            _queryLimit = this.zyFwCheckedLabel == "区域内" ? true : false;
          }
          if (isQueryCircle) {
            const _circle = turf.circle(
              [Number(circleCenter[0]), Number(circleCenter[1])],
              Number(circleRadius),
              {
                steps: 64,
                units: "kilometers",
              }
            );
            _coords = _circle.geometry.coordinates[0];
          }
        },
        funForm3(res) {
          let this_ = this;
          layui.use(
            ["form", "layer", "layedit", "slider", "formSelects"],
            function () {
              var form = layui.form;
              this_.layForm = form;
              var slider = layui.slider;
              var formSelects = layui.formSelects;
              this_.layer = layui.layer;
              common.funSearchInfoOnline22(
                {
                  url: "zhddzx/zhddzxMid017",
                  argument: { sgid: this_.sgId },
                },
                function (data) {
                  //类型-下拉框
                  if (this_.yjddType == "yjdd" || this_.yjddType == "yjzh") {
                    $(".zhddLx").empty();
                    let ops = [];
                    data.lx.map((item) => {
                      if (item.val == res.lx) {
                        ops.push(
                          `<option value="${item.val}" selected="">${item.name}</option>`
                        );
                      } else {
                        ops.push(
                          `<option value="${item.val}">${item.name}</option>`
                        );
                      }
                    });
                    $(".zhddLx").append(ops.join(""));
                    form.render("select"); //更新表单中下拉框
                    //指派单位-下拉框
                  }

                  form.on("select", function (data) {
                    this_.yjddLx = data.value;
                    common.funSearchInfoOnline22(
                      {
                        url: "zhddzx/zhddzxMid018",
                        argument: {
                          sgid: this_.sgId,
                          lx: this_.yjddLx,
                          type: $("#ddTab>.ddActive").text(),
                        },
                      },
                      this_.funZyddTab
                    ); //资源调度
                  });
                }
              );
              debugger;
              //表单赋值
              if (this_.yjddType == "yjdd") {
                form.val("form3", res);
                this_.laySlider = slider.render({
                  elem: "#slideTest3",
                  theme: "#00f0f8",
                  value: res.fwbj,
                  min: 0,
                  max: 10000,
                  change(value) {
                    console.log(value);
                    $("input[name=fwbj]").val(value);
                    this_.yjddRadius = value / 1000; // km
                    this_.funUpdateCircleYjdd(value);
                  },
                });
              }

              //一键调度-表单取值
              $("#zhddSubmit")
                .unbind("click")
                .bind("click", function () {
                  let formData = form.val("form3");
                  funLon(formData);
                  $("#yjddSelectAll").removeClass("liActive");
                  $("#wgddUl>li").removeClass("liActive");
                });
              // 一键调度-半径按钮
              $("#circleBtn3")
                .unbind("click")
                .bind("click", function () {
                  this_.funRmAllYjzhPoint();
                  this_.openYjdd();
                });
              // 一键调度-清除圈
              $("#circleBtn3Clear")
                .unbind("click")
                .bind("click", function () {
                  window.parent.mapUtil.plotTool.close(); // 关闭绘制
                  window.parent.mapUtil.drawTool.clear();
                });
              function funLon(formData) {
                common.funSearchInfoOnline22(
                  {
                    url: "zhddzx/zhddzxMid021",
                    argument: {
                      sgid: this_.sgId,
                      type: this_.yjddType,
                      form: formData,
                      dw: this_.yjddArr,
                      radius: this_.yjddRadius,
                      polygon: this_.yjtzPolygon.join(","),
                    },
                    type: "post",
                  },
                  function (data) {
                    this_.layer.msg("下发成功");
                    $("#" + `${this_.yjddType}Panel`).hide();
                    this_.selectAll = [];
                    // this_.funFq(data.sgid);
                    if (this_.yjddType == "yjdd") {
                      window.parent.postMessage(
                        { type: "zhddFqRight", sgid: data.sgid },
                        "*"
                      );
                    }
                    window.parent.postMessage(
                      { type: "zhddSubmit", sgid: data.sgid },
                      "*"
                    );
                  }
                );
              }
            }
          );
        },
        // 开启一键调度/通知/指挥画圈
        openYjdd() {
          // todo: 开启画圈
          const _onComplete = (res) => {
            const centerLnglat = [res.center.lng, res.center.lat];
            this.yjddRadius = res.radius; //km
            this.yjddCenter = centerLnglat;
            this.yjddCirc_bear = res.ptBearing || 0;
            this.yjddCirc_labelRotate = res.labelRotate || 0;
            this.yjzhCoords = res.coordinates[0];
            // middle.yjddSjPolygon = (middle.userCommonMap && middle.useArcgisMap) ? res.coordinates[0] :  res.coordinates.geometry.coordinates[0];
            mapUtilZhdd.getAddressByGD({
              lon: centerLnglat[0],
              lat: centerLnglat[1],
              crs: "wgs84",
              callback(result) {
                let _formVal = "form3";
                if (this.yjddType === "yjdd") {
                  _formVal = "form3";
                  this.yjddArr = [];
                  this.yjzhCoords = [];
                  this.currentYjddLx = $("#menuUl>.menuActive").data("id");
                  this.funloadSjZyBylx({
                    checkZyArr: this.currentSjkddzy,
                    coords: res.coordinates[0],
                    initList: true,
                    tabName: $("#menuUl>.menuActive").data("id"),
                  });
                  window.parent.postMessage(
                    {
                      type: "yjddDrawCircle",
                      center: centerLnglat,
                      radius: res.radius,
                    },
                    "*"
                  );
                } else if (this.yjddType === "yjtz") {
                  _formVal = "form1";
                  return;
                } else {
                  _formVal = "form2";
                  this.funGetGridList({
                    center: this.yjddCenter,
                    radius: this.yjddRadius,
                  });

                  // middle.funYjzhRy({coords: res.coordinates.geometry.coordinates[0], queryLimit:true})
                }
                this.laySlider.setValue(Math.floor(Number(res.radius * 1000)));
                this.layForm.val(_formVal, {
                  fwbj: Math.floor(Number(res.radius * 1000)),
                });
                this.layForm.val(_formVal, {
                  xcdz: result.formatted_address,
                });
              },
            });
          };
          window.parent.mapUtil.drawTool.clear();
          window.parent.mapUtil.plotTool.close();
          window.parent.mapUtil.plotTool.active("circle", function (res) {
            _onComplete(res);
          });
        },
        // 清除一键指挥点位(网格人员+执法人员)
        funRmAllYjzhPoint() {
          const zyPtTypes = [
            "zhdd_map_zydd_wgry_yjtz",
            "zhdd_map_zydd_xzzfry_yjtz",
          ];
          zyPtTypes.forEach((item) => {
            this.funClearPoint(item);
          });
        },
        // 获取网格列表
        funGetGridList(params = {}) {
          const { center = [119.642497, 29.082135], radius = 2 } = params;
          $zhddget("/zhddzx/getGridList", {
            center: center.join(","),
            distance: radius,
          }).then((data) => {
            if (data.length === 0) return;
            this.getGridData({ data: data, useEliApi: false }).then((res) => {
              console.log(res);
              this.funWgdd(res);
            });
          });
        },
        funWgdd(data, fieldName = "name") {
          this.yjddArr = [];
          let lis = [];
          data.map((item, index) => {
            lis.push(`<li class="zhdw-li flex-align-center" data-id="${
              item.adcode || item.ADCODE
            }">
                <div class="icon-tick"></div>
                <div class="text-linear-blue ellipsis zhdw-name" title="${
                  item.name || item.NAME
                }" style="width:95%;">${item.szs || item.SZS}&nbsp;${
              item.szqx || item.SZQX
            }&nbsp;${item.szz || item.SZZ}&nbsp;${
              item.szsq || item.SZSQ
            }&nbsp;${item.name || item.NAME}</div>
            </li>`);
          });
          $("#wgddUl").html(lis.join(""));
          this.funWgddClick();
        },
        funWgddClick() {
          $("#wgddUl>li")
            .unbind("click")
            .bind("click", function () {
              if ($(this).hasClass("liActive")) {
                $(this).removeClass("liActive");
                //去掉勾选并删除数组中的数据
                this.yjddArr.splice(
                  this.yjddArr.findIndex((item) => item == $(this).data("id")),
                  1
                );
              } else {
                $(this).addClass("liActive");
                //选中的添加到数组
                this.yjddArr.push($(this).data("id"));
              }
            });
        },
        // 获取网格并上图（易利网格服务）
        async getGridData(params = {}) {
          const {
            data = [
              { szqx: "永康市", adcode: "330784115218001", name: "柏川村网格" },
            ],
            polygonToMap = true,
            colorOutline = [255, 0, 0, 1],
            fillColor = [255, 0, 0, 0.1],
            useEliApi = true,
          } = params;

          this.gridAttrs = [];
          data.forEach((item) => {
            // 网格上图
            const _loadGridLayer = (res, layerId) => {
              const obj = Object.assign(res.features[0].properties, {
                polygon: res.features[0].geometry.coordinates[0],
                polygonLx: "szqxGridLayer",
              });
              this.gridAttrs.push(obj);
              if (polygonToMap) {
                window.parent.mapUtil.loadPolygonLayer({
                  layerid: layerId ? layerId : obj.ADCODE,
                  data: res, // res 是要素集合
                  style: {
                    strokeColor: colorOutline, //多边形轮廓颜色透明度
                    fillColor: fillColor, //多边形填充色
                  },
                  onclick: null,
                });
              }
            };
            // 使用易利发布
            if (useEliApi) {
              if (item.szqx && item.adcode && this.gridUrls[item.szqx]) {
                const _param = {
                  token: "3adffb2d586245e182c24828c8b63989",
                  count: 999999999,
                  FILTER: "adcode='" + item.adcode + "'",
                };
                this.funSearchInfoByUrl(
                  {
                    url: this.gridUrls[item.szqx].url,
                    argument: _param,
                    flag: false,
                  },
                  function (res) {
                    if (res.features.length > 0) {
                      _loadGridLayer(res, item.adcode);
                    }
                  }
                );
              }
            } else {
              const _url =
                this.geoserver_server_url +
                "/geoserver/poitest/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=poitest:jh_wangge&maxFeatures=5000&outputFormat=application/json&cql_filter=ADCODE='" +
                item.adcode +
                "'";
              this.funSearchInfoByUrl(
                { url: _url, flag: false },
                function (res) {
                  if (res.features.length > 0) {
                    _loadGridLayer(res);
                  }
                }
              );
            }
          });
          return this.gridAttrs;
        },
        // 清除网格
        removeGridData() {
          if (this.gridAttrs.length === 0) return;
          this.gridAttrs.forEach((item) => {
            window.parent.mapUtil.layers[item.adcode || item.ADCODE] &&
              window.parent.mapUtil.removeLayer(item.adcode || item.ADCODE);
          });
          this.gridAttrs = [];
        },
        // 更新圆
        funUpdateCircleYjdd(radius) {
          // 更新圆形回调
          const _onUpdateComplete = (res) => {
            if (this.yjddType === "yjdd") {
              this.yjddArr = [];
              this.yjddArrAdd = [];
              this.yjzhCoords = [];
              this.currentYjddLx = $("#menuUl>.menuActive").data("id");
              this.funloadSjZyBylx({
                checkZyArr: this.currentSjkddzy,
                coords: res.geometry.coordinates[0],
                initList: true,
                tabName: $("#menuUl>.menuActive").data("id"),
              });
            } else if (this.yjddType === "yjtz") {
              return;
            } else {
              this.removeGridData();
              this.funGetGridList({
                center: this.yjddCenter,
                radius: this.yjddRadius,
              });
            }
          };
          // 清除画圈
          window.parent.mapUtil.plotTool.close();
          window.parent.mapUtil.drawTool.clear();
          // 画圆
          window.parent.mapUtil.drawTool.draw("circle", {
            circles: [
              {
                center: this.yjddCenter,
                radius: radius,
                fillColor: [255, 255, 255, 0.1],
                strokeColor: [255, 255, 255, 1],
              },
            ],
            layerid: "yjddCircle",
          });
          const geojsonCircle = createGeoJSONCircle(
            this.yjddCenter,
            radius / 1000
          );
          _onUpdateComplete(geojsonCircle);
        },
        // 指定区域 救援队伍（应急队伍）上图及初始化列表
        loadjydwPtYjdd(params = {}) {
          let { coords = [], queryLimit = false, showList = true } = params;
          const typeNames = `poitest:yjdw`;
          let polygon = [];
          if (coords.length > 0) {
            if (coords.length >= 300) {
              // 多边形简化
              const geojsonPolygon = turf.polygon([coords]);
              coords = turf.simplify(geojsonPolygon, { tolerance: 0.1 })
                .geometry.coordinates[0];
            }
            coords.forEach((item, idx) => {
              const pt = item[0] + " " + item[1];
              polygon.push(pt);
            });
            polygon.push(polygon[0]);
          }

          let url =
            this.geoserver_server_url +
            "/geoserver/poitest/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=" +
            typeNames +
            "&maxFeatures=5000&outputFormat=application/json";
          if (queryLimit) {
            url +=
              "&cql_filter=within(the_geom,polygon((" +
              polygon.join(",") +
              ")))";
          } else {
            url +=
              "&bbox=119.22367661602989,28.52706653689894,120.77889703265598,29.68484864821428";
          }
          let jydwPtTemp = {
            pointId: "zhdd_map_zydd_jydw_yjtz",
            pointType: "zhdd_map_zydd_jydw",
            setClick: true,
            size: [0.05, 0.1, 0.15, 0.2],
            imageConfig: { iconSize: 0.4 },
            funcName: "pointLoad",
            pointData: [],
          };

          this.funClearPoint("zhdd_map_zydd_jydw_yjtz");
          this.funSearchInfoByUrl({ url: url }, function (res) {
            const { totalFeatures, features } = res;
            features.forEach((item2) => {
              const _idx = item2.id.indexOf(".");
              const type = item2.id.substring(0, _idx);
              let _inJHPolygon = true; // 是否在金华边界内
              if (this.jhGeojson && this.jhGeojson.features) {
                _inJHPolygon = turf.booleanPointInPolygon(
                  item2,
                  this.jhGeojson.features[0]
                );
              }
              if (
                type === "yjdw" &&
                item2.geometry &&
                item2.geometry.coordinates[0] !== 0 &&
                item2.geometry.coordinates[0] > 110 &&
                item2.properties["team_name"] !== "" &&
                _inJHPolygon
              ) {
                let _dis =
                  this.getFlatternDistance(
                    this.sgLat,
                    this.sgLon,
                    item2.geometry.coordinates[1],
                    item2.geometry.coordinates[0]
                  ) / 1000; // km
                _dis = Number(_dis.toFixed(2));
                jydwPtTemp.pointData.push({
                  data: {
                    ...item2.properties,
                    source: "zhddzxYjdd",
                    zylx: "yjdw_yjdd",
                    newDis: _dis,
                  },
                  point: item2.geometry.coordinates.join(","),
                  lng: item2.geometry.coordinates[0],
                  lat: item2.geometry.coordinates[1],
                });
              }
            });
            jydwPtTemp.pointData.sort((a, b) => {
              return a.data.newDis - b.data.newDis;
            }); //  升序
            if (jydwPtTemp.pointData.length > 0) {
              layercfgZhdd.jydwLayer.data = jydwPtTemp.pointData;
              layercfgZhdd.jydwLayer.onclick = middle.funOnClickMapLayerEvent;
              window.parent.mapUtil.loadPointLayer(layercfgZhdd.jydwLayer);
            }
            if (showList) {
              //   middle.funZyddKm(jydwPtTemp.pointData, "team_name");
            }
          });
        },
        //  应急物资上图及初始化列表
        loadyjwzPtYjdd(params = {}) {
          let { coords = [], queryLimit = false, showList = true } = params;
          const typeNames = `poitest:yjwz`;
          let polygon = [];
          if (coords.length > 0) {
            if (coords.length >= 300) {
              // 多边形简化
              const geojsonPolygon = turf.polygon([coords]);
              coords = turf.simplify(geojsonPolygon, { tolerance: 0.1 })
                .geometry.coordinates[0];
            }
            coords.forEach((item, idx) => {
              const pt = item[0] + " " + item[1];
              polygon.push(pt);
            });
            polygon.push(polygon[0]);
          }

          let url =
            common.geoserver_server_url +
            "/geoserver/poitest/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=" +
            typeNames +
            "&maxFeatures=5000&outputFormat=application/json";
          if (queryLimit) {
            url +=
              "&cql_filter=within(the_geom,polygon((" +
              polygon.join(",") +
              ")))";
          } else {
            url +=
              "&bbox=119.22367661602989,28.52706653689894,120.77889703265598,29.68484864821428";
          }

          let yjwzPtTemp = {
            pointId: "zhdd_map_zydd_yjwz_yjtz",
            pointType: "zhdd_map_zydd_yjwz",
            setClick: true,
            size: [0.05, 0.1, 0.15, 0.2],
            imageConfig: { iconSize: 0.4 },
            funcName: "pointLoad",
            pointData: [],
          };

          middle.funClearPoint("zhdd_map_zydd_yjwz_yjtz");
          common.funSearchInfoByUrl({ url: url }, function (res) {
            const { totalFeatures, features } = res;
            features.forEach((item2) => {
              const _idx = item2.id.indexOf(".");
              const type = item2.id.substring(0, _idx);
              let _inJHPolygon = true; // 是否在金华边界内
              if (middle.jhGeojson && middle.jhGeojson.features) {
                _inJHPolygon = turf.booleanPointInPolygon(
                  item2,
                  middle.jhGeojson.features[0]
                );
              }
              if (
                type === "yjwz" &&
                item2.geometry &&
                item2.geometry.coordinates[0] !== 0 &&
                item2.geometry.coordinates[0] > 110 &&
                item2.properties["material_n"] !== "" &&
                _inJHPolygon
              ) {
                let _dis =
                  middle.getFlatternDistance(
                    middle.sgLat,
                    middle.sgLon,
                    item2.geometry.coordinates[1],
                    item2.geometry.coordinates[0]
                  ) / 1000; // km
                _dis = Number(_dis.toFixed(2));
                yjwzPtTemp.pointData.push({
                  data: {
                    ...item2.properties,
                    source: "zhddzxYjdd",
                    zylx: "yjwz_yjdd",
                    newDis: _dis,
                  },
                  point: item2.geometry.coordinates.join(","),
                  lng: item2.geometry.coordinates[0],
                  lat: item2.geometry.coordinates[1],
                });
              }
            });

            yjwzPtTemp.pointData.sort((a, b) => {
              return a.data.newDis - b.data.newDis;
            }); //  升序
            if (yjwzPtTemp.pointData.length > 0) {
              if (middle.userCommonMap) {
                layercfgZhdd.yjwzLayer.data = yjwzPtTemp.pointData;
                layercfgZhdd.yjwzLayer.onclick = middle.funOnClickMapLayerEvent;
                top.mapUtil.loadPointLayer(layercfgZhdd.yjwzLayer);
              } else {
                middle.funMap(yjwzPtTemp);
              }
            }
            if (showList) {
              middle.funZyddKm(yjwzPtTemp.pointData, "material_n");
            }
          });
        },
        // 避灾安置点上图及初始化列表
        loadbzazdPtYjdd(params = {}) {
          let { coords = [], queryLimit, showList = true } = params;
          const typeNames = `poitest:bzazd`;
          let polygon = [];
          if (coords.length > 0) {
            if (coords.length >= 300) {
              // 多边形简化
              const geojsonPolygon = turf.polygon([coords]);
              coords = turf.simplify(geojsonPolygon, { tolerance: 0.1 })
                .geometry.coordinates[0];
            }
            coords.forEach((item, idx) => {
              const pt = item[0] + " " + item[1];
              polygon.push(pt);
            });
            polygon.push(polygon[0]);
          }

          let url =
            common.geoserver_server_url +
            "/geoserver/poitest/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=" +
            typeNames +
            "&maxFeatures=5000&outputFormat=application/json";
          if (queryLimit) {
            url +=
              "&cql_filter=within(the_geom,polygon((" +
              polygon.join(",") +
              ")))";
          } else {
            url +=
              "&bbox=119.22367661602989,28.52706653689894,120.77889703265598,29.68484864821428";
          }
          let bzazdPtTemp = {
            pointId: "zhdd_map_zydd_bzazd_yjtz",
            pointType: "zhdd_map_zydd_bzazd",
            setClick: true,
            size: [0.05, 0.1, 0.15, 0.2],
            imageConfig: { iconSize: 0.4 },
            funcName: "pointLoad",
            pointData: [],
          };

          middle.funClearPoint("zhdd_map_zydd_bzazd_yjtz");
          common.funSearchInfoByUrl({ url: url }, function (res) {
            const { totalFeatures, features } = res;
            features.forEach((item2) => {
              const _idx = item2.id.indexOf(".");
              const type = item2.id.substring(0, _idx);
              let _inJHPolygon = true; // 是否在金华边界内
              if (middle.jhGeojson && middle.jhGeojson.features) {
                _inJHPolygon = turf.booleanPointInPolygon(
                  item2,
                  middle.jhGeojson.features[0]
                );
              }
              if (
                type === "bzazd" &&
                item2.geometry &&
                item2.geometry.coordinates[0] !== 0 &&
                item2.geometry.coordinates[0] > 110 &&
                item2.properties["shelter_na"] !== "" &&
                _inJHPolygon
              ) {
                let _dis =
                  middle.getFlatternDistance(
                    middle.sgLat,
                    middle.sgLon,
                    item2.geometry.coordinates[1],
                    item2.geometry.coordinates[0]
                  ) / 1000; // km
                _dis = Number(_dis.toFixed(2));
                bzazdPtTemp.pointData.push({
                  data: {
                    ...item2.properties,
                    source: "zhddzxYjdd",
                    zylx: "bzazd_yjdd",
                    newDis: _dis,
                  },
                  point: item2.geometry.coordinates.join(","),
                  lng: item2.geometry.coordinates[0],
                  lat: item2.geometry.coordinates[1],
                });
              }
            });

            bzazdPtTemp.pointData.sort((a, b) => {
              return a.data.newDis - b.data.newDis;
            }); //  升序
            if (bzazdPtTemp.pointData.length > 0) {
              if (middle.userCommonMap) {
                layercfgZhdd.bzazdLayer.data = bzazdPtTemp.pointData;
                layercfgZhdd.bzazdLayer.onclick =
                  middle.funOnClickMapLayerEvent;
                top.mapUtil.loadPointLayer(layercfgZhdd.bzazdLayer);
              } else {
                middle.funMap(bzazdPtTemp);
              }
            }
            if (showList) {
              middle.funZyddKm(bzazdPtTemp.pointData, "shelter_na");
            }
          });
        },
        // 物联感知上图及初始化列表
        loadwlgzPtYjdd(params = {}) {
          let { coords = [], queryLimit = false, showList = true } = params;
          const typeNames = `poitest:wlgz`;
          let polygon = [];
          if (coords.length > 0) {
            if (coords.length >= 300) {
              // 多边形简化
              const geojsonPolygon = turf.polygon([coords]);
              coords = turf.simplify(geojsonPolygon, { tolerance: 0.1 })
                .geometry.coordinates[0];
            }
            coords.forEach((item, idx) => {
              const pt = item[0] + " " + item[1];
              polygon.push(pt);
            });
            polygon.push(polygon[0]);
          }

          let url =
            common.geoserver_server_url +
            "/geoserver/poitest/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=" +
            typeNames +
            "&maxFeatures=5000&outputFormat=application/json";
          if (queryLimit) {
            url +=
              "&cql_filter=within(the_geom,polygon((" +
              polygon.join(",") +
              ")))";
          } else {
            url +=
              "&bbox=119.22367661602989,28.52706653689894,120.77889703265598,29.68484864821428";
          }
          let wlgzPtTemp = {
            pointId: "zhdd_map_zydd_wlgz_yjtz",
            pointType: "zhdd_map_zydd_wlgz",
            setClick: true,
            size: [0.05, 0.1, 0.15, 0.2],
            imageConfig: { iconSize: 0.4 },
            funcName: "pointLoad",
            pointData: [],
          };

          middle.funClearPoint("zhdd_map_zydd_wlgz_yjtz");
          common.funSearchInfoByUrl({ url: url }, function (res) {
            const { totalFeatures, features } = res;
            features.forEach((item2) => {
              const _idx = item2.id.indexOf(".");
              const type = item2.id.substring(0, _idx);
              let _inJHPolygon = true; // 是否在金华边界内
              if (middle.jhGeojson && middle.jhGeojson.features) {
                _inJHPolygon = turf.booleanPointInPolygon(
                  item2,
                  middle.jhGeojson.features[0]
                );
              }
              if (
                type === "wlgz" &&
                item2.geometry &&
                item2.geometry.coordinates[0] !== 0 &&
                item2.geometry.coordinates[0] > 110 &&
                item2.properties["name"] !== "" &&
                _inJHPolygon
              ) {
                let _dis =
                  middle.getFlatternDistance(
                    middle.sgLat,
                    middle.sgLon,
                    item2.geometry.coordinates[1],
                    item2.geometry.coordinates[0]
                  ) / 1000; // km
                _dis = Number(_dis.toFixed(2));
                wlgzPtTemp.pointData.push({
                  data: {
                    ...item2.properties,
                    source: "zhddzxYjdd",
                    zylx: "wlgz_yjdd",
                    newDis: _dis,
                  },
                  point: item2.geometry.coordinates.join(","),
                  lng: item2.geometry.coordinates[0],
                  lat: item2.geometry.coordinates[1],
                });
              }
            });

            wlgzPtTemp.pointData.sort((a, b) => {
              return a.data.newDis - b.data.newDis;
            }); //  升序
            if (wlgzPtTemp.pointData.length > 0) {
              if (middle.userCommonMap) {
                layercfgZhdd.wlgzLayer.data = wlgzPtTemp.pointData;
                layercfgZhdd.wlgzLayer.onclick = middle.funOnClickMapLayerEvent;
                top.mapUtil.loadPointLayer(layercfgZhdd.wlgzLayer);
              } else {
                middle.funMap(wlgzPtTemp);
              }
            }
            if (showList) {
              middle.funZyddKm(wlgzPtTemp.pointData, "name");
            }
          });
        },
        // 网格人员上图接初始化列表
        loadwgryPtYjdd(params = {}) {
          let { coords = [], queryLimit = false, showList = true } = params;
          const typeNames = `poitest:wgry`;
          let polygon = [];
          if (coords.length > 0) {
            if (coords.length >= 300) {
              // 多边形简化
              const geojsonPolygon = turf.polygon([coords]);
              coords = turf.simplify(geojsonPolygon, { tolerance: 0.1 })
                .geometry.coordinates[0];
            }
            coords.forEach((item, idx) => {
              const pt = item[0] + " " + item[1];
              polygon.push(pt);
            });
            polygon.push(polygon[0]);
          }

          let url =
            common.geoserver_server_url +
            "/geoserver/poitest/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=" +
            typeNames +
            "&maxFeatures=5000&outputFormat=application/json";
          if (queryLimit) {
            url +=
              "&cql_filter=within(the_geom,polygon((" +
              polygon.join(",") +
              ")))";
          } else {
            url +=
              "&bbox=119.22367661602989,28.52706653689894,120.77889703265598,29.68484864821428";
          }
          let wgryPtTemp = {
            pointId: "zhdd_map_zydd_wgry_yjtz",
            pointType: "zhdd_map_zydd_wgry",
            setClick: true,
            size: [0.05, 0.1, 0.15, 0.2],
            imageConfig: { iconSize: 0.4 },
            funcName: "pointLoad",
            pointData: [],
          };

          middle.funClearPoint("zhdd_map_zydd_wgry_yjtz");
          common.funSearchInfoByUrl({ url: url }, function (res) {
            const { totalFeatures, features } = res;
            features.forEach((item2) => {
              const _idx = item2.id.indexOf(".");
              const type = item2.id.substring(0, _idx);
              let _inJHPolygon = true; // 是否在金华边界内
              if (middle.jhGeojson && middle.jhGeojson.features) {
                _inJHPolygon = turf.booleanPointInPolygon(
                  item2,
                  middle.jhGeojson.features[0]
                );
              }
              if (
                type === "wgry" &&
                item2.geometry &&
                item2.geometry.coordinates[0] !== 0 &&
                item2.geometry.coordinates[0] > 110 &&
                item2.properties["contacts_n"] !== "" &&
                _inJHPolygon
              ) {
                let _dis =
                  middle.getFlatternDistance(
                    middle.sgLat,
                    middle.sgLon,
                    item2.geometry.coordinates[1],
                    item2.geometry.coordinates[0]
                  ) / 1000; // km
                _dis = Number(_dis.toFixed(2));
                wgryPtTemp.pointData.push({
                  data: {
                    ...item2.properties,
                    source: "zhddzxYjdd",
                    zylx: "wgry_yjdd",
                    newDis: _dis,
                  },
                  point: item2.geometry.coordinates.join(","),
                  lng: item2.geometry.coordinates[0],
                  lat: item2.geometry.coordinates[1],
                });
              }
            });

            wgryPtTemp.pointData.sort((a, b) => {
              return a.data.newDis - b.data.newDis;
            }); //  升序
            if (wgryPtTemp.pointData.length > 0) {
              if (middle.userCommonMap) {
                layercfgZhdd.wgryLayer.data = wgryPtTemp.pointData;
                layercfgZhdd.wgryLayer.onclick = middle.funOnClickMapLayerEvent;
                top.mapUtil.loadPointLayer(layercfgZhdd.wgryLayer);
              } else {
                middle.funMap(wgryPtTemp);
              }
            }
            if (showList) {
              middle.funZyddKm(wgryPtTemp.pointData, "contacts_n");
            }
          });
        },
        // 行政执法人员上图及初始化列表
        loadxzzfryPtYjdd(params = {}) {
          console.log("执法人员上图");
          let { coords = [], queryLimit = false, showList = true } = params;
          const typeNames = `poitest:zfry`;
          let polygon = [];
          if (coords.length > 0) {
            if (coords.length >= 300) {
              // 多边形简化
              const geojsonPolygon = turf.polygon([coords]);
              coords = turf.simplify(geojsonPolygon, { tolerance: 0.1 })
                .geometry.coordinates[0];
            }
            coords.forEach((item, idx) => {
              const pt = item[0] + " " + item[1];
              polygon.push(pt);
            });
            polygon.push(polygon[0]);
          }

          let url =
            common.geoserver_server_url +
            "/geoserver/poitest/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=" +
            typeNames +
            "&maxFeatures=5000&outputFormat=application/json";
          if (queryLimit) {
            url +=
              "&cql_filter=within(the_geom,polygon((" +
              polygon.join(",") +
              "))) " +
              "&sys_depart<>''";
          } else {
            url +=
              "&bbox=119.22367661602989,28.52706653689894,120.77889703265598,29.68484864821428";
          }
          // else {
          //   url += "&cql_filter=sys_depart<>''"
          // }
          let zfryPtTemp = {
            pointId: "zhdd_map_zydd_xzzfry_yjtz",
            pointType: "zhdd_map_zydd_xzzfry",
            setClick: true,
            size: [0.05, 0.1, 0.15, 0.2],
            imageConfig: { iconSize: 0.4 },
            funcName: "pointLoad",
            pointData: [],
          };

          middle.funClearPoint("zhdd_map_zydd_xzzfry_yjtz");
          common.funSearchInfoByUrl({ url: url }, function (res) {
            const { totalFeatures, features } = res;
            features.forEach((item2) => {
              const _idx = item2.id.indexOf(".");
              const type = item2.id.substring(0, _idx);
              let _inJHPolygon = true; // 是否在金华边界内
              if (middle.jhGeojson && middle.jhGeojson.features) {
                _inJHPolygon = turf.booleanPointInPolygon(
                  item2,
                  middle.jhGeojson.features[0]
                );
              }
              if (
                type === "zfry" &&
                item2.geometry &&
                item2.geometry.coordinates[0] !== 0 &&
                item2.properties.sys_depart !== "" &&
                item2.geometry.coordinates[0] > 110 &&
                item2.properties["name"] !== "" &&
                _inJHPolygon
              ) {
                let _dis =
                  middle.getFlatternDistance(
                    middle.sgLat,
                    middle.sgLon,
                    item2.geometry.coordinates[1],
                    item2.geometry.coordinates[0]
                  ) / 1000; // km
                _dis = Number(_dis.toFixed(2));
                zfryPtTemp.pointData.push({
                  data: {
                    ...item2.properties,
                    source: "zhddzxYjdd",
                    zylx: "xzzfry_yjdd",
                    newDis: _dis,
                  },
                  point: item2.geometry.coordinates.join(","),
                  lng: item2.geometry.coordinates[0],
                  lat: item2.geometry.coordinates[1],
                });
              }
            });

            zfryPtTemp.pointData.sort((a, b) => {
              return a.data.newDis - b.data.newDis;
            }); //  升序
            if (zfryPtTemp.pointData.length > 0) {
              if (middle.userCommonMap) {
                layercfgZhdd.xzzfryLayer.data = zfryPtTemp.pointData;
                layercfgZhdd.xzzfryLayer.onclick =
                  middle.funOnClickMapLayerEvent;
                top.mapUtil.loadPointLayer(layercfgZhdd.xzzfryLayer);
              } else {
                middle.funMap(zfryPtTemp);
              }
            }
            if (showList) {
              middle.funZyddKm(zfryPtTemp.pointData, "name");
            }
          });
        },
        // 视频上图及初始化列表
        loadvideoPtYjdd(params = {}) {
          let { coords = [], queryLimit = false, showList = true } = params;
          const typeNames = `poitest:video`;
          let polygon = [];
          if (coords.length > 0) {
            if (coords.length >= 300) {
              // 多边形简化
              const geojsonPolygon = turf.polygon([coords]);
              coords = turf.simplify(geojsonPolygon, { tolerance: 0.1 })
                .geometry.coordinates[0];
            }
            coords.forEach((item, idx) => {
              const pt = item[0] + " " + item[1];
              polygon.push(pt);
            });
            polygon.push(polygon[0]);
          }

          let url =
            common.geoserver_server_url +
            "/geoserver/poitest/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=" +
            typeNames +
            "&maxFeatures=5000&outputFormat=application/json";
          if (queryLimit) {
            url +=
              "&cql_filter=within(the_geom,polygon((" +
              polygon.join(",") +
              ")))";
          } else {
            url +=
              "&bbox=119.22367661602989,28.52706653689894,120.77889703265598,29.68484864821428";
          }
          let videoPtTemp = {
            pointId: "zhdd_map_zydd_video_yjtz",
            pointType: "zhdd_map_zydd_spjk",
            setClick: true,
            size: [0.05, 0.1, 0.15, 0.2],
            imageConfig: { iconSize: 0.4 },
            funcName: "pointLoad",
            pointData: [],
          };

          middle.funClearPoint("zhdd_map_zydd_video_yjtz");
          common.funSearchInfoByUrl({ url: url }, function (res) {
            const { totalFeatures, features } = res;
            features.forEach((item2) => {
              const _idx = item2.id.indexOf(".");
              const type = item2.id.substring(0, _idx);
              let _inJHPolygon = true; // 是否在金华边界内
              if (middle.jhGeojson && middle.jhGeojson.features) {
                _inJHPolygon = turf.booleanPointInPolygon(
                  item2,
                  middle.jhGeojson.features[0]
                );
              }
              if (
                type === "video" &&
                item2.geometry &&
                item2.geometry.coordinates[0] !== 0 &&
                item2.geometry.coordinates[0] > 110 &&
                item2.properties["CHN_NAME"] !== "" &&
                _inJHPolygon
              ) {
                let _dis =
                  middle.getFlatternDistance(
                    middle.sgLat,
                    middle.sgLon,
                    item2.geometry.coordinates[1],
                    item2.geometry.coordinates[0]
                  ) / 1000; // km
                _dis = Number(_dis.toFixed(2));
                videoPtTemp.pointData.push({
                  data: {
                    ...item2.properties,
                    source: "zhddzxYjdd",
                    zylx: "video_yjdd",
                    newDis: _dis,
                  },
                  point: item2.geometry.coordinates.join(","),
                  lng: item2.geometry.coordinates[0],
                  lat: item2.geometry.coordinates[1],
                });
              }
            });

            videoPtTemp.pointData.sort((a, b) => {
              return a.data.newDis - b.data.newDis;
            }); //  升序
            if (videoPtTemp.pointData.length > 0) {
              if (middle.userCommonMap) {
                layercfgZhdd.videoLayer.data = videoPtTemp.pointData;
                layercfgZhdd.videoLayer.onclick =
                  middle.funOnClickMapLayerEvent;
                top.mapUtil.loadPointLayer(layercfgZhdd.videoLayer);
              } else {
                middle.funMap(videoPtTemp);
              }
            }
            if (showList) {
              middle.funZyddKm(videoPtTemp.pointData, "CHN_NAME");
            }
          });
        },
        // 应急广播上图及初始化列表
        loadyjgbPtYjdd(params = {}) {
          let { coords = [], queryLimit = false, showList = true } = params;
          const typeNames = `poitest:yjgb`;
          let polygon = [];
          if (coords.length > 0) {
            if (coords.length >= 300) {
              // 多边形简化
              const geojsonPolygon = turf.polygon([coords]);
              coords = turf.simplify(geojsonPolygon, { tolerance: 0.1 })
                .geometry.coordinates[0];
            }
            coords.forEach((item, idx) => {
              const pt = item[0] + " " + item[1];
              polygon.push(pt);
            });
            polygon.push(polygon[0]);
          }

          let url =
            common.geoserver_server_url +
            "/geoserver/poitest/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=" +
            typeNames +
            "&maxFeatures=5000&outputFormat=application/json";
          if (queryLimit) {
            url +=
              "&cql_filter=within(the_geom,polygon((" +
              polygon.join(",") +
              ")))";
          } else {
            url +=
              "&bbox=119.22367661602989,28.52706653689894,120.77889703265598,29.68484864821428";
          }
          let yjgbPtTemp = {
            pointId: "zhdd_map_zydd_yjgb_yjtz",
            pointType: "zhdd_map_zydd_yjgb",
            setClick: true,
            size: [0.05, 0.1, 0.15, 0.2],
            imageConfig: { iconSize: 0.4 },
            funcName: "pointLoad",
            pointData: [],
          };

          middle.funClearPoint("zhdd_map_zydd_yjgb_yjtz");
          common.funSearchInfoByUrl({ url: url }, function (res) {
            const { totalFeatures, features } = res;
            features.forEach((item2) => {
              const _idx = item2.id.indexOf(".");
              const type = item2.id.substring(0, _idx);
              let _inJHPolygon = true; // 是否在金华边界内
              if (middle.jhGeojson && middle.jhGeojson.features) {
                _inJHPolygon = turf.booleanPointInPolygon(
                  item2,
                  middle.jhGeojson.features[0]
                );
              }
              if (
                type === "yjgb" &&
                item2.geometry &&
                item2.geometry.coordinates[0] !== 0 &&
                item2.geometry.coordinates[0] > 110 &&
                item2.properties["GBMC"] !== "" &&
                _inJHPolygon
              ) {
                let _dis =
                  middle.getFlatternDistance(
                    middle.sgLat,
                    middle.sgLon,
                    item2.geometry.coordinates[1],
                    item2.geometry.coordinates[0]
                  ) / 1000; // km
                _dis = Number(_dis.toFixed(2));
                yjgbPtTemp.pointData.push({
                  data: {
                    ...item2.properties,
                    source: "zhddzxYjdd",
                    zylx: "yjgb_yjdd",
                    newDis: _dis,
                  },
                  point: item2.geometry.coordinates.join(","),
                  lng: item2.geometry.coordinates[0],
                  lat: item2.geometry.coordinates[1],
                });
              }
            });

            yjgbPtTemp.pointData.sort((a, b) => {
              return a.data.newDis - b.data.newDis;
            }); //  升序
            if (yjgbPtTemp.pointData.length > 0) {
              if (middle.userCommonMap) {
                layercfgZhdd.yjgbLayer.data = yjgbPtTemp.pointData;
                layercfgZhdd.yjgbLayer.onclick = middle.funOnClickMapLayerEvent;
                top.mapUtil.loadPointLayer(layercfgZhdd.yjgbLayer);
              } else {
                middle.funMap(yjgbPtTemp);
              }
            }
            if (showList) {
              middle.funZyddKm(yjgbPtTemp.pointData, "GBMC");
            }
          });
        },
        // 志愿者上图及初始化列表
        loadzyzPtYjdd(params = {}) {
          let { coords = [], queryLimit = false, showList = true } = params;
          const typeNames = `poitest:zyz`;
          let polygon = [];
          if (coords.length > 0) {
            if (coords.length >= 300) {
              // 多边形简化
              const geojsonPolygon = turf.polygon([coords]);
              coords = turf.simplify(geojsonPolygon, { tolerance: 0.1 })
                .geometry.coordinates[0];
            }
            coords.forEach((item, idx) => {
              const pt = item[0] + " " + item[1];
              polygon.push(pt);
            });
            polygon.push(polygon[0]);
          }

          let url =
            common.geoserver_server_url +
            "/geoserver/poitest/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=" +
            typeNames +
            "&maxFeatures=5000&outputFormat=application/json";
          if (queryLimit) {
            url +=
              "&cql_filter=within(the_geom,polygon((" +
              polygon.join(",") +
              ")))";
          } else {
            url +=
              "&bbox=119.22367661602989,28.52706653689894,120.77889703265598,29.68484864821428";
          }
          let zyzPtTemp = {
            pointId: "zhdd_map_zydd_zyz_yjtz",
            pointType: "zhdd_map_zydd_wgry",
            setClick: true,
            size: [0.05, 0.1, 0.15, 0.2],
            imageConfig: { iconSize: 0.4 },
            funcName: "pointLoad",
            pointData: [],
          };

          middle.funClearPoint("zhdd_map_zydd_zyz_yjtz");
          common.funSearchInfoByUrl({ url: url }, function (res) {
            const { totalFeatures, features } = res;
            features.forEach((item2) => {
              const _idx = item2.id.indexOf(".");
              const type = item2.id.substring(0, _idx);
              let _inJHPolygon = true; // 是否在金华边界内
              if (middle.jhGeojson && middle.jhGeojson.features) {
                _inJHPolygon = turf.booleanPointInPolygon(
                  item2,
                  middle.jhGeojson.features[0]
                );
              }
              if (
                type === "zyz" &&
                item2.geometry &&
                item2.geometry.coordinates[0] !== 0 &&
                item2.geometry.coordinates[0] > 110 &&
                item2.properties["mc"] !== "" &&
                _inJHPolygon
              ) {
                let _dis =
                  middle.getFlatternDistance(
                    middle.sgLat,
                    middle.sgLon,
                    item2.geometry.coordinates[1],
                    item2.geometry.coordinates[0]
                  ) / 1000; // km
                _dis = Number(_dis.toFixed(2));
                zyzPtTemp.pointData.push({
                  data: {
                    ...item2.properties,
                    source: "zhddzxYjdd",
                    zylx: "zyz_yjdd",
                    newDis: _dis,
                  },
                  point: item2.geometry.coordinates.join(","),
                  lng: item2.geometry.coordinates[0],
                  lat: item2.geometry.coordinates[1],
                });
              }
            });

            zyzPtTemp.pointData.sort((a, b) => {
              return a.data.newDis - b.data.newDis;
            }); //  升序
            if (zyzPtTemp.pointData.length > 0) {
              if (middle.userCommonMap) {
                layercfgZhdd.zyzLayer.data = zyzPtTemp.pointData;
                layercfgZhdd.zyzLayer.onclick = middle.funOnClickMapLayerEvent;
                top.mapUtil.loadPointLayer(layercfgZhdd.zyzLayer);
              } else {
                middle.funMap(zyzPtTemp);
              }
            }
            if (showList) {
              middle.funZyddKm(zyzPtTemp.pointData, "mc");
            }
          });
        },
        // 其他人员上图及初始化列表
        loadqtryPtYjdd(params = {}) {
          let { coords = [], queryLimit = false, showList = true } = params;
          const typeNames = `poitest:qtry`;
          let polygon = [];
          if (coords.length > 0) {
            if (coords.length >= 300) {
              // 多边形简化
              const geojsonPolygon = turf.polygon([coords]);
              coords = turf.simplify(geojsonPolygon, { tolerance: 0.1 })
                .geometry.coordinates[0];
            }
            coords.forEach((item, idx) => {
              const pt = item[0] + " " + item[1];
              polygon.push(pt);
            });
            polygon.push(polygon[0]);
          }

          let url =
            common.geoserver_server_url +
            "/geoserver/poitest/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=" +
            typeNames +
            "&maxFeatures=5000&outputFormat=application/json";
          if (queryLimit) {
            url +=
              "&cql_filter=within(the_geom,polygon((" +
              polygon.join(",") +
              ")))";
          } else {
            url +=
              "&bbox=119.22367661602989,28.52706653689894,120.77889703265598,29.68484864821428";
          }
          let qtryPtTemp = {
            pointId: "zhdd_map_zydd_qtry_yjtz",
            pointType: "zhdd_map_zydd_wgry",
            setClick: true,
            size: [0.05, 0.1, 0.15, 0.2],
            imageConfig: { iconSize: 0.4 },
            funcName: "pointLoad",
            pointData: [],
          };

          middle.funClearPoint("zhdd_map_zydd_qtry_yjtz");
          common.funSearchInfoByUrl({ url: url }, function (res) {
            const { totalFeatures, features } = res;
            features.forEach((item2) => {
              const _idx = item2.id.indexOf(".");
              const type = item2.id.substring(0, _idx);
              let _inJHPolygon = true; // 是否在金华边界内
              if (middle.jhGeojson && middle.jhGeojson.features) {
                _inJHPolygon = turf.booleanPointInPolygon(
                  item2,
                  middle.jhGeojson.features[0]
                );
              }
              if (
                type === "qtry" &&
                item2.geometry &&
                item2.geometry.coordinates[0] !== 0 &&
                item2.geometry.coordinates[0] > 110 &&
                item2.properties["mc"] !== "" &&
                _inJHPolygon
              ) {
                let _dis =
                  middle.getFlatternDistance(
                    middle.sgLat,
                    middle.sgLon,
                    item2.geometry.coordinates[1],
                    item2.geometry.coordinates[0]
                  ) / 1000; // km
                _dis = Number(_dis.toFixed(2));
                qtryPtTemp.pointData.push({
                  data: {
                    ...item2.properties,
                    source: "zhddzxYjdd",
                    zylx: "qtry_yjdd",
                    newDis: _dis,
                  },
                  point: item2.geometry.coordinates.join(","),
                  lng: item2.geometry.coordinates.join(","),
                  lat: item2.geometry.coordinates.join(","),
                });
              }
            });

            qtryPtTemp.pointData.sort((a, b) => {
              return a.data.newDis - b.data.newDis;
            }); //  升序
            if (qtryPtTemp.pointData.length > 0) {
              if (middle.userCommonMap) {
                layercfgZhdd.qtryLayer.data = qtryPtTemp.pointData;
                layercfgZhdd.qtryLayer.onclick = middle.funOnClickMapLayerEvent;
                top.mapUtil.loadPointLayer(layercfgZhdd.qtryLayer);
              } else {
                middle.funMap(qtryPtTemp);
              }
            }
            if (showList) {
              middle.funZyddKm(qtryPtTemp.pointData, "mc");
            }
          });
        },
        //  120急救车上图及初始化列表
        loadJjcPtYjdd(params = {}) {
          let { coords = [], queryLimit = false, showList = true } = params;
          const typeNames = `poitest:120car`;
          let polygon = [];
          if (coords.length > 0) {
            if (coords.length >= 300) {
              // 多边形简化
              const geojsonPolygon = turf.polygon([coords]);
              coords = turf.simplify(geojsonPolygon, { tolerance: 0.1 })
                .geometry.coordinates[0];
            }
            coords.forEach((item, idx) => {
              const pt = item[0] + " " + item[1];
              polygon.push(pt);
            });
            polygon.push(polygon[0]);
          }

          let url =
            common.geoserver_server_url +
            "/geoserver/poitest/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=" +
            typeNames +
            "&maxFeatures=5000&outputFormat=application/json";
          if (queryLimit) {
            url +=
              "&cql_filter=within(the_geom,polygon((" +
              polygon.join(",") +
              ")))";
          } else {
            url +=
              "&bbox=119.22367661602989,28.52706653689894,120.77889703265598,29.68484864821428";
          }
          let carPtTemp = {
            pointId: "zhdd_map_zydd_120car_yjtz",
            pointType: "zhdd_map_zydd_120car",
            setClick: true,
            size: [0.05, 0.1, 0.15, 0.2],
            imageConfig: { iconSize: 0.4 },
            funcName: "pointLoad",
            pointData: [],
          };

          middle.funClearPoint("zhdd_map_zydd_120car_yjtz");
          common.funSearchInfoByUrl({ url: url }, function (res) {
            const { totalFeatures, features } = res;
            features.forEach((item2) => {
              const _idx = item2.id.indexOf(".");
              const type = item2.id.substring(0, _idx);
              let _inJHPolygon = true; // 是否在金华边界内
              if (middle.jhGeojson && middle.jhGeojson.features) {
                _inJHPolygon = turf.booleanPointInPolygon(
                  item2,
                  middle.jhGeojson.features[0]
                );
              }
              if (
                type === "120car" &&
                item2.geometry &&
                item2.geometry.coordinates[0] !== 0 &&
                item2.geometry.coordinates[0] > 110 &&
                item2.properties["addr"] !== "" &&
                _inJHPolygon
              ) {
                let _dis =
                  middle.getFlatternDistance(
                    middle.sgLat,
                    middle.sgLon,
                    item2.geometry.coordinates[1],
                    item2.geometry.coordinates[0]
                  ) / 1000; // km
                _dis = Number(_dis.toFixed(2));
                carPtTemp.pointData.push({
                  data: {
                    ...item2.properties,
                    source: "zhddzxYjdd",
                    zylx: "120car_yjdd",
                    newDis: _dis,
                  },
                  point: item2.geometry.coordinates.join(","),
                  lng: item2.geometry.coordinates.join(","),
                  lat: item2.geometry.coordinates.join(","),
                });
              }
            });
            carPtTemp.pointData.sort((a, b) => {
              return a.data.newDis - b.data.newDis;
            }); //  升序
            if (carPtTemp.pointData.length > 0) {
              if (middle.userCommonMap) {
                layercfgZhdd.jjcLayer.data = carPtTemp.pointData;
                layercfgZhdd.jjcLayer.onclick = middle.funOnClickMapLayerEvent;
                top.mapUtil.loadPointLayer(layercfgZhdd.jjcLayer);
              } else {
                middle.funMap(carPtTemp);
              }
            }
            if (showList) {
              middle.funZyddKm(carPtTemp.pointData, "addr");
            }
          });
        },
        // 餐厨转运车上图及初始化列表
        loadCczycPtYjdd(params = {}) {
          let { coords = [], queryLimit = false, showList = true } = params;
          const typeNames = `poitest:cccar`;
          let polygon = [];
          if (coords.length > 0) {
            if (coords.length >= 300) {
              // 多边形简化
              const geojsonPolygon = turf.polygon([coords]);
              coords = turf.simplify(geojsonPolygon, { tolerance: 0.1 })
                .geometry.coordinates[0];
            }
            coords.forEach((item, idx) => {
              const pt = item[0] + " " + item[1];
              polygon.push(pt);
            });
            polygon.push(polygon[0]);
          }

          let url =
            common.geoserver_server_url +
            "/geoserver/poitest/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=" +
            typeNames +
            "&maxFeatures=5000&outputFormat=application/json";
          if (queryLimit) {
            url +=
              "&cql_filter=within(the_geom,polygon((" +
              polygon.join(",") +
              ")))";
          } else {
            url +=
              "&bbox=119.22367661602989,28.52706653689894,120.77889703265598,29.68484864821428";
          }
          let cczycPtTemp = {
            pointId: "zhdd_map_zydd_cczyc_yjtz",
            pointType: "zhdd_map_zydd_cczyc",
            setClick: true,
            size: [0.05, 0.1, 0.15, 0.2],
            imageConfig: { iconSize: 0.4 },
            funcName: "pointLoad",
            pointData: [],
          };

          middle.funClearPoint("zhdd_map_zydd_cczyc_yjtz");
          common.funSearchInfoByUrl({ url: url }, function (res) {
            const { totalFeatures, features } = res;
            features.forEach((item2) => {
              const _idx = item2.id.indexOf(".");
              const type = item2.id.substring(0, _idx);
              let _inJHPolygon = true; // 是否在金华边界内
              if (middle.jhGeojson && middle.jhGeojson.features) {
                _inJHPolygon = turf.booleanPointInPolygon(
                  item2,
                  middle.jhGeojson.features[0]
                );
              }
              if (
                type === "cccar" &&
                item2.geometry &&
                item2.geometry.coordinates[0] !== 0 &&
                item2.geometry.coordinates[0] > 110 &&
                item2.properties["carrier_na"] !== "" &&
                _inJHPolygon
              ) {
                let _dis =
                  middle.getFlatternDistance(
                    middle.sgLat,
                    middle.sgLon,
                    item2.geometry.coordinates[1],
                    item2.geometry.coordinates[0]
                  ) / 1000; // km
                _dis = Number(_dis.toFixed(2));
                cczycPtTemp.pointData.push({
                  data: {
                    ...item2.properties,
                    source: "zhddzxYjdd",
                    zylx: "cczyc_yjdd",
                    newDis: _dis,
                  },
                  point: item2.geometry.coordinates.join(","),
                  lng: item2.geometry.coordinates[0],
                  lat: item2.geometry.coordinates[1],
                });
              }
            });

            cczycPtTemp.pointData.sort((a, b) => {
              return a.data.newDis - b.data.newDis;
            }); //  升序
            if (cczycPtTemp.pointData.length > 0) {
              if (middle.userCommonMap) {
                layercfgZhdd.cczycLayer.data = cczycPtTemp.pointData;
                layercfgZhdd.cczycLayer.onclick =
                  middle.funOnClickMapLayerEvent;
                top.mapUtil.loadPointLayer(layercfgZhdd.cczycLayer);
              } else {
                middle.funMap(cczycPtTemp);
              }
            }
            if (showList) {
              middle.funZyddKm(cczycPtTemp.pointData, "carrier_na");
            }
          });
        },
        // 易涝点上图及初始化列表(geoserver)
        loadYldPtYjdd(params = {}) {
          let { coords = [], queryLimit = false, showList = true } = params;
          const typeNames = `poitest:yld`;
          let polygon = [];
          if (coords.length > 0) {
            if (coords.length >= 300) {
              // 多边形简化
              const geojsonPolygon = turf.polygon([coords]);
              coords = turf.simplify(geojsonPolygon, { tolerance: 0.1 })
                .geometry.coordinates[0];
            }
            coords.forEach((item, idx) => {
              const pt = item[0] + " " + item[1];
              polygon.push(pt);
            });
            polygon.push(polygon[0]);
          }

          let url =
            common.geoserver_server_url +
            "/geoserver/poitest/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=" +
            typeNames +
            "&maxFeatures=5000&outputFormat=application/json";
          if (queryLimit) {
            url +=
              "&cql_filter=within(the_geom,polygon((" +
              polygon.join(",") +
              ")))";
          } else {
            url +=
              "&bbox=119.22367661602989,28.52706653689894,120.77889703265598,29.68484864821428";
          }
          let yldPtTemp = {
            pointId: "zhdd_map_zydd_yld_yjtz",
            pointType: "zhdd_map_zydd_yld",
            setClick: true,
            size: [0.05, 0.1, 0.15, 0.2],
            imageConfig: { iconSize: 0.4 },
            funcName: "pointLoad",
            pointData: [],
          };

          middle.funClearPoint("zhdd_map_zydd_yld_yjtz");
          common.funSearchInfoByUrl({ url: url }, function (res) {
            const { totalFeatures, features } = res;
            features.forEach((item2) => {
              const _idx = item2.id.indexOf(".");
              const type = item2.id.substring(0, _idx);
              let _inJHPolygon = true; // 是否在金华边界内
              if (middle.jhGeojson && middle.jhGeojson.features) {
                _inJHPolygon = turf.booleanPointInPolygon(
                  item2,
                  middle.jhGeojson.features[0]
                );
              }
              if (
                type === "yld" &&
                item2.geometry &&
                item2.geometry.coordinates[0] !== 0 &&
                item2.geometry.coordinates[0] > 110 &&
                item2.properties["mc"] !== "" &&
                _inJHPolygon
              ) {
                let _dis =
                  middle.getFlatternDistance(
                    middle.sgLat,
                    middle.sgLon,
                    item2.geometry.coordinates[1],
                    item2.geometry.coordinates[0]
                  ) / 1000; // km
                _dis = Number(_dis.toFixed(2));
                yldPtTemp.pointData.push({
                  data: {
                    ...item2.properties,
                    source: "zhddzxYjdd",
                    zylx: "yld_yjdd",
                    newDis: _dis,
                  },
                  point: item2.geometry.coordinates.join(","),
                  lng: item2.geometry.coordinates[0],
                  lat: item2.geometry.coordinates[1],
                });
              }
            });

            yldPtTemp.pointData.sort((a, b) => {
              return a.data.newDis - b.data.newDis;
            }); //  升序
            if (yldPtTemp.pointData.length > 0) {
              if (middle.userCommonMap) {
                layercfgZhdd.yldLayer.data = yldPtTemp.pointData;
                layercfgZhdd.yldLayer.onclick = middle.funOnClickMapLayerEvent;
                top.mapUtil.loadPointLayer(layercfgZhdd.yldLayer);
              } else {
                middle.funMap(yldPtTemp);
              }
            }
            if (showList) {
              middle.funZyddKm(yldPtTemp.pointData, "mc");
            }
          });
        },
        // 一级指挥网格(geoserver)
        loadZhwgYjdd(params = {}) {
          let { coords = [], queryLimit = true } = params;
          const typeNames = `poitest:eventgrid`;
          let polygon = [];
          $("#wgddUl").html(""); // 网格列表置空
          if (coords.length > 2) {
            if (coords[0].toString() !== coords[coords.length - 1].toString()) {
              coords.push(coords[0]);
            }
            if (coords.length >= 300) {
              // 多边形简化
              const geojsonPolygon = turf.polygon([coords]);
              coords = turf.simplify(geojsonPolygon, { tolerance: 0.1 })
                .geometry.coordinates[0];
            }
            // 默认查询事故多边形中心点到多边形最远距离+0.5km 范围
            let polygonGeojson = turf.polygon([coords]);
            const polygonCenterLnglat =
              turf.centroid(polygonGeojson).geometry.coordinates;
            let centerToFurtheristLength = 0; // 中心点到多边形各点位最长距离
            coords.forEach((widthPoint) => {
              const line = turf.lineString([polygonCenterLnglat, widthPoint]);
              const length = turf.length(line, { units: "kilometers" });
              if (length > centerToFurtheristLength) {
                centerToFurtheristLength = length;
              }
            });
            centerToFurtheristLength = centerToFurtheristLength + 0.5; // km
            const circle = turf.circle(
              polygonCenterLnglat,
              centerToFurtheristLength,
              {
                steps: 64,
                units: "kilometers",
              }
            );
            polygon = circle.geometry.coordinates[0].flat();
          } else {
            return;
          }

          // let url = common.geoserver_server_url + "/geoserver/poitest/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=" + typeNames + "&maxFeatures=5000&outputFormat=application/json";
          // if (queryLimit) {
          //     url += "&cql_filter=within(the_geom,polygon((" + polygon.join(',') + ")))"
          // } else {
          //     url += "&bbox=119.22367661602989,28.52706653689894,120.77889703265598,29.68484864821428"
          // }
          let coordinates = []; // 坐标集合
          if (queryLimit) {
            coordinates = polygon;
          } else {
            coordinates = [
              119.22367661602989, 28.52706653689894, 120.77889703265598,
              28.52706653689894, 120.77889703265598, 29.68484864821428,
              119.22367661602989, 29.68484864821428, 119.22367661602989,
              28.52706653689894,
            ];
          }
          let posStr = "";
          for (let i = 0; i < coordinates.length; i = i + 2) {
            posStr += coordinates[i] + "," + coordinates[i + 1] + " ";
          }
          const filter =
            `<Filter xmlns="http://www.opengis.net/ogc" xmlns:gml="http://www.opengis.net/gml"><Intersects><PropertyName>the_geom</PropertyName><gml:Polygon><gml:outerBoundaryIs><gml:LinearRing><gml:coordinates>` +
            posStr +
            `</gml:coordinates></gml:LinearRing></gml:outerBoundaryIs></gml:Polygon></Intersects></Filter>`;
          let url =
            common.geoserver_server_url +
            "/geoserver/poitest/ows?service=WFS&request=GetFeature&version=1.0.0&typeName=" +
            typeNames +
            "&maxFeatures=5000&outputFormat=json&filter=" +
            filter;

          let zhwgTemp = []; // 网格数据

          common.funSearchInfoByUrl({ url: url, type: "post" }, function (res) {
            const { totalFeatures, features } = res;
            if (features.length == 0) return;
            features.forEach((item2) => {
              zhwgTemp.push({
                adcode: item2.properties.adcode,
                szqx: item2.properties.szqx,
              });
            });
            middle
              .getGridData({ data: zhwgTemp, useEliApi: false })
              .then((res) => {
                console.log(res);
                middle.funWgdd(res);
              });
          });
        },
        // 根据图层勾选加载事件资源/ 一键调度画圈根据事件类型图层查资源
        funloadSjZyBylx(params = {}) {
          const {
            checkZyArr,
            coords,
            initList = false,
            tabName = "",
            limit = true,
          } = params;
          // const _zyList = ['jydw', 'yjzj', 'yjwz', 'wlgz', 'wgry', 'zfry', 'spjk', 'bzazcs', 'yld', 'yjgb', 'jjcar', 'cczyc'];
          if (checkZyArr.length === 0) return;
          $("#zhdwUl").html("");
          checkZyArr.forEach((item) => {
            let _showList = initList && item == tabName ? true : false;
            if (item === "jydw") {
              this.loadjydwPtYjdd({
                coords,
                queryLimit: limit,
                showList: _showList,
              });
            } else if (item === "yjzj") {
              this.loadYjzjPtYjdd({
                coords,
                queryLimit: limit,
                showList: _showList,
              });
            } else if (item === "yjwz") {
              this.loadyjwzPtYjdd({
                coords,
                queryLimit: limit,
                showList: _showList,
              });
            } else if (item === "wlgz") {
              this.loadwlgzPtYjdd({
                coords,
                queryLimit: limit,
                showList: _showList,
              });
            } else if (item === "wgry" || item === "wgy") {
              this.loadwgryPtYjdd({
                coords,
                queryLimit: limit,
                showList: _showList,
              });
            } else if (item === "zfry") {
              this.loadxzzfryPtYjdd({
                coords,
                queryLimit: limit,
                showList: _showList,
              });
            } else if (item === "spjk") {
              this.loadvideoPtYjdd({
                coords,
                queryLimit: limit,
                showList: _showList,
              });
            } else if (item === "bzazcs" || item === "bzcs") {
              this.loadbzazdPtYjdd({
                coords,
                queryLimit: limit,
                showList: _showList,
              });
            } else if (item === "yld") {
              this.loadYldPtYjdd({
                coords,
                queryLimit: limit,
                showList: _showList,
              });
            } else if (item === "yjgb") {
              this.loadyjgbPtYjdd({
                coords,
                queryLimit: limit,
                showList: _showList,
              });
            } else if (item === "jjcar") {
              // 急救车
              // middle.loadJjcPtYjdd({coords, queryLimit: true, showList: _showList});
            } else if (item === "cczyc") {
              // middle.loadCczycPtYjdd({coords, queryLimit: true, showList: _showList});
            } else if (item === "bscg") {
              // 比赛场馆
              middle.funClearPoint("zhdd_map_zydd_bscg_yjtz");
              middle.loadBscgPtLayer();
            } else if (item === "jdbg") {
              // 酒店宾馆
              middle.funClearPoint("zhdd_map_zydd_jdbg_yjtz");
              middle.loadJdbgPtLayer();
            }
          });
        },
        close() {
          window.parent.frames["zhdd_middle"].vmMiddle.dispatchStatus = false;
          window.parent.lay.closeIframeByNames(["zhddDispatch"]);
        },
      },
    });
  </script>
</html>
