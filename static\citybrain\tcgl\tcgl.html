<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>图层管理</title>
  <link rel="stylesheet" href="/static/css/viewCss/index.css" />
  <script src="/Vue/vue.js"></script>
  <script src="/jquery/jquery-3.6.1.min.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <link rel="stylesheet" href="/elementui/css/index.css" />
  <script src="/elementui/js/index.js"></script>
  <script src="/Vue/vue-count-to.min.js"></script>
  <script src="/static/citybrain/zhdd/zhdd_page/zfy/zfjly_wyx/script.js"></script>

  <!-- <script type="text/javascript" src="/static/js/jslib/md5.js"></script>
  <script src="/static/citybrain/zhdd/zhdd_page/zfy/zfjly_lxs/poc_sdk.js"></script>
  <script src="/static/citybrain/zhdd/zhdd_page/zfy/zfjly_lxs/zfy_yws.js"></script> -->

  <style>
    .video_box {
      position: relative;
      width: 400px;
      height: 493px;
      background-image: url("/static/images/tcgl/bg.png");
      background-size: 100% 100%;
      overflow: hidden;
      padding: 20px 30px 30px 30px;
      box-sizing: border-box;
    }
    .tcgl-title {
      width: 270px;
      box-sizing: border-box;
      font-size: 32px;
      color: #fff;
      display: flex;
      margin-top: 10px;
    }
    .line {
      width: 600px;
      height: 40px;
      background-image: url(/static/images/tcgl/light.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: -88px 0;
    } /* 搜索框 */
    .topSou {
      width: 100%;
      height: 60px;
      margin-top: 40px;
      margin-bottom: 30px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .sou-css {
      width: 100px;
      height: 100%;
      font-size: 28px;
      line-height: 60px;
      text-align: center;
      color: #fff;
      border-radius: 10px;
      margin-left: 20px;
      background: url("/static/citybrain/tckz/img/tcgl/clear-bg.png")
      no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
    }
    .topSou .el-input-group {
      width: 100%;
      height: 100%;
      background: url("/static/citybrain/tckz/img/tcgl/search_bg.png")
      no-repeat;
      background-size: 700px 70px;
      background-position: -275px -5px;
      border: 1px solid #3e9bff;
      overflow: hidden;
    }
    .topSou button:hover {
      color: #fff !important;
    }
    .topSou button {
      width: 100% !important;
      height: 100% !important;
      color: #afdcfb !important;
      font-size: 1.2rem !important;
      border-radius: 0 !important;
      border-color: transparent !important;
    }
    .topSou .el-input-group__append {
      width: 60px;
      border-color: transparent;
      background-color: transparent;
      border-radius: 0;
      padding: 0;
      overflow: hidden;
    }
    .topSou .el-input__inner {
      border-radius: 0;
    }

    .topSou .el-input.is-active .el-input__inner,
    .el-input__inner:focus {
      border-color: transparent;
    }
    .topSou .el-input__inner:hover {
      border-color: transparent;
    }
    .el-input__inner {
      font-size: 30px !important;
      color: #ecebeb !important;
      height: 100%;
      border: none !important;
      background-color: transparent !important;
    } /* 搜索框叉的样式 */
    .el-input__suffix-inner .el-icon-circle-close.el-input__clear {
      /* margin-left: -190px; */
      margin-top: 3px;
      margin-right: -5px;
      color: #5ea8d3;
      font-size: 24px;
    }
    .el-checkbox__input {
      float: right;
      margin-right: 30px;
    }
    .el-tree-node__content {
      height: 50px !important;
      margin-bottom: 10px;
    }
    .is-focusable {
      background-color: unset;
    }
    .el-checkbox {
      display: block;
      border-radius: 15px;
      margin-bottom: 2px;
      margin-right: 0;
    }
    .el-checkbox-group .is-checked {
      background: linear-gradient(
              94deg,
              rgba(3, 97, 156, 0) 0%,
              #03619c 100%
      ) !important;
      border-radius: 0px 30px 30px 0px;
    }
    .el-checkbox__label {
      font-size: 30px;
      font-weight: bold;
      color: #c0d6ed;
      line-height: 58px;
    }
    .el-checkbox__inner {
      width: 33px;
      height: 33px;
      margin-top: 15px;
      background-color: #344d67;
    }
    .checkbox-box-img {
      width: 30px;
      height: 42px;
      position: relative;
      top: 10px;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: #252316;
      border-color: #ffc561;
    }

    .el-checkbox__inner::after {
      width: 7px;
      height: 18px;
      left: 10px;
      color: #ffc561 !important;
    }
    .shijian .contain::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }
    .shijian .contain::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background: #20aeff;
      height: 8px;
    }
    .el-icon-caret-left:before {
      font-size: 20px;
    }
    /* tree结构 */
    .el-tree {
      background-color: unset;
      margin-top: 10px;
    }
    .el-tree-node__content {
      /* /这里是我改的 原本是50，目的是为了让加的那两个下拉显示出来 */
      height: 35px !important;
      margin-bottom: 10px;
      padding: 0 !important;
    }
    .el-tree-node.is-current > .el-tree-node__content,
    .el-tree-node__content:hover {
      background: linear-gradient(
              94deg,
              rgba(3, 97, 156, 0) 0%,
              #03619c 100%
      ) !important;
      border-radius: 0px 30px 30px 0px;
    }
    .el-tree-node > .el-tree-node__children {
      margin-left: 30px;
    }
  </style>
</head>
<body>
<div id="app">
  <div class="video_box">
    <!-- 头 -->
    <div class="tcgl-title">
      <div class="arrow-css" style="margin-right: 10px">
        <img
                src="/static/images/tcgl/arrow.png"
                width="22px"
                height="24px"
                alt=""
        />
      </div>
      图层管理(
      <count-to :start-val="0" :end-val="2" :duration="1000"></count-to>
      )
    </div>
    <div class="line"></div>
    <el-tree
            node-key="id"
            :default-expanded-keys="[1,6]"
            :data="treeData"
            show-checkbox
            ref="tree"
            @check-change="checkChange"
            @check="checkArr"
    >
      <div style="display: flex" slot-scope="{ node, data }">
        <div
                style="
                line-height: 3.125rem;
                font-size: 30px;
                font-family: PangMenZhengDao;
                font-weight: bold;
                color: #c0d6ed;
                line-height: 58px;
              "
        >
          {{ node.label }}
        </div>
      </div>
    </el-tree>
    <!-- <el-checkbox-group v-model="checkList">
      <el-checkbox
        v-for="(item,index) in list"
        :key="index"
        :label="item"
        @change="changeList"
      ></el-checkbox>
    </el-checkbox-group> -->
  </div>
</div>
<script>
  var tcglVm = new Vue({
    el: "#app",
    data: {
      venuesList: [{ lng: 119.63847064547942, lat: 29.04367853193442 }],
      venuesList1: [{ lng: 119.68123346149268, lat: 29.081062703714288 }],
      venuesList2: [{ lng: 119.63028688672114, lat: 29.138030058453477 }],
      venuesList3: [{ lng: 119.66364999938686, lat: 29.104795668374507 }],
      treeData: localStorage.getItem("adminCity")=="金华市"?[
        {
          id: 1,
          label: "执法人员",
          Elabel: "zfry",
          children: [
            {
              id: 2,
              label: "在线",
              Elabel: "zfry",
              lineon: "1",
              icon: "zhdd_zfry_static", //绿色
            },
            {
              id: 3,
              label: "离线",
              Elabel: "zfry",
              lineon: "0",
              icon: "xzzf_grey", //灰色
            },
            // {
            //   id: 4,
            //   label: "其他",
            //   lineon: "2",
            //   icon: "zhdd_map_zydd_jydw", //红色
            // },
          ],
        },
        {
          id: 5,
          label: "执法站所",
          Elabel: "zfzs",
          icon: "学校c",
        },
        {
          id: 6,
          label: "城市风貌",
          Elabel: "yybz",
          children: [
            {
              id: 7,
              label: "风貌提升",
              Elabel: "yybz",
              // lineon: "1"
            },
            {
              id: 8,
              label: "主要场馆",
              Elabel: "yybz",
              icon: "video333", //绿色
            },
          ],
        },
        {
          id: 9,
          label: "执法车辆",
          Elabel: "zfcl",
          icon: "cl",
        },
      ]: [
        {
          id: 1,
          label: "执法人员",
          Elabel: "zfry",
          children: [
            {
              id: 2,
              label: "在线",
              Elabel: "zfry",
              lineon: "1",
              icon: "zhdd_zfry_static", //绿色
            },
            {
              id: 3,
              label: "离线",
              Elabel: "zfry",
              lineon: "0",
              icon: "xzzf_grey", //灰色
            },
          ],
        },
        {
          id: 5,
          label: "执法站所",
          Elabel: "zfzs",
          icon: "学校c",
        },
        {
          id: 9,
          label: "执法车辆",
          Elabel: "zfcl",
          icon: "cl",
        },
      ],
      time0: null,
      select:0,
      checkList: [],
      showZfryGJ: false,
      zfryGjLayer: null,
    },
    mounted() {
      let this_ = this;
      window.addEventListener("message", (e) => {
        console.log(e);
        if (e.data && e.data.tc_clear) {
          clearInterval(this_.time0);
          this_.time0 = null;
          window.parent.mapUtil.removeAllLayers([
            "zfry_point_在线",
            "zfry_point_离线",
            "zfry_point_其他",
            "zfry_point_执法站所",
            "zfry_pop",
            "zfry_point_终点",
            "zfry_point_起点",
            "zfry_point_lines",
            "zfcl_执法车辆",
            "yybz_point_jk1",

            "yybz_point_jk2",
            "yybz_point_jk3",
            "yybz_point_zsdtyg",
            "yybz_point_tyzx",
            "yybz_point_yyfc",
            "zfcl_lsgj",
            "zfcl_point_终点",
            "zfcl_point_起点",
          ]);
          try {
            this_.zfryPopClose();
            this_.yybzClose();
            // lxsVm.dialogBye();
          } catch {}
        }
        if (e.data && e.data.glDatas) {
          this_.zfryGl(e.data.glDatas);
        }
      });

      // setTimeout(() => {
      //   lxsVm.queryAuth(); //兰溪市执法仪获取数据
      // }, 4000);
    },
    methods: {
      checkChange(node, flag, arr) {
        if (flag) {
          if (node.label == "风貌提升") {
            this.yybzIframe(node.id);
          } else if (node.label == "执法人员"||node.label == "在线"||node.label == "离线"){
            if (this.select==0){
              this.setPoint(node);
            }
          } else {
            this.setPoint(node);
          }
        } else {
          if (node.label == "风貌提升") {
            this.yybzClose();
          } else {
            if (node.label == "主要场馆") {
              // this.removePoint(node);
              this.removePoint1("tyzx");
              this.removePoint1("zsdtyg");
              this.removePoint1("yyfc");
              this.removePoint1("jk1");
              this.removePoint1("jk2");
              this.removePoint1("jk3");
            } else {
              if (node.label == "执法车辆") {
                this.removePoint2("执法车辆");
                this.removePoint2("lsgj");
                this.removePoint2("point_终点");
                this.removePoint2("point_起点");
              } else if (node.label == "在线"||node.label == "离线"){
                //由于该图层加载较慢，先判断是否存在图层，再操作
                if (window.parent.mapUtil.layers[`${node.Elabel}_point_${node.label}`]) {
                  this.removePoint(node);
                  this.select=0;
                }else{
                  this.$refs.tree.setChecked(node.id,true);
                  this.select=1;
                }
              } else if (node.label == "执法人员"){
                if (window.parent.mapUtil.layers[`zfry_point_在线`]&&window.parent.mapUtil.layers[`zfry_point_离线`]) {
                  this.removePoint(node);
                  this.select=0;
                }else{
                  if (this.$refs.tree.getHalfCheckedNodes().indexOf(this.treeData[0])==-1){
                    this.$refs.tree.setChecked(node.id,true,true);
                    this.select=1;
                  }
                }
              } else {
                this.removePoint(node);
              }
            }
          }
        }
      },
      checkArr(data, node) {
        this.checkList = node.checkedNodes;
        let flag = node.checkedNodes.map((a) =>
                ["在线", "离线", "其他"].includes(a.label)
        );
        if (flag) {
          setTimeout(() => {
            // this.upDataZfryPoint();
          }, 3000);
        } else {
          clearInterval(this.time0);
          this.time0 = null;
        }
      },
      removePoint(item) {
        window.parent.mapUtil.removeLayer(`zfry_point_${item.label}`);
      },
      removePoint1(item) {
        window.parent.mapUtil.removeLayer(`yybz_point_${item}`);
      },
      removePoint2(item) {
        window.parent.mapUtil.removeLayer(`zfcl_${item}`);
      },
      setPoint(item) {
        if (localStorage.getItem("adminCity")=="金华市"){
          if (item.lineon) {
            $api("/xzzf_zfy", { lineon: item.lineon }).then((res) => {
              console.log(res);
              let pointAll = [];
              if (item.lineon == 1) {
                pointAll = res
                        .concat(window.parent.ywsVm.dataList)
                        .concat(window.parent.sbjVm.dataList)
                        .concat(window.parent.lxsVm.dataList)
                        .concat(window.parent.otherVm.dataList)
                        .concat(window.parent.newDyVm.dataList)
                        .filter(item => item.lineon == 1);
              } else {
                pointAll = res
                        .concat(window.parent.sbjVm.dataList)
                        .concat(window.parent.lxsVm.dataList)
                        .concat(window.parent.ywsVm.dataList)
                        .concat(window.parent.otherVm.dataList)
                        .concat(window.parent.newDyVm.dataList)
                        .filter(item => item.lineon == 0);
              }
              let pointData = [];
              pointAll.map((a) => {
                pointData.push({
                  name: (a.name && a.name) || "-",
                  phone: (a.phone && a.phone) || "-",
                  sys_depart: (a.sys_depart && a.sys_depart) || "-",
                  dept_name: (a.dept_name && a.dept_name) || "-",
                  lng: (a.lon && a.lon) || "-",
                  lat: (a.lat && a.lat) || "-",
                  county: (a.county && a.county) || "-",
                  lasttime: (a.lasttime && a.lasttime) || "-",
                  hostcode: (a.hostcode && a.hostcode) || "-",
                  type: (a.type && a.type) || "-",
                  laiyuan: (a.laiyuan && a.laiyuan) || "-",
                });
              });
              this.addPoint(
                      pointData,
                      `${item.Elabel}_point_${item.label}`,
                      item.icon
              );
            });
          }
          if (item.label == "主要场馆") {
            let url = `/geoserver/poitest/ows?service=WFS&request=GetFeature&version=1.0.0&typeName=poitest:video&maxFeatures=3000&outputFormat=json&filter=%3CFilter%20xmlns=%22http://www.opengis.net/ogc%22%20xmlns:gml=%22http://www.opengis.net/gml%22%3E%3CIntersects%3E%3CPropertyName%3Ethe_geom%3C/PropertyName%3E%3Cgml:Polygon%3E%3Cgml:outerBoundaryIs%3E%3Cgml:LinearRing%3E%3Cgml:coordinates%3E119.63850599490115,29.06155282055411%20119.63648908839177,29.061466196019875%20119.63449161587792,29.061207157093015%20119.63253282380728,29.06077819974596%20119.63063158535283,29.060183457160694%20119.62880621831029,29.05942865984354%20119.62707430838657,29.058521080329214%20119.62545253959286,29.057469463010527%20119.62395653338505,29.0562839397749%20119.62260069810802,29.05497593226575%20119.6213980901975,29.05355804171631%20119.62036028847841,29.05204392742353%20119.61949728276782,29.05044817503866%20119.61881737785198,29.048786155949557%20119.61832711375382,29.047073879114347%20119.61803120305072,29.04532783677868%20119.61793248583311,29.04356484556645%20119.61803190272558,29.041801884477394%20119.61832848621543,29.04005593135361%20119.61881937035737,29.038343799390628%20119.61949981874622,29.03668197526675%20119.62036327047355,29.03508646044772%20119.62140140361304,29.03357261719183%20119.62260421561123,29.032155020734578%20119.62396011980029,29.030847319071086%20119.62545605709619,29.02966210168113%20119.62707762180231,29.02861077845464%20119.62880920030571,29.02770346997724%20119.6306341213315,29.02694891022595%20119.63253481631295,29.026354362605947%20119.63449298833969,29.025925550131152%20119.63648978806671,29.025666600416194%20119.63850599490115,29.025580006005132%20119.64052220173556,29.025666600416194%20119.64251900146259,29.025925550131152%20119.64447717348934,29.026354362605947%20119.6463778684708,29.02694891022595%20119.6482027894966,29.02770346997724%20119.64993436799998,29.02861077845464%20119.65155593270612,29.02966210168113%20119.65305187000199,29.030847319071086%20119.65440777419107,29.032155020734578%20119.65561058618925,29.03357261719183%20119.65664871932874,29.03508646044772%20119.65751217105608,29.03668197526675%20119.65819261944492,29.038343799390628%20119.65868350358687,29.04005593135361%20119.65898008707671,29.041801884477394%20119.65907950396918,29.04356484556645%20119.65898078675157,29.04532783677868%20119.65868487604847,29.047073879114347%20119.65819461195032,29.048786155949557%20119.65751470703445,29.05044817503866%20119.6566517013239,29.05204392742353%20119.6556138996048,29.05355804171631%20119.65441129169426,29.05497593226575%20119.65305545641725,29.0562839397749%20119.65155945020945,29.057469463010527%20119.64993768141571,29.058521080329214%20119.648205771492,29.05942865984354%20119.64638040444947,29.060183457160694%20119.64447916599502,29.06077819974596%20119.64252037392438,29.061207157093015%20119.64052290141053,29.061466196019875%20119.63850599490115,29.06155282055411%20%3C/gml:coordinates%3E%3C/gml:LinearRing%3E%3C/gml:outerBoundaryIs%3E%3C/gml:Polygon%3E%3C/Intersects%3E%3C/Filter%3E`;
            axios.get(url).then((res) => {
              let result = res.data.features;
              let pointData = [];
              result.map((result) => {
                pointData.push({
                  lng:
                          (result.properties.gps_x && result.properties.gps_x) ||
                          "-",
                  lat:
                          (result.properties.gps_y && result.properties.gps_y) ||
                          "-",
                  CHN_NAME:
                          (result.properties.CHN_NAME &&
                                  result.properties.CHN_NAME) ||
                          "-",
                  CHN_CODE:
                          (result.properties.CHN_CODE &&
                                  result.properties.CHN_CODE) ||
                          "-",
                });
              });
              this.addPoint_yycgjqjkandcl(
                      pointData,
                      `yybz_point_jk1`,
                      item.icon,
                      50
              );
            });
            let url1 = `/geoserver/poitest/ows?service=WFS&request=GetFeature&version=1.0.0&typeName=poitest:video&maxFeatures=3000&outputFormat=json&filter=%3CFilter%20xmlns=%22http://www.opengis.net/ogc%22%20xmlns:gml=%22http://www.opengis.net/gml%22%3E%3CIntersects%3E%3CPropertyName%3Ethe_geom%3C/PropertyName%3E%3Cgml:Polygon%3E%3Cgml:outerBoundaryIs%3E%3Cgml:LinearRing%3E%3Cgml:coordinates%3E119.68131492298922,29.099785116246053%20119.67929726785312,29.099698491688123%20119.67729905394184,29.099439452691083%20119.67533953486198,29.099010495230065%20119.6734375908054,29.09841575249143%20119.67161154637834,29.097660954987415%20119.66987899382383,29.09675337525988%20119.66825662335118,29.095701757709886%20119.66676006221651,29.09451623423371%20119.66540372411086,29.09320822648402%20119.66420067031054,29.09179033570331%20119.66316248392884,29.090276221197396%20119.66229915847818,29.088680468625757%20119.66161900181145,29.08701844938338%20119.66112855636094,29.085306172434308%20119.66083253643308,29.083560130028566%20119.66073378315141,29.081797138792734%20119.66083323746827,29.08003417772745%20119.66112993149093,29.078288224673898%20119.66162099819081,29.076576092824904%20119.66230169938719,29.0749142688544%20119.66316547172178,29.07331875422222%20119.66420399016819,29.07180491117949%20119.665407248453,29.0703873149535%20119.66676365560467,29.0690796135305%20119.66826014769343,29.06789439638102%20119.6698823136817,29.066843073385762%20119.67161453417154,29.06593576512144%20119.67344013171468,29.06518120555692%20119.67534153124161,29.064586658090185%20119.67730042907206,29.064157845729287%20119.6792979688884,29.063898896084456%20119.68131492298922,29.063812301697073%20119.68333187709004,29.063898896084456%20119.68532941690638,29.064157845729287%20119.68728831473683,29.064586658090185%20119.68918971426378,29.06518120555692%20119.6910153118069,29.06593576512144%20119.69274753229675,29.066843073385762%20119.69436969828502,29.06789439638102%20119.69586619037378,29.0690796135305%20119.69722259752547,29.0703873149535%20119.69842585581026,29.07180491117949%20119.69946437425666,29.07331875422222%20119.70032814659127,29.0749142688544%20119.70100884778765,29.076576092824904%20119.70149991448753,29.078288224673898%20119.70179660851016,29.08003417772745%20119.70189606282702,29.081797138792734%20119.70179730954536,29.083560130028566%20119.7015012896175,29.085306172434308%20119.701010844167,29.08701844938338%20119.70033068750027,29.088680468625757%20119.6994673620496,29.090276221197396%20119.69842917566793,29.09179033570331%20119.6972261218676,29.09320822648402%20119.69586978376194,29.09451623423371%20119.69437322262728,29.095701757709886%20119.6927508521546,29.09675337525988%20119.69101829960009,29.097660954987415%20119.68919225517305,29.09841575249143%20119.68729031111648,29.099010495230065%20119.68533079203661,29.099439452691083%20119.68333257812534,29.099698491688123%20119.68131492298922,29.099785116246053%20%3C/gml:coordinates%3E%3C/gml:LinearRing%3E%3C/gml:outerBoundaryIs%3E%3C/gml:Polygon%3E%3C/Intersects%3E%3C/Filter%3E`;
            axios.get(url1).then((res) => {
              let result = res.data.features;
              let pointData = [];
              result.map((result) => {
                pointData.push({
                  lng:
                          (result.properties.gps_x && result.properties.gps_x) ||
                          "-",
                  lat:
                          (result.properties.gps_y && result.properties.gps_y) ||
                          "-",
                  CHN_NAME:
                          (result.properties.CHN_NAME &&
                                  result.properties.CHN_NAME) ||
                          "-",
                  CHN_CODE:
                          (result.properties.CHN_CODE &&
                                  result.properties.CHN_CODE) ||
                          "-",
                });
              });
              this.addPoint_yycgjqjkandcl(
                      pointData,
                      `yybz_point_jk2`,
                      item.icon,
                      50
              );
            });
            let url2 = `/geoserver/poitest/ows?service=WFS&request=GetFeature&version=1.0.0&typeName=poitest:video&maxFeatures=3000&outputFormat=json&filter=%3CFilter%20xmlns=%22http://www.opengis.net/ogc%22%20xmlns:gml=%22http://www.opengis.net/gml%22%3E%3CIntersects%3E%3CPropertyName%3Ethe_geom%3C/PropertyName%3E%3Cgml:Polygon%3E%3Cgml:outerBoundaryIs%3E%3Cgml:LinearRing%3E%3Cgml:coordinates%3E119.64449653842009,29.156846716190408%20119.64247776325355,29.15676009159708%20119.64047844012767,29.1565010524952%20119.63851783335957,29.15607209486393%20119.63661483364213,29.15547735189619%20119.63478777577093,29.154722554113004%20119.6330542617677,29.153814974067%20119.63143099111491,29.152763356171434%20119.62993359974647,29.151577832335914%20119.62857650935179,29.150269824226893%20119.62737278844938,29.148851933100683%20119.62633402656846,29.147337818276387%20119.62547022274984,29.145742065425715%20119.62478968943475,29.144080045954386%20119.62429897265997,29.142367768835218%20119.62400278931901,29.140621726324788%20119.62390398208099,29.13885873505369%20119.6240034923885,29.137095774023923%20119.62430035178039,29.135349821075287%20119.62479169160729,29.133637689396593%20119.62547277103218,29.131975865655207%20119.6263370230315,29.130380351302158%20119.62737611794078,29.128866508577858%20119.62858004392109,29.127448912697353%20119.6299372035621,29.126141211633616%20119.63143452568428,29.124955994843383%20119.63305759125932,29.123904672193532%20119.63479077223425,29.12299736424755%20119.63661738192475,29.122242804962035%20119.6385198355324,29.12164825772427%20119.64047981924831,29.121219445533498%20119.64247846632315,29.12096049599344%20119.64449653842009,29.12087390164143%20119.64651461051702,29.12096049599344%20119.64851325759183,29.121219445533498%20119.65047324130774,29.12164825772427%20119.65237569491542,29.122242804962035%20119.65420230460592,29.12299736424755%20119.65593548558086,29.123904672193532%20119.65755855115589,29.124955994843383%20119.65905587327808,29.126141211633616%20119.66041303291907,29.127448912697353%20119.66161695889936,29.128866508577858%20119.66265605380865,29.130380351302158%20119.66352030580798,29.131975865655207%20119.66420138523286,29.133637689396593%20119.66469272505977,29.135349821075287%20119.66498958445165,29.137095774023923%20119.66508909475917,29.13885873505369%20119.66499028752115,29.140621726324788%20119.66469410418019,29.142367768835218%20119.66420338740542,29.144080045954386%20119.66352285409033,29.145742065425715%20119.66265905027171,29.147337818276387%20119.66162028839078,29.148851933100683%20119.66041656748835,29.150269824226893%20119.65905947709369,29.151577832335914%20119.65756208572526,29.152763356171434%20119.65593881507246,29.153814974067%20119.65420530106921,29.154722554113004%20119.65237824319804,29.15547735189619%20119.65047524348057,29.15607209486393%20119.64851463671248,29.1565010524952%20119.64651531358662,29.15676009159708%20119.64449653842009,29.156846716190408%20%3C/gml:coordinates%3E%3C/gml:LinearRing%3E%3C/gml:outerBoundaryIs%3E%3C/gml:Polygon%3E%3C/Intersects%3E%3C/Filter%3E`;
            axios.get(url2).then((res) => {
              let result = res.data.features;
              let pointData = [];
              result.map((result) => {
                pointData.push({
                  lng:
                          (result.properties.gps_x && result.properties.gps_x) ||
                          "-",
                  lat:
                          (result.properties.gps_y && result.properties.gps_y) ||
                          "-",
                  CHN_NAME:
                          (result.properties.CHN_NAME &&
                                  result.properties.CHN_NAME) ||
                          "-",
                  CHN_CODE:
                          (result.properties.CHN_CODE &&
                                  result.properties.CHN_CODE) ||
                          "-",
                });
              });
              this.addPoint_yycgjqjkandcl(
                      pointData,
                      `yybz_point_jk3`,
                      item.icon,
                      50
              );
            });
            this.addPoint_yycgjqjkandcl(
                    this.venuesList,
                    `yybz_point_tyzx`,
                    "体育中心",
                    200
            );
            this.addPoint_yycgjqjkandcl(
                    this.venuesList1,
                    `yybz_point_yyfc`,
                    "亚运分村",
                    200
            );
            this.addPoint_yycgjqjkandcl(
                    this.venuesList2,
                    `yybz_point_zsdtyg`,
                    "浙师大体育馆",
                    200
            );
          }
          if (item.label == "执法站所") {
            $api("/csdn_yjyp14").then((res) => {
              let pointData = [];
              res.map((a) => {
                pointData.push({
                  street: (a.street && a.street) || "-",
                  address: (a.address && a.address) || "-",
                  lng: (a.longitude && a.longitude) || "-",
                  lat: (a.latitude && a.latitude) || "-",
                  station: (a.station && a.station) || "-",
                  image: (a.image && a.image) || "-",
                  num: (a.num && a.num) || "-",
                });
              });
              this.addPoint(pointData, `zfry_point_${item.label}`, item.icon);
            });
          }
          if (item.label == "执法车辆") {
            $api("/xzzfj_sy_tc_zfcl", {county: localStorage.getItem("city") == "金华市"?'':localStorage.getItem("city")}).then((res) => {
              let pointData = [];
              res.map((a) => {
                pointData.push({
                  lng: (a.lon && a.lon) || "-",
                  lat: (a.lat && a.lat) || "-",
                  name: (a.name && a.name) || "-",
                  carType: (a.carType && a.carType) || "-",
                });
              });
              this.addPoint_yycgjqjkandcl(
                      pointData,
                      `zfcl_${item.label}`,
                      item.icon,
                      40
              );
            });
          }
        }else{
          if (item.lineon) {
            $api("/xzzf_zfy", { lineon: item.lineon }).then((res) => {
              console.log(res);
              let pointAll = [];
              if (item.lineon == 1) {
                pointAll = res
                  .concat(window.parent.ywsVm.dataList)
                  .concat(window.parent.sbjVm.dataList)
                  .concat(window.parent.lxsVm.dataList)
                  .concat(window.parent.otherVm.dataList)
                  .concat(window.parent.newDyVm.dataList)
                  .filter(item => item.lineon == 1 && item.county == localStorage.getItem("adminCity"));
              } else {
                pointAll = res
                  .concat(window.parent.ywsVm.dataList)
                  .concat(window.parent.sbjVm.dataList)
                  .concat(window.parent.lxsVm.dataList)
                  .concat(window.parent.otherVm.dataList)
                  .concat(window.parent.newDyVm.dataList)
                  .filter(item => item.lineon == 0 && item.county == localStorage.getItem("adminCity"));
              }
              console.log(pointAll);
              let pointData = [];
              pointAll.map((a) => {
                pointData.push({
                  name: (a.name && a.name) || "-",
                  phone: (a.phone && a.phone) || "-",
                  sys_depart: (a.sys_depart && a.sys_depart) || "-",
                  dept_name: (a.dept_name && a.dept_name) || "-",
                  lng: (a.lon && a.lon) || "-",
                  lat: (a.lat && a.lat) || "-",
                  county: (a.county && a.county) || "-",
                  lasttime: (a.lasttime && a.lasttime) || "-",
                  hostcode: (a.hostcode && a.hostcode) || "-",
                  type: (a.type && a.type) || "-",
                  laiyuan: (a.laiyuan && a.laiyuan) || "-"
                });
              });
              this.addPoint(
                      pointData,
                      `${item.Elabel}_point_${item.label}`,
                      item.icon
              );
            });
          }
          if (item.label == "主要场馆") {
            let url = `/geoserver/poitest/ows?service=WFS&request=GetFeature&version=1.0.0&typeName=poitest:video&maxFeatures=3000&outputFormat=json&filter=%3CFilter%20xmlns=%22http://www.opengis.net/ogc%22%20xmlns:gml=%22http://www.opengis.net/gml%22%3E%3CIntersects%3E%3CPropertyName%3Ethe_geom%3C/PropertyName%3E%3Cgml:Polygon%3E%3Cgml:outerBoundaryIs%3E%3Cgml:LinearRing%3E%3Cgml:coordinates%3E119.63850599490115,29.06155282055411%20119.63648908839177,29.061466196019875%20119.63449161587792,29.061207157093015%20119.63253282380728,29.06077819974596%20119.63063158535283,29.060183457160694%20119.62880621831029,29.05942865984354%20119.62707430838657,29.058521080329214%20119.62545253959286,29.057469463010527%20119.62395653338505,29.0562839397749%20119.62260069810802,29.05497593226575%20119.6213980901975,29.05355804171631%20119.62036028847841,29.05204392742353%20119.61949728276782,29.05044817503866%20119.61881737785198,29.048786155949557%20119.61832711375382,29.047073879114347%20119.61803120305072,29.04532783677868%20119.61793248583311,29.04356484556645%20119.61803190272558,29.041801884477394%20119.61832848621543,29.04005593135361%20119.61881937035737,29.038343799390628%20119.61949981874622,29.03668197526675%20119.62036327047355,29.03508646044772%20119.62140140361304,29.03357261719183%20119.62260421561123,29.032155020734578%20119.62396011980029,29.030847319071086%20119.62545605709619,29.02966210168113%20119.62707762180231,29.02861077845464%20119.62880920030571,29.02770346997724%20119.6306341213315,29.02694891022595%20119.63253481631295,29.026354362605947%20119.63449298833969,29.025925550131152%20119.63648978806671,29.025666600416194%20119.63850599490115,29.025580006005132%20119.64052220173556,29.025666600416194%20119.64251900146259,29.025925550131152%20119.64447717348934,29.026354362605947%20119.6463778684708,29.02694891022595%20119.6482027894966,29.02770346997724%20119.64993436799998,29.02861077845464%20119.65155593270612,29.02966210168113%20119.65305187000199,29.030847319071086%20119.65440777419107,29.032155020734578%20119.65561058618925,29.03357261719183%20119.65664871932874,29.03508646044772%20119.65751217105608,29.03668197526675%20119.65819261944492,29.038343799390628%20119.65868350358687,29.04005593135361%20119.65898008707671,29.041801884477394%20119.65907950396918,29.04356484556645%20119.65898078675157,29.04532783677868%20119.65868487604847,29.047073879114347%20119.65819461195032,29.048786155949557%20119.65751470703445,29.05044817503866%20119.6566517013239,29.05204392742353%20119.6556138996048,29.05355804171631%20119.65441129169426,29.05497593226575%20119.65305545641725,29.0562839397749%20119.65155945020945,29.057469463010527%20119.64993768141571,29.058521080329214%20119.648205771492,29.05942865984354%20119.64638040444947,29.060183457160694%20119.64447916599502,29.06077819974596%20119.64252037392438,29.061207157093015%20119.64052290141053,29.061466196019875%20119.63850599490115,29.06155282055411%20%3C/gml:coordinates%3E%3C/gml:LinearRing%3E%3C/gml:outerBoundaryIs%3E%3C/gml:Polygon%3E%3C/Intersects%3E%3C/Filter%3E`;
            axios.get(url).then((res) => {
              let result = res.data.features;
              let pointData = [];
              result.map((result) => {
                pointData.push({
                  lng:
                          (result.properties.gps_x && result.properties.gps_x) ||
                          "-",
                  lat:
                          (result.properties.gps_y && result.properties.gps_y) ||
                          "-",
                  CHN_NAME:
                          (result.properties.CHN_NAME &&
                                  result.properties.CHN_NAME) ||
                          "-",
                  CHN_CODE:
                          (result.properties.CHN_CODE &&
                                  result.properties.CHN_CODE) ||
                          "-",
                });
              });
              this.addPoint_yycgjqjkandcl(
                      pointData,
                      `yybz_point_jk1`,
                      item.icon,
                      50
              );
            });
            let url1 = `/geoserver/poitest/ows?service=WFS&request=GetFeature&version=1.0.0&typeName=poitest:video&maxFeatures=3000&outputFormat=json&filter=%3CFilter%20xmlns=%22http://www.opengis.net/ogc%22%20xmlns:gml=%22http://www.opengis.net/gml%22%3E%3CIntersects%3E%3CPropertyName%3Ethe_geom%3C/PropertyName%3E%3Cgml:Polygon%3E%3Cgml:outerBoundaryIs%3E%3Cgml:LinearRing%3E%3Cgml:coordinates%3E119.68131492298922,29.099785116246053%20119.67929726785312,29.099698491688123%20119.67729905394184,29.099439452691083%20119.67533953486198,29.099010495230065%20119.6734375908054,29.09841575249143%20119.67161154637834,29.097660954987415%20119.66987899382383,29.09675337525988%20119.66825662335118,29.095701757709886%20119.66676006221651,29.09451623423371%20119.66540372411086,29.09320822648402%20119.66420067031054,29.09179033570331%20119.66316248392884,29.090276221197396%20119.66229915847818,29.088680468625757%20119.66161900181145,29.08701844938338%20119.66112855636094,29.085306172434308%20119.66083253643308,29.083560130028566%20119.66073378315141,29.081797138792734%20119.66083323746827,29.08003417772745%20119.66112993149093,29.078288224673898%20119.66162099819081,29.076576092824904%20119.66230169938719,29.0749142688544%20119.66316547172178,29.07331875422222%20119.66420399016819,29.07180491117949%20119.665407248453,29.0703873149535%20119.66676365560467,29.0690796135305%20119.66826014769343,29.06789439638102%20119.6698823136817,29.066843073385762%20119.67161453417154,29.06593576512144%20119.67344013171468,29.06518120555692%20119.67534153124161,29.064586658090185%20119.67730042907206,29.064157845729287%20119.6792979688884,29.063898896084456%20119.68131492298922,29.063812301697073%20119.68333187709004,29.063898896084456%20119.68532941690638,29.064157845729287%20119.68728831473683,29.064586658090185%20119.68918971426378,29.06518120555692%20119.6910153118069,29.06593576512144%20119.69274753229675,29.066843073385762%20119.69436969828502,29.06789439638102%20119.69586619037378,29.0690796135305%20119.69722259752547,29.0703873149535%20119.69842585581026,29.07180491117949%20119.69946437425666,29.07331875422222%20119.70032814659127,29.0749142688544%20119.70100884778765,29.076576092824904%20119.70149991448753,29.078288224673898%20119.70179660851016,29.08003417772745%20119.70189606282702,29.081797138792734%20119.70179730954536,29.083560130028566%20119.7015012896175,29.085306172434308%20119.701010844167,29.08701844938338%20119.70033068750027,29.088680468625757%20119.6994673620496,29.090276221197396%20119.69842917566793,29.09179033570331%20119.6972261218676,29.09320822648402%20119.69586978376194,29.09451623423371%20119.69437322262728,29.095701757709886%20119.6927508521546,29.09675337525988%20119.69101829960009,29.097660954987415%20119.68919225517305,29.09841575249143%20119.68729031111648,29.099010495230065%20119.68533079203661,29.099439452691083%20119.68333257812534,29.099698491688123%20119.68131492298922,29.099785116246053%20%3C/gml:coordinates%3E%3C/gml:LinearRing%3E%3C/gml:outerBoundaryIs%3E%3C/gml:Polygon%3E%3C/Intersects%3E%3C/Filter%3E`;
            axios.get(url1).then((res) => {
              let result = res.data.features;
              let pointData = [];
              result.map((result) => {
                pointData.push({
                  lng:
                          (result.properties.gps_x && result.properties.gps_x) ||
                          "-",
                  lat:
                          (result.properties.gps_y && result.properties.gps_y) ||
                          "-",
                  CHN_NAME:
                          (result.properties.CHN_NAME &&
                                  result.properties.CHN_NAME) ||
                          "-",
                  CHN_CODE:
                          (result.properties.CHN_CODE &&
                                  result.properties.CHN_CODE) ||
                          "-",
                });
              });
              this.addPoint_yycgjqjkandcl(
                      pointData,
                      `yybz_point_jk2`,
                      item.icon,
                      50
              );
            });
            let url2 = `/geoserver/poitest/ows?service=WFS&request=GetFeature&version=1.0.0&typeName=poitest:video&maxFeatures=3000&outputFormat=json&filter=%3CFilter%20xmlns=%22http://www.opengis.net/ogc%22%20xmlns:gml=%22http://www.opengis.net/gml%22%3E%3CIntersects%3E%3CPropertyName%3Ethe_geom%3C/PropertyName%3E%3Cgml:Polygon%3E%3Cgml:outerBoundaryIs%3E%3Cgml:LinearRing%3E%3Cgml:coordinates%3E119.64449653842009,29.156846716190408%20119.64247776325355,29.15676009159708%20119.64047844012767,29.1565010524952%20119.63851783335957,29.15607209486393%20119.63661483364213,29.15547735189619%20119.63478777577093,29.154722554113004%20119.6330542617677,29.153814974067%20119.63143099111491,29.152763356171434%20119.62993359974647,29.151577832335914%20119.62857650935179,29.150269824226893%20119.62737278844938,29.148851933100683%20119.62633402656846,29.147337818276387%20119.62547022274984,29.145742065425715%20119.62478968943475,29.144080045954386%20119.62429897265997,29.142367768835218%20119.62400278931901,29.140621726324788%20119.62390398208099,29.13885873505369%20119.6240034923885,29.137095774023923%20119.62430035178039,29.135349821075287%20119.62479169160729,29.133637689396593%20119.62547277103218,29.131975865655207%20119.6263370230315,29.130380351302158%20119.62737611794078,29.128866508577858%20119.62858004392109,29.127448912697353%20119.6299372035621,29.126141211633616%20119.63143452568428,29.124955994843383%20119.63305759125932,29.123904672193532%20119.63479077223425,29.12299736424755%20119.63661738192475,29.122242804962035%20119.6385198355324,29.12164825772427%20119.64047981924831,29.121219445533498%20119.64247846632315,29.12096049599344%20119.64449653842009,29.12087390164143%20119.64651461051702,29.12096049599344%20119.64851325759183,29.121219445533498%20119.65047324130774,29.12164825772427%20119.65237569491542,29.122242804962035%20119.65420230460592,29.12299736424755%20119.65593548558086,29.123904672193532%20119.65755855115589,29.124955994843383%20119.65905587327808,29.126141211633616%20119.66041303291907,29.127448912697353%20119.66161695889936,29.128866508577858%20119.66265605380865,29.130380351302158%20119.66352030580798,29.131975865655207%20119.66420138523286,29.133637689396593%20119.66469272505977,29.135349821075287%20119.66498958445165,29.137095774023923%20119.66508909475917,29.13885873505369%20119.66499028752115,29.140621726324788%20119.66469410418019,29.142367768835218%20119.66420338740542,29.144080045954386%20119.66352285409033,29.145742065425715%20119.66265905027171,29.147337818276387%20119.66162028839078,29.148851933100683%20119.66041656748835,29.150269824226893%20119.65905947709369,29.151577832335914%20119.65756208572526,29.152763356171434%20119.65593881507246,29.153814974067%20119.65420530106921,29.154722554113004%20119.65237824319804,29.15547735189619%20119.65047524348057,29.15607209486393%20119.64851463671248,29.1565010524952%20119.64651531358662,29.15676009159708%20119.64449653842009,29.156846716190408%20%3C/gml:coordinates%3E%3C/gml:LinearRing%3E%3C/gml:outerBoundaryIs%3E%3C/gml:Polygon%3E%3C/Intersects%3E%3C/Filter%3E`;
            axios.get(url2).then((res) => {
              let result = res.data.features;
              let pointData = [];
              result.map((result) => {
                pointData.push({
                  lng:
                          (result.properties.gps_x && result.properties.gps_x) ||
                          "-",
                  lat:
                          (result.properties.gps_y && result.properties.gps_y) ||
                          "-",
                  CHN_NAME:
                          (result.properties.CHN_NAME &&
                                  result.properties.CHN_NAME) ||
                          "-",
                  CHN_CODE:
                          (result.properties.CHN_CODE &&
                                  result.properties.CHN_CODE) ||
                          "-",
                });
              });
              this.addPoint_yycgjqjkandcl(
                      pointData,
                      `yybz_point_jk3`,
                      item.icon,
                      50
              );
            });
            this.addPoint_yycgjqjkandcl(
                    this.venuesList,
                    `yybz_point_tyzx`,
                    "体育中心",
                    200
            );
            this.addPoint_yycgjqjkandcl(
                    this.venuesList1,
                    `yybz_point_yyfc`,
                    "亚运分村",
                    200
            );
            this.addPoint_yycgjqjkandcl(
                    this.venuesList2,
                    `yybz_point_zsdtyg`,
                    "浙师大体育馆",
                    200
            );
          }
          if (item.label == "执法站所") {
            $api("/csdn_yjyp14").then((res) => {
              let pointAll1 = [];
              // \\过滤本县市区的点位数据
              pointAll1 = res.filter(item => item.station.includes(localStorage.getItem("adminCity")));
              let pointData = [];
              pointAll1.map((a) => {
                pointData.push({
                  street: (a.street && a.street) || "-",
                  address: (a.address && a.address) || "-",
                  lng: (a.longitude && a.longitude) || "-",
                  lat: (a.latitude && a.latitude) || "-",
                  station: (a.station && a.station) || "-",
                  image: (a.image && a.image) || "-",
                  num: (a.num && a.num) || "-",
                });
              });
              this.addPoint(pointData, `zfry_point_${item.label}`, item.icon);
            });
          }
          if (item.label == "执法车辆") {
            if (localStorage.getItem("adminCity")=="义乌市"){
              $api("/xzzfj_sy_tc_zfcl", {county: localStorage.getItem("city")}).then((res) => {
                let pointData = [];
                res.map((a) => {
                  pointData.push({
                    lng: (a.lon && a.lon) || "-",
                    lat: (a.lat && a.lat) || "-",
                    name: (a.name && a.name) || "-",
                    carType: (a.carType && a.carType) || "-",
                  });
                });
                this.addPoint_yycgjqjkandcl(
                        pointData,
                        `zfcl_${item.label}`,
                        item.icon,
                        40
                );
              });
            }
          }
        }
      },
      addPoint(pointData, layerid, icon, callBack) {
        let this_ = this;
        window.parent.mapUtil.loadPointLayer({
          data: pointData, //点位数据
          layerid: layerid, //图层id
          iconcfg: {
            image: `/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${icon}.png`,
            iconSize: 64,
          },
          popcfg: {
            offset: [50, -100],
            show: false, //关闭按钮
          },
          cluster: false,
          onclick: layerid.includes("站所")
                  ? this_.zfzsIframe
                  : this_.zfryPop,
        });
      },
      // 加载亚运场馆及其监控和车辆
      addPoint_yycgjqjkandcl(pointData, layerid, icon, iconSize, callBack) {
        let this_ = this;
        window.parent.mapUtil.loadPointLayer1({
          data: pointData, //点位数据
          layerid: layerid, //图层id
          iconcfg: {
            image: `/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${icon}.png`,
            iconSize: iconSize,
          },
          popcfg: {
            offset: [50, -100],
            show: false, //关闭按钮
          },
          cluster: false,
          onclick: layerid.includes("zfcl")
                  ? this_.zfclPop
                  : layerid.includes("jk")
                          ? this_.zfzsIframe1
                          : "",
        });
      },
      //更新执法人员点位图层
      // upDataZfryPoint() {
      //   this.time0 = setInterval(() => {
      //     this.checkList.map((a) => {
      //       testQueryPointsByQx(a);
      //     });
      //     function testQueryPointsByQx(item) {
      //       $api("/xzzf_zfy", { lineon: item.lineon }).then((res) => {
      //         let pointData = [];
      //         res.map((a) => {
      //           pointData.push({
      //             name: (a.name && a.name) || "-",
      //             phone: (a.phone && a.phone) || "-",
      //             sys_depart: (a.sys_depart && a.sys_depart) || "-",
      //             dept_name: (a.dept_name && a.dept_name) || "-",
      //             lng: (a.lon && a.lon) || "-",
      //             lat: (a.lat && a.lat) || "-",
      //             county: (a.county && a.county) || "-",
      //             hostcode: (a.hostcode && a.hostcode) || "-",
      //           });
      //         });
      //         window.parent.gisHelper.refreshPosition({
      //           layerid: "zfry_point_" + item.label, //需要s定时更新位置的执法人员图层id
      //           data: pointData, //最新的执法记录仪的位置信息
      //         });
      //       });
      //     }
      //   }, 50000);
      // },

      //城市风貌
      yybzIframe(nodeID) {
        this.hideIframe();
        window.parent.lay.openIframe({
          type: "openIframe", //指令
          name: "yybzDetail",
          src:
                  baseURL.url + "/static/citybrain/commonts/tcgl/yybzDetail.html",
          left: "120px",
          top: "680px",
          width: "700px",
          // height: "1400px",
          height: "920px",
          zIndex: 667,
          argument: { nodeID: nodeID },
        });
      },
      yybzClose() {
        window.parent.mapUtil.removeAllLayers(["yybz_point_event"]);
        window.parent.lay.closeIframeByNames(["yybzDetail"]);
        this.showIframe()
        try {
          window.parent.lay.closeIframeByNames(["yybzPoint"]);
        } catch {}
      },

      // 执法人员弹窗
      zfryPop(e) {
        let this_ = this;
        let str = `
                                          <div
                                        style="
                                          position: relative;
                                          background: url('/static/citybrain/csdn/img/du_bg2.png') no-repeat;
                                          background-size: 100% 100%;
                                          width: max-content;
                                          min-height: 250px;
                                        "
                                      >
                                        <nav
                                          class="s-flex s-m-l-20 s-m-r-20 s-row-between s-p-b-10 s-col-bottom"
                                          style="
                                            border-bottom: 1px solid;
                                            border-bottom: 2px solid;
                                            border-image: linear-gradient(-90deg, rgba(0, 216, 247, 0) 0%, #00afed 100%) 2 2 2 2;
                                            padding-left: 20px;
                                          "
                                        >
                                      <h2 style="margin-top: 20px; white-space: nowrap;color: #fff;font-size:35px;" class="s-flex">执法人员详情</h2>
                                          <span
                                            class="s-m-l-20 s-font-30 s-c-white"
                                            style="cursor: pointer"
                                            onclick="window.parent.frames['tckz_tcgl3840'].tcglVm.zfryPopClose()"
                                          >
                                            <img style="vertical-align: middle;" src="/static/citybrain/csdn/img/close.png" alt="" />
                                          </span>
                                        </nav>

                                        <header class="s-m-l-20 s-m-r-20 s-m-t-10 s-flex s-row-between s-font-30" style="padding-bottom: 13%;">
                                            <div class="s-m-l-40">
                                              <p style="width: 700px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">队员姓名 ：<span style="color: #ffff;">${
                e.name
        }</span></p>
                                              <p style="width: 700px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">所属部门 ：<span style="color: #ffff;">${
                e.dept_name
        }</span></p>
                                              <p style="width: 700px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">设备编号 ：<span style="color: #ffff;">${
                e.sys_depart || "--"
        }</span></p>
                                              <p style="width: 700px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">GPS录入时间 ：<span style="color: #ffff;">${
                e.lasttime || "--"
        }</span></p>
                                              <p class="s-font-30 s-flex s-m-t-10 s-c-white s-flex-wrap s-row-evenly">
                                                <span style="cursor:${
                e.sys_depart
                        ? "pointer"
                        : "no-drop"
        };background:#00c4fc;padding: 2px 20px;border-radius: 15px;" onclick="window.parent.frames['tckz_tcgl3840'].tcglVm.zfryPopFun({type:'调度呼叫',
                                                sys_depart:'${e.sys_depart}',
                                                county:'${e.county}',
                                                hostcode:'${e.hostcode}',
                                                type2:'${e.type}',
                                                laiyuan:'${e.laiyuan}'})"
                                                id="ddhjBtn" >调度呼叫</span>
                                                <span style="cursor:${
                e.sys_depart
                        ? "pointer"
                        : "no-drop"
        };background:#00c4fc;padding: 2px 20px;border-radius: 15px;" onclick="window.parent.frames['tckz_tcgl3840'].tcglVm.zfryPopFun({type:'查看直播',
                                                sys_depart:'${e.sys_depart}',
                                                county:'${e.county}',
                                                hostcode:'${e.hostcode}',
                                                type2:'${e.type}',
                                                laiyuan:'${e.laiyuan}'})"
                                                id="ckzbBtn">查看直播</span>
                                              </p>
                                              <p class="s-font-30 s-flex s-m-t-15 s-c-white s-flex-wrap s-row-evenly">
                                                <span onclick="window.parent.frames['tckz_tcgl3840'].tcglVm.zfryPopFun({type:'人员轨迹',
                                                sys_depart:'${e.sys_depart}',
                                                county:'${e.county}'})"
                                                  id="rygjBtn" style="cursor:pointer;background:#00c4fc;padding: 2px 20px;border-radius: 15px;">人员轨迹</span>
                                                <span onclick="window.parent.frames['tckz_tcgl3840'].tcglVm.zfryPopFun('接收告警')" id="jsgjBtn" style="cursor:no-drop;background:#7e8b8e;padding: 2px 20px;border-radius: 15px;">接受告警</span>
                                              </p>
                                            </div>
                                        </header>
                                      </div>
                                    `;
        let objData = {
          layerid: "zfry_pop",
          position: [e.esX, e.esY],
          offset: [60, -20],
          content: str,
        };
        window.parent.mapUtil._createPopup(objData);
      },
      zfryPopFun(item) {
        let typeName = item.type == "调度呼叫" ? "ddhjVideoTest" : item.type == "查看直播" ? "openVideoTest" : "";
        if (item.type == "人员轨迹") {
          this.showZfryGJ = !this.showZfryGJ;
          this.hideIframe();
          if (this.showZfryGJ) {
            window.parent.lay.openIframe({
              type: "openIframe", //指令
              name: "tcgl_ryglDate",
              src: "/static/citybrain/tcgl/commont/tcgl_ryglDate.html",
              left: "0px",
              top: "200px",
              width: "1030px",
              height: "1900px",
              zIndex: 997,
              argument: {
                tcgl_ryglDate: item.sys_depart,
                county: item.county,
              },
            });
          } else {
            window.parent.lay.closeIframeByNames(["tcgl_ryglDate"]);
            window.parent.lrFrameSHClick();
          }
        }
        if (typeName == "") return;
        this.zfryHj({
          name: typeName,
          sys_depart: item.sys_depart,
          county: item.county,
          hostcode: item.hostcode,
          type: item.type2,
          laiyuan: item.laiyuan
        });
      },
      zfryGl(glDatas) {
        let this_ = this;
        window.parent.mapUtil.removeAllLayers([
          "zfry_point_终点",
          "zfry_point_起点",
          "zfry_point_lines",
        ]);
        window.parent.mapUtil.flyTo({
          destination: glDatas[0], //飞行中心点
          zoom: 15, //飞行层级
        });
        let endPoint = [
          {
            lng: glDatas[glDatas.length - 1][0],
            lat: glDatas[glDatas.length - 1][1],
          },
        ];
        this_.addPoint(endPoint, "zfry_point_终点", "zhdd_routeplan_end");
        let startPoint = [
          {
            lng: glDatas[0][0],
            lat: glDatas[0][1],
          },
        ];
        this_.addPoint(endPoint, "zfry_point_起点", "zhdd_routeplan_start");

        window.parent.mapUtil.loadPolylineLayer({
          layerid: "zfry_point_lines",
          lines: [glDatas], //新地图  接口参数lines
          style: {
            width: 10,
            color: [28, 214, 108, 1],
            // image:
            //   baseURL.url +
            //   "/static/citybrain/hjbh/img/rkzt/jiantou_Right.png",
          },
        });
        // let view = window.parent.view;
        // try {
        //   this_.zfryGjLayer.clear();
        // } catch {}
        // let endPoint = [
        //   {
        //     lng: glDatas[glDatas.length - 1][0],
        //     lat: glDatas[glDatas.length - 1][1],
        //   },
        // ];
        // this_.addPoint(endPoint, "zfry_point_终点", "zhdd_routeplan_end");
        // this_.zfryGjLayer = new window.parent.ArcGisUtils.RouteLayer({
        //   view,
        //   data: glDatas,
        //   lineStyle: {
        //     color: [226, 119, 40],
        //     width: 5,
        //   },
        //   effectStyle: {
        //     url:
        //       baseURL.url +
        //       "/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/zhdd_routeplan_start.png",
        //     width: "100px",
        //     height: "100px",
        //   },
        //   speed: 3, // 速度，默认1
        // });
      },
      zfryHj(obj) {
        const { name, sys_depart, county, hostcode, type, laiyuan } = obj;
        let url = "";
        if (county === "市本级") {
          if (type == "poc") {
            url = "/static/citybrain/zhdd/zhdd_page/zfy/zfjly_sbj_poc/video.html";
          } else {
            url = "/static/citybrain/zhdd/zhdd_page/zfy/zfjly_sbj/video.html";
          }
        } else if (county === "永康市") {
          url = "/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yks/video.html";
        } else if (county === "东阳市") {
          url = "/static/citybrain/zhdd/zhdd_page/zfy/zfjly_dys/video.html";
        } else if (county === "浦江县") {
          url = "/static/citybrain/zhdd/zhdd_page/zfy/zfjly_pjx/video.html";
        } else if (county === "婺城区") {
          url = "/static/citybrain/zhdd/zhdd_page/zfy/zfjly_wcq/video.html";
        } else if (county === "磐安县") {
          url = "/static/citybrain/zhdd/zhdd_page/zfy/zfjly_pax/video.html";
        } else if (county == "兰溪市") {
          url = "/static/citybrain/zhdd/zhdd_page/zfy/zfjly_lxs/video.html";
        } else if(county === "义乌市" && type != "poc") {
          if (laiyuan == 2) {
            //交通局
            url = "/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yws2/video.html";
          } else if (laiyuan == 1) {
            //执法局
            url = "/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yws3/video.html";
          }
        } else if(county === "义乌市" && type == "poc") {
          url = "/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yws/video.html";
        } else if (county === "武义县") {
          if (hostcode == undefined || hostcode == null) {
            this.$message({
              message: '未绑定执法仪设备！',
              type: 'warning',
            })
          } else {
            wyxZfy.startClient()
            wyxZfy.connectClient({
              hostcode: hostcode,
              sys_depart: sys_depart,
            })
          }
          return
        }
        if (url === "") {
          this.$message({
            message: "设备处于离线状态！",
            type: "warning",
          });
          return;
        }
        window.parent.lay.openIframe({
          type: "openIframe", //指令
          name: "videoTest",
          src: baseURL.url + url,
          left: "1120px",
          top: "572px",
          width: "1509px",
          height: "1009px",
          zIndex: 667,
          argument: {
            name: obj.name,
            videoCode: [obj.sys_depart],
          },
        });
      },
      // 收缩两边
      hideIframe() {
        let lefthideCss =
                window.parent.document.getElementsByClassName("page_left")[0];
        if (
                lefthideCss != undefined &&
                lefthideCss.className.indexOf("fadeInLeft") != -1
        ) {
          window.parent.lrFrameSHClick && window.parent.lrFrameSHClick();
        }
      },
      showIframe() {
        let lefthideCss =
                window.parent.document.getElementsByClassName("page_left")[0];
        if (
                lefthideCss != undefined &&
                lefthideCss.className.indexOf("fadeOutLeft") != -1
        ) {
          window.parent.lrFrameSHClick && window.parent.lrFrameSHClick();
        }
      },
      zfryPopClose() {
        if (window.parent.mapUtil.popups.layerid.includes("zfry_pop")) {
          window.parent.mapUtil.removeLayer("zfry_pop");
          try {
            window.parent.mapUtil.removeAllLayers([
              "zfry_point_终点",
              "zfry_point_起点",
              "zfry_point_lines",
            ]);
            this.showZfryGJ = false;
            window.parent.lay.closeIframeByNames(["tcgl_ryglDate"]);
            this.showIframe();
            this.zfryGjLayer.clear();
          } catch {}
        }
      },
      // 执法站所--点位事件
      zfzsIframe(e) {
        window.parent.mapUtil.flyTo({
          destination: [e.esX, e.esY],
          offset: [0, -500],
        });
        window.parent.lay.openIframe({
          type: "openIframe", //指令
          name: "zfzsDetail",
          src:
                  baseURL.url + "/static/citybrain/commonts/tcgl/zfzsDetail.html",
          left: "calc(50% - 600px)",
          top: "600px",
          width: "1200px",
          height: "940px",
          zIndex: 667,
          argument: {
            data: e,
          },
        });
      },
      // 加载亚运场馆及其监控的视频弹窗
      zfzsIframe1(e, list) {
        let item = {
          obj: {
            // chn_name: e.data.chn_name,
            chn_name: e.CHN_NAME,
          },
          video_code: e.CHN_CODE,
          csrk: true,
        };
        let iframe1 = {
          type: "openIframe",
          name: "video_main_code",
          src:
                  baseURL.url +
                  "/static/citybrain/tcgl/commont/video_main_code.html",
          width: "3840px",
          height: "2160px",
          left: "0",
          top: "0",
          zIndex: "1000",
          argument: item,
        };
        window.parent.lay.openIframe(iframe1);
      },
      //执法车辆弹窗
      // clIframe(e) {
      //   console.log(e);
      //   window.parent.mapUtil.flyTo({
      //     destination: [e.longitude, e.latitude, 0], //飞行中心点
      //     zoom: 13, //飞行层级
      //   });
      //   let item = {
      //     obj: {
      //       // chn_name: e.data.chn_name,
      //       chn_name: e.station,
      //       lat: e.latitude,
      //       lon: e.longitude,
      //     },
      //     video_code: e.guid,
      //     csrk: true,
      //   };
      //   let iframe1 = {
      //     type: "openIframe",
      //     name: "zfcl_video_main_code",
      //     src:
      //       baseURL.url +
      //       "/static/citybrain/tcgl/commont/zfcl_video_main_code.html",
      //     width: "3840px",
      //     height: "2160px",
      //     left: "0",
      //     top: "0",
      //     zIndex: "1000",
      //     argument: item,
      //   };
      //   window.parent.lay.openIframe(iframe1);
      // },
      zfclPop(e) {
        console.log(e);
        let this_ = this;
        let str = `
                                          <div
                                        style="
                                          position: relative;
                                          background: url('/static/citybrain/csdn/img/cl.png') no-repeat;
                                          background-size: 100% 100%;
                                          width: max-content;
                                          min-height: 250px;
                                        "
                                      >
                                        <nav
                                          class="s-flex s-m-l-20 s-m-r-20 s-row-between s-p-b-10 s-col-bottom"
                                          style="
                                            border-bottom: 1px solid;
                                            border-bottom: 2px solid;
                                            padding-left: 20px;
                                          "
                                        >
                                      <h2 style="margin-top: 10px; white-space: nowrap;color: #fff;font-size:30px;" class="s-flex">车辆详情</h2>
                                          <span
                                            class="s-m-l-20 s-font-30 s-c-white"
                                            style="cursor: pointer"
                                            onclick="window.parent.frames['tckz_tcgl3840'].tcglVm.zfryPopClose()"
                                          >
                                            <img style="vertical-align: middle;" src="/static/citybrain/csdn/img/close.png" alt="" />
                                          </span>
                                        </nav>

                                        <header class="s-m-l-20 s-m-r-20 s-m-t-10 s-flex s-row-between s-font-30" style="padding-bottom: 13%;">
                                            <div class="s-m-l-40">
                                              <p style="width: 370px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">车牌号 ：<span style="color: #ffff;">${
                e.name
        }</span></p>
                                              <p style="margin-top:10px; width: 370px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">车辆类型 ：<span style="color: #ffff;">${
                e.carType
        }</span></p>

                                            </div>
                                        </header>
                                      </div>
                                    `;
        let objData = {
          layerid: "zfry_pop",
          position: [e.esX, e.esY],
          offset: [-70, 0],
          content: str,
        };
        window.parent.mapUtil._createPopup(objData);
      },
    },
  });
  window.parent.eventbus &&
  window.parent.eventbus.on("backHome", () => {
    try {
      tcglVm.zfryPopClose();
    } catch (error) {}
  });
  window.parent.eventbus &&
  window.parent.eventbus.on("beforeCloseIframe", (name) => {
    try {
      tcglVm.zfryPopClose();
    } catch (error) {}
  });
  window.parent.eventbus &&
  window.parent.eventbus.on("leftIframeShow", () => {
    try {
      window.parent.lay.closeIframeByNames(["tcgl_ryglDate"]);
    } catch (error) {}
  });
</script>
</body>
</html>
