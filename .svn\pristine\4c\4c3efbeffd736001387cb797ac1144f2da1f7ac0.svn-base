if (!poc.isWebRTCSupported || !poc.isWebAssemblySupported) {
  console.log("WebRTC or WebAssembly unsupported!");
} else {
  poc.ptt.init(function (err) {
    if (err != null) {
      if (err.name == "NotFoundError") {
        // alert(err + ". PTT Listen Only!");
      } else {
        // alert(
        //   err + ". POC PTT init has error! Some functions may not be available."
        // );
      }
    }
    poc.ptt.setLog(true);
  });
}
let ywsPassword = ""
let otherPassword = ""
var mainObj = {
  queryAuth() {
    // console.log("main");
    // var addr = "https://************:4010/dm-http-interface";
    // gUID = "9879310002";
    // gPWD = "82cgUF#-";
    // poc.ptt.doLogin(addr, gUID, gPWD);
    // poc.ptt.onLogin = function (result, secret) {
    //   if (result == 0) {
    //     //登陆成功,记住用户名密码
    //     document.cookie = "uid=" + gUID;
    //     document.cookie = "pwd=" + gPWD;
    //   }
    // };
    getYwsPassword();
    getOthersPassword();
    sbjVm.query();
    var obj = {
      Url: "https://************:4482/station/mobile/serverapi.action",
      HostIp: "", // (传空字符串即可)
      CustomId: "POC-1370",
      CustomPwd: hex_md5("Ddfk13!@"),
      Callback: function (res) {
        lxsVm.getSession(res);
        ywsVm.getSession(res);
        otherVm.getSession(res);
      },
    };
    poc.data.auth(obj);
  },
  //获取人员在线离线状态并合并数组
  async getOnlineStatus(arr,SessionId) {
    let result = []
    result = JSON.parse(JSON.stringify(arr));
    let onlineArr = []
    let uids = result.map(item => item.Uid)
    let newArr = mainObj.getSplitArray(uids,59) //以每个数组长度为59切割成的二维数组

    let promise = null
    promise = newArr.map(uidArr => {
      return mainObj.getStatus(uidArr,SessionId)  //将每个请求封装为promise
    })
    await Promise.all(promise).then(res => {  //遍历所有请求的数据
      console.log(res,"result");
      res.forEach((uidsArr,i) => {
        uidsArr.forEach((uid,i) => {
          onlineArr.push(uid)
        })
      })
    })
    onlineArr = onlineArr.filter(obj => obj.PresentStatus != -1)
    result.forEach((item,i) => {
      // PresentStatus 0离线 1在线 -1不在管辖范围 转 2: 在线, 3: 离线
      item.ipocid = item.Uid
      item.userstate = onlineArr.find(item2 => item2.Uid == item.Uid).PresentStatus == 1?2:3
    })
    console.log(result);
    return result;
  },
  // async getOnlineStatus(arr, SessionId) {
  //   const result = JSON.parse(JSON.stringify(arr)); // 复制一份数组以避免修改原始数据
  //   const uids = result.map(item => item.Uid);
  //   const uidChunks = mainObj.getSplitArray(uids, 59); // 切割数组得到子数组
  //
  //   // 将 getStatus 调用直接整合到 Promise.all 中
  //   const statuses = await Promise.all(uidChunks.map(uidArr => mainObj.getStatus(uidArr, SessionId)));
  //
  //   // 重构内部遍历逻辑
  //   const onlineArr = statuses.flat().filter(obj => obj.PresentStatus !== -1);
  //
  //   // 更新原始数组的状态信息
  //   result.forEach((item, i) => {
  //     const status = onlineArr.find(item2 => item2.Uid === item.Uid);
  //     if (status) {
  //       item.ipocid = item.Uid;
  //       item.userstate = status.PresentStatus === 1 ? 2 : 3; // 在线为2，离线为3
  //     }
  //   });
  //
  //   return result;
  // },
  //根据指定数组长度切割数组
  getSplitArray(list, splitCount) {
    const result = [];
    for (let i = 0; i < list.length; i += splitCount) {
      result.push(list.slice(i, i + splitCount));
    }
    return result;
  },
  getStatus(uidArr,SessionId) {
    return new Promise((resolve) => {
      poc.data.userOnlineStatus({
        SessionId:SessionId,
        Uids: uidArr,
        Callback: function (res) {
          console.log(res);
          if (res.Result == 200) {
            resolve(res.Users)
          }
        }
      })
    })
  }
}
var sbjVm = {
  dataList: [],
  query() {
    sbjVm.login()
    sbjVm.getUserList()
  },
  login() {
    fn_Start();
  },
  getUserList() {
    if (localStorage.getItem("MsgBody")) {
      let userList = JSON.parse(localStorage.getItem("MsgBody"))
      sbjVm.dataList = userList.map(item => {return {
          name: item.Name,
          phone: "",
          sys_depart: item.Num,
          dept_name: "",
          lon: item.GpsInfo[0].Longitude,
          lat: item.GpsInfo[0].Latitude,
          county: "市本级",
          lasttime: item.GpsInfo[0].Time,
          hostcode: "",
          lineon: item.status == 1?1:0,
          type:"idt"
      }})
      console.log(sbjVm.dataList,"qwe");
    }
  }
};
var lxsVm = {
  personList: null,
  dataList: [],
  gVideoSession: 0,
  getSession(e) {
    var obj = {
      ServiceCode: e.ServiceCode,
      DispatcherId: "9879310001",
      DispatcherPwd: hex_md5("LXzfj@123"),
      LoginType: 0,
      Callback: function (res) {
        lxsVm.getUserList(res)
      },
    };
    poc.data.getSession(obj);
  },
  getUserList(e) {
    $get("/zhdd/lxs_zfy_person").then((res) => {
      lxsVm.personList = res;
      lxsVm.getLocation(e);
    });
  },
  getLocation(e) {
    poc.data.orgMemberAll({
      SessionId: e.SessionId,
      Callback: function (res) {
        if (res.Result == 200) {
          mainObj.getOnlineStatus(res.Users,e.SessionId).then(userList => {
            let ids = [];
            userList.length > 0 &&
            userList.forEach((item) => {
              ids.push(item.ipocid);
              let i = lxsVm.personList.findIndex((a) => a.Uid == item.ipocid);
              i != -1 && (lxsVm.personList[i].lineon = item.userstate == 2 ? 1 : 0);
            });
            var obj = {
              SessionId: e.SessionId,
              Uids: ids,
              Callback: function (res) {
                res.Locations.map((item1) => {
                  lxsVm.personList.map((item2) => {
                    if (item1.Uid == item2.Uid) {
                      Object.assign(item1, item2);
                      lxsVm.dataList.push(item1);
                    }
                  });
                });
                lxsVm.dataList.forEach((obj) =>
                  Object.assign(obj, { county: "兰溪市",type:"" })
                );
                lxsVm.dataList = JSON.parse(
                  JSON.stringify(lxsVm.dataList)
                    .replace(/GpsLongitude/g, "lon")
                    .replace(/GpsLatitude/g, "lat")
                    .replace(/Uid/g, "sys_depart")
                    .replace(/Time/g, "lasttime")
                );
                console.log(lxsVm.dataList, "lxsList");
              },
            };
            poc.data.locationGet(obj);
          })
        }
      },
    })
  },
  dialogBye() {
    var session = lxsVm.gVideoSession;
    poc.ptt.doLeaveCall(session);
    console.log("dialogBye: session=" + session);
  },
};
var ywsVm = {
  personList: null,
  dataList: [],
  gVideoSession: 0,
  getSession(e) {
    var obj = {
      ServiceCode: e.ServiceCode,
      DispatcherId: "9883210002",
      DispatcherPwd: hex_md5(ywsPassword),
      LoginType: 0,
      Callback: function (res) {
        ywsVm.getUserList(res);
      },
    };
    poc.data.getSession(obj);
  },
  getUserList(e) {
    var obj = {
      SessionId: e.SessionId,
      Callback: function (res) {
        ywsVm.personList = res.Users;
        ywsVm.getLocation(e);
      },
    };
    poc.data.orgMemberAll(obj);
  },
  getLocation(e) {
    poc.data.orgMemberAll({
      SessionId: e.SessionId,
      Callback: function (res) {
        if (res.Result == 200) {
          mainObj.getOnlineStatus(res.Users,e.SessionId).then(userList => {
            let ids = [];
            userList.length > 0 &&
            userList.forEach((item) => {
              ids.push(item.ipocid);
              let i = ywsVm.personList.findIndex((a) => a.Uid == item.ipocid);
              i != -1 && (ywsVm.personList[i].lineon = item.userstate == 2 ? 1 : 0);
            });
            var obj = {
              SessionId: e.SessionId,
              Uids: ids,
              Callback: function (res) {
                res.Locations.map((item1) => {
                  ywsVm.personList.map((item2) => {
                    if (item1.Uid == item2.Uid) {
                      Object.assign(item1, item2);
                      ywsVm.dataList.push(item1);
                    }
                  });
                });
                if (ywsVm.dataList.length > 0) {
                  ywsVm.dataList.forEach((obj) =>
                    Object.assign(obj, { county: "义乌市",type:"" })
                  );
                  ywsVm.dataList = JSON.parse(
                    JSON.stringify(ywsVm.dataList)
                      .replace(/GpsLongitude/g, "lon")
                      .replace(/GpsLatitude/g, "lat")
                      .replace(/Uid/g, "sys_depart")
                      .replace(/Time/g, "lasttime")
                      .replace(/Type/g, "lineon")
                      .replace(/Name/g, "name")
                      .replace(/Phone/g, "phone")
                      .replace(/Orgname/g, "dept_name")
                  );
                }
                console.log(ywsVm.dataList, "ywsList");
              },
            };
            poc.data.locationGet(obj);
          })
        }
      },
    })

  },
  dialogBye() {
    var session = ywsVm.gVideoSession;
    poc.ptt.doLeaveCall(session);
    console.log("dialogBye: session=" + session);
  },
};
var otherVm =  {
  personList: null,
  dataList: [],
  gVideoSession: 0,
  getSession(e) {
    var obj = {
      ServiceCode: e.ServiceCode,
      DispatcherId: "11268810002",
      DispatcherPwd: hex_md5(otherPassword),
      LoginType: 0,
      Callback: function (res) {
        otherVm.getUserList(res);
      },
    };
    poc.data.getSession(obj);
  },
  getUserList(e) {
    var obj = {
      SessionId: e.SessionId,
      Callback: function (res) {
        otherVm.personList = res.Users;
        otherVm.getLocation(e);
      },
    };
    poc.data.orgMemberAll(obj);
  },
  getLocation(e) {
    poc.data.orgMemberAll({
      SessionId: e.SessionId,
      Callback: function (res) {
        if (res.Result == 200) {
          mainObj.getOnlineStatus(res.Users,e.SessionId).then(userList => {
            let ids = [];
            userList.length > 0 &&
            userList.forEach((item) => {
              ids.push(item.ipocid);
              let i = otherVm.personList.findIndex((a) => a.Uid == item.ipocid);
              i != -1 && (otherVm.personList[i].lineon = item.userstate == 2 ? 1 : 0);
            });
            var obj = {
              SessionId: e.SessionId,
              Uids: ids,
              Callback: function (res) {
                res.Locations.map((item1) => {
                  otherVm.personList.map((item2) => {
                    if (item1.Uid == item2.Uid) {
                      Object.assign(item1, item2);
                      otherVm.dataList.push(item1);
                    }
                  });
                });
                if (otherVm.dataList.length > 0) {
                  otherVm.dataList.forEach((obj) =>
                    Object.assign(obj, { county: "市本级",type:"poc" })
                  );
                  otherVm.dataList = JSON.parse(
                    JSON.stringify(otherVm.dataList)
                      .replace(/GpsLongitude/g, "lon")
                      .replace(/GpsLatitude/g, "lat")
                      .replace(/Uid/g, "sys_depart")
                      .replace(/Time/g, "lasttime")
                      .replace(/Type/g, "lineon")
                      .replace(/Name/g, "name")
                      .replace(/Phone/g, "phone")
                      .replace(/Orgname/g, "dept_name")
                  );
                }
                console.log(otherVm.dataList, "otherList");
              },
            };
            poc.data.locationGet(obj);
          })
        }
      },
    })

  },
  dialogBye() {
    var session = ywsVm.gVideoSession;
    poc.ptt.doLeaveCall(session);
    console.log("dialogBye: session=" + session);
  },
};
var getYwsPassword = function () {
  $api2Get("/xzzfj/hdjjUpdateLog/getPwdNow",{status:1}).then((res) => {
    ywsPassword = res.data.data.pwd
    sessionStorage.setItem("ywsPassword",ywsPassword)
  })
}
var getOthersPassword = function () {
  $api2Get("/xzzfj/hdjjUpdateLog/getPwdNow",{status:2}).then((res) => {
    otherPassword = res.data.data.pwd
    sessionStorage.setItem("otherPassword",otherPassword)
  })
}

