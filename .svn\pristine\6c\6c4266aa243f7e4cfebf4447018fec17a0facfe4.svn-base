<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>加载geojson使用示例</title>

    <link
      rel="stylesheet"
      href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <script src="./index.js" type="module"></script>

    <script>
      let layer;
      function addGeojson() {
        const geojson = {
          type: "FeatureCollection",
          features: [
            {
              type: "Feature",
              id: 387414,
              geometry: {
                type: "Polygon",
                coordinates: [
                  [
                    [119.84527329352784, 29.226942536668901],
                    [119.84527211002001, 29.227704744478558],
                    [119.84446106722464, 29.227703663493457],
                    [119.84446591457049, 29.224650340141238],
                    [119.84527684315196, 29.224651401341248],
                    [119.84527532689503, 29.225629845740968],
                    [119.84527329352784, 29.226942536668901],
                  ],
                ],
              },
              properties: {
                OBJECTID_1: 387414,
                OBJECTID: 387414,
                FEATUREGUI: "66b0f409c63f4197bbdc89b11e85eef0",
                FCODE: "3103011500",
                FLOOR: 1,
                FSCALE: 16,
                USOURCE: "1万电子地图",
                UPDATETIME: "20151111",
                UPDATESTAT: "A",
                Shape_Leng: 0.0077285858649299996,
                high: 3,
                Shape_Length: 0.0077286467973664849,
                Shape_Area: 2.4762172869668944e-6,
              },
            },
          ],
        };

        layer = ArcGisUtils.addGeojsonToMap(view, geojson);
      }
      function removeGeojson() {
        view.map.remove(layer);
      }
    </script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        left: 50%;
        width: 50%;
        height: 200px;
        display: flex;
      }

      .tools span {
        cursor: pointer;
        background-color: blue;
        width: 150px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        color: white;
      }

      .description {
        position: absolute;
        right: 10px;
        top: 10px;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv">
      <div class="tools">
        <span onclick="addGeojson()">添加geojson</span>
        <span onclick="removeGeojson()">移除geojson</span>
      </div>
      <div class="description">
        addGeojsonToMap(view, geojson, props)
        <p>添加JSON图层，并返回图层对象</p>
        <p>@param {*} view MapView|SceneView 对象</p>
        <p>@param {*} geojson 必填 geojison 数据</p>
        <p>@param {*} props 选填 图层设置信息</p>
        <p>@return GeojsonLayer</p>
      </div>
    </div>
  </body>
</html>
