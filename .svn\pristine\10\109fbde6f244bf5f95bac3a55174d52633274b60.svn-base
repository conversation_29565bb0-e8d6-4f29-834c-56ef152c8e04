import VideoElement from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/support/VideoElement.js";
import ExtentAndRotationGeoreference from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/support/ExtentAndRotationGeoreference.js";
import MediaLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/MediaLayer.js";
import Extent from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/geometry/Extent.js";
import Point from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/geometry/Point.js";
import * as reactiveUtils from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/core/reactiveUtils.js";

let watchHandle;
const LAYER_ID = "arcgis_gif";

/**
 * 添加GIF 图层
 * @param {object}  {view,data} 传入参数对象
 */
function addGifLayer({ view, data }) {
  function _calculateExtent({ view, x, y, imgSize }) {
    const leftBottomPoint = new Point({
      x: x,
      y: y,
      z: 0,
      spatialReference: view.spatialReference.clone(),
    });
    const leftBottomSceenPoint = view.toScreen(leftBottomPoint);
    const rightTopScreenPoint = {
      x: leftBottomSceenPoint.x + imgSize[0], // imgSize[0]
      y: leftBottomSceenPoint.y - imgSize[1], // imgSize[1]
    };
    const rightTopMapPoint = view.toMap(rightTopScreenPoint);
    let extent;
    if (rightTopMapPoint) {
      extent = new Extent({
        xmin: leftBottomPoint.x,
        ymin: leftBottomPoint.y,
        xmax: rightTopMapPoint.x,
        ymax: rightTopMapPoint.y,
        zmin: leftBottomPoint.z,
        zmax: rightTopMapPoint.z,
        spatialReference: view.spatialReference.clone(),
      });
    }
    return extent;
  }

  const videoElements = [];
  for (let i = 0; i < data.length; i++) {
    const item = data[i];
    const { x, y, imgSize, video } = item;
    let extent = _calculateExtent({ view, x, y, imgSize });
    if (extent) {
      const element = new VideoElement({
        video,
        georeference: new ExtentAndRotationGeoreference({
          extent,
        }),
      });
      videoElements.push(element);
    }
  }

  const layer = new MediaLayer({
    id: LAYER_ID,
    source: videoElements,
    title: "加载gif",
  });
  if (watchHandle) {
    watchHandle.remove();
  }
  watchHandle = reactiveUtils.watch(
    () => view.zoom,
    (newVal, oldValue) => {
      if (parseInt(newVal) !== parseInt(oldValue)) {
        addGifLayer({ view, data });
      }
    }
  );
  removeGifLayer(view);
  view.map.add(layer);
}

function removeGifLayer(view) {
  const layer = view.map.findLayerById(LAYER_ID);
  if (layer) {
    view.map.remove(layer);
  }
}

export { addGifLayer, removeGifLayer };
