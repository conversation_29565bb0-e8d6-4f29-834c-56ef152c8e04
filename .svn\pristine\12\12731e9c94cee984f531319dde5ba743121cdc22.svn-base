const vectorUrl='https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/bluegrey_102100/MapServer/WMTS/tile/1.0.0/Hosted_bluegrey_102100/default/default028mm/{z}/{y}/{x}.png'

export default class MapboxMap {
    constructor(options) {
      mapboxgl.accessToken = options.accessToken;
      this.map =null;
      this.#createMap(options)
      this.drawMultilateral=drawMultilateral.bind(this)
      this.clearLayerAndSource=clearLayerAndSource.bind(this)
    }
    #createMap(options) {
        const style = {
            version: 8,
            name: "tianditu",
            sprite: "mapbox://sprites/mapbox/streets-v8",
            glyphs: "mapbox://fonts/mapbox/{fontstack}/{range}.pbf",
            sources: {
              jinhua_vector: {
                type: 'raster',
                tiles: [
                vectorUrl
                ],
                tileSize: 256
              }
            },
            layers: [
              {
                id: "jinhua_vector",
                type: "raster",
                source: "jinhua_vector",
                minzoom: 0,
                maxzoom: 22
              }
            ]
          }
      this.map=  new mapboxgl.Map({
            container: options.container,
            // style: 'mapbox://styles/mapbox/satellite-v9',
            style, // style URL
            center: options.center,
            zoom: options.zoom,
          });
    }
    addGeoJSONLayer({ type, data, paint, id="geoJSONLayer" }) {
   
  
      var _self = this.map;
  
      _self.addSource(id, {
        type: "geojson",
        data: data,
      });
  
      _self.addLayer({
        id,
        type: type,
        source: id,
        paint: paint,
      });
      return {
          remove:()=>{
              this.map.removeLayer(id);
              this.map.removeSource(id); 
              id=null;
          }
      }
    }
    addPolygonLayer({ id = "polygon", data, fillPaint, outlinePaint }) {
      var _self = this.map;
      // Add a data source containing GeoJSON data.
      _self.addSource(id, {
        type: "geojson",
        data: data,
      });
  
      // Add a new layer to visualize the polygon.
      _self.addLayer({
        id: id+"fill",
        type: "fill",
        source: id, // reference the data source
        layout: {},
        paint: fillPaint,
      });
      // Add a black outline around the polygon.
      _self.addLayer({
        id: id + "outline",
        type: "line",
        source: id,
        layout: {},
        paint: outlinePaint,
      });
      return {
          remove:()=>{
              this.map.removeLayer(id+"fill");
              this.map.removeLayer(id+"outline");
              this.map.removeSource(id); 
              id=null;
         
          }
      }
    }
    // 添加图片的符号点要素
    addPointsImageLayer({ id = "imageLayer", data, image_url }) {
      var _self = this.map;
      // 添加图片符号层
      _self.loadImage(image_url, (error, image) => {
        if (error) throw error;
  
        _self.addImage(id, image);
  
        _self.addSource(id, {
          type: "geojson",
          data: data,
        });
  
        _self.addLayer({
          id: id,
          type: "symbol",
          source: id,
          layout: {
            "icon-image": id,
            "icon-size": 1,
          },
        });
      });
  
      return {
          remove:()=>{
              this.map.removeLayer(id);
              this.map.removeSource(id); 
              this.map.removeImage(id); 
              id=null;
          }
      }
    }
    // 添加点的热力图
    addHeatMapLayer({id="heatmap", data, paint}) {
      var _self = this.map;
      // 创建热力图图层
      var heatMapLayer = {
        id,
        type: "heatmap",
        source: {
          type: "geojson",
          data: data,
        },
        paint: paint,
      };
  
      // 添加热力图图层到地图
      _self.addLayer(heatMapLayer);
      return {
          remove:()=>{
              this.map.removeLayer(id); 
              id=null;
          }
      }
    }
    removeLayerById(layerId) {
      this.map.removeLayer(layerId);
      this.map.removeSource(layerId); // 如果有关联的数据源，也要进行删除
    }
    removeAll() {
      // 获取当前地图的样式对象
      const style = this.map.getStyle();
  
      // 遍历样式中的图层，并删除非底图的每个图层
      style.layers.forEach((layer) => {
        if (layer.id !== "satellite" || layer.id !== "background") {
          // 替换 'base-layer' 为你的底图图层 ID
          this.map.removeLayer(layer.id);
          this.map.removeSource(layer.id);
        }
      });
  
      // 清空样式中的图层数组，只保留底图图层
      style.layers = style.layers.filter((layer) => {
        layer.id === "satellite";
      }); // 替换 'base-layer' 为你的底图图层 ID
  
      // 将更新后的样式重新应用到地图
      this.map.setStyle(style);
    }

   
  }
  

  // 清除draw资源图层
  const DRAWSUBFIX="DRAW"
 function clearLayerAndSource (map) {
  if (map.getSource(DRAWSUBFIX+'circlePlan')) {
    map.removeLayer(DRAWSUBFIX+'circlePlan')
    map.removeSource(DRAWSUBFIX+'circlePlan')
  } else if (map.getLayer(DRAWSUBFIX+'silmp')) {
    map.removeLayer(DRAWSUBFIX+'silmp')
    map.removeSource(DRAWSUBFIX+'silmp')
  }
  if (map.getLayer(DRAWSUBFIX+'line')) {
    map.removeLayer(DRAWSUBFIX+'line')
  }
  if (map.getLayer(DRAWSUBFIX+'line')) {
    map.removeLayer(DRAWSUBFIX+'line')
  }
  if (map.getSource(DRAWSUBFIX+'line')) {
    map.removeSource(DRAWSUBFIX+'line')
  }
  if (map.getLayer(DRAWSUBFIX+'points')) {
    map.removeLayer(DRAWSUBFIX+'points')
  }
  if (map.getSource(DRAWSUBFIX+'points')) {
    map.removeSource(DRAWSUBFIX+'points')
  }
  if (map.getLayer(DRAWSUBFIX+'polygon')) {
    map.removeLayer(DRAWSUBFIX+'polygon')
  }
  if (map.getSource(DRAWSUBFIX+'polygon')) {
    map.removeSource(DRAWSUBFIX+'polygon')
  }
  if (map.getLayer(DRAWSUBFIX+'line-move')) {
    map.removeLayer(DRAWSUBFIX+'line-move')
  }
  if (map.getSource(DRAWSUBFIX+'line-move')) {
    map.removeSource(DRAWSUBFIX+'line-move')
  }
  if (map.getLayer(DRAWSUBFIX+'points-area')) {
    map.removeLayer(DRAWSUBFIX+'points-area')
  }
  if (map.getSource(DRAWSUBFIX+'points-area')) {
    map.removeSource(DRAWSUBFIX+'points-area')
  }
  if (map.getLayer(DRAWSUBFIX+'line-area-stroke')) {
    map.removeLayer(DRAWSUBFIX+'line-area-stroke')
  }
  if (map.getSource(DRAWSUBFIX+'line-area-stroke')) {
    map.removeSource(DRAWSUBFIX+'line-area-stroke')
  }
  if (map.getLayer(DRAWSUBFIX+'urban-areas-fill')) {
    map.removeLayer(DRAWSUBFIX+'urban-areas-fill')
  }
  if (map.getSource(DRAWSUBFIX+'line-area')) {
    map.removeSource(DRAWSUBFIX+'line-area')
  }
}
// 绘制多边形
 function drawMultilateral (map,resCallback) {
  const  _that = this
  map.getCanvas().style.cursor = 'crosshair'
  let isDraw = true
  _that.draw = isDraw
  // 禁止双击缩放
  map.doubleClickZoom.disable()
  const jsonLine = {
    type: 'FeatureCollection',
    features: [
      {
        type: 'Feature',
        geometry: {
          type: 'LineString',
          coordinates: []
        }
      }
    ]
  }
  const jsonPoint = {
    type: 'FeatureCollection',
    features: []
  }
  const jsonFull = {
    type: 'FeatureCollection',
    features: [
      {
        type: 'Feature',
        geometry: {
          type: 'Polygon',
          coordinates: [
            []
          ]
        }
      }
    ]
  }
  map.addSource(DRAWSUBFIX+'line', {
    type: 'geojson',
    data: jsonLine
  })
  map.addSource(DRAWSUBFIX+'points', {
    type: 'geojson',
    data: jsonPoint
  })
  map.addSource(DRAWSUBFIX+'line-move', {
    type: 'geojson',
    data: jsonLine
  })
  map.addSource(DRAWSUBFIX+'silmp', {
    type: 'geojson',
    data: jsonFull
  })
  map.addLayer({
    id: DRAWSUBFIX+'line',
    type: 'line',
    source: DRAWSUBFIX+'line',
    paint: {
      'line-color': '#ff0000',
      'line-width': 2,
      'line-opacity': 0.65
    }
  })
  map.addLayer({
    id: DRAWSUBFIX+'points',
    type: 'circle',
    source: DRAWSUBFIX+'points',
    paint: {
      'circle-color': '#ffffff',
      'circle-radius': 3,
      'circle-stroke-width': 2,
      'circle-stroke-color': '#ff0000'
    }
  })
  map.addLayer({
    id:DRAWSUBFIX+ 'line-move',
    type: 'line',
    source: DRAWSUBFIX+'line-move',
    paint: {
      'line-color': '#ff0000',
      'line-width': 2,
      'line-opacity': 0.65
    }
  })
  map.addLayer({
    id: DRAWSUBFIX+'silmp',
    type: 'fill',
    source: DRAWSUBFIX+'silmp',
    paint: {
      'fill-color': '#e6205e',
      'fill-opacity': 0.6
    }
  })
  map.on('click', (_e) => {
    if (!isDraw) return
    const pointLngLat = [_e.lngLat.lng, _e.lngLat.lat]
    const point = {
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: pointLngLat
      }
    }
    jsonPoint.features.push(point)
    jsonLine.features[0].geometry.coordinates.push(pointLngLat)
    jsonFull.features[0].geometry.coordinates[0].push(pointLngLat)
    map.getSource(DRAWSUBFIX+'line').setData(jsonLine)
    map.getSource(DRAWSUBFIX+'points').setData(jsonPoint)
  })
  map.on('mousemove', (_e) => {
    if (!isDraw) return
    const pointLngLat = [_e.lngLat.lng, _e.lngLat.lat]
    if (jsonPoint.features.length > 0) {
      const prev = jsonLine.features[0].geometry.coordinates[jsonLine.features[0].geometry.coordinates.length - 1]
      const jsonLineTemp = {
        type: 'Feature',
        geometry: {
          type: 'LineString',
          coordinates: [prev, pointLngLat]
        }
      }
      map.getSource(DRAWSUBFIX+'line-move').setData(jsonLineTemp)
    }
  })
  map.on('dblclick', (_e) => {
    if (!isDraw) return
    if (jsonFull.features[0].geometry.coordinates[0].length < 2) {
      console.warning('选取点位需大于两个')
      return
    }
    const pointLngLat = [_e.lngLat.lng, _e.lngLat.lat]
    const point = {
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: pointLngLat
      }
    }
    jsonPoint.features.push(point)
    jsonLine.features[0].geometry.coordinates.push(pointLngLat)
    jsonLine.features[0].geometry.coordinates.push(jsonLine.features[0].geometry.coordinates[0])
    jsonFull.features[0].geometry.coordinates[0].push(pointLngLat)
    jsonFull.features[0].geometry.coordinates[0].push(jsonFull.features[0].geometry.coordinates[0][0])
    map.getSource(DRAWSUBFIX+'line').setData(jsonLine)
    map.getSource(DRAWSUBFIX+'points').setData(jsonPoint)
    map.getSource(DRAWSUBFIX+'silmp').setData(jsonFull)
    map.getCanvas().style.cursor = 'grab'
    isDraw = false
    _that.draw = isDraw
   setTimeout(()=>{
    map.doubleClickZoom.enable()
  })
  resCallback(map.getSource(DRAWSUBFIX+'silmp')._data)
  })
}
