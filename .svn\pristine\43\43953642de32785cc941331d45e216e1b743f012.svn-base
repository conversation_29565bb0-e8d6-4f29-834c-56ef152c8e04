var zfjly = {
    get: axios.create({baseURL: "/zfjly_dys", withCredentials: true,}).get,
    post: axios.create({baseURL: "/zfjly_dys", withCredentials: true,}).post,
    dataInfo_set: [],
    form: {
        hostbodyArr: null,
        hostbody: null,
    },
    dataInfo_hostbody: null,
    wsUrl: null,
    ws: null,
    timerId: null,
    isAudio: false,
    isC2: false,
    wsChannelId: null,
    sn: null,
    player: null,
    recorders: null,

    //登录执法记录仪平台
    loginZFJLY: function () {
        return new Promise((resolve) => {
            this.getLoginBaseInfo("").then((res) => {
                var send = {
                    username: "admin2",
                    pwd: "dy@123456",
                    token: res.data,
                };
                this.login(send).then((el) => {
                    if (el.data.code === 200) {
                        this.isShow = true;
                        this.heartbeat().then(() => {
                            this.getUserInformation().then((e) => {
                                var _this = this;
                                try {
                                    zfjly.wsUrl = e.data.data.wsurl;
                                    this.createWebSocket(e.data.data.wsurl, e.data.data);
                                } catch (error) {
                                    console.log(error);
                                    resolve(false);
                                }
                                resolve(true);
                            });
                        });
                    } else {
                        resolve(false);
                    }
                });
            });
        });
    },

    // 创建WebSocket连接
    createWebSocket: function(wsUrl, userData) {
        var _this = this;
        try {
            this.ws = new WebSocket(wsUrl);
        } catch (error) {
            console.log("WebSocket创建失败:", error);
            return;
        }

        var data1 = {
            logincode: userData.logincode,
            username: userData.username,
            scode: userData.scode,
            cate: userData.auth_cate,
        };

        var psd = {
            command: "client_login",
            data: JSON.stringify(data1),
        };

        this.ws.onopen = function () {
            console.log("WebSocket已连接，发送登录信息");
            _this.ws.send(JSON.stringify(psd));
        };

        this.ws.onerror = function (e) {
            console.warn("WebSocket连接出错:", e);
            _this.ws.close();
            _this.ws = null;
        };

        this.ws.onclose = function () {
            console.log("WebSocket已断开连接");
        };

        this.ws.onmessage = function (event) {
            console.log("WebSocket收到消息:", event.data);
            var data = JSON.parse(event.data);

            // 处理site_data信息
            if (data.site_data) {
                var dataInfo = data.site_data;

                // 处理开始播放视频事件
                if (dataInfo.start_live) {
                    if (dataInfo.start_live.recorder_type == "2") {
                        _this.isC2 = true;
                    } else {
                        _this.isC2 = false;
                    }
                    var ws = "ws:/" + dataInfo.start_live.wsInfo.wsIp + ":" + dataInfo.start_live.wsInfo.wsPort;
                    var viewId = dataInfo.start_live.wsInfo.wsViewId;
                    window.pullFlow_vms2(ws, viewId);
                }

                // 处理开始音频事件
                if (dataInfo.start_audio) {
                    _this.isAudio = true;
                    var wss = `ws://${dataInfo.start_audio.wsInfo.wsIp}:${dataInfo.start_audio.wsInfo.wsPort}`;
                    _this.wsChannelId = dataInfo.start_audio.wsInfo.wsChannelId;
                    _this.sn = dataInfo.start_audio.wsInfo.sn;
                    window.voice_pull_vms2(wss, _this.wsChannelId);
                }

                // 处理预设点事件
                if (dataInfo.preset) {
                    window.preset2(dataInfo.preset.data);
                }
            }
        };
    },

    getLoginBaseInfo: function (data) {
        return this.get(`rest/index/login/get?key=${data}`)
    },

    login: function (param) {
        var base64 = {
            encode: function (str) {
                return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function (_, hex) {
                    return String.fromCharCode('0x' + hex);
                }));
            },
            decode: function (str) {
                return decodeURIComponent(atob(str).split('').map(function (c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));
            }
        };
        let {username, pwd, token, captcha_code} = param;
        let password = hex_md5(pwd);
        let login_if = base64.encode(JSON.stringify({ username, password }));
        let data = this.param_up({login_info: login_if, token, captcha_code, 'withCredentials': true});
        return this.post('/rest/index/login/login', data);
    },

    param_up: function (param_arr) {
        var keys = Object.keys(param_arr).sort();
        var string = "";
        for (var i = 0; i < keys.length; i++) {
            var k = keys[i];
            string += k + "=" + param_arr[k] + ";";
        }
        string += hex_md5("Pe2695jingyi");
        let str_encode = encodeURIComponent(string);
        //编码后MD5加密
        param_arr.pe_signals = hex_md5(str_encode);
        return JSON.stringify(param_arr);
    },

    getUserInformation: function () {
        return this.get('/rest/user/user/get_info');
    },

    //心跳
    heartbeat: function () {
        return new Promise((resolve) => {
            this.heart();
            this.timerId = setInterval(this.heart, 20000);
            resolve();
        });
    },

    heart: function () {
        zfjly.online().then((e) => {
            console.log("心跳检测:", e);
        }).catch(err => {
            console.error("心跳检测失败:", err);
        });
    },

    online: function () {
        return this.get('rest/other/user/online');
    },

    //获取设备信息
    getDeviceInfo: function () {
        return new Promise((resolve) => {
            this.unitEquipTree("1001", "bh", "dname", false).then((res) => {
                var lineon = [];
                let info = {
                    ids: []
                };
                res.data.data.forEach((item) => {
                    if (item.lineon == 1) {
                        this.dataInfo_set.push(item);
                        lineon.push(item.hostbody);
                        info.ids.push(item.hostbody)
                    }
                });
                this.getPosition(info).then((res) => {
                    let data = res.data.data;
                    if (data && data.length > 0) {
                        data.forEach((item) => {
                            let lat = item.lat;
                            let lng = item.lng;
                            let name = item.name;
                        });
                    }
                    this.pulldata = res.data;
                    this.dataInfo_hostbody = lineon.toString();
                    this.form.hostbodyArr = this.dataInfo_hostbody;
                    resolve(this.dataInfo_set);
                }).catch(err => {
                    console.error("获取位置信息失败:", err);
                    this.pulldata = res.data;
                    this.dataInfo_hostbody = lineon.toString();
                    this.form.hostbodyArr = this.dataInfo_hostbody;
                    resolve(this.dataInfo_set);
                });
            }).catch(err => {
                console.error("获取设备信息失败:", err);
                resolve([]);
            });
        });
    },

    unitEquipTree: function (id = '', bh = 'bh', text = 'dname', isNewapi = false) {
        let data = {
            "id": id,
            "bh": bh,
            "text": text
        };
        this.extendSignal(data);
        if (isNewapi) {
            return this.post('/rest/other/unitjson/gdlist_dv', data);
        } else {
            return this.post('/rest/other/unitjson/gdlist', data);
        }
    },

    extendSignal: function (target) {
        let keys = Object.keys(target),
            arr = [],
            solt = "Pe2695jingyi",
            str,
            pe_signals;
        keys.sort(); // 排序
        keys.forEach((key) => {
            const value = JSON.stringify(target[key]);
            arr.push(`${key}=${value}`);
        });
        str = arr.join(";") + hex_md5(solt);
        str = encodeURIComponent(str);
        pe_signals = hex_md5(str);
        target.pe_signals = pe_signals;
        return target;
    },

    //获取设备经纬度信息
    getPosition: function (data) {
        this.extendSignal(data)
        return this.post('/rest/gis/gismoni/get_point', data)
    },

    // 开始拉流指定设备的视频
    startLive: function(hostbody) {
        var send = {
            hostbody_arr: Array.isArray(hostbody) ? hostbody : [hostbody || "T0C0223"],
        };

        return new Promise((resolve, reject) => {
            this.startLiveVideo(send).then(res => {
                if (res.data.code == 200 && res.data.data[0].is_existed) {
                    // 使用WebRTC方式播放
                    const rtspUrl = res.data.data[0].play_info.rtsp_url;
                    // 使用固定的WebRTC服务地址
                    const webRtcUrl = res.data.data[0].play_info.webrtc_url;

                    // 保存sn值，用于停止视频流
                    if (res.data.data[0].sn) {
                        this.sn = res.data.data[0].sn;
                    }

                    console.log("准备使用WebRTC播放视频流:", rtspUrl, "SN:", res.data.data[0].sn || "1213");

                    // 返回完整的视频流信息
                    resolve({
                        data: {
                            code: 200,
                            data: [{
                                ...res.data.data[0],
                                webrtc_url: webRtcUrl,
                                rtsp: rtspUrl,
                                is_existed: true,
                                play_info: {
                                    webrtc_url: webRtcUrl
                                },
                                // 确保sn字段被传递
                                sn: res.data.data[0].sn || "1213" // 如果没有sn则使用默认值
                            }]
                        }
                    });
                } else {
                    // 设备不在线或其他错误
                    resolve(res);
                }
            }).catch(error => {
                console.error("开始视频流失败:", error);
                reject(error);
            });
        });
    },

    // 创建WebRTC播放器并播放视频流
    createWebRTCPlayer: function(videoElement, webRtcUrl, streamUrl) {
        if (!videoElement) {
            console.error("播放器元素不存在");
            return Promise.reject("播放器元素不存在");
        }

        return new Promise((resolve, reject) => {
            try {
                // 创建WebRTC播放器配置
                const webrtcConfig = {
                    element: videoElement,
                    debug: true,
                    zlmsdpUrl: webRtcUrl,
                    simulcast: false,
                    useCamera: false,
                    audioEnable: true,
                    videoEnable: true,
                    recvOnly: true,
                    resolution: { w: 1280, h: 720 },
                    usedatachannel: false
                };

                // 创建WebRTC播放器实例
                const webrtcPlayer = new Webrtc(webrtcConfig);

                // 开始播放
                webrtcPlayer.start_play().then(stream => {
                    console.log("WebRTC播放成功:", stream);
                    this.player = webrtcPlayer;
                    resolve({
                        player: webrtcPlayer,
                        stream: stream,
                        type: true
                    });
                }).catch(error => {
                    console.error("WebRTC播放失败:", error);
                    reject(error);
                });
            } catch (error) {
                console.error("创建WebRTC播放器失败:", error);
                reject(error);
            }
        });
    },

    //开始视频流
    startLiveVideo: function (data) {
        this.extendSignal(data);
        return this.post('/rest/live/chrome/startLive', data);
    },

    // 开始音频流
    startLiveAudio: function (data) {
        this.extendSignal(data)
        return this.post('/rest/live/chrome/startAudio', data)
    },

    // 发送命令
    send_cmd: function (data) {
        this.extendSignal(data)
        return this.post('/rest/gis/gismoni/send_cmd', data)
    },

    // 停止视频流
    stopLive: function(data) {
        this.extendSignal(data);
        return this.post('/rest/live/chrome/stopLive', data);
    },

    // 停止音频
    stopAudio: function(data) {
        this.extendSignal(data);
        return this.post('/rest/live/chrome/stopAudio', data);
    },

    // 关闭所有资源
    closeAll: function() {
        if (this.player) {
            this.player = null;
        }

        if (this.recorders) {
            this.recorders.closeWebsocket();
            this.recorders = null;
        }

        if (this.timerId) {
            clearInterval(this.timerId);
            this.timerId = null;
        }

        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }

        window.parent.lay.closeIframeByNames(["videoTest"]);
    }
}
