import TileInfo from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/@arcgis/core/layers/support/TileInfo.js";

const tileInfo = new TileInfo({
  dpi: 90.71428571427429,
  origin: {
    x: -180,
    y: 90,
  },
  spatialReference: {
    wkid: 4490,
  },
  lods: [
    {
      level: 0,
      levelValue: "1",
      resolution: 0.703125,
      scale: 295497593.05875003,
    },
    {
      level: 1,
      levelValue: "2",
      resolution: 0.3515625,
      scale: 147748796.52937502,
    },
    {
      level: 2,
      levelValue: "3",
      resolution: 0.17578125,
      scale: 73874398.264687508,
    },
    {
      level: 3,
      levelValue: "4",
      resolution: 0.087890625,
      scale: 36937199.132343754,
    },
    {
      level: 4,
      levelValue: "5",
      resolution: 0.0439453125,
      scale: 18468599.566171877,
    },
    {
      level: 5,
      levelValue: "6",
      resolution: 0.02197265625,
      scale: 9234299.7830859385,
    },
    {
      level: 6,
      levelValue: "7",
      resolution: 0.010986328125,
      scale: 4617149.8915429693,
    },
    {
      level: 7,
      levelValue: "8",
      resolution: 0.0054931640625,
      scale: 2308574.9457714846,
    },
    {
      level: 8,
      levelValue: "9",
      resolution: 0.00274658203125,
      scale: 1154287.4728857423,
    },
    {
      level: 9,
      levelValue: "10",
      resolution: 0.001373291015625,
      scale: 577143.73644287116,
    },
    {
      level: 10,
      levelValue: "11",
      resolution: 0.0006866455078125,
      scale: 288571.86822143558,
    },
    {
      level: 11,
      levelValue: "12",
      resolution: 0.00034332275390625,
      scale: 144285.93411071779,
    },
    {
      level: 12,
      levelValue: "13",
      resolution: 0.000171661376953125,
      scale: 72142.967055358895,
    },
    {
      level: 13,
      levelValue: "14",
      resolution: 8.58306884765625e-5,
      scale: 36071.483527679447,
    },
    {
      level: 14,
      levelValue: "15",
      resolution: 4.291534423828125e-5,
      scale: 18035.741763839724,
    },
    {
      level: 15,
      levelValue: "16",
      resolution: 2.1457672119140625e-5,
      scale: 9017.8708819198619,
    },
    {
      level: 16,
      levelValue: "17",
      resolution: 1.0728836059570313e-5,
      scale: 4508.9354409599309,
    },
    {
      level: 17,
      levelValue: "18",
      resolution: 5.3644180297851563e-6,
      scale: 2254.4677204799655,
    },
    {
      level: 18,
      levelValue: "19",
      resolution: 2.68220901489257815e-6,
      scale: 1127.23386023998275,
    },
    {
      level: 19,
      levelValue: "20",
      resolution: 1.341104507446289075e-6,
      scale: 563.616930119991375,
    },
  ],
});
const LAYER_CONFIG = {
  // 矢量底图
  VECTOR_BASEMAP: [
    {
      type: "tile",
      // url: "https://*************:6443/geoscene/rest/services/Hosted/JHMap4490/MapServer",
      url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/JHBaseMap2023/MapServer",
    },
  ],
  // 影像底图
  IMAGE_BASEMAP: [
    {
      type: "web-tile",
      id: "baseMap",
      title: "金华影像底图",
      urlTemplate:
        "https://csdn.dsjj.jinhua.gov.cn:8101/zjzwfw/services/wmts/imgmap/default/oss?layer=imgmap&style=default&tilematrixset=default028mm&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image/jpgpng&TileMatrix={level}&TileCol={col}&TileRow={row}&token=sy-6786f525-a9bd-477f-ab4a-f26d29524077",
      subDomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"],
      tileInfo: tileInfo,
      spatialReference: { wkid: 4490 },
      fullExtent: {
        xmin: -180,
        ymin: -90,
        xmax: 180,
        ymax: 90,
        spatialReference: { wkid: 4490 },
      },
    },
    {
      id: "IMAGE_MARK",
      type: "tile",
      // url: "https://*************/server/rest/services/Hosted/YXZhuji/MapServer",
      url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/YXZhuiji_xfw/MapServer",
    },
  ],
  // // 影像注记
  // IMAGE_MARK: {
  //   id: "IMAGE_MARK",
  //   type: "tile",
  //   url: "https://*************:6443/geoscene/rest/services/Hosted/JHMap_zhuji/MapServer",
  // },
  // 行政区
  shiliangqiepian: {
    id: "shiliangqiepian",
    type: "tile",
    // url: "https://*************:6443/geoscene/rest/services/Hosted/shiliangqiepian/VectorTileServer",
    url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/Country_scale/MapServer",
  },
  // 路网数据
  TRA_NET_LN: {
    id: "TRA_NET_LN",
    type: "tile",
    url:
      window.location.hostname === "127.0.0.1"
        // ? `https://geoplat.geoscene.cn/server/rest/services/TRA_NET_LN/MapServer`
        ? `https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/TRA_NET_LN/MapServer`
        : `https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/TRA_NET_LN/MapServer`,
  },
  // 水面数据
  WATER_M: {
    id: "WATER_M",
    type: "feature",
    url:
      window.location.hostname === "127.0.0.1"
        ? `https://geoplat.geoscene.cn/server/rest/services/HYD_PY_SymDiff/MapServer/0`
        : // : `https://*************:6443/geoscene/rest/services/Hosted/HYD_PY/MapServer/0`,
          `https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/HYD_PY/MapServer`,
  },

  // 流光线数据
  RailwayLine: {
    id: "RAILWAY_LINW",
    type: "feature",
    url:
      window.location.hostname === "127.0.0.1"
        ? `http://geoplat.geoscene.cn/server/rest/services/TRA_LRR_LN/MapServer/0`
        : // : `https://*************:6443/geoscene/rest/services/daluzizao/MapServer/0`,
          "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/TRA_NET_LN/MapServer/0",
  },

  // 气象数据
  WEATHER: {
    // 温度
    temperature: [
      {
        id: "temperature_1hours",
        type: "tile",
        hour: 1,
        // url: "https://*************:6443/geoscene/rest/services/Hosted/2022120811/MapServer",
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/2022120800_t/MapServer",
      },
      {
        id: "temperature_3hours",
        type: "tile",
        hour: 3,
        // url: "https://*************:6443/geoscene/rest/services/Hosted/2022120805_t/MapServer",
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/2022120802_t/MapServer",
      },
      {
        id: "temperature_6hours",
        type: "tile",
        hour: 6,
        // url: "https://*************:6443/geoscene/rest/services/Hosted/2022120802_t/MapServer",
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/2022120805_t/MapServer",
      },
      {
        id: "temperature_12hours",
        type: "tile",
        hour: 12,
        // url: "https://*************:6443/geoscene/rest/services/Hosted/2022120800_t/MapServer",
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/2022120811/MapServer",
      },
    ],
    // 湿度
    humidity: [
      {
        id: "humidity_1hours",
        type: "tile",
        hour: 1,
        // url: "https://*************:6443/geoscene/rest/services/Hosted/2022120811_rh/MapServer",
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/2022120800_rh/MapServer",
      },
      {
        id: "humidity_3hours",
        type: "tile",
        hour: 3,
        // url: "https://*************:6443/geoscene/rest/services/Hosted/2022120805_rh/MapServer",
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/2022120802_rh/MapServer",
      },
      {
        id: "humidity_6hours",
        type: "tile",
        hour: 6,
        // url: "https://*************:6443/geoscene/rest/services/Hosted/2022120802_rh/MapServer",
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/2022120805_rh/MapServer",
      },
      {
        id: "humidity_12hours",
        type: "tile",
        hour: 12,
        // url: "https://*************:6443/geoscene/rest/services/Hosted/2022120800_rh/MapServer",
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/2022120811_rh/MapServer",
      },
    ],
    // 云量
    clound: [
      {
        id: "clound_1hours",
        type: "tile",
        hour: 1,
        // url: "https://*************:6443/geoscene/rest/services/Hosted/2022120811_cloud/MapServer",
        url:"https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/2022120800_cloud/MapServer",
      },
      {
        id: "clound_3hours",
        type: "tile",
        hour: 3,
        // url: "https://*************:6443/geoscene/rest/services/Hosted/2022120805_cloud/MapServer",
        url:"https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/2022120802_cloud/MapServer",
      },
      {
        id: "clound_6hours",
        type: "tile",
        hour: 6,
        // url: "https://*************:6443/geoscene/rest/services/Hosted/2022120802_cloud/MapServer",
        url:"https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/2022120805_cloud/MapServer",
      },
      {
        id: "clound_12hours",
        type: "tile",
        hour: 12,
        // url: "https://*************:6443/geoscene/rest/services/Hosted/2022120800_cloud/MapServer",
        url:"https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/2022120811_cloud/MapServer",
      },
    ],
    // 风速
    wind: [
      {
        id: "wind_1hours",
        type: "tile",
        hour: 1,
        // url: "https://*************:6443/geoscene/rest/services/Hosted/2022120811_wWS/MapServer",
        url:"https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/2022120800_wWS/MapServer"
      },
      {
        id: "wind_3hours",
        type: "tile",
        hour: 3,
        // url: "https://*************:6443/geoscene/rest/services/Hosted/2022120805_wWS/MapServer",
        url:"https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/2022120802_wWS/MapServer"
      },
      {
        id: "wind_6hours",
        type: "tile",
        hour: 6,
        // url: "https://*************:6443/geoscene/rest/services/Hosted/2022120802_wWS/MapServer",
        url:"https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/2022120805_wWS/MapServer"
      },
      {
        id: "wind_12hours",
        type: "tile",
        hour: 12,
        // url: "https://*************:6443/geoscene/rest/services/Hosted/2022120800_wWS/MapServer",
        url:"https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/2022120811_wWS/MapServer"
      },
    ],
  },

  // 倾斜摄影数据
  INTEGRATED_Mesh: [
    // {
    //   id: "integratedMesh001",
    //   type: "integrated-mesh",
    //   url: "https://*************:6443/geoscene/rest/services/Hosted/SZF4490o/SceneServer",
    //   elevationInfo: {
    //     mode: "on-the-ground",
    //   },
    // },
    // {
    //   id: "integratedMesh002",
    //   type: "integrated-mesh",
    //   url: "https://*************:6443/geoscene/rest/services/Hosted/JB4490o/SceneServer",
    //   elevationInfo: {
    //     mode: "on-the-ground",
    //   },
    // },
    // {
    //   id: "integratedMesh003",
    //   type: "integrated-mesh",
    //   url: "https://*************:6443/geoscene/rest/services/Hosted/JN4490o/SceneServer",
    //   elevationInfo: {
    //     mode: "on-the-ground",
    //   },
    // },
    // {
    //   id: "integratedMesh004",
    //   type: "integrated-mesh",
    //   url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/qg_2018/SceneServer",
    //   elevationInfo: {
    //     mode: "on-the-ground",
    //   },
    // },
    // {
    //   id: "integratedMesh005",
    //   type: "integrated-mesh",
    //   url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/qg_2021_Project/SceneServer",
    //   elevationInfo: {
    //     mode: "on-the-ground",
    //   },
    // },

    {
      id: "integratedMesh006",
      type: "integrated-mesh",
      url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/jx18hb_Project/SceneServer",
    },
    {
      id: "integratedMesh007",
      type: "integrated-mesh",
      url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/qg_2021_Project2/SceneServer",
    },
    {
      id: "integratedMesh008",
      type: "integrated-mesh",
      url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/b_2022_Project/SceneServer",
    },
    {
      id: "integratedMesh009",
      type: "integrated-mesh",
      // url: "https://portal.mapserver.gis:6443/geoscene/rest/services/Hosted/SZF4490o/SceneServer",
      url:"https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/szf_merge/SceneServer"
    },
  ],

  // 白膜，精模
  WHITE_MODEL: {
    id: "WHITE_MODEL",
    type: "scene",
    // url: "https://*************:6443/geoscene/rest/services/Hosted/JHWM/SceneServer",
    //url: "https://*************:6443/geoscene/rest/services/Hosted/WMH/SceneServer@can ",
    url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/JHWM/SceneServer",
    elevationInfo: {
      mode: "on-the-ground",
      // mode: "absolute-height",
    },
  },
  PURE_MODEL: {
    id: "PURE_MODEL",
    type: "scene",
    elevationInfo: {
      mode: "absolute-height",
      offset: -34,
    },
    url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/obj_01/SceneServer",
  },
  // BIM 模型
  BIM_MODEL: {
    id: "BIM_MODEL",
    type: "building-scene",
    // type: "scene",
    //url: "https://*************:6443/geoscene/rest/services/Hosted/BIM/SceneServer",
    url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BIM_01/SceneServer",
  },
  VIDEO_BUFFER: {
    id: "VIDEO_BUFFER",
    type: "feature",
    url:
      window.location.hostname === "127.0.0.1"
        ? `https://geoplat.geoscene.cn/server/rest/services/Hosted/video_Project/FeatureServer`
        : `https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/video/FeatureServer`,
  },
};

export function getLayerConfigById(id) {
  if (id in LAYER_CONFIG) {
    return LAYER_CONFIG[id];
  } else {
    throw new Error(`无id=${id}的图层配置！`);
  }
}
