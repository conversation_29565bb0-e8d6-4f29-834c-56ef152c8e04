@font-face {
  font-family: 'YouSheBiaoTiHei';src: url('/static/fonts/YouSheBiaoTiHei-2.ttf');
}

/* 关闭winh弹窗样式 */
.top-close {
  width: 120px;
  height: 120px;
  background-image: url('/static/images/common/components/close-1.png');
  background-size: 100%;
}

#zindex_bg {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(3, 24, 39, 0.6);
  z-index: 0;
  display: block;
  top: 0;
}

* {
  margin: 0;
  padding: 0;
}

.c-blue {
  color: #3cfdff;
}

#app {
  overflow: hidden;
  width: 100vw;
  height: 100vh;
  position: relative;
  min-width: 3840px;
  min-height: 2160px;
}

.map_center {
  width: 100%;
  height: 100%;
  top: 0;
  z-index: 5;
  position: absolute;
  background: url('/static/images/index/bg-main.png') no-repeat;
  background-size: 100% 100%;
}

/* 头 */
.header_img {
  background: url('/static/images/index/header_bg.png') no-repeat;
  width: 100%;
  height: 180px;
  z-index: 777;
  margin: 0 auto;
  position: absolute;
  top: 0;
  font-size: 38px;
  color: #fff;
  background-size: cover;
}

.header_title_text {
  position: absolute;
  width: 3838px;
  height: 219px;
  line-height: 140px;
  text-align: center;
  font-size: 95px;
  font-weight: bold;
  font-family: YouSheBiaoTiHei;
  left: calc(50% - 1920px);
  z-index: 2;
  text-align: center;
  background: url('/static/images/index/header-title-bg.png') no-repeat;
  background-size: cover;
}

.header_text {
  background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(#ebf2ff), to(#94aadb));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

#header_weather {
  width: max-content;
  position: absolute;
  left: calc(50% - 1196px);
  top: 35px;
  display: flex;
  align-items: center;
}

#header_week {
  position: absolute;
  right: 2%;
  top: 25px;
}

.nowDate i {
  font-size: 40px !important;
  color: #fff;
  margin-right: 30px;
}

.city_select {
  position: absolute;
  left: 0;
  width: 345px;
  height: 100px;
  background: url('/static/images/index/city_bg.png') no-repeat;
  background-size: 100%;
  z-index: 99;
}

.el-input {
  width: 83% !important;
  margin-left: 12px;
}

.el-input__inner {
  color: #fff !important;
  height: 100px !important;
  line-height: 95px !important;
  border-color: transparent !important;
  font-size: 48px !important;
  font-style: oblique;
  font-weight: bold;
  background: linear-gradient(0deg,
      #9eb7ef 1.8798828125%,
      #a3d0ff 50.244140625%,
      #ffffff 53.0029296875%,
      #ebf2ff 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
}

.el-icon-arrow-up:before {
  content: '\e790' !important;
}

.el-select .el-input .el-select__caret.is-reverse {
  transform: rotateZ(180deg) !important;
}

.el-select .el-input .el-select__caret {
  color: #eef0f5 !important;
  transform: rotateZ(0deg) !important;
}

.el-input__suffix {
  right: -20px !important;
}
.el-scrollbar__wrap {
  overflow: initial;}
.el-input__icon {
  width: max-content !important;
}

.el-select .el-input .el-select__caret {
  font-size: 35px !important;
}

.el-select-dropdown {
  /*top: 75px !important;*/
  min-width: initial !important;
  background-color: #02103c94 !important;
  border: none !important;
  width: 280px;
}

.el-popper[x-placement^='bottom'] {
  margin-top: 0 !important;
}

.el-select-dropdown__item {
  text-align: center;
  font-size: 25px !important;
  color: #cfcfd6 !important;
  height: 44px !important;
  line-height: 44px !important;
}

.el-select-dropdown__wrap {
  max-height: initial !important;
}

.el-select-dropdown__item.hover {
  background-color: #27508f !important;
}

.el-popper .popper__arrow,
.el-popper .popper__arrow::after {
  display: none !important;
}
.el-scrollbar__wrap{
  overflow-y: scroll !important;
  overflow-x: hidden !important;
  margin-right: 0 !important;
}
/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 0;
}
/* 滚动槽 */
::-webkit-scrollbar-track {
  border-radius: 5px;
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(35, 144, 207, 0.4);
}
::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(27, 146, 215, 0.8);
}

/* 展开和收起iframe */
.close_left_right_iframe_btn {
  width: 74px;
  height: 74px;
  background-image: url('/static/images/index/oepn_iframe.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  position: absolute;
  z-index: 778;
  right: 20px;
  top: 130px;
  cursor: pointer;
}

.open_left_iframe_btn {
  display: inline-block;
  width: 15px;
  height: 1900px;
  background-image: url('/static/images/index/open_left_btn_bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  left: 0;
  top: 220px;
  z-index: 99;
}

.open_right_iframe_btn {
  width: 15px;
  height: 1900px;
  background-image: url('/static/images/index/open_right_btn_bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  right: 0;
  top: 220px;
  z-index: 99;
}

.open_left_iframe_btn>div {
  width: 58px;
  height: 297px;
  background-image: url('/static/images/index/open_left_btn.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  left: 15px;
  top: calc(50% - 148px);
  z-index: 99;
}

.open_right_iframe_btn>div {
  width: 58px;
  height: 297px;
  background-image: url('/static/images/index/open_right_btn.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  right: 15px;
  top: calc(50% - 148px);
  z-index: 99;
}

/* 移动位置 */
.map_mapIcon_move {
  left: 3700px !important;
  top: 244px !important;
}

.btns_move {
  left: 30px !important;
}

.back_home {
  width: 78px;
  height: 48px;
  /* background: url('/static/citybrain/') no-repeat; */
  z-index: 888;
  position: absolute;
  z-index: 778;
  right: 105px;
  top: 142px;
  display: none;
  cursor: pointer;
}


/* 地图弹窗样式 */
.mapPopup {
  position: fixed;
  z-index: 1;
  min-width: 300px;
  background: none;
  box-shadow: none;
  transform-origin: 50% 100%;
  transition: transform 0.3s ease-out;
}

.show {
  transform: translate(-50%, calc(-100% - 16px)) scale(1);
}

.hide {
  transform: translate(-50%, calc(-100% - 16px)) scale(0);
}

.esri-view-surface{
  height:2160px !important;
}
.mapPopup .header {
  display: none;
}

.mapPopup .body {
  position: relative;
}

.mapPopup .body::before {
  display: none;
}

.mapPopup .bodyContent {
  z-index: 1;
  padding: 0px;
}

.mapPopup .container {
  height: 100%;
  width: 100%;
}
.bounce {
  animation: 1s rowUp linear infinite alternate;
  -webkit-animation: 1s rowUp linear infinite alternate;
}
@keyframes rowUp {
  0% {
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
  }

  100% {
    transform: translateY(0px);
  }
}
