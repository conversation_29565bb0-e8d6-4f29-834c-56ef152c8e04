import WebTileLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/WebTileLayer.js";
import MapImageLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/MapImageLayer.js";
import IntegratedMeshLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/IntegratedMeshLayer.js";
import SceneLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/SceneLayer.js";
import FeatureLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/FeatureLayer.js";
import TileLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/TileLayer.js";
import VectorTileLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/VectorTileLayer.js";
import ImageryTileLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/ImageryTileLayer.js";
import GeoJSONLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/GeoJSONLayer.js";
import GraphicsLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/GraphicsLayer.js";
import BuildingSceneLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/BuildingSceneLayer.js";
import CSVLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/CSVLayer.js";
import ElevationLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/ElevationLayer.js";
import BingMapsLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/BingMapsLayer.js";
import WMTSLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/WMTSLayer.js";
import WMSLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/WMSLayer.js";

// 图层创建工厂方法
let layerFactory = {
  feature: (props) => {
    return new FeatureLayer(props);
  },
  tile: (props) => {
    return new TileLayer(props);
  },
  "vector-tile": (props) => {
    return new VectorTileLayer(props);
  },
  "imagery-tile": (props) => {
    return new ImageryTileLayer(props);
  },
  "map-image": (props) => {
    return new MapImageLayer(props);
  },
  geojson: (props) => {
    return new GeoJSONLayer(props);
  },
  graphics: (props) => {
    return new GraphicsLayer(props);
  },
  "building-scene": (props) => {
    return new BuildingSceneLayer(props);
  },
  scene: (props) => {
    return new SceneLayer(props);
  },
  "web-tile": (props) => {
    return new WebTileLayer(props);
  },
  "integrated-mesh": (props) => {
    // 比如，加载倾斜摄影
    return new IntegratedMeshLayer(props);
  },
  "bing-maps": (props) => {
    return new BingMapsLayer(props);
  },
  csv: (props) => {
    return new CSVLayer(props);
  },
  elevation: (props) => {
    return new ElevationLayer(props);
  },
  "geo-rss": (props) => {
    return new GeoRSSLayer(props);
  },
  imagery: (props) => {
    return new ImageryLayer(props);
  },
  kml: (props) => {
    return new KMLLayer(props);
  },
  "line-of-sight": (props) => {
    return new LineOfSightLayer(props);
  },
  media: (props) => {
    return new MediaLayer(props);
  },
  "ogc-feature": (props) => {
    return new OGCFeatureLayer(props);
  },
  "open-street-map": (props) => {
    return new OpenStreetMapLayer(props);
  },
  "point-cloud": (props) => {
    return new PointCloudLayer(props);
  },
  route: (props) => {
    return new RouteLayer(props);
  },
  stream: (props) => {
    return new StreamLayer(props);
  },
  "subtype-group": (props) => {
    return new SubtypeGroupLayer(props);
  },
  voxel: (props) => {
    return new VoxelLayer(props);
  },
  wcs: (props) => {
    return new WCSLayer(props);
  },
  wfs: (props) => {
    return new WFSLayer(props);
  },
  wms: (props) => {
    return new WMSLayer(props);
  },
  wmts: (props) => {
    return new WMTSLayer(props);
  },
};

/**
 * 同步图层创建方法
 * @param {*} layerProps
 * @returns
 */
function layerCreate(layerProps) {
  const { type } = layerProps;
  const propsClone = { ...layerProps };
  delete propsClone.type;
  const types = Object.keys(layerFactory);
  if (types.includes(type)) {
    const layer = layerFactory[type](propsClone);
    return layer;
  } else {
    console.log("无该类型图层创建函数，请自行扩展。");
  }
}

/**
 * 判断图层是否添加
 * @param {*} view
 * @param {*} layerConfig
 */
function isLayerAdd(view, layerConfig) {
  let isAdd = false;
  const layer = view.map.findLayerById(layerConfig.id);
  if (layer) {
    isAdd = true;
  }
}

/**
 * 添加图层到地图
 * @param {*} layerConfigs
 */
function addLayersToMap(view, layerConfigs) {
  let layers;
  if (Array.isArray(layerConfigs)) {
    layers = [];
    for (let i = 0, len = layerConfigs.length; i < len; i++) {
      const item = layerConfigs[i];
      const layer = layerCreate(item);
      layers.push(layer);
      view.map.addMany(layers);
    }
  } else {
    layers = layerCreate(layerConfigs);
    view.map.add(layers);
  }

  return layers;
}

/**
 * 修改图层的显影
 * @param {*} layers
 * @param {*} visible
 */
function changeLayersVisible(layers, visible) {
  if (Array.isArray(layers)) {
    for (let i = 0, len = layers.length; i < len; i++) {
      const item = layers[i];
      if (item) {
        item.visible = visible;
      }
    }
  } else {
    layers = visible;
  }
}

export { layerCreate, isLayerAdd, addLayersToMap, changeLayersVisible };
