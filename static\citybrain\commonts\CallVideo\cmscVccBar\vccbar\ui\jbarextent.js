﻿function showLog(Text) {
    var oTextareaInfo= document.getElementById("TextareaInfo");
    if(oTextareaInfo != null)
	    oTextareaInfo.value = oTextareaInfo.value + Text;
}
function emptyLog() {
    var oTextareaInfo= document.getElementById("TextareaInfo");
    if(oTextareaInfo != null)
        oTextareaInfo.value = "";
}
function onOnDebug(Text){
    showLog(Text+"\r\n");
}

///////////////////////////////////////////////////
// 电话条重载事件函数
///////////////////////////////////////////////////

//呼叫事件
function onOnCallRing(CallingNo,CalledNo,OrgCalledNo,CallData,SerialID,ServiceDirect,CallID,UserParam,TaskID,UserDn,AgentDn,AreaCode,fileName,networkInfo,queueTime,opAgentID,ringTimer,projectID, accessCode,taskName,cityName,userType,lastServiceId,lastServiceName,accessNumber) {
    showLog("【OnCallRing】：\r\n");
    showLog("         CallingNo：【"+CallingNo+"】\r\n");
    showLog("         CalledNo：【"+CalledNo+"】\r\n");
    showLog("         OrgCalledNo：【"+OrgCalledNo+"】\r\n");
    showLog("         CallData：【"+CallData+"】\r\n");
    showLog("         CallID ：【"+CallID+"】\r\n");
    showLog("         SerialID ：【"+SerialID+"】\r\n");
    showLog("         ServiceDirect ：【"+ServiceDirect+"】\r\n");
    showLog("         UserParam ：【"+UserParam+"】\r\n");
    showLog("         TaskID ：【"+TaskID+"】\r\n");
    showLog("         UserDn ：【"+UserDn+"】\r\n");
    showLog("         AgentDn ：【"+AgentDn+"】\r\n");
    showLog("         AreaCode ：【"+AreaCode+"】\r\n");
    showLog("         fileName ：【"+fileName+"】\r\n");
    showLog("         networkInfo：【"+networkInfo+"】\r\n");
    showLog("         queueTime ：【"+queueTime+"】\r\n");
    showLog("         opAgenID ：【"+opAgentID+"】\r\n");
    showLog("         ringTimer ：【"+ringTimer+"】\r\n");
    showLog("         projectID ：【"+projectID+"】\r\n");
	showLog("         accessCode ：【"+accessCode+"】\r\n");
    showLog("         taskName ：【"+taskName+"】\r\n");
    showLog("         cityName ：【"+cityName+"】\r\n");
    showLog("         userType ：【"+userType+"】\r\n");
    showLog("         lastServiceId ：【"+lastServiceId+"】\r\n");
    showLog("         lastServiceName ：【"+lastServiceName+"】\r\n");
    showLog("         accessNumber ：【"+accessNumber+"】\r\n");
    showLog(" *******************************************************************\r\n");

}
function onOnAnswerCall(UserNo,AnswerTime,SerialID,ServiceDirect,CallID,UserParam,TaskID,AV,TC,HaveAsrEvent) {
    showLog(" 【OnAnswerCall】:\r\n");
    showLog("         AnswerTime ：【"+AnswerTime+"】\r\n");
    showLog("        UserNo ：【"+UserNo+"】\r\n");
    showLog("        CallID ：【"+CallID+"】\r\n");
    showLog("        SerialID ：【"+SerialID+"】\r\n");
    showLog("        ServiceDirect ：【"+ServiceDirect+"】\r\n");
    showLog("        UserParam ：【"+UserParam+"】\r\n");
    showLog("        TaskID ：【"+TaskID+"】\r\n");
    showLog("        AV ：【"+AV+"】\r\n");
    showLog("        TC ：【"+TC+"】\r\n");
    showLog("        HaveAsrEvent ：【"+HaveAsrEvent+"】\r\n");
    showLog(" *******************************************************************\r\n");

}

function onOnCallEnd(callID,serialID,serviceDirect,userNo,bgnTime,endTime,agentAlertTime,userAlertTime,fileName,directory,disconnectType,userParam,taskID,serverName,networkInfo) {
    showLog(" 【OnCallEnd】:\r\n");
    showLog("         userNo ：【"+userNo+"】\r\n");
    showLog("         CallID   ：【"+callID+"】\r\n");
    showLog("         SerialID ：【"+serialID+"】\r\n");
    showLog("         ServiceDirect  ：【"+serviceDirect+"】\r\n");
    showLog("         userAlertTime  ：【"+userAlertTime+"】\r\n");
    showLog("         agentAlertTime ：【"+agentAlertTime+"】\r\n");
    showLog("         fileName      ：【"+fileName+"】\r\n");
    showLog("         directory      ：【"+directory+"】\r\n");
    showLog("         disconnectType      ：【"+disconnectType+"】\r\n");
    showLog("         userParam      ：【"+userParam+"】\r\n");
    showLog("         taskID         ：【"+taskID+"】\r\n");
    showLog("         serverName         ：【"+serverName+"】\r\n");
    showLog("         networkInfo         ：【"+networkInfo+"】\r\n");
    showLog(" *******************************************************************\r\n");

    DisplayDiv("divVedioDlg",false);
}

//提示事件
function onOnPrompt(code,description) {
    showLog("【OnPrompt】：\r\n");
    showLog(" code:【"+code+"】 description:【"+description+"】\r\n");
    showLog(" *******************************************************************\r\n");
}
function onReportBtnStatus(btnIDS) {
    if( appVccBar != null){
        appVccBar.ChangeBtnStatus(btnIDS);
        appVccBar.SetAgentStatus(_VccBar.GetAgentStatus());
    }
    showLog("【ReportBtnStatus】：\r\n");
    showLog("         可现状态值   ：【"+btnIDS+"】\r\n 当前座席状态：【"+_VccBar.GetAgentStatus()+"】\r\n");
    showLog(" *******************************************************************\r\n");
}
function onOnInitalSuccess() {
    showLog(" *******************************************************************\r\n");
}

function onOnInitalFailure(code,description,descode) {
    showLog("【OnInitalFailure】\r\n 【"+code+"】 【"+description+"】【"+descode+"】\r\n");
    showLog(" *******************************************************************\r\n");
}
function onOnBarExit(code,description) {
    showLog("【OnBarExit】 \r\n【"+code+"】 【"+description+"】\r\n");
    showLog(" *******************************************************************\r\n");
}
function onOnAgentWorkReport(workStatus,description) {
    showLog("【OnAgentWorkReport】 场景编号：【"+workStatus+"】 场景描述：【"+description+"】\r\n");
    showLog(" *******************************************************************\r\n");
}


function onOnUpdateVideoWindow(param){
  if(param.key_word == "GetVideoViews"){
    DisplayDiv("divVedioDlg",true);
    param.param.SetVideoViews("agent_selfView","agent_remoteView","agent_shareView");
  }
  else if(param.key_word == "OnGetLocalView"){
    showLog(JSON.stringify(param)+"\r\n");
  }
  else if(param.key_word == "OnGetRemoteView"){
    showLog(JSON.stringify(param)+"\r\n");
  }
  else if(param.key_word == "OnLeaveSuccess"){
    DisplayDiv("divVedioDlg",false);
  }
}

//命令异步返回事件
function onOnMethodResponseEvent(cmdName,param){
    showLog("【OnMethodResponseEvent】：\r\n");
    showLog(" cmdName:【"+cmdName+"】\r\n");
    showLog(" return param:【"+JSON.stringify(param)+"】\r\n");
    showLog(" *******************************************************************\r\n");
}

///////////////////////////////////////////////////
// 命令函数
///////////////////////////////////////////////////

function DisplayDiv(id,flag){
    var oDiv = document.getElementById(id);
    if(oDiv == null)
        return;
    if(flag == 0)
        oDiv.style.display = "none";
    else
        oDiv.style.display = "block";
}
function onOnQueueReport(params) {
    let queueInfo = dealCinccQueueData(params)
    showLog("【OnQueueReport】：\r\n");
    showLog("【"+queueInfo.serviceId+"】【"+queueInfo.skillName+"】【"+queueInfo.num+"】\r\n");
}
function dealCinccQueueData(xmlStr) {
    let obj = {};
    let xmIDoc = $.parseXML(xmlStr);
    obj.serviceId = $(xmIDoc).find('serviceInfo').attr('serviceid')
    obj.skillName = $(xmIDoc).find('serviceInfo').attr('servicename')
    obj.num = $(xmIDoc).find('service').attr( 'queuelen');
    return obj;
}
