<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>视频通话弹窗</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/js/jslib/s.min.vue.js"></script>
    <!-- <link href="../../static/css/commonObjyyh.css" rel="stylesheet"> -->
    <style>
      .rwgz-tc {
        width: 3220px;
        height: 1925px;
        /* background: url("../../static/img/zhddzx/bg_panel.png") no-repeat; */
        background-size: 100% 100%;
      }
      .rw-title {
        padding: 46px 3% 0;
        width: 95%;
        height: 60px;
        line-height: 60px;
      }
      .content {
        position: absolute;
        top: 130px;
        left: 133px;
        width: 91%;
        height: 80%;
        box-sizing: border-box;
        margin: 20px auto;
        text-align: center;
      }
      .iframe-phone {
        width: 100%;
        height: 112%;
      }
      #panel {
        position: relative;
        width: 3250px;
        height: 1920px;
      }
      .close {
        position: absolute;
        right: 44px;
        top: 73px;
        color: #fff;
        font-size: 80px;
        margin-right: 40px;
        cursor: pointer;
      }
      .head > span[data-v-29e9f896] {
        font-size: 62px !important;
      }
    </style>
  </head>
  <body>
    <div class="rwgz-tc" id="panel">
      <s-dialog title="视频通话" width="3250px" height="1920px"></s-dialog>
      <div class="close" id="rwClose1">×</div>
      <!-- <div class="rw-title flex-between">
        <div class="fs-44 text-mid-yellow" id="rwTitle">视频通话</div>
        <div class="close cursor" id="rwClose1"></div>
      </div> -->
      <div class="content">
        <iframe
          src=""
          frameborder="0"
          id="iframePhone"
          scrolling="no"
          allow="microphone;camera;"
          class="iframe-phone"
        ></iframe>
      </div>
    </div>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <!-- <script src="../../static/js/jslib/commonObjZhdd.js"></script> -->

    <script>
      new Vue({ el: "#panel" });
      window.addEventListener(
        "message",
        function (event) {
          //子获取父消息
          let newData;
          if (typeof event.data == "object") {
            newData = event.data;
          } else {
            newData = JSON.parse(event.data.argument);
          }
          if (newData.phone !== undefined && newData.phone !== "undefined") {
            window.sessionStorage.setItem("voltePhone", newData.phone);
          }
          $("#iframePhone").attr(
            "src",
            "https://wzywt.wzsafety.gov.cn/dispatch-ms/#/conference/single_url?access_type=1&local_chatid=JHCSDN001&volteIds=" +
              window.sessionStorage.getItem("voltePhone")
          );
        },
        false
      );
      function getUrlParamValue(paramName) {
        const reg = new RegExp("(^|&)" + paramName + "=([^&]*)(&|$)", "i");
        const r = window.location.search.substr(1).match(reg);
        if (r != null) {
          // 解码uri
          return decodeURI(r[2]);
        } else {
          const pr = window.parent.location.search.substr(1).match(reg);
          if (pr != null) {
            return decodeURI(pr[2]);
          } else {
            return null;
          }
        }
      }
      $("#rwClose1").click(function () {
        window.parent.lay.closeIframeByNames(["zbVideo"]);
        let obj = {
          type: "closeIframe",
          name: "zbVideo",
        };
        window.parent.postMessage(JSON.stringify(obj), "*");
      });
    </script>
  </body>
</html>
