window.addEventListener('message', async function (e) {
  if (e.data.type === 'doVideoMeeting') {
    console.log('监听到doVideoMeeting')
    console.log(e.data)
    dd.createVideoMeeting({
      title: '视频会议',
      calleeStaffIds: e.data.accountIds, //人员列表
    })
      .then((res) => {
        console.log('视频会议调用成功')
      })
      .catch((err) => {
        console.log('视频会议调用失败', err)
      })
  }
  if (e.data.type === 'doPhoneCall') {
    console.log('监听到doPhoneCall')
    console.log(e.data)
    dd.callPhone({
      phoneNum: e.data.phone,//电话号码
    }).then(res => {
      console.log(res)
    }).catch(err => { 
      console.log('电话调用失败', err)
    })
  }
  if (e.data.type === 'pointClick' && navigator.userAgent.toLowerCase().indexOf('dingtalk') > -1) {
    // alert('监听到pointClick喽234')
    console.log(e.data)
    console.log(e.data.data.data)
    console.log('监听到pointClick喽234')
    if (e.data) {
      const item = JSON.parse(e.data.data.data)
      let coor = e.data.data.point.split(',')
      // alert(item)
      // alert(coor)
      if (item.item.esType1) {
        let name = item.item.name
        let type = item.item.esType1
        let objData = {
          funcName: 'customPop',
          coordinates: coor,
          // coordinates: ['119.607129', '29.068155'],
          closeButton: true,
          html: `<div
                            onclick=" this.style.display = 'none'"
                  class="contain"
                  style="
                  position: absolute;

                    height: 200px;
                    width: max-content;
                    display:inline-block;
                    background-color: rgba(0, 0, 0, 0.8);
                    border: 2px solid #00aae2;
                    box-sizing: border-box;
                    border-style: solid;
                    border-width: 4px;
                    border-image-source: linear-gradient(0deg, #32abe4 0%, #0b5aa4 100%);
                    border-image-slice: 1;
                  "
                >
                  <div
                    class="title"
                    style="
                      background: linear-gradient(360deg, #096c8d, #073446);
                      width: 100%;
                      height: 60px;
                      font-size: 32px;
                      color: #fff;
                      padding:0 30px;
                      box-sizing: border-box;
                      line-height: 60px;
                    "
                  >
                   ${type}
                  </div>
                  <div
                    class="content"
                    style="
                      display: flex;
                      align-items: center;
                      font-size: 28px;
                      color: #fff;
                      padding: 20px;
                    "
                  >

                    <span style="display:inline-block;line-hight:30px;align-self: baseline;
              line-height: 55px;
          flex-shrink: 0"> 名称：${name}</span>
                  </div>
                </div>`,
        }
        console.log(top.document.getElementById('map'))
        top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(objData))
      } else if (item.item.type_name === '饮用水监测点') {
        // alert('饮用水监测点-pop')
        let area = item.item.area_name
        let name = item.item.carrier_name
        let type = item.item.type_name
        let danwei = item.item.dept_name

        const res0 = await axios({
          method: 'post',
          url: baseURL.url + '/dtdd/iot/aep/v1/gather/get-value',
          data: {
            device_data_type: item.item.device_data_type,
            did: item.item.did,
          },
        })

        let obj = res0.data.list[0]
        let timeStr = obj.cuiot_push_time ? obj.cuiot_push_time : '--'
        let yy = timeStr.slice(0, 4)
        let MM = timeStr.slice(4, 6)
        let dd = timeStr.slice(6, 8)
        let hh = timeStr.slice(8, 10)
        let mm = timeStr.slice(10, 12)
        let ss = timeStr.slice(12)
        let time = yy + '/' + MM + '/' + dd + ' ' + hh + ':' + mm + ':' + ss
        let state = obj.device_state === 'MISSING' ? '已离线' : '在线'
        let ph = obj.ph
        let yl = obj.yl
        let zd = obj.zd

        let objData = {
          funcName: 'customPop',
          coordinates: coor,

          // coordinates: ['119.607129', '29.068155'],
          closeButton: true,
          html: ` <div
                            onclick=" this.style.display = 'none'"
                style="
                  width: 1000px;
                  position: absolute;
                  border-radius: 5px;
                  background-color: rgba(10, 31, 53, 0.8);
                  z-index: 999999;
                  -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
                  box-shadow: inset 0 0 40px 0 #5ba3fa;
                  padding: 24px;
                "
              >
                <div class="container">
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">所属区域：</span>
                    <span style="color: #eccc83; width: 70%">${area}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">名&nbsp; &nbsp;   &nbsp;称 ：</span>
                    <span style="color: #eccc83; width: 70%">${name}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备状态：</span>
                    <span style="color: #eccc83; width: 70%">${state}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备类型：</span>
                    <span style="color: #eccc83; width: 70%">${type}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">承载单位：</span>
                    <span style="color: #eccc83; width: 70%">${danwei}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">余&nbsp; &nbsp;   &nbsp;氯 ：</span>
                    <span style="color: #eccc83; width: 70%">${yl}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">P&nbsp; &nbsp;   &nbsp;H ：</span>
                    <span style="color: #eccc83; width: 70%">${ph}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">浊&nbsp; &nbsp;   &nbsp;度 ：</span>
                    <span style="color: #eccc83; width: 70%">${zd}</span>
                  </div>
                      <div
                      class="item"
                      style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                    >
                      <span style="width: 30%; text-align: center">采集时间：</span>
                      <span style="color: #eccc83; width: 70%">${time}</span>
                    </div>
                </div>
              </div>`,
        }
        // alert(JSON.stringify(objData))
        top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(objData))
      } else if (item.item.type_name === '水库大坝监测点') {
        let area = item.item.carrier_addr
        let name = item.item.carrier_name
        let time = item.item.create_time
        let type = item.item.type_name
        let danwei = item.item.dept_name
        let objData = {
          funcName: 'customPop',
          coordinates: coor,

          // coordinates: ['119.607129', '29.068155'],
          closeButton: true,
          html: ` <div
                            onclick=" this.style.display = 'none'"
                style="
                  width: 1000px;
                  position: absolute;
                  border-radius: 5px;
                  background-color: rgba(10, 31, 53, 0.8);
                  z-index: 999999;
                  -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
                  box-shadow: inset 0 0 40px 0 #5ba3fa;
                  padding: 24px;
                "
              >
                <div class="container">

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">名&nbsp; &nbsp;   &nbsp;称 ：</span>
                    <span style="color: #eccc83; width: 70%">${name}</span>
                  </div>

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备类型：</span>
                    <span style="color: #eccc83; width: 70%">${type}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">承载单位：</span>
                    <span style="color: #eccc83; width: 70%">${danwei}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">视频关联：</span>
                    <span style="color: #eccc83; width: 70%"></span>
                  </div>
                </div>
              </div>`,
        }
        top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(objData))
      } else if (item.item.type_name === '地质灾害监测点') {
        const res0 = await axios({
          method: 'post',
          url: baseURL.url + '/dtdd/iot/aep/v1/gather/get-value',
          data: {
            device_data_type: item.item.device_data_type,
            did: item.item.did,
          },
        })

        let obj = res0.data.list[0]
        let timeStr = obj.cuiot_push_time ? obj.cuiot_push_time : '--'

        let yy = timeStr.slice(0, 4)
        let MM = timeStr.slice(4, 6)
        let dd = timeStr.slice(6, 8)
        let hh = timeStr.slice(8, 10)
        let mm = timeStr.slice(10, 12)
        let ss = timeStr.slice(12)
        let time = yy + '/' + MM + '/' + dd + ' ' + hh + ':' + mm + ':' + ss

        let state = obj.device_state === 'MISSING' ? '已离线' : '在线'
        let area = item.item.carrier_addr
        let name = item.item.carrier_name
        let type = item.item.type_name
        let danwei = item.item.dept_name
        let objData = {
          funcName: 'customPop',
          coordinates: coor,

          // coordinates: ['119.607129', '29.068155'],
          closeButton: true,
          html: ` <div
                            onclick=" this.style.display = 'none'"
                style="
                  width: 1000px;
                  position: absolute;
                  border-radius: 5px;
                  background-color: rgba(10, 31, 53, 0.8);
                  z-index: 999999;
                  -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
                  box-shadow: inset 0 0 40px 0 #5ba3fa;
                  padding: 24px;
                "
              >
                <div class="container">

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">名&nbsp; &nbsp;   &nbsp;称 ：</span>
                    <span style="color: #eccc83; width: 70%">${name}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备状态：</span>
                    <span style="color: #eccc83; width: 70%">${state}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备类型：</span>
                    <span style="color: #eccc83; width: 70%">${type}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">承载单位：</span>
                    <span style="color: #eccc83; width: 70%">${danwei}</span>
                  </div>
                      <div
                      class="item"
                      style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                    >
                      <span style="width: 30%; text-align: center">采集时间：</span>
                      <span style="color: #eccc83; width: 70%">${time}</span>
                    </div>
                </div>
              </div>`,
        }
        top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(objData))
      } else if (item.item.type_name === '水雨情监测点') {
        const res0 = await axios({
          method: 'post',
          url: baseURL.url + '/dtdd/iot/aep/v1/gather/get-value',
          data: {
            device_data_type: item.item.device_data_type,
            did: item.item.did,
          },
        })
        let obj = res0.data.list[0]
        let timeStr = obj.cuiot_push_time ? obj.cuiot_push_time : '--'

        let yy = timeStr.slice(0, 4)
        let MM = timeStr.slice(4, 6)
        let dd = timeStr.slice(6, 8)
        let hh = timeStr.slice(8, 10)
        let mm = timeStr.slice(10, 12)
        let ss = timeStr.slice(12)
        let time = yy + '/' + MM + '/' + dd + ' ' + hh + ':' + mm + ':' + ss

        let state = obj.device_state === 'MISSING' ? '已离线' : '在线'
        let area = item.item.carrier_addr
        let name = item.item.carrier_name
        let type = item.item.type_name
        let danwei = item.item.dept_name
        let objData = {
          funcName: 'customPop',
          coordinates: coor,

          // coordinates: ['119.607129', '29.068155'],
          closeButton: true,
          html: ` <div
                            onclick=" this.style.display = 'none'"
                style="
                  width: 1000px;
                  position: absolute;
                  border-radius: 5px;
                  background-color: rgba(10, 31, 53, 0.8);
                  z-index: 999999;
                  -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
                  box-shadow: inset 0 0 40px 0 #5ba3fa;
                  padding: 24px;
                "
              >
                <div class="container">
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">所属区域：</span>
                    <span style="color: #eccc83; width: 70%">${area}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">名&nbsp; &nbsp;   &nbsp;称 ：</span>
                    <span style="color: #eccc83; width: 70%">${name}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备状态：</span>
                    <span style="color: #eccc83; width: 70%">${state}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备类型：</span>
                    <span style="color: #eccc83; width: 70%">${type}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">承载单位：</span>
                    <span style="color: #eccc83; width: 70%">${danwei}</span>
                  </div>

                </div>
              </div>`,
        }
        top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(objData))
      } else if (item.item.type_name === '餐厨车GPS') {
        const res0 = await axios({
          method: 'post',
          url: baseURL.url + '/dtdd/iot/aep/v1/gather/get-value',
          data: {
            device_data_type: item.item.gather_value_list[0].device_data_type,
            did: item.item.did,
          },
        })
        console.log(res0)

        let obj = res0.data.list[0]
        let area = item.item.area_name
        let name = item.item.device_name
        let type = item.item.type_name
        let danwei = item.item.dept_name
        let sd = obj.speed
        let fx = obj.direction
        let state = obj.device_state === 'MISSING' ? '已离线' : '在线'
        coor.push(0)

        console.log(coor)

        let gc = obj.altitude
        let time = item.item.create_time

        // axios({
        //   method: 'get',
        //   url: baseURL.url230 + '/iot/aep/v1/gather/list',
        //   data: {
        //     device_data_type: 'COVER_TRAFFIC_GPS_BUS',
        //     did: '5799358',
        //     page_size: 200,
        //   },
        // }).then(function (res) {
        //   console.log(res)
        // })
        // top.document.getElementById('map').contentWindow.Work.funChange(
        //   JSON.stringify({
        //     funcName: 'rmAddMap',
        //     id: 'TDT_TITLE_ID_route',
        //   })
        // )
        // top.document.getElementById('map').contentWindow.Work.funChange(
        //   JSON.stringify({
        //     funcName: 'rmAddMap',
        //     id: 'TDT_TITLE_ID_point',
        //   })
        // )
        // inestance.remove()
        if (top.document.getElementById('map').contentWindow.map.TDT_TITLE_ID) {
          top.document.getElementById('map').contentWindow.map.TDT_TITLE_ID.remove()
        }
        const imgIcon = `${baseURL.url}/static/citybrain/tckz/img/tckz_gj/${type}.png`

        // console.log(await getGjList(item))
        let objData = {
          funcName: 'customPop',
          coordinates: coor,

          // coordinates: ['119.607129', '29.068155'],
          closeButton: true,
          html: ` <div
                  class="pop"
                 style="
                  width: 1000px;
                  position: absolute;
                  border-radius: 5px;
                  background-color: rgba(10, 31, 53, 0.8);
                  z-index: 999999;
                  -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
                  box-shadow: inset 0 0 40px 0 #5ba3fa;
                  padding: 24px;
                "
              >
              <div
                 onclick=" this.parentNode.style.display = 'none'
                 if (
                  window.map.TDT_TITLE_ID
                    ) {
                      window.map.TDT_TITLE_ID.remove()
                    }
                 "

                  style="
                    position: absolute;
                    right: 30px;
                    top: 30px;
                    font-size: 40px;
                    color: #fff;
                    cursor:pointer
                  "
                >
                  x
                </div>
                <div class="container">
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">所属区域：</span>
                    <span style="color: #eccc83; width: 70%">${area}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">名&nbsp; &nbsp;   &nbsp;称 ：</span>
                    <span style="color: #eccc83; width: 70%">${name}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备状态：</span>
                    <span style="color: #eccc83; width: 70%">${state}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备类型：</span>
                    <span style="color: #eccc83; width: 70%">${type}</span>
                  </div>

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">承载单位：</span>
                    <span style="color: #eccc83; width: 70%">${danwei}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">速&nbsp; &nbsp;   &nbsp;度 ：</span>
                    <span style="color: #eccc83; width: 70%">${sd}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">方&nbsp; &nbsp;   &nbsp;向 ：</span>
                    <span style="color: #eccc83; width: 70%">${fx}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">高&nbsp; &nbsp;   &nbsp;程 ：</span>
                    <span style="color: #eccc83; width: 70%">${gc}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px;align-items: center;"
                  >
                    <span style="width: 30%; text-align: center">历史轨迹：</span>
                    <button

                      style="
                        font-size: 28px;
                        color: #fff;
                        background-color:#b98262;
                        border: unset;
                        width: 171px;
                        height: 60%;
                        height: 59px;
                        line-height: 59px;
                      "
                      onclick="

                      top.document.getElementById('map').contentWindow.Work.funChange(
                          JSON.stringify({
                            funcName: 'createTrajectory',
                            data: {
                              id:'TDT_TITLE_ID',
                              type:'dynamic',
                               icon:'${imgIcon}',
                               coordinates: ${await getGjList(item)},
                                  iconStyle: {
                                 'icon-size':2,
                                 'icon-rotate': 360,
                             },
                             style: {
                                 'line-width': 10,
                             },
                             isGlowLine: false,
                             isBezierCurve: false,
                             color: ['#85d4b1'],
                             loop:false,
                             steps: 100
                            }
                                })
                                     )

                                     "
                    >
                      点击查看
                    </button>
                  </div>
                </div>
              </div>`,
        }
        // [
        //           [119.745613,29.07616],
        //           [119.74559,29.076169],
        //           [119.74559,29.076169],
        //           [119.745582,29.07615],
        //           [119.74559,29.076139],
        //           [119.745597,29.07615],
        //           [119.745597,29.07616],
        //           [119.74559,29.07616],
        //           [119.745597,29.076179],
        //           [119.745613,29.07616],
        //       ],
        top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(objData))

        // axios({
        //   method: 'post',
        //   url: baseURL.url + '/dtdd/iot/aep/v1/gather/get-value',
        //   data: {
        //     device_data_type:
        //       item.item.gather_value_list[0].device_data_type,
        //     did: item.item.did,
        //   },
        // }).then(function (res) {
        //   console.log(res)
        // })
        // $api('/iot/aep/v1/gather/list', {
        //   device_data_type: 'COVER_TRAFFIC_GPS_BUS',
        //   did: item.item.did,
        // }).then((res) => {
        //   console.log(res)
        // })
      } else if (item.item.type_name === '120急救车GPS') {
        const res0 = await axios({
          method: 'post',
          url: baseURL.url + '/dtdd/iot/aep/v1/gather/get-value',
          data: {
            device_data_type: item.item.gather_value_list[0].device_data_type,
            did: item.item.did,
          },
        })
        console.log(res0)

        let obj = res0.data.list[0]
        let area = item.item.area_name
        let name = item.item.device_name
        let type = item.item.type_name
        let danwei = item.item.dept_name
        let sd = obj.speed
        let fx = obj.direction
        let state = obj.device_state === 'MISSING' ? '已离线' : '在线'
        coor.push(0)

        console.log(coor)

        let gc = obj.altitude
        let time = item.item.create_time

        // axios({
        //   method: 'get',
        //   url: baseURL.url230 + '/iot/aep/v1/gather/list',
        //   data: {
        //     device_data_type: 'COVER_TRAFFIC_GPS_BUS',
        //     did: '5799358',
        //     page_size: 200,
        //   },
        // }).then(function (res) {
        //   console.log(res)
        // })
        // top.document.getElementById('map').contentWindow.Work.funChange(
        //   JSON.stringify({
        //     funcName: 'rmAddMap',
        //     id: 'TDT_TITLE_ID_route',
        //   })
        // )
        // top.document.getElementById('map').contentWindow.Work.funChange(
        //   JSON.stringify({
        //     funcName: 'rmAddMap',
        //     id: 'TDT_TITLE_ID_point',
        //   })
        // )
        // inestance.remove()
        if (top.document.getElementById('map').contentWindow.map.TDT_TITLE_ID) {
          top.document.getElementById('map').contentWindow.map.TDT_TITLE_ID.remove()
        }
        let pageSize = 50
        const imgIcon = `${baseURL.url}/static/citybrain/tckz/img/tckz_gj/${type}.png`

        // axios({
        //   method: 'post',
        //   url: baseURL.url + '/dtdd/iot/aep/v1/gather/list',
        //   data: {
        //     device_data_type:
        //       item.item.gather_value_list[0].device_data_type,
        //     did: item.item.did,
        //     page_size: pageSize,
        //   },
        // }).then(function (res) {
        //   const imgIcon = `${baseURL.url}/static/citybrain/tckz/img/tckz_gj/${type}.png`
        //   console.log(type, 'type')
        //   console.log(res)
        //   let jwdArr = res.data.data.rows.map((item) => {
        //     return [
        //       item.CUIOT_CARRIER_LONGITUDE - 0,
        //       item.CUIOT_CARRIER_LATITUDE - 0,
        //     ]
        //   })

        //   let jwdS = JSON.stringify(jwdArr)
        //   console.log(jwdArr)
        let objData = {
          funcName: 'customPop',
          coordinates: coor,

          // coordinates: ['119.607129', '29.068155'],
          closeButton: true,
          html: ` <div
                  class="pop"
                 style="
                  width: 1000px;
                  position: absolute;
                  border-radius: 5px;
                  background-color: rgba(10, 31, 53, 0.8);
                  z-index: 999999;
                  -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
                  box-shadow: inset 0 0 40px 0 #5ba3fa;
                  padding: 24px;
                "
              >
              <div
                 onclick=" this.parentNode.style.display = 'none'
                 if (
                  window.map.TDT_TITLE_ID
                    ) {
                      window.map.TDT_TITLE_ID.remove()
                    }

                 "

                  style="
                    position: absolute;
                    right: 30px;
                    top: 30px;
                    font-size: 40px;
                    color: #fff;
                    cursor:pointer
                  "
                >
                  x
                </div>
                <div class="container">
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">所属区域：</span>
                    <span style="color: #eccc83; width: 70%">${area}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">名&nbsp; &nbsp;   &nbsp;称 ：</span>
                    <span style="color: #eccc83; width: 70%">${name}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备状态：</span>
                    <span style="color: #eccc83; width: 70%">${state}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备类型：</span>
                    <span style="color: #eccc83; width: 70%">${type}</span>
                  </div>

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">承载单位：</span>
                    <span style="color: #eccc83; width: 70%">${danwei}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">速&nbsp; &nbsp;   &nbsp;度 ：</span>
                    <span style="color: #eccc83; width: 70%">${sd}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">方&nbsp; &nbsp;   &nbsp;向 ：</span>
                    <span style="color: #eccc83; width: 70%">${fx}</span>
                  </div>

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px;align-items: center;"
                  >
                    <span style="width: 30%; text-align: center">历史轨迹：</span>
                    <button

                      style="
                        font-size: 28px;
                        color: #fff;
                        background-color:#b98262;
                        border: unset;
                        width: 171px;
                        height: 60%;
                        height: 59px;
                        line-height: 59px;
                      "
                      onclick="top.document.getElementById('map').contentWindow.Work.funChange(
                          JSON.stringify({
                            funcName: 'createTrajectory',
                            data: {
                              id:'TDT_TITLE_ID',
                              type:'dynamic',
                               icon:'${imgIcon}',
                               coordinates: ${await getGjList(item)},
                                  iconStyle: {
                                 'icon-size':2,
                                 'icon-rotate': 360,
                             },
                             style: {
                                 'line-width': 10,
                             },
                             isGlowLine: false,
                             isBezierCurve: false,
                             color: ['#85d4b1'],
                             loop:false,
                             steps: 100
                            }
                                       })
                                     )

                                     "
                    >
                      点击查看
                    </button>
                  </div>
                </div>
              </div>`,
        }
        // [
        //           [119.745613,29.07616],
        //           [119.74559,29.076169],
        //           [119.74559,29.076169],
        //           [119.745582,29.07615],
        //           [119.74559,29.076139],
        //           [119.745597,29.07615],
        //           [119.745597,29.07616],
        //           [119.74559,29.07616],
        //           [119.745597,29.076179],
        //           [119.745613,29.07616],
        //       ],
        top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(objData))

        // axios({
        //   method: 'post',
        //   url: baseURL.url + '/dtdd/iot/aep/v1/gather/get-value',
        //   data: {
        //     device_data_type:
        //       item.item.gather_value_list[0].device_data_type,
        //     did: item.item.did,
        //   },
        // }).then(function (res) {
        //   console.log(res)
        // })
        // $api('/iot/aep/v1/gather/list', {
        //   device_data_type: 'COVER_TRAFFIC_GPS_BUS',
        //   did: item.item.did,
        // }).then((res) => {
        //   console.log(res)
        // })
      } else if (item.item.type_name === '危货车GPS') {
        const res0 = await axios({
          method: 'post',
          url: baseURL.url + '/dtdd/iot/aep/v1/gather/get-value',
          data: {
            device_data_type: item.item.gather_value_list[0].device_data_type,
            did: item.item.did,
          },
        })
        console.log(res0)

        let obj = res0.data.list[0]
        let area = item.item.area_name
        let name = item.item.device_name
        let type = item.item.type_name
        let danwei = item.item.dept_name
        let sd = obj.velocity
        let fx = obj.direction
        let state = obj.state
        let totalmileage = obj.totalmileage
        coor.push(0)

        console.log(coor)

        let gc = obj.altitude
        let time = item.item.create_time

        // axios({
        //   method: 'get',
        //   url: baseURL.url230 + '/iot/aep/v1/gather/list',
        //   data: {
        //     device_data_type: 'COVER_TRAFFIC_GPS_BUS',
        //     did: '5799358',
        //     page_size: 200,
        //   },
        // }).then(function (res) {
        //   console.log(res)
        // })
        if (top.document.getElementById('map').contentWindow.map.TDT_TITLE_ID) {
          top.document.getElementById('map').contentWindow.map.TDT_TITLE_ID.remove()
        }
        // inestance.remove()

        let pageSize = item.item.type_name === '公交车GPS' || item.item.type_name === '出租车GPS' ? 17 : 100
        axios({
          method: 'post',
          url: baseURL.url + '/dtdd/iot/aep/v1/gather/list',
          data: {
            device_data_type: item.item.gather_value_list[0].device_data_type,
            did: item.item.did,
            page_size: pageSize,
          },
        }).then(function (res) {
          const imgIcon = `${baseURL.url}/static/citybrain/tckz/img/tckz_gj/${type}.png`
          console.log(res)
          let jwdArr = res.data.data.rows.map((item) => {
            return [item.CUIOT_CARRIER_LONGITUDE - 0, item.CUIOT_CARRIER_LATITUDE - 0]
          })

          let jwdS = JSON.stringify(jwdArr)

          let objData = {
            funcName: 'customPop',
            coordinates: coor,

            // coordinates: ['119.607129', '29.068155'],
            closeButton: true,
            html: ` <div
                  class="pop"
                 style="
                  width: 1000px;
                  position: absolute;
                  border-radius: 5px;
                  background-color: rgba(10, 31, 53, 0.8);
                  z-index: 999999;
                  -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
                  box-shadow: inset 0 0 40px 0 #5ba3fa;
                  padding: 24px;
                "
              >
              <div
                 onclick=" this.parentNode.style.display = 'none'
                 if (
                  window.map.TDT_TITLE_ID
                    ) {
                      window.map.TDT_TITLE_ID.remove()
                    }
                 "

                  style="
                    position: absolute;
                    right: 30px;
                    top: 30px;
                    font-size: 40px;
                    color: #fff;
                    cursor:pointer
                  "
                >
                  x
                </div>
                <div class="container">

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">名&nbsp; &nbsp;   &nbsp;称 ：</span>
                    <span style="color: #eccc83; width: 70%">${name}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备状态：</span>
                    <span style="color: #eccc83; width: 70%">${state}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备类型：</span>
                    <span style="color: #eccc83; width: 70%">${type}</span>
                  </div>

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">承载单位：</span>
                    <span style="color: #eccc83; width: 70%">${danwei}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">速&nbsp; &nbsp;   &nbsp;度 ：</span>
                    <span style="color: #eccc83; width: 70%">${sd}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">方&nbsp; &nbsp;   &nbsp;向 ：</span>
                    <span style="color: #eccc83; width: 70%">${fx}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">高&nbsp; &nbsp;   &nbsp;程 ：</span>
                    <span style="color: #eccc83; width: 70%">${gc}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">总公里数：</span>
                    <span style="color: #eccc83; width: 70%">${totalmileage}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px;align-items: center;"
                  >
                    <span style="width: 30%; text-align: center">历史轨迹：</span>
                    <button

                      style="
                        font-size: 28px;
                        color: #fff;
                        background-color:#b98262;
                        border: unset;
                        width: 171px;
                        height: 60%;
                        height: 59px;
                        line-height: 59px;
                      "
                      onclick="top.document.getElementById('map').contentWindow.Work.funChange(
                          JSON.stringify({
                            funcName: 'createTrajectory',
                            data: {
                              id:'TDT_TITLE_ID',
                              type:'dynamic',
                               icon:'${imgIcon}',
                               coordinates: ${jwdS},
                                  iconStyle: {
                                 'icon-size':2,
                                 'icon-rotate': 360,
                             },
                             style: {
                                 'line-width': 10,
                             },
                             isGlowLine: false,
                             isBezierCurve: false,
                             color: ['#85d4b1'],
                             loop:false,
                             steps: 100
                            }
                                       })
                                     )

                                     "
                    >
                      点击查看
                    </button>
                  </div>
                </div>
              </div>`,
          }
          // [
          //           [119.745613,29.07616],
          //           [119.74559,29.076169],
          //           [119.74559,29.076169],
          //           [119.745582,29.07615],
          //           [119.74559,29.076139],
          //           [119.745597,29.07615],
          //           [119.745597,29.07616],
          //           [119.74559,29.07616],
          //           [119.745597,29.076179],
          //           [119.745613,29.07616],
          //       ],
          top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(objData))
        })
        // axios({
        //   method: 'post',
        //   url: baseURL.url + '/dtdd/iot/aep/v1/gather/get-value',
        //   data: {
        //     device_data_type:
        //       item.item.gather_value_list[0].device_data_type,
        //     did: item.item.did,
        //   },
        // }).then(function (res) {
        //   console.log(res)
        // })
        // $api('/iot/aep/v1/gather/list', {
        //   device_data_type: 'COVER_TRAFFIC_GPS_BUS',
        //   did: item.item.did,
        // }).then((res) => {
        //   console.log(res)
        // })
      } else if (item.item.type_name === '渣土车GPS') {
        const res0 = await axios({
          method: 'post',
          url: baseURL.url + '/dtdd/iot/aep/v1/gather/get-value',
          data: {
            device_data_type: item.item.gather_value_list[0].device_data_type,
            did: item.item.did,
          },
        })
        console.log(res0)

        let obj = res0.data.list[0]
        let timeStr = obj.cuiot_push_time ? obj.cuiot_push_time : '--'

        let yy = timeStr.slice(0, 4)
        let MM = timeStr.slice(4, 6)
        let dd = timeStr.slice(6, 8)
        let hh = timeStr.slice(8, 10)
        let mm = timeStr.slice(10, 12)
        let ss = timeStr.slice(12)
        let time = yy + '/' + MM + '/' + dd + ' ' + hh + ':' + mm + ':' + ss

        let area = item.item.area_name
        let name = item.item.carrier_name
        let type = item.item.type_name
        let danwei = item.item.dept_name
        let sd = obj.gpsspeed
        let dz = obj.location
        let state = obj.device_state === 'MISSING' ? '已离线' : '在线'
        let objData = {
          funcName: 'customPop',
          coordinates: coor,

          // coordinates: ['119.607129', '29.068155'],
          closeButton: true,
          html: ` <div
                            onclick=" this.style.display = 'none'"
                style="
                  width: 1000px;
                  position: absolute;
                  border-radius: 5px;
                  background-color: rgba(10, 31, 53, 0.8);
                  z-index: 999999;
                  -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
                  box-shadow: inset 0 0 40px 0 #5ba3fa;
                  padding: 24px;
                "
              >
                <div class="container">
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">所属区域 ：</span>
                    <span style="color: #eccc83; width: 70%">${area}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">名&nbsp; &nbsp;   &nbsp;称 ：</span>
                    <span style="color: #eccc83; width: 70%">${name}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备状态：</span>
                    <span style="color: #eccc83; width: 70%">${state}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备类型：</span>
                    <span style="color: #eccc83; width: 70%">${type}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">承载单位：</span>
                    <span style="color: #eccc83; width: 70%">${danwei}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">速&nbsp; &nbsp;   &nbsp;度 ：</span>
                    <span style="color: #eccc83; width: 70%">${sd}</span>
                  </div>

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">地&nbsp; &nbsp;   &nbsp;址 ：</span>
                    <span style="color: #eccc83; width: 70%">${dz}</span>
                  </div>
                    <div
                      class="item"
                      style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                    >
                      <span style="width: 30%; text-align: center">采集时间：</span>
                      <span style="color: #eccc83; width: 70%">${time}</span>
                    </div>
                </div>
              </div>`,
        }

        top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(objData))
      } else if (item.item.type_name === '公交车GPS') {
        const res0 = await axios({
          method: 'post',
          url: baseURL.url + '/dtdd/iot/aep/v1/gather/get-value',
          data: {
            device_data_type: item.item.gather_value_list[0].device_data_type,
            did: item.item.did,
          },
        })
        console.log(res0)

        let obj = res0.data.list[0]
        let area = item.item.area_name
        let name = item.item.device_name
        let type = item.item.type_name
        let danwei = item.item.dept_name
        let sd = obj.gpsspeed
        let fx = obj.rotationangle
        let state = obj.device_state === 'MISSING' ? '已离线' : '在线'
        let totalmileage = obj.totalmileage
        coor.push(0)

        console.log(coor)

        let gc = obj.gpsmile
        let time = item.item.create_time

        // axios({
        //   method: 'get',
        //   url: baseURL.url230 + '/iot/aep/v1/gather/list',
        //   data: {
        //     device_data_type: 'COVER_TRAFFIC_GPS_BUS',
        //     did: '5799358',
        //     page_size: 200,
        //   },
        // }).then(function (res) {
        //   console.log(res)
        // })
        if (top.document.getElementById('map').contentWindow.map.TDT_TITLE_ID) {
          top.document.getElementById('map').contentWindow.map.TDT_TITLE_ID.remove()
        }
        // inestance.remove()

        let pageSize = item.item.type_name === '公交车GPS' || item.item.type_name === '出租车GPS' ? 17 : 100
        axios({
          method: 'post',
          url: baseURL.url + '/dtdd/iot/aep/v1/gather/list',
          data: {
            device_data_type: item.item.gather_value_list[0].device_data_type,
            did: item.item.did,
            page_size: pageSize,
          },
        }).then(function (res) {
          const imgIcon = `${baseURL.url}/static/citybrain/tckz/img/tckz_gj/${type}.png`
          console.log(res)
          let jwdArr = res.data.data.rows.map((item) => {
            return [item.CUIOT_CARRIER_LONGITUDE - 0, item.CUIOT_CARRIER_LATITUDE - 0]
          })

          let jwdS = JSON.stringify(jwdArr)

          let objData = {
            funcName: 'customPop',
            coordinates: coor,

            // coordinates: ['119.607129', '29.068155'],
            closeButton: true,
            html: ` <div
                  class="pop"
                 style="
                  width: 1000px;
                  position: absolute;
                  border-radius: 5px;
                  background-color: rgba(10, 31, 53, 0.8);
                  z-index: 999999;
                  -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
                  box-shadow: inset 0 0 40px 0 #5ba3fa;
                  padding: 24px;
                "
              >
              <div
                 onclick=" this.parentNode.style.display = 'none'
                 if (
                  window.map.TDT_TITLE_ID
                    ) {
                      window.map.TDT_TITLE_ID.remove()
                    }
                 "

                  style="
                    position: absolute;
                    right: 30px;
                    top: 30px;
                    font-size: 40px;
                    color: #fff;
                    cursor:pointer
                  "
                >
                  x
                </div>
                <div class="container">

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">名&nbsp; &nbsp;   &nbsp;称 ：</span>
                    <span style="color: #eccc83; width: 70%">${name}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备状态：</span>
                    <span style="color: #eccc83; width: 70%">${state}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备类型：</span>
                    <span style="color: #eccc83; width: 70%">${type}</span>
                  </div>

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">承载单位：</span>
                    <span style="color: #eccc83; width: 70%">${danwei}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">速&nbsp; &nbsp;   &nbsp;度 ：</span>
                    <span style="color: #eccc83; width: 70%">${sd}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">方&nbsp; &nbsp;   &nbsp;向 ：</span>
                    <span style="color: #eccc83; width: 70%">${fx}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">里程数：</span>
                    <span style="color: #eccc83; width: 70%">${gc}</span>
                  </div>

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px;align-items: center;"
                  >
                    <span style="width: 30%; text-align: center">历史轨迹：</span>
                    <button

                      style="
                        font-size: 28px;
                        color: #fff;
                        background-color:#b98262;
                        border: unset;
                        width: 171px;
                        height: 60%;
                        height: 59px;
                        line-height: 59px;
                      "
                      onclick="top.document.getElementById('map').contentWindow.Work.funChange(
                          JSON.stringify({
                            funcName: 'createTrajectory',
                            data: {
                              id:'TDT_TITLE_ID',
                              type:'dynamic',
                               icon:'${imgIcon}',
                               coordinates: ${jwdS},
                                  iconStyle: {
                                 'icon-size':2,
                                 'icon-rotate': 360,
                             },
                             style: {
                                 'line-width': 10,
                             },
                             isGlowLine: false,
                             isBezierCurve: false,
                             color: ['#85d4b1'],
                             loop:false,
                             steps: 100
                            }
                                       })
                                     )

                                     "
                    >
                      点击查看
                    </button>
                  </div>
                </div>
              </div>`,
          }
          // [
          //           [119.745613,29.07616],
          //           [119.74559,29.076169],
          //           [119.74559,29.076169],
          //           [119.745582,29.07615],
          //           [119.74559,29.076139],
          //           [119.745597,29.07615],
          //           [119.745597,29.07616],
          //           [119.74559,29.07616],
          //           [119.745597,29.076179],
          //           [119.745613,29.07616],
          //       ],
          top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(objData))
        })
        // axios({
        //   method: 'post',
        //   url: baseURL.url + '/dtdd/iot/aep/v1/gather/get-value',
        //   data: {
        //     device_data_type:
        //       item.item.gather_value_list[0].device_data_type,
        //     did: item.item.did,
        //   },
        // }).then(function (res) {
        //   console.log(res)
        // })
        // $api('/iot/aep/v1/gather/list', {
        //   device_data_type: 'COVER_TRAFFIC_GPS_BUS',
        //   did: item.item.did,
        // }).then((res) => {
        //   console.log(res)
        // })
      } else if (item.item.type_name === '出租车GPS') {
        const res0 = await axios({
          method: 'post',
          url: baseURL.url + '/dtdd/iot/aep/v1/gather/get-value',
          data: {
            device_data_type: item.item.gather_value_list[0].device_data_type,
            did: item.item.did,
          },
        })
        console.log(res0)

        let obj = res0.data.list[0]
        let area = item.item.area_name
        let name = item.item.device_name
        let type = item.item.type_name
        let danwei = item.item.dept_name
        let sd = obj.speed
        let fx = obj.direction
        let tel = obj.tel
        let state = obj.device_state === 'MISSING' ? '已离线' : '在线'
        let totalmileage = obj.totalmileage
        coor.push(0)

        console.log(coor)

        let gc = obj.gpsmile
        let time = item.item.create_time

        // axios({
        //   method: 'get',
        //   url: baseURL.url230 + '/iot/aep/v1/gather/list',
        //   data: {
        //     device_data_type: 'COVER_TRAFFIC_GPS_BUS',
        //     did: '5799358',
        //     page_size: 200,
        //   },
        // }).then(function (res) {
        //   console.log(res)
        // })
        if (top.document.getElementById('map').contentWindow.map.TDT_TITLE_ID) {
          top.document.getElementById('map').contentWindow.map.TDT_TITLE_ID.remove()
        }
        // inestance.remove()

        let pageSize = item.item.type_name === '公交车GPS' || item.item.type_name === '出租车GPS' ? 17 : 100
        axios({
          method: 'post',
          url: baseURL.url + '/dtdd/iot/aep/v1/gather/list',
          data: {
            device_data_type: item.item.gather_value_list[0].device_data_type,
            did: item.item.did,
            page_size: pageSize,
          },
        }).then(function (res) {
          const imgIcon = `${baseURL.url}/static/citybrain/tckz/img/tckz_gj/${type}.png`
          console.log(res)
          let jwdArr = res.data.data.rows.map((item) => {
            return [item.CUIOT_CARRIER_LONGITUDE - 0, item.CUIOT_CARRIER_LATITUDE - 0]
          })

          let jwdS = JSON.stringify(jwdArr)

          let objData = {
            funcName: 'customPop',
            coordinates: coor,

            // coordinates: ['119.607129', '29.068155'],
            closeButton: true,
            html: ` <div
                  class="pop"
                 style="
                  width: 1000px;
                  position: absolute;
                  border-radius: 5px;
                  background-color: rgba(10, 31, 53, 0.8);
                  z-index: 999999;
                  -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
                  box-shadow: inset 0 0 40px 0 #5ba3fa;
                  padding: 24px;
                "
              >
              <div
                 onclick=" this.parentNode.style.display = 'none'
                 if (
                  window.map.TDT_TITLE_ID
                    ) {
                      window.map.TDT_TITLE_ID.remove()
                    }
                 "

                  style="
                    position: absolute;
                    right: 30px;
                    top: 30px;
                    font-size: 40px;
                    color: #fff;
                    cursor:pointer
                  "
                >
                  x
                </div>
                <div class="container">

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">车&nbsp; &nbsp;   &nbsp;牌 ：</span>
                    <span style="color: #eccc83; width: 70%">${name}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备状态：</span>
                    <span style="color: #eccc83; width: 70%">${state}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备类型：</span>
                    <span style="color: #eccc83; width: 70%">${type}</span>
                  </div>

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">承载单位：</span>
                    <span style="color: #eccc83; width: 70%">${danwei}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">速&nbsp; &nbsp;   &nbsp;度 ：</span>
                    <span style="color: #eccc83; width: 70%">${sd}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">方&nbsp; &nbsp;   &nbsp;向 ：</span>
                    <span style="color: #eccc83; width: 70%">${fx}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">电&nbsp; &nbsp;   &nbsp;话 ：</span>
                    <span style="color: #eccc83; width: 70%">${tel}</span>
                  </div>

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px;align-items: center;"
                  >
                    <span style="width: 30%; text-align: center">历史轨迹：</span>
                    <button

                      style="
                        font-size: 28px;
                        color: #fff;
                        background-color:#b98262;
                        border: unset;
                        width: 171px;
                        height: 60%;
                        height: 59px;
                        line-height: 59px;
                      "
                      onclick="top.document.getElementById('map').contentWindow.Work.funChange(
                          JSON.stringify({
                            funcName: 'createTrajectory',
                            data: {
                              id:'TDT_TITLE_ID',
                              type:'dynamic',
                               icon:'${imgIcon}',
                               coordinates: ${jwdS},
                                  iconStyle: {
                                 'icon-size':2,
                                 'icon-rotate': 360,
                             },
                             style: {
                                 'line-width': 10,
                             },
                             isGlowLine: false,
                             isBezierCurve: false,
                             color: ['#85d4b1'],
                             loop:false,
                             steps: 100
                            }
                                       })
                                     )

                                     "
                    >
                      点击查看
                    </button>
                  </div>
                </div>
              </div>`,
          }
          // [
          //           [119.745613,29.07616],
          //           [119.74559,29.076169],
          //           [119.74559,29.076169],
          //           [119.745582,29.07615],
          //           [119.74559,29.076139],
          //           [119.745597,29.07615],
          //           [119.745597,29.07616],
          //           [119.74559,29.07616],
          //           [119.745597,29.076179],
          //           [119.745613,29.07616],
          //       ],
          top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(objData))
        })
        // axios({
        //   method: 'post',
        //   url: baseURL.url + '/dtdd/iot/aep/v1/gather/get-value',
        //   data: {
        //     device_data_type:
        //       item.item.gather_value_list[0].device_data_type,
        //     did: item.item.did,
        //   },
        // }).then(function (res) {
        //   console.log(res)
        // })
        // $api('/iot/aep/v1/gather/list', {
        //   device_data_type: 'COVER_TRAFFIC_GPS_BUS',
        //   did: item.item.did,
        // }).then((res) => {
        //   console.log(res)
        // })
      } else if (item.item.type_name === '浦江大数据局-交通运营') {
        const res0 = await axios({
          method: 'post',
          url: baseURL.url + '/dtdd/iot/aep/v1/gather/get-value',
          data: {
            device_data_type: item.item.gather_value_list[0].device_data_type,
            did: item.item.did,
          },
        })
        console.log(res0)

        let obj = res0.data.list[0]
        let area = item.item.area_name
        let name = item.item.device_name
        let type = item.item.type_name
        let danwei = item.item.dept_name
        let sd = obj.speed
        let fx = obj.direction
        let tel = obj.tel
        let state = obj.device_state === 'MISSING' ? '已离线' : '在线'
        let totalmileage = obj.totalmileage
        coor.push(0)

        console.log(coor)

        let gc = obj.altitude
        let time = item.item.create_time

        // axios({
        //   method: 'get',
        //   url: baseURL.url230 + '/iot/aep/v1/gather/list',
        //   data: {
        //     device_data_type: 'COVER_TRAFFIC_GPS_BUS',
        //     did: '5799358',
        //     page_size: 200,
        //   },
        // }).then(function (res) {
        //   console.log(res)
        // })
        if (top.document.getElementById('map').contentWindow.map.TDT_TITLE_ID) {
          top.document.getElementById('map').contentWindow.map.TDT_TITLE_ID.remove()
        }
        // inestance.remove()

        let pageSize = item.item.type_name === '公交车GPS' || item.item.type_name === '出租车GPS' ? 17 : 200
        axios({
          method: 'post',
          url: baseURL.url + '/dtdd/iot/aep/v1/gather/list',
          data: {
            device_data_type: item.item.gather_value_list[0].device_data_type,
            did: item.item.did,
            page_size: pageSize,
          },
        }).then(function (res) {
          const imgIcon = `${baseURL.url}/static/citybrain/tckz/img/tckz_gj/${type}.png`
          console.log(res)
          let jwdArr = res.data.data.rows.map((item) => {
            return [item.CUIOT_CARRIER_LONGITUDE - 0, item.CUIOT_CARRIER_LATITUDE - 0]
          })

          let jwdS = JSON.stringify(jwdArr)

          let objData = {
            funcName: 'customPop',
            coordinates: coor,

            // coordinates: ['119.607129', '29.068155'],
            closeButton: true,
            html: ` <div
                  class="pop"
                 style="
                  width: 1000px;
                  position: absolute;
                  border-radius: 5px;
                  background-color: rgba(10, 31, 53, 0.8);
                  z-index: 999999;
                  -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
                  box-shadow: inset 0 0 40px 0 #5ba3fa;
                  padding: 24px;
                "
              >
              <div
                 onclick=" this.parentNode.style.display = 'none'
                 if (
                  window.map.TDT_TITLE_ID
                    ) {
                      window.map.TDT_TITLE_ID.remove()
                    }
                 "

                  style="
                    position: absolute;
                    right: 30px;
                    top: 30px;
                    font-size: 40px;
                    color: #fff;
                    cursor:pointer
                  "
                >
                  x
                </div>
                <div class="container">

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">车&nbsp; &nbsp;   &nbsp;牌 ：</span>
                    <span style="color: #eccc83; width: 70%">${name}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备状态：</span>
                    <span style="color: #eccc83; width: 70%">${state}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">设备类型：</span>
                    <span style="color: #eccc83; width: 70%">${type}</span>
                  </div>

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">承载单位：</span>
                    <span style="color: #eccc83; width: 70%">${danwei}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">速&nbsp; &nbsp;   &nbsp;度 ：</span>
                    <span style="color: #eccc83; width: 70%">${sd}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">高&nbsp; &nbsp;   &nbsp;程 ：</span>
                    <span style="color: #eccc83; width: 70%">${gc}</span>
                  </div>

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px;align-items: center;"
                  >
                    <span style="width: 30%; text-align: center">历史轨迹：</span>
                    <button

                      style="
                        font-size: 28px;
                        color: #fff;
                        background-color:#b98262;
                        border: unset;
                        width: 171px;
                        height: 60%;
                        height: 59px;
                        line-height: 59px;
                      "
                      onclick="top.document.getElementById('map').contentWindow.Work.funChange(
                          JSON.stringify({
                            funcName: 'createTrajectory',
                            data: {
                              id:'TDT_TITLE_ID',
                              type:'dynamic',
                               icon:'${imgIcon}',
                               coordinates: ${jwdS},
                                  iconStyle: {
                                 'icon-size':2,
                                 'icon-rotate': 360,
                             },
                             style: {
                                 'line-width': 10,
                             },
                             isGlowLine: false,
                             isBezierCurve: false,
                             color: ['#85d4b1'],
                             loop:false,
                             steps: 100
                            }
                                       })
                                     )

                                     "
                    >
                      点击查看
                    </button>
                  </div>
                </div>
              </div>`,
          }
          // [
          //           [119.745613,29.07616],
          //           [119.74559,29.076169],
          //           [119.74559,29.076169],
          //           [119.745582,29.07615],
          //           [119.74559,29.076139],
          //           [119.745597,29.07615],
          //           [119.745597,29.07616],
          //           [119.74559,29.07616],
          //           [119.745597,29.076179],
          //           [119.745613,29.07616],
          //       ],
          top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(objData))
        })
        // axios({
        //   method: 'post',
        //   url: baseURL.url + '/dtdd/iot/aep/v1/gather/get-value',
        //   data: {
        //     device_data_type:
        //       item.item.gather_value_list[0].device_data_type,
        //     did: item.item.did,
        //   },
        // }).then(function (res) {
        //   console.log(res)
        // })
        // $api('/iot/aep/v1/gather/list', {
        //   device_data_type: 'COVER_TRAFFIC_GPS_BUS',
        //   did: item.item.did,
        // }).then((res) => {
        //   console.log(res)
        // })
      } else {
        // debugger
        let name = item.item.device_name
        let type = item.item.type_name
        let mc = item.item.type_name.indexOf('车') === -1 ? '名称' : '车牌号'

        let objData = {
          funcName: 'customPop',
          coordinates: coor,

          // coordinates: ['119.607129', '29.068155'],
          closeButton: true,
          html: `<div
                  class="contain"
                  onclick=" this.style.display = 'none'"
                  style="
                    height: 200px;
                    position: absolute;
                    width: max-content;
                    display:inline-block;
                    background-color: rgba(0, 0, 0, 0.8);
                    border: 2px solid #00aae2;
                    box-sizing: border-box;
                    border-style: solid;
                    border-width: 4px;
                    border-image-source: linear-gradient(0deg, #32abe4 0%, #0b5aa4 100%);
                    border-image-slice: 1;
                  "
                >
                  <div
                    class="title"
                    style="
                      background: linear-gradient(360deg, #096c8d, #073446);
                      width: 100%;
                      height: 60px;
                      font-size: 32px;
                      color: #fff;
                      padding:0 30px;
                      box-sizing: border-box;
                      line-height: 60px;
                    "
                  >
                   ${type}
                  </div>
                  <div

                    class="content"
                    style="
                      display: flex;
                      align-items: center;
                      font-size: 28px;
                      color: #fff;
                      padding: 20px;
                    "
                  >

                    <span style="display:inline-block;line-hight:30px;align-self: baseline;
              line-height: 55px;
          flex-shrink: 0"> ${mc}：${name}</span>
                  </div>
                </div>`,
        }
        console.log(top.document.getElementById('map'))
        top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(objData))
      }
    }
  }
})
console.log(navigator.userAgent)
console.log('navigator.userAgent')
