

function loadSceneLayer() {
    const layerConfigs = [
      {
        type: "scene",
        url: "https://*************:6443/geoscene/rest/services/Hosted/obj_all/SceneServer",
      },
      {
        type: "integrated-mesh",
        url: "https://*************:6443/geoscene/rest/services/Hosted/SZF4490o/SceneServer",
      },
      {
        type: "integrated-mesh",
        url: "https://*************:6443/geoscene/rest/services/Hosted/JB4490o/SceneServer",
      },
      {
        type: "integrated-mesh",
        url: "https://*************:6443/geoscene/rest/services/Hosted/JN4490o/SceneServer",
      },
    ];
    const layers = [];
    for (let i = 0, len = layerConfigs.length; i < len; i++) {
      const layerConfig = layerConfigs[i];
      const { url } = layerConfig;
      let itemLayer;
      if (layerConfig.type === "scene") {
        itemLayer = new SceneLayer({
          url,
        });
      } else if (layerConfig.type === "integrated-mesh") {
        itemLayer = new IntegratedMeshLayer({
          url,
        });
      }
      layers.push(itemLayer);
    }
    view.map.addMany(layers);
    // view.whenLayerView(layers[0]).then((layerView) => {
    //   view.goTo(layers[0].fullExtent);
    // });
  }

  export default loadSceneLayer;