<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../css/home.css">
    <script src="../js/axios.min.js"></script>
    <script src="../js/ZLMRTCClient.js"></script>
    <script src="../js/webrtc.js"></script>
    <script src="../js/ip.js"></script>
    <script src="../js/http.js"></script>
    <title>home</title>
</head>

<body>
    <div class="container">
        <header>
            <h1 class="player">播放器</h1>
            <button value="Logout" class="logout" onclick="logoutClick()">
                退出登录
            </button>
        </header>
        <!-- 上 -->

        <div class="content">
            <aside>
                <div id="tree-container"></div>
            </aside>
            <!-- 左 -->
            <main>
                <div id="chrmoe_video" class="chrmoe_video" style="height: 100%">
                    <div class="video_head">
                        <!-- <div id="logoText" class="logo">实时图传</div> -->
                        <div id="logoText" class="logo"></div>
                        <div class="screen">
                            <ul>
                                <li class="screen_li screen_close" onclick="closeChrome()"></li>
                                <li class="screen_li screen_max" onclick="max()"></li>
                                <li class="screen_li screen_min" onclick="min()"></li>
                                <li class="screen_li_xian screen_xian"></li>
                                <li id="screen16" class="screen_li screen16" onclick="choice1(16)"></li>
                                <li id="screen9" class="screen_li screen9" onclick="choice1(9)"></li>
                                <li id="screen6" class="screen_li screen6" onclick="choice1(6)"></li>
                                <li id="screen4" class="screen_li screen4" onclick="choice1(4)"></li>
                                <li id="screen1" class="screen_li screen1" onclick="choice1(1)"></li>
                            </ul>
                        </div>
                    </div>
                    <div style="height: 95%; width:100%">
                        <div class="main_player">
                            <div id="boxss" class="type" ref="boxs">
                                <div id="info0" class="chromeBox16 data0" style="border: 1px solid rgb(238, 238, 238)" ondrop="drop(event)" draggable="true" ondragstart="drag(event)" ondragover="allowDrop(event)" onclick="chooseBox(event)" onmousedown="mousedown(event)">
                                    <div id="null0"></div>
                                </div>
                                <div id="info1" class="chromeBox16 data1" style="border: 1px solid rgb(238, 238, 238)" ondrop="drop(event)" draggable="true" ondragstart="drag(event)" ondragover="allowDrop(event)" onclick="chooseBox(event)" onmousedown="mousedown(event)">
                                    <div id="null1"></div>
                                </div>
                                <div id="info2" class="chromeBox16 data2" style="border: 1px solid rgb(238, 238, 238)" ondrop="drop(event)" draggable="true" ondragstart="drag(event)" ondragover="allowDrop(event)" onclick="chooseBox(event)" onmousedown="mousedown(event)">
                                    <div id="null2"></div>
                                </div>
                                <div id="info3" class="chromeBox16 data3" style="border: 1px solid rgb(238, 238, 238)" ondrop="drop(event)" draggable="true" ondragstart="drag(event)" ondragover="allowDrop(event)" onclick="chooseBox(event)" onmousedown="mousedown(event)">
                                    <div id="null3"></div>
                                </div>
                                <div id="info4" class="chromeBox16 data4" style="border: 1px solid rgb(238, 238, 238)" ondrop="drop(event)" draggable="true" ondragstart="drag(event)" ondragover="allowDrop(event)" onclick="chooseBox(event)" onmousedown="mousedown(event)">
                                    <div id="null4"></div>
                                </div>
                                <div id="info5" class="chromeBox16 data5" style="border: 1px solid rgb(238, 238, 238)" ondrop="drop(event)" draggable="true" ondragstart="drag(event)" ondragover="allowDrop(event)" onclick="chooseBox(event)" onmousedown="mousedown(event)">
                                    <div id="null5"></div>
                                </div>
                                <div id="info6" class="chromeBox16 data6" style="border: 1px solid rgb(238, 238, 238)" ondrop="drop(event)" draggable="true" ondragstart="drag(event)" ondragover="allowDrop(event)" onclick="chooseBox(event)" onmousedown="mousedown(event)">
                                    <div id="null6"></div>
                                </div>
                                <div id="info7" class="chromeBox16 data7" style="border: 1px solid rgb(238, 238, 238)" ondrop="drop(event)" draggable="true" ondragstart="drag(event)" ondragover="allowDrop(event)" onclick="chooseBox(event)" onmousedown="mousedown(event)">
                                    <div id="null7"></div>
                                </div>
                                <div id="info8" class="chromeBox16 data8" style="border: 1px solid rgb(238, 238, 238)" ondrop="drop(event)" draggable="true" ondragstart="drag(event)" ondragover="allowDrop(event)" onclick="chooseBox(event)" onmousedown="mousedown(event)">
                                    <div id="null8"></div>
                                </div>
                                <div id="info9" class="chromeBox16 data9" style="border: 1px solid rgb(238, 238, 238)" ondrop="drop(event)" draggable="true" ondragstart="drag(event)" ondragover="allowDrop(event)" onclick="chooseBox(event)" onmousedown="mousedown(event)">
                                    <div id="null9"></div>
                                </div>
                                <div id="info10" class="chromeBox16 data10" style="border: 1px solid rgb(238, 238, 238)" ondrop="drop(event)" draggable="true" ondragstart="drag(event)" ondragover="allowDrop(event)" onclick="chooseBox(event)" onmousedown="mousedown(event)">
                                    <div id="null10"></div>
                                </div>
                                <div id="info11" class="chromeBox16 data11" style="border: 1px solid rgb(238, 238, 238)" ondrop="drop(event)" draggable="true" ondragstart="drag(event)" ondragover="allowDrop(event)" onclick="chooseBox(event)" onmousedown="mousedown(event)">
                                    <div id="null11"></div>
                                </div>
                                <div id="info12" class="chromeBox16 data12" style="border: 1px solid rgb(238, 238, 238)" ondrop="drop(event)" draggable="true" ondragstart="drag(event)" ondragover="allowDrop(event)" onclick="chooseBox(event)" onmousedown="mousedown(event)">
                                    <div id="null12"></div>
                                </div>
                                <div id="info13" class="chromeBox16 data13" style="border: 1px solid rgb(238, 238, 238)" ondrop="drop(event)" draggable="true" ondragstart="drag(event)" ondragover="allowDrop(event)" onclick="chooseBox(event)" onmousedown="mousedown(event)">
                                    <div id="null13"></div>
                                </div>
                                <div id="info14" class="chromeBox16 data14" style="border: 1px solid rgb(238, 238, 238)" ondrop="drop(event)" draggable="true" ondragstart="drag(event)" ondragover="allowDrop(event)" onclick="chooseBox(event)" onmousedown="mousedown(event)">
                                    <div id="null14"></div>
                                </div>
                                <div id="info15" class="chromeBox16 data15" style="border: 1px solid rgb(238, 238, 238)" ondrop="drop(event)" draggable="true" ondragstart="drag(event)" ondragover="allowDrop(event)" onclick="chooseBox(event)" onmousedown="mousedown(event)">
                                    <div id="null15"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
            <!-- 右 -->
        </div>
    </div>
    <script type="text/javascript">
        window.onload = function() {

            var isLogin = window.localStorage.getItem("isLogin");
            if (!isLogin) {
                window.localStorage.setItem("isLogin", 0);
                window.location.href = "Login.html";
            }
        }

        var ws, treeData, screens, timerId, srcDiv, srcDiv, f_id, signBox, signBox_son, hideTimeout;
        var wsMessage;
        var isStartAudio = 0; //开启对讲的数量
        var pcStartAudio = null; //平台声音推流的webRTC实例

        var isStartVideo = 0; //开启平台画面的数量
        var pcStartVideo = null; //平台画面(摄像头)推流的webRTC实例
        var pcStartVideoUrl = null; //平台画面(摄像头)推流的地址
        var pcStartVideoWebRtcUrl = null; //平台画面(摄像头)推流的地址

        var isStartAudioNum = 0; //国标对讲数量
        var pcStartAudioGB = null;
        var wsAudioMessage; //国标对讲信息
        var pusVideo = null; //国标视频推流推送媒体网关WebSocket信息

        var dataExample = []; //存放窗口信息

        var isGBTwoInfo = []; //存放国标双人执法的信息

        // 国标双人执法指挥台信息
        var push_device_type = null;
        var assist_enforcer_hostbody = null;

        var saveMuteData = null;

        var isGB = Number(window.localStorage.getItem('isGB'));
        // 页面加载完成后调用初始化方法
        heartbeat().then(() => {
            createWs()
        });
        choice1(4);

        if (isGB) {
            assistEnforcerInfo().then((res) => {
                console.log(res);
                push_device_type = res.data.push_device_type;
                assist_enforcer_hostbody = res.data.hostbody;
            })
            treeInfo_gb()
        } else {
            treeInfo();
        }

        function treeInfo() {
            getTreeInfo('').then((response) => {
                console.log(response);
                var res = response.data;
                if (res.code == 200) {
                    res.data[0].sub.forEach((item) => {
                        if (item.treecate == '1') {
                            item.children = [];
                            item.lazy = true;
                            item.label = item.unitname;
                        } else {
                            item.label = item.hostname;
                        }
                    })
                    console.log(res.data[0].sub);
                    treeData = [{
                            label: res.data[0].unitname,
                            // lazy: true,
                            treecate: '1',
                            children: [...res.data[0].sub],
                        }]
                        // 调用渲染函数
                    renderTree(treeData, 'tree-container');
                }
            }).catch((err) => {
                console.log(err);
            })
        }

        function treeInfo_gb() {
            getTreeInfo_gb().then((response) => {
                console.log(response);
                var res = response.data;
                if (res.code == 200) {
                    treeData = [{
                            label: res.data.left_data[0].unitname,
                            id: res.data.left_data[0].unit,
                            lazy: true,
                            treecate: '1',
                            children: [],
                        }]
                        // 调用渲染函数
                    renderTree(treeData, 'tree-container');
                }
            }).catch((err) => {
                console.log(err);
            })
        }

        // 创建树节点的 HTML
        function createTreeNodeHTML(node) {
            console.log(node);
            let html = `<li ${node.lazy ? `lazy=${node.lazy} ` : 'class="noIcon"'}>`;
            // let html = '<li>';
            if(node.treecate === '1'){
                html += `<span data-node=${JSON.stringify(node)} style="cursor: pointer"><i class='icon unit'></i></i>${node.label}</span>`;
            }else{
                if(node.recorder_type == 2){
                    html += `<i class='icon ${node.lineon == "1" ? 'c2-online' : 'c2-offline'}'></i></i><span data-node=${JSON.stringify(node)} style="cursor: pointer" class="spanClass" title=${node.label}>${node.label}</span><i id="video" class="removeBtn treeIcon video ${node.lineon == 1 && node.typecate[2] == 1 &&
                    node.remote_kill == 0 ? 'videoEnable' : ''}"></i>${isGB ? node.typecate[9] == 1 ? `<i id="twoVideo" class="removeBtn treeIcon two ${node.lineon == 1 && node.typecate[9] == 1 ? 'twoEnable' : ''}"></i>` : '' : `<i id="voice" class="removeBtn treeIcon voice ${node.lineon == 1 && node.typecate[3] == 1 && node.remote_kill == 0 ? 'voiceEnable' : ''}"></i>`}`;
                }else if(node.recorder_type == 3){
                    html += `<i class='icon ${node.lineon == "1" ? 'jjy-online' : 'jjy-offline'}'></i></i><span data-node=${JSON.stringify(node)} style="cursor: pointer" class="spanClass" title=${node.label}>${node.label}</span><i id="video" class="removeBtn treeIcon video ${node.lineon == 1 && node.typecate[2] == 1 &&
                    node.remote_kill == 0 ? 'videoEnable' : ''}"></i>${isGB ?  node.typecate[9] == 1 ?`<i id="twoVideo" class="removeBtn treeIcon two ${node.lineon == 1 && node.typecate[9] == 1 ? 'twoEnable' : ''}"></i>`: ''  : `<i id="voice" class="removeBtn treeIcon voice ${node.lineon == 1 && node.typecate[3] == 1 && node.remote_kill == 0 ? 'voiceEnable' : ''}"></i>`}`;
                }if(node.recorder_type == 1){
                    html += `<i class='icon ${node.lineon == "1" ? 'equip2' : 'equip'}'></i></i><span data-node=${JSON.stringify(node)} style="cursor: pointer" class="spanClass" title=${node.label}>${node.label}</span><i id="video" class="removeBtn treeIcon video ${node.lineon == 1 && node.typecate[2] == 1 &&
                    node.remote_kill == 0 ? 'videoEnable' : ''}"></i>${isGB ?  node.typecate[9] == 1 ? `<i id="twoVideo" class="removeBtn treeIcon two ${node.lineon == 1 && node.typecate[9] == 1 ? 'twoEnable' : ''}"></i>` : '' : `<i id="voice" class="removeBtn treeIcon voice ${node.lineon == 1 && node.typecate[3] == 1 && node.remote_kill == 0 ? 'voiceEnable' : ''}"></i>`}`;
                }if(node.recorder_type == 0){
                    html += `<i class='icon ${node.lineon == "1" ? 'equip2' : 'equip'}'></i></i><span data-node=${JSON.stringify(node)} style="cursor: pointer" class="spanClass" title=${node.label}>${node.label}</span><i id="video" class="removeBtn treeIcon video ${node.lineon == 1 && node.typecate[2] == 1 &&
                    node.remote_kill == 0 ? 'videoEnable' : ''}"></i>${isGB ? node.typecate[9] == 1 ? `<i id="twoVideo" class="removeBtn treeIcon two ${node.lineon == 1 && node.typecate[9] == 1 ? 'twoEnable' : ''}"></i>`: ''  : `<i id="voice" class="removeBtn treeIcon voice ${node.lineon == 1 && node.typecate[3] == 1 && node.remote_kill == 0 ? 'voiceEnable' : ''}"></i>`}`;
                }if(node.recorder_type == -1){
                    html += `<i class='icon ${node.assist_enforcer_status == 0 ? 'offline' : node.assist_enforcer_status == 1 ? 'online'
                        : 'busy'}'></i></i><span data-node=${JSON.stringify(node)} style="cursor: pointer" class="spanClass" title=${node.label}>${node.label}</span>`;
                }
            }

            if (node.children && node.children.length > 0) {
                html += '<ul>';
                // html += '<ul style="display:none;">';
                node.children.forEach(childNode => {
                    html += createTreeNodeHTML(childNode);
                });
                html += '</ul>';
            }

            html += '</li>';
            return html;
        }

        // 渲染树组件
        function renderTree(treeData, containerId) {
            console.log(treeData, containerId);
            const container = document.getElementById(containerId);
            container.innerHTML = '<ul>' + createTreeNodeHTML(treeData[0]) + '</ul>';

            // 绑定点击事件，处理懒加载节点
            container.addEventListener('click', function(event) {
                const target = event.target;
                console.log(event,target,target.parentElement.getAttribute('lazy'));
                if (target.tagName === 'SPAN' && target.parentElement.tagName === 'LI' && !target.parentElement.getAttribute('lazy')) {
                    // console.log(JSON.parse(target.getAttribute('data-node')));
                    const ulElement = target.nextElementSibling;
                    if (ulElement && ulElement.tagName === 'UL') {
                        ulElement.style.display = ulElement.style.display === 'none' ? 'block' : 'none';
                    }
                } 
                else if(target.tagName === 'SPAN' && target.parentElement.tagName === 'LI' && target.parentElement.getAttribute('lazy')){
                    // target.innerHTML = ''
                    var dataInfo = JSON.parse(target.getAttribute('data-node'));
                    if(isGB){
                        unitEquipTreeGB(dataInfo.id, "", [0, 1, 2, 3, 4, 5, 6]).then((response)=>{
                            console.log(response);
                            var res = response.data;
                            if(res.code == 200){
                                res.data.childunit.forEach((item)=>{
                                    item.children = [];
                                    item.lazy = true;
                                    item.label = item.unitname;
                                    item.id = item.unit;
                                    item.treecate = '1';
                                })
                                res.data.devices.forEach((item)=>{
                                    item.label = item.hostname ? item.hostname : item.hsotcode ? item.hsotcode : item.hostbody;
                                    item.treecate = '0';
                                    item.lineon = item.status;
                                    if(item.recorder_type != -1 && item.typecate){
                                        item.typecate = JSON.parse(item.typecate)
                                    }
                                })
                                var childDataInfo = res.data.devices.concat(res.data.childunit)
                                console.log(childDataInfo);
                                treeData = [{
                                    label: dataInfo.label,
                                    // label: dataInfo.unitname,
                                    // lazy: true,
                                    treecate: '1',
                                    children: [...childDataInfo],
                                }];
                                // 渲染懒加载节点的子节点
                                console.log(treeData);
                                target.innerHTML = createTreeNodeHTML(treeData[0]);
                            }
                        })
                    }
                    else{
                        getTreeInfo(dataInfo.id).then((response)=>{
                            console.log(response);
                            var res = response.data;
                            if(res.code == 200){
                                res.data.forEach((item) => {
                                    if (item.treecate == '1') {
                                        item.children = [];
                                        item.lazy = true;
                                        item.label = item.unitname;
                                    } else {
                                        item.label = item.hostname;
                                    }
                                })
                                treeData = [{
                                    label: dataInfo.unitname,
                                    // lazy: true,
                                    treecate: '1',
                                    children: [...res.data],
                                }];
                                // 渲染懒加载节点的子节点
                                console.log(treeData);
                                target.innerHTML = createTreeNodeHTML(treeData[0]);
                            }
                        })
                    }
                } 
                else if(target.id === 'video'){
                    var dataInfo = JSON.parse(target.previousElementSibling.getAttribute('data-node'));
                    console.log(dataInfo);
                    startLive(dataInfo).then((res)=>{
                        console.log(res);
                        if(res.code == 200){
                            if(isGB){
                                if(res.data[0].is_existed){
                                    sign().then((dom)=>{
                                        var dynamicDiv = new DynamicElement_gb(dom,res.data[0])
                                        console.log(dynamicDiv);
                                        dynamicDiv.init()
                                        dataExample.push({
                                            hostbody:res.data[0].hostbody,
                                            example:dynamicDiv
                                        })
                                        console.log(dataExample,'数组');
                                    })
                                }
                            }else{
                                if(res.data[0].is_existed){
                                    sign().then((dom)=>{
                                        var dynamicDiv = new DynamicElement(dom,res.data[0])
                                        console.log(dynamicDiv);
                                        dynamicDiv.init()
                                        dataExample.push({
                                            hostbody:res.data[0].hostbody,
                                            example:dynamicDiv
                                        })
                                        console.log(dataExample,'数组');
                                    })
                                }
                            }
                        }
                    })
                } 
                else if(target.id === 'voice'){
                    var dataInfo = JSON.parse(target.previousElementSibling.previousElementSibling.getAttribute('data-node'));
                    console.log(dataInfo);
                    getStreamUrl().then((res)=>{
                        if(res.code == 200){
                            getPlatform(res.data.audio.webrtc_url).then(()=>{
                                var timer = setInterval(() => {
                                    console.log(wsMessage,'wsMessage');
                                    if(wsMessage?.zk_platform_push_audio){
                                        clearInterval(timer);
                                        if(wsMessage.zk_platform_push_audio.status == 1){
                                            startAudio([dataInfo.hostbody]).then((res1)=>{
                                                if(res1.code == 200){
                                                    if (res1.data[0].is_existed) {
                                                        sign().then((dom)=>{
                                                            var dynamicDiv = new DynamicElement_audio(dom,res1.data[0])
                                                            console.log(dynamicDiv);
                                                            dynamicDiv.init()
                                                            dataExample.push({
                                                                hostbody:res1.data[0].hostbody,
                                                                example:dynamicDiv
                                                            })
                                                            console.log(dataExample,'数组');
                                                        })
                                                    }
                                                }else{
                                                    console.log(res.msg);
                                                }
                                            })
                                        }else{
                                            console.log("平台推流失败");
                                        }
                                    }
                                },100)
                            }).catch((err)=>{
                                console.log(err);
                            })
                        }else{
                            console.log(res.msg);
                        }
                    }).catch((err)=>{
                        console.log(err);
                    })
                }
                else if(target.id === 'twoVideo'){
                    var dataInfo = JSON.parse(target.previousElementSibling.previousElementSibling.getAttribute('data-node'));
                    console.log(dataInfo);
                    startCall(dataInfo).then((res)=>{
                        if(res.code == 200){
                            var timer = setTimeout(() => {
                                let send = {
                                    status: 2,
                                    callId: res.data.callId,
                                };
                                stopTalk(send).then((res) => {
                                    clearTimeout(timer);
                                });
                            }, 30000);
                            isGBTwoInfo.push({
                                data:res.data,
                                hostbody:dataInfo.hostbody,
                                callId:res.data.callId,
                                timer:timer
                            })
                        }
                    }).catch((err)=>{
                        console.log(err);
                    })
                }
            });
        }

        function sign(){
            return new Promise((resolve,reject)=>{
                // 先判断有没有选择窗口
                if(!signBox){
                    var playerDom = [];
                    var playDomList = []; //还没有拉流的播放器dom
                    var father = document.getElementById("boxss").children;
                    Array.from(father).forEach((item) => {
                        console.log(item.children);
                        if (item.children.length == 0) {
                            playDomList.push(item.classList[item.classList.length - 1]);
                        } else if (item.children.length != 0) {
                            // 有空的id为null的盒子
                            if (item.children.length == 1) {
                                playerDom.push(item.children[0].id);
                                playDomList.push(item.classList[item.classList.length - 1]);
                            }
                        }
                    });
                    var dom = document.getElementById(document.getElementsByClassName(playDomList[0])[0].id)
                    resolve(dom)
                }else{
                    // 再判断选择的窗口有没有拉流
                    if(signBox_son.length != 2){
                        var dom = document.getElementById(signBox_son[0].parentElement.id)
                        resolve(dom)
                    }else{
                        var playerDom = [];
                        var playDomList = []; //还没有拉流的播放器dom
                        var father = document.getElementById("boxss").children;
                        Array.from(father).forEach((item) => {
                            console.log(item.children);
                            if (item.children.length == 0) {
                                playDomList.push(item.classList[item.classList.length - 1]);
                            } else if (item.children.length != 0) {
                                // 有空的id为null的盒子
                                if (item.children.length == 1) {
                                    playerDom.push(item.children[0].id);
                                    playDomList.push(item.classList[item.classList.length - 1]);
                                }
                            }
                        });
                        var dom = document.getElementById(document.getElementsByClassName(playDomList[0])[0].id)
                        resolve(dom)
                    }
                }
            })
        }

        function logoutClick(){
            logout().then(()=>{
                window.localStorage.setItem("isLogin", 0);
                window.location.href = 'Login.html';
            })
        }

        // 维持心跳
        function heartbeat() {
            return new Promise((resolve) => {
                heart();
                timerId = setInterval(heart, 20000);
                resolve();
            });
        }

        // 连接ws
        function createWs() {
            createWebSocket().then((res) => {
                console.log(res);
                ws = new WebSocket(res.data.data.wsurl);

                var data1 = {
                    logincode: res.data.data.logincode,
                    username: res.data.data.username,
                    scode: res.data.data.scode,
                    cate: res.data.data.auth_cate,
                };
                var psd = {
                    command: "client_login",
                    data: JSON.stringify(data1),
                };
                ws.onopen = function(e) {
                    console.log("---ws--已经连接----------", e, psd);
                    ws.send(JSON.stringify(psd));
                };
                ws.onerror = function(e) {
                    console.warn("scoket连接出错" + e);
                    ws.close();
                    ws = null;
                };
                ws.onclose = function() {
                    // console.log("ws-----已断开连接--------------");
                };
                ws.onmessage = function(event) {
                    var message = event.data; // 接收到的消息字符串
                    var object = JSON.parse(message); // 解析消息字符串为对象

                    // 私有拉流
                    if(object.zk_start_live){
                        // console.log(object);
                        // 先创建dom节点
                        sign().then((dom)=>{
                            var dynamicDiv = new DynamicElement(dom,object.zk_start_live)
                            console.log(dynamicDiv);
                            dynamicDiv.init()
                            dataExample.push({
                                hostbody:object.zk_start_live.hostbody,
                                example:dynamicDiv
                            })
                            console.log(dataExample,'数组');
                        })
                    }

                    // 私有对讲
                    if(object.zk_start_audio){
                        sign().then((dom)=>{
                            var dynamicDiv = new DynamicElement_audio(dom,object.zk_start_audio)
                            console.log(dynamicDiv);
                            dynamicDiv.init()
                            dataExample.push({
                                hostbody:object.zk_start_audio.hostbody,
                                example:dynamicDiv
                            })
                            console.log(dataExample,'数组');
                        })
                    }

                    // 私有设备端挂断拉流或者对讲
                    if(object.alarm_data){
                        if(object.alarm_data.type == '5'){
                            var data = dataExample.find((item)=>{
                                return item.hostbody == object.alarm_data.hostbody
                            })

                            console.log(data);
                            data.example.newCloseDiv_click()
                        }
                    }

                    // 私有对讲时，确保设备先收到平台的消息
                    if(object.zk_platform_push_audio){
                        wsMessage = object
                    }

                    // 设备发起双人执法
                    if(object.assist_enforcer_talk){
                        console.log(object.assist_enforcer_talk);
                        if(isGB){
                            // 这里默认收到双人执法消息就接受，第三方做一个弹窗按钮去实现,接受或者拒绝
                            var val = object.assist_enforcer_talk;
                            // 首先这里判断该双人执法的指挥台是否是设备，且该设备有没有在拉流，有就关闭掉，我这里就不处理
                            let sent = {
                                callId: val.callId,
                                sn: val.sn,
                                subsn: val.subsn,
                            };
                            console.log(sent, "1111111111111111111111111111");
                            startTalk(sent).then((res)=>{
                                if(res.code == 200){
                                    var send = {
                                        hostbody_arr: [val.hostbody],
                                        callId: val.callId,
                                    };
                                    startLiveGB(send).then((res)=>{
                                        if(res.code == 200){
                                            if(res.data[0].is_existed){
                                                sign().then((dom)=>{
                                                    var dynamicDiv = new DynamicElement_talk_gb(dom,res.data[0])
                                                    console.log(dynamicDiv);
                                                    dynamicDiv.init()
                                                    isGBTwoInfo.push({
                                                        hostbody:res.data[0].hostbody,
                                                        callId:res.data[0].callId,
                                                        example:dynamicDiv
                                                    })
                                                    console.log(isGBTwoInfo,'数组');
                                                })
                                            }
                                        }else{
                                            // 退出双人执法
                                        }
                                    }).catch((err)=>{
                                        console.log(err);
                                        // 退出双人执法
                                    })
                                }else{
                                    // 失败
                                }
                            }).catch((err)=>{
                                console.log(err);
                            })
                        }
                        else{
                            var val = object.assist_enforcer_talk;
                            // 这里默认收到双人执法消息就接受，第三方做一个弹窗按钮去实现
                            let send = {
                                callId: val.callId,
                                is_assist_enforcer: 1,
                            };
                            getStreamUrl(send).then((res)=>{
                                if (res.code == 200) {
                                    startTalk(val).then((response)=>{
                                        if(response.code == 200){
                                            pcStartVideoUrl = response.data.platform_rtsp
                                        }else{
                                            console.log(res.msg);
                                        }
                                    })
                                }else{
                                    console.log(res.msg);
                                }
                            })
                        }
                    }

                    // 双人执法设备响应后的信息
                    if(object.assist_enforcer_talk_confirm){
                        if(isGB){
                            var data = isGBTwoInfo.find((item)=>{
                                return item.callId == object.assist_enforcer_talk_confirm.callId
                            })
                            if(data){
                                clearTimeout(data.timer);
                                data.timer = null;
                            }
                            var send = {
                                hostbody_arr: [object.assist_enforcer_talk_confirm.hostbody],
                                callId: object.assist_enforcer_talk_confirm.callId,
                            };
                            startLiveGB(send).then((res)=>{
                                if(res.code == 200){
                                    if(res.data[0].is_existed){
                                        sign().then((dom)=>{
                                            var dynamicDiv = new DynamicElement_talk_gb(dom,res.data[0])
                                            console.log(dynamicDiv);
                                            dynamicDiv.init()
                                            isGBTwoInfo.push({
                                                hostbody:res.data[0].hostbody,
                                                callId:res.data[0].callId,
                                                example:dynamicDiv
                                            })
                                            console.log(isGBTwoInfo,'数组');
                                        })
                                    }
                                }else{
                                    // 退出双人执法
                                }
                            }).catch((err)=>{
                                console.log(err);
                                // 退出双人执法
                            })
                        }
                        else{
                            console.log(object.assist_enforcer_talk_confirm);
                            var val = object.assist_enforcer_talk_confirm;
                            sign().then((dom) => {
                                var dynamicDiv = new DynamicElement_talk(dom, val)
                                console.log(dynamicDiv);
                                dynamicDiv.init()
                                dataExample.push({
                                    callId:val.callId,
                                    example:dynamicDiv
                                })
                                console.log(dataExample,'数组');
                            })
                        }
                    }

                    // 设备端结束双人执法
                    if(object.assist_enforcer_talk_stop){
                        if(isGB){
                            console.log(object.assist_enforcer_talk_stop);
                            var data = isGBTwoInfo.find((item)=>{
                                return item.callId == object.assist_enforcer_talk_stop.callId
                            })
    
                            console.log(data);
                            if(data){
                                data.example.newCloseDiv_click()
                            }
                        }else{
                            console.log(object.assist_enforcer_talk_stop);
                            var data = dataExample.find((item)=>{
                                return item.callId == object.assist_enforcer_talk_stop.callId
                            })
    
                            console.log(data);
                            if(data){
                                data.example.newCloseDiv_click()
                            }
                        }
                    }

                    // 国标拉流
                    if(object.start_live){
                        if(object.start_live.callId){
                            console.log(object.start_live);
                            if(object.start_live.code == 200){
                                if(object.start_live.hostbody != assist_enforcer_hostbody){
                                    sign().then((dom)=>{
                                        var dynamicDiv = new DynamicElement_talk_gb(dom,object.start_live)
                                        console.log(dynamicDiv);
                                        dynamicDiv.init()
                                        var data = isGBTwoInfo.find((item)=>{
                                            return item.callId == object.start_live.callId
                                        })
                                        if(data){
                                            data.example = dynamicDiv;
                                        }else{
                                            isGBTwoInfo.push({
                                                hostbody:object.start_live.hostbody,
                                                callId:object.start_live.callId,
                                                example:dynamicDiv
                                            })
                                        }
                                        console.log(isGBTwoInfo,'数组');
                                    })
                                }else{
                                    // 作为指挥台的设备，只能是该调度台才可以拉流，在此没有做判断
                                    var data = isGBTwoInfo.find((item)=>{
                                        return item.callId == object.start_live.callId
                                    })
                                    if(data){
                                        data.example.initPlay1(object.start_live)
                                    }
                                }
                            }else{
                                // 退出双人执法
                            }
                        }else{
                            console.log(object.start_live);
                            sign().then((dom)=>{
                                var dynamicDiv = new DynamicElement_gb(dom,object.start_live)
                                console.log(dynamicDiv);
                                dynamicDiv.init()
                                dataExample.push({
                                    hostbody:object.start_live.hostbody,
                                    example:dynamicDiv
                                })
                                console.log(dataExample,'数组');
                            })
                        }
                    }

                    // 国标对讲
                    if(object.start_audio){
                        console.log(object.start_audio,'object.start_audio');
                        wsAudioMessage = object
                    }


                    // 平台拉起双人执法，设备拒绝
                    if(object.assist_enforcer_talk_refuse){
                        console.log(object.assist_enforcer_talk_refuse,'object.assist_enforcer_talk_refuse');
                        var index = isGBTwoInfo.findIndex((obj) => {
                            return obj.callId === object.assist_enforcer_talk_refuse.callId; // 查找id属性等于idToRemove的对象的索引
                        });
                        if (index !== -1) {
                            isGBTwoInfo.splice(index, 1); // 删除找到的对象
                        }
                        console.log(isGBTwoInfo, '数组');
                    }

                    // 国标双人执法时，平台的推流信息
                    if(object.push_video){
                        console.log(object.push_video,'object.push_video');
                        pusVideo = object.push_video;
                    }

                    // 国标双人执法禁言与否
                    if(object.gb_mute){
                        console.log(object.gb_mute,'object.gb_mute');
                        saveMuteData = object.gb_mute;
                    }


                };
            })
        }

        // 切换窗口
        function choice1(num){
            console.log(num);
            var screen1 = document.getElementById("screen1");
            var screen4 = document.getElementById("screen4");
            var screen6 = document.getElementById("screen6");
            var screen9 = document.getElementById("screen9");
            var screen16 = document.getElementById("screen16");
            switch (num) {
                case 1:
                    screen4.setAttribute("class", "screen_li screen4");
                    screen6.setAttribute("class", "screen_li screen6");
                    screen9.setAttribute("class", "screen_li screen9");
                    screen16.setAttribute("class", "screen_li screen16");
                    screen1.setAttribute("class", "screen_li screenOpen1");
                    screens = 1;
                    break;
                case 4:
                    screen1.setAttribute("class", "screen_li screen1");
                    screen6.setAttribute("class", "screen_li screen6");
                    screen9.setAttribute("class", "screen_li screen9");
                    screen16.setAttribute("class", "screen_li screen16");
                    screen4.setAttribute("class", "screen_li screenOpen4");
                    screens = 4;
                    break;
                case 6:
                    screen1.setAttribute("class", "screen_li screen1");
                    screen4.setAttribute("class", "screen_li screen4");
                    screen9.setAttribute("class", "screen_li screen9");
                    screen16.setAttribute("class", "screen_li screen16");
                    screen6.setAttribute("class", "screen_li screenOpen6");
                    screens = 6;
                    break;
                case 9:
                    screen1.setAttribute("class", "screen_li screen1");
                    screen4.setAttribute("class", "screen_li screen4");
                    screen6.setAttribute("class", "screen_li screen6");
                    screen16.setAttribute("class", "screen_li screen16");
                    screen9.setAttribute("class", "screen_li screenOpen9");
                    screens = 9;
                    break;
                case 16:
                    screen1.setAttribute("class", "screen_li screen1");
                    screen4.setAttribute("class", "screen_li screen4");
                    screen6.setAttribute("class", "screen_li screen6");
                    screen9.setAttribute("class", "screen_li screen9");
                    screen16.setAttribute("class", "screen_li screenOpen16");
                    screens = 16;
                    break;
                default:
                    break;
            }
            splitScreen(screens)
        }

        function splitScreen(num) {
            console.log(num);
            var arr = divDomId();
            switch (num) {
                case 1:
                arr.forEach((item, index) => {
                    if (index == 0) {
                    item.setAttribute("class", "type chromeBox1 data0");
                    } else {
                    item.setAttribute("class", `data${index}`);
                    item.style.display = "none";
                    }
                });
                break;
                case 4:
                arr.forEach((item, index) => {
                    if (index == 0 || index == 1 || index == 2 || index == 3) {
                    if (index == 0) {
                        item.setAttribute("class", "chromeBox data0");
                    } else if (index == 1) {
                        item.setAttribute("class", "chromeBox data1");
                    } else if (index == 2) {
                        item.setAttribute("class", "chromeBox data2");
                    } else if (index == 3) {
                        item.setAttribute("class", "chromeBox data3");
                    }
                    item.style.display = "block";
                    } else {
                    item.setAttribute("class", `data${index}`);
                    item.style.display = "none";
                    }
                });
                break;
                case 6:
                arr.forEach((item, index) => {
                    if (index == 0) {
                    item.setAttribute(
                        "class",
                        "chromeBoxLeft chromeBox1 chromeBox6 data0"
                    );
                    item.style.display = "block";
                    } else if (
                    index == 1 ||
                    index == 2 ||
                    index == 3 ||
                    index == 4 ||
                    index == 5
                    ) {
                    if (index == 1) {
                        item.setAttribute(
                        "class",
                        "chromeBox1 chromeBoxRight chromeBox6 data1"
                        );
                    } else if (index == 2) {
                        item.setAttribute(
                        "class",
                        "chromeBox1 chromeBoxRight chromeBox6 data2"
                        );
                    } else if (index == 3) {
                        item.setAttribute(
                        "class",
                        "chromeBox1 chromeBox6-1 chromeBox6 data3"
                        );
                    } else if (index == 4) {
                        item.setAttribute(
                        "class",
                        "chromeBox1 chromeBox6-1 chromeBox6 data4"
                        );
                    } else if (index == 5) {
                        item.setAttribute(
                        "class",
                        "chromeBox1 chromeBox6-1 chromeBox6 data5"
                        );
                    }
                    item.style.display = "block";
                    } else {
                    item.setAttribute("class", `data${index}`);
                    item.style.display = "none";
                    }
                });
                break;
                case 9:
                arr.forEach((item, index) => {
                    if (
                    index == 0 ||
                    index == 1 ||
                    index == 2 ||
                    index == 3 ||
                    index == 4 ||
                    index == 5 ||
                    index == 6 ||
                    index == 7 ||
                    index == 8
                    ) {
                    if (index == 0) {
                        item.setAttribute("class", "chromeBox9 data0");
                    } else if (index == 1) {
                        item.setAttribute("class", "chromeBox9 data1");
                    } else if (index == 2) {
                        item.setAttribute("class", "chromeBox9_right data2");
                    } else if (index == 3) {
                        item.setAttribute("class", "chromeBox9 data3");
                    } else if (index == 4) {
                        item.setAttribute("class", "chromeBox9 data4");
                    } else if (index == 5) {
                        item.setAttribute("class", "chromeBox9_right data5");
                    } else if (index == 6) {
                        item.setAttribute("class", "chromeBox9 data6");
                    } else if (index == 7) {
                        item.setAttribute("class", "chromeBox9 data7");
                    } else if (index == 8) {
                        item.setAttribute("class", "chromeBox9_right data8");
                    }
                    item.style.display = "block";
                    } else {
                    item.setAttribute("class", `data${index}`);
                    item.style.display = "none";
                    }
                });
                break;
                case 16:
                arr.forEach((item, index) => {
                    if (index <= 16) {
                    if (index == 0) {
                        item.setAttribute("class", "chromeBox16 data0");
                    } else if (index == 1) {
                        item.setAttribute("class", "chromeBox16 data1");
                    } else if (index == 2) {
                        item.setAttribute("class", "chromeBox16 data2");
                    } else if (index == 3) {
                        item.setAttribute("class", "chromeBox16 data3");
                    } else if (index == 4) {
                        item.setAttribute("class", "chromeBox16 data4");
                    } else if (index == 5) {
                        item.setAttribute("class", "chromeBox16 data5");
                    } else if (index == 6) {
                        item.setAttribute("class", "chromeBox16 data6");
                    } else if (index == 7) {
                        item.setAttribute("class", "chromeBox16 data7");
                    } else if (index == 8) {
                        item.setAttribute("class", "chromeBox16 data8");
                    } else if (index == 9) {
                        item.setAttribute("class", "chromeBox16 data9");
                    } else if (index == 10) {
                        item.setAttribute("class", "chromeBox16 data10");
                    } else if (index == 11) {
                        item.setAttribute("class", "chromeBox16 data11");
                    } else if (index == 12) {
                        item.setAttribute("class", "chromeBox16 data12");
                    } else if (index == 13) {
                        item.setAttribute("class", "chromeBox16 data13");
                    } else if (index == 14) {
                        item.setAttribute("class", "chromeBox16 data14");
                    } else if (index == 15) {
                        item.setAttribute("class", "chromeBox16 data15");
                    }
                    item.style.display = "block";
                    }
                });
                break;

                default:
                break;
            }
        }

        function divDomId(){
            var info0 = document.getElementById("info0");
            var info1 = document.getElementById("info1");
            var info2 = document.getElementById("info2");
            var info3 = document.getElementById("info3");
            var info4 = document.getElementById("info4");
            var info5 = document.getElementById("info5");
            var info6 = document.getElementById("info6");
            var info7 = document.getElementById("info7");
            var info8 = document.getElementById("info8");
            var info9 = document.getElementById("info9");
            var info10 = document.getElementById("info10");
            var info11 = document.getElementById("info11");
            var info12 = document.getElementById("info12");
            var info13 = document.getElementById("info13");
            var info14 = document.getElementById("info14");
            var info15 = document.getElementById("info15");
            var arr = [
                info0,
                info1,
                info2,
                info3,
                info4,
                info5,
                info6,
                info7,
                info8,
                info9,
                info10,
                info11,
                info12,
                info13,
                info14,
                info15,
            ];
            return arr;
        }

        function mousedown(e) {
            // chooseBox(e);
        }

        function chooseBox(e) {
            var dom = document.getElementById('boxss')
            Array.from(dom.children).forEach((item) => {
                if (item.classList[item.classList.length - 1] ==e.currentTarget.classList[e.currentTarget.classList.length - 1]) {
                    var clickBox = document.getElementById(item.id);
                    if (
                        getStyle(clickBox, "border") == "1px solid rgb(84,255,0)"
                    ) {
                        return
                    } else {
                        setStyle(clickBox, "border", "1px solid rgb(84,255,0)");
                        signBox = item.classList[item.classList.length - 1];
                        signBox_son = item.children;
                    }
                } else {
                    setStyle(item, "border", "1px solid rgb(238, 238, 238)");
                }
            });
            console.log(signBox,signBox_son);
        }

        function getStyle(obj, attr) {
            //获取非行间样式，obj是对象，attr是值
            if (obj.currentStyle) {
                //针对ie获取非行间样式
                return obj.currentStyle[attr];
            } else {
                return getComputedStyle(obj, false)[attr]; //针对非ie
            }
        }

        function setStyle(obj, attr, value) {
            //对象，样式，值。传2个参数的时候为获取样式，3个是设置样式
            if (arguments.length == 2) {
                //arguments参数数组，当参数数组长度为2时表示获取css样式
                return getStyle(obj, attr); //返回对象的非行间样式用上面的getStyle函数
            } else {
                if (arguments.length == 3) {
                //当传三个参数的时候为设置对象的某个值
                obj.style[attr] = value;
                }
            }
        }

        function allowDrop(e) {
            e.preventDefault(); ////避免浏览器对数据的默认处理（drop 事件的默认行为是以链接形式打开）
        }

        function drag(e) {
            //使得可拖动(有数据存入从而使拖动有可能实现)
            console.log("我俩拖动",e);
            if (e.target === e.currentTarget) {
                console.log(e.target.children.length);
                if (e.target.children.length == 2) {
                    var divdom = e.target.children[1].id;
                    srcDiv = document.getElementById(e.target.id);
                    f_id = e.target;
                    console.log(divdom);
                    e.dataTransfer.setData("Text", divdom);
                    e.dataTransfer.setData(
                        "Class",
                        e.target.classList[e.target.classList.length - 1]
                    );
                }
            }
        }

        function drop(e) {
            e.preventDefault();
            var data = e.dataTransfer.getData("Text");
            var dom = e.dataTransfer.getData("Class");
            console.log(dom, data);
            // chooseBox1(e, dom);
            if (data) {
                console.log(e, data, e.currentTarget, e.currentTarget.classList);
                if (e.currentTarget.children.length == 2) {
                var divdom = document.getElementById(e.currentTarget.children[1].id);
                }
                e.currentTarget.appendChild(document.getElementById(data)); //使得拖动后的图像可以得到显示(向目标添加一个子物体) 注意没引号
                if (divdom) {
                srcDiv.appendChild(divdom);
                }
                var id1 = deepCopy({
                    id: f_id.classList[f_id.classList.length - 1],
                    });
                    var id2 = deepCopy({
                    id: e.currentTarget.classList[e.currentTarget.classList.length - 1],
                });
                console.log(id1, id2, id1.id, id2.id);
                document.getElementById(f_id.id).classList.remove(id1.id);
                document.getElementById(f_id.id).classList.add(id2.id);
                document.getElementById(e.currentTarget.id).classList.remove(id2.id);
                document.getElementById(e.currentTarget.id).classList.add(id1.id);
            }
        }

        function deepCopy(object) {
            if (object === null || typeof object !== "object") {
                return;
            }
            let newObj = new object.constructor();
            for (let key in object) {
                if (object.hasOwnProperty(key)) {
                    let element = object[key];
                    if (element !== null && typeof element == "object") {
                        newObj[key] = methods.deepCopy(element);
                    } else {
                        newObj[key] = object[key];
                    }
                }
            }
            return newObj;
        }

        // 平台推流的webRTC实例
        function getPlatform(urlInfo) {
            return new Promise((resolve, reject) => {
                if (isStartAudio == 0) {
                    // 已经获取到了就不用再获取了
                    var InitialData = {
                        element: "",
                        debug: true,
                        zlmsdpUrl: urlInfo,
                        simulcast: false,
                        useCamera: false,
                        audioEnable: true,
                        videoEnable: false,
                        recvOnly: false,
                        resolution: { w: 3840, h: 2160 },
                        usedatachannel: false,
                    };
                    pcStartAudio = new Webrtc(InitialData);
                    pcStartAudio.start_play().then(() => {
                        resolve();
                    })
                    .catch((err) => {
                        console.log(err, "采集视音频报错");
                        reject();
                    });
                } else {
                    resolve();
                }
            });
        }
    </script>
    <script src="../js/DynamicDom.js"></script>
</body>

</html>