import { heatMapData } from "./heatMapData.js"
import Heatmap3D from './heatmap.js'
import * as externalRenderers from 'https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/views/3d/externalRenderers.js';

let heat3dEffect = null;

export const addHeatMap = function () {

    if (!heat3dEffect) {
        heat3dEffect = new Heatmap3D(window.view, {
            externalRenderers,
            pixelValueArray: heatMapData.data,
            extent: {
                xmax: 120.2344580,
                xmin: 119.3300629,
                ymax: 29.5424113,
                ymin: 28.8343048
            },
            overstate: 1200
        })
        externalRenderers.add(window.view, heat3dEffect);

    }


}

export function removeHeatmap() {
    if (heat3dEffect) {
        externalRenderers.remove(window.view, heat3dEffect);
        heat3dEffect = null;
    }
}