<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="referrer" content="no-referrer" />
    <title>行政执法局-左</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/Vue/vue-count-to.min.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/elementui/js/index.js"></script>
    <script src="/echarts/echarts.min.js"></script>
    <script src="/echarts/echarts-liquidfill.js"></script>
    <script src="/static/js/jslib/moment.js"></script>
    <style>
      .el-date-editor .el-range-input {
        color: #eee;
        width: 122%;
        background: transparent;
        font-size: 24px;
        z-index: 555;
      }
      .el-input__inner {
        height: 48px !important;
      }
      .el-date-range-picker__time-header .el-input__inner {
        background-color: transparent !important;
        border: none !important;
      }
      .el-picker-panel {
        color: #fff;
        background-color: #132c4e;
      }
      .el-date-editor .el-range-separator {
        color: #fff;
        line-height: 38px;
        font-size: 20px;
      }
      .el-month-table td.in-range div,
      .el-month-table td.in-range div:hover {
        background-color: #39537a;
      }
      .el-date-range-picker__content .el-date-range-picker__header div {
        font-size: 23px;
      }
      .el-month-table {
        font-size: 22px;
        white-space: nowrap;
      }
      .el-date-editor .el-range__icon {
        font-size: 20px;
        line-height: 39px;
      }
      .el-date-editor .el-range__close-icon {
        font-size: 20px;
        line-height: 40px;
      }

      .image-slot {
          text-align: center;
          line-height: 190px;
          color: #ffffff;
          font-size: 25px;
      }
    </style>
  </head>

  <body>
    <div id="left">
      <div class="hearder_h1 cursor" @click="changePage('zhdd')">
        <span>指挥调度 <i class="click-i"></i></span>
      </div>
      <div class="hearder_h2">
        <span>今日值班</span>
        <img
          src="../../../images/common/edit.png"
          alt=""
          style="margin-left: 10px; cursor: pointer"
          @click="openManage(1)"
        />
      </div>
      <!-- 指挥体系 -->
      <div class="zhtx_box box">
        <!-- <div class="table">
                    <div class="th">
                        <div class="th_td" style="flex: 0.25">职务</div>
                        <div class="th_td" style="flex: 0.25">部门</div>
                        <div class="th_td" style="flex: 0.25">姓名</div>
                        <div class="th_td" style="flex: 0.25">联系电话</div>
                    </div>
                    <div class="tbody" id="box" @mouseover="mouseenterEvent()" @mouseleave="mouseleaveEvent()">
                        <div class="tr" v-for="(item,index) in tableData" :key="index">
                            <div class="tr_td" style="flex: 0.25">{{item.duties}}</div>
                            <div class="tr_td" style="flex: 0.25">{{item.department}}</div>
                            <div class="tr_td" style="flex: 0.25">{{item.name}}</div>
                            <div class="tr_td" style="flex: 0.25">{{item.phone}}</div>
                        </div>
                    </div>
                </div> -->
        <div style="height: 315px">
          <!-- <div
            class="s-flex s-font-40 font-italic s-c-grey-light s-m-b-10"
            style="width: max-content; margin: auto"
          >
            <span>今日在岗人数</span>
            <div class="count_box">
              <div
                v-for="(item,index) in zbTotal"
                :key="index"
                class="count-toNum"
              >
                <count-to
                  :start-val="0"
                  :end-val="Number(item)"
                  :duration="3000"
                  class="s-c-yellow-gradient"
                >
                </count-to>
              </div>
            </div>
            <span>人</span>
          </div> -->
          <div class="s-flex s-row-around s-font-32 s-c-white s-m-t-20">
            <span>{{zbList[0].pos}}</span>
            <span>姓名：{{zbList[0].name}}</span>
            <span>
              电话：{{toPhone(zbList[0].phone)}}
              <span class="iconPhone" @click="openCall(zbList[0].phone)"></span>
              <span class="iconVideo" @click="openVideo(zbList[0].phone)"></span>
            </span>
          </div>
          <div class="yyjc s-flex s-row-evenly s-m-t-25">
            <div class="s-flex" v-for="(item,index) in zbList.slice(1)">
              <el-image
                style="width: 185px; height: 190px"
                :src="item.img"
                fit="cover">
                <div slot="error" class="image-slot">
                  <i>暂无数据</i>
                </div>
              </el-image>
              <div class="yyjc_item_title">
                <p class="s-font-35">{{item.pos}}</p>
                <span>姓名：{{item.name}} </span><br />
                <span style="white-space: nowrap">
                  电话：{{toPhone(item.phone)}}
                  <!-- <i class="cursor phone_icon" @click="openCall(item.phone)"></i> -->
                </span>
                <span style="white-space: nowrap">
                  联系方式：
                  <span class="iconPhone" @click="openCall(item.phone)"></span>
                  <span class="iconVideo" @click="openVideo(item.phone)"></span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="hearder_h2">
        <span>配套资源</span>
        <img
          src="../../../images/common/edit.png"
          alt=""
          style="margin-left: 15px; cursor: pointer"
          @click="openManage(2)"
        />
      </div>
      <!-- 配套资源 -->
      <div class="ptzy_box box">
        <div class="ptzy_item" v-for="(item,index) in ptzyList">
          <img :src="item.icon" alt="" width="89" height="100" />
          <div style="margin-top: 20px">
            <div class="item_name">{{item.name}}</div>
            <div class="item_bottom">
              <span
                class="item_value s-font-45 xt_font"
                :class="index%3==1?'s-blue':'s-yellow'"
                >{{item.value}}</span
              >
              <span
                class="item_unit s-font-25"
                :class="index%3==1?'s-blue':'s-yellow'"
                >{{item.unit}}</span
              >
            </div>
          </div>
        </div>
      </div>
      <div class="hearder_h1 cursor" @click="changePage('zfts')">
        <span>执法态势 <i class="click-i"></i></span>
      </div>
      <div class="hearder_h2"><span>行政检查</span></div>
      <!-- 行政检查 -->
      <div class="xzjc_box box">
        <div class="xzjc_top">
          <div style="display: flex">
            <span>检查总量</span>
            <div class="count_box">
              <div
                v-for="(item,index) in jcTotal"
                :key="index"
                :class="item!='.'?'count-toNum':''"
              >
                <count-to
                  v-if="item!='.'"
                  :start-val="0"
                  :end-val="Number(item)"
                  :duration="3000"
                  class="s-c-yellow-gradient"
                >
                </count-to>
                <span v-if="item=='.'">{{item}}</span>
              </div>
            </div>
            <span>户次</span>
          </div>
          <div style="display: flex">
            <span>综合查一次</span>
            <div class="count_box">
              <div
                v-for="(item,index) in lhjcTotal"
                :key="index"
                :class="item!='.'?'count-toNum':''"
              >
                <count-to
                  v-if="item!='.'"
                  :start-val="0"
                  :end-val="Number(item)"
                  :duration="3000"
                  class="s-c-blue-gradient"
                >
                </count-to>
                <span v-if="item=='.'">{{item}}</span>
              </div>
            </div>
            <span>户次</span>
          </div>
        </div>
        <div class="xzjc_middle">
          <div v-for="(item,index) in xzjcList">
            <div>
              <span style="white-space: nowrap">{{item.label}}</span>
              <br />
              <div
                :id="'echarts'+index"
                style="width: 100%; height: 210px"
              ></div>
              <!-- <span class="s-yellow s-w7 s-font-45 xt_font">
                {{item.num}}
              </span>
              <span class="s-yellow">万次</span> -->
            </div>
            <!-- 定义修改svg -->
            <!-- <div style="width: 0px; height: 0px">
              <svg width="100%" height="100%">
                <defs>
                  <linearGradient id="write" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop
                      offset="80%"
                      style="stop-color: #0299e2"
                      stop-opacity="0.8"
                    ></stop>
                    设置起始位置的颜色
                    <stop
                      offset="100%"
                      style="stop-color: #82e402"
                      stop-opacity="0.5"
                    ></stop>
                    设置起始位置的颜色
                  </linearGradient>
                </defs>
              </svg>
            </div>
            <el-progress
              style="margin-top: 20px"
              type="circle"
              :percentage="Number(item.zb)"
              :stroke-width="10"
            ></el-progress> -->
          </div>
        </div>
      </div>
      <div class="hearder_h2"><span>行政处罚案件</span></div>
      <div class="box">
        <div style="position: absolute; right: 0; z-index: 888" v-show="year != '2023'">
          <el-date-picker
            v-model="datas"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            value-format="yyyy-MM"
            @change="(res) => xzcfaj(res,'')"
          >
          </el-date-picker>
        </div>
        <div
          v-show="chartData1.length==0"
          class="s-font-30 s-c-white s-text-center"
          style="margin-top: 200px"
        >
          暂无数据
        </div>
        <div id="chart01" style="width: 100%; height: 445px"></div>
      </div>
    </div>
    <script>
      window.parent.eventbus &&
        window.parent.eventbus.on("cityChange", (city) => {
          let filtName = (vm.city =
            city == "金义新区"
              ? "金东区"
              : city == "金华开发区"
              ? "开发区"
              : city);
          vm.initApi(filtName,localStorage.getItem("year"));
        });

      window.parent.eventbus &&
      window.parent.eventbus.on("yearChange", (year) => {
        vm.year = year
        vm.initApi(localStorage.getItem("city"),year);
      });
      var vm = new Vue({
        el: "#left",
        data: {
          year:"",
          city: "金华市",
          datas: [
            new Date().getFullYear() + "-01",
            moment(new Date()).format("YYYY-MM"),
          ],
          zbTotal: "123",
          zbList: [{ sector: "值班领导", name: "张晓军", phone: "123454677" }],
          time1: null,
          dom1: null,
          tableData: [
            {
              duties: "职务1",
              department: "部门1",
              name: "某某某",
              phone: "188****5678",
            },
            {
              duties: "职务2",
              department: "部门2",
              name: "某某某",
              phone: "188****5678",
            },
            {
              duties: "职务3",
              department: "部门3",
              name: "某某某",
              phone: "188****5678",
            },
          ],
          ptzyList: [
            {
              icon: "/static/images/xzzfj/ptzy1.png",
              name: "执法人员",
              value: 333,
              unit: "人",
            },
            {
              icon: "/static/images/xzzfj/ptzy2.png",
              name: "机动车辆",
              value: 100,
              unit: "辆",
            },
            {
              icon: "/static/images/xzzfj/ptzy3.png",
              name: "非机动车辆",
              value: 56,
              unit: "辆",
            },
            {
              icon: "/static/images/xzzfj/ptzy4.png",
              name: "执法记录仪",
              value: 4307,
              unit: "个",
            },
            {
              icon: "/static/images/xzzfj/ptzy5.png",
              name: "对讲机",
              value: 113,
              unit: "个",
            },
            {
              icon: "/static/images/xzzfj/ptzy6.png",
              name: "PDA",
              value: 98,
              unit: "个",
            },
          ],
          jcTotal: "00000",
          lhjcTotal: "00000",
          xzjcList: [],
          cfsxList: [
            {
              name: "乱扔垃圾",
              value: 18,
            },
            {
              name: "乱扔垃圾",
              value: 18,
            },
            {
              name: "乱扔垃圾",
              value: 23,
            },
            {
              name: "无照经营游商",
              value: 10,
            },
            {
              name: "乱扔垃圾",
              value: 10,
            },
            {
              name: "乱扔垃圾",
              value: 18,
            },
            {
              name: "乱扔垃圾",
              value: 18,
            },
            {
              name: "露天烧烤",
              value: 23,
            },
            {
              name: "乱扔垃圾",
              value: 10,
            },
            {
              name: "乱扔垃圾",
              value: 10,
            },
          ],
          tabList: ["按区域", "按领域"],
          value: null,
          options: [
            {
              value: "1",
              label: "月度",
            },
            {
              value: "2",
              label: "年度",
            },
          ],
          chartData1: [
            {
              name: "婺城区",
              value: 100,
            },
            {
              name: "金东区",
              value: 140,
            },
            {
              name: "兰溪市",
              value: 230,
            },
            {
              name: "东阳市",
              value: 100,
            },
            {
              name: "义乌市",
              value: 164,
            },
            {
              name: "永康市",
              value: 130,
            },
            {
              name: "武义县",
              value: 167,
            },
            {
              name: "浦江县",
              value: 147,
            },
            {
              name: "磐安县",
              value: 193,
            },
            {
              name: "开发区",
              value: 225,
            },
          ],
        },
        mounted() {
          this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
          // 表格滚动
          // this.dom1 = document.getElementById("box");
          // this.mouseleaveEvent();
        },
        methods: {
          changeCity() {return localStorage.getItem('city')},
          toPhone(phone) {
            var reg = /(\d{3})\d{4}(\d{4})/; //正则表达式
            return phone.replace(reg, "$1****$2");
          },
          subUrl(url) {
            return url.replace("8303", "8300");
          },
          initApi(city1,year) {
            if (city1=="金华开发区") {
              var city = "开发区";
            }else{
              var city = city1;
            }
            if (year && year != '') {
              if (year == new Date().getFullYear()) {
                this.datas = [new Date().getFullYear() + "-01", moment(new Date()).format("YYYY-MM")]
              } else {
                this.datas = [year + "-01", moment(`${year}-01-01`).endOf('year').format('YYYY-MM-DD')]
              }
            }
            this.xzcfaj(this.datas,year);
            $api("/csdn_yjyp13new", { area_name: city }).then((res) => {
              this.zbList = [
                { pos: "常务副指挥长", name: res[0] && res[0].zzhzxm?res[0].zzhzxm:"暂无数据", phone: res[0] && res[0].zzhzdh?res[0].zzhzdh:"暂无数据" },
                { pos: "值班领导", name: res[0] && res[0].zbldxm?res[0].zbldxm:"暂无数据", phone: res[0] && res[0].zblddh?res[0].zblddh:"暂无数据", img:res[0] && res[0].zbldtx?this.subUrl(res[0].zbldtx):"" },
                {
                  pos: "值班长",
                  name: res[0] && res[0].zbzxm?res[0].zbzxm:"暂无数据",
                  phone: res[0] && res[0].zbzdh?res[0].zbzdh:"暂无数据",
                  img:res[0] && res[0].zbztx?this.subUrl(res[0].zbztx):""
                },
              ];
            });
            $api("/xzzfj_zfts_lgzb", { qx: city }).then(res => {
              console.log(res,"xzzfj_zfts_lgzb");
              this.jcTotal = res[0].jchc;
              this.lhjcTotal = res[0].ssjjzxlhhcs;
            })
            $api("/csdn_yjyp3", { area_code: city,sjwd2: year }).then((res) => {

              // this.jcTotal = res.find((a) => a.label.includes("检查总量")).num;
              // this.lhjcTotal = res.find((a) => a.label.includes("跨部门")).num;
              this.xzjcList = [];
              res.map((a) => {
                if (!a.label.includes("占比") && !a.label.includes("检查总量") && !a.label.includes("跨部门联合检查")) {
                  this.xzjcList.push({
                    label: a.label,
                    num: a.num,
                    unit: a.unit,
                  });
                }
              });

              // res.map((a) => {
              //   if (a.label.includes("占比")) {
              //     let index = this.xzjcList.findIndex(
              //       (c) => c.label == a.label.replace("占比", "")
              //     );
              //     this.xzjcList[index].zb = a.num;
              //   }
              // });
              setTimeout(() => {
                this.xzjcList.map((a, i) => {
                  this.getliquidFill(`echarts${i}`, a);
                });
              }, 500);
            });
            $api("/csdn_yjyp12", { area_name: city }).then((res) => {
              this.ptzyList[1].value = res[0].jdcl;
              this.ptzyList[2].value = res[0].fjdcl;
              this.ptzyList[3].value = res[0].jlyzx;
              this.ptzyList[4].value = res[0].djj;
              this.ptzyList[5].value = res[0].pda;
            });
            $api("/csdn_yjyp26", { area_code: city }).then((res) => {
              this.ptzyList[0].value = res[0].tjz;
            });
          },
          //行政处罚案件
          xzcfaj(datas,year) {
            let city = localStorage.getItem("city")
            city == '金华市'?this.dataFun(datas,year):this.dataFun2(datas,city,year)
          },
          dataFun(e,year) {
            $api("/csdn_yjyp7", {
              sjwd1: e[0].replace(/-/g, ""),
              sjwd3: e[1].replace(/-/g, ""),
              sjwd2: year
            }).then((res) => {
              let legends = [];
              if (res.length > 0) {
                let citydata = [
                  { city: "婺城区" },
                  { city: "金东区" },
                  { city: "兰溪市" },
                  { city: "东阳市" },
                  { city: "义乌市" },
                  { city: "永康市" },
                  { city: "浦江县" },
                  { city: "武义县" },
                  { city: "磐安县" },
                  { city: "开发区" },
                ];
                res.map((a) => {
                  !legends.includes(a.label) && legends.push(a.label);
                });
                citydata.forEach((ele, i) => {
                  let filtArr = res.filter((a) => a.area_code == ele.city);
                  filtArr.map((el) => {
                    ele[el.label] = el.num;
                  });
                });
                this.chartData1 = citydata;
              } else {
                this.chartData1 = [];
              }
              // }
              this.getChart01("chart01", this.chartData1, legends);
            });
          },
          dataFun2(e,city,year) {
            $api("/csdn_yjyp72", {
              qxwd: city == "金华开发区"?"开发区": city,
              sjwd1: e[0].replace(/-/g, ""),
              sjwd3: e[1].replace(/-/g, ""),
              sjwd2: year
            }).then((res) => {
              let legends = [];
              if (res.length > 0) {
                let citydata = [
                  { city: "交通运输" },
                  { city: "卫生健康" },
                  { city: "市场监管" },
                  { city: "应急管理" },
                  { city: "文化市场" },
                  { city: "生态环境" },
                  { city: "自然资源" },
                  { city: "综合执法" },
                ];
                res.map((a) => {
                  !legends.includes(a.label) && legends.push(a.label);
                });
                citydata.forEach((ele, i) => {
                  let filtArr = res.filter((a) => a.ywwd2 == ele.city);
                  filtArr.map((el) => {
                    ele[el.label] = el.num;
                  });
                });
                this.chartData1 = citydata;
              } else {
                this.chartData1 = [];
              }
              // }
              this.getChart01("chart01", this.chartData1, legends);
            });
          },
          getMonthBetween(start, end) {
            //传入的格式YYYY-MM
            var result = [];
            var s = start.split("-");
            var e = end.split("-");
            var min = new Date();
            var max = new Date();
            min.setFullYear(s[0], s[1] * 1 - 1, 1); //开始日期
            max.setFullYear(e[0], e[1] * 1 - 1, 1); //结束日期
            var curr = min;
            while (curr <= max) {
              var month = curr.getMonth();
              result.push((month + 1).toString().padStart(2, "0"));
              curr.setMonth(month + 1);
            }
            return result;
          },
          /**切换页面 */
          changePage(page) {
            let pageTit =
              page == "zhdd" ? "指挥调度" : page == "zfts" ? "执法态势" : "";
            console.log(page);
            window.parent.xzzfzx.page_menu = page;
            window.parent.xzzfzx.header_title = pageTit;
            window.parent.lay.closeIframeByKeepNames([
              "tckz_tcgl3840",
              "yybzDetail",
              "zfzsDetail",
              "yybzPoint",
              "videoManage",
              "wggl3840",
              "wgsjDetail",
              "wg_map_main",
            ]);
            if (page == "zfts") {
              window.parent.mapUtil.removeAllLayers();
              window.parent.lay.closeIframeByKeepNames([]);
            }
            if (page == "zhdd") {
              window.parent.lay.openIframe({
                type: "openIframe",
                name: `${page}_left`,
                id: `${page}_left`,
                src: baseURL.url + `/static/citybrain/${page}/zhdd_page/${page}_left.html`,
                width: "1030px",
                height: "1900px",
                left: "20px",
                top: "calc(50% - 870px)",
                zIndex: "666",
              });
              window.parent.lay.openIframe({
                type: "openIframe",
                name: `${page}_right`,
                id: `${page}_right`,
                src: baseURL.url + `/static/citybrain/${page}/zhdd_page/${page}_right.html`,
                width: "1030px",
                height: "1900px",
                right: "20px",
                top: "calc(50% - 870px)",
                zIndex: "666",
              });
            } else {
              window.parent.lay.openIframe({
                type: "openIframe",
                name: `${page}_left`,
                id: `${page}_left`,
                src: baseURL.url + `/static/citybrain/${page}/${page}_left.html`,
                width: "1030px",
                height: "1900px",
                left: "20px",
                top: "calc(50% - 870px)",
                zIndex: "666",
              });
              window.parent.lay.openIframe({
                type: "openIframe",
                name: `${page}_right`,
                id: `${page}_right`,
                src: baseURL.url + `/static/citybrain/${page}/${page}_right.html`,
                width: "1030px",
                height: "1900px",
                right: "20px",
                top: "calc(50% - 870px)",
                zIndex: "666",
              });
            }
          },
          openCall(phones) {
            if (phones != "") {
              // window.parent.lay.openIframe({
              //   type: "openIframe",
              //   name: "zbPhone",
              //   id: "zbPhone",
              //   src: "/static/citybrain/zhdd/zhdd_page/zhtx_callPhone/zbPhone.html",
              //   left: "1200px",
              //   top: "469px",
              //   width: "1500px",
              //   height: "920px",
              //   zIndex: "666",
              //   argument: { phone: phones },
              // });

              window.parent.lay.openIframe({
                type: "openIframe",
                name: "CallPhone",
                id: "CallPhone",
                src: baseURL.url + "/static/citybrain/commonts/CallPhone/CallPhone.html",
                left: "1200px",
                top: "575px",
                width: "1515px",
                height: "866px",
                zIndex: "666",
                argument: { phone: phones },
              });
            }
          },
          openVideo(phones) {
            if (phones != "") {
              // window.parent.lay.openIframe({
              //   type: "openIframe",
              //   name: "zbVideo",
              //   id: "zbVideo",
              //   src: "/static/citybrain/zhdd/zhdd_page/zhtx_callPhone/zbVideo.html",
              //   left: "500px",
              //   top: "100px",
              //   width: "3250px",
              //   height: "1920px",
              //   zIndex: "666",
              //   argument: { phone: phones },
              // });

              window.parent.lay.openIframe({
                type: "openIframe",
                name: "CallVideo",
                id: "CallVideo",
                src: baseURL.url + "/static/citybrain/commonts/CallVideo/CallVideo.html",
                left: "0px",
                top: "0px",
                width: "3840px",
                height: "2160px",
                zIndex: "666",
                argument: { phone: phones },
              });
            }
          },
          mouseenterEvent() {
            clearInterval(this.time1);
          },
          mouseleaveEvent() {
            this.time1 = setInterval(() => {
              // this.dom1.scrollTop += 1.5
              this.dom1.scrollBy({
                top: 78,
                behavior: "smooth",
              });
              if (
                this.dom1.scrollTop >=
                this.dom1.scrollHeight - this.dom1.offsetHeight
              ) {
                this.dom1.scrollTop = 0;
              }
            }, 1500);
          },
          getChart01(id, chartData, legends) {
            echarts.init(document.getElementById(id)).dispose();
            let myEc = echarts.init(document.getElementById(id));
            console.log(chartData);
            let xdata = [];
            let ydata = [[], []];
            chartData.forEach((item) => {
              xdata.push(item.city);
              ydata[0].push(item.一般程序案件数量 || 0);
              ydata[1].push(item.简易程序案件数量 || 0);
              // ydata[2].push(item.行政处罚案件数量 || 0);
            });
            let legend = legends;
            let color = ["245,102,121", "172,171,52", "76,152,251"];
            let seriesData = legends.map((ele, index) => {
              return {
                name: ele,
                type: "bar",
                barWidth: "25%",
                stack: "总量",
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: "rgba(" + color[index] + ",0.99)",
                      },
                      {
                        offset: 1,
                        color: "rgba(" + color[index] + ",0)",
                      },
                    ]),
                    barBorderRadius: 4,
                  },
                },
                data: ydata[index],
                label: {
                  show: index == legends.length - 1?true:false,
                  position: 'top', // 将标签的位置设置为顶部
                  formatter: function (params) {
                    // 计算当前x轴所有系列的累计值
                    let sum = 0;
                    for (let i = 0; i < legends.length; i++) {
                      sum += option.series[i].data[params.dataIndex] || 0;
                    }
                    return sum;
                  },
                  textStyle: {
                    fontSize: 18,      // 字体大小
                    fontWeight: 'bold',// 加粗
                    color: 'deepSkyblue'      // 字体颜色
                  }
                }
              };
            });
            option = {
              tooltip: {
                trigger: "axis",
                backgroundColor: "rgba(51, 51, 51, 0.7)",
                borderWidth: 0,
                axisPointer: {
                  type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                },
                textStyle: {
                  color: "white",
                  fontSize: "24",
                },
                formatter: function (params) {
                  let total = params[0].data + params[1].data;
                  let yb =
                    total == 0
                      ? "100"
                      : ((params[0].data * 100) / total).toFixed(1);
                  let jy =
                    total == 0
                      ? "100"
                      : ((params[1].data * 100) / total).toFixed(1);
                  return (
                    params[0].name +
                    " ：<br />" +
                    "行政处罚案件数量：" +
                    total +
                    "件<br />" +
                    params[0].seriesName +
                    " ：" +
                    params[0].data +
                    "件<br />" +
                    params[1].seriesName +
                    " ：" +
                    params[1].data +
                    "件<br />" +
                    "一般程序案件占比：" +
                    yb +
                    "%<br />" +
                    "简易程序案件占比：" +
                    jy +
                    "%"
                  );
                },
              },
              grid: {
                left: "5%",
                right: "5%",
                top: "28%",
                bottom: "5%",
                containLabel: true,
              },
              legend: {
                data: legends,
                top: 60,
                left: "center",
                itemWidth: 16,
                itemHeight: 16,
                itemGap: 50,
                textStyle: {
                  fontSize: 24,
                  color: "#D6E7F9",
                  padding: [3, 0, 0, 0],
                },
              },
              xAxis: [
                {
                  type: "category",
                  data: xdata,
                  axisLine: {
                    lineStyle: {
                      color: "rgb(119,179,241,.4)", // 颜色
                      width: 1, // 粗细
                    },
                  },
                  axisTick: {
                    show: false,
                  },
                  axisLabel: {
                    interval: 0,
                    textStyle: {
                      color: "#D6E7F9",
                      fontSize: 24,
                    },
                  },
                },
                {
                  type: "category",
                  axisLine: {
                    show: false,
                  },
                  axisTick: {
                    show: false,
                  },
                  axisLabel: {
                    show: false,
                  },
                  splitArea: {
                    show: false,
                  },
                  splitLine: {
                    show: false,
                  },
                  data: xdata,
                },
              ],
              yAxis: [
                {
                  name: "",
                  type: "value",
                  nameTextStyle: {
                    fontSize: 24,
                    color: "#D6E7F9",
                    padding: 5,
                  },
                  splitLine: {
                    lineStyle: {
                      color: "rgb(119,179,241,.4)",
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 24,
                      color: "#D6E7F9",
                    },
                  },
                },
              ],
              series: seriesData,
            };
            myEc.setOption(option);
            myEc.getZr().on("mousemove", (param) => {
              myEc.getZr().setCursorStyle("default");
            });
          },
          getliquidFill(id, chartData) {
            echarts.init(document.getElementById(id)).dispose();
            let myEc = echarts.init(document.getElementById(id));
            var arr = ["middleLost", 0.6, "今日完成进度"];
            option = {
              title: {
                top: "45%",
                left: "center",
                text: "",
                textStyle: {
                  color: "#fff",
                  fontStyle: "normal",
                  fontWeight: "normal",
                  fontSize: 28,
                },
                subtext: chartData.num + chartData.unit,
                subtextStyle: {
                  color: "#fff",
                  fontSize: 34,
                },
              },
              tooltip: {
                trigger: "item",
                backgroundColor: "rgba(51, 51, 51, 0.7)",
                borderWidth: 0,
                axisPointer: {
                  type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                },
                textStyle: {
                  color: "white",
                  fontSize: "24",
                },
                formatter: function (res) {
                  if (res.componentSubType == "liquidFill") {
                    return res.seriesName + ": " + res.data.value + "次";
                  }
                },
              },
              series: [
                {
                  type: "liquidFill",
                  itemStyle: {
                    normal: {
                      opacity: 0.4,
                      shadowBlur: 0,
                      shadowColor: "#215091",
                    },
                  },
                  name: chartData.label,
                  data: [
                    {
                      value: chartData.num / 10,
                      itemStyle: {
                        normal: {
                          color: "#14a4ff",
                          opacity: 0.6,
                        },
                      },
                    },
                  ],
                  color: ["#14a4ff"],
                  center: ["48%", "60%"],
                  radius: "75%",
                  backgroundStyle: {
                    color: "#22508f87",
                  },
                  label: {
                    normal: {
                      formatter: "",
                      textStyle: {
                        fontSize: 24,
                      },
                    },
                  },
                  outline: {
                    itemStyle: {
                      borderColor: "#215091",
                      borderWidth: 0,
                    },
                    borderDistance: 0,
                  },
                },
              ],
            };
            myEc.setOption(option);
            myEc.getZr().on("mousemove", (param) => {
              myEc.getZr().setCursorStyle("default");
            });
          },
          openManage(type) {
            type == 1
              ? window.open("https://csdn.dsjj.jinhua.gov.cn:8303/dutyToday")
              : window.open("https://csdn.dsjj.jinhua.gov.cn:8303/lawEquip");
          },
        },
      });
    </script>
  </body>
</html>
<style>
  .box {
    padding: 20px 40px;
    box-sizing: border-box;
    position: relative;
  }
  .zhtx_box {
    padding: 10px 40px;
  }
  /* 滚动文字 */
  .count_box {
    display: flex;
    margin: 0 2px;
  }

  .count-toNum {
    font-weight: 700;
    width: 40px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    font-size: 43px;
    background: url("/static/images/xzzfj/sz-bg.png");
    background-size: 100% 100%;
  }

  /* 值班 */
  .el-carousel__container {
    margin-top: 35px;
    margin-bottom: 20px;
    height: 180px !important;
  }

  .iconPhone {
    cursor: pointer;
    background: #51c422;
    padding: 5px 7px 0px 7px;
    border-radius: 4px;
    height: 40px;
    width: 34px;
    line-height: 50px;
  }
  .iconPhone::after {
    content: "";
    display: inline-block;
    width: 32px;
    height: 32px;
    background: url(/static/images/zhdd/icon_call.png) no-repeat;
    background-size: cover;
  }
  .iconVideo {
    cursor: pointer;
    background: #22a0c4;
    padding: 5px 7px 0px 7px;
    line-height: 45px;
    border-radius: 4px;
    height: 40px;
    width: 34px;
    line-height: 50px;
  }
  .iconVideo::after {
    content: "";
    display: inline-block;
    width: 32px;
    height: 32px;
    background: url(/static/images/zhdd/video.png) no-repeat;
    background-size: cover;
  }
  .phone_icon {
    width: 27px;
    height: 27px;
    display: inline-block;
    background: url("/static/images/zhdd/dianhua.png") no-repeat;
  }

  .yyjc_item_title {
    width: 100%;
    line-height: 52px;
    width: 100%;
    background: url("/static/images/zhdd/zb_bg.png") no-repeat;
    background-position: 0 24px;
    background-size: 100%;
  }

  .yyjc_item_title > p {
    padding-left: 15px;
    font-style: italic;
    font-weight: 700;
    background: linear-gradient(to bottom, #7cb2ff, #ffffff);
    -webkit-background-clip: text;
    color: transparent;
  }

  .yyjc_item_title > span {
    font-size: 30px;
    color: #eeeaea;
    letter-spacing: -1px;
    margin-left: 10px;
    line-height: 62px;
  }

  .el-carousel__arrow {
    background-color: rgba(31, 45, 61, 0) !important;
    font-size: 30px !important;
  }
  .ptzy_box {
    width: 100%;
    height: 270px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 20px 70px;
  }

  .ptzy_item {
    display: flex;
    width: 30%;
    margin-bottom: 10px;
  }

  .item_name {
    width: 200px;
    height: 35px;
    font-size: 28px;
    line-height: 35px;
    color: #d1d6df;
  }
  .xt_font {
    font-family: DINCondensed;
    font-style: italic;
  }
  .s-yellow {
    color: #eed252;
  }

  .s-blue {
    color: #34dfe3;
  }

  .xzjc_top {
    white-space: nowrap;
    width: 100%;
    display: flex;
    justify-content: space-around;
    font-size: 30px;
    color: #ffffff;
    line-height: 60px;
  }

  .count_box {
    display: flex;
    margin: 0 3px;
  }

  .count-toNum {
    width: 40px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    font-size: 43px;
    background: url("/static/images/xzzfj/sz-bg.png");
    background-size: 100% 100%;
  }

  .xzjc_middle {
    display: flex;
    justify-content: space-evenly;
    text-align: center;
    font-size: 30px;
    color: #ffffff;
    margin-top: 25px;
  }

  .xzjc_middle > div {
    flex: 1;
    background: url(/static/images/xzzfj/percent_bg.png) no-repeat 0px 72px;
    background-size: 95%;
  }
  .cfsx_box {
    width: 100%;
    height: 490px;
    background: url("/static/images/xzzfj/cfsx_bg.png") no-repeat 188px -44px;
    position: relative;
  }
  .cfsx_item {
    color: #ffffff;
    font-size: 28px;
    display: inline-block;
    padding: 0 15px;
    box-sizing: border-box;
    position: absolute;
  }
  .bg1 {
    height: 48px;
    line-height: 48px;
    background: linear-gradient(
      -90deg,
      rgba(192, 196, 204, 0.02),
      rgba(192, 196, 204, 0.12),
      rgba(192, 196, 204, 0.02)
    );
  }
  .bg2 {
    height: 48px;
    line-height: 48px;
    background: linear-gradient(
      -90deg,
      rgba(40, 124, 232, 0.02),
      rgba(40, 124, 232, 0.51),
      rgba(40, 124, 232, 0.02)
    );
  }
  .bg3 {
    height: 48px;
    line-height: 48px;
    background: linear-gradient(
      -90deg,
      rgba(65, 228, 222, 0.02),
      rgba(65, 228, 222, 0.71),
      rgba(65, 228, 222, 0.02)
    );
  }
  .cfsx_item:nth-child(1) {
    left: 290px;
    top: 30px;
  }
  .cfsx_item:nth-child(2) {
    left: 213px;
    top: 110px;
  }
  .cfsx_item:nth-child(3) {
    left: 185px;
    top: 190px;
  }
  .cfsx_item:nth-child(4) {
    left: 225px;
    top: 270px;
  }
  .cfsx_item:nth-child(5) {
    left: 260px;
    top: 350px;
  }
  .cfsx_item:nth-child(6) {
    right: 260px;
    top: 360px;
  }
  .cfsx_item:nth-child(7) {
    right: 225px;
    top: 280px;
  }
  .cfsx_item:nth-child(8) {
    right: 185px;
    top: 200px;
  }
  .cfsx_item:nth-child(9) {
    right: 213px;
    top: 120px;
  }
  .cfsx_item:nth-child(10) {
    right: 290px;
    top: 40px;
  }

  /* element */
  .el-progress path:first-child {
    stroke: #507190;
  }

  .el-progress__text {
    color: #00c7fd;
    font-size: 35px !important;
  }

  svg > path:nth-child(2) {
    stroke: url(#write);
  }

  .xzcf-select {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
  }
  /* 表格 */
  .table {
    width: 100%;
    height: 315px;
    padding: 10px;
    box-sizing: border-box;
    overflow-y: auto;
  }

  .table .th {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-style: italic;
    font-weight: 700;
    font-size: 28px;
    line-height: 60px;
    color: #ffffff;
  }

  .table .th_td {
    letter-spacing: 0px;
    text-align: center;
  }

  .table .tbody {
    width: 100%;
    height: calc(100% - 60px);
    overflow: hidden;
  }

  .table .tbody:hover {
    overflow-y: auto;
  }

  .table .tbody::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  .table .tbody::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: #20aeff;
    height: 8px;
  }

  .table .tr {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 78px;
    line-height: 78px;
    font-size: 28px;
    color: #ffffff;
    cursor: pointer;
    border-top: 1px solid #959aa1;
    box-sizing: border-box;
  }

  .table .tr:nth-child(2n) {
    background-color: rgba(50, 134, 248, 0.2);
  }

  .table .tr:nth-child(2n + 1) {
    background-color: rgba(50, 134, 248, 0.1);
  }

  .table .tr:hover {
    background-color: #6990b6;
  }

  .table .tr_td {
    letter-spacing: 0px;
    text-align: center;
    box-sizing: border-box;
  }

  .table .tr_td > img {
    position: relative;
    top: 25px;
  }
  /*  */
  .el-scrollbar {
    overflow: hidden !important;
  }
  .el-scrollbar__wrap {
    overflow: hidden !important;
    margin-bottom: 0px !important;
    margin-right: 0px !important;
  }
  .el-input {
    width: 200px;
    font-size: 30px;
  }

  .el-input__inner {
    height: 60px;
    background-color: #132c4e;
    border: 1px solid #afdcfb;
    color: #fff;
    border-radius: 15px;
  }

  .el-select-dropdown__item {
    font-size: 30px;
    height: 50px;
    color: #cfcfd6 !important;
    line-height: 50px;
  }

  .el-select-dropdown {
    background-color: #132c4e;
    border: 1px solid #afdcfb;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #27508f !important;
  }
  .el-carousel__button {
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }
  .el-carousel__arrow {
    width: 50px;
    height: 50px;
    font-size: 30px;
    background-color: rgba(31, 45, 61, 0.5);
  }
  .el-picker-panel__icon-btn {
    color: #fff !important;
  }
</style>
