<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
        />
        <meta http-equiv="X-UA-Compatible" content="ie=edge" />
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <script src="/Vue/vue.js"></script>
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <script src="/elementui/js/index.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script type="module" src="https://csdn.dsjj.jinhua.gov.cn:8101/static/js/jslib/gisHelper.js"></script>
    </head>

    <body>
        <div id="video_app">
            <div class="s-flex v-content" style="position: absolute;left: 0">
                <div class="video_box">
                    <div class="s-flex s-m-b-10">
                        <el-input
                            placeholder="请输入标签名称.."
                            v-model="filterText"
                            @keyup.enter.native="onSearchLabel"
                        ></el-input>
                        <el-button class="s-m-r-5" @click="resetLeft">重置</el-button>
                    </div>
                    <div style="overflow-y: scroll; height: 1050px">
                        <el-tree
                            v-show="showLeft"
                            class="filter-tree"
                            :filter-node-method="filterNode"
                            :load="loadNode"
                            :props="defaultProps1"
                            node-key="id"
                            ref="tree"
                            @node-click="lableClick1"
                            lazy
                        >
                            <span class="filter-tree" slot-scope="{ node, data }">
                                <i
                                    v-if="data.parentCode === 0 "
                                    :class="data.icon"
                                    class="s-m-l-25"
                                    style="font-size: 26px"
                                ></i>
                                <svg v-else-if="data.parentCode !== 0 " class="icon s-m-l-25" aria-hidden="true">
                                    <use xlink:href="#icon-dian"></use>
                                </svg>
                                <span
                                    :class="[node.level!=1?'v-text':'','s-m-l-25']"
                                    style="font-size: 30px; line-height: 50px"
                                    :title="node.label"
                                >
                                    {{ node.label }}
                                </span>
                            </span>
                        </el-tree>
                        <el-tree
                            v-show="!showLeft"
                            :data="options"
                            class="filter-tree"
                            :props="defaultProps"
                            node-key="id"
                            ref="tree"
                            :filter-node-method="filterNode"
                            @node-click="lableClick1"
                        >
                            <span class="filter-tree" slot-scope="{ node, data }">
                                <i
                                    v-if="data.parentCode === 0 "
                                    :class="data.icon"
                                    class="s-m-l-25"
                                    style="font-size: 26px"
                                ></i>
                                <svg v-else-if="data.parentCode !== 0 " class="icon s-m-l-25" aria-hidden="true">
                                    <use xlink:href="#icon-dian"></use>
                                </svg>
                                <span class="s-m-l-25" style="font-size: 30px; line-height: 50px"
                                    >{{ node.label }}</span
                                >
                            </span>
                        </el-tree>
                    </div>
                </div>
                <!-- 有区划的三四级 -->
                <div class="video_box video_box_l2">
                    <div class="s-flex s-m-b-10" style="width: 380px">
                        <el-input
                            placeholder="请输入搜索内容.."
                            v-model="videoSearchText"
                            @keyup.enter.native="onSearchVideo"
                        ></el-input>
                        <el-button class="s-m-r-5" @click="reset">重置</el-button>
                    </div>
                    <div style="overflow-y: scroll; height: 1050px">
                        <el-tree
                            :data="L2options"
                            class="filter-tree"
                            :props="L2defaultProps"
                            :default-expand-all="expandAll"
                            node-key="id"
                            ref="L2tree"
                            :filter-node-method="L2filterNode"
                            @node-click="lableClick2"
                        >
                            <span class="filter-tree" slot-scope="{ node, data }">
                                <i
                                    v-if="data.parentCode === 0 "
                                    :class="data.icon"
                                    class="s-m-l-25"
                                    style="font-size: 26px"
                                ></i>
                                <svg v-else-if="data.parentCode !== 0 " class="icon s-m-l-25" aria-hidden="true">
                                    <use xlink:href="#icon-dian"></use>
                                </svg>
                                <span
                                    :class="[data.level == 4 ? 'w-60':data.level!=1?'v-text':'','s-m-l-25']"
                                    style="font-size: 30px; line-height: 50px"
                                    :title="node.label"
                                    @dblclick="dblClickItem(node)"
                                >
                                    {{ node.label }}
                                </span>
                                <span v-loading="loading && node.label==currentNode"></span>
                                <span v-show="data.num && data.num!=0" style="font-size: 30px; line-height: 50px">
                                    ({{ data.num }})
                                </span>
                            </span>
                        </el-tree>
                    </div>
                </div>
            </div>
        </div>
        <script>
            var vm = new Vue({
                el: "#video_app",
                data: {
                    loading: false,
                    currentNode: "",
                    showLeft: true,
                    show: false,
                    level2: false,
                    L2TreeMap: null,
                    expandAll: false,
                    filterText: "",
                    options: [],
                    defaultProps1: {
                        label: "label",
                        children: "path",
                        isLeaf: "leaf",
                    },
                    defaultProps: {
                        children: "children",
                        label: "label",
                    },
                    L2filterText: "",
                    videoSearchText: "",
                    L2options: [],
                    L2optionsArr: [
                        {
                            label: "婺城区",
                            parentCode: 0,
                            icon: "el-icon-edit",
                            level: 1,
                            num: 0,
                            children: [],
                        },
                        {
                            label: "金义新区",
                            parentCode: 0,
                            icon: "el-icon-edit",
                            level: 1,
                            num: 0,
                            children: [],
                        },
                        {
                            label: "东阳市",
                            parentCode: 0,
                            icon: "el-icon-edit",
                            level: 1,
                            num: 0,
                            children: [],
                        },
                        {
                            label: "义乌市",
                            parentCode: 0,
                            icon: "el-icon-edit",
                            level: 1,
                            num: 0,
                            children: [],
                        },
                        {
                            label: "永康市",
                            parentCode: 0,
                            icon: "el-icon-edit",
                            level: 1,
                            num: 0,
                            children: [],
                        },
                        {
                            label: "兰溪市",
                            parentCode: 0,
                            icon: "el-icon-edit",
                            level: 1,
                            num: 0,
                            children: [],
                        },
                        {
                            label: "浦江县",
                            parentCode: 0,
                            icon: "el-icon-edit",
                            level: 1,
                            num: 0,
                            children: [],
                        },
                        {
                            label: "武义县",
                            parentCode: 0,
                            icon: "el-icon-edit",
                            level: 1,
                            num: 0,
                            children: [],
                        },
                        {
                            label: "磐安县",
                            parentCode: 0,
                            icon: "el-icon-edit",
                            level: 1,
                            num: 0,
                            children: [],
                        },
                        {
                            label: "开发区",
                            parentCode: 0,
                            icon: "el-icon-edit",
                            level: 1,
                            num: 0,
                            children: [],
                        },
                    ],
                    L2defaultProps: {
                        children: "children",
                        label: "label",
                        letter: "letter",
                    },
                    radio: "",
                    letters: [
                        "A",
                        "B",
                        "C",
                        "D",
                        "E",
                        "F",
                        "G",
                        "H",
                        "I",
                        "J",
                        "K",
                        "L",
                        "M",
                        "N",
                        "O",
                        "P",
                        "Q",
                        "R",
                        "S",
                        "T",
                        "U",
                        "V",
                        "W",
                        "X",
                        "Y",
                        "Z",
                    ],
                    letter: "",
                    oneWords: [], //存放一级菜单
                    nowClickData: null,
                    nowClickData1: null,
                    cameraList: [
                        {
                            name: "枪机在线",
                            code: "camera-zx-qiangji",
                        },
                        {
                            name: "枪机离线",
                            code: "camera-lx-qiangji",
                        },
                        {
                            name: "球机在线",
                            code: "camera-zx-qiuji",
                        },
                        {
                            name: "球机离线",
                            code: "camera-lx-qiuji",
                        },
                        {
                            name: "半球机在线",
                            code: "camera-zx-banqiu",
                        },
                        {
                            name: "半球机离线",
                            code: "camera-lx-banqiu",
                        },
                        {
                            name: "高点在线",
                            code: "camera-zx-gaodian",
                        },
                        {
                            name: "高点离线",
                            code: "camera-lx-gaodian",
                        },
                    ],
                },
                watch: {
                    // filterText(val) {
                    //   console.log(val)
                    //   this.$refs.tree.filter(val)
                    // },
                    L2filterText(val) {
                        this.$refs.L2tree.filter(val);
                    },
                    letter(val) {
                        this.$refs.L2tree.filter("");
                    },
                    // videoSearchText(val) {
                    //   this.$refs.L2tree.filter(val)
                    // },
                },
                mounted: function () {},
                methods: {
                    onclick(e, list) {
                      console.log(e,list)
                        if (e.status == 0) {
                            this.$message("设备离线");
                            return;
                        }
                        // 在视频点击的时候去除缩略图
                        window.parent.mapUtil.removeLayer("mouseente");
                        window.parent.mapUtil.removeLayer("mouseente2");
                        window.parent.mapUtil.flyTo({
                            destination: [e.esX, e.esY],
                            // zoom: 15,
                            offset: [0, -666],
                        });
                        // if (e.pointId == 'camera-load-index13840_1' || e.pointId == 'camera-load-index13840_2') {
                        if (e.data.hls_url) {
                            let item = {
                                obj: {
                                    chn_url: e.data.hls_url,
                                    chn_name: e.data.mp_name,
                                },
                                data: e.data,
                            };
                            if (
                                window.location.ancestorOrigins.length != 0 &&
                                (JSON.stringify(window.location.ancestorOrigins).includes(
                                    "https://csdn.dsjj.jinhua.gov.cn:8102"
                                ) ||
                                    JSON.stringify(window.location.ancestorOrigins).includes(
                                        "https://csdn.dsjj.jinhua.gov.cn:9601"
                                    ))
                            ) {
                                let url = "",
                                    left = "0";
                                if (
                                    JSON.stringify(window.location.ancestorOrigins).includes(
                                        "https://csdn.dsjj.jinhua.gov.cn:8102"
                                    )
                                ) {
                                    url =
                                        "https://csdn.dsjj.jinhua.gov.cn:8102/static/citybrain/tckz/commont/video_main_code.html";
                                } else if (
                                    JSON.stringify(window.location.ancestorOrigins).includes(
                                        "https://csdn.dsjj.jinhua.gov.cn:9601"
                                    )
                                ) {
                                    left = "1920px";
                                    url =
                                        "https://csdn.dsjj.jinhua.gov.cn:9601/static/citybrain/csrk_3840/tckz/commont/video_main_code.html";
                                }
                                top.postMessage(
                                    JSON.stringify({
                                        type: "openIframe",
                                        name: "video_main_code",
                                        src: url,
                                        width: "3840px",
                                        height: "2160px",
                                        left: left,
                                        top: "0",
                                        zIndex: "1000",
                                        argument: item,
                                    }),
                                    "*"
                                );
                            } else {
                                let iframe1 = {
                                    type: "openIframe",
                                    name: "video_main_code",
                                    src: baseURL.url + "/static/citybrain/tcgl/commont/video_main_code.html",
                                    width: "3840px",
                                    height: "2160px",
                                    left: "0",
                                    top: "0",
                                    zIndex: "1000",
                                    argument: item,
                                };
                                window.parent.postMessage(JSON.stringify(iframe1), "*");
                            }
                        } else {
                            let item = {
                                obj: {
                                    is_collection: e.data.is_collection,
                                    // chn_name: e.data.chn_name || e.data.label,
                                    chn_name: e.data.video_name || e.data.label,
                                    highAltitude_type: e.data.highAltitude_type,
                                    pointList: list,
                                },
                                video_code: e.data.chn_code,
                            };
                            if (e.data.highAltitude_type == 3) {
                                axios({
                                    method: "get",
                                    url: baseURL.admApi + "/mis/system/videos/webPlay/" + e.data.chn_code,
                                    headers: {
                                        "Content-Type": "application/json;charset=UTF-8",
                                        Authorization: sessionStorage.getItem("Authorization"),
                                        ptid: "PT0001",
                                    },
                                }).then((res) => {
                                    // console.log(res.data.data)
                                    let num = 1200;
                                    let higth = 800;
                                    let moveLeft = (3840 - num) / 2;
                                    let moveHigth = (2160 - higth) / 2;
                                    window.open(
                                        res.data.data,
                                        "婺城区",
                                        "directories=no, location=no, toolbar=no,scrollbars=yes, resizable=yes, height=" +
                                            higth +
                                            ", width=" +
                                            num +
                                            ", top=" +
                                            moveHigth +
                                            ", left=" +
                                            moveLeft +
                                            ""
                                    );
                                });
                            } else {
                                if (
                                    window.location.ancestorOrigins.length != 0 &&
                                    (JSON.stringify(window.location.ancestorOrigins).includes(
                                        "https://csdn.dsjj.jinhua.gov.cn:8102"
                                    ) ||
                                        JSON.stringify(window.location.ancestorOrigins).includes(
                                            "https://csdn.dsjj.jinhua.gov.cn:9601"
                                        ))
                                ) {
                                    let url = "",
                                        left = "0";
                                    if (
                                        JSON.stringify(window.location.ancestorOrigins).includes(
                                            "https://csdn.dsjj.jinhua.gov.cn:8102"
                                        )
                                    ) {
                                        url =
                                            "https://csdn.dsjj.jinhua.gov.cn:8102/static/citybrain/tckz/commont/video_main_code.html";
                                    } else if (
                                        JSON.stringify(window.location.ancestorOrigins).includes(
                                            "https://csdn.dsjj.jinhua.gov.cn:9601"
                                        )
                                    ) {
                                        left = "1920px";
                                        url =
                                            "https://csdn.dsjj.jinhua.gov.cn:9601/static/citybrain/csrk_3840/tckz/commont/video_main_code.html";
                                    }
                                    top.postMessage(
                                        JSON.stringify({
                                            type: "openIframe",
                                            name: "video_main_code",
                                            src: url,
                                            width: "3840px",
                                            height: "2160px",
                                            left: left,
                                            top: "0",
                                            zIndex: "1000",
                                            argument: item,
                                        }),
                                        "*"
                                    );
                                } else {
                                    let iframe1 = {
                                        type: "openIframe",
                                        name: "video_main_code",
                                        src: baseURL.url + "/static/citybrain/tcgl/commont/video_main_code.html",
                                        width: "3840px",
                                        height: "2160px",
                                        left: "0",
                                        top: "0",
                                        zIndex: "1000",
                                        argument: item,
                                    };
                                    window.parent.lay.openIframe(iframe1);
                                }
                            }
                        }
                        // }
                    },
                    onblur(e) {
                        //onblur
                        let info = e.data;
                        let str = "";
                        if (e.status == 0) {
                            str = `<div onclick=" this.style.display = 'none'"
                        style="
                          width: 300px;
                          position: absolute;
                          border-radius: 5px;
                          background-color: rgba(6, 26, 48, 1);
                          box-shadow: inset 0 0 20px 0 #00bcfa;
                          z-index: 999999;
                          padding: 24px;">
                          <div
                            style="
                              width: 30px;
                              height: 30px;
                              position: absolute;
                              bottom: -29px;
                              left: 100px;
                              background: rgb(47 92 143);
                              clip-path: polygon(100% 0, 0 0, 25% 100%);"
                          ></div>
                    <div class="container1" style="font-size: 30px;color: white;text-align: center;">
                      设备离线中...
                    </div>
                  </div>`;
                            let objData = {
                                layerid: "mouseente",
                                position: [e.lng, e.lat],
                                content: str,
                                offset: [50, -150],
                            };
                            window.parent.mapUtil._createPopup(objData);
                        } else {
                            $api("xxwh_bqcx_name", { chnCode: info.chn_code }).then((res) => {
                                $api("xxwh_dwzl_video_path", { chnCode: info.chn_code }).then((el) => {
                                    let url = "";
                                    let lable = "";
                                    let des = "";

                                    if (el[0] && el[0].path != null) {
                                        url = baseURL.url + "/imgPath/" + el[0].path.split("fileServer/")[1];
                                    } else {
                                        url = "/static/citybrain/tckz/img/video/404.png";
                                    }
                                    if (res[0]) {
                                        let labelName = res[0].lableName.split(",").slice(0, 3);
                                        let description = res[0].description.split("，");
                                        labelName.forEach((item) => {
                                            lable += `<div style="color: #dbdee2;
                                            font-size: 28px;
                                            height: 40px;
                                            line-height: 40px;
                                            padding: 0 20px;
                                            box-sizing: border-box;
                                            border-radius: 10px;
                                            margin-right: 10px;
                                            margin-bottom: 10px;
                                            background-color: #393967;">${item}
                              </div>`;
                                        });
                                        description.forEach((item) => {
                                            des += `<div style="color: #dbdee2;
                                            font-size: 28px;
                                            height: 40px;
                                            line-height: 40px;
                                            padding: 0 20px;
                                            box-sizing: border-box;
                                            border-radius: 10px;
                                            margin-right: 10px;
                                            margin-bottom: 10px;
                                            background-color: #393967;">${item}
                              </div>`;
                                        });
                                    } else {
                                        lable = `<div style="color: #dbdee2;
                                            font-size: 28px;
                                            height: 40px;
                                            line-height: 40px;
                                            padding: 0 20px;
                                            box-sizing: border-box;
                                            border-radius: 10px;
                                            margin-right: 10px;
                                            margin-bottom: 10px;">暂无
                              </div>`;
                                    }

                                    str = `<div onclick=" this.style.display = 'none'"
                          style="
                            width: 800px;
                            position: absolute;
                            border-radius: 5px;
                            background-color: rgba(10, 31, 53, 0.8);
                            z-index: 999999;
                            box-shadow: inset 0 0 40px 0 #5ba3fa;
                            padding: 24px;">
                          <div style="
                            width: 30px;
                            height: 30px;
                            position: absolute;
                            top: -29px;
                            left: 100px;
                            background: rgb(47 92 143);
                            clip-path: polygon(25% 0, 0 100%, 100% 100%);"></div>
                      <div class="container1">
                        <div style="display:flex;justify-content: space-between;">
                          <p title='${
                              info.video_name
                          }' style='height: 30px;line-height: 30px;color: #fff;font-size: 30px;
                                  white-space: nowrap;overflow: hidden;text-overflow: ellipsis;'>${info.video_name}</p>
                          <img style='width:30px;height:30px;'
                              src="${
                                  info.is_collection == 0
                                      ? "/static/citybrain/tckz/img/not-col.png"
                                      : "/static/citybrain/tckz/img/col.png"
                              }">
                        </div>
                        <div style="width:100%;display:flex;flex-wrap: wrap;margin-top: 10px;">
                          <span style="font-size:30px;color:#fff;line-height:40px;">标签：</span>
                          ${lable}
                        </div>
                        <div style="width:100%;display:flex;flex-wrap: wrap;margin-top: 0px;">
                          <span style="font-size:30px;color:#fff;line-height:40px;">视频内容：</span>
                          ${des}
                        </div>
                        <img src="${url}" alt="" style='width:100%;height:400px;margin-top: 10px;'>
                      </div>
                    </div>`;
                                    let objData = {
                                        layerid: "mouseente2",
                                        position: [e.lng, e.lat],
                                        content: str,
                                        offset: [50, 100],
                                    };
                                    window.parent.mapUtil._createPopup(objData);
                                });
                            });
                        }
                    },
                    loadNode(node, resolve) {
                        if (node.level == 0) {
                            $api("/xxwh_bqcx_new", { parentCode: 0 }).then((res) => {
                                let allArr = res.map((arr) => {
                                    return {
                                        value: arr.lableNumber + "",
                                        label: arr.lableName,
                                        parentCode: arr.parentCode,
                                        lableCode: arr.lableCode,
                                        lableNumber: arr.lableNumber,
                                        icon: "el-icon-edit",
                                        level: node.level,
                                        // letter: arr.letter,
                                        // pinyin: arr.labelPinYin,
                                    };
                                });
                                this.oneWords = allArr;
                                resolve(allArr);
                            });
                        } else if (node.level == 1) {
                            $api("/xxwh_bqcx_new", { parentCode: node.data.lableNumber }).then((res) => {
                                let allArr = res.map((arr) => {
                                    return {
                                        value: arr.lableNumber + "",
                                        label: arr.lableName,
                                        parentCode: arr.parentCode,
                                        lableCode: arr.lableCode,
                                        lableNumber: arr.lableNumber,
                                        icon: "el-icon-edit",
                                        level: node.level,
                                        // letter: arr.letter,
                                        // pinyin: arr.labelPinYin,
                                    };
                                });
                                resolve(allArr);
                            });
                        }
                    },
                    resetLeft() {
                        this.filterText = "";
                        this.showLeft = true;
                    },
                    selectLeft() {
                        this.showLeft = false;
                        $api("/xxwh_mhcxq", { labelName: this.filterText }).then((res) => {
                            let allArr = res.map((arr) => {
                                return {
                                    value: arr.lableNumber + "",
                                    label: arr.lableName,
                                    parentCode: arr.parentCode,
                                    lableCode: arr.lableCode,
                                    lableNumber: arr.lableNumber,
                                    icon: "el-icon-edit",
                                };
                            });
                            this.options = allArr;
                        });
                    },
                    onSearchLabel() {
                        this.showLeft = false;
                        $api("/xxwh_mhcxq1", { name: this.filterText }).then((res) => {
                            let isTree = false;
                            let rarr = [];
                            this.options = [];
                            this.optionSize = res.length;
                            let arr = res.map((item) => {
                                if (item.parent_code === 0) {
                                    return {
                                        labelLevel: 1,
                                        value: item.lable_number + "",
                                        label: item.label_name,
                                        parentCode: item.parent_code,
                                        lableCode: item.lable_code,
                                        lableNumber: item.lable_number,
                                        icon: "el-icon-edit",
                                    };
                                } else {
                                    return {
                                        labelLevel: 2,
                                        value: item.lable_number + "",
                                        label: item.label_name,
                                        lableCode: item.lable_code,
                                        lableNumber: item.lable_number,
                                        parentCode: item.parent_code,
                                    };
                                }
                            });
                            arr.forEach((item) => {
                                if (item.parentCode == 0) {
                                    isTree = true;
                                    return;
                                }
                            });
                            rarr = this.tranListToTreeData(arr, 0);
                            if (isTree) {
                                rarr = this.tranListToTreeData(arr, 0);
                            } else {
                                rarr = arr;
                            }
                            this.options = rarr;
                            // console.log('that.options==>', this.options)
                        });
                    },
                    tranListToTreeData(list, rootValue) {
                        var arr = [];
                        list.forEach((item) => {
                            if (item.parentCode === rootValue) {
                                // 找到之后 就要去找 item 下面有没有子节点
                                const children = this.tranListToTreeData(list, item.lableNumber);
                                if (children.length) {
                                    // 如果children的长度大于0 说明找到了子节点
                                    item.children = children;
                                }
                                arr.push(item); // 将内容加入到数组中
                            }
                        });
                        return arr;
                    },
                    onSearchVideo() {
                        let that = this;
                        if (this.nowClickData1 && this.nowClickData1.label == "电信") {
                            $api("/mp_info_newLike", { chn_name: this.videoSearchText }).then((res) => {
                                that.L2options = res.map((o) => {
                                    return {
                                        hls_url: o.hls_url,
                                        label: o.mp_name,
                                        level: 4,
                                        data: o,
                                        lng: o.longitude,
                                        lat: o.latitude,
                                        is_online: o.is_online,
                                        chn_name: o.mp_name,
                                    };
                                });
                            });
                        } else {
                            // $api('/xxwh_dwzl_newLike', { chn_name: this.videoSearchText }).then((res) => {
                            axios({
                                method: "get",
                                url: baseURL.admApi + "/mis/esVideo/xxwh_dwzl_newLike",
                                params: {
                                    chn_name: this.videoSearchText,
                                },
                                headers: {
                                    "Content-Type": "application/json;charset=UTF-8",
                                    Authorization: sessionStorage.getItem("Authorization"),
                                    ptid: "PT0001",
                                },
                            }).then((res) => {
                                that.L2options = res.data.data.map((o) => {
                                    return {
                                        chn_code: o.chn_code,
                                        chn_name: o.chn_name,
                                        label: o.video_name,
                                        level: 4,
                                        is_collection: o.is_collection,
                                        highAltitude_type: o.highAltitude_type,
                                        value: o.chn_code,
                                    };
                                });
                            });
                        }
                    },
                    reset() {
                        this.radio = "";
                        this.videoSearchText = "";
                        this.L2options = this.L2optionsArr;
                        window.parent.mapUtil.removeLayer("camera-load-index13840_1");
                    },
                    searchOnLetter(value) {
                        this.letter === value ? (this.letter = "") : (this.letter = value);
                    },
                    openVideoSearch() {
                        if (this.show) {
                            this.show = false;
                            this.closeVideoSearch();
                            return;
                        }

                        if (
                            window.parent.window.frames["indexTcglBtn"] &&
                            window.parent.window.frames["indexTcglBtn"].vm.show
                        )
                            return;
                        this.show = true;
                        window.parent.postMessage(
                            JSON.stringify({
                                type: "fadeIframe",
                                data: ["leftOut"],
                            }),
                            "*"
                        );
                    },
                    closeVideoSearch() {
                        this.show = false;
                        this.level2 = false;

                        window.parent.postMessage(
                            JSON.stringify({
                                type: "fadeIframe",
                                data: ["leftIn"],
                            }),
                            "*"
                        );
                        window.parent.mapUtil.removeLayer("camera-load-index13840_1");
                    },
                    getL2TreeMap(type) {
                        if (!this.L2TreeMap.has(type)) {
                            this.L2TreeMap.set(type, []);
                        }
                        return this.L2TreeMap.get(type);
                    },

                    lableClick1(data) {
                        if (data.level != 0) {
                            this.nowClickData1 = data;
                            this.nowClickData = null;
                            this.videoSearchText = "";
                        }
                        if (data.level == 0 && data.label == "收藏") {
                            $api("xxwh_dir_select", { userAccess: top.commonObj.userId }).then((res) => {
                                this.L2options = [];
                                res.map((arr) => {
                                    let str = {
                                        dirCode: arr.id,
                                        label: arr.dir_name,
                                        parentCode: 0,
                                        icon: "el-icon-edit",
                                        level: 1,
                                        children: [],
                                    };
                                    this.L2options.push(str);
                                });
                            });
                        } else if (data.level == 0 && data.label != "收藏") {
                            return;
                        } else {
                            this.letter = "";
                            let that = this;
                            // this.L2options = this.getL2TreeMap(data.lableNumber)
                            // if (this.L2options.length > 0) {
                            //   this.level2 = true
                            // }

                            // $api('/xxwh_bqcx_new', { parentCode: data.lableNumber }).then((res) => {
                            //   console.log('res三级==>', res)
                            //   let allArr = res.map((arr) => {
                            //     return {
                            //       value: arr.lableNumber + '',
                            //       label: arr.lableName,
                            //       parentCode: arr.parentCode,
                            //       lableCode: arr.lableCode,
                            //       lableNumber: arr.lableNumber,
                            //       letter: arr.letter,
                            //       pinyin: arr.labelPinYin,
                            //       area_name: arr.area_name,
                            //       level: 3,
                            //       children: [],
                            //     }
                            //   })
                            //   // this.L2options = allArr
                            //   // 过滤区县的数据
                            //   for (let ele in that.L2optionsArr) {
                            //     that.L2optionsArr[ele].children = allArr.filter(
                            //       (o) => o.area_name.indexOf(that.L2optionsArr[ele].label) != -1
                            //     )
                            //   }
                            //   this.L2options = that.L2optionsArr
                            // })

                            //二级菜单打点
                            if (!data.children) {
                                //console.log("3333",data)
                                try {
                                    window.parent.mapUtil.removeLayer("camera-load-index");
                                    window.parent.mapUtil.removeLayer("camera-load-index13840_1");
                                    window.parent.mapUtil.removeLayer("camera-load-index13840_2");
                                    window.parent.mapUtil.removeLayer("camera-load-index2");
                                    window.parent.mapUtil.removeLayer("mouseente");
                                    window.parent.mapUtil.removeLayer("mouseente2");
                                    window.parent.mapUtil.removeAllLayers([
                                        "camera-lx-qiangji",
                                        "camera-lx-qiuji",
                                        "camera-lx-banqiu",
                                        "camera-lx-gaodian",
                                        "camera-zx-qiangji",
                                        "camera-zx-qiuji",
                                        "camera-zx-banqiu",
                                        "camera-zx-gaodian",
                                    ]);
                                    this.L2optionsArr.forEach((item) => {
                                        item.num = 0;
                                    });
                                } catch (error) {}

                                //社会面-电信
                                if (data.lableNumber == 999999920) {
                                    $api("mp_info").then((res) => {
                                        let mapObj = res.map((item) => {
                                            return {
                                                hls_url: item.hls_url,
                                                label: item.mp_name,
                                                level: 5,
                                                data: item,
                                                lng: item.longitude,
                                                lat: item.latitude,
                                                is_online: item.is_online,
                                            };
                                        });
                                        this.L2options = mapObj;
                                        let arr = mapObj.map((item) => {
                                            return {
                                                code: item.hls_url,
                                                pointId: "camera-load-index13840_1",
                                                data: item.data,
                                                point: item.lng + "," + item.lat,
                                                lng: item.lng,
                                                lat: item.lat,
                                                status: item.data.is_online,
                                            };
                                        });
                                        this.getShmPoint(arr);
                                        window.parent.mapUtil.flyTo({
                                            destination: [mapObj[0].lng, mapObj[0].lat],
                                        });
                                    });
                                } else {
                                    // $api('xxwh_dwzl_er_new', {
                                    //   lableNumber: data.parentCode == 999999901 ? data.parentCode : data.lableNumber,
                                    // }).then((res) => {
                                    axios({
                                        method: "get",
                                        url: baseURL.admApi + "/mis/esVideo/xxwh_dwzl_er_new",
                                        params: {
                                            lableNumber:
                                                data.parentCode == 999999901 ? data.parentCode : data.lableNumber,
                                        },
                                        headers: {
                                            "Content-Type": "application/json;charset=UTF-8",
                                            Authorization: sessionStorage.getItem("Authorization"),
                                            ptid: "PT0001",
                                        },
                                    }).then((res) => {
                                        let mapObj = [];
                                        let arr = [];
                                        let not_high = false;
                                        if (data.parentCode == 999999901 && data.label == "铁塔") {
                                            mapObj = res.data.data.filter((v) => v.highAltitude_type == 1);
                                        } else if (data.parentCode == 999999901 && data.label == "雪亮") {
                                            mapObj = res.data.data.filter((v) => v.highAltitude_type == 2);
                                        } else if (data.parentCode == 999999901 && data.label == "其他") {
                                            mapObj = res.data.dataes.filter((v) => v.highAltitude_type == 3);
                                        } else {
                                            mapObj = res.data.data;
                                            not_high = true;
                                        }
                                        if (not_high) {
                                            let allArr = mapObj.map((item) => {
                                                return {
                                                    value: item.chn_code,
                                                    label: item.video_name,
                                                    xzqx: item.xzqx,
                                                    level: 5,
                                                    data: item,
                                                    lng: item.gps_x,
                                                    lat: item.gps_y,
                                                    is_collection: item.is_collection,
                                                    cameraType: item.cameraType,
                                                };
                                            });
                                            // console.log(allArr)
                                            // this.L2options = allArr
                                            // 过滤区县的数据
                                            for (let ele in that.L2optionsArr) {
                                                that.L2optionsArr[ele].children = allArr.filter(
                                                    (o) => o.xzqx.indexOf(that.L2optionsArr[ele].label) != -1
                                                );
                                            }
                                            this.L2options = that.L2optionsArr;
                                            arr = mapObj.map((item) => {
                                                return {
                                                    code: item.chn_code,
                                                    pointId: "camera-load-index13840_1",
                                                    data: item,
                                                    point: item.gps_x + "," + item.gps_y,
                                                    lng: item.gps_x,
                                                    lat: item.gps_y,
                                                    status: item.is_online,
                                                    cameraType: item.cameraType,
                                                    pointType: this.getPointType(item.is_online, item.cameraType),
                                                };
                                            });
                                        } else {
                                            for (let ele in that.L2optionsArr) {
                                                that.L2optionsArr[ele].children = [];
                                            }
                                            this.L2options = that.L2optionsArr;
                                            arr = mapObj.map((item) => {
                                                return {
                                                    code: item.chn_code,
                                                    pointId: "camera-load-index13840_1",
                                                    data: item,
                                                    point: item.gps_x + "," + item.gps_y,
                                                    lng: item.gps_x,
                                                    lat: item.gps_y,
                                                    status: item.is_online,
                                                    cameraType: "4",
                                                    pointType: this.getPointType(item.is_online, "4"),
                                                };
                                            });
                                        }
                                        this.getManyPoint(arr);
                                        // this.cameraList.forEach((item) => {
                                        //   this.getManyPoint(this.filterData(arr, item.name), item.code)
                                        // })
                                        window.parent.mapUtil.flyTo({
                                            destination: [arr[0].lng, arr[0].lat],
                                        });
                                    });
                                }
                            }
                        }
                    },
                    getPointType(is_online, cameraType) {
                        let arr = is_online + "-" + cameraType;
                        let obj = {
                            枪机在线: "1-1",
                            枪机离线: "0-1",
                            球机在线: "1-2",
                            球机离线: "0-2",
                            半球机在线: "1-3",
                            半球机离线: "0-3",
                            高点在线: "1-4",
                            高点离线: "0-4",
                        };
                        for (key in obj) {
                            if (obj[key] == arr) {
                                return key;
                            }
                        }
                    },
                    filterData(mapObj, name) {
                        let obj = {
                            枪机在线: [1, "1"],
                            枪机离线: [0, "1"],
                            球机在线: [1, "2"],
                            球机离线: [0, "2"],
                            半球机在线: [1, "3"],
                            半球机离线: [0, "3"],
                            高点在线: [1, "4"],
                            高点离线: [0, "4"],
                        };
                        return mapObj.filter((item) => {
                            return item.status == obj[name][0] && item.cameraType == obj[name][1];
                        });
                    },
                    //一次绘制多种不同类型的点
                    getManyPoint(pointData, pointId) {
                        // if (pointData.length > 0) {
                        //   window.parent.mapUtil.loadPointLayer({
                        //     data: pointData,
                        //     layerid: pointId,
                        //     iconcfg: {
                        //       image: pointId, //'camera-load4',
                        //       iconSize: 0.5,
                        //     }, //图标
                        //     onclick: this.onclick,
                        //     onblur: this.onblur,
                        //   })
                        // }
                        window.parent.mapUtil.loadPointLayer({
                            layerid: "camera-load-index13840_1",
                            data: pointData,
                            onclick: this.onclick,
                            onblur: this.onblur,
                            cluster: true, //是否定义为聚合点位：true/false
                            iconcfg: {
                                image: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
                                iconSize: 0.5,
                                iconlist: {
                                    field: "pointType",
                                    list: [
                                        {
                                            value: "枪机在线",
                                            size: "50",
                                            src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
                                        },
                                        {
                                            value: "枪机离线",
                                            size: "50",
                                            src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-qiangji.png`,
                                        },
                                        {
                                            value: "球机在线",
                                            size: "50",
                                            src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiuji.png`,
                                        },
                                        {
                                            value: "球机离线",
                                            size: "50",
                                            src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-qiuji.png`,
                                        },
                                        {
                                            value: "半球机在线",
                                            size: "50",
                                            src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-banqiu.png`,
                                        },
                                        {
                                            value: "半球机离线",
                                            size: "50",
                                            src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-banqiu.png`,
                                        },
                                        {
                                            value: "高点在线",
                                            size: "50",
                                            src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-gaodian.png`,
                                        },
                                        {
                                            value: "高点离线",
                                            size: "50",
                                            src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-gaodian.png`,
                                        },
                                    ],
                                },
                            },
                        });
                    },
                    getShmPoint(pointData) {
                        window.parent.mapUtil.loadPointLayer({
                            layerid: "camera-load-index13840_1",
                            data: pointData,
                            onclick: this.onclick,
                            // onblur: this.onblur,
                            cluster: true, //是否定义为聚合点位：true/false
                            iconcfg: {
                                image: `camera-zx-gaodian.png`,
                                iconSize: 0.5,
                                iconlist: {
                                    field: "status",
                                    list: [
                                        {
                                            value: "1",
                                            size: "50",
                                            src: `camera-zx-gaodian.png`,
                                        },
                                        {
                                            value: "0",
                                            size: "50",
                                            src: `camera-lx-gaodian.png`,
                                        },
                                    ],
                                },
                            },
                        });
                    },
                    lableClick2(data) {
                        this.currentNode = data.label;
                        if (data.level != 5) {
                            if (this.nowClickData == data) {
                                return;
                            }
                            this.nowClickData = data;
                        }
                        if (data.level == 1 && this.nowClickData1 != null) {
                            window.parent.mapUtil.removeLayer("camera-load-index");
                            window.parent.mapUtil.removeLayer("camera-load-index13840_1");
                            window.parent.mapUtil.removeLayer("camera-load-index13840_2");
                            window.parent.mapUtil.removeLayer("mouseente");
                            window.parent.mapUtil.removeLayer("mouseente2");
                            window.parent.mapUtil.removeAllLayers([
                                "camera-lx-qiangji",
                                "camera-lx-qiuji",
                                "camera-lx-banqiu",
                                "camera-lx-gaodian",
                                "camera-zx-qiangji",
                                "camera-zx-qiuji",
                                "camera-zx-banqiu",
                                "camera-zx-gaodian",
                            ]);
                            if (this.nowClickData1.parentCode == "999999901") {
                                let code = "";
                                switch (this.nowClickData1.label) {
                                    case "铁塔":
                                        code = "1";
                                        break;
                                    case "雪亮":
                                        code = "2";
                                        break;
                                    case "其他":
                                        code = "3";
                                        break;
                                }
                                this.loading = true;
                                $api("/xxwh_hight", {
                                    areaName: data.label,
                                    typeCode: code,
                                }).then((res) => {
                                    this.loading = false;
                                    let arr = res.map((item) => {
                                        return {
                                            code: item.chn_code,
                                            pointId: "camera-load-index13840_1",
                                            data: item,
                                            point: item.gps_x + "," + item.gps_y,
                                            lng: item.gps_x,
                                            lat: item.gps_y,
                                            status: item.is_online,
                                            cameraType: "4",
                                            pointType: this.getPointType(item.is_online, "4"),
                                        };
                                    });
                                    // console.log('arr', arr)
                                    let allArr = arr.map((item) => {
                                        return {
                                            value: item.data.chn_code,
                                            label: item.data.video_name,
                                            area_name: data.area_name,
                                            point: item.point,
                                            lng: item.lng,
                                            lat: item.lat,
                                            level: 5,
                                            is_collection: item.data.is_collection,
                                            pointId: "camera-load-index13840_1",
                                            status: item.data.is_online,
                                        };
                                    });
                                    data.children = allArr;
                                    data.num = allArr.length;
                                    this.$refs.L2tree.store.currentNode.expanded = true;
                                    this.getManyPoint(arr);
                                    // this.cameraList.forEach((item) => {
                                    //   this.getManyPoint(this.filterData(arr, item.name), item.code)
                                    // })
                                    window.parent.mapUtil.flyTo({
                                        destination: [arr[0].lng, arr[0].lat],
                                    });
                                });
                            }
                        }
                        if (data.level == 1 && data.dirCode) {
                            window.parent.mapUtil.removeLayer("camera-load-index");
                            window.parent.mapUtil.removeLayer("camera-load-index13840_1");
                            window.parent.mapUtil.removeLayer("camera-load-index13840_2");
                            window.parent.mapUtil.removeLayer("mouseente");
                            window.parent.mapUtil.removeLayer("mouseente2");
                            window.parent.mapUtil.removeAllLayers([
                                "camera-lx-qiangji",
                                "camera-lx-qiuji",
                                "camera-lx-banqiu",
                                "camera-lx-gaodian",
                                "camera-zx-qiangji",
                                "camera-zx-qiuji",
                                "camera-zx-banqiu",
                                "camera-zx-gaodian",
                            ]);
                            $api("xxwh_collection", { userAccess: top.commonObj.userId, dirCode: data.dirCode }).then(
                                (res) => {
                                    // console.log(res)
                                    let arr = res.map((item) => {
                                        return {
                                            code: item.chn_code,
                                            pointId: "camera-load-index13840_1",
                                            data: item,
                                            point: item.gps_x + "," + item.gps_y,
                                            lng: item.gps_x,
                                            lat: item.gps_y,
                                            status: item.is_online,
                                            cameraType: item.cameraType,
                                            pointType: this.getPointType(item.is_online, item.cameraType),
                                        };
                                    });
                                    // console.log('arr', arr)
                                    let allArr = arr.map((item) => {
                                        return {
                                            value: item.data.chn_code,
                                            label: item.data.video_name,
                                            area_name: data.area_name,
                                            point: item.point,
                                            lng: item.lng,
                                            lat: item.lat,
                                            level: 5,
                                            is_collection: item.data.is_collection,
                                            pointId: "camera-load-index13840_1",
                                            status: item.data.is_online,
                                            cameraType: item.data.cameraType,
                                        };
                                    });
                                    data.children = allArr;
                                    this.$refs.L2tree.store.currentNode.expanded = true;
                                    this.getManyPoint(arr);
                                    // this.cameraList.forEach((item) => {
                                    //   this.getManyPoint(this.filterData(arr, item.name), item.code)
                                    // })
                                    window.parent.mapUtil.flyTo({
                                        destination: [arr[0].lng, arr[0].lat],
                                    });
                                }
                            );
                        } else {
                            // console.log('三级点击', data)
                            if (data.level != 5) {
                                try {
                                    window.parent.mapUtil.removeLayer("camera-load-index");
                                    window.parent.mapUtil.removeLayer("camera-load-index13840_1");
                                    window.parent.mapUtil.removeLayer("camera-load-index13840_2");
                                    window.parent.mapUtil.removeLayer("mouseente");
                                    window.parent.mapUtil.removeLayer("mouseente2");
                                    window.parent.mapUtil.removeAllLayers([
                                        "camera-lx-qiangji",
                                        "camera-lx-qiuji",
                                        "camera-lx-banqiu",
                                        "camera-lx-gaodian",
                                        "camera-zx-qiangji",
                                        "camera-zx-qiuji",
                                        "camera-zx-banqiu",
                                        "camera-zx-gaodian",
                                    ]);
                                } catch (error) {}
                            }

                            if (data.chn_name) {
                                //视频名称搜索

                                if (data.hls_url) {
                                    let arr = [
                                        {
                                            code: data.data.value,
                                            pointId: "camera-load-index13840_1",
                                            data: data.data,
                                            point: data.data.longitude + "," + data.data.latitude,
                                            lng: data.data.longitude,
                                            lat: data.data.latitude,
                                            status: data.data.is_online,
                                        },
                                    ];
                                    this.getShmPoint(arr);
                                    window.parent.mapUtil.flyTo({
                                        destination: [arr[0].lng, arr[0].lat],
                                    });
                                } else {
                                    this.radio = data.label;
                                    $api("/xxwh_dwzlmore", { code: data.chn_code }).then((res) => {
                                        let arr = res.map((item) => {
                                            return {
                                                code: item.chn_code,
                                                pointId: "camera-load-index13840_1",
                                                data: item,
                                                lng: item.gps_x,
                                                lat: item.gps_y,
                                                point: item.gps_x + "," + item.gps_y,
                                                status: item.is_online,
                                                cameraType: item.cameraType,
                                                pointType: this.getPointType(item.is_online, item.cameraType),
                                            };
                                        });
                                        // console.log(arr)
                                        this.getManyPoint(arr);
                                        // this.cameraList.forEach((item) => {
                                        //   this.getManyPoint(this.filterData(arr, item.name), item.code)
                                        // })
                                        window.parent.mapUtil.flyTo({
                                            destination: [arr[0].lng, arr[0].lat],
                                        });
                                    });
                                }
                            } else {
                                if (data.level != 5) {
                                    let pointData = data.children;
                                    let mapObj1 = pointData.filter((item) => {
                                        return item.data.is_online == 0;
                                    });
                                    let mapObj2 = pointData.filter((item) => {
                                        return item.data.is_online == 1;
                                    });
                                    let arr = pointData.map((item) => {
                                        return {
                                            code: item.data.chn_code,
                                            pointId: "camera-load-index13840_1",
                                            data: item.data,
                                            point: item.data.gps_x + "," + item.data.gps_y,
                                            lng: item.data.gps_x,
                                            lat: item.data.gps_y,
                                            status: item.data.is_online,
                                            cameraType: item.cameraType,
                                            pointType: this.getPointType(item.is_online, item.cameraType),
                                        };
                                    });
                                    this.getManyPoint(arr);
                                    // this.cameraList.forEach((item) => {
                                    //   this.getManyPoint(this.filterData(arr, item.name), item.code)
                                    // })
                                    window.parent.mapUtil.flyTo({
                                        destination: [arr[0].lng, arr[0].lat],
                                    });
                                } else {
                                    // console.log(data)
                                    window.parent.mapUtil.flyTo({
                                        // destination: [data.data.gps_x, data.data.gps_y],
                                        destination: [data.lng, data.lat],
                                    });
                                }
                            }
                        }
                    },
                    dblClickItem(node) {
                        if (node.data.level === 5 || node.data.level === 4) {
                            if (node.data.status == 0) {
                                this.$message("设备离线");
                                return;
                            }
                            if (node.data.hls_url) {
                                let item = {
                                    obj: {
                                        chn_url: node.data.data.hls_url,
                                        chn_name: node.data.data.mp_name,
                                    },
                                    data: node.data.data,
                                };
                                if (
                                    window.location.ancestorOrigins.length != 0 &&
                                    (JSON.stringify(window.location.ancestorOrigins).includes(
                                        "https://csdn.dsjj.jinhua.gov.cn:8102"
                                    ) ||
                                        JSON.stringify(window.location.ancestorOrigins).includes(
                                            "https://csdn.dsjj.jinhua.gov.cn:9601"
                                        ))
                                ) {
                                    let url = "",
                                        left = "0";
                                    if (
                                        JSON.stringify(window.location.ancestorOrigins).includes(
                                            "https://csdn.dsjj.jinhua.gov.cn:8102"
                                        )
                                    ) {
                                        url =
                                            "https://csdn.dsjj.jinhua.gov.cn:8102/static/citybrain/tckz/commont/video_main_code.html";
                                    } else if (
                                        JSON.stringify(window.location.ancestorOrigins).includes(
                                            "https://csdn.dsjj.jinhua.gov.cn:9601"
                                        )
                                    ) {
                                        left = "1920px";
                                        url =
                                            "https://csdn.dsjj.jinhua.gov.cn:9601/static/citybrain/csrk_3840/tckz/commont/video_main_code.html";
                                    }
                                    top.postMessage(
                                        JSON.stringify({
                                            type: "openIframe",
                                            name: "video_main_code",
                                            src: url,
                                            width: "3840px",
                                            height: "2160px",
                                            left: "1920px",
                                            top: "0",
                                            zIndex: "1000",
                                            argument: item,
                                        }),
                                        "*"
                                    );
                                } else {
                                    let iframe1 = {
                                        type: "openIframe",
                                        name: "video_main_code",
                                        src: baseURL.url + "/static/citybrain/tcgl/commont/video_main_code.html",
                                        width: "3840px",
                                        height: "2160px",
                                        left: "0",
                                        top: "0",
                                        zIndex: "1000",
                                        argument: item,
                                    };
                                    window.parent.postMessage(JSON.stringify(iframe1), "*");
                                }
                            } else {
                                let item = {
                                    obj: {
                                        is_collection: node.data.is_collection,
                                        chn_name: node.data.label,
                                        highAltitude_type: node.data.highAltitude_type,
                                    },
                                    video_code: node.data.value,
                                };
                                if (node.data.highAltitude_type == 3) {
                                    axios({
                                        method: "get",
                                        url: baseURL.admApi + "/mis/system/videos/webPlay/" + node.data.chn_code,
                                        headers: {
                                            "Content-Type": "application/json;charset=UTF-8",
                                            Authorization: sessionStorage.getItem("Authorization"),
                                            ptid: "PT0001",
                                        },
                                    }).then((res) => {
                                        // console.log(res.data.data)
                                        let num = 1200;
                                        let higth = 800;
                                        let moveLeft = (3840 - num) / 2;
                                        let moveHigth = (2160 - higth) / 2;
                                        window.open(
                                            res.data.data,
                                            "婺城区",
                                            "directories=no, location=no, toolbar=no,scrollbars=yes, resizable=yes, height=" +
                                                higth +
                                                ", width=" +
                                                num +
                                                ", top=" +
                                                moveHigth +
                                                ", left=" +
                                                moveLeft +
                                                ""
                                        );
                                    });
                                } else {
                                    if (
                                        window.location.ancestorOrigins.length != 0 &&
                                        (JSON.stringify(window.location.ancestorOrigins).includes(
                                            "https://csdn.dsjj.jinhua.gov.cn:8102"
                                        ) ||
                                            JSON.stringify(window.location.ancestorOrigins).includes(
                                                "https://csdn.dsjj.jinhua.gov.cn:9601"
                                            ))
                                    ) {
                                        let url = "",
                                            left = "0";
                                        if (
                                            JSON.stringify(window.location.ancestorOrigins).includes(
                                                "https://csdn.dsjj.jinhua.gov.cn:8102"
                                            )
                                        ) {
                                            url =
                                                "https://csdn.dsjj.jinhua.gov.cn:8102/static/citybrain/tckz/commont/video_main_code.html";
                                        } else if (
                                            JSON.stringify(window.location.ancestorOrigins).includes(
                                                "https://csdn.dsjj.jinhua.gov.cn:9601"
                                            )
                                        ) {
                                            left = "1920px";
                                            url =
                                                "https://csdn.dsjj.jinhua.gov.cn:9601/static/citybrain/csrk_3840/tckz/commont/video_main_code.html";
                                        }
                                        top.postMessage(
                                            JSON.stringify({
                                                type: "openIframe",
                                                name: "video_main_code",
                                                src: url,
                                                width: "3840px",
                                                height: "2160px",
                                                left: left,
                                                top: "0",
                                                zIndex: "1000",
                                                argument: item,
                                            }),
                                            "*"
                                        );
                                    } else {
                                        let iframe1 = {
                                            type: "openIframe",
                                            name: "video_main_code",
                                            src: baseURL.url + "/static/citybrain/tcgl/commont/video_main_code.html",
                                            width: "3840px",
                                            height: "2160px",
                                            left: "0",
                                            top: "0",
                                            zIndex: "1000",
                                            argument: item,
                                        };
                                        window.parent.lay.openIframe(iframe1);
                                    }
                                }
                            }
                        }
                    },
                    filterNode(value, data) {
                        if (!value) return true;
                        return data.label.indexOf(value) !== -1;
                        // || data.pinyin.indexOf(value) !== -1
                    },
                    L2filterNode(value, data) {
                        if (!value) return true;
                        return data.label.indexOf(value) !== -1;
                        // if (this.letter !== '') {
                        //   if (!value) return data.letter.indexOf(this.letter) !== -1
                        //   else
                        //     return (
                        //       data.letter.indexOf(this.letter) !== -1 &&
                        //       (data.label.indexOf(value) !== -1 || data.pinyin.indexOf(value) !== -1)
                        //     )
                        // }
                        // if (!value) return true
                        // return data.label.indexOf(value) !== -1 || data.pinyin.indexOf(value) !== -1
                    },
                },
            });
        </script>
        <style>
            @keyframes wordsLoop {
                0% {
                    transform: translateX(0%);

                    -webkit-transform: translateX(0%);
                }

                100% {
                    transform: translateX(-100%);

                    -webkit-transform: translateX(-100%);
                }
            }

            @-webkit-keyframes wordsLoop {
                0% {
                    transform: translateX(0%);

                    -webkit-transform: translateX(0%);
                }

                100% {
                    transform: translateX(-100%);

                    -webkit-transform: translateX(-100%);
                }
            }

            /* 调整样式 */
            /* .el-tree-node__expand-icon.is-leaf{
        color: #C0C4CC;
      } */
            .el-tree-node__loading-icon {
                display: none;
            }

            .w-60 {
                display: inline-block;
                width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .w-60:hover {
                animation: 3s wordsLoop linear infinite normal;
                text-overflow: inherit !important;
                overflow: visible !important;
            }

            .v-text {
                display: inline-block;
            }

            .v-text:hover {
                animation: 3s wordsLoop linear infinite normal;
                text-overflow: inherit !important;
                overflow: visible !important;
            }

            .el-tree-node {
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                outline: 0;
                max-width: 307px;
            }

            .v-content {
                width: 810px;
                background-color: #091e35;
                border: 1px solid #359cf8;
                border-radius: 10px;
                top: 10px;
            }

            .retrieval {
                width: 40px;
                height: 994px;
                background: #003d7650;
                border-radius: 10px;
                position: absolute;
                top: 100px;
                right: 10px;
                color: #d6e7f950;
                text-align: center;
                padding: 15px 0;
                font-size: 28px;
            }

            .retrieval p {
                margin: 0;
                cursor: pointer;
            }

            .retrieval p:hover {
                color: #00ffff;
            }

            .retrieval_click {
                color: #00ffff;
            }

            .icon {
                width: 26px;
                height: 26px;
                vertical-align: -0.15em;
                fill: currentColor;
                overflow: hidden;
            }

            .btn-active {
                /* position: absolute !important;
        right: -60px !important;
        top:500px !important; */
                color: #ffc460 !important;
                background-image: url("./static/images/home/<USER>") !important;
            }

            .btn {
                width: 54px;
                height: 255px;
                background-image: url("./static/images/home/<USER>");
                background-repeat: no-repeat;
                background-size: 100% 100%;
                font-size: 38px;
                color: white;
                line-height: 42px;
                text-align: center;
                cursor: pointer;
                position: absolute;
                top: 550px;
                left: 0px;
            }

            /* .btn p {
        padding-top: 30px;
      } */
            .btn img {
                width: 28px;
                height: 29px;
                position: relative;
                top: -40px;
            }

            .video_box {
                width: 380px;
                height: 1133px;
                /* background-color: #091e35;
        border-top: 1px solid #359cf8;
        border-left: 1px solid #359cf8;
        border-bottom: 1px solid #359cf8;
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px; */
            }

            .video_box_l2 {
                width: 385px;
                /* border-left:0px;
        border-right: 1px solid #359cf8;
        border-top-left-radius: 0px;
        border-bottom-left-radius: 0px;
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px; */
            }

            ::-webkit-scrollbar {
                width: 0;
                height: 0;
                background-color: transparent;
            }

            .video_box input {
                margin-left: 15px;
                margin-top: 20px;
                width: 90%;
                height: 44px;
                background: #132c4e;
                border: 1px solid #359cf8;
                opacity: 0.9;
                border-radius: 10px;
                font-size: 28px;
            }

            .video_box button {
                margin-top: 20px;
                height: 44px;
                line-height: 21px;
                width: 100px;
                border: 1px solid #359cf8;
                background-image: linear-gradient(-32deg, rgba(0, 32, 52, 0.9), rgba(0, 89, 147, 0.9));
                border-radius: 10px;
                font-size: 28px;
                font-weight: 400;
                color: #accbee;
            }

            .el-tree {
                background-color: transparent;
                color: white;
            }

            .el-tree-node__content:hover {
                background-color: #ffc46050;
                color: #ffc460;
            }

            .el-tree-node:focus > .el-tree-node__content {
                background-color: #ffc46050;
                color: #ffc460;
            }

            .video_box_l2 .el-tree-node__content:hover {
                background-color: #00c0ff50;
                color: #00c0ff;
            }

            .video_box_l2 .el-tree-node:focus > .el-tree-node__content {
                background-color: #00c0ff50;
                color: #00c0ff;
            }

            .el-tree-node__content {
                height: 70px;
                padding-left: 20px !important;
            }

            .el-tree-node__expand-icon {
                font-size: 22px;
                position: absolute;
                right: 35px;
            }

            .el-radio__inner {
                background-color: #39516a;
                border: 1px solid #90a2bd;
            }

            .el-radio__input.is-checked .el-radio__inner {
                background: #22e8e8;
                border-color: #22e8e8;
            }

            .el-radio__label {
                display: none;
            }

            .el-input__inner {
                color: #fff;
            }
        </style>
    </body>
</html>
