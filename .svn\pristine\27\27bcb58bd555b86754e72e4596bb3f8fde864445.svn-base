<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>案件详情</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/Vue/vue.js"></script>
    <script src="/static/js/jslib/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <style>

      .dialogContainer {
        width: 2190px;
        height: 1559px;
        background: url("../../../images/zfts/rwbg.png");
        background-size: cover;
        position: relative;
        overflow: hidden;
        display: flex;
        justify-content: flex-start;
        flex-direction: column;
        align-items: center;
      }

      .titleLine {
        width:100%;
        text-align: center;
        height: 47px;
        margin-top: 127px;
      }

      .title {
        font-size: 48px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #FFFFFF;
        background: linear-gradient(0deg, #FFCC00 0.4150390625%, #FFFFFF 99.5849609375%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .close {
        width: 24px;
        height: 24px;
        background: url("../../../images/zfts/close.png");
        background-size: cover;
        position: absolute;
        right: 75px;
        top: 105px;
        cursor: pointer;
      }

      .content {
        display: flex;
        justify-content: space-around;
        flex-direction: column;
        align-items: center;
        width: 2050px;
        height: 1246px;
        margin-top: 49px;
      }

      .container {
        width:100%;
        background: rgba(10, 97, 158, 0.15);
      }

      .container1 {
        height: 590px;
      }

      .container2 {
        height: 300px;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        flex-direction: column;
      }

      .container3 {
        height: 276px;
      }

      .container1-inner {
        width: 1972px;
        height: 524px;
        margin: 26px 0 0 40px;
      }

      .container3-inner {
          width: 1972px;
          height: 210px;
          margin: 26px 0 0 40px;
      }

      .code {
        font-size: 30px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #C8D5E0;
      }

      .table {
        width:1980px;
        height: 478px;
        margin-top: 23px;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-wrap: wrap;
        border-top: 2px solid #335176;
        border-left: 2px solid #335176;
      }

      .table2 {
        height: 158px;
      }

      .table-column {
        flex-shrink: 0;
        width: 660px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }

      .activeTableColumn {
        width:1980px;
      }

      .table-column-key {
        width: 278px;
        height: 78px;
        background: rgba(50,134,248,0.1);
        line-height: 80px;
        text-align: right;
        border-right: 2px solid #335176;
        border-bottom: 2px solid #335176;
      }

      .table-column-value {
        width: 378px;
        height: 78px;
        border-right: 2px solid #335176;
        border-bottom: 2px solid #335176;
        line-height: 80px;
        text-align: left;
      }

      .activeKey {
        height: 118px;
      }

      .activeValue {
        width: 1702px;
        height: 118px;
        line-height: unset;
      }

      .text-key {
        font-size: 30px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #C8D5E0;
        margin-right: 22px;
      }

      .text-value {
        width: 334px;
        font-size: 30px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
        margin-left: 22px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .activeTextValue {
        margin: 15px 0 0 15px;
        width: 1654px;
        height:88px;
        line-height: unset;
        white-space: pre-wrap;
        overflow-y: scroll;
      }

      ::-webkit-scrollbar {
          width:0
      }

      .container2-title {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        color: #EED252;
      }

      .container2-info {
          text-indent: 70px;
        width: 1935px;
        height: 128px;
        font-size: 30px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
        overflow-y: scroll;
        line-height: 48px;
      }


      .lian {
          width: 201px;
          height: 201px;
          background: url("../../../images/zfts/lian.png");
          background-size: cover;
          position: absolute;
          right: 44px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          flex-direction: column;
      }

      .songda {
          width: 201px;
          height: 201px;
          background: url("../../../images/zfts/songda.png");
          background-size: cover;
          position: absolute;
          right: 44px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          flex-direction: column;
      }

      .date {
          width: 115px;
          height: 76px;
          font-size: 30px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-style: italic;
          color: #FFFFFF;
          white-space: pre-wrap;
          margin-top: 30px;
          text-align: center;
      }

      .dateText {
          font-size: 48px;
          font-family: YouSheBiaoTiHei;
          font-weight: 400;
          color: #FFFFFF;
          text-align: center;
      }

      .rotate {
          transform: rotate(20deg);
          display: flex;
          justify-content: flex-start;
          align-items: center;
          flex-direction: column;
          margin-left: 15px;
      }

    </style>
  </head>
  <body>
    <div id="root">
      <div class="dialogContainer">
        <div class="close" @click="close">x</div>
        <div class="titleLine">
          <div class="title">{{MainData.title}}</div>
        </div>
        <div class="content">
          <div class="container container1">
            <div class="lian">
              <div class="rotate">
                <div class="date">{{MainData.filing_date}}</div>
                <div class="dateText">立案</div>
              </div>
            </div>
            <div class="container1-inner">
              <div class="code">编号：{{MainData.case_no}}</div>
              <div class="table">
                <div class="table-column" v-for="(item,i) in tableData1" :key="i" :class="{activeTableColumn:item.isActive}">
                  <div class="table-column-key" :class="{activeKey:item.isActive}">
                    <div class="text-key">{{item.name}}</div>
                  </div>
                  <div class="table-column-value" :class="{activeValue:item.isActive}">
                    <div class="text-value" v-if="item.attribute == 'group_discusInd'">{{MainData[item.attribute] && MainData[item.attribute] == "1"?"是":"否"}}</div>
                    <div class="text-value" v-else-if="item.attribute == 'law_enforcement_officersName'">{{MainData[item.attribute]?nameListDesensitization(MainData[item.attribute].split(",")):""}}</div>
                    <div class="text-value" v-else-if="item.attribute == 'admstra_cp_name'">{{MainData[item.attribute]?xzzfzx.handleName(MainData[item.attribute]):""}}</div>
                    <div class="text-value" v-else-if="item.attribute == 'admstra_cp_cert_no'">{{MainData[item.attribute]?xzzfzx.formatIdentity(MainData[item.attribute]):""}}</div>
                    <div class="text-value" :class="{activeTextValue:item.isActive}" v-else :title="MainData[item.attribute]">{{MainData[item.attribute]}}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="container container2">
            <div class="container2-title">违法事件事实</div>
            <div class="container2-info">{{MainData.illegal_fact}}</div>
          </div>
          <div class="container container3">
            <div class="songda" v-show="MainData.deci_delivery_date">
              <div class="rotate">
                <div class="date">{{MainData.deci_delivery_date}}</div>
                <div class="dateText">送达</div>
              </div>
            </div>
            <div class="container3-inner">
              <div class="code">决定文书号：{{MainData.decision_symbol}}</div>
              <div class="table table2">
                <div class="table-column" v-for="(item,i) in tableData2" :key="i" :class="{activeTableColumn:item.isActive}">
                  <div class="table-column-key" :class="{activeKey:item.isActive}">
                    <div class="text-key">{{item.name}}</div>
                  </div>
                  <div class="table-column-value" :class="{activeValue:item.isActive}">
                    <div class="text-value" v-if="item.attribute == 'not_adminis_penaltiesInd'">{{MainData[item.attribute] && MainData[item.attribute] == "1"?"是":"否"}}</div>
                    <div class="text-value" v-else-if="item.name == '处罚金额'">{{MainData['fine_amt']?MainData['fine_amt']:''}}</div>
                    <div class="text-value" :class="{activeTextValue:item.isActive}" v-else>{{MainData[item.attribute]}}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      new Vue({
        el: "#root",
        data: {
          MainData: {

          },
          //attribute 数据对应的字段名 isActive:是否为整行表格元素
          tableData1:[
            {
              name:"实施机构名称",
              attribute:"implementInist_name",
              isActive:false
            },
            {
              name:"处罚程序描述",
              attribute:"punish_proce_desc",
              isActive:false
            },
            {
              name:"执法领域",
              attribute:"lawEnforce_domain_name",
              isActive:false
            },
            {
              name:"执法人员",
              attribute:"law_enforcement_officersName",
              isActive:false
            },
            {
              name:"行政相对人",
              attribute:"admstra_cp_name",
              isActive:false
            },
            {
              name:"行政相对人身份证",
              attribute:"admstra_cp_cert_no",
              isActive:false
            },
            {
              name:"案件描述",
              attribute:"case_cause_desc",
              isActive:true
            },
            {
              name:"违法日期",
              attribute:"illegal_time",
              isActive:false
            },
            {
              name:"是否集体讨论",
              attribute:"group_discusInd",
              isActive:false
            },
            {
              name:"听证举行日期",
              attribute:"hearing_hold_date",
              isActive:false
            },
            {
              name:"听证方式描述",
              attribute:"hearing_way_desc",
              isActive:true
            }
          ],
          tableData2:[
            {
              name:"决定日期",
              attribute:"deci_date",
              isActive:false
            },
            {
              name:"处罚类型",
              attribute:"punish_type_desc",
              isActive:false
            },
            {
              name:"处罚金额",
              attribute:"fine_amt",
              isActive:false
            },
            {
              name:"没收违法所得金额",
              attribute:"confiscateIllegal_getAmt",
              isActive:false
            },
            {
              name:"没收非法财物金额",
              attribute:"confiscateIllegal_property_amt",
              isActive:false
            },
            // {
            //   name:"是否给与处罚标志",
            //   attribute:"not_adminis_penaltiesInd",
            //   isActive:false
            // },
            {
              name:"结案日期",
              attribute:"cases_closed_date",
              isActive:false
            }
          ]
        },
        mounted() {
          let this_ = this;
          window.addEventListener("message", (e) => {
            if (e.data.zfrw_details) {
              let obj = e.data.zfrw_details;
              obj.ywwd2 = obj.code;
              this_.getData(obj,obj.name);
            }
          });
        },
        computed:{
          xzzfzx() {
            return window.parent.xzzfzx
          }
        },
        methods: {
          getData(parameter,name) {
            $api("/zfts_zfrw_tc", parameter).then((res) => {
              console.log(res);
              this.MainData = res[0];
              this.MainData.title = name
            });
          },
          close() {
            window.parent.lay.closeIframeByNames(["zfrw_details2"]);
          },
          nameListDesensitization(arr) {
            let result = JSON.parse(JSON.stringify(arr))
            result = result.map(item => this.xzzfzx.handleName(item))
            return result.join(',');
          }
        },
      });
    </script>
  </body>
</html>
