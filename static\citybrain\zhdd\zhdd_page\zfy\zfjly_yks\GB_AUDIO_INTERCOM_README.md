# 国标音频对讲功能使用说明

## 概述

基于7.0-demo的实现模式，在zfjly.js模块中集成了国标音频对讲功能。该功能通过WebSocket与后端通信，使用WebRTC进行音频推流，实现与国标设备的双向音频对讲。

## 功能特性

1. **WebSocket通信**：通过WebSocket发送音频对讲命令
2. **WebRTC推流**：使用WebRTC进行音频数据传输
3. **状态管理**：完整的对讲状态跟踪和管理
4. **静音控制**：支持对讲过程中的静音/取消静音
5. **事件监听**：提供音频开始/停止事件回调
6. **错误处理**：完善的错误处理和连接恢复机制

## API接口

### 1. 开始国标音频对讲

```javascript
// 设备信息对象
const deviceInfo = {
    hostbody: "设备编码",
    imei: "设备IMEI", // 可选，默认使用hostbody
    name: "设备名称"  // 可选
};

// 开始对讲
zfjly.startGBIntercom(deviceInfo)
    .then(result => {
        console.log("对讲启动成功:", result.message);
        console.log("设备信息:", result.device);
    })
    .catch(error => {
        console.error("对讲启动失败:", error);
    });
```

### 2. 停止国标音频对讲

```javascript
zfjly.stopGBIntercom()
    .then(result => {
        console.log("对讲停止成功:", result.message);
    })
    .catch(error => {
        console.error("对讲停止失败:", error);
    });
```

### 3. 切换对讲状态

```javascript
// 如果当前没有对讲则开始，如果正在对讲则停止
zfjly.toggleGBIntercom(deviceInfo)
    .then(result => {
        console.log("对讲状态切换成功:", result.message);
    })
    .catch(error => {
        console.error("对讲状态切换失败:", error);
    });
```

### 4. 静音控制

```javascript
// 静音
zfjly.toggleGBIntercomMute(true)
    .then(result => {
        console.log("静音成功:", result.message);
    });

// 取消静音
zfjly.toggleGBIntercomMute(false)
    .then(result => {
        console.log("取消静音成功:", result.message);
    });
```

### 5. 获取对讲状态

```javascript
const status = zfjly.getGBIntercomStatus();
console.log("对讲状态:", {
    isActive: status.isActive,           // 是否正在对讲
    currentDevice: status.currentDevice, // 当前对讲设备
    hasWebRTC: status.hasWebRTC,        // 是否有WebRTC连接
    audioWsInfo: status.audioWsInfo     // 音频WebSocket信息
});

// 简单检查是否正在对讲
if (zfjly.isGBIntercomActive()) {
    console.log("正在进行音频对讲");
}
```

### 6. 检查设备支持

```javascript
if (zfjly.checkGBIntercomSupport(deviceInfo)) {
    console.log("设备支持音频对讲");
} else {
    console.log("设备不支持音频对讲或WebSocket未连接");
}
```

### 7. 设置事件监听器

```javascript
zfjly.setGBIntercomListeners({
    onAudioStart: function(audioWsInfo) {
        console.log("音频开始事件:", audioWsInfo);
        // 可以在这里更新UI状态
    },
    onAudioStop: function() {
        console.log("音频停止事件");
        // 可以在这里更新UI状态
    }
});
```

## 使用流程

### 基本使用流程

1. **初始化连接**：确保WebSocket连接已建立
2. **检查设备支持**：使用`checkGBIntercomSupport()`检查设备是否支持对讲
3. **开始对讲**：调用`startGBIntercom()`开始音频对讲
4. **控制对讲**：使用`toggleGBIntercomMute()`进行静音控制
5. **停止对讲**：调用`stopGBIntercom()`停止音频对讲

### 完整示例

```javascript
// 1. 设置事件监听器
zfjly.setGBIntercomListeners({
    onAudioStart: function(audioWsInfo) {
        console.log("音频对讲已开始");
        // 更新UI，显示对讲状态
        updateIntercomUI(true);
    },
    onAudioStop: function() {
        console.log("音频对讲已停止");
        // 更新UI，隐藏对讲状态
        updateIntercomUI(false);
    }
});

// 2. 设备信息
const deviceInfo = {
    hostbody: "34020000001320000001",
    imei: "34020000001320000001",
    name: "前端摄像头01"
};

// 3. 检查设备支持
if (!zfjly.checkGBIntercomSupport(deviceInfo)) {
    alert("设备不支持音频对讲或网络未连接");
    return;
}

// 4. 开始对讲
zfjly.startGBIntercom(deviceInfo)
    .then(result => {
        console.log("对讲启动成功");
        
        // 5. 可以进行静音控制
        setTimeout(() => {
            zfjly.toggleGBIntercomMute(true).then(() => {
                console.log("已静音");
            });
        }, 5000);
        
        // 6. 10秒后停止对讲
        setTimeout(() => {
            zfjly.stopGBIntercom().then(() => {
                console.log("对讲已停止");
            });
        }, 10000);
    })
    .catch(error => {
        console.error("对讲启动失败:", error);
        alert("对讲启动失败: " + error);
    });
```

## 注意事项

1. **WebSocket连接**：确保在调用对讲功能前WebSocket连接已建立
2. **设备在线**：设备必须在线才能进行音频对讲
3. **浏览器权限**：需要用户授权麦克风权限
4. **并发限制**：同时只能与一个设备进行对讲
5. **网络稳定**：音频对讲对网络稳定性要求较高
6. **资源清理**：页面关闭或刷新时会自动清理对讲资源

## 错误处理

常见错误及处理方式：

- `"设备信息不完整"`：检查deviceInfo对象是否包含hostbody字段
- `"WebSocket连接未建立或已断开"`：检查网络连接，重新建立WebSocket连接
- `"已有设备正在对讲中"`：先停止当前对讲再开始新的对讲
- `"Webrtc类未加载"`：确保webrtc.js文件已正确引入
- `"设备不在线或不支持对讲"`：检查设备状态和配置

## 技术实现

### WebSocket命令格式

```javascript
// 开始音频对讲
{
    command: "start_audio",
    data: JSON.stringify({
        hostbody: "设备编码",
        imei: "设备IMEI",
        type: "intercom"
    })
}

// 停止音频对讲
{
    command: "stop_audio",
    data: JSON.stringify({
        hostbody: "设备编码",
        imei: "设备IMEI"
    })
}

// 静音控制
{
    command: "audio_mute",
    data: JSON.stringify({
        hostbody: "设备编码",
        imei: "设备IMEI",
        mute: true/false
    })
}
```

### WebRTC配置

```javascript
const webrtcConfig = {
    element: "",                    // 音频对讲不需要视频元素
    debug: true,                   // 开启调试模式
    zlmsdpUrl: "推流地址",          // 从服务器获取
    simulcast: false,              // 不使用联播
    useCamera: false,              // 不使用摄像头
    audioEnable: true,             // 启用音频
    videoEnable: false,            // 禁用视频
    recvOnly: false,               // 双向通信
    resolution: { w: 0, h: 0 }     // 音频不需要分辨率
};
```

## 更新日志

- **v1.0.0**：初始版本，实现基本的国标音频对讲功能
- 参考7.0-demo/js/DynamicDom.js和http.js的实现模式
- 集成WebSocket通信和WebRTC推流
- 提供完整的API接口和事件监听机制
