<!--
 * @Author: xk_zhang
 * @Date: 2022-09-19 00:32:53
 * @E-mail: <EMAIL>
 * @LastEditors: xk_zhang
 * @LastEditTime: 2022-09-19 02:11:46
 * @Desc:
-->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>执法记录仪监控</title>
  <script src="/jquery/jquery-3.6.1.min.js"></script>
  <script src="/static/js/layui/layui.js"></script>
  <script src="axios.min.js"></script>
  <script src="md5.js"></script>
  <script src="zfjly.js"></script>
  <script src="DynamicDom.js"></script>
  <script src="webrtc.js"></script>
  <script src="ZLMRTCClient.js"></script>
  <style>
      html, body {
          margin: 0;
          padding: 0;
          height: 100%;
          width: 100%;
          overflow: hidden;
      }
      .container {
          position: relative;
          width: 100%;
          height: 100%;
          background-color: #000;
          display: flex;
          flex-direction: column;
      }
      .header {
          height: 50px;
          background-color: rgba(0, 0, 0, 0.7);
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 20px;
          color: #fff;
      }
      .title {
          font-size: 18px;
          font-weight: bold;
      }
      .close-btn {
          background: url("/static/images/zhdd/close.png") no-repeat center;
          width: 34px;
          height: 34px;
          cursor: pointer;
      }
      .video-container {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
      }
      .video {
          width: 100%;
          height: 100%;
          object-fit: contain;
          background-color: #000;
      }
      .control-panel {
          position: absolute;
          bottom: 20px;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          background-color: rgba(0, 0, 0, 0.5);
          padding: 10px;
          border-radius: 5px;
          gap: 10px;
      }
      .btn {
          background-color: #1890ff;
          color: white;
          border: none;
          padding: 8px 15px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
      }
      .btn:hover {
          background-color: #40a9ff;
      }
      .btn:disabled {
          background-color: #d9d9d9;
          cursor: not-allowed;
      }

      /* 声音控制按钮样式 */
      .volume-off {
          background-color: #ff4d4f;
      }

      .volume-off:hover {
          background-color: #ff7875;
      }

      .volume-on {
          background-color: #52c41a;
      }

      .volume-on:hover {
          background-color: #73d13d;
      }
      .status {
          position: absolute;
          top: 60px;
          left: 20px;
          color: #fff;
          background-color: rgba(0, 0, 0, 0.5);
          padding: 5px 10px;
          border-radius: 3px;
          display: none;
      }
      .loading {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.7);
          display: flex;
          justify-content: center;
          align-items: center;
          color: #fff;
          font-size: 18px;
          display: none;
      }

      /* 音频对讲组件样式 - 参考7.0-demo */
      .chromeMain {
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 9999;
          background: rgba(0, 0, 0, 0.8);
          border-radius: 8px;
          padding: 20px;
          min-width: 300px;
      }

      .video_mian {
          position: relative;
          background: #000;
          border-radius: 4px;
          overflow: hidden;
      }

      .playerAudioBox {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 4px;
          min-height: 150px;
          justify-content: center;
      }

      .textName {
          color: #fff;
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 15px;
          text-align: center;
      }

      .chrome {
          width: 100%;
          max-width: 250px;
      }

      .myControls {
          display: flex;
          justify-content: center;
          gap: 10px;
          margin-top: 15px;
          padding: 10px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
      }

      .control {
          width: 32px;
          height: 32px;
          cursor: pointer;
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          border-radius: 4px;
          transition: all 0.3s ease;
      }

      .control:hover {
          background-color: rgba(255, 255, 255, 0.2);
      }

      .end {
          background-image: url('/static/images/zhdd/close.png');
      }

      .voiceClose {
          background-image: url('/static/images/zhdd/volume-off.png');
      }

      .voiceOpen {
          background-image: url('/static/images/zhdd/volume-on.png');
      }

      .voice_intercom {
          background-image: url('/static/images/zhdd/mic-off.png');
      }

      .video_open {
          background-image: url('/static/images/zhdd/mic-on.png');
      }

      .boxs {
          transition: all 0.3s ease;
      }

      .box_audio_bg {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      }
  </style>
</head>
<body>
<div class="container">
  <div class="header">
    <div class="title">执法记录仪监控</div>
    <div class="close-btn" id="closeBtn"></div>
  </div>
  <div class="video-container">
    <video id="videoPlayer" class="video" controls autoplay muted="false"></video>
    <div class="control-panel">
      <button class="btn" id="playBtn">播放视频</button>
      <button class="btn" id="audioBtn">开启对讲</button>
      <button class="btn" id="stopBtn">停止</button>
    </div>
    <div class="status" id="statusText"></div>
  </div>
  <div class="loading" id="loading">正在加载，请稍候...</div>
</div>

<script type="module">
  import {
    Wsplayer,
    HZRecorder_pcm_push,
    HZRecorder_pcm,
  } from "./JY-chromePlayer.min.js";

  window.addEventListener(
    "message",
    function (event) {
      //子获取父消息
      let newData;
      if (typeof event.data == "object") {
        newData = event.data;
      } else {
        newData = JSON.parse(event.data.argument);
      }
      if (newData.name === "openVideoTest") {
        // 页面初始化
        init(newData.videoCode);
      } else if (newData.name === "ddhjVideoTest") {
        // 页面初始化
        init(newData.videoCode);
      }
    },
    false
  );

  // DOM 元素
  const videoPlayer = document.getElementById("videoPlayer");
  const playBtn = document.getElementById("playBtn");
  const audioBtn = document.getElementById("audioBtn");
  const stopBtn = document.getElementById("stopBtn");
  const closeBtn = document.getElementById("closeBtn");
  const statusText = document.getElementById("statusText");
  const loading = document.getElementById("loading");

  // 状态变量
  let player = null;
  let recorders = null;
  let recordersPlatform = null;
  let isAudioActive = false;
  let currentHostbody = null;
  let wsConnected = false;
  let webRtcUrl = null;
  let currentSn = null; // 保存当前流的sn值

  // 初始化
  function init(Hostbody) {
    // 按钮事件
    playBtn.addEventListener("click", startVideo);
    audioBtn.addEventListener("click", toggleAudio);
    stopBtn.addEventListener("click", stopAll);
    closeBtn.addEventListener("click", closeWindow);

    // 禁用按钮，等待登录完成
    setButtonsEnabled(false);

    // 初始登录
    showLoading(true);
    showStatus("正在连接执法记录仪平台...");

    // 自动登录
    zfjly.loginZFJLY().then(success => {
      if (success) {
        showStatus("已连接执法记录仪平台");
        wsConnected = true;
        setButtonsEnabled(true);

        // 尝试获取URL参数中的设备编码
        currentHostbody = Hostbody[0] || "T0C2675"; // 默认设备编码

        // 获取设备信息
        return zfjly.getDeviceInfo();
      } else {
        showStatus("连接执法记录仪平台失败");
        return Promise.reject("登录失败");
      }
    }).then(devices => {
      showLoading(false);
      // 如果URL参数指定了自动播放
      startVideo();
    }).catch(err => {
      console.error("初始化失败:", err);
      showStatus("初始化失败: " + err);
      showLoading(false);
    });
  }

  // 开始视频播放
  function startVideo() {
    if (!currentHostbody || !wsConnected) {
      showStatus("请先选择设备或确保连接正常");
      return;
    }

    showLoading(true);
    showStatus("正在连接视频流...");

    zfjly.startLive(currentHostbody).then(res => {
      if (res.data.code == 200) {
        if (res.data.data[0].is_existed) {
          webRtcUrl = res.data.data[0].play_info.webrtc_url;
          const webRtcUrls = res.data.data[0].play_info.webrtc_url;

          // 保存sn值，用于停止视频流时使用
          if (res.data.data[0].sn) {
            currentSn = res.data.data[0].sn;
            console.log("获取到视频流SN:", currentSn);
          }

          showStatus("视频流连接成功，正在加载...");
          showLoading(false);
          // 使用WebRTC方式播放
          return zfjly.createWebRTCPlayer(videoPlayer, webRtcUrls);
        } else {
          showStatus("视频拉流失败");
          showLoading(false);
          return Promise.reject("视频拉流失败");
        }
      } else {
        showStatus("无法连接设备: " + (res.data.msg || "未知错误"));
        showLoading(false);
        return Promise.reject("连接失败");
      }
    }).then(result => {
      showLoading(false);

      if (result && result.type === true) {
        showStatus("视频播放中");
        videoPlayer.classList.add("active");

        // 保存播放器引用
        player = result.player;
      } else {
        showStatus("视频播放失败");
      }
    }).catch(err => {
      console.error("视频播放错误:", err);
      showStatus("视频播放错误: " + err);
      showLoading(false);
    });
  }

  // 切换音频对讲
  function toggleAudio() {
    if (isAudioActive) {
      stopAudio();
    } else {
      startAudio();
    }
  }

  // 开始音频对讲 - 使用zfjly.js中的方法
  function startAudio() {
    if (!currentHostbody || !wsConnected) {
      showStatus("请先选择设备或确保连接正常");
      return;
    }

    showLoading(true);
    showStatus("正在建立对讲连接...");

    // 使用zfjly的startAudioIntercom方法
    zfjly.startAudioIntercom(currentHostbody).then((result) => {
      if (result.success) {
        isAudioActive = true;
        audioBtn.textContent = "关闭对讲";
        audioBtn.setAttribute("class", "btn");
        showStatus("对讲已开启");
        showLoading(false);
        console.log("音频对讲启动成功:", result.message);
      } else {
        showLoading(false);
        showStatus("对讲启动失败");
      }
    }).catch(err => {
      console.error("对讲初始化失败:", err);
      showStatus("对讲初始化失败: " + err);
      showLoading(false);
    });
  }

  // 停止音频对讲 - 使用zfjly.js中的方法
  function stopAudio() {
    if (!isAudioActive) return;

    showStatus("正在关闭对讲...");

    // 使用zfjly的stopAudioIntercom方法
    zfjly.stopAudioIntercom(currentHostbody).then((result) => {
      console.log("音频对讲停止结果:", result.message);

      // 重置状态
      isAudioActive = false;
      audioBtn.textContent = "开启对讲";
      audioBtn.setAttribute("class", "btn");
      showStatus("对讲已关闭");
    }).catch(err => {
      console.error("停止音频对讲失败:", err);

      // 即使失败也重置状态
      isAudioActive = false;
      audioBtn.textContent = "开启对讲";
      audioBtn.setAttribute("class", "btn");
      showStatus("对讲已关闭");
    });
  }

  // 全局函数 - 供DynamicElement_audio使用（保持兼容性）
  window.stopAudio = function(hostbodyArr) {
    return zfjly.stopAudioInVideo(hostbodyArr);
  };

  window.startAudioInVideo = function(hostbody) {
    return zfjly.startAudioInVideo(hostbody);
  };

  window.sendMute = function(imei, mute) {
    return zfjly.sendMute(imei, mute, currentHostbody);
  };

  window.dataExample = []; // 全局数据数组，供DynamicElement_audio使用

  // 测试音频和声音功能的调试函数
  window.testAudioFunction = function() {
    console.log("开始测试音频和声音功能...");
    console.log("当前设备:", currentHostbody);
    console.log("连接状态:", wsConnected);
    console.log("音频状态:", isAudioActive);
    console.log("zfjly音频推流计数:", zfjly.isStartAudio);
    console.log("声音状态:", zfjly.getVolumeStatus());

    if (currentHostbody) {
      zfjly.getDeviceImei(currentHostbody).then(imei => {
        console.log("设备IMEI:", imei);
      }).catch(err => {
        console.log("获取IMEI失败:", err);
      });
    }
  };

  // 初始化麦克风
  function initMicrophone() {
    return new Promise((resolve, reject) => {
      navigator.getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia;

      if (!navigator.getUserMedia) {
        layui.use("layer", function () {
          var layer = layui.layer;
          layer.msg('<span style="font-size: 30px;">浏览器不支持音频输入</span>');
        });
        reject("浏览器不支持音频输入");
        return;
      }

      navigator.getUserMedia(
        { audio: true },
        function (stream) {
          recordersPlatform = new HZRecorder_pcm_push(stream, {});
          recorders = new HZRecorder_pcm(stream, {});
          resolve();
        },
        function (error) {
          console.error("麦克风访问失败:", error);
          reject("麦克风访问失败: " + (error.name || error.message || "未知错误"));
        }
      );
    });
  }

  function closeIframe() {
    // 关闭当前窗口/框架
    if (window.parent && window.parent.lay && window.parent.lay.closeIframeByNames) {
      window.parent.lay.closeIframeByNames(["videoTest"]);
    } else {
      try {
        window.parent.postMessage(JSON.stringify({type: "closeIframe", name: "videoTest"}), "*");
      } catch (e) {
        window.close();
      }
    }
  }

  // 停止所有流
  function stopAll() {
    showStatus("正在停止...");

    // 停止音频对讲
    if (isAudioActive) {
      stopAudio();
    }

    // 停止视频流
    if (currentHostbody && currentSn) {
      // 调用接口停止视频流
      const stopData = {
        hostbody_arr: Array.isArray(currentHostbody) ? currentHostbody : [currentHostbody],
        sn_arr: [currentSn]
      };

      console.log("调用stopLive接口停止视频流:", stopData);

      zfjly.stopLive(stopData).then(res => {
        if (res.data && res.data.code == 200) {
          console.log("成功停止视频流");
          closeIframe()
        } else {
          console.warn("停止视频流失败:", res.data);
          closeIframe()
        }
      }).catch(err => {
        console.error("停止视频流接口调用失败:", err);
        closeIframe()
      });

      // 重置sn值
      currentSn = null;
    }

    // 停止视频播放器
    if (player) {
      // 使用WebRTC播放器的正确关闭方法
      if (typeof player.close_play === 'function') {
        player.close_play(videoPlayer).then(() => {
          console.log("视频流已关闭");
        }).catch(err => {
          console.error("关闭视频流时出错:", err);
        });
      } else if (typeof player.close === 'function') {
        player.close();
        console.log("视频流已关闭");
      } else {
        console.warn("播放器没有有效的关闭方法");
        // 尝试直接清理视频元素
        if (videoPlayer) {
          videoPlayer.pause();
          videoPlayer.srcObject = null;
          videoPlayer.load();
        }
      }
      player = null;
    }

    // 重置视频元素样式
    if (videoPlayer) {
      videoPlayer.classList.remove("active");
      setTimeout(() => {
        closeIframe()
      },1000)
    }

    showStatus("已停止");
  }

  // 关闭窗口
  function closeWindow() {

    // 先停止所有流
    stopAll();

  }

  // 辅助函数：显示/隐藏加载状态
  function showLoading(show) {
    loading.style.display = show ? "flex" : "none";
  }

  // 辅助函数：显示状态文本
  function showStatus(text) {
    statusText.textContent = text;
    statusText.style.display = text ? "block" : "none";
    console.log("状态:", text);
  }

  // 辅助函数：设置按钮状态
  function setButtonsEnabled(enabled) {
    playBtn.disabled = !enabled;
    audioBtn.disabled = !enabled;
    stopBtn.disabled = !enabled;
  }

  // 添加拉流和音频流处理函数 - 从home.html移植过来的处理WebSocket消息的函数
  window.pullFlow_vms2 = function (ws, viewId) {
    console.log("pullFlow_vms2", ws, viewId);

    // 使用WebRTC播放
    zfjly.createWebRTCPlayer(videoPlayer, webRtcUrl).then((result) => {
      console.log("WebRTC拉流结果：", result);

      if (!result) {
        console.warn("WebRTC播放失败，可能连接异常");
        showStatus("拉流连接异常");
        return;
      }

      if (result.type == true) {
        videoPlayer.classList.add("active");
        player = result.player;

        // 保存来自WebSocket的sn值，如果存在的话
        if (zfjly.sn) {
          currentSn = zfjly.sn;
          console.log("WebSocket消息触发的视频流，保存SN:", currentSn);
        }

        showStatus("视频播放中");
      } else {
        showStatus("拉流失败");
      }
    }).catch(error => {
      console.error("WebRTC拉流过程中发生错误：", error);
      showStatus("拉流失败，请检查连接");
    });
  };

  // 音频流处理 - 使用zfjly.js中的音频对讲功能
  window.voice_pull_vms2 = function (wss, wsChannelId) {
    console.log("voice_pull_vms2", wss, wsChannelId);

    // 如果使用了zfjly的音频对讲功能，则不需要手动处理
    if (zfjly.isStartAudio > 0) {
      console.log("使用zfjly音频对讲功能处理音频流");
      return;
    }

    // 兼容旧的实现方式
    if (recorders) {
      recorders.openWebSocket(wss, wsChannelId);
      isAudioActive = true;
      audioBtn.textContent = "关闭对讲";
      showStatus("对讲已开启");
    }
  };

  // 预设点处理
  window.preset2 = function (Info) {
    console.log("收到预设点信息:", Info);
  };

  // 窗口消息处理
  window.addEventListener("message", function (event) {
    let newData;
    try {
      if (typeof event.data == "object") {
        newData = event.data;
      } else {
        newData = JSON.parse(event.data.argument || event.data);
      }

      if (newData.name === "openVideoTest" && newData.videoCode) {
        currentHostbody = Array.isArray(newData.videoCode) ? newData.videoCode[0] : newData.videoCode;

        // 如果已连接，直接开始播放
        if (wsConnected) {
          startVideo();
        } else {
          // 否则先登录再播放
          zfjly.loginZFJLY().then(() => {
            startVideo();
          });
        }
      } else if (newData.name === "ddhjVideoTest" && newData.videoCode) {
        currentHostbody = Array.isArray(newData.videoCode) ? newData.videoCode[0] : newData.videoCode;

        // 如果已连接，直接开始播放并对讲
        if (wsConnected) {
          startVideo();
          setTimeout(() => {
            startAudio();
          }, 2000);
        } else {
          // 否则先登录再播放并对讲
          zfjly.loginZFJLY().then(() => {
            startVideo();
            setTimeout(() => {
              startAudio();
            }, 2000);
          });
        }
      }
    } catch (e) {
      console.error("处理窗口消息出错:", e);
    }
  }, false);
</script>
</body>
</html>
