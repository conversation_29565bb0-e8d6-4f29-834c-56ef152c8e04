<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>任务详情弹窗</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <link rel="stylesheet" href="/static/css/viewCss/commonObjzhdd.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/Vue/vue-count-to.min.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/static/js/jslib/Emiter.js"></script>
    <script src="/elementui/js/index.js"></script>
    <style>
      ul,
      ul li {
        list-style: none;
      }

      .el-textarea__inner {
          display: block;
          resize: vertical;
          padding: 5px 15px;
          line-height: 1.5;
          box-sizing: border-box;
          width: 100%;
          color: #ffffff;
          background-color: transparent;
          border: 1px solid #DCDFE6;
          border-radius: 4px;
          transition: border-color .2s cubic-bezier(.645,.045,.355,1);
          font-size: 25px !important;
      }

      .rwgz-tc {
        width: 1500px;
        height: 1000px;
        background: url("/static/images/zhdd/bg_panel.png") no-repeat;
        background-size: 100% 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: column;
      }

      .rw-title {
        padding: 46px 3% 0;
        width: 95%;
        height: 60px;
        line-height: 60px;
      }

      .close {
        background: url("/static/images/zhdd/close.png") no-repeat;
        width: 34px;
        height: 34px;
      }

      .ql-indent-1 img {
        width: 100%;
      }

      img {
        width: 100%;
      }

      .blue {
        background: linear-gradient(
          to bottom,
          #cbf2ff,
          #ffffff,
          #00c0ff,
          #80e0ff,
          #ffffff
        );
        -webkit-background-clip: text;
        color: transparent;
      }

      .point {
        width: 19px;
        height: 19px;
        background: url("/static/citybrain/csdn/img/window-img/point.png");
        background-size: cover;
      }

      .check {
        width: 31px;
        height: 31px;
        background: url("/static/citybrain/csdn/img/window-img/checkcircleyuangou.png");
        background-size: cover;
        margin-left: 5px;
      }

      .content {
        width: 1400px;
        height: 850px;
        margin-top: 10px;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
      }

      .content-line {
        width: 2px;
        height: 706px;
        background-color: #00c0ff;
        opacity: 0.39;
      }

      .content-box {
        width: 600px;
        height: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-direction: column;
      }

      .content-name {
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }

      .name {
        margin-left: 21px;
        font-family: SourceHanSansCN-Bold;
        font-size: 40px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
      }

      .content-left-item {
        width: 566px;
        height: 90px;
        margin-top: 20px;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
      }

      .content-left-item-key {
        white-space: nowrap;
        font-family: SourceHanSansCN-Regular;
        font-size: 32px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #ffffff;
        margin-top: 24px;
      }

      .content-left-item-container {
        width: 400px;
        height: 100%;
        background-color: rgba(10, 97, 158, 0.2);
        border-radius: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .container-textContainer {
        width: 350px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-family: SourceHanSansCN-Regular;
        font-size: 32px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #ffffff;
        overflow-x: scroll;
      }

      .text {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          white-space: nowrap;
          margin-right: 15px;
          width: fit-content;
          flex-shrink: 0;
      }

      ::-webkit-scrollbar {
        width: 0;
        height: 0;
      }

      .time-line {
        width: 640px;
        height: 650px;
        margin-top: 60px;
        overflow-y: scroll;
      }

      .time-line-item {
        width: 100%;
        height: fit-content;
        display: flex;
        justify-content: space-evenly;
        align-items: flex-start;
        overflow: hidden;
      }

      .time-line-item-left {
        width: 245px;
        height: fit-content;
        font-family: SourceHanSansCN-Regular;
        font-size: 32px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #60e3ff;
        word-break: break-all;
        text-align: right;
      }

      .time-line-item-left2 {
          width: 300px;
          height: fit-content;
          font-family: SourceHanSansCN-Regular;
          font-size: 28px;
          font-weight: normal;
          font-stretch: normal;
          letter-spacing: 0px;
          color: #60e3ff;
          text-align: right;
      }

      .time-line-item-center {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: column;
        flex-shrink: 0;
        height: 150px;
        margin: 0 20px 0 20px;
      }

      .time-line-center-top {
        width: 21px;
        height: 21px;
        border-radius: 100px;
        background: url("/static/citybrain/csdn/img/window-img/point2.png");
        background-size: cover;
        margin-top: 10px;
        flex-shrink: 0;
      }

      .time-line-center-bottom {
        width: 3px;
        height: 100000px;
        background-color: #ffffff;
        flex-shrink: 0;
      }

      .time-line-item-right {
        width: 550px;
        height: fit-content;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        flex-direction: column;
      }

      .time-line-item-right-nameBox {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin: 0 0 29px 25px;
      }

      .time-line-item-right-point {
          width: 12px;
          height: 12px;
          background: #60E3FF;
          border-radius: 50%;
      }

      .time-line-item-right-name2-inner {
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: bold;
          color: #60E3FF;
          margin-left: 19px;
          white-space: nowrap;
      }

      .btns {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 280px;
      }

      .btn {
          width: 130px;
          height: 44px;
          background: rgba(29, 183, 255, 0.7);
          border-radius: 22px;
          font-size: 24px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #FFFFFF;
          cursor: pointer;
          line-height: 44px;
      }

      .time-line-item-right-name {
        font-family: SourceHanSansCN-Bold;
        font-size: 32px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #ffffff;
      }

      .time-line-item-right-name2 {
          font-family: SourceHanSansCN-Bold;
          font-size: 36px;
          font-weight: bold;
          font-stretch: normal;
          letter-spacing: 0px;
          color: #ffffff;

          background: linear-gradient(180deg, #FFFFFF 0%, #93DDFF 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
      }

      .time-line-item-right-container {
        width: 400px;
        /* height: 80px; */
        background-color: rgba(10, 97, 158, 0.2);
        font-family: SourceHanSansCN-Regular;
        font-size: 32px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #b8d3f1;
        /* line-height: 80px; */
        border-radius: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
        margin-bottom: 20px;
        padding: 10px 0;
      }

      .time-line-item-right-container2 {
          width: 550px;
          /* height: 80px; */
          background-color: rgba(10, 97, 158, 0.2);
          font-family: SourceHanSansCN-Regular;
          font-size: 32px;
          font-weight: normal;
          font-stretch: normal;
          letter-spacing: 0px;
          color: #b8d3f1;
          /* line-height: 80px; */
          border-radius: 10px;
          display: flex;
          justify-content: center;
          align-items: flex-start;
          margin-top: 20px;
          margin-bottom: 20px;
          padding: 10px 0;
          flex-direction: column;
      }

      .time-line-item-right-img {
        width: 205px;
        height: fit-content;
        border-radius: 10px;
        margin-bottom: 40px;
        /*overflow-y: scroll;*/
      }

      .time-line-item-right-img {
          margin: 15px 0 15px 25px;
      }

      .right-text-container {
        width: 380px;
        height: fit-content;
        /* height: 60px;
        line-height: 60px; */
        word-break: break-all;
        overflow-y: scroll;
        margin: 0 25px 0 25px;
      }

      .right-text-container2 {
          width: 500px;
          height: fit-content;
          /* height: 60px;
          line-height: 60px; */
          word-break: break-all;
          overflow-y: scroll;
          margin: 0 25px 0 25px;
          color: #ffffff;
      }

      .right-text-container2-time {
          font-size: 28px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #9DAEC4;
      }

      .isImg {
        height: 400px;
      }
      .el-image-viewer__img {
        height: fit-content;
        width: fit-content;
      }

      .moreContent {
          margin-bottom: 30px;
      }
    </style>
  </head>

  <body>
    <div id="taskxq" class="rwgz-tc">
      <div class="rw-title flex-between">
        <div class="fs-44 text-mid-yellow" id="rwTitle">任务详情</div>
        <div class="close cursor" @click="close"></div>
      </div>
      <!-- 接收对象、任务下达时间、执法人员、处置状态，右侧带滚动条 -->
      <div class="content">
        <div class="content-box">
          <div class="content-name blue">
            <div class="point"></div>
            <div class="name">基本信息</div>
          </div>
          <div class="content-left-item">
            <div class="content-left-item-key">任务来源：</div>
            <div class="content-left-item-container">
              <div class="container-textContainer">
                <div class="text">{{info.taskSource}}</div>
              </div>
            </div>
          </div>
          <div class="content-left-item">
            <div class="content-left-item-key">任务名称：</div>
            <div class="content-left-item-container">
              <div class="container-textContainer" :title="info.rwnr">
                <div class="text">{{info.rwnr}}</div>
              </div>
            </div>
          </div>
          <div class="content-left-item">
            <div class="content-left-item-key">响应级别：</div>
            <div class="content-left-item-container">
              <div class="container-textContainer">
                <div class="text">{{info.rwjb}}</div>
              </div>
            </div>
          </div>
          <div class="content-left-item">
            <div class="content-left-item-key">任务内容：</div>
            <div class="content-left-item-container">
              <div class="container-textContainer" :title="info.rwnr">
                <div class="text">{{info.rwnr}}</div>
              </div>
            </div>
          </div>
          <div class="content-left-item">
            <div class="content-left-item-key">接受对象：</div>
            <div class="content-left-item-container">
              <div class="container-textContainer" :title="(info.zfjb.map(item => item.name)).join(',')">
                <div class="text" v-for="(item,i) in info.zfjb" :key="i">
                  <div>{{item.name}}</div>
                  <div :class="{check:item.status == 1}"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="content-left-item">
            <div class="content-left-item-key">下达时间：</div>
            <div class="content-left-item-container">
              <div class="container-textContainer">{{info.startTime}}</div>
            </div>
          </div>
          <div class="content-left-item">
            <div class="content-left-item-key">处置状态：</div>
            <div class="content-left-item-container">
              <div class="container-textContainer">
                {{setData(info.status)}}
              </div>
            </div>
          </div>
        </div>
        <div class="content-line"></div>
<!--        <div class="content-box">-->
<!--          <div class="content-name blue">-->
<!--            <div class="point"></div>-->
<!--            <div class="name">处置信息</div>-->
<!--          </div>-->
<!--          <div class="time-line">-->
<!--            <div-->
<!--              class="time-line-item"-->
<!--              v-for="(item,i) in info.persons"-->
<!--              :key="i"-->
<!--            >-->
<!--              <div class="time-line-item-left">{{item.createTime}}</div>-->
<!--              <div-->
<!--                class="time-line-item-center"-->
<!--                :class="{isImg:item.fileUrl != null && item.fileUrl != ''}"-->
<!--              >-->
<!--                <div class="time-line-center-top"></div>-->
<!--                <div class="time-line-center-bottom" v-show="i != info.persons.length && info.persons[info.persons.length - 1].type == 3"></div>-->
<!--                <div class="time-line-center-bottom" v-show="i != info.persons.length - 1 && info.persons[info.persons.length - 1].type != 3"></div>-->
<!--              </div>-->
<!--              <div class="time-line-item-right">-->
<!--                <div class="time-line-item-right-name">{{item.name}}</div>-->
<!--                <div class="time-line-item-right-container">-->
<!--                  <div class="right-text-container">{{item.content}}</div>-->
<!--                </div>-->
<!--                <div class="time-line-item-right-img" v-if="item.fileUrl">-->
<!--                  <el-image-->
<!--                    style="width: 205px"-->
<!--                    :src="item.fileUrl"-->
<!--                    fit="cover"-->
<!--                    :preview-src-list="[item.fileUrl]"-->
<!--                  >-->
<!--                  </el-image>-->
<!--                </div>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div-->
<!--              class="time-line-item"-->
<!--              v-show="info.persons[info.persons.length - 1].type == 3"-->
<!--            >-->
<!--              <div class="time-line-item-left">{{getNow}}</div>-->
<!--              <div-->
<!--                class="time-line-item-center"-->
<!--              >-->
<!--                <div class="time-line-center-top"></div>-->
<!--              </div>-->
<!--              <div class="time-line-item-right">-->
<!--                <div style="display: flex;justify-content: space-between;align-items: center;width: 100%;">-->
<!--                  <span class="time-line-item-right-name">结案</span>-->
<!--                  <div class="btns" v-show="!editShow">-->
<!--                    <div class="btn" @click="resetEvent">重新处置</div>-->
<!--                    <div class="btn" @click="confirmEvent">确认结案</div>-->
<!--                  </div>-->
<!--                  <div class="btns" v-show="editShow">-->
<!--                    <div class="btn" @click="editShow = false">取消</div>-->
<!--                    <div class="btn" @click="handle">确定</div>-->
<!--                  </div>-->
<!--                </div>-->
<!--                <div class="time-line-item-right-container" v-show="editShow">-->
<!--                  <div class="right-text-container">-->
<!--                    <el-input-->
<!--                      style="background: transparent;font-size: 20px !important;color: #ffffff"-->
<!--                      type="textarea"-->
<!--                      :rows="6"-->
<!--                      placeholder="请输入内容"-->
<!--                      v-model="textarea">-->
<!--                    </el-input>-->
<!--                  </div>-->
<!--                </div>-->
<!--              </div>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->



        <!--test-->
        <div class="content-box">
          <div class="content-name blue">
            <div class="point"></div>
            <div class="name">处置信息</div>
          </div>
          <div class="time-line">
            <div
              class="time-line-item"
              v-for="(item,i) in info.persons"
              :key="i"
            >
              <div
                class="time-line-item-center"
                :class="{isImg:item.fileUrl != null && item.fileUrl != ''}"
              >
                <div class="time-line-center-top"></div>
                <div class="time-line-center-bottom" v-show="info.persons.length != 0?i != info.persons.length && info.persons[info.persons.length - 1].type == 3:false"></div>
                <div class="time-line-center-bottom" v-show="info.persons.length != 0?i != info.persons.length - 1 && info.persons[info.persons.length - 1].type != 3:false"></div>
              </div>
              <div class="time-line-item-right">
                <div style="display: flex;justify-content: space-between;align-items: center;width: 100%">
                  <div class="time-line-item-right-name2">{{item.name}}</div>
                  <div class="time-line-item-left2">{{item.createTime}}</div>
                </div>
                <div v-if="item.list">
                  <div class="time-line-item-right-container2" v-for="(item2,j) in item.list">
                    <div class="time-line-item-right-nameBox">
                      <div class="time-line-item-right-point"></div>
                      <div class="time-line-item-right-name2-inner">{{item2.name}}</div>
                    </div>
                    <div v-for="(content,k) in item2.contentList" :class="{moreContent:k != item2.contentList.length - 1}" style="margin-left: 30px">
                      <div class="right-text-container2">内容：{{content.content}}</div>
                      <div class="right-text-container2 right-text-container2-time">{{content.createTime}}</div>
                      <div v-if="content.fileUrl">
                        <div class="time-line-item-right-img" v-for="(imgItem,i) in content.fileUrl.split(',')">
                          <el-image
                            style="width: 205px"
                            :src="imgItem"
                            fit="cover"
                            :preview-src-list="content.fileUrl.split(',')"
                          >
                          </el-image>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="time-line-item-right-container2" v-else>
                  <div class="right-text-container2">内容：{{item.content}}</div>
                </div>
              </div>
            </div>
            <div class="time-line-item" v-show="info.persons.length != 0?info.persons[info.persons.length - 1].type == 3 && info.needJa == 1:false">
              <div class="time-line-item-center" style="height: 70px">
                <div class="time-line-center-top"></div>
              </div>
              <div class="time-line-item-right">
                <div style="display: flex;justify-content: space-between;align-items: center;width: 100%">
                  <div class="time-line-item-right-name2">结案</div>
                  <div class="time-line-item-left2" v-show="!(info.persons.length != 0?info.persons[info.persons.length - 1].type == 3 && info.needJa == 1:false)">{{getNow}}</div>
                </div>
              </div>
            </div>
            <div style="margin-left: 25px" v-show="info.persons.length != 0?info.persons[info.persons.length - 1].type == 3 && info.needJa == 1:false">
              <div style="display: flex;justify-content: space-between;align-items: center;width: 100%;">
                <div class="btns" v-show="!editShow">
                  <div class="btn" @click="resetEvent">重新处置</div>
                  <div class="btn" @click="confirmEvent">确认结案</div>
                </div>
                <div class="btns" v-show="editShow">
                  <div class="btn" @click="editShow = false">取消</div>
                  <div class="btn" @click="handle">确定</div>
                </div>
              </div>
              <div class="time-line-item-right-container2" v-show="editShow">
                <div class="right-text-container2">
                  <el-input
                    style="background: transparent;font-size: 20px !important;color: #ffffff"
                    type="textarea"
                    :rows="6"
                    placeholder="请输入内容"
                    v-model="textarea">
                  </el-input>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      let vm = new Vue({
        el: "#taskxq",
        data: {
          id:"",
          params: {},
          info: {
            rwnr: "任务内容",
            rwjb: "级别",
            zfjb: [],
            persons: [],
            status: null,
            startTime: null,
            taskSource: null,
            needJa:"",
            title:""
          },
          textarea:"", //结案信息
          eventType:"",
          editShow:false,
          timelinelist: [
            {
              "searchValue": null,
              "createBy": null,
              "createTime": "2023-09-28 15:14:02",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "params": {},
              "id": 12720,
              "mobile": null,
              "name": "系统",
              "step": null,
              "content": "基层事件下发任务",
              "fileUrl": null,
              "office": null,
              "role": null,
              "workNoticeId": 2257,
              "type": 1,
              "msg": null
            },
            {
              "searchValue": null,
              "createBy": null,
              "createTime": "2023-09-28 15:14:02",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "params": {},
              "id": 12721,
              "mobile": null,
              "name": "胡江飞",
              "step": null,
              "content": "已受理",
              "fileUrl": null,
              "office": null,
              "role": null,
              "workNoticeId": 2257,
              "type": 4,
              "msg": null,
              "list": [
                {
                  "searchValue": null,
                  "createBy": null,
                  "createTime": "2023-09-28 15:14:02",
                  "updateBy": null,
                  "updateTime": null,
                  "remark": null,
                  "params": {},
                  "id": 12720,
                  "mobile": null,
                  "name": "系统",
                  "step": null,
                  "contentList": [
                    {createTime:"2023.05.26 14:00:00",content:"基层事件下发任务"},
                    {createTime:"2023.05.26 14:00:00",content:"基层事件下发任务"},
                  ],
                  "fileUrl": null,
                  "office": null,
                  "role": null,
                  "workNoticeId": 2257,
                  "type": 1,
                  "msg": null
                },
                {
                  "searchValue": null,
                  "createBy": null,
                  "createTime": "2023-09-28 15:14:02",
                  "updateBy": null,
                  "updateTime": null,
                  "remark": null,
                  "params": {},
                  "id": 12721,
                  "mobile": null,
                  "name": "胡江飞",
                  "step": null,
                  "contents": [
                    {time:"2023.05.26 14:00:00",value:"已受理"},
                  ],
                  "fileUrl": null,
                  "office": null,
                  "role": null,
                  "workNoticeId": 2257,
                  "type": 4,
                  "msg": null
                }
              ]
            },
            {
              "searchValue": null,
              "createBy": null,
              "createTime": "2023-09-28 20:47:56",
              "updateBy": null,
              "updateTime": null,
              "remark": null,
              "params": {},
              "id": 12725,
              "mobile": null,
              "name": "结案",
              "step": null,
              "content": "经处置，小鱼儿有限公司付清耿欢等4人劳务费1000元，全部结清，双方不存在任何纠纷。",
              "fileUrl": null,
              "office": null,
              "role": null,
              "workNoticeId": 2257,
              "type": 4,
              "msg": null
            }
          ]
        },
        computed: {
          //获取当前格式为YYYY-MM-DD HH:MM:SS年月日时分秒
          getNow() {
            return this.Format("yyyy-MM-dd hh:mm:ss",new Date());
          },
        },

        mounted() {
          let that = this;
          window.addEventListener("message", function (event) {
            //子获取父消息
            let newData;
            if (typeof event.data == "object") {
              newData = event.data;
            } else {
              newData = JSON.parse(event.data.argument);
            }
            // that.id = newData.id;
            that.params = newData;
            that.getDetail(that.params);
          });
          // that.getDetail(96);
        },
        methods: {
          //重新处置
          resetEvent() {
            this.eventType = 2;
            this.editShow = true;
          },
          //确认结案
          confirmEvent() {
            this.eventType = 1;
            this.editShow = true;
          },
          //办理
          handle() {
            if (this.textarea.trim() == "") {
              this.$message.warning("内容不能为空！")
            } else {
              axios({
                method: "post",
                url: baseURL.url + "/adm-api/pub/provincial/CommandXzzfj/closeCase",
                data: {
                  id: this.params.id,
                  content: this.textarea,
                  type:this.eventType
                },
              }).then((res) => {
                console.log(res);
                if (res.data.code == 200) {
                  this.$message.success("办理成功")
                  this.editShow = false;
                  this.getDetail(this.params);
                }
              })
            }
          },
          //获取详情数据
          getDetail(params) {
            let that = this;
            $api2Get("/xzzfj/workNotice/getWorkNoticeDetailsNew",params).then((res) => {
              console.log(res);
              this.info.rwnr = res.data.data.msg;
              this.info.rwjb = res.data.data.level;
              this.info.zfjb = res.data.data.personList;
              this.info.persons = res.data.data.timelineList;
              this.info.needJa = res.data.data.needJa;
              this.info.status = res.data.data.status;
              this.info.startTime = res.data.data.startTime;
              this.info.taskSource = res.data.data.taskSource;
              this.info.title = res.data.data.title;
            })
            // axios({
            //   method: "get",
            //   url:
            //     baseURL.url +
            //     "/xzzfj/workNotice/getWorkNoticeDetailsNew",
            //     // "/adm-api/pub/provincial/CommandXzzfj/getWorkNoticeDetails",
            //   params: params,
            // }).then((res) => {
            //   console.log(res);
            //   this.info.rwnr = res.data.data.msg;
            //   this.info.rwjb = res.data.data.level;
            //   this.info.zfjb = res.data.data.personList;
            //   this.info.persons = res.data.data.timelineList;
            //   this.info.status = res.data.data.status;
            //   this.info.startTime = res.data.data.startTime;
            //   this.info.taskSource = res.data.data.taskSource;
            // });
          },
          setData(item) {
            if (item === 0 || item === 1) {
              return "待处置";
            }
            if (item === 2) {
              return "已反馈";
            }
            if (item === 3) {
              return "确认结案";
            }
            if (item === 4) {
              return "重新处置";
            }
            return "";
          },
          close() {
            window.parent.lay.closeIframeByNames(["taskxq"]);
          },
          Format(fmt,date) { // author: meizz
            var o = {
              "M+": date.getMonth() + 1, // 月份
              "d+": date.getDate(), // 日
              "h+": date.getHours(), // 小时
              "m+": date.getMinutes(), // 分
              "s+": date.getSeconds(), // 秒
              "q+": Math.floor((date.getMonth() + 3) / 3), // 季度
              "S": date.getMilliseconds() // 毫秒
            };
            if (/(y+)/.test(fmt))
              fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
            for (var k in o)
              if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            return fmt;
          }
        },
      });
    </script>
  </body>
</html>
