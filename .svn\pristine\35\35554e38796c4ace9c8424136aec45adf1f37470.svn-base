const loginUserInfo = {};
const loginUser = {
  username: "liyun",
  password: window.btoa("liyun@123"),
};
(function () {
  // $.ajax({
  //   url: 'getEnvConfig',
  //   dataType: 'json',
  //   type: 'get',
  //   async: false,
  //   success: function (data) {
  //     console.log(data.env)
  //     localStorage.setItem("env", data.env)
  //   },
  //   error: function (e) {
  //     console.log(e)
  //   },
  // })
  $post("/adm-api/login", loginUser).then((res) => {
    console.log(res,"/adm-api/login");
    sessionStorage.setItem("token", res.data.portToken);
    sessionStorage.setItem("Authorization", res.data.token);
    sessionStorage.setItem("role", res.data.userId);
    loginUserInfo.userId = res.data.userId;
    loginUserInfo.token = res.data.portToken;
    loginUserInfo.Authorization = res.data.token;
    $.ajax({
      url: baseURL.admApi + "/auth/getUserInfo?token=" + res.data.portToken,
      dataType: "json",
      type: "get",
      success: function (res) {
        if (res.code == 200) {
          loginUserInfo.deptName = res.data.deptName;
          loginUserInfo.nickName = res.data.nickName;
        }
      },
      error: function (e) {},
    });
  });
})();
window.loginUserInfo = loginUserInfo;
var login = new Vue({
  el: "#main",
  data: {
    loginUser: {
      username:"",
      password: ""
    },
    flag:false,
    componentsList:[
      {
        key:"zhdd",
        components:[
          {
            name: "zhdd_left",
            url: "/static/citybrain/zhdd/zhdd_left.html",
            width: "1030px",
            height: "1900px",
            left: "20px",
            right: "unset",
            top: "220px"
          },
          {
            name: "zhdd_right",
            url: "/static/citybrain/zhdd/zhdd_right.html",
            width: "1030px",
            height: "1900px",
            left: "unset",
            right: "20px",
            top: "220px"
          },
          {
            name: "zhdd_middle",
            url: "/static/citybrain/zhdd/zhdd_middle.html",
            width: "1760px",
            height: "150px",
            left: "1037px",
            top: "1400px",
            zIndex: "666",
          },
          {
            name: "zhdd_bottom",
            url: "/static/citybrain/zhdd/zhdd_bottom.html",
            width: "1760px",
            height: "525px",
            left: "1037px",
            top: "1600px",
          },
        ]
      },
      {
        key:"zfts",
        components:[
          {
            name: "zfts_left",
            url: "/static/citybrain/zfts/zfts_left.html",
            width: "1030px",
            height: "1900px",
            left: "20px",
            right: "unset",
            top: "220px"
          },
          {
            name: "zfts_right",
            url: "/static/citybrain/zfts/zfts_right.html",
            width: "1030px",
            height: "1900px",
            left: "unset",
            right: "20px",
            top: "220px"
          },
          {
            name: "zfts_bottom",
            url: "/static/citybrain/zfts/zfts_bottom.html",
            width: "1760px",
            height: "525px",
            left: "1037px",
            top: "1600px",
          },
        ]
      },
      {
        key:"yyjc",
        components:[
          {
            name: "yyjc_index",
            url: "/static/citybrain/yyjc/yyjc_index.html",
            width: "3840px",
            height: "2160px",
            left: "0",
            top: "0"
          }
        ]
      },
      {
        key:"ajhf",
        components:[
          {
            name: "ajhf_left",
            url: "/static/citybrain/ajhf/ajhf_left.html",
            width: "1030px",
            height: "1900px",
            left: "20px",
            right: "unset",
            top: "220px"
          },
          {
            name: "ajhf_right",
            url: "/static/citybrain/ajhf/ajhf_right.html",
            width: "1030px",
            height: "1900px",
            left: "unset",
            right: "20px",
            top: "220px"
          },
          {
            name: "ajhf_bottom",
            url: "/static/citybrain/ajhf/ajhf_bottom.html",
            width: "1760px",
            height: "525px",
            left: "1037px",
            top: "1600px",
          },
        ]
      }
    ],
    page:""
  },
  created() {
    //url包含参数
    if (JSON.stringify(this.getUrlParams(window.location.href)) != "{}") {
      let params = this.getUrlParams(window.location.href)
      if (params.csdnToken) {
        $api2Get("/token/authentication",{authorizeToken: params.csdnToken}).then(res => {
          this.loginUser.username = res.data.data.userName;
          this.loginUser.password = res.data.data.password;
          this.Tologin(1)
        })
      } else {
        $api2Get("/token/authentication",{appId: params.appid,authorizeToken:params.authorizeToken}).then(res => {
          if (res.data.code == 200) {
            this.loginUser.username = res.data.data.userName;
            this.loginUser.password = window.atob(res.data.data.password);
            this.Tologin(2,params)
          } else {
            this.$message.warning(res.data.msg)
          }
        })
      }
    }
  },
  methods:{
    //type:1 大脑跳转免登 type:2 县市区链接免登 type:3 正常登录流程
    Tologin(type,params) {
      const that = this;
      axios({url:baseURL2.url + "/login", data:{username:this.loginUser.username,password:type == 1?this.loginUser.password:window.btoa(this.loginUser.password)},method: "post"}).then(res => {
        if (res.data.code == 200) {
          Cookies.set("Admin-Token",res.data.token)
          localStorage.setItem("Admin-Token",res.data.token)
          localStorage.setItem('bigScreen', res.data.bigScreen)
          this.getArea(res.data.token,type,params)
        } else {
          that.$message.warning('用户名或密码错误!')
        }
      }).catch(() => {
        that.$message.error('系统错误,响应超时')
      })
    },
    //获取URL中的参数
    getUrlParams(url) {
      // 创建空对象存储参数
      let obj = {};
      if (url.split('?')[1]) {
        // 通过 ? 分割获取后面的参数字符串
        let urlStr = url.split('?')[1]
        // 再通过 & 将每一个参数单独分割出来
        let paramsArr = urlStr.split('&')
        for(let i = 0,len = paramsArr.length;i < len;i++){
          // 再通过 = 将每一个参数分割为 key:value 的形式
          let arr = paramsArr[i].split('=')
          obj[arr[0]] = arr[1];
        }
      }
      return obj
    },
    //获取用户所属区县
    getArea(token,type,params) {
      const that = this;
      $.ajax({
        url: baseURL2.url + "/xzzfj/xzzfjZfsb/getArea",
        dataType: "json",
        type: "get",
        headers: {
          "Content-Type": "application/json;charset=utf-8",
          "Authorization": token
        },
        success: function (res) {
          if (res.code == 200) {
            localStorage.setItem('city',res.data.area)
            localStorage.setItem('adminCity',res.data.area)
            that.$message.success('登录成功!')
            type == 2 && params.page?that.OpenMdJumpPage(params):that.OpenDefaultPage();
          }
        },
        error: function (e) {},
      });
    },
    //获取免登的对应跳转页
    OpenMdJumpPage(params) {
      const that = this;
      const defaultPage = {
        "img":"/static/images/login/驾驶舱.png",
        "back":"/static/images/login/驾驶舱_back.png",
        "name":"驾驶舱","key":"home",
        "components":[
          {"name":"home_left",
            "url":"/static/citybrain/home/<USER>",
            "width":"1030px",
            "height":"1900px",
            "left":"20px",
            "right":"unset",
            "top":"220px"
          },
          {
            "name":"home_right",
            "url":"/static/citybrain/home/<USER>",
            "width":"1030px",
            "height":"1900px",
            "left":"unset",
            "right":"20px",
            "top":"220px"
          },
          {
            "name":"home_top",
            "url":"/static/citybrain/home/<USER>",
            "width":"828px",
            "height":"200px",
            "left":"calc(50% - 414px)",
            "top":"200px"
          },
          {
            "name":"home_bottom",
            "url":"/static/citybrain/home/<USER>",
            "width":"1760px",
            "height":"525px",
            "left":"1037px",
            "top":"1600px"
          }]}
      if (params.page == 'ssyj') {
        localStorage.setItem('currentPage', JSON.stringify(defaultPage));
        $api2Get("/token/getTokenInfo",{jmppage:"home"}).then(res => {
          if (res.data.code == 200) {
            // window.open(res.data.data.url);
            window.location.href = res.data.data.url;
          }
        })
      } else {
        $.ajax({
          url: baseURL.url + "/api/?indexid=xzzfj_sy_cd_tz",
          type: "get",
          headers: {
            "Content-Type": "application/json;charset=UTF-8",
            portToken: hasToken(),
            ptid: "PT0001",
          },
          dataType: "json",
          data: {area:localStorage.getItem("adminCity"),name:params.page},
          success: function (res) {
            that.page = res.data
            that.page.forEach((item,i) => {
              item.components = that.componentsList.find(item2 => item2.key == item.key)?that.componentsList.find(item2 => item2.key == item.key).components:""
              localStorage.setItem('currentPage', JSON.stringify(that.page[0]))
              window.location.href = "/index.html"
            })
          },
          error: function (e) {

          },
        });
      }
    },
    // 加载模块
    OpenDefaultPage() {
      window.location.href = "/home2.html";
    },
    formatDate (date = new Date()) {
      let year = date.getFullYear();
      let month = (date.getMonth() + 1) ;
      let day = date.getDate();
      let week = ['日', '一', '二', '三', '四', '五', '六'][date.getDay()];

      const dateInfo = {
        date: new Date(date),
        dateStr: `${year}-${month.toString().padStart(2,'0')}-${day.toString().padStart(2,'0')}`,
        year,
        month,
        day,
        week,
        isToday:false
      }

      const today = new Date();
      // 判断是否为当天
      if (today.getFullYear() === year && (today.getMonth() + 1) === month && today.getDate() === day) {
        dateInfo['isToday'] = true;
      }
      return dateInfo;
    }
  }
});
