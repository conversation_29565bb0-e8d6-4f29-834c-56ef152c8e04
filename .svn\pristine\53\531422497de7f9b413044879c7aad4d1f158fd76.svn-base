<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>添加水波纹效果图层</title>

    <link
      rel="stylesheet"
      href="https://dev.arcgisonline.cn/jsapi/4.24/esri/themes/light/main.css"
    />
    <script src="./index.js" type="module"></script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        right: 20px;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }
      .color-btn {
        border: 1px solid rgb(173, 172, 172);
        width: 40px;
        height: 20px;
        cursor: pointer;
      }

      #navy {
        background-color: #25427c;
      }

      #green {
        background-color: #039962;
      }

      #turqoise {
        background-color: #a2f9f5;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv"></div>
    <div class="tools">
      <div>
        <p>步骤1：添加图层</p>
        <button onclick="addLayer();">添加水波纹图层</button>
        <p>步骤2：添加水波纹</p>
        <button onclick="addLayer2();">添加水波纹效果</button>
        <p>步骤3：设置波纹大小值（0-360）</p>
        <h4>波浪强度</h4>
        <input
          type="radio"
          name="waveStrengthRadio"
          value="calm"
          id="calm"
        /><label for="calm">平静的</label><br />
        <input
          type="radio"
          name="waveStrengthRadio"
          value="rippled"
          id="rippled"
        /><label for="rippled">扩散的</label><br />
        <input
          type="radio"
          name="waveStrengthRadio"
          value="slight"
          id="slight"
        /><label for="slight">轻微的</label><br />
        <input
          type="radio"
          name="waveStrengthRadio"
          value="moderate"
          id="moderate"
          checked
        /><label for="moderate">适度的</label><br />
        <h4>波浪颜色</h4>
        <button id="navy" class="color-btn"></button>
        <button id="green" class="color-btn"></button>
        <button id="turqoise" class="color-btn"></button>
      </div>
    </div>
  </body>
  <script>
    let layer;
    let color = "#25427c";
    let waveStrength = "moderate";
    async function addLayer() {
      ArcGisUtils.addWaterLayer();
    }
    async function addLayer2() {
      ArcGisUtils.changeWaterLayer(color, waveStrength);
    }
    const waveStrengthRadio = document.getElementsByName("waveStrengthRadio");

    for (let i = 0; i < waveStrengthRadio.length; i++) {
      const element = waveStrengthRadio[i];
      element.addEventListener("change", (event) => {
        waveStrength = event.target.value;
        ArcGisUtils.changeWaterLayer(color, waveStrength);
      });
    }

    document.getElementById("navy").addEventListener("click", () => {
      color = "#25427c";
      ArcGisUtils.changeWaterLayer(color, waveStrength);
    });
    document.getElementById("green").addEventListener("click", () => {
      color = "#039962";
      ArcGisUtils.changeWaterLayer(color, waveStrength);
    });
    document.getElementById("turqoise").addEventListener("click", () => {
      color = "#a2f9f5";
      ArcGisUtils.changeWaterLayer(color, waveStrength);
    });
  </script>
</html>
