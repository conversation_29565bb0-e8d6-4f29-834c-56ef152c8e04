
const THREE = window.THREE_r116;

export default class FountainTHREERenderer {
  constructor() {
    this.view = null;
    this.locations = [];
    this.models = [];
    this.loaded = false;
    this.fountainObj = null;
    this.fountains = [];
    this.fountainAnims = [];
    this.externalRenderers = null;
  }

  load() {
    const that = this;

    return new Promise(resolve => {
      new THREE.TextureLoader().load('./mesh/fountain/Fountain_Diffuse-min.png', function(texture) {

        const fbxLoader = new THREE.FBXLoader();
        fbxLoader.load('./mesh/fountain/Prop_Fountain.fbx', function(obj) {
          obj.scale.set(0.5, 0.5, 0.5);
          obj.rotateX(Math.PI / 2);

          that.fountainObj = obj;
          that.mixer = new THREE.AnimationMixer(obj);

          obj.traverse(function(child){
            if (child.isMesh) {
              child.material.map = texture;
              child.castShadow = true;
              child.receiveShadow = true;
            }
          })

          that.loaded = true;
          that.loadPromise = null;
          resolve();
        })
      })
    })
  }

  createFountainsAtLoc() {
    if (this.fountainAnims && this.fountainAnims.length > 0) {
      this.fountainAnims.forEach(anim => {
        anim.stop();
      });
    }

    if (this.fountains && this.fountains.length > 0) {
      this.fountains.forEach(fountain => {
        this.scene.remove(fountain);
      })
    }

    this.fountains = [];
    this.fountainAnims = [];

    if (this.locations.length === 0) return;
    const loc = this.locations[this.locations.length - 1];

      const renderLoc = [];

      this.externalRenderers.toRenderCoordinates(
        this.view,
        [loc.x, loc.y, 10],
        0,
        this.view.spatialReference,
        renderLoc,
        0,
        1,
      );

      const fount = this.fountainObj;
      fount.position.set(renderLoc[0], renderLoc[1], renderLoc[2]);
      this.fountains.push(fount);
      this.scene.add(fount);

      const mixer = this.mixer;
      const clip = mixer.clipAction(fount.animations[0]);
      this.fountainAnims.push(clip);
      clip.play();

  }

  createModels() {
    if (!this.loaded  ) {
      if (!this.loadPromise) {
        this.loadPromise = this.load();
        this.loadPromise.then(() => {
          this.createFountainsAtLoc();
        });
      }
    } else {
      this.createFountainsAtLoc();
    }
  }

  setup(context) {
    this.renderer = new THREE.WebGLRenderer({
      context: context.gl, // 可用于将渲染器附加到已有的渲染环境(RenderingContext)中
      alpha: true,
      depthTest: true,
      transparent: true,
      premultipliedAlpha: false, // renderer是否假设颜色有 premultiplied alpha. 默认为true
    });
    this.renderer.setPixelRatio(window.devicePixelRatio); // 设置设备像素比。通常用于避免HiDPI设备上绘图模糊
    this.renderer.setViewport(0, 0, this.view.width, this.view.height); // 视口大小设置
    // 防止Three.js清除ArcGIS JS API提供的缓冲区。
    this.renderer.autoClear = false;

    // ArcGIS JS API渲染自定义离屏缓冲区，而不是默认的帧缓冲区。
    // 我们必须将这段代码注入到three.js运行时中，以便绑定这些缓冲区而不是默认的缓冲区。
    const originalSetRenderTarget = this.renderer.setRenderTarget.bind(
      this.renderer,
    );
    this.renderer.setRenderTarget = function (target) {
      originalSetRenderTarget(target);
      if (target == null) {
        // 绑定外部渲染器应该渲染到的颜色和深度缓冲区
        context.bindRenderTarget();
      }
    };

    this.scene = new THREE.Scene(); // 场景
    this.camera = new THREE.PerspectiveCamera(); // 相机

    // setup scene lighting
    this.ambient = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(this.ambient);
    this.sun = new THREE.DirectionalLight(0xffffff, 0.5);
    this.scene.add(this.sun);

    this.createModels()
    this.clock = new THREE.Clock();
    context.resetWebGLState();
  }

  render(context) {
    // 更新相机参数
    const cam = context.camera;
    this.camera.position.set(cam.eye[0], cam.eye[1], cam.eye[2]);
    this.camera.up.set(cam.up[0], cam.up[1], cam.up[2]);
    this.camera.lookAt(
      new THREE.Vector3(cam.center[0], cam.center[1], cam.center[2]),
    );
    // 投影矩阵可以直接复制
    this.camera.projectionMatrix.fromArray(cam.projectionMatrix);

    let delta = this.clock.getDelta();
    if (this.mixer) {
      this.mixer.update(delta);
    }


    // if (delta < 0.2) { // 1秒 / 5帧 = 0.2 秒/帧
    //   this.particleEngines.forEach(particleEngine => {
    //     particleEngine.update(delta * 0.5);
    //   });
    // } else {
    //   this.createSmokes();
    // }

    // if (this.locations.length > this.models.length) {
    //   this.createModels();
    // }

    var l = context.sunLight;
    this.sun.position.set(l.direction[0], l.direction[1], l.direction[2]);
    this.sun.intensity = l.diffuse.intensity;
    this.sun.color = new THREE.Color(l.diffuse.color[0], l.diffuse.color[1], l.diffuse.color[2]);

    this.ambient.intensity = l.ambient.intensity;
    this.ambient.color = new THREE.Color(l.ambient.color[0], l.ambient.color[1], l.ambient.color[2]);

    // 绘制场景
    this.renderer.state.reset();
    this.renderer.render(this.scene, this.camera);
    // 请求重绘视图。
    this.externalRenderers.requestRender(this.view);
    // cleanup
    context.resetWebGLState();
  }
}
