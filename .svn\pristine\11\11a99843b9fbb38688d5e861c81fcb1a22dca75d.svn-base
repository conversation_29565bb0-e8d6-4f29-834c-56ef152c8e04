@font-face {
  font-family: "iconfont"; /* Project id 3894746 */
  src: url('iconfont.woff2?t=1684468812431') format('woff2'),
       url('iconfont.woff?t=1684468812431') format('woff'),
       url('iconfont.ttf?t=1684468812431') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-sousuo:before {
  content: "\e61a";
}

.icon-yuan:before {
  content: "\e629";
}

.icon-qingli:before {
  content: "\e60d";
}

.icon-rili:before {
  content: "\e628";
}

.icon-shangxia:before {
  content: "\e608";
}

