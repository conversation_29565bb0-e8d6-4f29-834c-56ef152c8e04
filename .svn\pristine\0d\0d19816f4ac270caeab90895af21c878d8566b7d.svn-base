import Weather from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/widgets/Weather.js";

/**
 * 创建天气微件
 * @param {*} view
 * @param {*} container
 * @returns Weather实例
 */
async function createWeatherWidget(view, container) {
  console.log(view, container);

  if (!view || !container) {
    console.error("天气微件有必要参数没传");
  }
  await new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve();
    }, 300);
  });
  return new Weather({ view: view, container });
}

export default createWeatherWidget;
