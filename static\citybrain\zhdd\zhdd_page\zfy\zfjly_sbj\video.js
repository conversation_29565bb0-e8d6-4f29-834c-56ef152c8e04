const zfySBJ = {
  m_IdtApi: null,
  id_video_my: null,
  id_video_peer: null,
  id_video_watch: null,

  myBrowser(){
    var userAgent = navigator.userAgent //取得浏览器的userAgent字符串
    var isOpera = userAgent.indexOf("Opera") > -1
    if (isOpera) {
      return "Opera";
    } //判断是否Opera浏览器
    if (userAgent.indexOf("Firefox") > -1) {
      return "FF";
    } //判断是否Firefox浏览器
    if (userAgent.indexOf("Chrome") > -1) {
      return "Chrome";
    }
    if (userAgent.indexOf("Safari") > -1) {
      return "Safari";
    } //判断是否Safari浏览器
    if (
      userAgent.indexOf("compatible") > -1 &&
      userAgent.indexOf("MSIE") > -1 &&
      !isOpera
    ) {
      return "IE";
    } //判断是否IE浏览器

    if (!!window.ActiveXObject || "ActiveXObject" in window) {
      return "IE";
    }
    return "Unknown";
  },
  fn_Start() {
    if (null == zfySBJ.m_IdtApi) {
      zfySBJ.m_IdtApi = new CIdtApi();
    }
    zfySBJ.m_IdtApi.RUN_MODE = 2; //0=release,1=打印消息,2=打印消息和日志
    var strSrvUrl = "wss://127.0.0.1:8801/mc_wss";
    var strGpsSrvUrl = "wss://127.0.0.1:8801/gs_wss";
    var strNsUrl = "wss://127.0.0.1:8801/ns_wss";
    var strUserId = "1001";
    var strPwd = "Zfj@123456";
    var CallBack = {
      //   onRecvMsgHook: onRecvMsgHook, //收到消息的钩子函数,只用来调试打印,如果修改消息内容,会出问题
      //   onSendMsgHook: onSendMsgHook, //发送消息的钩子函数,只用来调试打印,如果修改消息内容,会出问题
        onStatusInd: zfySBJ.onStatusInd, //登录状态指示
      //   onGInfoInd: onGInfoInd, //组信息指示,指示用户在哪些组里面
      //   onIMRecv: onIMRecv, //短信接收指示
      //   onIMStatusInd: onIMStatusInd, //短信状态指示
      //   onGUOamInd: onGUOamInd, //用户/组OAM操作指示
      //   onGUStatusInd: onGUStatusInd, //用户/组状态指示
      onGpsRecInd: zfySBJ.onGpsRecInd, //GPS数据指示
      //   onGpsHisQueryInd: onGpsHisQueryInd, //GPS历史数据查询响应
      //   onCallInd: onCallInd, //呼叫指示
      //   onNsQueryInd: onNsQueryInd, //NS查询响应
    };
    var bIsIe = false;
    if ("IE" == zfySBJ.myBrowser()) {
      bIsIe = true;
    }
    zfySBJ.m_IdtApi.Start(
      strSrvUrl,
      strGpsSrvUrl,
      strUserId,
      strPwd,
      1024,
      32,
      1,
      4096,
      CallBack,
      bIsIe,
      strNsUrl
    );
  },
  // 回调函数
  onGpsRecInd(GpsRecStr) {
    debugger;
    PUtility.Log(
      "GPS数据指示IDTUser",
      PUtility.PGetCurTime(),
      "onGpsRecInd",
      GpsRecStr
    );
    return 0;
  },
  //
  onStatusInd(status, usCause){
    PUtility.Log("IDTUser", PUtility.PGetCurTime(), "onStatusInd", status, usCause);
    var statusCtrl = document.getElementById("id_status");
    if (0 == status){ //离线
      console.log(PUtility.PGetCurTime() + "  " + "离线 " + IDT.GetCauseStr(usCause) );
    } else{ //在线
      console.log(PUtility.PGetCurTime() + "  " + "在线 " + IDT.GetCauseStr(usCause) );
      zfySBJ.fn_UQueryAll()
      //fn_callout();
      //m_IdtApi.StatusSubs(IDT.GU_STATUSSUBS_STR_ALL, IDT.GU_STATUSSUBS_DETAIL1);
      //加载组织下所有用户和组
      //fn_UQueryAll();
      //fn_GQueryAll();
    }
    return 0;
  },
  //
  fn_UQueryAll(){
    var strGNum = '0';
    var iGroup  = 0;
    var iUser   = 1;
    var iPage   = 0;
    var strOrgNum = null; //document.getElementById("id_org_num").value;

    // pucGNum: 组号码
    // ucGroup: 是否查询下属组,0不查询,1查询
    // ucUser: 是否查询下属用户,0不查询,1查询
    // dwPage: 第几页,从0开始.默认每页1024个用户,如果不到1024个用户,说明查询结束

    var query = {
      GNum: strGNum,
      QueryExt:{
        All: 1,
        Group: iGroup,
        User: iUser,
        Order: 0,
        Page: iPage,
        Count: 1024,
        TotalCount: 0
      },
      OrgListMgr:[
        {
          Num: strOrgNum
        }
      ]
    };
    
    zfySBJ.m_IdtApi.GQueryU(query, zfySBJ.fn_OamCallBack_UQueryAll());
  },
  fn_OamCallBack_UQueryAll(bRes, cause, strCause, MsgBody){
    if (true == PUtility.isEmpty(MsgBody)){
      return 0;
    }

    if (cause != IDT.CAUSE_ZERO){
      PUtility.Log("IDTUser", PUtility.PGetCurTime(), "fn_OamCallBack_UQueryAll", IDT.GetCauseStr(cause));
      return 0;
    }
    
    var i;
    for (i = 0; i < MsgBody.GNumU; i++) {
      zfySBJ.m_IdtApi.GpsSubs(MsgBody.GMember[i].Num, IDT.GU_STATUSSUBS_DETAIL1);
      //m_IdtApi.GpsSubs(MsgBody.GMember[i].Num, IDT.GU_STATUSSUBS_QUERY_ONETIME);
    }

    if (MsgBody.GNumU < 1024)//查询完成
        return 0;
    //if (MsgBody.QueryExt.Page * MsgBody.QueryExt.Count )

    var query = {
      GNum: MsgBody.GNum,
			QueryExt:{
        All: 1,
        Group: 0,
        User: 1,
        Order: 0,
        Page: MsgBody.QueryExt.Page + 1,
        Count: 1024,
        TotalCount: 0
      }
    };
    zfySBJ.m_IdtApi.GQueryU(query, zfySBJ.fn_OamCallBack_UQueryAll());    
    return 0;
  }
};
window.onload = function () {
  zfySBJ.fn_Start()
};
