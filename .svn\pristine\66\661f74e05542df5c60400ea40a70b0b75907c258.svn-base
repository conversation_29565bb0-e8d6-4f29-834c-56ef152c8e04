<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>点报警使用示例</title>

    <link
      rel="stylesheet"
      href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/base16/dracula.min.css"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <!-- and it's easy to individually load additional languages -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/go.min.js"></script>

    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.1/echarts.min.js"
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>

    <script src="./index.js" type="module"></script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        left: 30%;
        top: 20px;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }

      .description {
        position: absolute;
        top: 20px;
        right: 20px;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv"></div>
    <div class="tools">
      <div>
        <p>点报警效果</p>
        <div>
          <button onclick="showPointAlarm()">挂载效果</button>
          <button onclick="destroyPointAlarm()">解除效果</button>
        </div>
      </div>
    </div>
    <div class="description">
      使用 添加报警点：调用以下方法即可
      <pre><code class="language-javascript">
        /**
        * 点报警效果
        * @param {SceneView} view
        * @param {} data
        */
        ArcGisUtils.ScatterToMap.create(view,data) 

        data= [
        { name: "金华", value: [119.649506, 29.089524, 2] },  // name:名称,value:[经度,纬度,点位大小]
        { name: "东阳", value: [120.24185, 29.28942, 2] },
        { name: "武义县", value: [119.8164, 28.89331, 1] },
      ]
      </code></pre>
      删除报警点：调用上述方法返回对象的destroy()方法
    </div>
  </body>

  <script>
    var scatterObj = null;
    function showPointAlarm() {
      scatterObj = ArcGisUtils.ScatterToMap.create(window.view, [
        { name: "金华", value: [119.649506, 29.089524, 2] },
        { name: "东阳", value: [120.24185, 29.28942, 2] },
        { name: "武义县", value: [119.8164, 28.89331, 1] },
      ]);
    }
    function destroyPointAlarm() {
      scatterObj && scatterObj.destroy();
    }
  </script>
  <script>
    hljs.highlightAll();
  </script>
</html>
