// 加载拉伸图层
import { layerCreate } from "./core.js";

let ClickHandler = [];
let LayerIds = [];
// 根据字段获取颜色值
function _getColorStops(geojson, colorField, colors) {
  let colorMap = {};
  if (colors && Array.isArray(colors)) {
    for (let i = 0; i < colors.length; i++) {
      const item = colors[i];
      colorMap[item.value] = item.color;
    }
  }

  const colorStops = [];
  const { type } = geojson;
  if (type === "FeatureCollection") {
    const { features } = geojson;
    for (let i = 0; i < features.length; i++) {
      const item = features[i];
      const { properties } = item;
      colorStops.push({
        value: i,
        color: colors
          ? colorMap[properties[colorField]]
          : properties[colorField],
      });
      properties["RenderColor"] = i;
    }
  } else if (type === "Feature") {
    const { properties } = item;
    colorStops.push({
      value: 0,
      color: colors ? colorMap[properties[colorField]] : properties[colorField],
    });
    properties["RenderColor"] = 0;
  }
  return { geojson, colorStops };
}

function addExtrudeLayer({
  view,
  geojson,
  height,
  color,
  colorField,
  colorMap,
  opacity = 1,
  onClick,
}) {
  let geometryData = geojson;
  const renderer = {
    type: "simple", // autocasts as new SimpleRenderer()
    symbol: {
      type: "polygon-3d", // autocasts as new PolygonSymbol3D()
      symbolLayers: [
        {
          type: "extrude",
          size: height, // 单一高度
        },
      ],
    },
    visualVariables: [],
  };

  if (color) {
    renderer.symbol.symbolLayers[0].material = { color };
  } else if (colorField) {
    const { geojson, colorStops } = _getColorStops(geometryData, colorField);
    renderer.visualVariables.push({
      type: "color",
      field: "RenderColor",
      stops: colorStops,
    });
    geometryData = geojson;
  } else if (colorMap) {
    const { field, colors, valueExpression } = colorMap;
    if (field) {
      const { geojson, colorStops } = _getColorStops(
        geometryData,
        field,
        colors
      );
      renderer.visualVariables.push({
        type: "color",
        field: "RenderColor",
        stops: colorStops,
      });
      geometryData = geojson;
    } else if (valueExpression) {
      renderer.visualVariables.push({
        type: "color",
        valueExpression: valueExpression.replace("$field.", "$feature."), // "( $feature.TOT_VOTES / $feature.REG_VOTERS ) * 100",
        stops: colors,
      });
    }
  }

  const blob = new Blob([JSON.stringify(geometryData)], {
    type: "application/json",
  });

  const url = URL.createObjectURL(blob);
  const layer = layerCreate({
    type: "geojson",
    url,
    renderer,
    opacity,
  });
  view.map.add(layer);
  LayerIds.push(layer.id);
  view.whenLayerView(layer).then(() => {
    view.goTo(layer.fullExtent);
  });
  if (onClick && typeof onClick === "function") {
    const clickEvent = view.on("click", (e) => {
      const { mapPoint } = e;
      const query = layer.createQuery();

      query.geometry = mapPoint;
      query.returnGeometry = false;
      query.outFields = ["*"];
      layer.queryFeatures(query).then(function (results) {
        let data;
        const { features } = results;
        if (features.length > 0) {
          data = features[0].attributes;
        }
        if ("RenderColor" in data) {
          delete data["RenderColor"];
        }
        onClick(data);
      });
    });
    ClickHandler.push(clickEvent);
  }

  return layer;
}

function _removeEventHandler(layer) {
  for (let i = 0; i < ClickHandler.length; i++) {
    const item = ClickHandler[i];
    ClickHandler[i].remove();
  }
  ClickHandler=[];
}

function removeExtrudeLayer(view = window.view) {
  for (let i = 0; i < LayerIds.length; i++) {
    const layer = view.map.findLayerById(LayerIds[i]);
    if (layer) {
      view.map.remove(layer);
    }
  }
  LayerIds=[];
  _removeEventHandler();
}

export { addExtrudeLayer, removeExtrudeLayer };
