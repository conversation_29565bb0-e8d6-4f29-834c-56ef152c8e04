<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>地表透明度使用示例</title>

    <link
      rel="stylesheet"
      href="https://dev.arcgisonline.cn/jsapi/4.24/esri/themes/light/main.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/base16/dracula.min.css"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <!-- and it's easy to individually load additional languages -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/go.min.js"></script>

    <script src="./index.js" type="module"></script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        left: 50%;
        top: 20px;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }

      .description {
        position: absolute;
        top: 20px;
        right: 20px;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv"></div>
    <div class="tools">
      <div>
        <p>设置地表透明度，值为（0-1）</p>
        <div>
          <button onclick="opacityReduce()">-</button
          ><button onclick="opacityAdd()">+</button>
        </div>
      </div>
    </div>
    <div class="description">
      使用：调用以下方法即可
      <pre><code class="language-javascript">
        /**
        * 设置地表透明度
        * @param {SceneView} view
        * @param {number} opacity 透明度值：0-1
        */
        setGroundOpacity(view, opacity = 1)
      </code></pre>
    </div>
  </body>
  <script>
    hljs.highlightAll();
  </script>
  <script>
    function opacityAdd() {
      let groundOpacity = view.map.ground.opacity;
      ArcGisUtils.setGroundOpacity(view, groundOpacity + 0.1);
    }
    function opacityReduce() {
      let groundOpacity = view.map.ground.opacity;
      ArcGisUtils.setGroundOpacity(view, groundOpacity - 0.1);
    }
  </script>
</html>
