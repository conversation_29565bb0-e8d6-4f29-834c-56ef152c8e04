<!DOCTYPE HTML>

<head>
	<meta http-equiv="origin-trial" content="AtOhr2NvRTD4rPvKdQBaVfFcVEQiDeBR97NMxxYIFp2F+FdWsKpROhrE1lUKhib4bVcJyxBNAOy1+90xRk3cyAYAAABgeyJvcmlnaW4iOiJodHRwOi8vbG9jYWxob3N0OjMwMDEiLCJmZWF0dXJlIjoiVW5yZXN0cmljdGVkU2hhcmVkQXJyYXlCdWZmZXIiLCJleHBpcnkiOjE2NzUyOTU5OTl9">
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>POC-WASM</title>
    <style>
        body {
            font-family: "Quicksand", sans-serif;
            font-weight: 400;
            margin: 4em 10%;
        }

        textarea {
            width: 500px;
            min-height: 75px;
        }
    </style>

</head>

<body>
    <!-- no cache -->
    <script type="text/javascript" src="poc_sdk.js"></script>
    <h1>Wasm Demo</h1>

    <div>
        <input type="text" id="inputAddr" placeholder="登录地址" value="https://************:4010/dm-http-interface" /> <br />
        <input type="text" id="inputUserID" placeholder="用户名" value="9879310002"/> <br />
        <input type="text" id="inputPwd" placeholder="密码"  value="82cgUF#-"/> <br />
        <button onclick="window.login()"> 登录 </button>
        <button onclick="window.logout()"> 注销 </button><br />

        <select id="inputType">
            <option value="sid">预设频道</option>
            <option value="tempuser">临时呼叫创建(user)</option>
            <option value="tempinvite">临时呼叫拉人(user)</option>
            <option value="tempsid">临时呼叫创建(sid)</option>
        </select>
        <input type="text" id="inputSID" value="" placeholder="Channel ID"/>
         <input type="text" id="inputSession" value="" placeholder="Session Index"/><br />
        <button id="enterButton" onclick="window.channelEnterButton()" disabled=true> Channel Enter </button>
        <button onclick="window.channelExitButton()"> Channel Exit </button><br />
        <button onclick="window.dialogCall()"> Dialog Call </button>
        <button onclick="window.dialogBye()"> Dialog Bye </button><br />
        <input type="text" id="inputUsers" value="" placeholder="User List"/> <button id="pttButton" onclick="window.ptt()"> *
        </button> <button onclick="window.refreshMic()"> Refresh Mic </button><br />
        Send IM <input type="text" id="sendIM" placeholder="Text Message"/><button id="pttButton" onclick="window.sendIM()">Send
        </button><br /> <br />
        文件类型:
        <select id="uploadType">
            <option value=4>png</option>
            <option value=5>audio</option>
            <option value=6>video</option>
            <option value=22>jpeg</option>
        </select>
        <button onclick="window.upload()"> Upload </button><br />
        <strong>Result:<br /></strong><span id="uploadMsg"></span>
        <br /><br />
        <video id="videoPlay" autoplay="autoplay"></video> <span id="videoLable"></span><br />
        <button id="videoPauseBtn" onclick="window.videoPause()"> * </button><button id="videoMuteBtn"
            onclick="window.videoMute()"> * </button><button id="videoPauseBtn" onclick="window.videoStop()"> Stop
        </button><button id="videoDupBtn" onclick="window.videoDup()"> Duplicate </button> <button id="videoSnapshotBtn"
            onclick="window.videoSnapshot()"> Snapshot </button><br />

        <br />
        <img src="" id="videoSnapshotImg" style="display: none" alt="Snapshot Image" />
        <br />
        Logs <button id="logClean" onclick="window.logClean()">Clean</button> <br />
        <div id="logs"></div>
    </div>
    <script type="text/javascript" src="testUI.js"></script>
</body>
