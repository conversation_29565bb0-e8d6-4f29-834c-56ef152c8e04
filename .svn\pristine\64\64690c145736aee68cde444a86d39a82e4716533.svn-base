import DirectLineMeasurement3DViewModel from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/widgets/DirectLineMeasurement3D/DirectLineMeasurement3DViewModel.js";

/**
 * 创建距离测量微件
 * @param {*} view
 * @param {*} container
 * @returns
 */

function measuringDistance(view, container) {
  if (!view) {
    throw new Error("参数view为必传！");
  }
  if (!container) {
    throw new Error("参数container为必传！");
  }
  const measurementWidget = new DirectLineMeasurement3DViewModel({
    view,
    container,
  });
  measurementWidget.start();
  return measurementWidget;

}

export default measuringDistance;
