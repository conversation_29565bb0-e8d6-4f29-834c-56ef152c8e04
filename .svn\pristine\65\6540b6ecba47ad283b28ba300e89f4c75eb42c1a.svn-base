<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>弹框气泡</title>

    <link
      rel="stylesheet"
      href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <script src="./index.js" type="module"></script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        left: 50%;
      }

      .description {
        width: 600px;
        position: absolute;
        top: 20px;
        right: 30px;
        background-color: white;
        border-radius: 8px;
        padding: 20px;
      }
    </style>

    <link rel="stylesheet" href="./popu.css" />
  </head>

  <body>
    <div id="viewDiv">
      <div class="description">
        <p>使用步骤：</p>
        <p>1：在html中引入ArcGisUtils包中的popu.css</p>
        <p>
          2:
          为图层注册点击事件ArcGisUtils.MapClickEventHandle.add(layerId,(point,graphic)=>{})
        </p>
        <p>3: 回调函数会传回点击的点坐标，以及点击的图形。</p>
        <p>4: 判断是否点击到图形，调用ArcGisUtils.mapPopupWidget.showAt()显示Popup</p>
      </div>
    </div>
  </body>

  <script>
    const clock = setInterval(async () => {
      if (window.view) {
        clearInterval(clock);
        const layerConfig = {
          code: 1, // code的类型
          title: "公共厕所[1170]-点", // 图层名字
          type: "customFeature", // 1,3,5 传入这个类型
          requestUrl:
            "http://*************:9000/spacesearch/esData/geojson/list", // 请求接口地址
          // 请求参数
          payload: {
            esType1: "公厕",
            pageIndex: 1,
            pageSize: 3000,
          },
          objectIdField: "id", // 接口返回值：唯一的字段
          rendererIcon: {
            size: 64, // 图片大小
            src: "https://maponline0.bdimg.com/sty/map_icons2x/MapRes/zhongcan.png?udt=20221122", // 图片src
          },
          // data:[] // 接口请求返回的数据列表
        };
        const layer = await ArcGisUtils.loadArcgisLayer(view, layerConfig);

        // 2. 注册点击事件
        ArcGisUtils.mapClickEventHandle.add(layer.id, (point, graphic) => {
          if (graphic) {
            // 点击到图形
            const { attributes } = graphic;
            const data = [];
            for (key in attributes) {
              data.push({ key, value: attributes[key] });
            }
            ArcGisUtils.mapPopupWidget.showAt({
              point,
              title: "属性",
              onClose: () => {
                console.log("光标");
              },
              data,
            });
          } else {
            return;
          }
        });

        let mapPopupWidget;
        function initMapPopup() {
          const content = document.getElementById("popupContent");
          mapPopupWidget = new ArcGisUtils.MapPopupWidget({
            view,
            title: "自定义Popu",
            onClose: () => {
              console.log("光标");
            },
            data: [
              {
                key: "地址",
                value: "双林街吕献塘新区幼儿园东南侧约60米",
              },
              {
                key: "大类名称",
                value: "使用说明",
              },
            ],
          });
          mapPopupWidget.showAt({
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.52627070232286,
            y: 29.15638470555751,
            z: 0,
          });
        }
        function destroyPopu() {
          mapPopupWidget.destroy();
        }
      }
    }, 1000);
  </script>
</html>
