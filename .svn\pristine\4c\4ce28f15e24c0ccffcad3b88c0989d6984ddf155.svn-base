//======================================动态同步加载JS,请勿修改=======================================
loadjs = function () { var h = function () { }, c = {}, u = {}, f = {}; function o(e, n) { if (e) { var r = f[e]; if (u[e] = n, r) for (; r.length;)r[0](e, n), r.splice(0, 1) } } function l(e, n) { e.call && (e = { success: e }), n.length ? (e.error || h)(n) : (e.success || h)(e) } function d(r, t, s, i) { var c, o, e = document, n = s.async, u = (s.numRetries || 0) + 1, f = s.before || h, l = r.replace(/[\?|#].*$/, ""), a = r.replace(/^(css|img)!/, ""); i = i || 0, /(^css!|\.css$)/.test(l) ? ((o = e.createElement("link")).rel = "stylesheet", o.href = a, (c = "hideFocus" in o) && o.relList && (c = 0, o.rel = "preload", o.as = "style")) : /(^img!|\.(png|gif|jpg|svg|webp)$)/.test(l) ? (o = e.createElement("img")).src = a : ((o = e.createElement("script")).src = r, o.async = void 0 === n || n), !(o.onload = o.onerror = o.onbeforeload = function (e) { var n = e.type[0]; if (c) try { o.sheet.cssText.length || (n = "e") } catch (e) { 18 != e.code && (n = "e") } if ("e" == n) { if ((i += 1) < u) return d(r, t, s, i) } else if ("preload" == o.rel && "style" == o.as) return o.rel = "stylesheet"; t(r, n, e.defaultPrevented) }) !== f(r, o) && e.head.appendChild(o) } function r(e, n, r) { var t, s; if (n && n.trim && (t = n), s = (t ? r : n) || {}, t) { if (t in c) throw "LoadJS"; c[t] = !0 } function i(n, r) { !function (e, t, n) { var r, s, i = (e = e.push ? e : [e]).length, c = i, o = []; for (r = function (e, n, r) { if ("e" == n && o.push(e), "b" == n) { if (!r) return; o.push(e) } --i || t(o) }, s = 0; s < c; s++)d(e[s], r, n) }(e, function (e) { l(s, e), n && l({ success: n, error: r }, e), o(t, e) }, s) } if (s.returnPromise) return new Promise(i); i() } return r.ready = function (e, n) { return function (e, r) { e = e.push ? e : [e]; var n, t, s, i = [], c = e.length, o = c; for (n = function (e, n) { n.length && i.push(e), --o || r(i) }; c--;)t = e[c], (s = u[t]) ? n(t, s) : (f[t] = f[t] || []).push(n) }(e, function (e) { l(n, e) }), r }, r.done = function (e) { o(e, []) }, r.reset = function () { c = {}, u = {}, f = {} }, r.isDefined = function (e) { return e in c }, r }();
//======================================动态同步加载JS,请勿修改=======================================

var poc = new function () {
    this.data = {}; //数据接口相关函数/回调
    this.ptt = {}; //通信接口相关函数/回调
    this.video = {}; //视频接口相关函数/回调
};

poc.JS_VERSION = "20200701 12:00:00";

loadjs([
    '/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yws/engine/ptt/webrtc-adapter.js',
    '/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yws/engine/ptt/fingerprint2.min.js',
    '/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yws/engine/data/aes.js',
    '/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yws/engine/data/pad-zeropadding.js',
    '/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yws/engine/data/axios.min.js',
    '/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yws/engine/data/dataAction.js',
], { async: false });

// isWebRTCSupported 检测webrtc支持
poc.isWebRTCSupported = function () {
    try {
        if ((RTCPeerConnection !== undefined && RTCPeerConnection !== null) && (navigator.mediaDevices.getUserMedia !== undefined && navigator.mediaDevices.getUserMedia !== null)) {
            return true;
        }
    } catch (e) {
    }
    return false;
}();

// isWebAssemblySupported 检测wasm支持
poc.isWebAssemblySupported = function () {
    try {
        if (typeof WebAssembly === "object"
            && typeof WebAssembly.instantiate === "function") {
            var module = new WebAssembly.Module(Uint8Array.of(0x0, 0x61, 0x73, 0x6d, 0x01, 0x00, 0x00, 0x00));
            if (module instanceof WebAssembly.Module)
                return new WebAssembly.Instance(module) instanceof WebAssembly.Instance;
        }
    } catch (e) {
    }
    return false;
}();

var Module = typeof Module !== "undefined" ? Module : {};
poc.ptt.init = function (onInitialized) {
    if (!poc.isWebRTCSupported || !poc.isWebAssemblySupported) {
        onInitialized("WebRTC or WebAssembly unsupported!");
        return;
    }

    Module.onRuntimeInitialized = function () {
        console.log("[POC] Info: Module.onRuntimeInitialized!");

        //加载janus视频相关
        loadjs([
            '/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yws/engine/video/ctl_mdsvideo.js',
            '/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yws/engine/video/janus.js',
            '/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yws/engine/video/mdsvideoclass.js',
        ], { async: false });

        poc.ptt.init_internal(poc).then(function (x) {
            onInitialized(null);
        }).catch(function (err) {
            onInitialized(err);
        })
    };

    //加载ptt对讲相关
    loadjs([
        '/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yws/engine/ptt/BenzAMRRecorder.min.js',
        '/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yws/engine/ptt/web_engine.js',
    ], { async: false });
}
