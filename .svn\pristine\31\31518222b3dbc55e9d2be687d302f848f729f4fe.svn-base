/*
 * @Descripttion: 添加白膜，移除白膜
 * @Author: <EMAIL>
 * @Date: 2022-12-10 15:02:16
 */
import { getLayerConfigById } from "./layerConfig.js";
import layerCreatAsync from "./layerCreatAsync.js";

const LAYER_ID = "WHITE_MODEL";

/**
 * 添加 白膜 方法
 * @param { MapView|SceneView} view 对象 必填
 * @returns
 */

async function addWhiteModalLayer(view) {
  const layerConfig = getLayerConfigById(LAYER_ID);
  const layer = await layerCreatAsync(layerConfig);
  view.map.add(layer);
}

/**
 * 移除 白膜 方法
 * @param { MapView|SceneView} view 对象 必填
 * @returns
 */

function removeWhiteModalLayer(view) {
  let layer = view.map.findLayerById(LAYER_ID);
  view.map.remove(layer);
}

export { addWhiteModalLayer, removeWhiteModalLayer };
