<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>指挥体系弹窗</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/zhdd_left.css" />
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/elementui/js/index.js"></script>
    <!-- <script src="https://g.alicdn.com/gdt/jsapi/1.9.22/index.js"></script>
    <script src="/static/js/jslib/gdt-jsapi.js"></script>
    <script src="/static/js/jslib/watchmsg.js"></script> -->
    <style>
      body {
        margin: 0;
        padding: 0;
      }
      #app {
        width: 1600px;
        height: 880px;
        position: relative;
      }
      .close {
        position: absolute;
        right: -30px;
        top: -90px;
        color: #fff;
        font-size: 68px;
        margin-right: 40px;
        cursor: pointer;
      }
      .content {
        width: 94%;
        position: absolute;
        top: 135px;
        left: 45px;
        height: 80%;
      }
      .search_box {
        display: flex;
        justify-content: space-between;
      }
      input {
        padding-left: 20px;
        font-size: 28px;
        width: 300px;
        height: 50px;
        margin-right: 10px;
        background: transparent;
        border: 1px solid #2ba5b0;
        color: #ccc;
      }
      .search_box span {
        padding: 6px 22px;
        border: 1px solid #2ba5b0;
        border-radius: 10px;
        background: rgba(43, 165, 176, 0.2);
        cursor: pointer;
        font-size: 32px;
        color: #fff;
      }
      .tr {
        -webkit-animation: none !important;
      }
      .call_box {
        width: 750px;
        height: 688px;
        background-color: rgba(3, 27, 60, 0.9);
        box-shadow: inset 0px 0px 40px 0px rgba(35, 154, 228, 0.8);
        border-radius: 20px;
        position: absolute;
        top: 85px;
        left: 300px;
        padding: 20px;
      }
      .call_input {
        width: 500px;
        height: 61px;
        line-height: 61px;
        font-size: 68px;
        box-sizing: border-box;
        padding: 0 4px;
        color: #fff;
        background: none;
        border: none;
        outline: none;
      }
      .call_num li {
        list-style: none;
        width: 220px;
        height: 80px;
        line-height: 80px;
        font-size: 48px;
        color: #fff;
        text-align: center;
        background-color: #053564;
        border-radius: 10px;
        border: solid 2px #074a8c;
        margin: 11px 8px 11px 14px;
        float: left;
        cursor: pointer;
      }
      .call_num li:hover {
        background-color: #0d59a0;
      }
      .iconPhone1 {
        cursor: pointer;
        background: #51c422;
        padding: 8px 15px;
        border-radius: 10px;
        height: 80px;
        width: 300px;
        text-align: center;
        line-height: 100px;
      }

      .iconPhone1::after {
        content: "";
        display: inline-block;
        width: 32px;
        height: 32px;
        background: url(/static/images/zhdd/icon_call.png) no-repeat;
        background-size: cover;
      }

      .iconVideo1 {
        text-align: center;
        cursor: pointer;
        background: #22a0c4;
        padding: 8px 15px;
        border-radius: 10px;
        height: 80px;
        width: 300px;
        line-height: 100px;
        cursor: pointer;
      }

      .iconVideo1::after {
        content: "";
        display: inline-block;
        width: 32px;
        height: 32px;
        background: url(/static/images/zhdd/video.png) no-repeat;
        background-size: cover;
      }
      .call_input::-webkit-outer-spin-button,
      .call_input::-webkit-inner-spin-button {
        -webkit-appearance: none;
      }
      .iconPhone::after {
        background: url(/static/images/zhdd/icon_call.png) no-repeat;
        background-size: cover;
      }

      .iconVideo::after {
        background: url(/static/images/zhdd/video.png) no-repeat;
        background-size: cover;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <s-dialog title="指挥体系" width="1600px" height="880px"></s-dialog>
      <div class="content">
        <div class="close" @click="close()">×</div>
        <div class="search_box">
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 40%;
            "
          >
            <input type="text" placeholder="请输入单位" v-model="val" />
            <span @click="initApi()">查询</span>
            <span @click="resetFun">重置</span>
          </div>
          <div><span @click="call_close=true">拨号</span></div>
        </div>
        <div class="table-bottom" style="height: 620px">
          <div class="th">
            <div class="th_td" style="flex: 0.15; text-align: center">序号</div>
            <div class="th_td" style="flex: 0.2; text-align: center">单位</div>
            <div class="th_td" style="flex: 0.2; text-align: center">职务</div>
            <div class="th_td" style="flex: 0.15; text-align: center">姓名</div>
            <div class="th_td" style="flex: 0.2; text-align: center">
              联系方式
            </div>
          </div>
          <div class="tbody" id="box">
            <div class="tr" v-for="(item,index) in tableData" :key="index">
              <div class="tr_td" style="flex: 0.15; text-align: center">
                {{index+1}}
              </div>
              <div class="tr_td" style="flex: 0.2; text-align: center">
                {{(item.bm == null?"":item.bm) + (item.town == null?"":item.town)}}
              </div>
              <div class="tr_td" style="flex: 0.2; text-align: center">
                {{item.zw}}
              </div>
              <div class="tr_td" style="flex: 0.15; text-align: center">
                {{item.name}}
              </div>
              <div class="tr_td" style="flex: 0.2; text-align: center">
                <div
                  style="
                    display: flex;
                    justify-content: space-around;
                    align-items: center;
                  "
                >
                  <span
                    class="iconPhone"
                    @click="openCall(item.phone)"
                  ></span>
                  <span
                    class="iconVideo"
                    @click="openVideo(item.phone)"
                  ></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="call_box" v-show="call_close">
        <div class="s-flex s-row-between s-font-30 s-c-white">
          <h2 style="font-style: italic">电话拨号</h2>
          <div style="top: 0" class="close" @click="call_close=false">×</div>
        </div>
        <div class="s-flex s-row-between s-m-t-20">
          <input type="number" class="call_input" v-model="callnum" />
          <img
            style="cursor: pointer"
            src="/static/images/zhdd/call_x.png"
            alt=""
            @click="clearCallNum()"
          />
        </div>
        <ul class="call_num s-flex s-flex-wrap">
          <li
            @click="callNumFun(num)"
            :style="{cursor:callnum.length>=11?'no-drop':'pointer'}"
            v-for="num in call_list"
          >
            {{num}}
          </li>
        </ul>
        <div class="s-flex s-row-between s-m-t-10">
          <span class="iconPhone1" @click="openCall(callnum)"></span>
          <span class="iconVideo1" @click="openVideo(callnum)"></span>
        </div>
      </div>
    </div>
    <script>
      let vm = new Vue({
        el: "#app",
        data() {
          return {
            val: "",
            tableDataAll: [],
            tableData: [],
            call_close: false,
            call_list: [1, 2, 3, 4, 5, 6, 7, 8, 9, "*", 0, "#"],
            callnum: "",
          };
        },
        mounted() {
          this.initApi();
        },
        methods: {
          // 拨号功能
          callNumFun(num) {
            if (this.callnum.length < 11) {
              this.callnum += num;
            } else {
              return;
            }
          },
          clearCallNum() {
            this.callnum = this.callnum.substring(0, this.callnum.length - 1);
          },
          // 指挥体系功能
          async initApi() {
            $api2Get("/xzzfj/dutyPersonnel/dutyListDetails",{area:localStorage.getItem("city"),deptName:this.val}).then(res => {
              console.log(res);
              if (res.data.code == 200) {
                this.tableData = res.data.data.map(item => {return {
                  bm: item.deptName,
                  name: item.name,
                  zw: item.duties,
                  phone: item.phone,
                  town: item.town
                }})
              }
            })
          },
          filterTableFun() {
            if (this.val == "") {
              this.$message({
                message: "部门不能为空",
                type: "warning",
              });
              return;
            }
            let filterArr = this.tableDataAll.filter((a) => {
              return a.bm.indexOf(this.val) > 0;
            });
            this.tableData = filterArr;
          },
          resetFun() {
            this.val = "";
            // this.tableData = this.tableDataAll;
            this.initApi();
          },
          openCall(phones) {
            if (phones != "") {
              // window.parent.lay.openIframe({
              //   type: "openIframe",
              //   name: "zbPhone",
              //   id: "zbPhone",
              //   src: "/static/citybrain/zhdd/zhdd_page/zhtx_callPhone/zbPhone.html",
              //   left: "1200px",
              //   top: "469px",
              //   width: "1500px",
              //   height: "920px",
              //   zIndex: "666",
              //   argument: { phone: phones },
              // });

              window.parent.lay.openIframe({
                type: "openIframe",
                name: "CallPhone",
                id: "CallPhone",
                src: baseURL.url + "/static/citybrain/commonts/CallPhone/CallPhone.html",
                left: "1200px",
                top: "575px",
                width: "1515px",
                height: "866px",
                zIndex: "666",
                argument: { phone: phones },
              });
            }
          },
          openVideo(phones) {
            if (phones != "") {
              // window.parent.lay.openIframe({
              //   type: "openIframe",
              //   name: "zbVideo",
              //   id: "zbVideo",
              //   src: "/static/citybrain/zhdd/zhdd_page/zhtx_callPhone/zbVideo.html",
              //   left: "500px",
              //   top: "100px",
              //   width: "3250px",
              //   height: "1920px",
              //   zIndex: "666",
              //   argument: { phone: phones },
              // });

              window.parent.lay.openIframe({
                type: "openIframe",
                name: "CallVideo",
                id: "CallVideo",
                src: baseURL.url + "/static/citybrain/commonts/CallVideo/CallVideo.html",
                left: "1200px",
                top: "575px",
                width: "1515px",
                height: "866px",
                zIndex: "666",
                argument: { phone: phones },
              });
            }
          },
          close() {
            window.parent.lay.closeIframeByNames(["zhtx_diong"]);
          },
          // doVideoMeeting(phone) {
          //   const that = this;
          //   const phones = [phone];
          //   that.$message({
          //     message: "视频会议",
          //     phones,
          //     type: "warning",
          //   });
          //   let params = {
          //     source: "jinhuacsdn",
          //     phones: phones,
          //   };
          //   axios({
          //     method: "post",
          //     url: baseURL.url + "/adm-api/pub/dingtalk/accountId",
          //     data: params,
          //   }).then(function (response) {
          //     let result = response.data.data;
          //     console.log(result);
          //     const accountIds = result.map((item) => item.accountId);
          //     console.log(accountIds);
          //     if (!accountIds.length) {
          //       that.$message({
          //         message: "视频会议人员的accountId均为空，发起视频会议失败",
          //         type: "warning",
          //       });
          //     } else {
          //       window.top.postMessage(
          //         {
          //           accountIds,
          //           type: "doVideoMeeting",
          //         },
          //         "*"
          //       );
          //     }
          //   });
          // },
          // doPhoneCall(phone) {
          //   if (phone) {
          //     window.top.postMessage(
          //       {
          //         phone,
          //         type: "doPhoneCall",
          //       },
          //       "*"
          //     );
          //   }
          // },
        },
      });
    </script>
  </body>
</html>
