<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>Draw工具使用示例</title>

    <link
      rel="stylesheet"
      href="https://dev.arcgisonline.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <script src="./index.js" type="module"> </script>

    <script>
      function drawOnClick(type) {
        if (!window?.drawTool) {
          window.drawTool = new ArcGisUtils.Draw({ view });
        }

        drawTool.draw(type);
      }

      function clearOnClick() {
        // 调用方法clear()
        window.drawTool.clear();
      }
    </script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        left: 50%;
        width: 50%;
        height: 200px;
        display: flex;
      }

      .tools span {
        cursor: pointer;
        background-color: blue;
        width: 150px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        color: white;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv">
      <div class="tools">
        <span onclick="drawOnClick('point')">绘点</span>
        <span onclick="drawOnClick('polygon')">绘面</span>
        <span onclick="drawOnClick('polyline')">绘线</span>
        <span onclick="drawOnClick('circle')">绘圆</span>
        <span onclick="drawOnClick('rectangle')">绘矩形</span>
        <span onclick="clearOnClick()">清除</span>
      </div>
    </div>
  </body>
</html>
