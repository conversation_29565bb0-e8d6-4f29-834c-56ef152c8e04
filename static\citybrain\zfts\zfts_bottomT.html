<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>指挥调度底部</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/Vue/vue-count-to.min.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/elementui/js/index.js"></script>
    <style>
      [v-cloak] {
        display: none;
      }
      #bottom {
        width: 1760px;
        height: 525px;
        padding: 20px;
        box-sizing: border-box;
        background: url("/static/images/index/bottom-bg.png") no-repeat;
        background-size: 100% 100%;
      }
      .zfrw_box {
        width: 100%;
        display: flex;
      }
      .zfrw_left {
        width: 300px;
      }
      .zfrw_right {
        width: calc(100% - 300px);
      }
      .tssj_title {
        width: 100%;
        height: 80px;
        line-height: 80px;
        font-style: italic;
        color: #ffffff;
        font-size: 32px;
        text-align: center;
        background: url("/static/images/zhdd/zfrw-bg.png") no-repeat 46px -10px;
      }
      .count_box {
        display: flex;
        justify-content: center;
        margin: 0 10px;
      }

      .count-toNum {
        width: 50px;
        height: 60px;
        text-align: center;
        line-height: 60px;
        font-size: 43px;
        background: url("/static/images/xzzfj/sz-bg.png");
        background-size: 100% 100%;
      }

      /* 表格 */
      .table {
        width: 100%;
        height: 500px;
        padding: 10px;
        box-sizing: border-box;
      }

      .table .th {
        width: 100%;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        font-weight: 700;
        font-size: 28px;
        line-height: 60px;
        color: #ffffff;
      }

      .table .th_td {
        letter-spacing: 0px;
        text-align: left;
      }

      .table .tbody {
        width: 100%;
        height: calc(100% - 148px);
        /* overflow-y: auto; */
        overflow: hidden;
      }

      .table .tbody:hover {
        overflow-y: auto;
      }

      .table .tbody::-webkit-scrollbar {
        width: 4px;
        /*滚动条整体样式*/
        height: 4px;
        /*高宽分别对应横竖滚动条的尺寸*/
      }

      .table .tbody::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: #20aeff;
        height: 8px;
      }

      .table .tr {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        height: 80px;
        line-height: 80px;
        font-size: 25px;
        color: #ffffff;
        cursor: pointer;
        border-top: 1px solid #959aa1;
        border-image: linear-gradient(to right, #e9f5ff3b, #f5ffffd4, #e9f5ff3b)
          1;
        box-sizing: border-box;
      }

      .table .tr:nth-child(2n) {
        background: rgba(50, 134, 248, 0.2);
      }

      .table .tr:nth-child(2n + 1) {
        background: rgba(50, 134, 248, 0.12);
      }

      .table .tr:hover {
        background-color: #0074da75;
      }

      .table .tr_td {
        letter-spacing: 0px;
        text-align: left;
        box-sizing: border-box;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .table .tr_td > img {
        position: relative;
        top: 25px;
      }
      .line {
        display: inline-block;
        width: 4px;
        height: 20px;
        background: url("/static/images/zhdd/line.png") no-repeat;
        background-size: 100% 100%;
      }
      .jindu {
        border-radius: 10px;
        background: #18abff8a;
        overflow: hidden;
        box-sizing: border-box;
      }
      .jindu > span {
        display: inline-block;
        flex: 1;
        height: 15px;
      }
      .jindu span:last-child {
        border-radius: 10px;
        margin-right: 3px;
      }
      .line_blue {
        background: linear-gradient(
          90deg,
          rgba(24, 225, 255, 0) 0%,
          #18e1ff 100%
        );
      }
      .line_yellow {
        background: linear-gradient(
          90deg,
          rgba(255, 196, 53, 0) 0%,
          #ffc435 100%
        );
      }
      .line_green {
        background: linear-gradient(
          90deg,
          rgba(24, 255, 182, 0) 0%,
          #18ffb6 100%
        );
      }
      .jindu_text {
        color: #77b3f1;
        margin-top: 2px;
        font-size: 24px;
        justify-content: space-around;
      }
      .tabChange {
        display: flex;
        font-size: 30px;
        position: absolute;
        top: 30px;
        left: 450px;
        color: #fff;
      }
      .tabItem {
        cursor: pointer;
        margin-left: 40px;
        padding-bottom: 5px;
        border-bottom: 4px solid transparent;
        box-sizing: border-box;
      }
      .tabActive {
        color: #e6c804;
        border-bottom-color: #e6c804;
      }
    </style>
  </head>

  <body>
    <div id="bottom" v-cloak>
      <div class="hearder_h1"><span>执法任务</span></div>
      <div class="tabChange">
        <div
          class="tabItem"
          v-for="(item,index) in tab"
          :class="{tabActive:activeIndex===index}"
          @click="changeTab(index,item)"
        >
          {{item}}
        </div>
      </div>
      <div class="zfrw_box">
        <div class="zfrw_left">
          <div
            class="tssj"
            v-for="(item,index) in tssjData"
            style="margin-top: 30px"
          >
            <div class="tssj_title">{{item.name}}</div>
            <div class="count_box">
              <div v-for="(el,i) in item.value" :key="i" class="count-toNum">
                <count-to
                  :start-val="0"
                  :end-val="Number(el)"
                  :duration="3000"
                  :class="index==0?'s-c-yellow-gradient':'s-c-blue-gradient'"
                >
                </count-to>
              </div>
            </div>
          </div>
        </div>
        <div class="zfrw_right s-flex-1">
          <div class="table">
            <div class="th">
              <div class="th_td" style="flex: 0.15">案件来源</div>
              <div class="th_td" style="flex: 0.25">案件名称</div>
              <div class="th_td" style="flex: 0.22">办理单位</div>
              <!-- <div class="th_td" style="flex: 0.13">{{activeIndex==0?'检查':'处置'}}时间</div>
              <div class="th_td" style="flex: 0.2">处理状态</div> -->
              <div class="th_td" style="flex: 0.13">案件状态</div>
              <div class="th_td" style="flex: 0.2">时间</div>
            </div>
            <div
              class="tbody"
              id="box"
              @mouseenter="mouseenterEvent"
              @mouseleave="mouseleaveEvent"
              v-loading="loading"
              element-loading-text="拼命加载中"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 0, 0, 0.2)"
            >
              <div
                class="tr"
                v-for="(item,index) in tableData"
                :key="index"
                @click="tableClick(item)"
              >
                <div class="tr_td" style="flex: 0.15">
                  {{item.case_source_desc}}
                </div>
                <div
                  class="tr_td"
                  style="flex: 0.25"
                  :title="item.punish_action_name"
                >
                  {{item.punish_action_name}}
                </div>
                <div
                  class="tr_td"
                  style="flex: 0.22"
                  :title="item.implementInist_name"
                >
                  {{item.implementInist_name}}
                </div>
                <div class="tr_td" style="flex: 0.13">
                  {{item.pub_status_cd==0?'已上报':item.pub_status_cd==1?'已完结':'处罚执行'}}
                </div>
                <div class="tr_td" style="flex: 0.2">{{item.filing_date}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      let vm = new Vue({
        el: "#bottom",
        data: {
          city: "金华市",
          activeIndex: 0,
          // tab: ["检查案件", "处罚案件"],
          tab: ["重大案件", "普通程序", "简易程序"],
          time1: null,
          dom1: null,
          tssjData: [],
          jcAllData: [
            {
              name: "周检查数",
              value: "0207",
            },
            {
              name: "周立案数",
              value: "1506",
            },
          ],
          czAllData: [
            {
              name: "周检查数",
              value: "4502",
            },
            {
              name: "周立案数",
              value: "5256",
            },
          ],
          jycxData: [
            {
              name: "周检查数",
              value: "4502",
            },
            {
              name: "周立案数",
              value: "5256",
            },
          ],
          tableData: [],
          jcData: [
            {
              ly: "日常督查发现",
              name: "多湖原避街道金都美地社区",
              bm: "金东区综合行政执.",
              time: "2023-03-21",
              state: 2,
            },
            {
              ly: "日常督查发现",
              name: "双龙南街金都美地社区",
              bm: "婺城区综合行政执.",
              time: "2023-02-21",
              state: 2,
            },
            {
              ly: "日常督查发现",
              name: "多湖原避街道金都美地社区",
              bm: "金东区综合行政执.",
              time: "2023-05-21",
              state: 2,
            },
            {
              ly: "日常督查发现",
              name: "三江街道金都美地社区",
              bm: "兰溪市综合行政执.",
              time: "2023-01-21",
              state: 2,
            },
            {
              ly: "日常督查发现",
              name: "平中街道金都美地社区",
              bm: "义乌市综合行政执.",
              time: "2023-03-01",
              state: 2,
            },
          ],
          czData: [
            {
              ly: "日常督查发现",
              name: "多湖原避街道金都美地社区",
              bm: "金东区综合行政执.",
              time: "2023-03-21",
              state: 2,
            },
            {
              ly: "上级交办",
              name: "武榜昂义江东眉公园处乱，",
              bm: "市环保局、水利局",
              time: "2023-03-21",
              state: 1,
            },
            {
              ly: "来信来访",
              name: "开发区大润发共享单车",
              bm: "开发区综合行政执.",
              time: "2023-03-21",
              state: 1,
            },
            {
              ly: "日常督查发现",
              name: "城中街道明月花园乱倒",
              bm: "婺城区综合行政执",
              time: "2023-03-21",
              state: 2,
            },
            {
              ly: "日常督查发现",
              name: "城中街道明月花园乱倒",
              bm: "婺城区综合行政执",
              time: "2023-03-21",
              state: 3,
            },
          ],
          loading: false,
        },
        created() {
          this.initApi(localStorage.getItem("city"));
        },
        mounted() {
          // 表格滚动
          this.dom1 = document.getElementById("box");
          this.mouseleaveEvent();
          const this_ = this;
          window.parent.eventbus.on("cityChange", (city) => {
            let filtName = (this_.city =
              city == "金义新区"
                ? "金东区"
                : city == "金华开发区"
                ? "开发区"
                : city);
            this_.initApi(filtName);
          });

          window.parent.eventbus &&
          window.parent.eventbus.on("yearChange", (year) => {
            this.initApi(localStorage.getItem("city"),year);
          });
        },
        methods: {
          async initApi(city) {
            $api("/csdn_yjyp16", { area_code: city }).then((res) => {
              this.jcAllData = this.czAllData = this.jycxData = res.map((a) => {
                return {
                  name: a.label.split("-")[1],
                  value: a.num,
                };
              });
              this.changeTab(this.activeIndex, this.tab[this.activeIndex]);
            });
          },
          tableClick(item) {
            window.parent.lay.openIframe({
              type: "openIframe",
              name: "zfrw_details2",
              src: "/static/citybrain/commonts/zfts/zfrw_details2.html",
              left: "calc(50% - 1100px)",
              top: "300px",
              width: "2190px",
              height: "1559px",
              zIndex: 667,
              argument: {
                zfrw_details: {
                  code: this.activeIndex == 0 ? 1 : 0,
                  qxwd: localStorage.getItem("city") == "金华市" ? "市本级" : localStorage.getItem("city"),
                  id: item.id,
                  name: item.punish_action_name
                },
              },
            });
          },
          // async actuator(val) {
          //   this.loading = true;
          //   let result = [];
          //   for (let i = 0; i < val.length; i++) {
          //     let res = await axios({
          //       method: "post",
          //       url: `${baseURL.url}/prod-api/mis/irs/actuator`,
          //       headers: {
          //         "Content-Type": "application/json;charset=UTF-8",
          //         Authorization: sessionStorage.getItem("Authorization"),
          //       },
          //       data: {
          //         name: "处罚办案事件详情接口",
          //         url: "http://dw.jinhua.gov.cn/gateway/api/0010080070122430/dataSharing/a4c07y34fC781M31.htm",
          //         params: {
          //           // case_no: '金综执 当罚决字〔2023〕第001127号',
          //           case_no: val[i].ywwd1,
          //         },
          //       },
          //     });
          //     let datas = res.data.datas.data;
          //     result.push(...datas);
          //   }
          //   console.log(result);
          //   this.tableData = result;
          //   this.loading = false;
          // },
          changeTab(index, item) {
            this.loading = true;
            this.tableData = [];
            this.activeIndex = index;
            if (index == 0) {
              this.tssjData = this.jcAllData;
              // this.tableData = this.jcData;
            } else if (index == 1){
              this.tssjData = this.czAllData;
              // this.tableData = this.czData;
            } else {
              this.tssjData = this.jycxData;
            }
            // this.queryData(item);
            $api("/zfrw_cfbasj_xq", {
              ywwd2: index == 0 ? 1 : index == 1 ? 0 : 2,
              code: index == 0 ? 1 : index == 1 ? 0 : 2,
              qxwd: localStorage.getItem("city") == "金华市" ? "市本级" : localStorage.getItem("city"),
              nd: localStorage.getItem("year")
            }).then((res) => {
              this.loading = false;
              this.tableData = res;
              // this.actuator(res)
            });
          },
          queryData(param) {
            console.log(param);
            $api("/xzzf_zhts_zfrw_jcaj", { ajlx: param }).then((res) => {
              console.log(res);
              this.tableData = res;
            });
          },
          mouseenterEvent() {
            clearInterval(this.time1);
          },
          mouseleaveEvent() {
            this.time1 = setInterval(() => {
              // this.dom1.scrollTop += 1.5
              this.dom1.scrollBy({
                top: 78,
                behavior: "smooth",
              });
              if (
                this.dom1.scrollTop >=
                this.dom1.scrollHeight - this.dom1.offsetHeight
              ) {
                this.dom1.scrollTop = 0;
              }
            }, 1500);
          },
        },
      });
    </script>
  </body>
</html>
