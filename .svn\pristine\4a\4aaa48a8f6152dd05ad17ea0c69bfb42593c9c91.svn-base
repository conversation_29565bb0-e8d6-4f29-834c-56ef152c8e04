<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <title>地图工具栏</title>
        <script src="/Vue/vue.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/elementui.css" />
        <link rel="stylesheet" href="/static/citybrain/tcgl/icon/iconfont.css" />
        <link rel="stylesheet" href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css" />
        <style>
            body,
            html {
                margin: 0;
            }

            [v-cloak] {
                display: none;
            }

            /* 设置滚动条的样式 */
            ::-webkit-scrollbar {
                width: 10px;
            }

            /* 滚动槽 */
            ::-webkit-scrollbar-track {
                border-radius: 5px;
            }

            /* 滚动条滑块 */
            ::-webkit-scrollbar-thumb {
                border-radius: 10px;
                background: rgba(35, 144, 207, 0.1);
            }

            ::-webkit-scrollbar-thumb:window-inactive {
                background: rgba(27, 146, 215, 0.4);
            }

            .toolbar {
                width: 500px;
                height: max-content;
                background: rgba(3, 24, 39, 0.88);
                border: 2px solid #afdcfb;
                border-radius: 25px;
                box-sizing: border-box;
                position: relative;
                padding: 30px;
                color: #b6d1f0;
                font-size: 26px;
                overflow-y: scroll;
            }

            .box {
                width: 100%;
                display: flex;
                flex-wrap: wrap;
                justify-content: left;
            }

            .el-divider--horizontal {
                margin: 10px 0 15px;
                opacity: 0.5;
            }

            .item {
                width: 50px;
                height: 50px;
                border: 1px solid #b6d1f0;
                border-radius: 10px;
                font-size: 30px;
                line-height: 50px;
                text-align: center;
                margin: 5px 17px;
                cursor: pointer;
                box-sizing: border-box;
            }

            .item-active {
                border: 1px solid #03f0ff;
                color: #03f0ff;
            }

            .iconfont {
                font-size: 35px;
            }

            .bottom {
                display: flex;
                justify-content: flex-end;
                /* margin: 10px 15px; */
            }

            .el-button--mini {
                font-size: 18px;
            }

            #area-box,
            #distance-box {
                position: absolute;
                top: 100px;
                right: 80px;
            }
            .el-slider__runway {
                width: 100px;
                height: 10px;
                margin-left: 30px;
            }
            .el-slider__bar {
                height: 10px;
            }
            .el-slider__button-wrapper {
                width: 60px;
                height: 60px;
                top: -25px;
            }

            .transparent {
                font-size: 26px;
                color: #c0d6ed;
                text-align: center;
                padding-left: 20px;
                box-sizing: border-box;
            }
            .transparent p {
                margin: 0;
            }
        </style>
    </head>

    <body>
        <div id="app" v-cloak>
            <div class="toolbar">
                <div v-for="(items,index) in list" :key="index" style="margin-bottom: 20px">
                    <span>{{items.name}}</span>
                    <el-divider></el-divider>
                    <div class="box">
                        <div
                            class="item"
                            v-for="(item,i) in items.mapTool"
                            @click="mapFun(item)"
                            :title="item.name"
                            :class="((iconIndex==item.id)
                            ||(item.name=='新建剖切' && sliceFlag)
                            ||(item.name=='添加精模' && modelFlag)
                            ||(item.name=='通视分析' && sightFlag)
                            ||(item.name=='日照分析' && lightFlag))
                            ?  'item-active' : ''"
                        >
                            <!-- 上面一行实现剖切工具的单独判断，通过sliceFlag与modelFlag控制 -->
                            <i
                                v-if="item.name!='日照分析'&&item.name!='通视分析'"
                                class="iconfont"
                                :class="item.icon"
                            ></i>
                            <img v-else :src=`/static/images/icon/${item.icon}.png` alt="">
                        </div>
                    </div>
                </div>
                <span>地表透明度</span>
                <el-divider></el-divider>
                <div style="display: flex">
                    <el-slider
                        v-model="sliderValue"
                        :min="min"
                        :max="max"
                        :show-tooltip="false"
                        @change="sliderChange"
                    ></el-slider>
                    <div class="transparent">
                        <p>透明度：{{sliderValue/10}}</p>
                    </div>
                </div>
                <div class="bottom">
                    <el-button type="primary" size="mini" icon="el-icon-delete" @click="cleanall">清除</el-button>
                </div>
            </div>
            <div id="area-box"></div>
            <div id="distance-box"></div>
            <!-- <div id="sliceContainer"></div> -->
        </div>
    </body>

    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/elementui/js/elementui.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/go.min.js"></script> -->
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/js/jslib/ArcGisUtils/libs/three-r116.min.js"></script>
    <script src="/static/js/jslib/ArcGisUtils/index.js"></script>
    <script>
        var souName = new Vue({
            el: "#app",
            data() {
                return {
                    //地表透明度滑块
                    sliderValue: 10,
                    min: 0,
                    max: 10,
                    iconIndex: null,
                    areaMeasurementWidget: null,
                    distanceMeasurementWidget: null,
                    sliceWidget: null,
                    modelFlag: false, //控制精模按钮
                    sliceFlag: false, //控制剖切按钮
                    sightFlag: false, //控制通视按钮
                    lightFlag: false, //控制日照按钮
                    lineOfSightWidget: null,
                    lightWidget: null,
                    list: [
                        {
                            id: 1,
                            name: "绘图工具",
                            mapTool: [
                                {
                                    id: 11,
                                    name: "绘点",
                                    icon: "icon-ditu",
                                },
                                {
                                    id: 12,
                                    name: "绘面",
                                    icon: "icon-icon-mian",
                                },
                                {
                                    id: 13,
                                    name: "绘线",
                                    icon: "icon-icon-xian",
                                },
                                {
                                    id: 14,
                                    name: "绘圆",
                                    icon: "icon-yuanxinyuan",
                                },
                                {
                                    id: 15,
                                    name: "绘矩形",
                                    icon: "icon-juxing",
                                },
                            ],
                        },
                        {
                            id: 2,
                            name: "测量工具",
                            mapTool: [
                                {
                                    id: 21,
                                    name: "面积测量",
                                    icon: "icon-mianjiceliang",
                                },
                                {
                                    id: 22,
                                    name: "距离测量",
                                    icon: "icon-juliceliang",
                                },
                            ],
                        },
                        {
                            id: 3,
                            name: "空间分析工具",
                            mapTool: [
                                {
                                    id: 31,
                                    name: "添加精模",
                                    icon: "icon-kongjianfenxi",
                                },
                                {
                                    id: 32,
                                    name: "新建剖切",
                                    icon: "icon-pouqie",
                                },
                                {
                                    id: 33,
                                    name: "通视分析",
                                    icon: "icon-ts",
                                },
                                {
                                    id: 34,
                                    name: "日照分析",
                                    icon: "icon-rz",
                                },
                            ],
                        },
                    ],
                };
            },

            mounted() {},
            methods: {
                mapFun(item) {
                    let view = window.parent.window.mapUtil.mapview;
                    let drawType = {
                        绘点: "point",
                        绘面: "polygon",
                        绘线: "polyline",
                        绘圆: "circle",
                        绘矩形: "rectangle",
                    };
                    let measureType = {
                        面积测量: "area-box",
                        距离测量: "distance-box",
                    };

                    switch (item.name) {
                        case "添加精模":
                            if (this.modelFlag) {
                                window.parent.ArcGisUtils.removePureModalLayer(view);
                                this.modelFlag = false;
                            } else {
                                window.parent.ArcGisUtils.addPureModalLayer(view);
                                this.modelFlag = true;
                            }
                            break;
                        case "新建剖切":
                            if (this.sliceFlag) {
                                //判断状态
                                this.sliceWidget.destroy();
                                this.sliceFlag = false;
                            } else {
                                if (!window.parent.document.getElementById("sliceContainer")) {
                                    //判断剖切widget状态，并提升至index
                                    let widget = window.parent.document.createElement("div");
                                    widget.id = "sliceContainer";
                                    window.parent.$(".container").append(widget);
                                }
                                window.parent.document.getElementById("sliceContainer").style.width = "300px";
                                window.parent.document.getElementById("sliceContainer").style.height = "350px";
                                console.log(window.parent.document.getElementById("sliceContainer"));
                                window.parent.ArcGisUtils.createSiceWidget(view, "sliceContainer").then((slice) => {
                                    this.sliceWidget = slice;
                                });
                                window.parent.$(".esri-slice__actions >button").css("font-size", "40px");
                                this.sliceFlag = true;
                            }
                            break;
                        case "通视分析":
                            if (this.sightFlag) {
                                //判断状态
                                this.lineOfSightWidget.destroy();
                                this.sightFlag = false;
                            } else {
                                if (!window.parent.document.getElementById("lineOfSight")) {
                                    //判断剖切widget状态，并提升至index
                                    let widget = window.parent.document.createElement("div");
                                    widget.id = "lineOfSight";
                                    window.parent.$(".container").append(widget);
                                }
                                window.parent.document.getElementById("lineOfSight").style.width = "300px";
                                window.parent.document.getElementById("lineOfSight").style.height = "350px";
                                console.log(window.parent.document.getElementById("lineOfSight"));
                                this.lineOfSightWidget = window.parent.ArcGisUtils.createLineOfSightWidget(
                                    view,
                                    "lineOfSight"
                                );
                                window.parent.$(".esri-slice__actions >button").css("font-size", "40px");
                                this.sightFlag = true;
                            }

                            break;
                        case "日照分析":
                            if (this.lightFlag) {
                                //判断状态
                                this.lightWidget.destroy();
                                view.environment.lighting.date = new Date(
                                    "Wed Mar 15 2023 12:01:22 GMT+0800 (中国标准时间)"
                                );
                                view.environment.lighting.directShadowsEnabled = false;
                                this.lightFlag = false;
                            } else {
                                if (!window.parent.document.getElementById("daylightWidget")) {
                                    //判断剖切widget状态，并提升至index
                                    let widget = window.parent.document.createElement("div");
                                    widget.id = "daylightWidget";
                                    window.parent.$(".container").append(widget);
                                }
                                view.environment.lighting.date = new Date();
                                this.lightWidget = window.parent.ArcGisUtils.createDaylightWidget(
                                    view,
                                    "daylightWidget"
                                );
                                window.parent.$(".esri-slice__actions >button").css("font-size", "40px");
                                window.parent.$(".esri-slider__content >button").css("width", "0");
                                window.parent.$(".esri-slider__content >button").css("height", "0");
                                this.lightFlag = true;
                            }
                            break;
                        default:
                            break;
                    }

                    if (this.iconIndex == item.id) {
                        //判断是否取消
                        if (drawType[item.name]) {
                            console.log("停止绘制工具");
                            return;
                        } else if (measureType[item.name]) {
                            console.log("停止测量工具");
                            return;
                        }
                        this.iconIndex = null;
                        return;
                    } else {
                        if (drawType[item.name]) {
                            console.log("创建绘图工具之前", window.parent.window.drawTool);
                            if (!window.parent.window?.drawTool) {
                                window.parent.window.drawTool = new window.parent.ArcGisUtils.Draw({ view });
                            }
                            console.log("创建绘图工具之后", window.parent.window.drawTool);

                            window.parent.window.drawTool.draw(drawType[item.name]);
                        } else if (item.name == "面积测量") {
                            this.areaMeasurementWidget = window.parent.ArcGisUtils.createAreaMeasurementWidget(
                                view,
                                measureType[item.name]
                            );
                        } else if (item.name == "距离测量") {
                            this.distanceMeasurementWidget = window.parent.ArcGisUtils.measuringDistance(
                                view,
                                measureType[item.name]
                            );
                        }
                    }

                    // if (this.iconIndex == item.id) {
                    //   this.iconIndex = null
                    //   return
                    // }
                    this.iconIndex =
                        item.id == 31 || item.id == 32 || item.id == 33 || item.id == 34 ? this.iconIndex : item.id; //将剖切工具排除在只允许单个按钮活动的逻辑之外
                },

                //
                areameasureClearClick() {
                    // console.log(areaMeasurementWidget);
                    if (this.areaMeasurementWidget) {
                        this.areaMeasurementWidget.clear();
                    }
                },

                distmeasureClearClick() {
                    if (this.distanceMeasurementWidget) {
                        this.distanceMeasurementWidget.clear();
                    }
                },

                sliderChange() {
                    console.log("滑块的值==>", this.sliderValue / 10);
                    const { ground } = window.parent.window.mapUtil.mapview.map;
                    ground.opacity = this.sliderValue / 10;
                },

                cleanall() {
                    let view = window.parent.window.mapUtil.mapview;
                    this.iconIndex = null;
                    this.modelFlag = false;
                    this.sliceFlag = false;
                    this.sightFlag = false;
                    this.lightFlag = false;

                    window.parent.ArcGisUtils.removePureModalLayer(view);

                    //关闭剖切工具
                    this.sliceWidget?.destroy();

                    //关闭通视工具
                    this.lineOfSightWidget?.destroy();

                    //关闭测量工具
                    this.areameasureClearClick();
                    this.distmeasureClearClick();

                    //关闭日照分析
                    this.lightWidget?.destroy();
                    view.environment.lighting.date = new Date("Wed Mar 15 2023 12:01:22 GMT+0800 (中国标准时间)");
                    view.environment.lighting.directShadowsEnabled = false;

                    //关闭绘图工具并置空
                    window.parent.window.drawTool?.destroy();
                    window.parent.window.drawTool = null;
                },
            },
        });
    </script>
</html>
