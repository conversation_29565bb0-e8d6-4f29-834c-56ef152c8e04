var zfjly = {
    get: axios.create({baseURL: "/zfjly", withCredentials: true,}).get,
    post: axios.create({baseURL: "/zfjly", withCredentials: true,}).post,
    dataInfo_set: [],
    form: {
        hostbodyArr: null,
        hostbody: null,
    },
    dataInfo_hostbody: null,
    //登录执法记录仪平台
    loginZFJLY: function () {
        this.getLoginBaseInfo("").then((res) => {
            if (res.data.code === 200) {
                this.token1 = res.data.data.page.token;
                var send = {
                    username: "jymanager",
                    pwd: "jy26957381",
                    token: res.data.data.page.token,
                };
                this.login(send).then((el) => {
                    if (el.data.code === 200) {
                        this.isShow = true;
                        this.heartbeat().then(() => {
                            this.getUserInformation().then((e) => {
                                var _this = this;
                                try {
                                    this.ws = new WebSocket(e.data.data.wsurl);
                                } catch (error) {
                                    console.log(error);
                                }
                                var data1 = {
                                    logincode: e.data.data.logincode,
                                    username: e.data.data.username,
                                    scode: e.data.data.scode,
                                    cate: e.data.data.auth_cate,
                                };
                                var psd = {
                                    command: "client_login",
                                    data: JSON.stringify(data1),
                                };
                                console.log(this.ws, "ws是什么");
                                // ws已经连接
                                this.ws.onopen = function () {
                                    console.log(
                                        psd,
                                        "---ws--已经连接----------",
                                        JSON.stringify(psd)
                                    );
                                    _this.ws.send(JSON.stringify(psd));
                                };
                                this.ws.onerror = function (e) {
                                    console.warn("scoket连接出错" + e);
                                    _this.ws.close();
                                    _this.ws = null;
                                };
                                this.ws.onclose = function () {
                                    console.log("ws-----已断开连接--------------");
                                };
                                this.ws.onmessage = function (event) {
                                    console.log("MESSAGE1: " + event.data);
                                };
                            });
                        });
                    }
                });
            }
        });
    },
    getLoginBaseInfo: function (data) {
        return this.get(`rest/index/login/get?key=${data}`)
    },
    login: function (param) {
        let {username, pwd, token, captcha_code} = param;
        let password = hex_md5(pwd);
        // let login_if = base64.encode(JSON.stringify({ username, password }));
        let login_if = "eyJ1c2VybmFtZSI6Imp5bWFuYWdlciIsInBhc3N3b3JkIjoiNTE0NzEzMDI1ZDU4ODE3N2ZiM2MwYTNkMTJhMGM4OWUifQ=="
        let data = this.param_up({login_info: login_if, token, captcha_code, 'withCredentials': true});
        return this.post('/rest/index/login/login', data);
    },
    param_up: function (param_arr) {
        var keys = Object.keys(param_arr).sort();
        var string = "";
        for (var i = 0; i < keys.length; i++) {
            var k = keys[i];
            string += k + "=" + param_arr[k] + ";";
        }
        string += hex_md5("Pe2695jingyi");
        let str_encode = encodeURIComponent(string);
        //编码后MD5加密
        param_arr.pe_signals = hex_md5(str_encode);
        return JSON.stringify(param_arr);
    },
    getUserInformation: function () {
        return this.get('/rest/user/user/get_info');
    },
    //心跳
    heartbeat: function () {
        return new Promise((resolve) => {
            this.heart();
            this.timerId = setInterval(this.heart, 20000);
            resolve();
        });
    },
    heart: function () {
        zfjly.online().then((e) => {
            console.log(e);
        });
    },
    online: function () {
        return this.get('rest/other/user/online');
    },
    //获取设备信息
    getDeviceInfo: function () {
        this.unitEquipTree("1001", "bh", "dname", false).then((res) => {
            var lineon = [];
            let info = {
                ids: []
            };
            res.data.data.forEach((item) => {
                if (item.lineon == 1) {
                    this.dataInfo_set.push(item);
                    lineon.push(item.hostbody);
                    info.ids.push(item.hostbody)
                }
            });
            this.getPosition(info).then((res) => {
                let data = res.data.data;
                data.forEach((item) => {
                    let lat = item.lat;
                    let lng = item.lng;
                    let name = item.name;
                })
            })
            this.pulldata = res.data;
            console.log(this.pulldata);
            this.dataInfo_hostbody = lineon.toString();
            this.form.hostbodyArr = this.dataInfo_hostbody;
        });
    },
    unitEquipTree: function (id = '', bh = 'bh', text = 'dname', isNewapi = false) {
        let data = {
            "id": id,
            "bh": bh,
            "text": text
        };
        this.extendSignal(data);
        // console.log(data)
        if (isNewapi) {
            return this.post('/rest/other/unitjson/gdlist_dv', data);
        } else {
            return this.post('/rest/other/unitjson/gdlist', data);
        }
    },
    extendSignal: function (target) {
        let keys = Object.keys(target),
            arr = [],
            solt = "Pe2695jingyi",
            str,
            pe_signals;
        keys.sort(); // 排序
        keys.forEach((key) => {
            const value = JSON.stringify(target[key]);
            arr.push(`${key}=${value}`);
        });
        str = arr.join(";") + hex_md5(solt);
        str = encodeURIComponent(str);
        pe_signals = hex_md5(str);
        target.pe_signals = pe_signals;
        return target;
    },
    //获取设备经纬度信息
    getPosition: function (data) {
        this.extendSignal(data)
        return this.post('/rest/gis/gismoni/get_point', data)
    },
    startLive: function() {
        var send = {
            hostbody_arr: ["T0C0223"],
        };
        this.startLiveVideo(send).then((res) => {
            if (res.data.code == 200) {
                var data;
              /*/ 将ws地址固定成nginx代理后的地址
              var ws =
                  "ws://" +
                  res.data.data[0].wsip +
                  ":" +
                  res.data.data[0].wsport +
                  "/RTSP/H264_AAC";
              /*/
              var ws = "wss://csdn.dsjj.jinhua.gov.cn:8101/pullFlow";
              //*/
                var rtsp = res.data.data[0].rtsp;
                console.log(ws, rtsp);
                this.dataInfo_set.forEach((item) => {
                    if (item.hostbody == this.form.hostbody) {
                        data = item;
                    }
                });
                let dataObj = {
                  /*/ 将ws协议修改成wss协议
                  ws: ws,
                  /*/
                  wss: ws,
                  //*/
                    rtsp: rtsp,
                    data: data,
                    isVideo: true, //true为视频拉流，false为音频拉流
                };
            } else {
                alert(res.msg);
            }
        })
    },
    //开始引流
    startLiveVideo: function (data) {
        this.extendSignal(data)
        return this.post('/rest/live/chrome/startLive', data);
    },
    startLiveAudio: function (data) {
        this.extendSignal(data)
        return this.post('/rest/live/chrome/startAudio', data)
    },
    send_cmd: function (data) {
        this.extendSignal(data)
        return this.post('/rest/gis/gismoni/send_cmd', data)
    }
}
