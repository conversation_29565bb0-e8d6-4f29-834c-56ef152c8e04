<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>指挥调度弹窗</title>
    <link rel="shortcut icon" href="#" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <link rel="stylesheet" href="/elementui/css/elementui.css" />
    <script src="/elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="https://g.alicdn.com/gdt/jsapi/1.9.22/index.js"></script>
    <script src="/static/js/jslib/s.min.vue.js"></script>
    <script src="/static/js/jslib/gdt-jsapi.js"></script>
    <script src="/static/js/jslib/watchmsg.js"></script>

    <style type="text/css" scoped>
      [v-cloak] {
        display: none;
      }

      body {
        margin: 0;
        padding: 0;
      }

      #zhdddialog {
        width: 1600px;
        height: 880px;
        position: relative;
      }

      .t-name {
        font-family: SourceHanSansCN-Bold;
        font-size: 48px;
        font-weight: normal;
        font-stretch: normal;
        /* line-height: 45px; */
        letter-spacing: 1px;
        margin-left: 55px;
        /* height: 50px; */
        line-height: 100px;
      }

      .s {
        margin-top: 5px;
        margin-left: 44px;
        display: inline-block;
        width: 190px;
        height: 48px;
        background-color: #0a619e;
        border: 1px solid #afdcfb;
        font-weight: normal;
        font-stretch: normal;
        /* line-height: 96px; */
        letter-spacing: 0px;
        color: #fefefe;
        text-indent: 12px;
        border-radius: 4px;
        text-align: center;
        font-size: 34px;
        background-image: linear-gradient(0deg, #073346 0%, #00aae2 100%),
          linear-gradient(#ffffff, #ffffff);
        background-blend-mode: normal, normal;
        border-style: solid;
        border-width: 1px;
        border-image-source: linear-gradient(0deg, #acd8f4 0%, #ecf4ff 100%);
        border-image-slice: 1;
      }

      .s1 {
        margin-top: 5px;
        margin-left: 44px;
        display: inline-block;
        width: 190px;
        height: 48px;
        background-color: #031827;
        border: 1px solid #afdcfb;
        font-weight: normal;
        font-stretch: normal;
        /* line-height: 96px; */
        letter-spacing: 0px;
        color: #fefefe;
        text-indent: 12px;
        border-radius: 4px;
        text-align: center;
        font-size: 34px;
        /* background-image: linear-gradient(0deg,
          #073346 0%,
          #00aae2 100%),
        linear-gradient(#ffffff,
          #ffffff); */
        background-blend-mode: normal, normal;
        border-style: solid;
        border-width: 1px;
        border-image-source: linear-gradient(0deg, #acd8f4 0%, #ecf4ff 100%);
        border-image-slice: 1;
      }

      select:focus-visible {
        /* outline: -webkit-focus-ring-color auto 1px; 这是谷歌浏览器的原生样式*/
        outline: none;
      }

      .zhdddialogTab {
        display: flex;
        align-items: center;
        align-items: center;
      }

      .zhdddialogTab > div {
        margin-left: 40px;
        margin-top: 42px;
        background-image: url("./image/zhdd1.png");
        background-size: cover;
        width: 220px;
        height: 59px;
        font-family: SourceHanSansCN-Regular;
        font-size: 32px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 59px;
        letter-spacing: 0px;
        color: #ffffff;
        text-align: center;
        cursor: pointer;
      }

      .zhdddialogTabActuve {
        background-image: url("./image/zhdd2.png") !important;
      }

      .formbd {
        display: flex;
        margin: 40px 0 0 0;
        align-items: center;
      }

      .zhddbox {
        width: 775px;
        height: 540px;
      }

      .box-img {
        width: 72px;
        height: 121px;
        margin: 0 20px;
        cursor: pointer;
      }

      .zhddbox-title {
        height: 80px;
        background-color: #004c70;
        margin-top: 10px;
        display: flex;
      }

      .zhddbox-title > div {
        font-family: SourceHanSansCN-Medium;
        font-size: 30px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 81px;
        letter-spacing: 0px;
        color: #ffffff;
        text-align: center;
      }

      .zhddbox-con {
        height: 450px;
        overflow-y: auto;
      }

      .zhddbox-con::-webkit-scrollbar {
        display: none;
      }

      .itemcon {
        width: 775px;
        height: 65px;
        display: flex;
      }

      .itemcon > div {
        font-family: SourceHanSansCN-Medium;
        font-size: 30px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 65px;
        letter-spacing: 0px;
        color: #ffffff;
        text-align: center;
      }

      .itemcon:hover {
        background-color: #15588a !important;
      }

      .itemconChild {
        background-color: #004c70;
      }

      input[type="checkbox"] {
        /* margin-right: 5px; */
        cursor: pointer;
        font-size: 14px;
        width: 24px;
        height: 24px;
        position: relative;
      }

      input[type="checkbox"]:after {
        position: absolute;
        width: 24px;
        height: 24px;
        top: 0;
        content: " ";
        color: #fff;
        display: inline-block;
        visibility: visible;
        /* padding: 0px 3px; */
        border-radius: 3px;
        background-color: #cde7fe;
        /* border: 1px solid #ffc561; */
      }

      input[type="checkbox"]:checked:after {
        content: "\2713";
        color: #ffc561;
        font-size: 18px;
        font-weight: 600;
        background-color: #252316;
        border: none;
        text-align: center;
        border: 1px solid #ffc561;
      }

      /* ::-webkit-scrollbar {
      display: none;
    } */

      .bottombtn {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .bottombtn > div {
        width: 240px;
        height: 73px;
        margin-right: 80px;
        margin-top: 46px;
        font-family: SourceHanSansCN-Bold;
        font-size: 32px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 73px;
        letter-spacing: 0px;
        color: #ffffff;
        text-align: center;
        cursor: pointer;
        background: linear-gradient(0deg, #387af0, #003ca6);
        border: 1px solid;
        border-image: linear-gradient(-32deg, #359cf8, #afdcfb) 1 1;
        border-radius: 10px;
      }

      .xfzlbox {
        position: fixed;
        left: -93px;
        top: 0;
        width: 2233px;
        height: 1162px;
        /* background-color: hsl(205, 86%, 8%, 0.7); */
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .xfzl {
        width: 1030px;
        height: 652px;
        border: solid 1px #afdcfb;

        background-color: #031726;
      }

      .xfzltitlt {
        display: flex;
        height: 80px;
        align-items: center;
        border-bottom: solid 1px #afdcfb;
      }

      .t-name1 {
        /* height: 80px; */
        color: #fff;
        font-size: 36px;
        flex: 1;
        margin-left: 20px;
      }

      .close1 {
        color: #fff;
        font-size: 46px;
        margin-right: 40px;
        cursor: pointer;
      }

      .xfzlitem {
        display: flex;
        align-items: center;
        margin-top: 60px;
        margin-left: 100px;
      }

      .xfzlitem > div:first-child {
        font-family: SourceHanSansCN-Medium;
        font-size: 36px;
        font-weight: normal;
        font-stretch: normal;
        /* line-height: 81px; */
        letter-spacing: 0px;
        color: #ffffff;
        min-width: 200px;
        text-align: right;
      }

      .textareadiv {
        width: 400px;
        margin-left: 40px;
      }

      .el-textarea .el-textarea__inner {
        background-color: transparent !important;
        color: #fff;
        font-size: 32px;
      }

      .bottombtn1 {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .bottombtn1 > div {
        width: 240px;
        height: 73px;
        background-image: linear-gradient(0deg, #073346 0%, #00aae2 100%),
          linear-gradient(#2891f0, #2891f0);
        background-blend-mode: normal, normal;
        border-style: solid;
        border-width: 2px;
        border-image-source: linear-gradient(-32deg, #359cf8 0%, #afdcfb 100%);
        border-image-slice: 1;
        margin-right: 80px;
        margin-top: 46px;
        font-family: SourceHanSansCN-Bold;
        font-size: 32px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 73px;
        letter-spacing: 0px;
        color: #ffffff;
        text-align: center;
        cursor: pointer;
      }

      .bzl {
        width: 10px;
        height: 40px;
        background-color: #00aae2;
        margin-left: 30px;
      }

      /* 蓝白色渐变 */
      .lanbailine {
        background: linear-gradient(
          180deg,
          #caffff 0,
          #caffff 25%,
          #ffffff 50%,
          #00c0ff 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .tooltip {
        position: absolute;
        width: 500px;
        height: 300px;
        background-color: #031726;
        border: 1px solid #fff;
        z-index: 999;
        border-radius: 10px;
        color: #fff;
        font-size: 40px;
        text-align: center;
        line-height: 300px;
      }

      .box-td {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .title {
        height: 100px;
        width: 1670px;
      }

      .tabList {
        width: 100%;
        height: 80px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        overflow-x: scroll;
        overflow-y: hidden;
        white-space: nowrap;
      }

      .tabList::-webkit-scrollbar {
        /* width: 5px; */
        margin-bottom: -10px;
        height: 5px;
        /* opacity: 0; */
        /* margin-top: -50px; */
        /* transform: scaleX(-1); */
      }

      .tabList::-webkit-scrollbar-thumb {
        border-radius: 1px;
        box-shadow: inset 0 0 5px #80e0ff;
        background: #80e0ff;
      }

      .tabList::-webkit-scrollbar-track {
        box-shadow: inset 0 0 3px #80e0ff;
        border-radius: 0;
        background: rgba(0, 0, 0, 0.1);
      }

      .tabList .tab {
        width: 166px;
        height: 60px;
        cursor: pointer;
        text-align: center;
        margin-right: 20px;
        line-height: 50px;
        box-sizing: border-box;
        float: left;
        flex-shrink: 0;
        background-color: #ffffff40;
        color: #fff;
        font-size: 22px;
        border-radius: 15px;
        border: solid 1px #ffffff40;
      }

      .tabList .tab:hover {
        color: #000000;
        background-color: #ffffff65;
        transition: 0.2s;
      }

      .tab-selected {
        border: solid 1px #ffffff !important;
      }

      .close {
        width: 46px;
        height: 78px;
        font-size: 60px;
        color: #fff;
        font-weight: 600;
        cursor: pointer;
        margin-left: 1620px;
      }
      .content {
        position: absolute;
        top: 75px;
        left: 65px;
      }
      .tabChange {
        display: flex;
        font-size: 30px;
        position: absolute;
        top: 10px;
        left: 250px;
        color: #fff;
      }
      .tabItem {
        cursor: pointer;
        margin-left: 40px;
        padding-bottom: 5px;
        border-bottom: 4px solid transparent;
        box-sizing: border-box;
      }
      .tabActive {
        color: #e6c804;
        border-bottom-color: #e6c804;
      }
      .tabIndex {
        width: 250px !important;
      }
    </style>
  </head>

  <body>
    <div id="zhdddialog" v-cloak>
      <s-dialog
        title="一键会商"
        width="1800px"
        height="1000px"
        top="52px"
        left="40px"
      ></s-dialog>
      <div class="content">
        <div class="close" @click="close()">×</div>
        <div class="tabChange">
          <div
            class="tabItem"
            v-for="(item,index) in tab"
            :class="{tabActive:activeIndex===index}"
            @click="changeTab(index)"
          >
            {{item}}
          </div>
        </div>
        <div class="title">
          <div class="tabList" v-if="data1new!=''">
            <span
              class="tab"
              :class="activeIndex===1?'tabIndex':''"
              v-for="(item,i) in officearr"
              :key="i"
              @click="changeOfficeIndex(i)"
            >
              {{item}}
            </span>
          </div>
        </div>
        <div v-if="true">
          <div class="zhdddialogTab"></div>
          <div class="formbd">
            <div class="zhddbox">
              <div class="zhddbox-title">
                <div style="flex: 0.05"></div>
                <div style="flex: 0.15">序号</div>
                <div style="flex: 0.25">职务</div>
                <div style="flex: 0.25">姓名</div>
                <div style="flex: 0.25">所属部门</div>
                <div style="flex: 0.25">联系方式</div>
              </div>
              <div class="zhddbox-con">
                <div
                  class="itemcon"
                  v-for="(item,i) in data1new[officeindex].info"
                  :key="i"
                  :class="{'itemconChild':i%2!=0}"
                >
                  <label
                    class="itemcon"
                    :for="item.userId"
                    style="cursor: pointer"
                  >
                    <div style="flex: 0.05">
                      <input
                        :id="item.userId"
                        type="checkbox"
                        :value="item"
                        v-model="checkboxIdStr"
                        @change="itemChecked(item.userId)"
                      />
                    </div>
                    <div class="box-td" style="flex: 0.15">{{i+1}}</div>
                    <div class="box-td" style="flex: 0.25">{{item.zw}}</div>
                    <div class="box-td" style="flex: 0.25">{{item.name}}</div>
                    <div class="box-td" style="flex: 0.25" :title="item.bm">
                      {{item.bm}}
                    </div>
                    <div class="box-td" style="flex: 0.25" :title="item.phone">
                      {{item.phone}}
                    </div>
                  </label>
                </div>
              </div>
            </div>
            <img
              class="box-img"
              src="/static/images/zhdd/zhdd3.png"
              alt=""
              @click="add()"
            />
            <div class="zhddbox">
              <div class="zhddbox-title">
                <div style="flex: 0.15">序号</div>
                <div style="flex: 0.25">职务</div>
                <div style="flex: 0.25">姓名</div>
                <div style="flex: 0.25">所属部门</div>
                <div style="flex: 0.25">联系方式</div>
                <div style="flex: 0.05"></div>
              </div>
              <div class="zhddbox-con">
                <div
                  class="itemcon"
                  v-for="(item,i) in checkedData"
                  :key="i"
                  :class="{'itemconChild':i%2!=0}"
                >
                  <div style="flex: 0.15">{{i+1}}</div>
                  <div class="box-td" style="flex: 0.25">{{item.zw}}</div>
                  <div class="box-td" style="flex: 0.25">{{item.name}}</div>
                  <div class="box-td" style="flex: 0.25" :title="item.bm">
                    {{item.bm}}
                  </div>
                  <div class="box-td" style="flex: 0.25" :title="item.phone">
                    {{item.phone}}
                  </div>
                  <div style="flex: 0.05; cursor: pointer" @click="del(item)">
                    x
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="bottombtn">
            <!-- <div @click="xfzlFn()">下发指令</div> -->
            <div @click="doSpth" style="width: 40%">发起会商</div>
          </div>
        </div>
        <!-- v-if="xfzlFlag" -->
        <!-- 下发指令 -->
        <div class="xfzlbox" v-if="xfzlFlag">
          <div class="tooltip" v-if="tooltipShow">{{tipText}} !</div>
          <div class="xfzl">
            <div class="xfzltitlt">
              <div class="bzl"></div>
              <div class="t-name t-name1" style="color: #fff; font-size: 36px">
                指令下达
              </div>
              <div class="close1" @click="closeZL()">×</div>
            </div>
            <div class="xfzlitem">
              <div>级别</div>
              <select class="s1" v-model="select1">
                <option :value="item.value" v-for="(item,index) in data2">
                  {{item.text}}
                </option>
              </select>
            </div>
            <div class="xfzlitem">
              <div>指令内容</div>
              <div class="textareadiv">
                <el-input
                  placeholder="请填写指令内容"
                  :rows="4"
                  type="textarea"
                  v-model="zlMsg"
                  width="200"
                ></el-input>
              </div>
            </div>
            <div class="bottombtn1">
              <div @click="doXfzl" style="margin-right: 0px; width: 400px">
                下发指令
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
  <script
    type="text/javascript"
    src="https://cdn.bootcss.com/animejs/2.2.0/anime.min.js"
  ></script>
  <script>
    var vm = new Vue({
      el: " #zhdddialog",
      data() {
        return {
          activeIndex: 0,
          tab: ["市直部门", "县市区指挥中心"],
          // dept: '',
          sgid: "",
          tipText: "提交成功",
          tabList: ["现场执法人员", "网格员"],
          zhddactiveNum: 0,
          tooltipShow: false,
          data8: [],
          officearr: [],
          officeindex: 0,
          data1new: [
            {
              office: "市公安局",
              info: [],
            },
            {
              office: "市体育局",
              info: [],
            },
            {
              office: "市水利局",
              info: [],
            },
            {
              office: "市生态环境局",
              info: [],
            },
            {
              office: "市卫健委",
              info: [],
            },
            {
              office: "市市场监管局",
              info: [],
            },
            {
              office: "市农业农村局",
              info: [],
            },
            {
              office: "市文广旅游局",
              info: [],
            },
            {
              office: "市交通运输局",
              info: [],
            },
          ],
          data1: [],
          checkboxIdStr: [],
          checkedData: [],
          select1: "0",
          data2: [
            { value: "0", text: "一般" },
            { value: "1", text: "严重" },
          ],
          xfzlFlag: false,
          zlMsg: "",

          szbmTable: [
            {
              bm: "市行政执法局",
              children: [
                { name: "黄斌", phone: "18869911666", zw: "值班领导" },
                { name: "陈旭兰", phone: "13819988550", zw: "值班人员" },
              ],
            },
            {
              bm: "市市场监管局",
              children: [
                { name: "赵国荣", phone: "13806795678", zw: "值班领导" },
                { name: "李世敏", phone: "13575917388", zw: "值班人员" },
              ],
            },
            {
              bm: "市生态环境局",
              children: [
                { name: "陈蕾妍", phone: "13588659188", zw: "值班领导" },
                { name: "金宏卓", phone: "19812375858", zw: "值班人员" },
              ],
            },
            {
              bm: "市交通运输局",
              children: [
                { name: "姜能", phone: "13905795677", zw: "值班领导" },
                { name: "王栋", phone: "15888990309", zw: "值班人员" },
              ],
            },
            {
              bm: "市文广旅游局",
              children: [
                { name: "叶顺清", phone: "13506583040", zw: "值班领导" },
                { name: "郭敏健", phone: "18257017326", zw: "值班人员" },
              ],
            },
            {
              bm: "市农业农村局",
              children: [
                { name: "卢关荣", phone: "13967968889", zw: "值班领导" },
                { name: "赵羽中", phone: "13116877858", zw: "值班人员" },
              ],
            },
            {
              bm: "市应急管理局",
              children: [
                { name: "胡宏立", phone: "13905894809", zw: "值班领导" },
                { name: "王志中", phone: "15957921229", zw: "值班人员" },
              ],
            },
            {
              bm: "市卫生健康委",
              children: [
                { name: "施志芳", phone: "13757993223", zw: "值班领导" },
                { name: "方志坚", phone: "15305897766", zw: "值班人员" },
              ],
            },
            {
              bm: "市资规局",
              children: [
                { name: "张旭辉", phone: "0579-82473602", zw: "值班领导" },
                { name: "陈璐", phone: "13957980184", zw: "值班人员" },
              ],
            },
          ],
          xsqTableData: [
            {
              bm: "婺城区行政执法指挥中心",
              children: [
                { name: "楼国伟", phone: "13806787633", zw: "值班领导" },
                { name: "周睿智", phone: "+86-13646591073", zw: "值班人员" },
              ],
            },
            {
              bm: "金东区行政执法指挥中心",
              children: [
                { name: "邢浩俊", phone: "13957997972", zw: "值班领导" },
                { name: "郑一晓", phone: "13957989312", zw: "值班人员" },
              ],
            },
            {
              bm: "兰溪市行政执法指挥中心",
              children: [
                { name: "章立峰", phone: "+86-13905896940", zw: "值班领导" },
                { name: "王聪", phone: "+86-15158998879", zw: "值班人员" },
              ],
            },
            {
              bm: "东阳市行政执法指挥中心",
              children: [
                { name: "贾新民", phone: "+86-13706799910", zw: "值班领导" },
                { name: "张江权", phone: "+86-13575996310", zw: "值班人员" },
              ],
            },
            {
              bm: "义乌市行政执法指挥中心",
              children: [
                { name: "朱跃平", phone: "+86-13516956999", zw: "值班领导" },
                { name: "余晓波", phone: "+86-18266910102", zw: "值班人员" },
              ],
            },
            {
              bm: "永康市行政执法指挥中心",
              children: [
                { name: "项宗玖", phone: "+86-15867927677", zw: "值班领导" },
                { name: "应晶晶", phone: "+86-13575694460", zw: "值班人员" },
              ],
            },
            {
              bm: "浦江县行政执法指挥中心",
              children: [
                { name: "贾国锋", phone: "+86-15867965988", zw: "值班领导" },
                { name: "张霏霏", phone: "+86-13626693106", zw: "值班人员" },
              ],
            },
            {
              bm: "武义县行政执法指挥中心",
              children: [
                { name: "李旭琴", phone: "+86-13967978275", zw: "值班领导" },
                { name: "应蔚蔚", phone: "+86-15058585955", zw: "值班人员" },
              ],
            },
            {
              bm: "磐安县行政执法指挥中心",
              children: [
                { name: "韦向良", phone: "+86-13706792387", zw: "值班领导" },
                { name: "黄艳", phone: "+86-15869296224", zw: "值班人员" },
              ],
            },
            {
              bm: "开发区行政执法指挥中心",
              children: [
                { name: "徐寿春", phone: "+86-15105793508", zw: "值班领导" },
                { name: "钱庆华", phone: "+86-13738936386", zw: "值班人员" },
              ],
            },
          ],
        };
      },
      created() {},
      mounted() {
        this.initApi();
      },
      methods: {
        initApi() {
          $get("/zhdd/szbm_zb").then((res) => {
            this.szbmTable = res;
            this.changeTab(0);
          });
          $get("/zhdd/xsqbm_zb").then((res) => {
            this.xsqTableData = res;
          });
        },
        changeTab(index) {
          let that = this;
          this.activeIndex = index;
          this.officearr = [];
          let bmArr = index == 0 ? this.szbmTable : this.xsqTableData;
          let bmData = [];
          let data = [];
          bmArr.forEach((ele) => {
            let str = [
              {
                userId: Math.floor(Math.random() * (1000 - 1) + 1),
                bm: ele.bm,
                name: ele.ld,
                zw: "值班领导",
                phone: ele.ldPhone,
              },
              {
                userId: Math.floor(Math.random() * (1000 - 1) + 1),
                bm: ele.bm,
                name: ele.person,
                zw: "值班人员",
                phone: ele.personPhone,
              },
            ];
            data.push(str);
            bmData.push(ele.bm);
          });
          this.officearr = Array.from(new Set(bmData));
          this.data1new = [];
          for (let i = 0; i < this.officearr.length; i++) {
            let a = {
              office: "this.officearr[i]",
              info: [],
            };
            for (let j = 0; j < data.length; j++) {
              if (data[j][0].bm == this.officearr[i]) {
                a.info = data[j];
              }
            }
            this.data1new.push(a);
          }
        },
        closeZL() {
          this.xfzlFlag = false;
        },
        close() {
          window.parent.frames["zhdd_middle"].vmMiddle.discussStatus = false;
          window.parent.lay.closeIframeByNames(["zhddDiscuss"]);
        },
        // 获取url参数
        getUrlParamValue(paramName) {
          const reg = new RegExp("(^|&)" + paramName + "=([^&]*)(&|$)", "i");
          const r = window.location.search.substr(1).match(reg);
          if (r != null) {
            // 解码uri
            return decodeURI(r[2]);
          } else {
            return null;
          }
        },
        zhddTabFn(i) {
          this.zhddactiveNum = i;
        },
        itemChecked(i) {
          // console.log(this.checkboxIdStr)
          //如果有input被选中,tab加上选中效果
          var obj = document.getElementsByClassName("tab");
          obj[this.officeindex].classList.add("tab-selected");
          var arr = document.getElementsByTagName("input");
          //在每次更改input状态时，检测是否全部input未被选中，如果是，则移除tab的选中效果
          var flag = false;
          for (let i = 0; i < arr.length; i++) {
            if (arr[i].checked == true) {
              flag = true;
            }
          }
          if (flag == false) {
            obj[this.officeindex].classList.remove("tab-selected");
          }
        },
        add() {
          this.checkedData = this.checkboxIdStr;
        },
        del(item) {
          let that = this;

          this.checkedData.forEach((m, n) => {
            console.log(item.userId);
            console.log(m.userId);
            if (item.userId == m.userId) {
              this.checkedData.splice(n, 1);
              // console.log(this.checkedData);
            }
          });
          this.checkboxIdStr.forEach((m, n) => {
            if (item.userId == m.userId) {
              this.checkboxIdStr.splice(n, 1);
            }
          });

          this.$nextTick(() => {
            var obj = document.getElementsByClassName("tab");
            for (let i = 0; i < obj.length; i++) {
              obj[i].classList.remove("tab-selected");
            }
          });
        },
        xfzlFn() {
          this.xfzlFlag = true;
        },
        changeOfficeIndex(i) {
          this.officeindex = i;
          // console.log(this.officeindex);
          // console.log(this.data1new);
        },
        doXfzl() {
          const that = this;
          if (!this.checkedData.length) {
            this.$message({
              message: "请至少选择一条数据并移至右侧表格",
              type: "warning",
            });
            return;
          }
          if (!this.zlMsg) {
            this.$message({
              message: "指令内容不能为空",
              type: "warning",
            });
            return;
          }
          console.log(this.checkedData);
          const phones = this.checkedData.map((item) => item.phone);
          console.log(phones, "phones");
          let params = {
            source: "jinhuacsdn",
            phones: phones,
            msg: this.zlMsg,
          };
          axios({
            method: "post",
            url:
              baseURL.url + baseURL.admApi + "/pub/dingtalk/workNotification",
            data: params,
          }).then((res) => {
            that.$message({
              message: "指令下达成功！",
              type: "success",
            });
            that.xfzlFlag = false;
          });
        },
        doSpth() {
          const that = this;
          if (!this.checkedData.length) {
            this.$message({
              message: "请至少选择一条数据并移至右侧表格",
              type: "warning",
            });
            return;
          }
          const phones = this.checkedData.map((item) => item.phone);
          let params = {
            source: "jinhuacsdn",
            phones: phones,
          };
          console.log(phones, "phones");

          axios({
            method: "post",
            url: baseURL.url + "/adm-api/pub/dingtalk/accountId",
            data: params,
          }).then(function (response) {
            let result = response.data.data;
            console.log(result);
            const accountIds = result.map((item) => item.accountId);
            console.log(accountIds);
            if (!accountIds.length) {
              that.$message({
                message: "视频会议人员的accountId均为空，发起视频会议失败",
                type: "warning",
              });
            } else {
              // dd.createVideoMeeting({
              //   title: "视频会议",
              //   calleeStaffIds: accountIds, //人员列表
              // });
              window.top.postMessage(
                {
                  accountIds,
                  type: "doVideoMeeting",
                },
                "*"
              );
            }
          });
        },
      },
    });
  </script>
</html>
