<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>Random Points with Mapbox</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="./libs/mapbox-gl.css" rel="stylesheet" />
    <script src="./libs/mapbox-gl.js"></script>
    <script src="./mock/points.js"></script>
    <script src="./mock/polygonjd.js"></script>
    <style>
      html,
      body,
      #map {
        z-index: 10;
        padding: 0;
        margin: 0;
        height: 2160px;
        width: 3840px;
      }

      #buttonContainer {
        position: relative;
        z-index: 100;
        text-align: center;
        margin-top: 50px;
      }

      .button {
        z-index: 100;
        font-size: 24px;
        width: 200px;
        height: 100px;
        background-color: #4caf50;
        color: white;
        border: none;
        cursor: pointer;
      }

      .my-class {
        max-width: 1000px !important;
        width: 1000px;
        height: 500px;
      }

      .box {
        height: 500px;
        background-color: #4caf50;
        color: antiquewhite;
      }
    </style>
  </head>

  <body>
    <script type="module">
      import MapboxMap from "./utils/MapboxMap.js";
      // 创建MapboxMap实例
      const options = {
        container: "map",
        center: [119.6623993, 29.0924835], // 设置地图中心点经纬度坐标
        zoom: 10, // 设置地图缩放级别
        accessToken:
          "pk.eyJ1IjoibGlodWJhZGJveSIsImEiOiJjbGlhMWI1cGI0YmFzM2xtd3ZtMzRuZTlsIn0.ZCwhepTq83p_JviNDK9Z3w",
      };
      const map = new MapboxMap(options);

      const pointsData = {
        type: "FeatureCollection",
        features: points.map(function (item) {
          return {
            type: "Feature",
            properties: { count: item.count },
            geometry: {
              type: "Point",
              coordinates: [item.lng, item.lat],
            },
          };
        }),
      };
      let layer1 = null,
        popupArrays = [];
      button1.addEventListener("click", function () {
        // 为添加点击事件
        map.map.on("click", "pointLayer", (e) => {
          console.log(e.features);
          popupArrays.forEach((popup) => {
            popup.remove();
          });
          popupArrays.length = 0;
          //弹窗事件
          const popup = new mapboxgl.Popup({ className: "my-class" })
            .setLngLat(e.lngLat)
            .setHTML("<h1 class='box'>Hello World!</h1>")
            .addTo(map.map);
          popupArrays.push(popup);
        });
        layer1 = map.addPointsImageLayer({
          data: pointsData,
          image_url: "./images/person.png",
          id: "pointLayer",
        });
      });

      button2.addEventListener("click", function () {
        layer1 && layer1.remove();
        popupArrays.forEach((popup) => {
          popup.remove();
        });
      });
      let layer2 = null;
      button3.addEventListener("click", function () {
        layer2 = map.addPolygonLayer({
          id: "plygonLayer",
          data: polygonjd,
          fillPaint: {
            "fill-color": "#0080ff", // blue color fill
            "fill-opacity": 0.5,
          },
          outlinePaint: {
            "line-color": "#000",
            "line-width": 3,
          },
        });
      });

      button4.addEventListener("click", function () {
        layer2 && layer2.remove();
      });
      let layer3 = null;
      button5.addEventListener("click", function () {
        const paint = {
          "heatmap-weight": {
            property: "count",
            type: "exponential",
            stops: [
              [1, 0],
              [100, 1],
            ],
          },
          "heatmap-intensity": 1.5,
          "heatmap-color": [
            "interpolate",
            ["linear"],
            ["heatmap-density"],
            0,
            "rgba(0, 0, 255, 0)",
            0.2,
            "royalblue",
            0.4,
            "cyan",
            0.6,
            "lime",
            0.8,
            "yellow",
            1,
            "red",
          ],
          "heatmap-radius": 5,
          "heatmap-opacity": 0.8,
        };
        layer3 = map.addHeatMapLayer({
          id: "heatmap",
          data: pointsData,
          paint,
        });
      });

      button6.addEventListener("click", function () {
        layer3.remove(layer3);
      });
    </script>
    <div id="map">
      <div id="buttonContainer">
        <button class="button" id="button1">点位上图</button>
        <button class="button" id="button2">点位清除</button>
        <button class="button" id="button3">全市网格</button>
        <button class="button" id="button4">网格清除</button>
        <button class="button" id="button5">二维热力</button>
        <button class="button" id="button6">删除热力图</button>
      </div>
    </div>
  </body>
</html>
