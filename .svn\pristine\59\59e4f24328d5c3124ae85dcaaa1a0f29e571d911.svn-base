var zfjly = {
    get: axios.create({baseURL: "/zfjly_yws2", withCredentials: true, headers:{Cookie: "PHPSESSID=dg87u7q1k8rlakrinpgj6alnhg;Admin-Token=" + localStorage.getItem("Admin-Token")}}).get,
    post: axios.create({baseURL: "/zfjly_yws2", withCredentials: true, headers:{Cookie: "PHPSESSID=dg87u7q1k8rlakrinpgj6alnhg;Admin-Token=" + localStorage.getItem("Admin-Token")}}).post,
    dataInfo_set: [],
    form: {
        hostbodyArr: null,
        hostbody: null,
    },
    ws: null,
    dataInfo_hostbody: null,
    //登录执法记录仪平台
    loginZFJLY: function () {
        this.getLoginBaseInfo("").then((res) => {
            // if (res.data.code === 200) {
            //     this.token1 = res.data.data.page.token;
            var that = this;
            var send = {
                username: "djcs",
                password: hex_md5("Ywjt@2023")
            };
            this.login(send).then((el) => {
                if (el.data.code === 200) {
                    this.isShow = true;
                    this.heartbeat().then(() => {
                        this.getUserInformation().then((e) => {
                            var _this = this;
                            try {
                                this.ws = new WebSocket(e.data.data.wsurl);
                            } catch (error) {
                                console.log(error);
                            }
                            var data1 = {
                                logincode: e.data.data.logincode,
                                username: e.data.data.username,
                                scode: e.data.data.scode,
                                cate: e.data.data.auth_cate,
                            };
                            var psd = {
                                command: "client_login",
                                data: JSON.stringify(data1),
                            };
                            console.log(this.ws, "ws是什么");
                            // ws已经连接
                            this.ws.onopen = function () {
                                console.log(
                                  psd,
                                  "---ws--已经连接----------",
                                  JSON.stringify(psd)
                                );
                                _this.ws.send(JSON.stringify(psd));
                            };
                            this.ws.onerror = function (e) {
                                console.warn("scoket连接出错" + e);
                                _this.ws.close();
                                _this.ws = null;
                            };
                            this.ws.onclose = function () {
                                console.log("ws-----已断开连接--------------");
                            };
                            this.ws.onmessage = function (event) {
                                console.log("MESSAGE1: " + event.data);
                                var dataInfo = JSON.parse(event.data);
                                if (dataInfo.start_live) {
                                    if (dataInfo.start_live.recorder_type == "2") {
                                        that.isC2 = true;
                                    } else {
                                        that.isC2 = false;
                                    }
                                    console.log(that.isC2);
                                    var ws =
                                      "ws:/" +
                                      dataInfo.start_live.wsInfo.wsIp +
                                      ":" +
                                      dataInfo.start_live.wsInfo.wsPort;
                                    var viewId = dataInfo.start_live.wsInfo.wsViewId;
                                    this.ws = ws;
                                    this.viewId = viewId;
                                    window.pullFlow_vms2(ws, viewId);
                                }
                                if (dataInfo.start_audio) {
                                    console.log(dataInfo);
                                    that.isAudio = true;
                                    var wss = `ws://${dataInfo.start_audio.wsInfo.wsIp}:${dataInfo.start_audio.wsInfo.wsPort}`;
                                    that.wsChannelId =
                                      dataInfo.start_audio.wsInfo.wsChannelId;
                                    that.sn = dataInfo.start_audio.wsInfo.sn;
                                    window.voice_pull_vms2(wss, that.wsChannelId);
                                }
                                if (dataInfo.preset) {
                                    console.log(dataInfo.preset.data);
                                    window.preset2(dataInfo.preset.data);
                                }
                            };
                        });
                    });
                }
            });
            // }
        });
    },
    getLoginBaseInfo: function (data) {
        return this.get(`rest/index/login/get?key=${data}`)
    },
    login: function (param) {
        let encodedData = encodeURIComponent(JSON.stringify(param));
        let str = toBase64(encodedData)
        return this.post('/rest/index/login/login', {login_info: str});
    },
    param_up: function (param_arr) {
        var keys = Object.keys(param_arr).sort();
        var string = "";
        for (var i = 0; i < keys.length; i++) {
            var k = keys[i];
            string += k + "=" + param_arr[k] + ";";
        }
        string += hex_md5("Pe2695jingyi");
        let str_encode = encodeURIComponent(string);
        //编码后MD5加密
        param_arr.pe_signals = hex_md5(str_encode);
        return JSON.stringify(param_arr);
    },
    getUserInformation: function () {
        return this.get('/rest/user/user/get_info');
    },
    //心跳
    heartbeat: function () {
        return new Promise((resolve) => {
            this.heart();
            this.timerId = setInterval(this.heart, 20000);
            resolve();
        });
    },
    heart: function () {
        zfjly.online().then((e) => {
            console.log(e);
        });
    },
    online: function () {
        return this.get('rest/other/user/online');
    },
    //获取设备信息
    getDeviceInfo: function () {
        this.unitEquipTreeGB("1001", "", [1, 2, 3, 4, 5]).then((res) => {
            console.log(res);
            var lineon = [];
            res.data.data.gblineon.forEach((item) => {
                lineon.push(item.hostbody);
            });
            this.pulldata = res.data.gblineon;
            console.log(this.pulldata);
            this.dataInfo_hostbody = lineon.toString();
            this.form.hostbodyArr = this.dataInfo_hostbody;
        });
    },
    unitEquipTree: function (id = '', bh = 'bh', text = 'dname', isNewapi = false) {
        let data = {
            "id": id,
            "bh": bh,
            "text": text
        };
        this.extendSignal(data);
        // console.log(data)
        if (isNewapi) {
            return this.post('/rest/other/unitjson/gdlist_dv', data);
        } else {
            return this.post('/rest/other/unitjson/gdlist', data);
        }
    },
    // 获取设备树-设备-国标
    unitEquipTreeGB: function(unit = '', key = '', recorder_type_arr = []) {
        let data = { unit, key, recorder_type_arr };
        return this.post('rest/monitor/videopatrols/getdevice', data);
    },
    extendSignal: function (target) {
        let keys = Object.keys(target),
          arr = [],
          solt = "Pe2695jingyi",
          str,
          pe_signals;
        keys.sort(); // 排序
        keys.forEach((key) => {
            const value = JSON.stringify(target[key]);
            arr.push(`${key}=${value}`);
        });
        str = arr.join(";") + hex_md5(solt);
        str = encodeURIComponent(str);
        pe_signals = hex_md5(str);
        target.pe_signals = pe_signals;
        return target;
    },
    //获取设备经纬度信息
    getPosition: function (data) {
        this.extendSignal(data)
        return this.post('/rest/gis/gismoni/get_point', data)
    },
    startLive: function(send) {
        this.startLiveVideo(send).then((res) => {
            if (res.data.code == 200) {
                if (res.code == 200) {
                    console.log(res);
                } else {
                    console.log(res);
                }
            } else {
                layui.use("layer", function () {
                    var layer = layui.layer;
                    layer.msg('<span style="font-size: 30px;">设备不在线</span>');
                });
                // alert(res.msg);
            }
        })
    },
    //开始引流
    startLiveVideo: function (data) {
        this.extendSignal(data)
        return this.post('/rest/live/chrome/startLive', data);
    },
    startLiveAudio: function (data) {
        this.extendSignal(data)
        return this.post('/rest/live/chrome/startAudio', data)
    },
    send_cmd: function (data) {
        this.extendSignal(data)
        return this.post('/rest/gis/gismoni/send_cmd', data)
    },
    // 结束视频呼叫接口
    stopLive: function (data) {
        this.extendSignal(data)
        return this.post('/rest/live/chrome/stopLive', data);
    },
    // 结束音频呼叫接口
    stopAudio: function (data) {
        this.extendSignal(data)
        return this.post('/rest/live/chrome/stopAudio', data)
    }
}

