<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>效果</title>

    <link
      rel="stylesheet"
      href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <!-- <script src="./libs/three-r79.min.js"></script> -->
    <script src="./index.js" type="module"></script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        left: 50%;
        width: 50%;
        height: 200px;
        display: flex;
      }

      .tools span {
        cursor: pointer;
        background-color: blue;
        width: 150px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        color: white;
      }

      #weather-box {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv">
      <div class="tools">
        <span class="pos-xy">定位</span>
        <!-- <span
          id="'pos-xy"
          onclick="ArcGisUtils.setXYPos(window.view,{x: 119.7048288269463,y: 27.99616488279615})"
          >定位</span
        > -->
      </div>
      <div
        class="input-pos"
        style="
          width: 200px;
          position: absolute;
          top: 0;
          left: 80px;
          z-index: 99;
          background: white;
        "
      >
        <label for="cheese">x: </label>
        <input type="text" class="input-x" value="119.7048288269463" />

        <label for="cheese">y: </label>
        <input type="text" class="input-y" value="27.99616488279615" />
      </div>
    </div>
  </body>
  <script>
    function handlePosClick() {
      let x = document.querySelector(".input-x").value;
      let y = document.querySelector(".input-y").value;
      console.log(x, y);

      ArcGisUtils.setXYPos(window.view, { x, y });
    }

    document.querySelector(".pos-xy").addEventListener("click", handlePosClick);
  </script>
</html>
