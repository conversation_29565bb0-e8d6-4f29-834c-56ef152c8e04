! function(e, t) {
    "object" == typeof exports && "object" == typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define([], t) : "object" == typeof exports ? exports.axios = t() : e.axios = t()
}(this, (function() {
    return function(e) {
        var t = {};

        function n(r) {
            if (t[r]) return t[r].exports;
            var o = t[r] = { i: r, l: !1, exports: {} };
            return e[r].call(o.exports, o, o.exports, n), o.l = !0, o.exports
        }

        return n.m = e, n.c = t, n.d = function(e, t, r) {
            n.o(e, t) || Object.defineProperty(e, t, { enumerable: !0, get: r })
        }, n.r = function(e) {
            "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e, "__esModule", { value: !0 })
        }, n.t = function(e, t) {
            if (1 & t && (e = n(e)), 8 & t) return e;
            if (4 & t && "object" == typeof e && e && e.__esModule) return e;
            var r = Object.create(null);
            if (n.r(r), Object.defineProperty(r, "default", {
                    enumerable: !0,
                    value: e
                }), 2 & t && "string" != typeof e)
                for (var o in e) n.d(r, o, function(t) {
                    return e[t]
                }.bind(null, o));
            return r
        }, n.n = function(e) {
            var t = e && e.__esModule ? function() {
                return e.default
            } : function() {
                return e
            };
            return n.d(t, "a", t), t
        }, n.o = function(e, t) {
            return Object.prototype.hasOwnProperty.call(e, t)
        }, n.p = "", n(n.s = 11)
    }([function(e, t, n) {
        "use strict";
        var r = n(3),
            o = Object.prototype.toString;

        function i(e) {
            return "[object Array]" === o.call(e)
        }

        function s(e) {
            return void 0 === e
        }

        function a(e) {
            return null !== e && "object" == typeof e
        }

        function u(e) {
            if ("[object Object]" !== o.call(e)) return !1;
            var t = Object.getPrototypeOf(e);
            return null === t || t === Object.prototype
        }

        function c(e) {
            return "[object Function]" === o.call(e)
        }

        function f(e, t) {
            if (null != e)
                if ("object" != typeof e && (e = [e]), i(e))
                    for (var n = 0, r = e.length; n < r; n++) t.call(null, e[n], n, e);
                else
                    for (var o in e) Object.prototype.hasOwnProperty.call(e, o) && t.call(null, e[o], o, e)
        }

        e.exports = {
            isArray: i,
            isArrayBuffer: function(e) {
                return "[object ArrayBuffer]" === o.call(e)
            },
            isBuffer: function(e) {
                return null !== e && !s(e) && null !== e.constructor && !s(e.constructor) && "function" == typeof e.constructor.isBuffer && e.constructor.isBuffer(e)
            },
            isFormData: function(e) {
                return "undefined" != typeof FormData && e instanceof FormData
            },
            isArrayBufferView: function(e) {
                return "undefined" != typeof ArrayBuffer && ArrayBuffer.isView ? ArrayBuffer.isView(e) : e && e.buffer && e.buffer instanceof ArrayBuffer
            },
            isString: function(e) {
                return "string" == typeof e
            },
            isNumber: function(e) {
                return "number" == typeof e
            },
            isObject: a,
            isPlainObject: u,
            isUndefined: s,
            isDate: function(e) {
                return "[object Date]" === o.call(e)
            },
            isFile: function(e) {
                return "[object File]" === o.call(e)
            },
            isBlob: function(e) {
                return "[object Blob]" === o.call(e)
            },
            isFunction: c,
            isStream: function(e) {
                return a(e) && c(e.pipe)
            },
            isURLSearchParams: function(e) {
                return "undefined" != typeof URLSearchParams && e instanceof URLSearchParams
            },
            isStandardBrowserEnv: function() {
                return ("undefined" == typeof navigator || "ReactNative" !== navigator.product && "NativeScript" !== navigator.product && "NS" !== navigator.product) && ("undefined" != typeof window && "undefined" != typeof document)
            },
            forEach: f,
            merge: function e() {
                var t = {};

                function n(n, r) {
                    u(t[r]) && u(n) ? t[r] = e(t[r], n) : u(n) ? t[r] = e({}, n) : i(n) ? t[r] = n.slice() : t[r] = n
                }

                for (var r = 0, o = arguments.length; r < o; r++) f(arguments[r], n);
                return t
            },
            extend: function(e, t, n) {
                return f(t, (function(t, o) {
                    e[o] = n && "function" == typeof t ? r(t, n) : t
                })), e
            },
            trim: function(e) {
                return e.trim ? e.trim() : e.replace(/^\s+|\s+$/g, "")
            },
            stripBOM: function(e) {
                return 65279 === e.charCodeAt(0) && (e = e.slice(1)), e
            }
        }
    }, function(e, t, n) {
        "use strict";
        var r = n(0),
            o = n(17),
            i = n(5),
            s = { "Content-Type": "application/x-www-form-urlencoded" };

        function a(e, t) {
            !r.isUndefined(e) && r.isUndefined(e["Content-Type"]) && (e["Content-Type"] = t)
        }

        var u, c = {
            transitional: { silentJSONParsing: !0, forcedJSONParsing: !0, clarifyTimeoutError: !1 },
            adapter: (("undefined" != typeof XMLHttpRequest || "undefined" != typeof process && "[object process]" === Object.prototype.toString.call(process)) && (u = n(6)), u),
            transformRequest: [function(e, t) {
                return o(t, "Accept"), o(t, "Content-Type"), r.isFormData(e) || r.isArrayBuffer(e) || r.isBuffer(e) || r.isStream(e) || r.isFile(e) || r.isBlob(e) ? e : r.isArrayBufferView(e) ? e.buffer : r.isURLSearchParams(e) ? (a(t, "application/x-www-form-urlencoded;charset=utf-8"), e.toString()) : r.isObject(e) || t && "application/json" === t["Content-Type"] ? (a(t, "application/json"), function(e, t, n) {
                    if (r.isString(e)) try {
                        return (t || JSON.parse)(e), r.trim(e)
                    } catch (e) {
                        if ("SyntaxError" !== e.name) throw e
                    }
                    return (n || JSON.stringify)(e)
                }(e)) : e
            }],
            transformResponse: [function(e) {
                var t = this.transitional || c.transitional,
                    n = t && t.silentJSONParsing,
                    o = t && t.forcedJSONParsing,
                    s = !n && "json" === this.responseType;
                if (s || o && r.isString(e) && e.length) try {
                    return JSON.parse(e)
                } catch (e) {
                    if (s) {
                        if ("SyntaxError" === e.name) throw i(e, this, "E_JSON_PARSE");
                        throw e
                    }
                }
                return e
            }],
            timeout: 0,
            xsrfCookieName: "XSRF-TOKEN",
            xsrfHeaderName: "X-XSRF-TOKEN",
            maxContentLength: -1,
            maxBodyLength: -1,
            validateStatus: function(e) {
                return e >= 200 && e < 300
            },
            headers: { common: { Accept: "application/json, text/plain, */*" } }
        };
        r.forEach(["delete", "get", "head"], (function(e) {
            c.headers[e] = {}
        })), r.forEach(["post", "put", "patch"], (function(e) {
            c.headers[e] = r.merge(s)
        })), e.exports = c
    }, function(e, t, n) {
        "use strict";

        function r(e) {
            this.message = e
        }

        r.prototype.toString = function() {
            return "Cancel" + (this.message ? ": " + this.message : "")
        }, r.prototype.__CANCEL__ = !0, e.exports = r
    }, function(e, t, n) {
        "use strict";
        e.exports = function(e, t) {
            return function() {
                for (var n = new Array(arguments.length), r = 0; r < n.length; r++) n[r] = arguments[r];
                return e.apply(t, n)
            }
        }
    }, function(e, t, n) {
        "use strict";
        var r = n(0);

        function o(e) {
            return encodeURIComponent(e).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]")
        }

        e.exports = function(e, t, n) {
            if (!t) return e;
            var i;
            if (n) i = n(t);
            else if (r.isURLSearchParams(t)) i = t.toString();
            else {
                var s = [];
                r.forEach(t, (function(e, t) {
                    null != e && (r.isArray(e) ? t += "[]" : e = [e], r.forEach(e, (function(e) {
                        r.isDate(e) ? e = e.toISOString() : r.isObject(e) && (e = JSON.stringify(e)), s.push(o(t) + "=" + o(e))
                    })))
                })), i = s.join("&")
            }
            if (i) {
                var a = e.indexOf("#"); -
                1 !== a && (e = e.slice(0, a)), e += (-1 === e.indexOf("?") ? "?" : "&") + i
            }
            return e
        }
    }, function(e, t, n) {
        "use strict";
        e.exports = function(e, t, n, r, o) {
            return e.config = t, n && (e.code = n), e.request = r, e.response = o, e.isAxiosError = !0, e.toJSON = function() {
                return {
                    message: this.message,
                    name: this.name,
                    description: this.description,
                    number: this.number,
                    fileName: this.fileName,
                    lineNumber: this.lineNumber,
                    columnNumber: this.columnNumber,
                    stack: this.stack,
                    config: this.config,
                    code: this.code,
                    status: this.response && this.response.status ? this.response.status : null
                }
            }, e
        }
    }, function(e, t, n) {
        "use strict";
        var r = n(0),
            o = n(18),
            i = n(19),
            s = n(4),
            a = n(20),
            u = n(23),
            c = n(24),
            f = n(7),
            l = n(1),
            p = n(2);
        e.exports = function(e) {
            return new Promise((function(t, n) {
                var d, h = e.data,
                    m = e.headers,
                    v = e.responseType;

                function y() {
                    e.cancelToken && e.cancelToken.unsubscribe(d), e.signal && e.signal.removeEventListener("abort", d)
                }

                r.isFormData(h) && delete m["Content-Type"];
                var g = new XMLHttpRequest;
                if (e.auth) {
                    var b = e.auth.username || "",
                        x = e.auth.password ? unescape(encodeURIComponent(e.auth.password)) : "";
                    m.Authorization = "Basic " + btoa(b + ":" + x)
                }
                var w = a(e.baseURL, e.url);

                function E() {
                    if (g) {
                        var r = "getAllResponseHeaders" in g ? u(g.getAllResponseHeaders()) : null,
                            i = {
                                data: v && "text" !== v && "json" !== v ? g.response : g.responseText,
                                status: g.status,
                                statusText: g.statusText,
                                headers: r,
                                config: e,
                                request: g
                            };
                        o((function(e) {
                            t(e), y()
                        }), (function(e) {
                            n(e), y()
                        }), i), g = null
                    }
                }

                if (g.open(e.method.toUpperCase(), s(w, e.params, e.paramsSerializer), !0), g.timeout = e.timeout, "onloadend" in g ? g.onloadend = E : g.onreadystatechange = function() {
                        g && 4 === g.readyState && (0 !== g.status || g.responseURL && 0 === g.responseURL.indexOf("file:")) && setTimeout(E)
                    }, g.onabort = function() {
                        g && (n(f("Request aborted", e, "ECONNABORTED", g)), g = null)
                    }, g.onerror = function() {
                        n(f("Network Error", e, null, g)), g = null
                    }, g.ontimeout = function() {
                        var t = e.timeout ? "timeout of " + e.timeout + "ms exceeded" : "timeout exceeded",
                            r = e.transitional || l.transitional;
                        e.timeoutErrorMessage && (t = e.timeoutErrorMessage), n(f(t, e, r.clarifyTimeoutError ? "ETIMEDOUT" : "ECONNABORTED", g)), g = null
                    }, r.isStandardBrowserEnv()) {
                    var S = (e.withCredentials || c(w)) && e.xsrfCookieName ? i.read(e.xsrfCookieName) : void 0;
                    S && (m[e.xsrfHeaderName] = S)
                }
                "setRequestHeader" in g && r.forEach(m, (function(e, t) {
                    void 0 === h && "content-type" === t.toLowerCase() ? delete m[t] : g.setRequestHeader(t, e)
                })), r.isUndefined(e.withCredentials) || (g.withCredentials = !!e.withCredentials), v && "json" !== v && (g.responseType = e.responseType), "function" == typeof e.onDownloadProgress && g.addEventListener("progress", e.onDownloadProgress), "function" == typeof e.onUploadProgress && g.upload && g.upload.addEventListener("progress", e.onUploadProgress), (e.cancelToken || e.signal) && (d = function(e) {
                    g && (n(!e || e && e.type ? new p("canceled") : e), g.abort(), g = null)
                }, e.cancelToken && e.cancelToken.subscribe(d), e.signal && (e.signal.aborted ? d() : e.signal.addEventListener("abort", d))), h || (h = null), g.send(h)
            }))
        }
    }, function(e, t, n) {
        "use strict";
        var r = n(5);
        e.exports = function(e, t, n, o, i) {
            var s = new Error(e);
            return r(s, t, n, o, i)
        }
    }, function(e, t, n) {
        "use strict";
        e.exports = function(e) {
            return !(!e || !e.__CANCEL__)
        }
    }, function(e, t, n) {
        "use strict";
        var r = n(0);
        e.exports = function(e, t) {
            t = t || {};
            var n = {};

            function o(e, t) {
                return r.isPlainObject(e) && r.isPlainObject(t) ? r.merge(e, t) : r.isPlainObject(t) ? r.merge({}, t) : r.isArray(t) ? t.slice() : t
            }

            function i(n) {
                return r.isUndefined(t[n]) ? r.isUndefined(e[n]) ? void 0 : o(void 0, e[n]) : o(e[n], t[n])
            }

            function s(e) {
                if (!r.isUndefined(t[e])) return o(void 0, t[e])
            }

            function a(n) {
                return r.isUndefined(t[n]) ? r.isUndefined(e[n]) ? void 0 : o(void 0, e[n]) : o(void 0, t[n])
            }

            function u(n) {
                return n in t ? o(e[n], t[n]) : n in e ? o(void 0, e[n]) : void 0
            }

            var c = {
                url: s,
                method: s,
                data: s,
                baseURL: a,
                transformRequest: a,
                transformResponse: a,
                paramsSerializer: a,
                timeout: a,
                timeoutMessage: a,
                withCredentials: a,
                adapter: a,
                responseType: a,
                xsrfCookieName: a,
                xsrfHeaderName: a,
                onUploadProgress: a,
                onDownloadProgress: a,
                decompress: a,
                maxContentLength: a,
                maxBodyLength: a,
                transport: a,
                httpAgent: a,
                httpsAgent: a,
                cancelToken: a,
                socketPath: a,
                responseEncoding: a,
                validateStatus: u
            };
            return r.forEach(Object.keys(e).concat(Object.keys(t)), (function(e) {
                var t = c[e] || i,
                    o = t(e);
                r.isUndefined(o) && t !== u || (n[e] = o)
            })), n
        }
    }, function(e, t) {
        e.exports = { version: "0.24.0" }
    }, function(e, t, n) {
        e.exports = n(12)
    }, function(e, t, n) {
        "use strict";
        var r = n(0),
            o = n(3),
            i = n(13),
            s = n(9);
        var a = function e(t) {
            var n = new i(t),
                a = o(i.prototype.request, n);
            return r.extend(a, i.prototype, n), r.extend(a, n), a.create = function(n) {
                return e(s(t, n))
            }, a
        }(n(1));
        a.Axios = i, a.Cancel = n(2), a.CancelToken = n(26), a.isCancel = n(8), a.VERSION = n(10).version, a.all = function(e) {
            return Promise.all(e)
        }, a.spread = n(27), a.isAxiosError = n(28), e.exports = a, e.exports.default = a
    }, function(e, t, n) {
        "use strict";
        var r = n(0),
            o = n(4),
            i = n(14),
            s = n(15),
            a = n(9),
            u = n(25),
            c = u.validators;

        function f(e) {
            this.defaults = e, this.interceptors = { request: new i, response: new i }
        }

        f.prototype.request = function(e) {
            "string" == typeof e ? (e = arguments[1] || {}).url = arguments[0] : e = e || {}, (e = a(this.defaults, e)).method ? e.method = e.method.toLowerCase() : this.defaults.method ? e.method = this.defaults.method.toLowerCase() : e.method = "get";
            var t = e.transitional;
            void 0 !== t && u.assertOptions(t, {
                silentJSONParsing: c.transitional(c.boolean),
                forcedJSONParsing: c.transitional(c.boolean),
                clarifyTimeoutError: c.transitional(c.boolean)
            }, !1);
            var n = [],
                r = !0;
            this.interceptors.request.forEach((function(t) {
                "function" == typeof t.runWhen && !1 === t.runWhen(e) || (r = r && t.synchronous, n.unshift(t.fulfilled, t.rejected))
            }));
            var o, i = [];
            if (this.interceptors.response.forEach((function(e) {
                    i.push(e.fulfilled, e.rejected)
                })), !r) {
                var f = [s, void 0];
                for (Array.prototype.unshift.apply(f, n), f = f.concat(i), o = Promise.resolve(e); f.length;) o = o.then(f.shift(), f.shift());
                return o
            }
            for (var l = e; n.length;) {
                var p = n.shift(),
                    d = n.shift();
                try {
                    l = p(l)
                } catch (e) {
                    d(e);
                    break
                }
            }
            try {
                o = s(l)
            } catch (e) {
                return Promise.reject(e)
            }
            for (; i.length;) o = o.then(i.shift(), i.shift());
            return o
        }, f.prototype.getUri = function(e) {
            return e = a(this.defaults, e), o(e.url, e.params, e.paramsSerializer).replace(/^\?/, "")
        }, r.forEach(["delete", "get", "head", "options"], (function(e) {
            f.prototype[e] = function(t, n) {
                return this.request(a(n || {}, { method: e, url: t, data: (n || {}).data }))
            }
        })), r.forEach(["post", "put", "patch"], (function(e) {
            f.prototype[e] = function(t, n, r) {
                return this.request(a(r || {}, { method: e, url: t, data: n }))
            }
        })), e.exports = f
    }, function(e, t, n) {
        "use strict";
        var r = n(0);

        function o() {
            this.handlers = []
        }

        o.prototype.use = function(e, t, n) {
            return this.handlers.push({
                fulfilled: e,
                rejected: t,
                synchronous: !!n && n.synchronous,
                runWhen: n ? n.runWhen : null
            }), this.handlers.length - 1
        }, o.prototype.eject = function(e) {
            this.handlers[e] && (this.handlers[e] = null)
        }, o.prototype.forEach = function(e) {
            r.forEach(this.handlers, (function(t) {
                null !== t && e(t)
            }))
        }, e.exports = o
    }, function(e, t, n) {
        "use strict";
        var r = n(0),
            o = n(16),
            i = n(8),
            s = n(1),
            a = n(2);

        function u(e) {
            if (e.cancelToken && e.cancelToken.throwIfRequested(), e.signal && e.signal.aborted) throw new a("canceled")
        }

        e.exports = function(e) {
            return u(e), e.headers = e.headers || {}, e.data = o.call(e, e.data, e.headers, e.transformRequest), e.headers = r.merge(e.headers.common || {}, e.headers[e.method] || {}, e.headers), r.forEach(["delete", "get", "head", "post", "put", "patch", "common"], (function(t) {
                delete e.headers[t]
            })), (e.adapter || s.adapter)(e).then((function(t) {
                return u(e), t.data = o.call(e, t.data, t.headers, e.transformResponse), t
            }), (function(t) {
                return i(t) || (u(e), t && t.response && (t.response.data = o.call(e, t.response.data, t.response.headers, e.transformResponse))), Promise.reject(t)
            }))
        }
    }, function(e, t, n) {
        "use strict";
        var r = n(0),
            o = n(1);
        e.exports = function(e, t, n) {
            var i = this || o;
            return r.forEach(n, (function(n) {
                e = n.call(i, e, t)
            })), e
        }
    }, function(e, t, n) {
        "use strict";
        var r = n(0);
        e.exports = function(e, t) {
            r.forEach(e, (function(n, r) {
                r !== t && r.toUpperCase() === t.toUpperCase() && (e[t] = n, delete e[r])
            }))
        }
    }, function(e, t, n) {
        "use strict";
        var r = n(7);
        e.exports = function(e, t, n) {
            var o = n.config.validateStatus;
            n.status && o && !o(n.status) ? t(r("Request failed with status code " + n.status, n.config, null, n.request, n)) : e(n)
        }
    }, function(e, t, n) {
        "use strict";
        var r = n(0);
        e.exports = r.isStandardBrowserEnv() ? {
            write: function(e, t, n, o, i, s) {
                var a = [];
                a.push(e + "=" + encodeURIComponent(t)), r.isNumber(n) && a.push("expires=" + new Date(n).toGMTString()), r.isString(o) && a.push("path=" + o), r.isString(i) && a.push("domain=" + i), !0 === s && a.push("secure"), document.cookie = a.join("; ")
            },
            read: function(e) {
                var t = document.cookie.match(new RegExp("(^|;\\s*)(" + e + ")=([^;]*)"));
                return t ? decodeURIComponent(t[3]) : null
            },
            remove: function(e) {
                this.write(e, "", Date.now() - 864e5)
            }
        } : {
            write: function() {},
            read: function() {
                return null
            },
            remove: function() {}
        }
    }, function(e, t, n) {
        "use strict";
        var r = n(21),
            o = n(22);
        e.exports = function(e, t) {
            return e && !r(t) ? o(e, t) : t
        }
    }, function(e, t, n) {
        "use strict";
        e.exports = function(e) {
            return /^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)
        }
    }, function(e, t, n) {
        "use strict";
        e.exports = function(e, t) {
            return t ? e.replace(/\/+$/, "") + "/" + t.replace(/^\/+/, "") : e
        }
    }, function(e, t, n) {
        "use strict";
        var r = n(0),
            o = ["age", "authorization", "content-length", "content-type", "etag", "expires", "from", "host", "if-modified-since", "if-unmodified-since", "last-modified", "location", "max-forwards", "proxy-authorization", "referer", "retry-after", "user-agent"];
        e.exports = function(e) {
            var t, n, i, s = {};
            return e ? (r.forEach(e.split("\n"), (function(e) {
                if (i = e.indexOf(":"), t = r.trim(e.substr(0, i)).toLowerCase(), n = r.trim(e.substr(i + 1)), t) {
                    if (s[t] && o.indexOf(t) >= 0) return;
                    s[t] = "set-cookie" === t ? (s[t] ? s[t] : []).concat([n]) : s[t] ? s[t] + ", " + n : n
                }
            })), s) : s
        }
    }, function(e, t, n) {
        "use strict";
        var r = n(0);
        e.exports = r.isStandardBrowserEnv() ? function() {
            var e, t = /(msie|trident)/i.test(navigator.userAgent),
                n = document.createElement("a");

            function o(e) {
                var r = e;
                return t && (n.setAttribute("href", r), r = n.href), n.setAttribute("href", r), {
                    href: n.href,
                    protocol: n.protocol ? n.protocol.replace(/:$/, "") : "",
                    host: n.host,
                    search: n.search ? n.search.replace(/^\?/, "") : "",
                    hash: n.hash ? n.hash.replace(/^#/, "") : "",
                    hostname: n.hostname,
                    port: n.port,
                    pathname: "/" === n.pathname.charAt(0) ? n.pathname : "/" + n.pathname
                }
            }

            return e = o(window.location.href),
                function(t) {
                    var n = r.isString(t) ? o(t) : t;
                    return n.protocol === e.protocol && n.host === e.host
                }
        }() : function() {
            return !0
        }
    }, function(e, t, n) {
        "use strict";
        var r = n(10).version,
            o = {};
        ["object", "boolean", "number", "function", "string", "symbol"].forEach((function(e, t) {
            o[e] = function(n) {
                return typeof n === e || "a" + (t < 1 ? "n " : " ") + e
            }
        }));
        var i = {};
        o.transitional = function(e, t, n) {
            function o(e, t) {
                return "[Axios v" + r + "] Transitional option '" + e + "'" + t + (n ? ". " + n : "")
            }

            return function(n, r, s) {
                if (!1 === e) throw new Error(o(r, " has been removed" + (t ? " in " + t : "")));
                return t && !i[r] && (i[r] = !0, console.warn(o(r, " has been deprecated since v" + t + " and will be removed in the near future"))), !e || e(n, r, s)
            }
        }, e.exports = {
            assertOptions: function(e, t, n) {
                if ("object" != typeof e) throw new TypeError("options must be an object");
                for (var r = Object.keys(e), o = r.length; o-- > 0;) {
                    var i = r[o],
                        s = t[i];
                    if (s) {
                        var a = e[i],
                            u = void 0 === a || s(a, i, e);
                        if (!0 !== u) throw new TypeError("option " + i + " must be " + u)
                    } else if (!0 !== n) throw Error("Unknown option " + i)
                }
            },
            validators: o
        }
    }, function(e, t, n) {
        "use strict";
        var r = n(2);

        function o(e) {
            if ("function" != typeof e) throw new TypeError("executor must be a function.");
            var t;
            this.promise = new Promise((function(e) {
                t = e
            }));
            var n = this;
            this.promise.then((function(e) {
                if (n._listeners) {
                    var t, r = n._listeners.length;
                    for (t = 0; t < r; t++) n._listeners[t](e);
                    n._listeners = null
                }
            })), this.promise.then = function(e) {
                var t, r = new Promise((function(e) {
                    n.subscribe(e), t = e
                })).then(e);
                return r.cancel = function() {
                    n.unsubscribe(t)
                }, r
            }, e((function(e) {
                n.reason || (n.reason = new r(e), t(n.reason))
            }))
        }

        o.prototype.throwIfRequested = function() {
            if (this.reason) throw this.reason
        }, o.prototype.subscribe = function(e) {
            this.reason ? e(this.reason) : this._listeners ? this._listeners.push(e) : this._listeners = [e]
        }, o.prototype.unsubscribe = function(e) {
            if (this._listeners) {
                var t = this._listeners.indexOf(e); -
                1 !== t && this._listeners.splice(t, 1)
            }
        }, o.source = function() {
            var e;
            return {
                token: new o((function(t) {
                    e = t
                })),
                cancel: e
            }
        }, e.exports = o
    }, function(e, t, n) {
        "use strict";
        e.exports = function(e) {
            return function(t) {
                return e.apply(null, t)
            }
        }
    }, function(e, t, n) {
        "use strict";
        e.exports = function(e) {
            return "object" == typeof e && !0 === e.isAxiosError
        }
    }])
}));
//# sourceMappingURL=axios.min.map