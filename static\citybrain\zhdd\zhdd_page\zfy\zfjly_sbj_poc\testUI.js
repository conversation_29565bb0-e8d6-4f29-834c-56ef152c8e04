//==========检测webrtc/wasm
if (poc.isWebRTCSupported) {
    console.log("[POC] Info: WebRTC is supported!");
} else {
    console.log("[POC] Warning: WebRTC is unsupported!");
}

if (poc.isWebAssemblySupported) {
    console.log("[POC] Info: WebAssembly is supported!");
} else {
    console.log("[POC] Warning: WebAssembly is unsupported!");
}

if (!poc.isWebRTCSupported || !poc.isWebAssemblySupported) {
    var h1 = document.getElementsByTagName('h1')[0];
    h1.innerHTML = "WebRTC or WebAssembly unsupported!";
} else {
    poc.ptt.init(function (err) {
        if (err != null) {
            if (err.name == "NotFoundError") {
                alert(err + ". PTT Listen Only!");
            } else {
                alert(err + ". POC PTT init has error! Some functions may not be available.");
            }
        }
        document.getElementById('enterButton').disabled = false;
        poc.ptt.setLog(true);
        wasmOnLoad();
    });
}

var html_log = function (msg) {
    document.getElementById('logs').innerHTML += msg + '<br>'
}
//===============

const pttButtonOnTalk = " Release "
const pttButtonOnIdle = " Request "
const pttButtonOnQueue = " OutQueue "

const videoButtonMute = " Mute "
const videoButtonUnmute = " Unmute "
const videoButtonPause = " Pause "
const videoButtonResume = " Resume "

var gVideoSession = 0;
var gVideoOwner = "";

var gUID = "";
var gPWD = "";

function wasmOnLoad() {
    window.logClean = function () {
        document.getElementById('logs').innerHTML = "";
    }

    window.login = function () {
        var addr = document.getElementById('inputAddr').value;
        gUID = document.getElementById('inputUserID').value;
        gPWD = document.getElementById('inputPwd').value;
        poc.ptt.doLogin(addr, gUID, gPWD);
    }
    window.logout = function () {
        poc.ptt.doLoginOut();
    }

    window.ptt = function () {
        var session = currSession();
        var users = currUsers();
        if (document.getElementById("pttButton").innerHTML == pttButtonOnIdle) {
            if (users.length > 0) { //广播带用户
                poc.ptt.doTalkRequestWithUserlist(session, users.split(","));
            } else { //out queue or release
                poc.ptt.doTalkRequest(session);
            }
        } else {
            poc.ptt.doTalkRelease(session);
        }
    }

    window.refreshMic = function () {
        document.getElementById('enterButton').disabled = true;

        poc.ptt.refreshMic().then(function (x) {
            document.getElementById('enterButton').disabled = false;
        }).catch(function (err) {
            alert("load error: " + err);
        });
    }

    window.channelEnterButton = function () {
        var sid = currSID();
        poc.ptt.doChannelEnter(sid, function (session) {
            html_log("channelEnter: sid=" + sid + " session=" + session);
        })
    }

    window.channelExitButton = function () {
        var sid = currSID();
        poc.ptt.doChannelExit(sid);
        html_log("channelExit: sid=" + sid);
    }

    window.dialogCall = function () {
        var inputStr = currSID();
        if (inputStr == null || inputStr.length <= 0) {
            return;
        }
        var t = inputType();
        if (t == "sid" || t == "tempsid") {
            poc.ptt.doSessionTempCall("", inputStr, 0, function (session) {
                html_log("doSessionTempCall: sid=" + inputStr + " session=" + session);
            });
        } else if (t == "tempuser") {
            var users = inputStr.split(",");
            poc.ptt.doSessionTempCall(users, "", 0, function (session) {
                html_log("doSessionTempCall: users=" + users + " session=" + session);
            });
        } else if (t == "tempinvite") {
            var users = inputStr.split(",");
            var session = currSession();
            poc.ptt.doSessionCallInvite(session, users);
            html_log("doSessionCallInvite: users=" + users + " session=" + session);
        }
    }

    window.dialogBye = function () {
        var session = currSession();
        poc.ptt.doLeaveCall(session);
        html_log("dialogBye: session=" + session);
    }

    window.sendIM = function () {
        var im = inputSendIM();
        var users = currUsers().split(",");
        poc.ptt.doSessionMessageSend(2, null, users, im, null, null, true);
    }

    window.upload = function () {
        var inputElem = document.createElement('input');
        inputElem.type = "file";
        inputElem.onchange = function () {
            poc.ptt.doResourceUpload(uploadType(), inputElem.files[0]).then(function (response) {
                if (response.ok && response.status == 200) {
                    response.text().then(function (text) {
                        var parser = new DOMParser();
                        var xmlDoc = parser.parseFromString(text, "text/xml");
                        var urlElem = xmlDoc.querySelector("fileUrl");
                        var urlText = urlElem.innerText || urlElem.textContent;
                        document.getElementById('uploadMsg').innerHTML = urlText;
                    }).catch(function (err) {  //http text get failed
                        document.getElementById('uploadMsg').innerHTML = "err=" + err + ",response=" + JSON.stringify(response);
                        console.log("window.upload=>doResourceUpload catch. response=" + JSON.stringify(response) + ",err=" + err);
                    });
                }
                else {  //http upload network failed
                    document.getElementById('uploadMsg').innerHTML = "response=" + JSON.stringify(response);
                    console.log("window.upload=>doResourceUpload failed. response=" + JSON.stringify(response));
                }
            }).catch(function (err) { //http upload failed
                document.getElementById('uploadMsg').innerHTML = "err=" + err;
                console.log("window.upload=>doResourceUpload catch. err=" + err);
            });
        };
        inputElem.click();
    }

    //video
    window.videoPause = function () {
        var btn = document.getElementById("videoPauseBtn");
        if (btn.innerHTML == videoButtonPause) {
            poc.video.pause(gVideoSession);
            btn.innerHTML = videoButtonResume
        } else if (btn.innerHTML == videoButtonResume) {
            poc.video.resume(gVideoSession);
            btn.innerHTML = videoButtonPause
        }
    }

    window.videoMute = function () {
        var btn = document.getElementById("videoMuteBtn");
        if (btn.innerHTML == videoButtonMute) {
            poc.video.mute(gVideoSession, true);
            btn.innerHTML = videoButtonUnmute
        } else if (btn.innerHTML == videoButtonUnmute) {
            poc.video.unmute(gVideoSession, true);
            btn.innerHTML = videoButtonMute
        }
    }

    window.videoStop = function () {
        cleanVideoDup(); //清理多余标签
        poc.video.stop(gVideoSession);
    }

    window.videoDup = function () {
        var v = document.createElement("video");
        v.autoplay = true;
        v.className = "videoDup";
        document.getElementById("videoPlay").after(v);
        poc.video.play(gVideoSession, v);
    }

    window.videoSnapshot = function () {
        var snapshotB64 = poc.video.snapshot(gVideoSession);
        poc.ptt.doSnapshotUpload(gVideoOwner, snapshotB64);
        html_log("videoSnapshot: session=" + gVideoSession);
    }

    //set default uid/pwd
    //获取cookie字符串
    var strCookie = document.cookie;
    //将多cookie切割为多个名/值对
    var arrCookie = strCookie.split("; ");
    //遍历cookie数组，处理每个cookie对
    for (var i = 0; i < arrCookie.length; i++) {
        var arr = arrCookie[i].split("=");
        //找到名称为userId的cookie，并返回它的值
        if ("uid" == arr[0]) {
            document.getElementById('inputUserID').value = arr[1];
            html_log("load uid from cookie: " + arr[1]);
        } else if ("pwd" == arr[0]) {
            document.getElementById('inputPwd').value = arr[1];
            html_log("load pwd from cookie: " + arr[1]);
        }
    }

    html_log("wasmOnLoad complete!");
}

//=============callback==========

poc.ptt.onLogin = function (result, secret) {
    if (result == 0) {
        //登陆成功,记住用户名密码
        document.cookie = "uid=" + gUID;
        document.cookie = "pwd=" + gPWD;
    }
    html_log("onLogin: result=" + result + " secret=" + secret);
}

poc.ptt.onLogout = function (result, reason) {
    html_log("onLogout: result=" + result + " reason=" + reason);
}

poc.ptt.onMediaStateTalk = function (session, result) {
    if (session == currSession()) {
        document.getElementById("pttButton").innerHTML = result == 0 ? pttButtonOnTalk : pttButtonOnQueue;
    }
    html_log("onMediaStateTalk: session=" + session + " result=" + result);
}

poc.ptt.onMediaStateIdle = function (session, reason) {
    if (session == currSession()) {
        document.getElementById("pttButton").innerHTML = pttButtonOnIdle;
    }
    html_log("onMediaStateIdle: session=" + session + " reason=" + reason);
}

poc.ptt.onMediaStateListern = function (session, speaker) {
    html_log("onMediaStateListern: session=" + session + " speaker=" + speaker);
}

poc.ptt.onChatRoomEnter = function (replyOk, session) {
    if (replyOk == 0)
        document.getElementById("pttButton").innerHTML = pttButtonOnIdle;

    document.getElementById('inputSession').value = session;
    html_log("onChatRoomEnter: session=" + session + " replyOk=" + replyOk);
}

poc.ptt.onChatRoomExit = function (session, reason) {
    document.getElementById('inputSession').value = "";
    html_log("onChatRoomExit: " + session + " reason:" + reason);
}

poc.ptt.onSessionOutgoingRinging = function (session, sid) {
    html_log("onSessionOutgoingRinging: session=" + session + ",sid=" + sid);
}

poc.ptt.onSessionIncomingAlert = function (session, user, sid) {
    html_log("onSessionIncomingAlert begin: " + session + " sid: " + sid + " callerID: " + user);

    setTimeout(function () {
        var r = confirm("onSessionIncomingAlert: sid =" + sid + "callerID=" + user + " session=" + session);
        if (r == true) {
            poc.ptt.doAcceptCall(session);
        } else {
            poc.ptt.doRejectCall(session);
        }
        html_log("onSessionIncomingAlert end: " + session + " sid: " + sid + " callerID: " + user + " accept=" + r);
    }, 100);
}

poc.ptt.onSessionRelease = function (session, reason) {
    document.getElementById('inputSession').value = "";
    html_log("onSessionRelease: " + session + " reason: " + reason);
}

poc.ptt.onSessionEstablish = function (session) {
    document.getElementById("pttButton").innerHTML = pttButtonOnIdle;
    document.getElementById('inputSession').value = session;
    html_log("onSessionEstablish: " + session);
}

poc.ptt.onContactPresence = function (userList) {
    /*
        JSON字段:
        ipocid: 用户帐号
        name: 用户名称
        userstate: 状态 (1: 前台在线, 2: 后台在线, 3: 离线)
        参数实例:
        [
            {
                "ipocid":"13918765678"
                "name:":"123"
                "userstate": 1
            },
            {
                "ipocid":"13918765678"
                "name:":"1233 "
                "userstate": 2
            }
        ]
    */

    for (var i = 0; i < userList.length; i++) {
        html_log("onContactPresence:  id=" + userList[i].ipocid + ",name=" + userList[i].name + ",state=" + userStateString(userList[i].userstate));
    }
}

poc.ptt.onSessionChannelPresence = function (userList) {
    /*
     {
         "sessionindex": 1,
         "sessionmember": [
             {
                 "ipocid": "32434",
                 "userstate": 1
             },
             {
                 "ipocid": "89589",
                 "userstate": 1
             }
         ]
     }
    */
    for (var i = 0; i < userList.sessionmember.length; i++) {
        html_log("onSessionChannelPresence:  session=" + userList.sessionindex + ",id=" + userList.sessionmember[i].ipocid + ",state=" + userStateString(userList.sessionmember[i].userstate));
    }
}

poc.ptt.onSessionChannelUserEnter = function (userList) {
    // {
    //     "sessionindex": 1,
    //         "sessionmember": [
    //             {
    //                 "ipocid": "32434"
    //             }
    //         ]
    // }
    for (var i = 0; i < userList.sessionmember.length; i++) {
        html_log("onSessionChannelUserEnter:  session=" + userList.sessionindex + ",id=" + userList.sessionmember[i].ipocid)
    }
}

poc.ptt.onSessionChannelUserExit = function (userList) {
    // {
    //     "sessionindex": 1,
    //         "sessionmember": [
    //             {
    //                 "ipocid": "32434"
    //             }
    //         ]
    // }
    for (var i = 0; i < userList.sessionmember.length; i++) {
        html_log("onSessionChannelUserExit:  session=" + userList.sessionindex + ",id=" + userList.sessionmember[i].ipocid)
    }
}

poc.ptt.onSessionDialogPresence = function (userList) {
    /*
    {
        "sessionindex": 1,
        "sessionmember": [
            {
                "ipocid": "32434",
                "userstate": 1
            },
            {
                "ipocid": "89589",
                "userstate": 1
            }
        ]
    }
    */
    for (var i = 0; i < userList.sessionmember.length; i++) {
        html_log("onSessionDialogPresence:  session=" + userList.sessionindex + ",id=" + userList.sessionmember[i].ipocid + ",state=" + userStateString(userList.sessionmember[i].userstate));
    }
}

poc.ptt.onLocation = function (loc) {
    html_log("onLocation:  loc=" + JSON.stringify(loc));
}

poc.ptt.onMessageRecv = function (msg) {
    html_log("onMessageRecv:  msg=" + JSON.stringify(msg));
}

poc.ptt.onMessageSent = function (result, msg) {
    html_log("onMessageSent:  result=" + result + ",msg=" + JSON.stringify(msg));
}

poc.ptt.onWarningFence = function (json) {
    html_log("onWarningFence:  json=" + JSON.stringify(json));
}

poc.ptt.onVideoCaptureGet = function (json) {
    html_log("onVideoCaptureGet:  json=" + JSON.stringify(json));
}

poc.ptt.onVideoCaptureLocal = function (json) {
    var imgTag = document.getElementById("videoSnapshotImg");
    imgTag.src = json.url;
    imgTag.style.display = "block"

    html_log("onVideoCaptureLocal:  json=" + JSON.stringify(json));
}

poc.ptt.onVideoStorePush = function (json) {
    html_log("onVideoStorePush:  json=" + JSON.stringify(json));
}

poc.ptt.onBroadcastPush = function (json) {
    html_log("onBroadcastPush:  json=" + JSON.stringify(json));
}

poc.ptt.onPushUserNameChanged = function (json) {
    html_log("onPushUserNameChanged:  json=" + JSON.stringify(json));
}

poc.ptt.onPushOrgNameChanged = function (json) {
    html_log("onPushOrgNameChanged:  json=" + JSON.stringify(json));
}

poc.ptt.onPushUserRoleChanged = function (json) {
    html_log("onPushUserRoleChanged:  json=" + JSON.stringify(json));
}

poc.ptt.onPushOrgStructChanged = function (json) {
    html_log("onPushOrgStructChanged:  json=" + JSON.stringify(json));
}

poc.ptt.onUserMessage = function (json) {
    html_log("onUserMessage:  json=" + JSON.stringify(json));
}

poc.ptt.onChatroomQueue = function (json) {
    html_log("onChatroomQueue:  json=" + JSON.stringify(json));
}

poc.ptt.onRecordPlayStart = function (result, url) {
    html_log("onRecordPlayStart:  result=" + result + ",url=" + url);
}

poc.ptt.onRecordPlayStop = function (url) {
    html_log("onRecordPlayStop:  url=" + url);
}

poc.ptt.onRecRecordStart = function (result) {
    html_log("onRecRecordStart:  result=" + result);
}

poc.ptt.onRecRecordStop = function (result, url, duration) {
    html_log("onRecRecordStop:  result=" + result + ",url=" + url + ",duration=" + duration);
}

poc.ptt.onSessionLocked = function (session) {
    html_log("onSessionLocked:  session=" + session);
}

poc.ptt.onSessionUnlocked = function (session) {
    html_log("onSessionUnlocked:  session=" + session);
}

poc.ptt.onSessionMediaBegin = function (info) {
    html_log("onSessionMediaBegin: " + JSON.stringify(info));
}

poc.ptt.onSessionMediaEnd = function (info) {
    html_log("onSessionMediaEnd: " + JSON.stringify(info));
}

//=============Video================
poc.video.onVideoShare = function (session, sid, ipocid, valid) {
    var r = false;
    if (valid) {
        r = confirm("onVideoShare: sid =" + sid + "ipocid=" + ipocid);
        if (r == true) {
            gVideoOwner = ipocid;
            poc.video.play(session, document.getElementById('videoPlay'));
            gVideoSession = session;
            document.getElementById("videoPauseBtn").innerHTML = videoButtonPause;
            document.getElementById("videoMuteBtn").innerHTML = videoButtonMute;
            document.getElementById('videoLable').innerHTML = "session=" + session + ",sid=" + sid + ",ipocid=" + ipocid;
        }
    } else {
        //清理多余标签
        cleanVideoDup();
    }

    html_log("onVideoShare:  session=" + session + " accept=" + r + " sid=" + sid + " ipocid=" + ipocid + " valid=" + valid);
}
//=============Video================

//==============DOM access=========
function currSID() {
    return document.getElementById('inputSID').value;
}

function currSession() {
    return document.getElementById('inputSession').value;
}

function currUsers() {
    return document.getElementById('inputUsers').value;
}

function checkPOCInit() {
    if (inited()) {
        document.getElementById('enterButton').disabled = false;
        return;
    }
    setTimeout(checkPOCInit, 100)
}

function userStateString(state) {
    // userstate: 状态(1: 前台在线, 2: 后台在线, 3: 离线)
    switch (state) {
        case 1:
            return "前台在线"
        case 2:
            return "后台在线"
        case 3:
            return "离线"
        default:
            return "未知状态"
    }
}

function inputType() {
    // <option value="sid">预设频道</option>
    // <option value="tempuser">临时呼叫(user)</option>
    // <option value="tempsid">临时呼叫(sid)</option>
    // <option value="broadcast ">广播呼叫</option>
    var t = document.getElementById('inputType').value;
    return t;
}

function uploadType() {
    //  <option value=4>png</option>
    //  <option value=5>audio</option>
    //  <option value=6>video</option>
    //  <option value=22>jpeg</option>
    var t = document.getElementById('uploadType').value;
    return t;
}

function inputSendIM() {
    // <option value="sid">预设频道</option>
    // <option value="tempuser">临时呼叫(user)</option>
    // <option value="tempsid">临时呼叫(sid)</option>
    // <option value="broadcast ">广播呼叫</option>
    var t = document.getElementById('sendIM').value;
    return t;
}

function cleanVideoDup() {
    //清理多余标签
    var tags = null;
    do {
        tags = document.getElementsByClassName('videoDup');
        console.log("remove " + tags.length)
        for (i = 0; i < tags.length; i++) {
            tags[i].removeAttribute('src')
            tags[i].removeAttribute('srcObject')
            console.log(tags[i])
            tags[i].parentNode.removeChild(tags[i]);
        }
    } while (tags != null && tags.length > 0);
}