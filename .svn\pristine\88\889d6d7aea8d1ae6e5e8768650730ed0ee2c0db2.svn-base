<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
  <title>剖切工具使用示例</title>

  <link rel="stylesheet" href="https://dev.arcgisonline.cn/jsapi/4.25/esri/themes/light/main.css" />
  <script src="./index.js" type="module"> </script>
  <style>
    html,
    body,
    #viewDiv {
      padding: 0;
      margin: 0;
      height: 100%;
      width: 100%;
    }

    .tools {
      position: absolute;
      top: 20px;
      left: 50%;
      width: 50%;
      height: 200px;
      display: flex;
    }

    .tools span {
      cursor: pointer;
      background-color: blue;
      width: 150px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 20px;
      color: white;
    }

    #sliceContainer {
      width: 200px;
      height: 200px;
      position: absolute;
      top: 10px;
      left: 10px;
      z-index: 1;
    }

    .description {
      position: absolute;
      right: 10px;
      top: 10px;
      background-color: white;
      border-radius: 5px;
      padding: 20px;
    }
  </style>


</head>

<body>
  <div id="viewDiv">
    <div class="tools">
      <span onclick="ArcGisUtils.createSiceWidget(view,'sliceContainer')">新建剖切</span>
    </div>
    <div class="description">
      createSiceWidget(view, container)
      <p>创建剖切微件</p>
      <p>@param { MapView | SceneView } view 对象 必填</p>
      <p>@param {HTMLElement} container dom元素 必填</p>
    </div>
    <div id="sliceContainer"></div>
  </div>
  </div>
</body>

</html>