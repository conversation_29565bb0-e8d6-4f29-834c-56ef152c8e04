
export function setCam(view, options = { heading: 15, tilt: 60, fov: 90 }, cb) {
  const { heading, tilt, fov } = options;

  const camera = view.camera.clone();
  heading && (camera.heading = heading);
  tilt && (camera.tilt = tilt);
  fov && (camera.fov = fov);

  view.camera = camera;
}

export function updateCam(
  view,
  inputClass = { heading: ".c-heading", tilt: ".c-tilt", fov: ".c-fov" }
) {
  if (window.__cameraHandle) {
    return;
  }

  window.__cameraHandle = view.watch("camera", (c) => {
    try {
      document.querySelector(inputClass.heading).value = c.heading;
      document.querySelector(inputClass.fov).value = c.fov;
      document.querySelector(inputClass.tilt).value = c.tilt;
    } catch (error) {
      console.error(error);
    }
  });
}
