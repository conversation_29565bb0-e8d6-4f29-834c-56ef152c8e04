<!--
 * @Author: xk_zhang
 * @Date: 2022-09-19 00:32:53
 * @E-mail: <EMAIL>
 * @LastEditors: xk_zhang
 * @LastEditTime: 2022-09-19 02:11:46
 * @Desc:
-->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>执法记录测试</title>
  <script src="/jquery/jquery-3.6.1.min.js"></script>
  <script src="/static/js/layui/layui.js"></script>
  <script src="axios.min.js"></script>
  <script src="md5.js"></script>
  <script src="zfjly.js"></script>
  <script src="../zfjly_lxs/engine/data/dataAction.js"></script>
  <style>
      .rwgz-tc {
          position: relative;
          width: 1500px;
          height: 1000px;
          background-image: url(/static/images/zhdd/bg_panel.png);
          background-size: 100% 100%;
          /* border: 2px solid #3a9ff8;
          background: rgba(3, 24, 39, 0.8); */
          border-radius: 57px;
      }
      .rw-title {
          position: absolute;
          top: 50px;
          z-index: 888;
          /* background: linear-gradient(#00aae2, #064b65); */
          border-radius: 57px 57px 0 0;
          width: 100%;
          height: 60px;
          line-height: 60px;
          padding: 1% 3%;
          box-sizing: border-box;
      }
      .close {
          background: url("/static/images/zhdd/close.png") no-repeat;
          width: 34px;
          height: 34px;
          cursor: pointer;
          float: right;
      }
  </style>
</head>
<body>
<div class="rwgz-tc">
  <div class="rw-title flex-between">
    <div class="fs-44 font-syBold" id="rwTitle"></div>
    <div class="close" id="rwClose1" style="z-index: 9999"></div>
  </div>
  <video
    id="video"
    class="chorme disappear"
    controls="controls"
    autoplay="autoplay"
    muted="false"
    name="media"
    style="
          width: 94%;
          height: 90%;
          position: absolute;
          top: 75px;
          left: 50px;
        "
    ref="video"
  ></video>
  <div
    style="
          z-index: 1000;
          position: absolute;
          top: 40px;
          left: 20px;
          display: none;
        "
  >
    <button
      style="width: 70px; height: 30px; margin-bottom: 10px; display: none"
      onclick="openZFJLY()"
    >
      登录
    </button>
    <button
      style="width: 70px; height: 30px; margin-bottom: 10px; display: none"
      onclick="getInfo()"
    >
      打开视频
    </button>
    <button
      style="width: 70px; height: 30px; margin-bottom: 10px"
      onclick="startAudio()"
    >
      呼叫
    </button>
  </div>
</div>
</body>
<script type="module">
  import {
    Wsplayer,
    HZRecorder_pcm_push,
    HZRecorder_pcm,
  } from "./JY-chromePlayer.min.js";
  var videoDom = document.getElementById("video");
  var player,
    isAudio,
    num = 0,
    recorders,
    recordersPlatform;
  function getplatform() {
    return new Promise((reslove, reject) => {
      var that = this;
      navigator.getUserMedia =
        navigator.getUserMedia || navigator.webkitGetUserMedia;
      if (navigator.getUserMedia) {
        navigator.getUserMedia(
          { audio: true }, //只启用音频
          function (stream) {
            recordersPlatform = new HZRecorder_pcm_push(stream, {});
            reslove();
          },
          function (error) {
            switch (error.code || error.name) {
              case "PERMISSION_DENIED":
              case "PermissionDeniedError":
                throwError("用户拒绝提供信息。");
                break;
              case "NOT_SUPPORTED_ERROR":
              case "NotSupportedError":
                throwError("浏览器不支持硬件设备。");
                break;
              case "MANDATORY_UNSATISFIED_ERROR":
              case "MandatoryUnsatisfiedError":
                throwError("无法发现指定的硬件设备。");
                break;
              default:
                throwError("无法打开麦克风。" + error);
                break;
            }
          }
        );
      } else {
        throwError("当前浏览器不支持录音功能。");
        return;
      }
    });
  }
  window.openZFJLY = function () {
    zfjly.loginZFJLY();
  };
  window.getInfo = function (vdDept = ["T0C2675"]) {
    setTimeout(() => {
      zfjly.getDeviceInfo();
      var send = {
        hostbody_arr: vdDept || ["T0C2675"],
      };
      zfjly.startLive(send);
    },5000)
  };
  window.startAudio = function (vdDept = ["T0C2675"]) {
    setTimeout(() => {
      var imei;
      zfjly.unitEquipTree("1001", "bh", "dname", false).then((res) => {
        res.data.data.forEach((item) => {
          if (item.hostbody === zfjly.form.hostbody) {
            imei = item.imei;
          }
        });
        if (isAudio) {
          let send = {
            imei: imei,
            type: "startmute",
          };
          zfjly.send_cmd(send).then((res) => {
            isAudio = false;
          });
        } else {
          if (num === 0) {
            getplatform().then(() => {
              // 只允许拉一路音频流
              navigator.getUserMedia =
                navigator.getUserMedia || navigator.webkitGetUserMedia;
              if (!navigator.getUserMedia) {
                layui.use("layer", function () {
                  var layer = layui.layer;
                  layer.msg(
                    '<span style="font-size: 30px;">浏览器不支持音频输入</span>'
                  );
                });
                // console.log("浏览器不支持音频输入");
              } else {
                navigator.getUserMedia(
                  {
                    audio: true,
                  },
                  function (mediaStream) {
                    // 初始化
                    recorders = new HZRecorder_pcm(mediaStream, {});
                    let send = {
                      // hostbody_arr: [zfjly.form.hostbody],
                      hostbody_arr: vdDept || ["T0C2675"],
                    };
                    zfjly.startLiveAudio(send).then((res) => {
                      if (res.data.code === 200) {
                        var ws =
                          "ws://" +
                          res.data.data[0].wsip +
                          ":" +
                          res.data.data[0].wsport +
                          "/RTSP/AAC";
                        var wsBroadcast =
                          "ws://" +
                          res.data.data[0].wsip +
                          ":" +
                          res.data.data[0].wsport +
                          "/RTSP/AAC/Broadcast";
                        var rtspAudio = res.data.data[0].rtsp;
                        var logincode = res.data.data[0].logincode;
                        isAudio = true;
                        num++;
                        recordersPlatform
                          .openWebSocket(wsBroadcast, logincode)
                          .then(() => {});
                      } else {
                        layui.use("layer", function () {
                          var layer = layui.layer;
                          layer.msg(
                            '<span style="font-size: 30px;">设备不在线</span>'
                          );
                        });
                        // console.log(res.msg);
                      }
                    });
                  },
                  function (error) {
                    console.log(error);
                    switch (error.message || error.name) {
                      case "PERMISSION_DENIED":
                      case "PermissionDeniedError":
                        console.info("用户拒绝提供信息。");
                        break;
                      case "NOT_SUPPORTED_ERROR":
                      case "NotSupportedError":
                        console.info("浏览器不支持硬件设备。");
                        break;
                      case "MANDATORY_UNSATISFIED_ERROR":
                      case "MandatoryUnsatisfiedError":
                        console.info("无法发现指定的硬件设备。");
                        break;
                      default:
                        console.info(
                          "无法打开麦克风。异常信息:" +
                          (error.code || error.name)
                        );
                        break;
                    }
                  }
                );
              }
            });
          } else {
            let send = {
              imei: imei,
              type: "stopmute",
            };
            zfjly.send_cmd(send).then((res) => {
              isAudio = true;
            });
          }
        }
      });
    },5000)
  };
  document.getElementById("rwClose1").onclick = function () {
    console.log("关闭视频");
    window.parent.lay.closeIframeByNames(["videoTest"]);
    // let obj={
    //   type:"closeIframe",
    //   name:"videoTest"
    // };
    // window.parent.postMessage(JSON.stringify(obj),"*");
  };

  window.addEventListener(
    "message",
    function (event) {
      //子获取父消息
      let newData;
      if (typeof event.data == "object") {
        newData = event.data;
      } else {
        newData = JSON.parse(event.data.argument);
      }
      if (newData.name === "openVideoTest") {
        window.openZFJLY();
        setTimeout(() => {
          window.getInfo(newData.videoCode);
        }, 2000);
      } else if (newData.name === "ddhjVideoTest") {
        window.openZFJLY();
        setTimeout(() => {
          window.getInfo(newData.videoCode); // 打开视频
          window.startAudio(newData.videoCode); // 打开音频
        }, 2000);
      }
    },
    false
  );

  window.pullFlow_vms3 = function (ws, rtsp) {
    console.log("pullFlow_vms3",ws, rtsp, videoDom, "isGB");
    this.player = new Wsplayer(ws, rtsp, videoDom, "isGB");
    this.player.openws().then((obj) => {
      console.log(obj);
      if (obj.type == true) {
        //拉流拉起来了
        videoDom.setAttribute("class", "chorme");
      } else if (obj.type == false) {
        //拉流异常，未拉起来
        layui.use('layer', function(){
          var layer = layui.layer;
          layer.msg('<span style="font-size: 30px;">拉流失败</span>');
        });
        this.closeEnd();
      } else if (obj.type == undefined) {
        layui.use('layer', function(){
          var layer = layui.layer;
          layer.msg('<span style="font-size: 30px;">拉流失败</span>');
        });
        this.closeEnd();
      }
    });
  };

  window.voice_pull_vms3 = function (ws, rtsp) {
    console.log(this.recorders);
    this.recorders.openWebSocket(ws, rtsp);
  };

  window.preset3 = function (Info) {
    for (let i = 0; i < 256; i++) {
      var data = {
        PresetID: parseInt(i),
        PresetName: "Preset" + parseInt(i),
        type: false,
      };
      this.count[i] = data;
    }
    var hasCount = Info;
    this.count.forEach((item) => {
      hasCount.forEach((el) => {
        if (item.PresetID == el.PresetID) {
          item.type = true;
        }
      });
    });
    this.openPresetBit = true;
    this.flag = true;
  };

  window.closeEnd = function () {
    console.log("我是关闭", this.isAudio);
    if (!this.isAudio) {
      //暂停音频
      var send = {
        hostbody_arr: [this.form.hostbody],
        sn_arr: [this.sn],
      };
      this.stopLive(send).then((res) => {
        if (res.code == 200) {
          console.log(res);
          this.player.stop();
          this.videoDom.setAttribute("class", "chorme disappear");
        }
      });
    } else {
      var send1 = {
        hostbody_arr: [this.form.hostbody],
        wsChannelId_arr: [this.wsChannelId],
      };
      this.stopAudio(send1).then((res) => {
        console.log(res);
        this.isAudio = false;
        this.recorders.closeWebsocket(); //暂停音频
        //暂停音频
        var sent = {
          hostbody_arr: [this.form.hostbody],
          sn_arr: [this.sn],
        };
        this.stopLive(sent).then((res) => {
          if (res.code == 200) {
            console.log(res);
            this.player.stop();
            this.videoDom.setAttribute("class", "chorme disappear");
          }
        });
        // }
      });
    }
  };
  // window.openZFJLY();
  // setTimeout(()=>{
  //   window.getInfo(["T0C1067"])
  // },2000)
</script>
</html>
<style></style>
