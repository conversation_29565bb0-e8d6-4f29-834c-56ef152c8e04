<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  <title>金华市行政执法指挥中心</title>
  <link rel="shortcut icon" href="#" />
  <link rel="stylesheet" href="/static/css/common.css" />
  <link rel="stylesheet" href="/static/css/login3840.css" />
  <style>
    /*@media only screen and (max-width: 3840px) {*/
    /*  .main {*/
    /*    !*transform: scale(0.5) translate(-50%, -50%);*!*/
    /*  }*/
    /*}*/
    @media only screen and (max-width: 1920px) {
      .main {
        transform: scale(0.5) translate(-50%, -50%);
      }
    }

    .el-message__content {
      font-size: 28px !important;
    }
  </style>
</head>

<body>
  <div class="main" id="main">
<!--    <iframe src="UAV.html" style="width: 3840px;height: 2160px;"></iframe>-->
    <div class="bg-login">
      <!-- 二维码 -->
      <div class="tab_click" style="display: none"></div>
      <div class="loginBox">
        <div class="login-title">金华市行政执法指挥中心</div>
        <div class="icon-shine"></div>
        <div class="login-content">
          <div class="login-row color-0ff">
            <span class="icon-user lf"></span>
            <input id="username" class="ipt-un fs-40 lf input"name="username" placeholder="请输入您的账号"
                   type="text" v-model="loginUser.username"
                   readonly onfocus="this.removeAttribute('readonly');" onblur="this.setAttribute('readonly',true);"/>
          </div>
          <div class="login-row color-0ff">
            <span class="icon-pass lf"></span>
            <input id="password" class="ipt-pw fs-40 lf input" name="password" placeholder="请输入您的密码"
                   :type="[flag?'text':'password']" autocomplete="off" v-model="loginUser.password" data="password">
              <i :class="[flag?'el-icon-minus':'el-icon-view']" style="margin-top:8px;font-size:36px;color: #019cd8" @click="flag=!flag"></i>
            </input>
          </div>
          <div id="login" class="btn fs-50" @click="Tologin(3)">点击登录</div>
        </div>
        <div class="icon-shine icon-shine1"></div>
      </div>
      <!--        <i class="login-text text1 font-fashion">指挥调度</i>-->
      <!--        <i class="login-text text2 font-fashion">三色预警</i>-->
      <!--        <i class="login-text text3 font-fashion">执法态势</i>-->
      <!--        <i class="login-text text4 font-fashion">考核评价</i>-->
    </div>
  </div>
</body>
<!-- <script src="/static/js/jslib/jquery-3.6.1.min.js"></script> -->
<!-- <script src="./static/js/jslib/axios.min.js"></script>
  <script src="./static/js/jslib/http.interceptor.js"></script> -->
<!-- <script src="/static/js/jslib/common.new.js"></script> -->
<!-- <script src="/static/js/home_services/login.js"></script> -->
<!--<script type="text/javascript" src='js/hyvideo/hysdk.js'></script>
  <script src="js/jslib/common.js"></script>-->
<script src="/static/js/jslib/md5.js"></script>
<script src="/static/citybrain/tcgl/lib/components/core.js"></script>
<script src="/static/citybrain/tcgl/lib/components/enc-base64.js"></script>
<script src="/Vue/vue.js"></script>
<script src="/elementui/js/elementui.js"></script>
<link rel="stylesheet" href="/elementui/css/elementui.css" />
<script src="/jquery/jquery-3.6.1.min.js"></script>
<script rel="preload" src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script src="/static/js/jslib/md5.js"></script>
<script src="/static/js/jslib/layer.js"></script>
<script src="/static/js/jslib/cookie.js"></script>
<script src="/static/js/jslib/login.js"></script>

</html>
