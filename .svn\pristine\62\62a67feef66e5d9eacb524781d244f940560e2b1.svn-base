<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>日照分析</title>

    <link
      rel="stylesheet"
      href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/base16/dracula.min.css"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/go.min.js"></script>
    <script src="./index.js" type="module"></script>
    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        left: 50%;
        width: 50%;
        height: 200px;
        display: flex;
      }

      .tools span {
        cursor: pointer;
        background-color: blue;
        width: 150px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        color: white;
      }

      #daylightWidget {
        position: absolute;
        bottom: 30px;
        right: 10px;
        z-index: 1;
      }

      .description {
        position: absolute;
        right: 10px;
        top: 10px;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv"></div>
    <div class="tools">
      <span onclick="init()">初始化</span>
      <span onclick="remove()">移除</span>
    </div>
    <div class="description">
      使用：
      <pre><code class="language-javascript">
        /**
        * 日照分析微件
        * @param {SceneView} view
        * @param {string} container 容器ID
        * @returns
        */
        createDaylightWidget(view, container)
      </code></pre>
    </div>
    <div id="daylightWidget"></div>
  </body>
  <script>
    let daylightWidget;
    function init() {
      ArcGisUtils.addWhiteModalLayer(view);
      daylightWidget = ArcGisUtils.createDaylightWidget(view, "daylightWidget");
    }
    function remove() {
      daylightWidget.destroy();
    }
  </script>
  <script>
    hljs.highlightAll();
  </script>
</html>
