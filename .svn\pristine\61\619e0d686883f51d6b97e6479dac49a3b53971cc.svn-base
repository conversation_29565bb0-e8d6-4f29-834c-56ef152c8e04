<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>案件回访底部</title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/css/viewCss/index.css" />
  <script src="/Vue/vue.js"></script>
  <script src="/jquery/jquery-3.6.1.min.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/Vue/vue-count-to.min.js"></script>
  <link rel="stylesheet" href="/elementui/css/index.css" />
  <script src="/elementui/js/index.js"></script>
  <script src="/echarts/5.4.1/echarts.min.js"></script>
  <script src="/static/js/jslib/moment.js"></script>
  <style>
    [v-cloak] {
      display: none;
    }

    #bottom {
      width: 1760px;
      height: 525px;
      padding: 20px;
      box-sizing: border-box;
      background: url("/static/images/index/bottom-bg.png") no-repeat;
      background-size: 100% 100%;
    }

    .el-range-editor .el-range-input {
      background: transparent;
    }

    .el-picker-panel__icon-btn {
      color: #fff !important;
    }

    .el-input__inner {
      background-color: rgba(19, 44, 78, 0.8) !important;
      border: 1px solid #359cf8 !important;
    }

    .el-date-editor .el-range-input,
    .el-date-editor .el-range-separator {
      font-size: 25px !important;
    }

    .el-date-editor .el-range__icon {
      font-size: 22px !important;
    }

    .el-date-editor .el-range-input,
    .el-month-table td .cell {
      color: #fff !important;
    }

    .el-picker-panel {
      border: 1px solid #00b7f3;
      box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%) !important;
      background: #0f233e !important;
    }

    .el-month-table,
    .el-year-table,
    .el-date-range-picker__header div {
      font-size: 20px !important;
    }

    .el-month-table td.in-range div,
    .el-month-table td.in-range div:hover {
      background-color: #264068 !important;
    }

    .el-date-editor .el-range__close-icon {
      font-size: 20px !important;
    }

    .el-date-editor .el-range-separator {
      color: #e6e9ee !important;
    }

    .chart {
      width: 100%;
      height: 465px;
    }
  </style>
</head>

<body>
  <div id="bottom" v-cloak>
    <div class="hearder_h1"><span>回访排名</span></div>
    <div style="position: absolute; top: 26px; left: 1430px; z-index: 2" v-show="year != '2023'">
      <el-date-picker v-model="value" type="monthrange" @change="(range) => initApi(range,city,year)"
        range-separator="-" start-placeholder="开始月份" value-format="yyyy-MM" end-placeholder="结束月份">
      </el-date-picker>
    </div>
    <div class="chart" id="ajhfBottomChart"></div>
  </div>
  <script>
    window.parent.eventbus &&
      window.parent.eventbus.on("yearChange", (year) => {
        vm.year = year
        if (year && year != '') {
          if (year == new Date().getFullYear()) {
            vm.value = [new Date().getFullYear() + "-01", moment(new Date()).format("YYYY-MM")]
          } else {
            vm.value = [year + "-01", moment(`${year}-01-01`).endOf('year').format('YYYY-MM-DD')]
          }
          vm.initApi(vm.value, localStorage.getItem("city"),year);
        }
      });
    let vm = new Vue({
      el: "#bottom",
      data: {
        year: "",
        city: "",
        value: [
          new Date().getFullYear() + "-01",
          moment(new Date()).format("YYYY-MM"),
        ],
        chartsData: []
      },
      mounted() {
        this.year = localStorage.getItem("year");
        this.city = localStorage.getItem("city");
        if (this.year && this.year != '') {
          if (this.year == new Date().getFullYear()) {
            this.value = [new Date().getFullYear() + "-01", moment(new Date()).format("YYYY-MM")]
          } else {
            this.value = [year + "-01", moment(`${year}-01-01`).endOf('year').format('YYYY-MM-DD')]
          }
          this.initApi(this.value, this.city,this.year);
        }
        window.parent.eventbus && window.parent.eventbus.on("cityChange", (city) => {
          let filtName =
            city == "金义新区" ?
            "金东区" :
            city == "金华开发区" ?
            "开发区" :
            city;
          this.initApi(this.value, filtName,this.year);
        });
      },
      methods: {
        initApi(range, city,year) {
            $api2Get("/ajhf/zftsZfrw/hfpm", {
              xsq: city,
              startTime: range[0],
              endTime: range[1],
            }).then(res => {
              if (res.data.code == 200) {
                this.chartsData = res.data.data
                this.initChart();
              }
            })
        },
        initChart() {
          let myChart = echarts.init(document.getElementById("ajhfBottomChart"));
          let option = {
            tooltip: {
              trigger: "axis",
              borderWidth: 0,
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
              },
              formatter: '{b}: <br/> {c}%',
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '28',
              },
            },
            legend: {
              orient: "horizontal",
              // icon: "circle",
              itemGap: 45,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 28,
              },
            },
            grid: {
              left: "2%",
              right: "2%",
              top: "10%",
              bottom: "12%",
              containLabel: true,
            },
            xAxis: [{
              type: "category",
              data: this.chartsData.map(item => item.xsq),
              axisLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)", // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 28,
                },
              },
            }, ],
            yAxis: [{
              name: "",
              type: "value",
              nameTextStyle: {
                fontSize: 24,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            }],
            series: [{
              name: "",
              type: "bar",
              barWidth: "20%",
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                      offset: 0,
                      color: "#28F7E1",
                    },
                    {
                      offset: 1,
                      color: "#1A80FF",
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              label: {
                show: true,
                position: 'top',
                color: "#fff",
                formatter: function (params) {
                  return params.value + "%";
                },
                fontSize: 32,
                textStyle: {
                  color: '#FFFFFF',
                  fontWeight: 'bold',
                  fontFamily: 'Source Han Sans CN'
                }
              },
              data: this.chartsData.map(item => item.pm)
            }],
          };
          myChart.setOption(option)
          myChart.getZr().on('mousemove', param => {
            myChart.getZr().setCursorStyle('default')
          })
        }
      }
    });
  </script>
</body>

</html>
