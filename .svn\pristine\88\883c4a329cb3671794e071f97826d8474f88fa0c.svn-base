<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
  <title>箭头线</title>

  <link rel="stylesheet" href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css" />
  <link
  rel="stylesheet"
  href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/base16/dracula.min.css"
/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/go.min.js"></script>
  <script src="./index.js" type="module"></script>
  <style>
    html,
    body,
    #viewDiv {
      padding: 0;
      margin: 0;
      height: 100%;
      width: 100%;
    }

    .tools {
      position: absolute;
      top: 20px;
      left: 50%;
      width: 50%;
      height: 200px;
      display: flex;
    }

    .tools span {
      cursor: pointer;
      background-color: blue;
      width: 150px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 20px;
      color: white;
    }

    #lineOfSight {
      width: 200px;
      height: 200px;
      position: absolute;
      bottom: 10px;
      right: 10px;
      z-index: 1;
    }

    .description {
      position: absolute;
      right: 10px;
      top: 10px;
      background-color: white;
      border-radius: 5px;
      padding: 20px;
    }
  </style>
</head>

<body>
  <div id="viewDiv"></div>
  <div class="tools">
    <span onclick="add()">添加</span>
    <span onclick="remove()">移除</span>
  </div>
  <div class="description">
    添加：
    <pre><code class="language-javascript">
        layer = ArcGisUtils.addArrowLineLayer({
          view,
          data: [
            // 具有相同属性的多条线
            {
              paths: [
                // 一条线
                [
                  [119.6242823104002, 29.05841639892346, 0],
                  [119.6347687244354, 29.060895800987417, 0],
                  [119.64139923789716, 29.051970888875243, 0],
                  [119.63234038208938, 29.046869990233358, 30],
                  [119.62837627043022, 29.047905322490834, 100],
                ],
              ],
              attributes: { name: "测试" },
            },
          ],
          width: 5,
          color: "#00ff00",
        });
      </code></pre>
    移除：
    <pre><code class="language-javascript">
        view.map.remove(layer);
      </code></pre>
  </div>
  <div id="lineOfSight"></div>
</body>
<script>
  let layer;
  const paths = [
    // 一条线
    [
      [119.6242823104002, 29.05841639892346, 0],
      [119.6347687244354, 29.060895800987417, 0],
      [119.64139923789716, 29.051970888875243, 0],
      [119.63234038208938, 29.046869990233358, 30],
      [119.62837627043022, 29.047905322490834, 100],
    ],
  ]
  async function add() {
    layer = await ArcGisUtils.addArrowLineLayer({
      view,
      data: [
        // 具有相同属性的多条线
        {
          paths: await ArcGisUtils.converPathToPoints(paths),
          attributes: { name: "测试" },
        },

      ],
      width: 5,
      color: "#00ff00",
    });
  }
  function remove() {
    window.view.map.remove(layer);
  }
</script>
<script>
  hljs.highlightAll();
</script>
</html>