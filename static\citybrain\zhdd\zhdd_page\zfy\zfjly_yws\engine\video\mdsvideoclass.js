// We make use of this 'server' variable to provide the address of the
// REST Janus API. By default, in this example we assume that <PERSON><PERSON> is
// co-located with the web server hosting the HTML pages but listening
// on a different port (8088, the default for HTTP in <PERSON><PERSON>), which is
// why we make use of the 'window.location.hostname' base address. Since
// <PERSON><PERSON> can also do HTTPS, and considering we don't really want to make
// use of HTTP for <PERSON><PERSON> if your demos are served on HTTPS, we also rely
// on the 'window.location.protocol' prefix to build the variable, in
// particular to also change the port used to contact <PERSON><PERSON> (8088 for
// HTTP and 8089 for HTTPS, if enabled).
// In case you place <PERSON><PERSON> behind an Apache frontend (as we did on the
// online demos at http://janus.conf.meetecho.com) you can just use a
// relative path for the variable, e.g.:
//
// 		var server = "/janus";
//
// which will take care of this on its own.
//
//
// If you want to use the WebSockets frontend to <PERSON><PERSON>, instead, you'll
// have to pass a different kind of address, e.g.:
//
// 		var server = "ws://" + window.location.hostname + ":8188";
//
// Of course this assumes that support for WebSockets has been built in
// when compiling the server. WebSockets support has not been tested
// as much as the REST API, so handle with care!
//
//
// If you have multiple options available, and want to let the library
// autodetect the best way to contact your server (or pool of servers),
// you can also pass an array of servers, e.g., to provide alternative
// means of access (e.g., try WebSockets first and, if that fails, fall
// back to plain HTTP) or just have failover servers:
//
//		var server = [
//			"ws://" + window.location.hostname + ":8188",
//			"/janus"
//		];
//
// This will tell the library to try connecting to each of the servers
// in the presented order. The first working server will be used for
// the whole session.
//
class MdsVideoClass {
    constructor() {
        this.server = "ws://************:8188/janus";
        //this.server = "wss://************:8989/janus";
        this.janus = null;

        this.max_room_num = 1000;

        this.publishVideo = false;

        // for local
        this.myusername = null;
        this.mystream = null;
        this.mysfutest = null;

        // for remote
        this.rooms = [];
        this.feeds = [[]];
        this.remotestreams = [[]];
        this.remotefeeds = [[]];

        this.roomPluginHandle = [];
        this.mapIdToDisplay = [[]];

        this.participants = [];
    }

    // remove plugin handle
    removePluginHandle(idx) {
        if (idx == -1)
            return

        this.roomPluginHandle[idx] = null
    }

    // find the matched plugin handle with the specified room
    // avail is used to indicate whether to find one unused element
    findMatchPluginHandle(room, avail) {
        //Janus.log(this.roomPluginHandle)
        //Janus.log(room)
        for (var i = 0; i < this.max_room_num; i++) {
            if (this.roomPluginHandle[i] == null || this.roomPluginHandle[i] == undefined)
                continue

            if (this.roomPluginHandle[i]["room"] == room) {
                if (avail == true) {
                    var info = {};
                    info["room"] = room
                    this.roomPluginHandle[i] = info
                    this.feeds[i] = [];
                    this.remotestreams[i] = [];
                    this.remotefeeds[i] = [];
                    this.mapIdToDisplay = [];
                }
                return i
            }
        }

        if (!avail) {
            return -1
        }

        for (var i = 0; i < this.max_room_num; i++) {
            if (this.roomPluginHandle[i] == null || this.roomPluginHandle[i] == undefined) {
                var info = {};
                info["room"] = room
                this.roomPluginHandle[i] = info
                this.feeds[i] = [];
                this.remotestreams[i] = [];
                this.remotefeeds[i] = [];
                this.mapIdToDisplay = [];
                return i
            }
        }

        return -1
    }

    // initialization: attach Janus VideoRoom plugin
    init(url, eventRetry) {
        // Initialize the library (all console debuggers enabled)
        console.log(url);
        var _topThis = this;
        return new Promise(function (topResolve, topReject) {
            let promise_obj = new Promise((resolve, reject) => {
                Janus.init({
                    debug: "all", callback: function () {
                        // Make sure the browser supports WebRTC
                        if (!Janus.isWebrtcSupported()) {
                            Janus.log("No WebRTC support... ");
                            reject(new Error("No WebRTC support"));
                        } else {
                            resolve()
                        }
                    }
                });
            })

            promise_obj.then(() => {
                // Create session
                _topThis.janus = new Janus(
                    {
                        server: url,
                        success: function () {
                            topResolve(url);
                        },
                        connFailed: function (err) {
                            console.log("Janus connection failed!");
                            topReject(err)
                            eventRetry();
                        },
                        connClosed: function() {
                            console.log("Janus connection closed!");
                            eventRetry();
                        }
                    })
            }).catch(function (err) {
                topReject(err)
            });
        });
    };

    // get all video rooms information
    listNonEmptyRooms() {
        var listroom = { "request": "list" };
        var rooms = []
        this.mysfutest.send({
            "message": listroom,
            success: function (data) {
                var idx = 0;
                for (var i = 0; i < data.list.length; i++) {
                    if (data.list[i]["num_participants"] > 0) {
                        rooms[idx] = data.list[i]
                        idx++
                    }
                }
                Janus.log(rooms)

                return rooms
            }
        });
    }

    listAllRooms() {
        var listroom = { "request": "list" };
        this.rooms = []

        let promise_obj = new Promise((resolve, reject) => {
            this.mysfutest.send({
                "message": listroom,
                success: function (data) {
                    resolve(data)
                }
            });
        })

        promise_obj.then((data) => {
            this.rooms = data.list
            Janus.log(this.rooms)
        })
    }

    listAllParticipants(room) {
        var listroom = { "request": "listparticipants", "room": room };
        this.participants = []
        this.mysfutest.send({
            "message": listroom,
            success: function (data) {
                Janus.log(data.participants)
                this.participants = data.participants
            }
        });
    }

    listAllStreams() {
        var result = []
        var idx = 0
        for (var i = 0; i < this.roomPluginHandle.length; i++) {
            var room = this.roomPluginHandle[i]["room"]
            //Janus.log(feeds[i])
            for (let [key, value] of Object.entries(this.feeds[i])) {
                result[idx] = { "room": room, "display": key }
                idx++;
            }
        }

        return result
    }

    removeRoom(room) {
        var removeroom = { "request": "destroy", "room": room, "permanent": true }
        this.mysfutest.send({
            "message": removeroom,
            success: function (data) {
                Janus.log(data)
            }
        })
    }

    // publish audio and video as one publisher
    publishOwnFeed(pluginHandle, useAudio, useVideo) {
        // Publish our stream
        pluginHandle.createOffer(
            {
                // Add data:true here if you want to publish datachannels as well
                // media: { audioRecv: false, videoRecv: false, audioSend: useAudio, videoSend: true },	// Publishers are sendonly
                media: { audioRecv: false, videoRecv: false, audioSend: useAudio, videoSend: useVideo },	// Publishers are sendonly
                // If you want to test simulcasting (Chrome and Firefox only), then
                // pass a ?simulcast=true when opening this demo page: it will turn
                // the following 'simulcast' property to pass to janus.js to true
                success: function (jsep) {
                    Janus.log("Got publisher SDP!");
                    Janus.log(jsep);
                    // var publish = { "request": "configure", "audio": useAudio, "video": true };
                    var publish = { "request": "configure", "audio": useAudio, "video": useVideo, "audiocodec": "opus" };
                    // You can force a specific codec to use when publishing by using the
                    // audiocodec and videocodec properties, for instance:
                    // 		publish["audiocodec"] = "opus"
                    // to force Opus as the audio codec to use, or:
                    // 		publish["videocodec"] = "vp9"
                    // to force VP9 as the videocodec to use. In both case, though, forcing
                    // a codec will only work if: (1) the codec is actually in the SDP (and
                    // so the browser supports it), and (2) the codec is in the list of
                    // allowed codecs in a room. With respect to the point (2) above,
                    // refer to the text in janus.plugin.videoroom.cfg for more details
                    pluginHandle.send({ "message": publish, "jsep": jsep });
                },
                error: function (error) {
                    Janus.error("WebRTC error:", error);
                    if (useAudio) {
                        publishOwnFeed(pluginHandle, false, this.publishVideo);
                    } else {
                        Janus.error("WebRTC error... " + JSON.stringify(error));
                    }
                }
            });
    }

    // publish local stream
    publish(room, username) {
        var private_id = null

        let attach_promise = new Promise((resolve, reject) => {

            let selfobj = this

            this.janus.attach(
                {
                    plugin: "janus.plugin.videoroom",
                    opaqueId: "mdsvideo-" + Janus.randomString(12),
                    success: function (pluginHandle) {
                        Janus.log("Plugin attached! (" + pluginHandle.getPlugin() + ", id=" + pluginHandle.getId() + ")");
                        Janus.log("  -- This is a publisher/manager");
                        resolve(pluginHandle)
                    },
                    error: function (error) {
                        Janus.error("  -- Error attaching plugin...", error);
                    },
                    consentDialog: function (on) {
                        Janus.log("Consent dialog should be " + (on ? "on" : "off") + " now");
                    },
                    mediaState: function (medium, on) {
                        Janus.log("Janus " + (on ? "started" : "stopped") + " receiving our " + medium);
                    },
                    webrtcState: function (on) {
                        Janus.log("Janus says our WebRTC PeerConnection is " + (on ? "up" : "down") + " now");
                        if (!on)
                            return false;
                    },
                    onmessage: function (msg, jsep) {
                        Janus.log(" ::: Got a message (publisher) :::");
                        Janus.log(msg);
                        var event = msg["videoroom"];
                        Janus.log("Event: " + event);
                        if (event != undefined && event != null) {
                            if (event === "joined") {
                                // Publisher/manager created, negotiate WebRTC and attach to existing feeds, if any
                                Janus.log("Successfully joined room " + msg["room"] + " with ID " + msg["id"]);

                                private_id = msg["private_id"]

                                selfobj.publishOwnFeed(selfobj.mysfutest, true, true);

                                // Any new feed to attach to?
                                if (msg["publishers"] !== undefined && msg["publishers"] !== null) {
                                    var list = msg["publishers"];
                                    Janus.log("Got a list of available publishers/feeds:");
                                    Janus.log(list);
                                    for (var f = 0, len = list.length; f < len; f++) {
                                        var id = list[f]["id"];
                                        var display = list[f]["display"];
                                        var audio = list[f]["audio_codec"];
                                        var video = list[f]["video_codec"];
                                        Janus.log("  >> [" + id + "] " + display + " (audio: " + audio + ", video: " + video + ")");
                                    }
                                }
                            } else if (event === "destroyed") {
                                // The room has been destroyed
                                Janus.warn("The room has been destroyed!");
                            } else if (event === "event") {
                                // Any new feed to attach to?
                                if (msg["publishers"] !== undefined && msg["publishers"] !== null) {
                                    var list = msg["publishers"];
                                    Janus.log("Got a list of available publishers/feeds:");
                                    Janus.log(list);

                                    for (var f = 0, len = list.length; f < len; f++) {
                                        var id = list[f]["id"];
                                        var display = list[f]["display"];
                                        var audio = list[f]["audio_codec"];
                                        var video = list[f]["video_codec"];
                                        Janus.log("  >> [" + id + "] " + display + " (audio: " + audio + ", video: " + video + ")");
                                    }
                                } else if (msg["leaving"] !== undefined && msg["leaving"] !== null) {
                                    // One of the publishers has gone away?
                                    var leaving = msg["leaving"];
                                    Janus.log("Publisher left: " + leaving);
                                } else if (msg["unpublished"] !== undefined && msg["unpublished"] !== null) {
                                    // One of the publishers has unpublished?
                                    var unpublished = msg["unpublished"];
                                    Janus.log("Publisher left: " + unpublished);

                                    if (unpublished === 'ok') {
                                        // That's us
                                        selfobj.mysfutest.hangup();
                                        return;
                                    }
                                } else if (msg["error"] !== undefined && msg["error"] !== null) {
                                    Janus.log(msg["error"]);
                                }
                            }
                        }

                        if (jsep !== undefined && jsep !== null) {
                            Janus.log("Handling SDP as well...");
                            Janus.log(jsep);

                            selfobj.mysfutest.handleRemoteJsep({ jsep: jsep });
                        }
                    },
                    onlocalstream: function (stream) {
                        Janus.log(" ::: Got a local stream :::");
                        selfobj.mystream = stream;
                        Janus.log(stream);
                    },
                    onremotestream: function (stream) {
                        // The publisher stream is sendonly, we don't expect anything here
                    },
                    oncleanup: function () {
                        Janus.log(" ::: Got a cleanup notification: we are unpublished now :::");
                        selfobj.mystream = null
                    },
                    error: function (error) {
                        Janus.error(error);
                    },
                    destroyed: function () {
                        Janus.log(" destroyed");
                    }
                });
        })

        var register = { "request": "join", "room": room, "ptype": "publisher", "display": username, "pin": "" };
        this.myusername = username;

        attach_promise.then((pluginHandle) => {
            this.mysfutest = pluginHandle;
            this.mysfutest.send({ "message": register });
        })
    }

    // unpublish local stream
    unpublish() {
        if (this.mysfutest == null || this.mysfutest == undefined)
            return

        // Unpublish our stream
        var unpublish = { "request": "unpublish" };
        this.mysfutest.send({ "message": unpublish });
    }

    // toggle mute audio or video as one publisher
    toggleMuteLocal(isAudio) {
        if (this.mysfutest == null || this.mysfutest == undefined)
            return

        var muted = false;
        if (isAudio) {
            this.mysfutest.isAudioMuted();
        } else {
            this.mysfutest.isVideoMuted();
        }
        Janus.log((muted ? "Unmuting" : "Muting") + " local " + (isAudio ? "audio " : "video ") + "stream...");
        if (muted) {
            if (isAudio) {
                this.mysfutest.unmuteAudio();
            } else {
                this.mysfutest.unmuteVideo();
            }
        } else {
            if (isAudio) {
                this.mysfutest.muteAudio();
            } else {
                this.mysfutest.muteVideo();
            }
        }
        if (isAudio) {
            muted = this.mysfutest.isAudioMuted();
        } else {
            muted = this.mysfutest.isVideoMuted();
        }
    }

    // mute audio or video as one publisher
    muteLocal(room, isAudio) {
        if (this.mysfutest == null || this.mysfutest == undefined)
            return

        if (isAudio) {
            this.mysfutest.muteAudio()
        } else {
            this.mysfutest.muteVideo()
        }
    }

    // unmute audio or video as one publisher
    unmuteLocal(room, isAudio) {
        if (this.mysfutest == null || this.mysfutest == undefined)
            return

        if (isAudio) {
            this.mysfutest.unmuteAudio()
        } else {
            this.mysfutest.unmuteVideo()
        }
    }

    // mute audio or video as one subscriber
    muteRemote(room, display, isAudio) {
        var idx = this.findMatchPluginHandle(room, false)
        if (idx == -1)
            return

        var configure = null
        if (isAudio)
            configure = { "request": "configure", "audio": false }
        else
            configure = { "request": "configure", "video": false }

        if (this.remotefeeds && this.remotefeeds[idx] && this.remotefeeds[idx][display])
            this.remotefeeds[idx][display].send({ "message": configure })
    }

    // unmute audio or video as one subscriber
    unmuteRemote(room, display, isAudio) {
        var idx = this.findMatchPluginHandle(room, false)
        if (idx == -1)
            return

        var configure = null
        if (isAudio)
            configure = { "request": "configure", "audio": true }
        else
            configure = { "request": "configure", "video": true }

        if (this.remotefeeds && this.remotefeeds[idx] && this.remotefeeds[idx][display])
            this.remotefeeds[idx][display].send({ "message": configure })
    }

    isPaused() { return this.janus ? this.janus.isPaused : false; }

    // pause subscribing one room
    pause(room, display) {
        var idx = this.findMatchPluginHandle(room, false)
        if (idx == -1)
            return

        var pause = { "request": "configure", "audio": false, "video": false }
        // var pause = { "request": "pause"}

        if (this.remotefeeds && this.remotefeeds[idx] && this.remotefeeds[idx][display])
            this.remotefeeds[idx][display].send({ "message": pause })
    }

    // resume subscribing one room
    resume(room, display) {
        var idx = this.findMatchPluginHandle(room, false)
        if (idx == -1)
            return

        var resume = { "request": "configure", "audio": true, "video": true }
        // var resume = { "request": "start" }

        if (this.remotefeeds && this.remotefeeds[idx] && this.remotefeeds[idx][display])
            this.remotefeeds[idx][display].send({ "message": resume })
    }

    // leave room as one subscriber
    leave(room, display) {
        var idx = this.findMatchPluginHandle(room, false)
        if (idx == -1)
            return

        var leave = { "request": "leave" }

        Janus.log(this.remotefeeds[idx])

        if (this.remotefeeds == null || this.remotefeeds[idx][display] == null || this.remotefeeds[idx][display] == undefined)
            return;

        this.remotefeeds[idx][display].send({ "message": leave })

        this.remotestreams[idx][display] = null
        this.feeds[idx][display] = null
        this.remotefeeds[idx][display] = null
    }

    // join the room as one subscriber
    subscribe(room, username, pin, callback) {
        var handle = null
        var private_id = null

        let attach_promise = new Promise((resolve, reject) => {

            let selfobj = this

            this.janus.attach(
                {
                    plugin: "janus.plugin.videoroom",
                    opaqueId: "mdsvideo-" + Janus.randomString(12),
                    success: function (pluginHandle) {
                        Janus.log("Plugin attached! (" + pluginHandle.getPlugin() + ", id=" + pluginHandle.getId() + ")");
                        Janus.log("  -- This is a publisher/manager");
                        resolve(pluginHandle)
                    },
                    error: function (error) {
                        Janus.error("  -- Error attaching plugin...", error);
                    },
                    consentDialog: function (on) {
                        Janus.log("Consent dialog should be " + (on ? "on" : "off") + " now");
                    },
                    mediaState: function (medium, on) {
                        Janus.log("Janus " + (on ? "started" : "stopped") + " receiving our " + medium);
                    },
                    webrtcState: function (on) {
                        Janus.log("Janus says our WebRTC PeerConnection is " + (on ? "up" : "down") + " now");
                        if (!on)
                            return false;
                    },
                    onmessage: function (msg, jsep) {
                        Janus.log(" ::: Got a message (publisher) :::");
                        Janus.log(msg);
                        var event = msg["videoroom"];
                        Janus.log("Event: " + event);
                        if (event != undefined && event != null) {
                            if (event === "joined") {
                                // Publisher/manager created, negotiate WebRTC and attach to existing feeds, if any
                                Janus.log("Successfully joined room " + msg["room"] + " with ID " + msg["id"]);

                                private_id = msg["private_id"]

                                var avail_id = selfobj.findMatchPluginHandle(msg["room"], true)
                                if (avail_id != -1) {
                                    var roomInfo = { "room": msg["room"], "id": msg["id"], "private_id": msg["private_id"], "sfutest": handle }
                                    selfobj.roomPluginHandle[avail_id] = roomInfo
                                }

                                // Any new feed to attach to?
                                if (msg["publishers"] !== undefined && msg["publishers"] !== null) {
                                    var list = msg["publishers"];
                                    Janus.log("Got a list of available publishers/feeds:");
                                    Janus.log(list);

                                    for (var f = 0, len = list.length; f < len; f++) {
                                        var id = list[f]["id"];
                                        var display = list[f]["display"];
                                        var audio = list[f]["audio_codec"];
                                        var video = list[f]["video_codec"];
                                        if (id === undefined || id === null)
                                            continue
                                        Janus.log("  >> [" + id + "] " + display + " (audio: " + audio + ", video: " + video + ")");
                                        selfobj.newRemoteFeed(room, id, display, audio, video, private_id, pin)
                                        callback("joined", room, id, display);
                                    }
                                }
                            } else if (event === "destroyed") {
                                // The room has been destroyed
                                Janus.warn("The room " + msg["room"] + " has been destroyed!");

                                callback("destroyed", room)

                                var pluginIdx = selfobj.findMatchPluginHandle(msg["room"], false)
                                if (pluginIdx != -1) {
                                    selfobj.remotestreams[pluginIdx] = []
                                    selfobj.feeds[pluginIdx] = []
                                    selfobj.remotefeeds[pluginIdx] = []

                                    selfobj.removePluginHandle(pluginIdx)
                                }
                            } else if (event === "event") {
                                // Any new feed to attach to?
                                if (msg["publishers"] !== undefined && msg["publishers"] !== null) {
                                    var list = msg["publishers"];
                                    Janus.log("Got a list of available publishers/feeds:");
                                    Janus.log(list);

                                    for (var f = 0, len = list.length; f < len; f++) {
                                        var id = list[f]["id"];
                                        var display = list[f]["display"];
                                        var audio = list[f]["audio_codec"];
                                        var video = list[f]["video_codec"];
                                        Janus.log("  >> [" + id + "] " + display + " (audio: " + audio + ", video: " + video + ")");
                                        selfobj.newRemoteFeed(room, id, display, audio, video, private_id, pin)
                                    }
                                } else if (msg["leaving"] !== undefined && msg["leaving"] !== null) {
                                    // One of the publishers has gone away?
                                    var leaving = msg["leaving"];
                                    Janus.log("Publisher left: " + leaving);

                                    var remoteFeed = null;
                                    var display = null;
                                    var pluginIdx = selfobj.findMatchPluginHandle(msg["room"], false)
                                    if (pluginIdx != -1) {
                                        display = selfobj.mapIdToDisplay[leaving]
                                        if (display != null && display != undefined) {
                                            remoteFeed = selfobj.feeds[pluginIdx][display]
                                        }
                                    }
                                    if (remoteFeed != null) {
                                        Janus.log("Feed " + remoteFeed.rfid + " (" + remoteFeed.rfdisplay + ") has left the room, detaching");

                                        selfobj.feeds[pluginIdx][display] = null;
                                        selfobj.feeds[pluginIdx] = [];
                                        remoteFeed.detach();
                                        selfobj.mapIdToDisplay[leaving] = null;
                                        selfobj.remotefeeds[pluginIdx][display] = null;
                                        selfobj.remotefeeds[pluginIdx] = [];
                                    }

                                    callback("leaving", room, leaving)
                                } else if (msg["unpublished"] !== undefined && msg["unpublished"] !== null) {
                                    // One of the publishers has unpublished?
                                    var unpublished = msg["unpublished"];
                                    Janus.log("Publisher left: " + unpublished);

                                    var pluginIdx = selfobj.findMatchPluginHandle(msg["room"], false)

                                    if (pluginIdx == -1)
                                        return

                                    if (unpublished === 'ok') {
                                        // That's us
                                        selfobj.roomPluginHandle[pluginIdx]["sfutest"].hangup();
                                        return;
                                    }
                                    var remoteFeed = null;
                                    var display = selfobj.mapIdToDisplay[leaving]
                                    if (display != null && display != undefined) {
                                        remoteFeed = selfobj.feeds[pluginIdx][display]
                                    }
                                    if (remoteFeed != null) {
                                        Janus.log("Feed " + remoteFeed.rfid + " (" + remoteFeed.rfdisplay + ") has left the room, detaching");
                                        selfobj.feeds[pluginIdx][display] = null;
                                        remoteFeed.detach();
                                    }

                                    callback("unpublished", room, display)
                                } else if (msg["error"] !== undefined && msg["error"] !== null) {
                                    Janus.log(msg["error"]);
                                }
                            }
                        }

                        if (jsep !== undefined && jsep !== null) {
                            Janus.log("Handling SDP as well...");
                            Janus.log(jsep);

                            var pluginIdx = selfobj.findMatchPluginHandle(msg["room"], false)
                            if (pluginIdx != -1)
                                selfobj.roomPluginHandle[pluginIdx]["sfutest"].handleRemoteJsep({ jsep: jsep });
                        }
                    },
                    onlocalstream: function (stream) {
                        Janus.log(" ::: Got a local stream :::");
                        Janus.log(stream);
                    },
                    onremotestream: function (stream) {
                        // The publisher stream is sendonly, we don't expect anything here
                    },
                    oncleanup: function () {
                        Janus.log(" ::: Got a cleanup notification: we are unpublished now :::");
                    },
                    error: function (error) {
                        Janus.error(error);
                    },
                    destroyed: function () {
                        Janus.log(" destroyed");
                    }
                });
        })

        var register = { "request": "join", "room": room, "ptype": "publisher", "display": username, "pin": pin };
        //var register = { "request": "join", "room": room, "ptype": "subscriber", "display": username, "pin": pin };

        attach_promise.then((pluginHandle) => {
            handle = pluginHandle
            handle.send({ "message": register });
        })
    }

    getBitrate() {
        // var res = this.listAllStreams();
        // if (res && res.length > 0 && this.remotefeeds[0][res[0].display]) {
        //     return this.remotefeeds[0][res[0].display].getBitrate();
        // } else {
        //     return "0";
        // }

        var res = this.listAllStreams();
        if (!res || res.length <= 0)
            return "0";

        var index = this.findMatchPluginHandle(res[0].room, false);
        if (res && res.length > 0 && index != -1 && this.remotefeeds[index][res[0].display]) {
            return this.remotefeeds[index][res[0].display].getBitrate();
        } else {
            return "0";
        }
    }

    newRemoteFeed(room, id, display, audio, video, private_id, pin) {
        this.mapIdToDisplay[id] = display;
        var idx = this.findMatchPluginHandle(room, false);

        if (idx == -1)
            return

        var remoteFeed = null;

        let selfobj = this

        this.janus.attach(
            {
                plugin: "janus.plugin.videoroom",
                opaqueId: "mdsvideo-" + Janus.randomString(12),
                success: function (pluginHandle) {
                    remoteFeed = pluginHandle;
                    Janus.log("Plugin attached! (" + remoteFeed.getPlugin() + ", id=" + remoteFeed.getId() + ")");
                    Janus.log("  -- This is a subscriber");
                    Janus.log(remoteFeed)
                    // We wait for the plugin to send us an offer
                    var subscribe = { "request": "join", "room": room, "ptype": "subscriber", "feed": id, "private_id": private_id, "pin": pin };
                    // In case you don't want to receive audio, video or data, even if the
                    // publisher is sending them, set the 'offer_audio', 'offer_video' or
                    // 'offer_data' properties to false (they're true by default), e.g.:
                    // 		subscribe["offer_video"] = false;
                    // For example, if the publisher is VP8 and this is Safari, let's avoid video
                    if (Janus.webRTCAdapter.browserDetails.browser === "safari" &&
                        (video === "vp9" || (video === "vp8" && !Janus.safariVp8))) {
                        if (video)
                            video = video.toUpperCase()
                        subscribe["offer_video"] = false;
                    }
                    remoteFeed.videoCodec = video;
                    remoteFeed.send({ "message": subscribe });
                    if (selfobj.remotefeeds[idx] == null) {
                        selfobj.remotefeeds[idx] = []
                    }
                    selfobj.remotefeeds[idx][display] = remoteFeed;
                },
                error: function (error) {
                    Janus.error("  -- Error attaching plugin...", error);
                },
                onmessage: function (msg, jsep) {
                    Janus.log(" ::: Got a message (subscriber) :::");
                    Janus.log(msg);

                    var event = msg["videoroom"];
                    Janus.log("Event: " + event);

                    if (msg["error"] !== undefined && msg["error"] !== null) {
                        Janus.error(msg["error"]);
                    } else if (event != undefined && event != null) {
                        if (event === "attached") {
                            // Subscriber created and attached {}
                            if (selfobj.feeds[idx] == null || selfobj.feeds[idx] == undefined) {
                                selfobj.feeds[idx] = []
                            }
                            selfobj.feeds[idx][display] = remoteFeed;
                            remoteFeed.rfindex = idx;
                            remoteFeed.rfid = msg["id"];
                            remoteFeed.rfdisplay = msg["display"];
                            Janus.log("Successfully attached to feed " + remoteFeed.rfid + " (" + remoteFeed.rfdisplay + ") in room " + msg["room"]);
                        } else if (event === "event") {
                            // Check if we got an event on a simulcast-related event from this publisher
                            var substream = msg["substream"];
                            var temporal = msg["temporal"];
                            if ((substream !== null && substream !== undefined) || (temporal !== null && temporal !== undefined)) {
                            }
                        } else {
                            // What has just happened?
                        }
                    }
                    if (jsep !== undefined && jsep !== null) {
                        Janus.log("Handling SDP as well...");
                        Janus.log(jsep);
                        // Answer and attach

                        remoteFeed.createAnswer(
                            {
                                jsep: jsep,
                                // Add data:true here if you want to subscribe to datachannels as well
                                // (obviously only works if the publisher offered them in the first place)
                                media: { audioSend: false, videoSend: false },	// We want recvonly audio/video
                                success: function (jsep) {
                                    Janus.log("Got SDP!");
                                    Janus.log(jsep);
                                    var body = { "request": "start", "room": room };
                                    remoteFeed.send({ "message": body, "jsep": jsep });
                                },
                                error: function (error) {
                                    Janus.error("WebRTC error:", error);
                                }
                            });
                    }
                },
                webrtcState: function (on) {
                    Janus.log("Janus says this WebRTC PeerConnection (feed #" + remoteFeed.rfindex + ") is " + (on ? "up" : "down") + " now");
                },
                onlocalstream: function (stream) {
                    // The subscriber stream is recvonly, we don't expect anything here
                },
                onremotestream: function (stream) {
                    Janus.log("idx:" + idx + " display:" + display)

                    if (selfobj.remotestreams[idx] == null || selfobj.remotestreams[idx] == undefined) {
                        selfobj.remotestreams[idx] = []
                    }

                    selfobj.remotestreams[idx][display] = stream;

                    Janus.log(selfobj.remotestreams[idx][display])
                },
                oncleanup: function () {
                    Janus.log(" ::: Got a cleanup notification (remote feed " + id + ") :::");
                    Janus.log("idx:" + idx + " display:" + display)
                }
            });
    }

    destroy() {
        if (this.janus) {
            this.janus.destroy();
        }
    };
}