<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>案件回访-右</title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/css/viewCss/index.css" />
  <script src="/Vue/vue.js"></script>
  <link rel="stylesheet" href="/elementui/css/index.css" />
  <script src="/elementui/js/index.js"></script>
  <script src="/jquery/jquery-3.6.1.min.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/echarts/5.4.1/echarts.min.js"></script>
  <script src="/echarts/echarts-gl.min.js"></script>
  <script src="/static/js/jslib/moment.js"></script>
  <script src="/elementui/js/index.js"></script>
  <style>
    .el-range-editor .el-range-input {
      background: transparent;
    }

    .el-picker-panel__icon-btn {
      color: #fff !important;
    }

    .el-input__inner {
      background-color: rgba(19, 44, 78, 0.8) !important;
      border: 1px solid #359cf8 !important;
    }

    .el-date-editor .el-range-input,
    .el-date-editor .el-range-separator {
      font-size: 25px !important;
    }

    .el-date-editor .el-range__icon {
      font-size: 22px !important;
    }

    .el-date-editor .el-range-input,
    .el-month-table td .cell {
      color: #fff !important;
    }

    .el-picker-panel {
      border: 1px solid #00b7f3;
      box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%) !important;
      background: #0f233e !important;
    }

    .el-month-table,
    .el-year-table,
    .el-date-range-picker__header div {
      font-size: 20px !important;
    }

    .el-month-table td.in-range div,
    .el-month-table td.in-range div:hover {
      background-color: #264068 !important;
    }

    .el-date-editor .el-range__close-icon {
      font-size: 20px !important;
    }

    .el-date-editor .el-range-separator {
      color: #e6e9ee !important;
    }

    .hffx-container {
      width: 100%;
      margin-bottom: 53px;
    }

    .hffx-container-charts {
      width: 100%;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin-top: 48px;
    }

    .hffx-container-chart {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
    }

    .hearder_h2 {
      width: 487px !important;
      height: 56px !important;
      background: url("/static/images/ajhf/titleBg.png") no-repeat;
      background-size: cover !important;
    }

    .chart {
      width: 320px;
      height: 580px;
      margin-top: 36px;
    }

    .chart2 {
      width: 800px;
      height: 616px;
    }

    .jbgz-container {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
    }

    .jbgz-container-tabs {
      width: 100%;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin-top: 27px;
    }

    .jbgz-container-tab {
      width: 210.3px;
      height: 59px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 36px;
      color: #ABCEEF;
      font-style: italic;
      text-align: center;
      cursor: pointer;
    }

    .jbgzContainerActiveTab {
      width: 210.3px;
      height: 59px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      font-size: 36px;
      color: #DAEDFF;
      font-style: italic;
      background: url('/static/images/ajhf/activeBg.png') no-repeat;
      background-size: cover;
      text-align: center;
    }

    .jbgz-container-table {
      width: 976px;
      margin-top: 37px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
    }

    .jbgz-container-table-container {
      width: 100%;
      height: 700px;
      overflow-y: scroll;
    }

    .jbgz-container-table-container-line {
      width: 100%;
      height: 100px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      background: rgba(50, 134, 248, 0.25);
    }

    .lineTitle {
      background: transparent !important;
    }

    .twiceLine {
      background: rgba(50, 134, 248, 0.15) !important;
    }

    .columnTitle {
      font-family: Source Han Sans CN;
      font-weight: bold;
      font-size: 30px;
    }

    .jbgz-container-table-container-line-column {
      flex: 1;
      height: 100px;
      line-height: 100px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 30px;
      color: #FFFFFF;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 30px;
    }

    ::-webkit-scrollbar {
      width: 0;
    }

    .blueColor {
      color: #3CFDFF !important;
    }

    .yellowColor {
      color: #EED252 !important;
    }

    .tipFont {
      font-size: 32px;
    }

    .commonTab {
        font-size: 45px !important;
    }
  </style>
</head>

<body>
  <div id="right" v-cloak>
    <div class="hearder_h1"><span>回访分析</span></div>
    <div class="hffx-container">
      <div style="position: absolute; top: 26px; left: 680px; z-index: 2" v-show="year != '2023'">
        <el-date-picker v-model="value1" type="monthrange" @change="(range) => initAllChart(range,city,year)"
          range-separator="-" start-placeholder="开始月份" value-format="yyyy-MM" end-placeholder="结束月份">
        </el-date-picker>
      </div>
      <div class="hffx-container-charts">
        <!--          <div class="hffx-container-chart">-->
        <!--            <div class="hearder_h2"><span>理由维度</span></div>-->
        <!--            <div class="chart" id="liyouChart"></div>-->
        <!--          </div>-->
        <div class="hffx-container-chart">
          <!--            <div class="hearder_h2"><span>领域维度</span></div>-->
          <div class="chart2" id="lingyuChart"></div>
        </div>
      </div>
    </div>


    <div class="hearder_h1">
      <span v-for="(item,i) in Tabs" @click="menuClick(item,i)" style="cursor: pointer" :class="{commonTab: currentTab != i}">{{item}}</span>
    </div>
    <div style="position: absolute; top: 820px; left: 680px; z-index: 2" v-show="year != '2023'">
      <el-date-picker v-model="value2" type="monthrange"
                      @change="(range) => dateChange(range)" range-separator="-"
                      start-placeholder="开始月份" value-format="yyyy-MM" end-placeholder="结束月份">
      </el-date-picker>
    </div>
    <!--认领跟踪-->
    <div class="jbgz-container" v-show="currentTab == 1">
      <div class="jbgz-container-tabs">
        <div class="jbgz-container-tab" v-for="(item,i) in jbgzTabList" :key="i"
          :class="{jbgzContainerActiveTab: currentjbgz == i}" @click="jbgzTabClick(i,item)">
          {{ item.type }}
        </div>
      </div>
      <div class="jbgz-container-table">
        <div class="jbgz-container-table-container-line lineTitle">
          <div class="jbgz-container-table-container-line-column columnTitle">任务名称</div>
          <div class="jbgz-container-table-container-line-column columnTitle">接收部门</div>
          <div class="jbgz-container-table-container-line-column columnTitle">接收时间</div>
          <div class="jbgz-container-table-container-line-column columnTitle">处理状态</div>
        </div>
        <div class="jbgz-container-table-container">
          <div class="jbgz-container-table-container-line" v-for="(item,i) in tableData" :key="i"
            :class="{twiceLine:i%2 != 0}">
            <el-tooltip class="item" effect="dark" :content="item.caseNo" placement="top" popper-class="tipFont">
              <div class="jbgz-container-table-container-line-column">{{item.caseNo}}</div>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" :content="item.claimDept" placement="top" popper-class="tipFont">
              <div class="jbgz-container-table-container-line-column">{{item.claimDept}}</div>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" :content="item.claimTime" placement="top" popper-class="tipFont">
              <div class="jbgz-container-table-container-line-column">{{item.claimTime}}</div>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" :content="item.claim == '1'?'处置中':'已完成'" placement="top"
              popper-class="tipFont">
              <div class="jbgz-container-table-container-line-column"
                :class="{blueColor:item.handleStatus == '3',yellowColor:item.handleStatus == '2'}">{{item.handleStatus == "2"?"处置中":"已完成"}}</div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
    <!--回访跟踪-->
    <div class="jbgz-container" v-show="currentTab == 0">
      <div class="jbgz-container-tabs">
        <div class="jbgz-container-tab" v-for="(item,i) in hfgzTabList" :key="i"
             :class="{jbgzContainerActiveTab: currenthfgz == i}" @click="hfgzTabClick(i,item)">
          {{ item.label }}
        </div>
      </div>
      <div class="jbgz-container-table">
        <div class="jbgz-container-table-container-line lineTitle">
          <div class="jbgz-container-table-container-line-column columnTitle">案件编号</div>
          <div class="jbgz-container-table-container-line-column columnTitle">区域</div>
          <div class="jbgz-container-table-container-line-column columnTitle">决定送达日期</div>
          <div class="jbgz-container-table-container-line-column columnTitle">回访状态</div>
        </div>
        <div class="jbgz-container-table-container">
          <div class="jbgz-container-table-container-line" v-for="(item,i) in tableData2" :key="i"
               :class="{twiceLine:i%2 != 0}" @click="showDetail(item.caseNo)">
              <div class="jbgz-container-table-container-line-column" :title="item.caseNo">{{item.caseNo}}</div>
              <div class="jbgz-container-table-container-line-column">{{item.nationRegionName}}</div>
              <div class="jbgz-container-table-container-line-column">{{item.deciDeliveryDate}}</div>
              <div class="jbgz-container-table-container-line-column"
                   :class="{blueColor:item.followStatus == '2',yellowColor:item.followStatus == '3'}">{{labelGet(followStatusList,item.followStatus)}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script>
    window.parent.eventbus &&
      window.parent.eventbus.on("yearChange", (year) => {
        vm.year = year
        vm.initApi(localStorage.getItem("city"), year);
      });
    let vm = new Vue({
      el: "#right",
      data: {
        year: "",
        city: "",
        value1: [
          new Date().getFullYear() + "-01",
          moment(new Date()).format("YYYY-MM"),
        ],
        value2: [
          new Date().getFullYear() + "-01",
          moment(new Date()).format("YYYY-MM"),
        ],
        currentjbgz: 0,
        currenthfgz: 0,
        jbgzTabList: [
          {
            type: "全部",
            value: ""
          },
          {
            type: "处置中",
            value: "2"
          },
          {
            type: "已完成",
            value: "3"
          }
        ],
        hfgzTabList: [
          {
            label: '全部',
            value: '',
          },
          {
            label: '未回访',
            value: '1',
          },
          {
            label: '回访中',
            value: '3',
          },
          {
            label: '已回访',
            value: '2',
          }
        ],
        // 回访状态列表
        followStatusList: [
          {
            label: '无需回访',
            value: '0',
          },
          {
            label: '未回访',
            value: '1',
          },
          {
            label: '回访中',
            value: '3',
          },
          {
            label: '已回访',
            value: '2',
          }
        ],
        tableData: [],
        tableData2: [],
        questionList: [
          {
            id:"1",
            name:"需要了解信用信息",
            value:0
          },
          {
            id:"2",
            name:"按期履行缴纳困难",
            value:0
          },
          {
            id:"3",
            name:"未告知裁量理由",
            value:0
          },
          {
            id:"4",
            name:"执法人员疑似存在廉洁问题",
            value:0
          },
          {
            id:"5",
            name:"未告知并保障相应权利",
            value:0
          },
          {
            id:"6",
            name:"不当行为侵害权利",
            value:0
          },
          {
            id:"7",
            name:"需进一步法律释疑",
            value:0
          },
          {
            id:"8",
            name:"需要法律帮扶",
            value:0
          },
          {
            id:"9",
            name:"需要了解相关审批事项",
            value:0
          },
          {
            id:"10",
            name:"执法工作不规范",
            value:0
          },
          {
            id:"11",
            name:"有意见建议",
            value:0
          }
        ],
        currentTab: 0,
        Tabs: ["回访跟踪","认领跟踪"]
      },
      computed: {

      },
      mounted() {
        this.year = localStorage.getItem("year");
        this.city = localStorage.getItem("city");
        this.initApi(this.city, this.year);
        window.parent.eventbus && window.parent.eventbus.on("cityChange", (city) => {
          let filtName =
            city == "金义新区" ?
            "金东区" :
            city == "金华开发区" ?
            "开发区" :
            city;
          this.initApi(filtName, this.year);
        });
      },
      methods: {
        menuClick(item,index) {
          this.currentTab = index;
          if (item == "回访跟踪") {
            window.open("https://csdn.dsjj.jinhua.gov.cn:8303/ajhf/casepool")
          }
        },
        dateChange(range) {
          this.getJbgz(range, this.city, this.jbgzTabList[this.currentjbgz].value)
          this.getHfgz(range, this.city, this.hfgzTabList[this.currenthfgz].value)
        },
        initApi(city, year) {
          if (year && year != '') {
            if (year == new Date().getFullYear()) {
              this.value1 = [new Date().getFullYear() + "-01", moment(new Date()).format("YYYY-MM")]
              this.value2 = [new Date().getFullYear() + "-01", moment(new Date()).format("YYYY-MM")]
            } else {
              this.value1 = [year + "-01", moment(`${year}-01-01`).endOf('year').format('YYYY-MM-DD')]
              this.value2 = [year + "-01", moment(`${year}-01-01`).endOf('year').format('YYYY-MM-DD')]
            }
            this.initAllChart(this.value1, city)
            this.getJbgz(this.value2, city, this.jbgzTabList[this.currentjbgz].value)
            this.getHfgz(this.value2, city, this.hfgzTabList[this.currenthfgz].value)
          }

        },
        //获取交办跟踪列表
        getJbgz(range, city, value) {
          // console.log(range, city, value);
          $api2Get("/ajhf/zftsZfrw/jbgz", {
            xsq: city,
            startTime: range[0],
            endTime: range[1],
            handleStatus:value
          }).then(res => {
            if (res.data.code == 200) {
              let resdata = res.data.data
              this.tableData = resdata
            }
          })
        },
        //获取回访跟踪列表
        getHfgz(range, city, value) {
          // console.log(range, city, value);
          $api2Get("/ajhf/zftsZfrw/qlScreenList", {
            nationRegionName: city === "金华市"?"":city,
            deciDeliveryDate: range[0],
            deciDeliveryDateEnd: range[1],
            followStatus:value
          }).then(res => {
            this.tableData2 = res.data.data
          })
        },
        //获取理由维度，领域维度图表信息
        initAllChart(range, city) {
          // this.initChartLIYOU(city,year);
          this.questionListMap = this.questionList.reduce((map, obj) => {
            map[obj.id] = obj;
            return map;
          }, {});
          $api2Get("/ajhf/zftsZfrw/hffx", {
            xsq: city,
            startTime: range[0],
            endTime: range[1],
          }).then(res => {
            if (res.data && res.data.code === 200) {
              let resdata = res.data.data.map(item => {
                const { name, bfb } = item;
                return {
                  name: this.questionListMap[name]?.name || '', // 添加了防御性编程，如果找不到name，则提供一个默认值
                  value: bfb.toFixed(0)
                };
              });
              this.initChartLINGYU(resdata)
            } else {
              console.error("请求失败，错误代码:", res.data.code);
            }
          })
        },
        initChartLIYOU(city, year) {
          let that = this
          let myChart = echarts.init(document.getElementById("liyouChart"));
          let imgUrl = '/static/images/ajhf/chartBg.png'
          let data = [{
            name: "不满意理由1",
            value: 60
          }, {
            name: "不满意理由2",
            value: 30
          }, {
            name: "不满意理由3",
            value: 80
          }, {
            name: "不满意理由4",
            value: 50
          }, {
            name: "不满意理由5",
            value: 40
          }]


          // 图表option整理
          let pieSeries = []
          let sum = this.getSum(data, "value")
          data.forEach((v, i) => {
            pieSeries.push({
              name: '不满意',
              type: 'pie',
              clockWise: false,
              hoverAnimation: false,
              radius: [65 - i * 15 + '%', 57 - i * 15 + '%'],
              center: ['52%', '31%'],
              label: {
                show: false
              },
              data: [{
                  value: v.value,
                  name: v.name
                },
                {
                  value: sum - v.value,
                  name: '',
                  itemStyle: {
                    color: "rgba(0,0,0,0)"
                  }
                }
              ]
            });
            pieSeries.push({
              name: '满意',
              type: 'pie',
              silent: true,
              z: 1,
              clockWise: false, //顺时加载
              hoverAnimation: false, //鼠标移入变大
              radius: [65 - i * 15 + '%', 57 - i * 15 + '%'],
              center: ['52%', '31%'],
              label: {
                show: false
              },
              data: [{
                value: 7.5,
                itemStyle: {
                  color: "#E3F0FF"
                }
              }, {
                value: 2.5,
                name: '',
                itemStyle: {
                  color: "rgba(0,0,0,0)"
                }
              }]
            });
            v.percent = (v.value / sum * 100).toFixed(1) + "%";
          })
          let option = {
            color: ['#00C0FF', '#22E8E8', '#FFD461', '#A9DB52', '#B76FD8', '#FD852E', '#FF4949', '#0594C3',
              '#009D9D', '#A47905'
            ],
            tooltip: {
              trigger: 'item',
              // formatter: '{b}: <br/> {d}%',
              formatter: '{b}: <br/> {c}个<br/> {d}%',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '25',
              },
            },
            legend: {
              // orient: 'vertical',
              left: '10%',
              top: '60%',
              bottom: '0%',
              icon: 'circle',
              itemGap: 20,
              textStyle: {
                rich: {
                  name: {
                    fontSize: 25,
                    color: '#ffffff',
                    padding: [0, 20, 0, 15]
                  },
                  value: {
                    fontSize: 25,
                    color: '#2CC6F9',
                    // padding: [10, 0, 0, 15]
                  },
                }
              },
              formatter: function (name) {
                var data = that.concatChildArr(option.series.filter(item => item.name == "不满意"))
                var total = 0
                var tarValue
                for (var i = 0, l = data.length; i < l; i++) {
                  total += data[i].value
                  if (data[i].name == name) {
                    tarValue = data[i].value
                  }
                }
                that.serverNum = total;
                var p = ((tarValue / total) * 100).toFixed(0)
                return '{name|' + name + '}{value|' + p + '%}'
              },
            },
            graphic: [{
              type: "image",
              id: "logo1",
              left: "10.4%",
              top: "8.4%",
              z: -10,
              bounding: "raw",
              rotation: 0, //旋转
              origin: [50, 50], //中心点
              scale: [0.8, 0.8], //缩放
              style: {
                image: imgUrl,
                opacity: 1,
              },
            }, ],
            series: pieSeries,
          }
          myChart.setOption(option)
          myChart.getZr().on('mousemove', param => {
            myChart.getZr().setCursorStyle('default')
          })
        },
        initChartLINGYU(resdata) {
          let that = this
          let myChart = echarts.init(document.getElementById("lingyuChart"));
          let imgUrl = '/static/images/ajhf/chartBg3.png'
          let option = {
            color: ['#00C0FF', '#22E8E8', '#FFD461', '#A9DB52', '#B76FD8', '#FD852E', '#FF4949', '#0594C3',
              '#009D9D', '#A47905', '#E91E63'
            ],

            tooltip: {
              trigger: "item",
              backgroundColor: "rgba(51, 51, 51, 0.7)",
              borderWidth: 0,
              axisPointer: {
                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
              },
              textStyle: {
                color: "white",
                fontSize: "24",
              },
            },
            legend: {
              orient: 'vertical',
              left: '0',
              top: '65%',
              bottom: '0%',
              icon: 'circle',
              itemGap: 10,
              textStyle: {
                rich: {
                  name: {
                    fontSize: 25,
                    color: '#ffffff',
                    padding: [0, 20, 0, 15]
                  },
                  value: {
                    fontSize: 25,
                    color: '#2CC6F9',
                    // padding: [10, 0, 0, 15]
                  },
                }
              },
              formatter: function (name) {
                var data = option.series[0].data //获取series中的data
                var total = 0
                var tarValue
                for (var i = 0, l = data.length; i < l; i++) {
                  total += Number(data[i].value)
                  if (data[i].name == name) {
                    tarValue = data[i].value
                  }
                }
                that.serverNum = total;
                // var p = ((tarValue / total) * 100).toFixed(2)
                // return '{name|' + name + '}{value|' + Number.isNaN(p)?0:p + '%}'
                if (total === 0) {
                  return `{name|${name}}{value|0%}`; // 直接返回0%，同时保持了字符串格式不变
                }

                // 计算百分比并确保其为数字类型
                let percentage = ((tarValue / total) * 100).toFixed(2);
                percentage = Number(percentage);

                // 使用模板字符串和Number.isNaN来提高代码的可读性和准确性
                return `{name|${name}}{value|${Number.isNaN(percentage) ? 0 : percentage + '%'}}`;
              },
            },
            graphic: [{
              type: "image",
              id: "logo2",
              left: "28.4%",
              top: "2.5%",
              z: -10,
              bounding: "raw",
              rotation: 0, //旋转
              origin: [50, 50], //中心点
              scale: [0.8, 0.8], //缩放
              style: {
                image: imgUrl,
                opacity: 1,
              },
            }, ],
            series: [{
              name: '',
              type: 'pie',
              radius: ['30%', '45%'],
              center: ['50%', '30%'],
              roseType: '',
              itemStyle: {
                borderRadius: 0,
              },
              label: {
                show: true,
                color: "#FFFFFF",
                formatter: function (params) {
                  return params.percent + "%"
                },
                fontSize: 20
              },
              labelLine: {
                length: 15,
                length2: 30,
                maxSurfaceAngle: 80
              },
              data: resdata,
            }, ],
          }
          myChart.setOption(option)
          myChart.getZr().on('mousemove', param => {
            myChart.getZr().setCursorStyle('default')
          })
        },
        jbgzTabClick(i, item) {
          this.currentjbgz = i
          this.getJbgz(this.value2, this.city, item.value)
        },
        hfgzTabClick(i, item) {
          this.currenthfgz = i
          this.getHfgz(this.value2, this.city, item.value)
        },
        showDetail(caseNo) {
          //已回访案件内点击案件名称
          window.parent.lay.openIframe({
            type: "openIframe",
            name: "ajDetailDialog",
            id: "ajDetailDialog",
            src: baseURL.url + "/static/citybrain/ajhf/ajDetailDialog.html",
            left: "1160px",
            top: "255px",
            width: "1515px",
            height: "1186px",
            zIndex: "667",
            argument: {
              caseNo: caseNo,
            },
          });
        },
        getSum(arr, key) {
          let sum = 0
          arr.forEach((item, i) => {
            sum += item[key]
          })
          return sum;
        },
        concatChildArr(arr) {
          let newArr = []
          arr.forEach((item, i) => {
            newArr.push(item.data.filter(item => item.name != "")[0])
          })
          return newArr
        },
        // 字典翻译
        labelGet(list, value) {
          if (!Array.isArray(list)) {
            console.error("Expected an array for the 'list' parameter.")
            return ''
          }

          const foundItem = list.find((item) => item.value == value)

          // 确保找到对应的item后再返回其label属性，否则返回空字符串
          return value === null || value === undefined || value === '' || !foundItem
            ? ''
            : foundItem.label
        }
      }
    });
  </script>
</body>

</html>
