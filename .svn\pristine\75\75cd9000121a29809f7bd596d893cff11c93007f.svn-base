import { getLayerConfigById } from "./layerConfig.js";
import * as reactiveUtils from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/core/reactiveUtils.js";
import { addLayersToMap, changeLayersVisible } from "./core.js";

const LAYER_ID = "INTEGRATED_Mesh";

async function addIntegratedMesh(view, zoom) {
  if (!view) {
    throw new Error("参数view为必传！");
  }
  const layerConfigs = getLayerConfigById(LAYER_ID);
  let layers;
  if (zoom === undefined) {
    layers = addLayersToMap(view, layerConfigs);
  }

  //
  if (zoom) {
    if (typeof zoom === "number") {
      reactiveUtils.watch(
        () => view.zoom,
        async (updating) => {
          if (updating > zoom) {
            if (!layers) {
              layers = addLayersToMap(view, layerConfigs);
            } else {
              changeLayersVisible(layers, true);
            }
          } else {
            changeLayersVisible(layers, false);
          }
        }
      );
    } else {
      throw new Error("zoom必须为数字！");
    }
  }
}

function removeIntegratedMesh(view) {
  if (!view) {
    throw new Error("参数view为必传！");
  }

  const layerConfigs = getLayerConfigById(LAYER_ID);
  const layers = [];
  for (let i = 0, len = layerConfigs.length; i < len; i++) {
    const item = layerConfigs[i];
    let itemLayer = view.map.findLayerById(item.id);
    if (itemLayer) {
      layers.push(itemLayer);
    }
  }
  view.map.removeMany(layers);
}

export { addIntegratedMesh, removeIntegratedMesh };
