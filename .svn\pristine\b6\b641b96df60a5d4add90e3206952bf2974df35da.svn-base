const baseURL = {
  url230: "",
  // url: 'http://***************:8101',
  // url: 'https://csdn.dsjj.jinhua.gov.cn:8300',
  url: "http://localhost:8201",
  // admApi: '/prod-api',
  admApi: "/adm-api",
};
hasToken = function () {
  function getUser(variable) {
    let urlValAll = window.location.search.substring(1).split("&");
    for (var i = 0; i < urlValAll.length; i++) {
      // 从等号部分分割成字符
      var pair = urlValAll[i].split("=");
      // 如果第一个元素等于 传进来的参的话 就输出第二个元素
      if (pair[0] == variable) {
        return pair[1];
      }
    }
    return false;
  }
  if (getUser("portToken")) {
    return getUser("portToken");
  } else if (getUser("token")) {
    return getUser("token");
  } else if (sessionStorage.getItem("token")) {
    return sessionStorage.getItem("token");
  } else {
    return false;
  }
};
const install = axios.create({
  baseURL: baseURL.url + "/data/", // 本地接口
  timeout: 30000, // 请求超时时间
  headers: {
    "Content-Type": "application/json;charset=UTF-8",
    portToken: hasToken(),
    ptid: "PT0001",
  },
});
const install_ = axios.create({
  baseURL: baseURL.url + "/api/?indexid=", // 本地接口
  timeout: 30000, // 请求超时时间
  headers: {
    "Content-Type": "application/json;charset=UTF-8",
    ptid: "PT0001",
  },
});
const installPost = axios.create({
  baseURL: baseURL.url, // 本地接口
  timeout: 30000, // 请求超时时间
});

//请求拦截
install.interceptors.request.use(
  (config) => {
    return config;
  },
  (err) => {
    Promise.reject(err);
  }
);

install.interceptors.response.use(
  (res) => {
    if (res.data.responsecode == 10006) {
      console.error("Token失效 ");
      top.postMessage(JSON.stringify({ type: "toLogin" }));
    } else if (res.data.responsecode !== 200) {
      console.error("接口返回异常 ", res);
    }
    return res.data.data;
  },
  (err) => {
    console.error("接口返回异常 ", res);
  }
);

install_.interceptors.response.use(
  (res) => {
    if (res.data.responsecode == 10006) {
      console.error("Token失效 ");
      // top.postMessage({ type: 'toLogin4k' }, '*')
      top.postMessage(JSON.stringify({ type: "toLogin" }));
    } else if (res.data.responsecode !== 200) {
      console.error("接口返回异常 ", res);
    }
    return res.data.data;
  },
  (err) => {
    console.error("接口返回异常 ", err);
  }
);

const $post = (url, data) => {
  return new Promise((resolve, reject) => {
    installPost
      .post(url, data)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};
const $get = (url, data) => {
  return new Promise((resolve, reject) => {
    install
      .get(url, { params: data })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

const baseURL2 = {
  url:"http://172.16.10.98:8080/xzzfj-api",
  // url:"http://***************:8330/xzzfj-api", //测试环境
  // url:"https://csdn.dsjj.jinhua.gov.cn:8300/xzzfj-api"
}
const install2_ = axios.create({
  baseURL: baseURL2.url, // 本地接口
  timeout: 30000, // 请求超时时间
  headers: {
    "Content-Type": "application/json;charset=utf-8",
    "Authorization": localStorage.getItem("Admin-Token"),
  },
});
const install_indexPort = axios.create({
  baseURL: baseURL2.url + "/indexPort?indexid=", // 指标接口
  timeout: 30000, // 请求超时时间
  headers: {
    "Content-Type": "application/json;charset=utf-8",
    portToken: sessionStorage.getItem("csdnToken"),
    ptid: "PT0001",
  },
});
const $api = (url, data) => {
  return new Promise((resolve, reject) => {
    install_
      .get(url, { params: data })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};
const $api2Get = (url, data) => {
  return new Promise((resolve, reject) => {
    install2_
      .get(url, { params: data })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};
const $api2Post = (url, data) => {
  return new Promise((resolve, reject) => {
    install2_
      .post(url, data)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

//请求拦截
install_.interceptors.request.use(
  (config) => {
    console.log(config);
    // 每次请求时动态获取最新的token
    config.headers.portToken = hasToken();
    if (config.headers.portToken == null) {
      debugger;
      // location.href = "/login";
    }
    return config;
  },
  (err) => {
    Promise.reject(err);
  }
);

//请求拦截
install2_.interceptors.request.use(
  (config) => {
    console.log(config);
    // 每次请求时动态获取最新的token
    config.headers["Authorization"] = localStorage.getItem("Admin-Token");
    if (config.headers.portToken == null) {
      debugger;
      // location.href = "/login";
    }
    return config;
  },
  (err) => {
    Promise.reject(err);
  }
);

const uavUrl = "http://localhost:8201/uav"
// url:"http://***************:8330/uav", //测试环境
// url:"https://csdn.dsjj.jinhua.gov.cn:8300/uav"
const uavinstall = axios.create({
  baseURL: uavUrl, // 本地接口
  timeout: 30000, // 请求超时时间
  headers: {
    "Content-Type": "application/json;charset=utf-8",
    "token": localStorage.getItem("uav-token")?localStorage.getItem("uav-token"):"",
  },
});
const $uavGet = (url, data) => {
  return new Promise((resolve, reject) => {
    uavinstall
      .get(url, { params: data })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};
const $uavPost = (url, data) => {
  return new Promise((resolve, reject) => {
    uavinstall
      .post(url, data)
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
      });
  });
};
