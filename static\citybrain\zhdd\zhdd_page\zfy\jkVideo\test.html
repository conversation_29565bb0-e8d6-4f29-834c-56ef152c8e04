<!--
 * @Author: xk_zhang
 * @Date: 2022-09-19 00:32:53
 * @E-mail: <EMAIL>
 * @LastEditors: xk_zhang
 * @LastEditTime: 2022-09-19 02:11:46
 * @Desc:
-->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>执法记录测试</title>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/layui/layui.js"></script>
    <script src="axios.min.js"></script>
    <script src="md5.js"></script>
    <script src="zfjly.js"></script>
    <style>
      .rwgz-tc {
        position: relative;
        width: 1500px;
        height: 1000px;
        background-image: url(/static/images/zhdd/bg_panel.png);
        background-size: 100% 100%;
        /* border: 2px solid #3a9ff8;
        background: rgba(3,24,39,.8); */
        border-radius: 57px;
      }
      .rw-title {
        position: absolute;
        top: 50px;
        z-index: 888;
        /* background: linear-gradient(#00aae2, #064b65); */
        border-radius: 57px 57px 0 0;
        width: 100%;
        height: 60px;
        line-height: 60px;
        padding: 1% 3%;
        box-sizing: border-box;
      }
      .close {
        background: url("/static/images/zhdd/close.png") no-repeat;
        width: 34px;
        height: 34px;
        cursor: pointer;
        float: right;
      }
    </style>
  </head>
  <body>
    <div class="rwgz-tc">
      <div class="rw-title flex-between">
        <div class="fs-44 font-syBold" id="rwTitle"></div>
        <div class="close" id="rwClose1" style="z-index: 9999"></div>
      </div>
      <video
        id="video"
        controls="controls"
        autoplay="autoplay"
        muted="false"
        name="media"
        style="
          width: 94%;
          height: 90%;
          position: absolute;
          top: 75px;
          left: 50px;
        "
        ref="video"
      ></video>
      <div
        style="
          z-index: 1000;
          position: absolute;
          top: 40px;
          left: 20px;
          display: none;
        "
      >
        <button
          style="width: 70px; height: 30px; margin-bottom: 10px; display: none"
          onclick="openZFJLY()"
        >
          登录
        </button>
        <button
          style="width: 70px; height: 30px; margin-bottom: 10px; display: none"
          onclick="getInfo()"
        >
          打开视频
        </button>
        <button
          style="width: 70px; height: 30px; margin-bottom: 10px"
          onclick="startAudio()"
        >
          呼叫
        </button>
      </div>
    </div>
  </body>
  <script type="module">
    import {
      Wsplayer,
      HZRecorder_pcm_push,
      HZRecorder_pcm,
    } from "./JY-chromePlayer.min.js";
    var videoDom = document.getElementById("video");
    var player,
      isAudio,
      num = 0,
      recorders,
      recordersPlatform;
    function getplatform() {
      return new Promise((reslove, reject) => {
        var that = this;
        navigator.getUserMedia =
          navigator.getUserMedia || navigator.webkitGetUserMedia;
        if (navigator.getUserMedia) {
          navigator.getUserMedia(
            { audio: true }, //只启用音频
            function (stream) {
              recordersPlatform = new HZRecorder_pcm_push(stream, {});
              reslove();
            },
            function (error) {
              switch (error.code || error.name) {
                case "PERMISSION_DENIED":
                case "PermissionDeniedError":
                  throwError("用户拒绝提供信息。");
                  break;
                case "NOT_SUPPORTED_ERROR":
                case "NotSupportedError":
                  throwError("浏览器不支持硬件设备。");
                  break;
                case "MANDATORY_UNSATISFIED_ERROR":
                case "MandatoryUnsatisfiedError":
                  throwError("无法发现指定的硬件设备。");
                  break;
                default:
                  throwError("无法打开麦克风。" + error);
                  break;
              }
            }
          );
        } else {
          throwError("当前浏览器不支持录音功能。");
          return;
        }
      });
    }
    window.getInfo = function (vdDept = ["T0C2675"]) {

      zfjly.getDeviceInfo();
      var send = {
        hostbody_arr: vdDept || ["T0C2675"],
      };
      zfjly.startLiveVideo(send).then((res) => {

        if (res.data.code == 200) {
          var data;
          /*/ 将ws地址固定成nginx代理后的地址
                var ws =
                    "ws://" +
                    res.data.data[0].wsip +
                    ":" +
                    res.data.data[0].wsport +
                    "/RTSP/H264_AAC";
                /*/
          var ws = "wss://csdn.dsjj.jinhua.gov.cn:8101/pullFlow";
          //*/
          var rtsp = res.data.data[0].rtsp;
          console.log(ws, rtsp);
          zfjly.dataInfo_set.forEach((item) => {
            if (item.hostbody == zfjly.form.hostbody) {
              data = item;
            }
          });
          let dataObj = {
            /*/ 将ws协议修改成wss协议
                     ws: ws,
                     /*/
            wss: ws,
            //*/
            rtsp: rtsp,
            data: data,
            isVideo: true, //true为视频拉流，false为音频拉流
          };
          player = new Wsplayer(ws, rtsp, videoDom, "noGB");
          player.openws().then((obj) => {});
        } else {
          layui.use("layer", function () {
            var layer = layui.layer;
            layer.msg('<span style="font-size: 30px;">设备不在线</span>');
          });
          // alert(res.msg);
        }
      });
    };
    window.openZFJLY = function () {
      zfjly.loginZFJLY();
    };
    window.startAudio = function (vdDept = ["T0C2675"]) {
      var imei;
      zfjly.unitEquipTree("1001", "bh", "dname", false).then((res) => {
        res.data.data.forEach((item) => {
          if (item.hostbody === zfjly.form.hostbody) {
            imei = item.imei;
          }
        });
        if (isAudio) {
          let send = {
            imei: imei,
            type: "startmute",
          };
          zfjly.send_cmd(send).then((res) => {
            isAudio = false;
          });
        } else {
          if (num === 0) {
            getplatform().then(() => {
              // 只允许拉一路音频流
              navigator.getUserMedia =
                navigator.getUserMedia || navigator.webkitGetUserMedia;
              if (!navigator.getUserMedia) {
                alert("浏览器不支持音频输入");
              } else {
                navigator.getUserMedia(
                  {
                    audio: true,
                  },
                  function (mediaStream) {
                    // 初始化
                    recorders = new HZRecorder_pcm(mediaStream, {});
                    let send = {
                      // hostbody_arr: [zfjly.form.hostbody],
                      hostbody_arr: vdDept || ["T0C2675"],
                    };
                    zfjly.startLiveAudio(send).then((res) => {
                      if (res.data.code === 200) {
                        var ws =
                          "ws://" +
                          res.data.data[0].wsip +
                          ":" +
                          res.data.data[0].wsport +
                          "/RTSP/AAC";
                        var wsBroadcast =
                          "ws://" +
                          res.data.data[0].wsip +
                          ":" +
                          res.data.data[0].wsport +
                          "/RTSP/AAC/Broadcast";
                        var rtspAudio = res.data.data[0].rtsp;
                        var logincode = res.data.data[0].logincode;
                        isAudio = true;
                        num++;
                        recordersPlatform
                          .openWebSocket(wsBroadcast, logincode)
                          .then(() => {});
                      } else {
                        alert(res.msg);
                      }
                    });
                  },
                  function (error) {
                    console.log(error);
                    switch (error.message || error.name) {
                      case "PERMISSION_DENIED":
                      case "PermissionDeniedError":
                        console.info("用户拒绝提供信息。");
                        break;
                      case "NOT_SUPPORTED_ERROR":
                      case "NotSupportedError":
                        console.info("浏览器不支持硬件设备。");
                        break;
                      case "MANDATORY_UNSATISFIED_ERROR":
                      case "MandatoryUnsatisfiedError":
                        console.info("无法发现指定的硬件设备。");
                        break;
                      default:
                        console.info(
                          "无法打开麦克风。异常信息:" +
                            (error.code || error.name)
                        );
                        break;
                    }
                  }
                );
              }
            });
          } else {
            let send = {
              imei: imei,
              type: "stopmute",
            };
            zfjly.send_cmd(send).then((res) => {
              isAudio = true;
            });
          }
        }
      });
    };
    document.getElementById("rwClose1").onclick = function () {
      console.log("关闭视频");
      window.parent.lay.closeIframeByNames(["videoTest"]);
      // let obj = {
      //   type: "closeIframe",
      //   name: "videoTest",
      // };
      // window.parent.postMessage(JSON.stringify(obj), "*");
    };

    window.addEventListener(
      "message",
      function (event) {
        //子获取父消息
        let newData;
        if (typeof event.data == "object") {
          newData = event.data;
        } else {
          newData = JSON.parse(event.data.argument);
        }
        if (newData.name === "openVideoTest") {
          window.openZFJLY();
          setTimeout(() => {
            window.getInfo(newData.videoCode);
          }, 2000);
        } else if (newData.name === "ddhjVideoTest") {
          window.openZFJLY();
          setTimeout(() => {
            window.getInfo(newData.videoCode); // 打开视频
            window.startAudio(newData.videoCode); // 打开音频
          }, 2000);
        }
      },
      false
    );
  </script>
</html>
<style></style>
