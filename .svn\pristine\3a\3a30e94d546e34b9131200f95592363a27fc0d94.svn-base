window.addEventListener("message", async function (e) {
    if (e.data.type === "doVideoMeeting") {
        console.log("监听到doVideoMeeting");
        console.log(e.data);
        dd.createVideoMeeting({
            title: "视频会议",
            calleeStaffIds: e.data.accountIds, //人员列表
        })
            .then((res) => {
                console.log("视频会议调用成功");
            })
            .catch((err) => {
                console.log("视频会议调用失败", err);
            });
    }
});
