<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>行政执法局-首页</title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/css/animate.css" />
  <link rel="stylesheet" href="/static/css/iconfont.css" />
  <link rel="stylesheet" href="/static/css/viewCss/xzzfzx_index.css" />
  <script src="/Vue/vue.js"></script>
  <script src="/jquery/jquery-3.6.1.min.js"></script>
  <script src="/static/js/jslib/layer.js"></script>
  <!-- <script src="/static/js/jslib/listener.js"></script> -->
  <script src="/static/js/jslib/Emiter.js"></script>
  <link rel="stylesheet" href="/elementui/css/elementui.css" />
  <script src="/elementui/js/elementui.js"></script>

  <!--    <script src="/static/citybrain/zhdd/zhdd_page/zfy/zfjly_sbj/json.js"></script>-->
<!--  <script src="/static/citybrain/zhdd/zhdd_page/zfy/zfjly_sbj/json.js"></script>-->
  <script src="/static/citybrain/zhdd/zhdd_page/zfy/zfjly_sbj/adapter.js"></script>
  <script src="/static/citybrain/zhdd/zhdd_page/zfy/zfjly_sbj/RecordRTC.js"></script>
  <script src="/static/citybrain/zhdd/zhdd_page/zfy/zfjly_sbj/xlsx.core.min.js"></script>
  <script src="/static/citybrain/zhdd/zhdd_page/zfy/zfjly_sbj/IdtConst.js"></script>
  <script src="/static/citybrain/zhdd/zhdd_page/zfy/zfjly_sbj/IdtApiAll.js"></script>
  <script src="/static/citybrain/zhdd/zhdd_page/zfy/zfjly_sbj/IdtTest.js"></script>

  <script type="text/javascript" src="/static/js/jslib/md5.js"></script>
  <script type="text/javascript" src="/static/citybrain/zhdd/zhdd_page/zfy/jkVideo/md5.js"></script>
  <script src="/static/citybrain/zhdd/zhdd_page/zfy/zfjly_lxs/poc_sdk.js"></script>
  <script src="/static/citybrain/zhdd/zhdd_page/zfy/zfjly_lxs/zfy_lxs.js"></script>
  <link rel="stylesheet" href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css" />

  <script src="/static/js/jslib/iframeResizer.contentWindow.min.js"></script>
  <script src="/static/js/jslib/ArcGisUtils/libs/three-r79.min.js"></script>
  <script src="/static/js/jslib/ArcGisUtils/libs/three-r116.min.js"></script>
  <script src="/static/js/jslib/arcgis-to-geojson.js"></script>
  <script src="/static/js/jslib/biz.min.js"></script>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/login.js"></script>
  <script src="/static/js/jslib/turf.js"></script>

  <script src="/static/js/jslib/http.interceptor.js"></script>
  <!-- api -->
  <script src="https://g.alicdn.com/gdt/jsapi/1.9.22/index.js"></script>
  <!-- 视频 -->
  <script src="/static/js/jslib/meeting.js"></script>
  <script src="/static/js/jslib/DHWs_tc.js"></script>

  <style>

      .el-message-box__title {
          font-size: 38px;
          background: linear-gradient(to bottom, #6ca9fc, #ffffff);
          -webkit-background-clip: text;
          color: transparent;
          font-weight: 700;
          font-style: italic;
      }
      .el-message-box__message p {
          font-size: 1.5rem;
          line-height: 100px;
      }
      .el-message-box__status {
          font-size: 1.5rem !important;
      }
      .el-message-box__content {
          color: #fff;
      }
      .el-message-box {
          width: 650px;
          box-shadow: inset 0px 0px 16px 0px rgba(0, 145, 255, 1);
          background-color: #021037;
          border-radius: 21px;
          border: 1px solid#155181;
      }
      .el-message-box__headerbtn {
          font-size: 30px;
      }
      .el-message-box__headerbtn .el-message-box__close {
          color: #fff;
      }
      .el-message-box__headerbtn {
          font-size: 38px !important;
      }

      .el-button--small {
          font-size: 28px;
          padding: 10px 33px;
          border-radius: 14px;
      }

    .esri-view-surface {
      height: 2160px !important;
    }

    [v-cloak] {
      display: none;
    }

    @media only screen and (max-width: 1920px) {
      #app {
        transform: scale(0.5) translate(-50%, -50%);
      }
      .el-popper[x-placement^='bottom'] {
        transform: scale(0.5) translate(-50%, 0%);
      }
    }
    html,
    body {
      width: 100%;
      height: 100%;
      font: 20px "微软雅黑", Arial !important;
      user-select: none;
    }
  </style>
</head>

<body>
  <div id="app" v-cloak>
    <!-- 头部 -->
    <header class="header_img">
      <div class="city_select">
        <el-select v-model="active_cityName" placeholder="县市区" @change="cityFun">
          <el-option v-for="(item,i) in cityList" :label="item.name" :value="item.name"></el-option>
        </el-select>
      </div>
      <div id="header_weather"></div>
      <div class="header_title_text" style="cursor: pointer" onclick="backHome()">
        <span class="header_text">{{header_title}}</span>
      </div>
      <el-select v-model="currentYear" style="width: 300px;position:relative;left: calc(50% + 900px);top: 15px;z-index: 99" v-show="currentPage2 != '指挥调度' && currentPage2 != '县级中心' && currentPage2 != '应用集成'">
        <el-option v-for="(item,i) in yearList" :label="item.name" :value="item.value"></el-option>
      </el-select>
      <div id="header_week"></div>
    </header>
    <!-- 地图 -->
<!--    <div class="map_center" id="viewDiv"></div>-->
    <iframe
        class="index_frame animated"
        frameborder="0"
        name="map"
        scrolling="no"
        src="/static/citybrain/home/<USER>/map.html"
        style="position: absolute; z-index: 100;"
        width="100%"
        height="100%"
    ></iframe>
    <!-- 公用地图导航 -->
    <iframe v-show="page_menu!='zfts'" class="index_frame animated" frameborder="0" height="200px"
      name="IndexTcglBtn3840" scrolling="no" src="/static/citybrain/tcgl/tcbtn.html#1080"
      style="position: absolute; z-index: 100; left: 1080px; top: calc(50% - 848px)" width="100px"></iframe>
    <iframe v-show="page_menu!='zfts'" class="index_frame animated" frameborder="0" height="230px"
      name="IndexVideoBtnall3840" scrolling="no" src="/static/citybrain/tcgl/IndexVideoBtnall.html#1080"
      style="position: absolute; z-index: 100; left: 1080px; top: calc(50% - 639px)" width="100px"></iframe>
    <iframe v-show="page_menu!='zfts'" class="index_frame animated" frameborder="0" height="260px"
      name="IndexWgBtnall3840" scrolling="no" src="/static/citybrain/tcgl/wgbtn.html#1080"
      style="position: absolute; z-index: 100; left: 1080px; top: calc(50% - 370px)" width="100px"></iframe>
    <iframe v-show="page_menu!='zfts'" class="index_main_mapIcon animated" frameborder="0" height="660px"
      name="indexMapIcon3840" scrolling="no" src="/static/citybrain/tcgl/main_mapIcon.html#2656"
      style="position: absolute; z-index: 100; right: 1030px; top: calc(50% - 848px)" width="140px"></iframe>
    <!-- 展示内容盒子 -->
    <div id="Pages"></div>
    <div id="zindex_bg"></div>
    <!-- <div class="back_home" title="返回首页" onclick="backHome()"></div> -->
    <!-- 收起和展开的按钮 -->
    <div class="close_left_right_iframe_btn" onclick="lrFrameSHClick('xzzf')"></div>
    <div class="open_left_iframe_btn" style="display: none; cursor: pointer" onclick="lframeShow('xzzf')">
      <div></div>
    </div>
    <div class="open_right_iframe_btn" style="display: none; cursor: pointer" onclick="rframeShow('xzzf')">
      <div></div>
    </div>
  </div>

  <script src="/static/js/jslib/hideAndShow.js"></script>
  <script defer src="/static/js/xzzfzx_index.js"></script>
  <script>
    let citylist = [
      {
        name: "婺城区",
        destination: [119.51328272762952, 28.905759961785844],
        zoom: 10.7,
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/wuchengqu/MapServer",
        id: "e242d35fdc8748b1aa12774c3d08ea04",
        urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/wucheng_szz_1/FeatureServer",
        idv: "7772867eaa16410780c68af71baa70e4",
      },
      {
        name: "金东区",
        destination: [119.79687141288449, 29.102065894175603],
        zoom: 11.3,
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/jinyixinqu/MapServer",
        id: "204cc0ef48a4488682f19e09b7790fca",
        urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/jinyi_szz_1/FeatureServer",
        idv: "bcc7fd7d72b745aa9b443ed9e08a6abc"
      },
      {
        name: "开发区",
        destination: [119.49024650108194, 29.0043553135286],
        zoom: 11.5,
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/kaifaqu/MapServer",
        id: "92decdc702c8491fa8e41fa07ccebee9",
        urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/kaifa_szz_1/FeatureServer",
        idv: "4df9f2133484421880de32eb1e6effad"
      },
      {
        name: "金华开发区",
        destination: [119.49024650108194, 29.0043553135286],
        zoom: 11.5,
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/kaifaqu/MapServer",
        id: "92decdc702c8491fa8e41fa07ccebee9",
        urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/kaifa_szz_1/FeatureServer",
        idv: "4df9f2133484421880de32eb1e6effad"
      },
      {
        name: "兰溪市",
        destination: [119.55667006174536, 29.20872154955791],
        zoom: 10.7,
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/lanxishi/MapServer",
        id: "3866fa38ae304c8e9c7fa7e5baa429f1",
        urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/lanxi_szz_1/FeatureServer",
        idv: "2f8e8266fcb9445da32e629ddda0f88b"
      },
      {
        name: "浦江县",
        destination: [119.89688053673072, 29.471222523091836],
        zoom: 11.2,
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/pujiangxian/MapServer",
        id: "128af7c9d45e4813ba31b1d767aa6286",
        urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/pujiang_szz_1/FeatureServer",
        idv: "aaa1adcc079a433a8b9bff1f8ade3e6a"
      },
      {
        name: "义乌市",
        destination: [120.06448988478343, 29.223500274752382],
        zoom: 10.65,
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/yiwushi/MapServer",
        id: "ff477c9986b442b98141d58d673053a3",
        urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/yiwu_szz_1/FeatureServer",
        idv: "4776c51c14b44cf2b26dd2707e5f1b39"
      },
      {
        name: "东阳市",
        destination: [120.38385520588402, 29.201613535927276],
        zoom: 10.667,
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/dongyangshi/MapServer",
        id: "69f0b9c4267e4419ae99bcffcfa9033c",
        urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/dongyang_szz_1/FeatureServer",
        idv: "59d462413a2c4321bec17ddfa8cdddc6"
      },
      {
        name: "磐安县",
        destination: [120.53881217398691, 28.996132529782322],
        zoom: 10.75,
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/pananxian/MapServer",
        id: "8c3e0e4d222f4a51a9429d5fb3175f1f",
        urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/panan_szz_1/FeatureServer",
        idv: "93975ebae4e84a32b4bb1e01e947f5dd"
      },
      {
        name: "永康市",
        destination: [120.1242150759098, 28.88507934117054],
        zoom: 11.2,
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/yongkangshi/MapServer",
        id: "0a1db3cf96854170b63e9a0c1e6b9ee4",
        urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/yongkang_szz_1/FeatureServer",
        idv: "d227a0eb749148b3b31c7024f5629bb9"
      },
      {
        name: "武义县",
        destination: [119.6912335854188, 28.707478067993392],
        zoom: 10.65,
        url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/wuyixian/MapServer",
        id: "0542c20e062344dd875900db1593a41d",
        urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/wuyi_szz_1/FeatureServer",
        idv: "96c51787c1b24f5c878321f02e6ea19e"
      },
      {
        name: "金华市",
        destination: [119.96596514308335, 29.03677381816546],
        zoom: 9.5,
        urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/jinhua_szz/FeatureServer",
        idv: "079d63b2cc604baf95e1d8dd80d3cb7a"
      }
    ];
      // 监听esc键并将一键调度弹框复位
    window.addEventListener("message", function(e) {
      console.log(e,"eeeee");
      if (e.data=="keydown"){
        document.getElementById("zhddDispatch").style.top = "380px";
      }
    })

    var pageCache = [
      (topWin) => {
        console.log("bak home---------------------");
        topWin.mapUtil.plotTool.close(); //关闭画线closeDraw
        localStorage.getItem("adminCity")=="金华市"?topWin.mapUtil.removeAllLayers(): window.parent.frames['map'].ArcGisUtils.removeExtrudeLayer(window.parent.frames['map'].view);
        topWin.lay.closeIframeByKeepNames([]);
        topWin.fadeIfr.run(["leftIn", "rightIn", "backBtnHide"]);
        topWin.xzzfzx.setHomeTitle();
        topWin.xzzfzx.openDefaultPage();
        citylist.forEach((item)=> {
          if (item.name == localStorage.getItem('city')) {
            topWin.mapUtil.flyTo(item)
          }
        })
      },
    ];
    function backHome() {
      top.xzzfzx.getCityList()
      let merge = window.frames['map']?window.frames['map'].view.map.findLayerById("VECTOR_POI_Merge"):window.parent.frames['map'].view.map.findLayerById("VECTOR_POI_Merge")
      if (merge) merge.visible = true
      let dem = window.frames['map']?window.frames['map'].view.map.findLayerById("JINHUA_DEM"):window.parent.frames['map'].view.map.findLayerById("JINHUA_DEM")
      if (dem) dem.visible = true
      let label = window.frames['map']?window.frames['map'].view.map.findLayerById("VECTOR_Road_Label"):window.parent.frames['map'].view.map.findLayerById("VECTOR_Road_Label")
      if (label) label.visible = true
      let vector = window.frames['map']?window.frames['map'].view.map.findLayerById("VECTOR"):window.parent.frames['map'].view.map.findLayerById("VECTOR")
      if (vector) vector.visible = true
      window.frames['map']?window.frames['map'].mapUtil.removeLayer("map_text"):window.parent.frames['map'].mapUtil.removeLayer("map_text")
      window.frames['map']?window.frames['map'].mapUtil.removeLayer("bankuai"):window.parent.frames['map'].mapUtil.removeLayer("bankuai")
      window.frames['map']?window.frames['map'].view.map.remove(window.frames['map'].xingzhenlayer):window.parent.frames['map'].view.map.remove(window.parent.frames['map'].xingzhenlayer)
      window.frames['map']?window.frames['map'].xingzhen("金华市"):window.parent.frames['map'].xingzhen("金华市")
      //  由于执法态势页面没有mask，所以在backhome时要加载当前县市区的mask
      window.frames['map'].mapMask(localStorage.getItem("city"),[8, 40, 73, 0.9]);
      if (window.frames["left"] === undefined) {
        eventbus.emit("backHome");
        xzzfzx.page_menu = "home";
        if (pageCache.length > 1) {
          const page = pageCache.pop();
          page(window);
        } else if (pageCache.length === 1) {
          pageCache[0](window);
        }
      }
    }
    setTimeout(() => {
      mainObj.queryAuth(); //获取兰溪市,义乌市执法仪获取数据
    }, 7000)
    window.DHWsInstance = DHWs.getInstance({
      reConnectCount: 2,
      connectionTimeout: 30 * 1000,
      messageEvents: {
        loginState() { },
      },
    });
    DHWsInstance.detectConnectQt().then((res) => {
      if (res) {
        // 连接客户端成功
        DHWsInstance.login({
          loginIp: "*************",
          // loginPort: "6443",
          loginPort: "7901",
          userName: "csdn",
          userPwd: "Jhcsdn2024$",
          token: "",
          https: 0,
        });
        // this.$Message.info('登录中...')
        console.log("登录中...");
        DHWsInstance.on("loginState", (res) => {
          if (res) {
            // this.$Message.success('登录成功')
            console.log("登录成功");
            // this.activePanel = "key2";
          } else {
            // this.$Message.info('登录失败')
            console.log("登录失败");
          }
        });
      }
    });
  </script>
</body>

</html>
