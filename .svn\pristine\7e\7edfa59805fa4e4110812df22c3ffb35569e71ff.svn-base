<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>治理协同弹窗</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <link rel="stylesheet" href="/static/css/viewCss/commonObjzhdd.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/Vue/vue-count-to.min.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/static/js/jslib/Emiter.js"></script>
    <script src="/elementui/js/index.js"></script>
    <style>

        ::-webkit-scrollbar {
             display: none;
         }


      ul,
      ul li {
        list-style: none;
      }

      .rwgz-tc {
        width: 1015px;
        height: 965px;
        background: url("/static/images/zhdd/dialogBg.png") no-repeat;
        background-size: 100% 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: column;
      }

      .rw-title {
        padding: 46px 3% 0;
        width: 95%;
        height: 60px;
        line-height: 60px;
      }

      .close {
        background: url("/static/images/zhdd/close.png") no-repeat;
        width: 34px;
        height: 34px;
      }

      .ql-indent-1 img {
        width: 100%;
      }

      img {
        width: 100%;
      }

      .content {
          height: 850px;
          margin-top: 10px;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
          flex-direction: column;
      }

      ::-webkit-scrollbar {
          width: 0;
          height: 0;
      }

      .scrollTab {
          width:928px;
          height:70px;
      }

      .table-container {
          height: 642px;
          overflow-y: scroll;
      }

      .activeContainer {
          height: 722px;
      }

        .tableContainer2 {
            height: 400px;
            overflow-y: scroll;
        }

      .table-line {
          width: 928px;
          height: 80px;
          display: flex;
          justify-content: space-evenly;
          align-items: center;
          background: rgba(50,134,248,0.15);
      }

      .title-line {
          background: transparent !important;
      }

      .table-title {
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: bold;
          color: #FFFFFF;
          text-align: left;
      }

      .table-column {
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #FFFFFF;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
      }
      .activeTableLine {
          background: rgba(50,134,248,0.25);
      }

        .el-input {
            /*position: absolute;*/
            /*top: 229px;*/
            /*left: 1630px;*/
            width: 537px;
            height: 72px;
        }

        .el-input__icon {
            height: 100%;
            width: 25px;
            text-align: center;
            -webkit-transition: all .3s;
            transition: all .3s;
            line-height: 75px;
        }

        .el-scrollbar {
            overflow: hidden;
            /*position: relative;*/
            height: 500px;
            background: #020b28;
            border-radius: 10px;
        }

        .el-autocomplete-suggestion__wrap {
            max-height: unset !important;
            padding: 10px 0;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
        }

        .el-input--suffix .el-input__inner {
            border: 1px solid #359CF8;
            border-radius: 8px;
            padding-right: 30px;
            height: 70px;
            font-family: MicrosoftYaHei;
            font-size: 28px;
            font-weight: normal;
            font-stretch: normal;
            letter-spacing: 1px;
            color: #bbe5fd !important;
            background: #020b28;
        }

        .el-input.is-active .el-input__inner,
        .el-input__inner:focus {
            border: 1px solid #bbe5fd;
            outline: 0;
        }

        .el-input__suffix-inner {
            pointer-events: all;
            font-size: 28px;
            margin: 15px 20px 0 0;
            color: #bbe5fd !important;
        }

        .el-autocomplete-suggestion li {
            padding: 0 20px;
            line-height: 34px;
            cursor: pointer;
            color: #bbe5fd;
            font-size: 28px;
            list-style: none;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin: 10px 0 25px 0;
        }

        .el-autocomplete-suggestion li:hover {
            background: unset !important;
        }

        .search {
            width: 100px;
            height: 65px;
            line-height: 65px;
            text-align: center;
            background: #0A619E;
            border: 1px solid #359CF8;
            border-radius: 8px;
            font-size: 28px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FEFEFE;
            margin-left: 10px;
            cursor: pointer;
        }

        .tab-con {
            width: 351px;
            height: 70px;
            line-height: 70px;
            color: #fff;
            font-size: 32px;
        }

        .s-flex {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-evenly;
        }

        .tab-item {
            font-size: 36px;
            font-family: Source Han Sans CN;
            font-weight: 500;
            font-style: italic;
            color: rgba(171, 206, 239, 0.7);
            line-height: 59px;
        }

        .tabConActive {
            background: url('/static/images/zfts/tab-active.png') no-repeat;
            background-size: 110% 100%;
            font-family: Source Han Sans CN;
            font-weight: bold;
            font-size: 36px;
            color: #ffffff;
        }
    </style>
  </head>

  <body>
    <div id="zlxt" class="rwgz-tc">
      <div class="rw-title flex-between">
        <div class="fs-44 text-mid-yellow" id="rwTitle" style="margin: 25px 0 0 20px;">{{name}}</div>
        <div class="close cursor" @click="close" style="margin-right: 20px;"></div>
      </div>
      <div class="content">
        <div style="width: 100%;display: flex;justify-content: center;align-items: center">
          <div class="tab-con s-flex" v-show="activeValue != '部门'">
            <span
              class="tab-item"
              v-for="(item,i) in tabList"
              :class="{tabConActive:btnActive==i}"
              @click="changeTab(i)"
            >{{item.name}}
            </span>
          </div>
        </div>
        <div class="table">
          <div class="table-line title-line">
            <div class="table-column table-title" style="flex: 2;margin-left: 30px">序号</div>
            <div class="table-column table-title" style="flex: 4" v-show="activeValue == '市直部门' || city != '金华市'">部门</div>
            <div class="table-column table-title" style="flex: 4" v-show="activeValue == '区域'">区域</div>
            <div class="table-column table-title" style="flex: 3;margin-left: 50px" v-if="name != '罚没金额'">数量(件)</div>
            <div class="table-column table-title" style="flex: 3;margin-left: 50px" v-else>数量(万元)</div>
          </div>
          <div class="table-container" :class="{activeContainer:activeValue == '部门'}">
            <div class="table-line" v-for="(item,i) in tableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
              <div class="table-column" style="flex: 2;margin-left: 30px">{{i + 1}}</div>
              <div class="table-column" style="flex: 4" :title="item.name" >{{item.name}}</div>
              <div class="table-column" style="flex: 3;margin-left: 50px" v-if="name != '罚没金额'">{{item.num}}</div>
              <div class="table-column" style="flex: 3;margin-left: 50px" v-else>{{Number(item.num).toFixed(2)}}</div>
            </div>
            <div class="table-line">
              <div class="table-column" style="flex: 2;margin-left: 30px"></div>
              <div class="table-column" style="flex: 4;">合计</div>
              <div class="table-column" style="flex: 3;margin-left: 30px">{{count}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      let vm = new Vue({
        el: "#zlxt",
        data: {
          count: 0,
          name:"",
          tableData: [],
          btnActive: 0,
          btnList: [{name:"市直部门",type:"金华市"}, {name:"区域",type:"金华市"},{name:"部门",type:""}],
        },
        computed:{
          city() {
            return localStorage.getItem("city")
          },
          tabList() {
            return this.city == '金华市'?this.btnList.filter(item => item.type == '金华市'):this.btnList.filter(item => item.type != '金华市')
          },
          activeValue() {
            return this.tabList[this.btnActive].name
          },
        },
        mounted() {
          const that = this;
          window.addEventListener("message", function (event) {
            //子获取父消息
            let newData;
            if (typeof event.data == "object") {
              newData = event.data;
            } else {
              newData = JSON.parse(event.data.argument);
            }
            that.name = newData.type;
            that.getDetail(newData.type,localStorage.getItem("year"));
          });
          window.parent.eventbus &&
          window.parent.eventbus.on("yearChange", (year) => {
            this.getDetail(that.name,year);
          });
        },
        methods: {
          changeTab(i) {
            this.btnActive = i;
            this.getDetail(this.name,localStorage.getItem("year"))
          },
          //获取数据
          getDetail(name,year) {
            switch (name) {
              case "案件总数":
                this.getAjzs(year);
                break;
              case "简易程序案件数":
                this.getJyajs(year);
                break;
              case "重大案件数":
                this.getZdajs(year);
                break;
              case "罚没金额":
                this.getFmje(year);
                break;
              case "普通程序案件数":
                this.getPtcxajs(year);
                break;
            }
          },
          getAjzs(year) {
            $api("/csdn_yjyp61",{qxwd:this.city == "金华开发区"?"开发区":this.city,ywwd1:"",ywwd2:localStorage.getItem("city") == "金华市"?this.activeValue == "区域"?"地区":this.activeValue:"",sjwd2:year}).then(res => {
              this.tableData = res.map(item => {return {
                name: item.ywwd1,
                num: item.tjz,
              }})
              this.count = (res.reduce((a,b) => a + Number(b.tjz),0)).toFixed(2)
            })
          },
          getJyajs(year) {
            $api("/csdn_yjyp62",{qxwd:this.city == "金华开发区"?"开发区":this.city,ywwd1:"",ywwd2:localStorage.getItem("city") == "金华市"?this.activeValue == "区域"?"地区":this.activeValue:"",sjwd2:year}).then(res => {
              this.tableData = res.map(item => {return {
                name: item.ywwd1,
                num: item.tjz,
              }})
              this.count = (res.reduce((a,b) => a + Number(b.tjz),0)).toFixed(2)
            })
          },
          getZdajs(year) {
            $api("/csdn_yjyp63",{qxwd:this.city == "金华开发区"?"开发区":this.city,ywwd1:"",ywwd2:localStorage.getItem("city") == "金华市"?this.activeValue == "区域"?"地区":this.activeValue:"",sjwd2:year}).then(res => {
              this.tableData = res.map(item => {return {
                name: item.ywwd1,
                num: item.tjz,
              }})
              this.count = (res.reduce((a,b) => a + Number(b.tjz),0)).toFixed(2)
            })
          },
          getFmje(year) {
            $api("/csdn_yjyp64",{qxwd:this.city == "金华开发区"?"开发区":this.city,ywwd1:"",ywwd2:localStorage.getItem("city") == "金华市"?this.activeValue == "区域"?"地区":this.activeValue:"",sjwd2:year}).then(res => {
              this.tableData = res.map(item => {return {
                name: item.ywwd1,
                num: item.tjz,
              }})
              this.count = (res.reduce((a,b) => a + Number(b.tjz),0)).toFixed(2)
            })
          },
          getPtcxajs(year) {
            $api("/csdn_yjyp65",{qxwd:this.city == "金华开发区"?"开发区":this.city,ywwd1:"",ywwd2:localStorage.getItem("city") == "金华市"?this.activeValue == "区域"?"地区":this.activeValue:"",sjwd2:year}).then(res => {
              this.tableData = res.map(item => {return {
                name: item.ywwd1,
                num: item.tjz,
              }})
              this.count = (res.reduce((a,b) => a + Number(b.tjz),0)).toFixed(2)
            })
          },
          close() {
            window.parent.lay.closeIframeByNames(["AnjianDialog"]);
          }
        },
      });
    </script>
  </body>
</html>
