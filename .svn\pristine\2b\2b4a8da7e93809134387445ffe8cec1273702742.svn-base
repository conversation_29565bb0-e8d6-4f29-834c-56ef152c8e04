<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
  <title>加载气象图层</title>


  <link rel="stylesheet" href="https://dev.arcgisonline.cn/jsapi/4.24/esri/themes/light/main.css" />
  <script src="./index.js" type="module"> </script>

  <style>
    html,
    body,
    #viewDiv {
      padding: 0;
      margin: 0;
      height: 100%;
      width: 100%;
    }

    .tools {
      position: absolute;
      top: 20px;
      right: 20px;
      background-color: white;
      border-radius: 5px;
      padding: 20px;
    }
  </style>

</head>

<body>
  <div id="viewDiv"></div>
  <div class="tools">
    <div class="item">
      <p>湿度：</p>
      <button onclick="ArcGisUtils.loadWeatherLayer('humidity',1)">未来一小时</button>
      <button onclick="ArcGisUtils.loadWeatherLayer('humidity',3)">未来3小时</button>
      <button onclick="ArcGisUtils.loadWeatherLayer('humidity',6)">未来6小时</button>
      <button onclick="ArcGisUtils.loadWeatherLayer('humidity',12)">未来12小时</button>
      <button onclick="ArcGisUtils.removeWeatherLayer('humidity',1)">未来一小时(移除)</button>
    </div>
    <div class="item">
      <p>温度：</p>
      <button onclick="ArcGisUtils.loadWeatherLayer('temperature',1)">未来一小时</button>
      <button onclick="ArcGisUtils.loadWeatherLayer('temperature',3)">未来3小时</button>
      <button onclick="ArcGisUtils.loadWeatherLayer('temperature',6)">未来6小时</button>
      <button onclick="ArcGisUtils.loadWeatherLayer('temperature',12)">未来12小时</button>
    </div>
    <div class="item">
      <p>云量：</p>
      <button onclick="ArcGisUtils.loadWeatherLayer('clound',1)">未来一小时</button>
      <button onclick="ArcGisUtils.loadWeatherLayer('clound',3)">未来3小时</button>
      <button onclick="ArcGisUtils.loadWeatherLayer('clound',6)">未来6小时</button>
      <button onclick="ArcGisUtils.loadWeatherLayer('clound',12)">未来12小时</button>
    </div>
    <div class="item">
      <p>风速：</p>
      <button onclick="ArcGisUtils.loadWeatherLayer('wind',1)">未来一小时</button>
      <button onclick="ArcGisUtils.loadWeatherLayer('wind',3)">未来3小时</button>
      <button onclick="ArcGisUtils.loadWeatherLayer('wind',6)">未来6小时</button>
      <button onclick="ArcGisUtils.loadWeatherLayer('wind',12)">未来12小时</button>
    </div>
  </div>
</body>

<script>
  function loadWeatherLayer() {
    ArcGisUtils.loadWeatherLayer('humidity', 3)
  }
</script>

</html>