<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>图层按钮</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
      }
      #tcgl_app {
        width: 100%;
        height: 100%;
      }
      .btn-active {
        position: relative;
        color: #ffc460 !important;
        background-image: url("/static/images/home/<USER>") !important;
        background-size: 100% 100%;
      }
      .btn {
        width: 54px;
        height: 180px;
        background-image: url("/static/images/home/<USER>");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        font-size: 32px;
        color: white;
        line-height: 42px;
        text-align: center;
        cursor: pointer;
      }
      .btn p {
        padding-top: 30px;
      }
      .btn img {
        width: 28px;
        height: 29px;
        position: relative;
      }
    </style>
  </head>
  <body>
    <div id="tcgl_app" @click="cli">
      <div v-show="!show" class="btn">
        <p>图层</p>
        <img src="/static/images/home/<USER>" alt="" />
      </div>
      <div v-show="show">
        <div class="btn-active btn">
          <p>图层</p>
          <img
            src="/static/images/home/<USER>"
            alt=""
          />
        </div>
      </div>
    </div>
    <script>
      let vm = new Vue({
        el: "#tcgl_app",
        data: { show: false },
        mounted() {},
        methods: {
          cli() {
            if (
              !!window.parent.frames["videoManage"] ||
              !!window.parent.frames["wggl3840"]
            )
              return;
            this.show = !this.show;
            if (this.show) {
              // this.hideIframe();
              this.open();
            } else {
              this.close();
            }
          },
          // 收缩两边
          hideIframe() {
            let lefthideCss =
              window.parent.document.getElementsByClassName("page_left")[0];
            if (
              lefthideCss != undefined &&
              lefthideCss.className.indexOf("fadeInLeft") != -1
            ) {
              window.parent.lrFrameSHClick
                ? window.parent.lrFrameSHClick()
                : "";
            }
          },
          open() {
            this.show = true;
            let left = "120px";
            // 判断左右页面是否是收起和展开的状态
            if (
              window.parent.document
                .getElementsByClassName("index_frame")[0]
                .getAttribute("class")
                .indexOf("btns_move") > -1
            ) {
              left = "120px";
            } else {
              left = "1160px";
            }
            window.parent.lay.openIframe({
              type: "openIframe",
              name: "tckz_tcgl3840",
              src: baseURL.url + "/static/citybrain/tcgl/tcgl.html",
              width: "400px",
              height: "500px",
              left: left,
              top: "208px",
              zIndex: 666,
            });
          },
          close() {
            window.parent.frames["tckz_tcgl3840"].postMessage(
              { tc_clear: "清除tc" },
              "*"
            );
            try {
            } catch (error) {}
            window.parent.lay.closeIframeByNames(["tckz_tcgl3840"]);
            window.parent.lay.closeIframeByNames(["yybzDetail"]);
          },
        },
      });
      window.parent.eventbus &&
        window.parent.eventbus.on("backHome", (right) => {
          vm.show = false;
        });
      window.parent.eventbus &&
        window.parent.eventbus.on("leftIframeShow", (right) => {
          let rightVideo = Number(right) + 250;
          try {
            parent.document.getElementById("tckz_tcgl3840").style.left = "auto";
            parent.document.getElementById("tckz_tcgl3840").style.right =
              rightVideo + "px";
          } catch (error) {}
        });
      window.parent.eventbus &&
        window.parent.eventbus.on("leftIframeHide", (right) => {
          let rightVideo = Number(right) + 250;
          try {
            parent.document.getElementById("tckz_tcgl3840").style.left = "auto";
            parent.document.getElementById("tckz_tcgl3840").style.right =
              rightVideo + "px";
          } catch (error) {}
        });
    </script>
  </body>
</html>
