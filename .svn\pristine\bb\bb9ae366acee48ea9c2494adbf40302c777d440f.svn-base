<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>执法设备弹窗</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <link rel="stylesheet" href="/static/css/viewCss/commonObjzhdd.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/Vue/vue-count-to.min.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/static/js/jslib/Emiter.js"></script>
    <script src="/elementui/js/index.js"></script>
    <style>

        ::-webkit-scrollbar {
             display: none;
         }


      ul,
      ul li {
        list-style: none;
      }

      .rwgz-tc {
        width: 1387px;
        height: 726px;
        background: url("/static/images/zhdd/dialogBg.png") no-repeat;
        background-size: 100% 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: column;
      }

      .rw-title {
        padding: 46px 3% 0;
        width: 95%;
        height: 60px;
        line-height: 60px;
      }

      .close {
        background: url("/static/images/zhdd/close.png") no-repeat;
        width: 34px;
        height: 34px;
      }

      .ql-indent-1 img {
        width: 100%;
      }

      img {
        width: 100%;
      }

      .content {
          height: 850px;
          margin-top: 10px;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
          flex-direction: column;
      }

      ::-webkit-scrollbar {
          width: 0;
          height: 0;
      }

      .scrollTab {
          width:928px;
          height:70px;
      }

      .table-container {
          height: 482px;
          overflow-y: scroll;
      }

        .tableContainer2 {
            height: 400px;
            overflow-y: scroll;
        }

      .table-line {
          width: 1296px;
          height: 80px;
          display: flex;
          justify-content: space-evenly;
          align-items: center;
          background: rgba(50,134,248,0.15);
      }

      .title-line {
          background: transparent !important;
      }

      .table-title {
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: bold;
          color: #FFFFFF;
          text-align: left;
      }

      .table-column {
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #FFFFFF;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
      }
      .activeTableLine {
          background: rgba(50,134,248,0.25);
      }

        .el-input {
            /*position: absolute;*/
            /*top: 229px;*/
            /*left: 1630px;*/
            width: 537px;
            height: 72px;
        }

        .el-input__icon {
            height: 100%;
            width: 25px;
            text-align: center;
            -webkit-transition: all .3s;
            transition: all .3s;
            line-height: 75px;
        }

        .el-scrollbar {
            overflow: hidden;
            /*position: relative;*/
            height: 500px;
            background: #020b28;
            border-radius: 10px;
        }

        .el-autocomplete-suggestion__wrap {
            max-height: unset !important;
            padding: 10px 0;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
        }

        .el-input--suffix .el-input__inner {
            border: 1px solid #359CF8;
            border-radius: 8px;
            padding-right: 30px;
            height: 70px;
            font-family: MicrosoftYaHei;
            font-size: 28px;
            font-weight: normal;
            font-stretch: normal;
            letter-spacing: 1px;
            color: #bbe5fd !important;
            background: #020b28;
        }

        .el-input.is-active .el-input__inner,
        .el-input__inner:focus {
            border: 1px solid #bbe5fd;
            outline: 0;
        }

        .el-input__suffix-inner {
            pointer-events: all;
            font-size: 28px;
            margin: 15px 20px 0 0;
            color: #bbe5fd !important;
        }

        .el-autocomplete-suggestion li {
            padding: 0 20px;
            line-height: 34px;
            cursor: pointer;
            color: #bbe5fd;
            font-size: 28px;
            list-style: none;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin: 10px 0 25px 0;
        }

        .el-autocomplete-suggestion li:hover {
            background: unset !important;
        }

        .search {
            width: 100px;
            height: 65px;
            line-height: 65px;
            text-align: center;
            background: #0A619E;
            border: 1px solid #359CF8;
            border-radius: 8px;
            font-size: 28px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FEFEFE;
            margin-left: 10px;
            cursor: pointer;
        }
    </style>
  </head>

  <body>
    <div id="zlxt" class="rwgz-tc">
      <div class="rw-title flex-between">
        <div class="fs-44 text-mid-yellow" id="rwTitle" style="margin-left: 20px;">{{name}}</div>
        <div class="close cursor" @click="close" style="margin-right: 20px;"></div>
      </div>
      <div class="content">
        <div class="table">
          <div v-show="['机动车辆','非机动车辆','汽车','摩托车','四轮电瓶车','两轮电瓶车'].indexOf(name) != -1">
            <div class="table-line title-line" >
              <div class="table-column table-title" style="flex: 1;margin-left: 30px">序号</div>
              <div class="table-column table-title" style="flex: 2;">区域</div>
              <div class="table-column table-title" style="flex: 4">部门</div>
              <div class="table-column table-title" style="flex: 2">车辆类型</div>
              <div class="table-column table-title" style="flex: 2">车牌号</div>
            </div>
            <div class="table-container">
              <div class="table-line" v-for="(item,i) in cartableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
                <div class="table-column" style="flex: 1;margin-left: 30px">{{i + 1}}</div>
                <div class="table-column" style="flex: 2">{{item.area}}</div>
                <div class="table-column" style="flex: 4" :title="item.depart">{{item.depart}}</div>
                <div class="table-column" style="flex: 2">{{item.carType}}</div>
                <div class="table-column" style="flex: 2">{{item.carNumber}}</div>
              </div>
            </div>
          </div>

          <div v-show="['机动车辆','非机动车辆','汽车','摩托车','四轮电瓶车','两轮电瓶车','执法记录仪'].indexOf(name) == -1">
            <div class="table-line title-line" >
              <div class="table-column table-title" style="flex: 1;margin-left: 30px">序号</div>
              <div class="table-column table-title" style="flex: 2">区域</div>
              <div class="table-column table-title" style="flex: 4">部门</div>
              <div class="table-column table-title" style="flex: 3">品牌</div>
              <div class="table-column table-title" style="flex: 3">型号</div>
              <div class="table-column table-title" style="flex: 2">数量</div>
            </div>
            <div class="table-container">
              <div class="table-line" v-for="(item,i) in othertableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
                <div class="table-column" style="flex: 1;margin-left: 30px">{{i + 1}}</div>
                <div class="table-column" style="flex: 2">{{item.area}}</div>
                <div class="table-column" style="flex: 4" :title="item.depart">{{item.depart}}</div>
                <div class="table-column" style="flex: 3">{{item.pp}}</div>
                <div class="table-column" style="flex: 3" :title="item.xh">{{item.xh}}</div>
                <div class="table-column" style="flex: 2">{{item.num}}</div>
              </div>
            </div>
          </div>

          <div v-show="name == '执法记录仪'">
            <div class="table-line title-line" >
              <div class="table-column table-title" style="flex: 1;margin-left: 30px">序号</div>
              <div class="table-column table-title" style="flex: 2">区域</div>
              <div class="table-column table-title" style="flex: 4">部门</div>
              <div class="table-column table-title" style="flex: 3;margin-left: 30px;">设备人员</div>
              <div class="table-column table-title" style="flex: 2">状态</div>
              <div class="table-column table-title" style="flex: 2"></div>
            </div>
            <div class="table-container">
              <div class="table-line" v-for="(item,i) in jlytableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
                <div class="table-column" style="flex: 1;margin-left: 30px">{{i + 1}}</div>
                <div class="table-column" style="flex: 2">{{item.area}}</div>
                <div class="table-column" style="flex: 4" :title="item.depart">{{item.depart}}</div>
                <div class="table-column" style="flex: 3;margin-left: 30px;">{{item.name}}</div>
                <div class="table-column" style="flex: 2">{{item.status == "0"?"离线":"在线"}}</div>
                <div class="table-column" style="flex: 2;cursor: pointer" v-if="item.status != 0" @click="openVideo(item)">查看直播</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      let vm = new Vue({
        el: "#zlxt",
        data: {
          name:"",
          cartableData: [],
          othertableData: [],
          jlytableData: [],
          city: localStorage.getItem("city")
        },
        mounted() {
          const that = this;
          //监测县市区的切换
          window.parent.eventbus &&
          window.parent.eventbus.on("cityChange", (city) => {
            let filtName =
              city == "金义新区"
                ? "金东区"
                : city == "金华开发区"
                  ? "开发区"
                  : city;
            that.city = filtName;
          });
          window.addEventListener("message", function (event) {
            //子获取父消息
            let newData;
            if (typeof event.data == "object") {
              newData = event.data;
            } else {
              newData = JSON.parse(event.data.argument);
            }
            that.name = newData.type;
            that.getDetail(newData.type);
          });
        },
        methods: {
          openVideo(obj) {
            const urlMap = {
              "永康市": "/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yks/video.html",
              "东阳市": "/static/citybrain/zhdd/zhdd_page/zfy/zfjly_dys/video.html",
              "浦江县": "/static/citybrain/zhdd/zhdd_page/zfy/zfjly_pjx/video.html",
              "婺城区": "/static/citybrain/zhdd/zhdd_page/zfy/jkVideo/test.html",
              "磐安县": "/static/citybrain/zhdd/zhdd_page/zfy/zfjly_pax/video.html",
              "兰溪市": "/static/citybrain/zhdd/zhdd_page/zfy/zfjly_lxs/video.html",
              "义乌市": obj.laiyuan == 2?"/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yws2/video.html":"/static/citybrain/zhdd/zhdd_page/zfy/zfjly_yws3/video.html",
            }
            if (obj.area_name == "武义县") {
              if (obj.hostcode == undefined || obj.hostcode == null) {
                this.$message({
                  message: '未绑定执法仪设备！',
                  type: 'warning',
                })
              } else {
                wyxZfy.startClient()
                wyxZfy.connectClient({
                  hostcode: obj.hostcode,
                  sys_depart: obj.sys_depart,
                })
              }
            } else {
              window.parent.lay.openIframe({
                type: "openIframe", //指令
                name: "videoTest",
                src: baseURL.url + urlMap[obj.area_name],
                left: "1120px",
                top: "572px",
                width: "1509px",
                height: "1009px",
                zIndex: 667,
                argument: {
                  name: "openVideoTest",
                  videoCode: [obj.sys_depart],
                },
              });
            }
          },
          //获取数据
          getDetail(name) {
            switch (name) {
              case "机动车辆":
                this.getJdclList();
                break;
              case "非机动车辆":
                this.getFjdclList();
                break;
              case "执法记录仪":
                this.getZfjlyList();
                break;
              case "对讲机":
                this.getDjjList();
                break;
              case "PDA":
                this.getPdaList();
                break;
              case "无人机":
                this.getWrjList();
                break;
              default:
                this.getCarTypeList(name);
            }
          },
          //判断是不是机动车
          isjdc(str) {
            return ["汽车","摩托车"].indexOf(str) != -1?"5":"6"
          },
          getCarTypeList(name) {
            $api("/xzzfj_zhdd_sblb",{county:this.city == "金华市"?"":this.city,device_type:this.isjdc(name),carType:name}).then(res => {
              console.log(res);
              this.cartableData = res.map(item => {return {
                area:item.area_name,
                depart:item.department,
                carType:item.car_type,
                carNumber:item.car_num
              }})
            })
          },
          //机动车辆
          getJdclList() {
            $api("/xzzfj_zhdd_sblb",{county:this.city == "金华市"?"":this.city,device_type:"5"}).then(res => {
              console.log(res);
              this.cartableData = res.map(item => {return {
                area:item.area_name,
                depart:item.department,
                carType:item.car_type,
                carNumber:item.car_num
              }})
            })
          },
          //非机动车辆
          getFjdclList() {
            $api("/xzzfj_zhdd_sblb",{county:this.city == "金华市"?"":this.city,device_type:"6"}).then(res => {
              console.log(res);
              this.cartableData = res.map(item => {return {
                area:item.area_name,
                depart:item.department,
                carType:item.car_type,
                carNumber:item.car_num
              }})
            })
          },
          //执法记录仪
          getZfjlyList() {
            $api("/xzzfj_zhdd_sblb",{county:this.city == "金华市"?"":this.city,device_type:"1"}).then(res => {
              console.log(res);
              this.jlytableData = res.map(item => {return {
                ...item,
                area:item.area_name,
                depart:item.department,
                name:item.name,
                status:item.lineon
              }})
            })
          },
          //对讲机
          getDjjList() {
            $api("/xzzfj_zhdd_sblb",{county:this.city == "金华市"?"":this.city,device_type:"4"}).then(res => {
              this.othertableData = res.map(item => {return {
                area:item.area_name,
                depart:item.department,
                pp:item.brand,
                xh:item.model,
                num:item.num
              }})
            })
          },
          //PDA
          getPdaList() {
            $api("/xzzfj_zhdd_sblb",{county:this.city == "金华市"?"":this.city,device_type:"3"}).then(res => {
              this.othertableData = res.map(item => {return {
                area:item.area_name,
                depart:item.department,
                pp:item.brand,
                xh:item.model,
                num:item.num
              }})
            })
          },
          //无人机
          getWrjList() {
            $api("/xzzfj_zhdd_sblb",{county:this.city == "金华市"?"":this.city,device_type:"2"}).then(res => {
              this.othertableData = res.map(item => {return {
                area:item.area_name,
                depart:item.department,
                pp:item.brand,
                xh:item.model,
                num:item.num
              }})
            })
          },
          close() {
            window.parent.lay.closeIframeByNames(["zfsbDialog"]);
          }
        },
      });
    </script>
  </body>
</html>
