<!--
 * @Descripttion: 添加BIM模型
 * @Author: <EMAIL>
 * @Date: 2022-12-11 14:04:59
-->
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
    <title>添加BIM模型</title>

    <link rel="stylesheet" href="https://dev.arcgisonline.cn/jsapi/4.24/esri/themes/light/main.css" />
    <script src="./libs/three-r116.min.js"></script>
    <script src="./index.js" type="module"> </script>

    <style>
        html,
        body,
        #viewDiv {
            padding: 0;
            margin: 0;
            height: 100%;
            width: 100%;
        }

        .tools {
            position: absolute;
            top: 20px;
            left: 50%;
            width: 50%;
            height: 200px;
            display: flex;
        }

        .tools span {
            cursor: pointer;
            background-color: blue;
            width: 150px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 20px;
            color: white;
        }

        .description {
            position: absolute;
            right: 10px;
            top: 10px;
            background-color: white;
            border-radius: 5px;
            padding: 20px;
        }
    </style>
</head>

<body>
    <div id="viewDiv"></div>
    <div class="tools">
        <span onclick="ArcGisUtils.addBimModalLayer(view)">添加BIM模型</span>
        <span onclick="ArcGisUtils.removeBimModalLayer(view)">清除BIM模型</span>
    </div>
    <div class="description">
        addBimModalLayer(view, type)
        <p>添加BIM模型</p>
        <p>@param { MapView | SceneView } view 对象 必填</p>
        <p>@param {true | false} isGoTo 是否定位过去 可选参数(默认为true)</p>
        <br />
        removeBimModalLayer(view)
        <p>移出BIM模型</p>
        <p>@param { MapView | SceneView } view 对象 必填</p>
    </div>
</body>

</html>