var wyxZfy = {
    clientStarter: new WebSocket('ws://127.0.0.1:9090'),
    client: null,
    startClient: function () {
        wyxZfy.clientStarter.send(JSON.stringify({
            "cmd": "LOGIN",
            "data": {
                "ip": "***************",
                "port": 5090,
                "type": 1,
                "username": "tonmx",
                "passwd": "1234"
            }
        }))
        $('#log').append('启动客户端<br/>')
    },
    connectClient: function (params = {}) {
        wyxZfy.client = new WebSocket('ws://127.0.0.1:9090')
        wyxZfy.client.onopen = function () {
            console.log('客户端连接成功')
            wyxZfy.call(params.hostcode, params.sys_depart)
            $('#log').append('客户端连接成功<br/>')
        }
        wyxZfy.client.onclose = function () {
            console.log('客户端连接断开')
            $('#log').append('客户端连接断开<br/>')
        }
        wyxZfy.client.onmessage = function (e) {
            if (e.data !== 'OK') {
                let data = JSON.parse(e.data)
                let flag = false

                if (!!data.cmd && data.cmd === 'CONN_RES') {
                    flag = data.conn === 1
                }
                console.log('视频服务连接' + (flag ? '成功' : '失败'))
                $('#log').append('视频服务连接' + (flag ? '成功' : '失败') + '<br/>')
            }
        }
        wyxZfy.client.onerror = function () {
            console.log('客户端连接发生错误')
            $('#log').append('客户端连接发生错误<br/>')
        }
    },
    connectService: function () {
        let loginMsg = {
            cmd: 'LOGIN',
            data: {
                ip: $('#ip').val(),
                port: Number($('#port').val()),
                type: 1,
                username: 'demo',
                passwd: 'demo',
            },
        }
        let msg = JSON.stringify(loginMsg)
        wyxZfy.client.send(msg)
        $('#log').append('注册信息:<br/>')
        $('#log').append(msg + '<br/>')
    },
    call: function (hostcode, sys_depart) {
        let msg = {
            device_id: sys_depart,
            camera_id: sys_depart,
            pno: '',
            pname: hostcode,
            speaker_id: sys_depart,
            sos: 0,
            deviceType: 2,
            cmd: 'VEDIO',
        }

        wyxZfy.client.send(JSON.stringify(msg))
        $('#log').append('开始拉流<br/>')
    },
}
