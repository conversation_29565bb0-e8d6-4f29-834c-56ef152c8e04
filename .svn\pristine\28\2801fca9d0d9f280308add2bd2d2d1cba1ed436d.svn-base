<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>指挥调度中心-video</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/DHWs_tc.js"></script>
    <style>
      /* 视频 */
      .videoBox {
        width: 400px;
        height: 198px;
        position: absolute;
        top: 1595px;
        left: 60px;
      }

      .container {
        position: relative;
      }

      .bottom-con {
        width: 96%;
        position: absolute;
        left: 20px;
        top: 340px;
      }

      .bottom-con ul {
        width: 100%;
        display: flex;
        justify-content: space-between;
      }

      .bottom-con ul li {
        width: 400px;
        text-align: center;
        line-height: 30px;
        color: #fff;
        font-size: 28px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .select {
        position: relative;
      }

      .icon-xz {
        position: absolute;
        right: 130px;
        top: 6px;
        opacity: 0.8;
        cursor: pointer;
      }

      .inputDiv {
        position: absolute;
        width: 280px;
        background-color: rgb(15, 28, 64, 0.8);
        right: 230px;
        top: 85px;
        border-radius: 20px 0 20px 0;
        z-index: 100;
      }

      .inputDivcon {
        overflow-y: auto;
        max-height: 220px;
      }

      .inputDivcon::-webkit-scrollbar {
        width: 8px;
        /* display: none; */
      }

      .inputDivcon::-webkit-scrollbar-thumb {
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgb(0 0 0 / 20%);
        background: #00c0ff;
      }

      .inputDivcon::-webkit-scrollbar-track {
        width: 5px;
        box-shadow: inset 0 0 5px rgb(0 0 0 / 20%);
        border-radius: 10px;
        background: rgba(0, 192, 255, 0.5);
      }

      .inputDivBottom {
        display: flex;
        height: 50px;
        align-items: center;
        justify-content: space-between;
        margin-top: 10px;
        margin-bottom: 10px;
      }

      .el-checkbox {
        display: flex !important;
        align-items: center;
        padding-left: 25px;
        height: 30px;
        margin-top: 20px;
      }

      .el-checkbox__inner {
        width: 27px;
        height: 27px;
        background-color: #cde7fe;
        border: none;
      }

      .el-checkbox__label {
        font-size: 30px;
        color: #fff;
      }

      .el-checkbox__inner::after {
        content: "\2713";
        color: #ffc561;
        font-size: 24px;
        line-height: 22px;
        font-weight: 600;
        background-color: #252316;
        border: none;
        text-align: center;
      }

      .el-checkbox__input.is-checked .el-checkbox__inner::after {
        transform: rotate(0deg) scaleY(1);
      }

      .el-checkbox__input.is-checked .el-checkbox__inner,
      .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        background-color: #252316;
        border-color: #ffc561;
        border: 1px solid #ffc561;
      }

      .el-checkbox__input.is-checked + .el-checkbox__label {
        color: #fff;
      }

      .inputDivBottom > div:first-child {
        font-size: 32px;
        font-weight: 300;
        color: #fff;
        margin-left: 20px;
        cursor: pointer;
      }

      .inputDivBottom > div:nth-child(2) {
        font-size: 32px;
        font-weight: 300;
        color: #ffc460;
        margin-right: 20px;
        cursor: pointer;
      }

      .panel-video {
        width: 440px;
        height: 270px;
        float: left;
        position: relative;
        margin: 0 20px 6px 0;
      }

      .text {
        width: 100%;
        position: absolute;
        bottom: 20px;
        font-size: 30px;
        color: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-left: 30px;
        padding: 2px 0;
        background: url("/static/images/zhdd/tit_bg.png") no-repeat;
      }

      .icon-cir {
        float: left;
        width: 37px;
        height: 37px;
        line-height: 37px;
        text-align: center;
        border-radius: 30px;
        background: #eeb755;
        margin-top: 3px;
        margin-right: 12px;
      }

      .icon-cir2 {
        background: #95e95b;
      }

      .icon-cir3 {
        background: #5dabf1;
      }

      .icon-cir4 {
        background: #a767d9;
      }
    </style>
  </head>

  <body>
    <div class="container" id="video" style="position: relative">
      <div class="panel-video">
        <div class="videoBox" id="zhddDom0"></div>
        <div class="text text1"></div>
      </div>
      <div class="panel-video">
        <div class="videoBox" id="zhddDom1"></div>
        <div class="text text2"></div>
      </div>
      <div class="panel-video">
        <div class="videoBox" id="zhddDom2"></div>
        <div class="text text3"></div>
      </div>
      <div class="panel-video">
        <div class="videoBox" id="zhddDom3"></div>
        <div class="text text4"></div>
      </div>
    </div>
    <script>
      const DHWsInstance = DHWs.getInstance({
        reConnectCount: 2,
        connectionTimeout: 30 * 1000,
        messageEvents: {
          loginState() { },
        },
      });
      DHWsInstance.detectConnectQt().then((res) => {
        if (res) {
          // 连接客户端成功
          DHWsInstance.login({
            loginIp: "*************",
            // loginPort: "6443",
            loginPort: "7901",
            userName: "csdn",
            userPwd: "Jhcsdn2024$",
            token: "",
            https: 0,
          });
          // this.$Message.info('登录中...')
          console.log("登录中...");
          DHWsInstance.on("loginState", (res) => {
            if (res) {
              // this.$Message.success('登录成功')
              console.log("登录成功");
              // this.activePanel = "key2";
            } else {
              // this.$Message.info('登录失败')
              console.log("登录失败");
            }
          });
        }
      });
      var vm = new Vue({
        el: "#video",
        data() {
          return {
            videoToken: "",
            ws: DHWsInstance,
            isLogin: false,
            showVideoList: [],
            sgId: "",
            // requestSource: window.localStorage.getItem('requestSource'),
            requestSource: "screen",
          };
        },
        // created() {
        //   this.login()
        // },
        mounted() {
          // this.login();
          this.initVideo();
        },
        methods: {
          // login() {
          //   // 调用登录接口
          //   this.ws.detectConnectQt().then((res) => {
          //     if (res) {
          //       // 连接客户端成功
          //       this.ws.login({
          //         loginIp: "*************",
          //         loginPort: "7902",
          //         userName: "csdn",
          //         userPwd: "Jhcsdn2024$",
          //         token: "",
          //         https: 1,
          //       });
          //       // this.$Message.info('登录中...')
          //       console.log("登录中...");
          //       this.ws.on("loginState", (res) => {
          //         this.isLogin = res;
          //         if (res) {
          //           console.log("登录成功");
          //           this.initVideo();
          //         } else {
          //           console.log("登录失败");
          //         }
          //       });
          //     } else {
          //       // 连接客户端失败
          //       this.$Message.info("请重新安装客户端");
          //     }
          //   });
          // },
          initVideo() {
            this.sgId = this.getUrlParamValue("sgid");
            let yjid = this.getUrlParamValue("yjid");
            let jl = this.getUrlParamValue("jl");
            let lx = this.getUrlParamValue("lx");

            this.setVideoDivLeft();
            if (this.getUrlParamValue("name") == "null") {
              axios({
                method: "get",
                url:
                  baseURL.url +
                  "/jhyjzh-server/screen_api/zhdd/zhddzx/evetVideo",
                params: { sgid: this.sgId, yjid: yjid, jl: jl, lx: lx },
              }).then((res) => {
                console.log(res,"dhVideo");
                // 接口调用不通 临时数据
                let datas = [
                  {
                    bq: "建筑住宅/楼宇/金义新区/金东鸿泰豪苑楼顶",
                    code: "33071007041321087394",
                    jl: 0.89,
                    name: "曹宅鸿泰豪苑楼顶高倍球_DH201912LT雪亮X94",
                  },
                  {
                    code: "33070354001321081061",
                    name: "金东鸿泰豪苑楼顶_DH201912LT雪亮X61",
                    jl: 1.45,
                    bq: "建筑住宅/楼宇/金义新区/金东鸿泰豪苑楼顶",
                  },
                  {
                    code: "33070399001321047685",
                    name: "金汇路葱草街西口南侧行闯_DH201912HS雪亮X85",
                    jl: 1.49,
                    name: "金汇路葱草街西口南侧行闯_DH201912HS雪亮X85",
                  },
                  {
                    code: "33070354001321084078",
                    name: "金东镇政府西侧路口_DH201912DX雪亮G78",
                    jl: 1.52,
                    name: "金东镇政府西侧路口_DH201912DX雪亮G78",
                  },
                  {
                    code: "33070354001321085924",
                    name: "金东杜宅工业园区黄金畈村_DH201912DX雪亮G24",
                    jl: 1.57,
                    bq: "-",
                    name: "金东杜宅工业园区黄金畈村_DH201912DX雪亮G24",
                  },
                  {
                    code: "33070354001321086645",
                    name: "金东3省道金江路438号_DH201912LT雪亮X45",
                    jl: 3,
                    name: "金东3省道金江路438号_DH201912LT雪亮X45",
                  },
                  {
                    code: "33070354001320080067",
                    name: "金东大黄村工业区大维电子路口_2DX67",
                    jl: 2.83,
                    name: "金东大黄村工业区大维电子路口_2DX67",
                  },
                  {
                    code: "33070354001321086538",
                    name: "金东枧头村北村口_DH201912DX雪亮G38",
                    jl: 2.7,
                    bq: "-",
                    name: "金东枧头村北村口_DH201912DX雪亮G38",
                  },
                  {
                    code: "33070354001321088717",
                    name: "金东六大山村南岔路口_DH201912DX雪亮G17",
                    jl: 2.68,
                    name: "金东六大山村南岔路口_DH201912DX雪亮G17",
                  },
                ];
                this.getVideoList(datas);
              });
            } else {
              let NewData = JSON.parse(this.getUrlParamValue("name"));
              console.log("接收name数组", NewData);
              this.getVideoList(NewData);
            }
            // common.funSearchInfoOnline22({ url: 'zhddzx/evetVideo', argument: { "sgid": zhddzxRight.sgId,"yjid":yjid } }, zhddzxRight.getVideoList);

            window.addEventListener(
              "message",
              function (event) {
                // if (typeof event.data == "object") {
                // } else {
                //   let data = JSON.parse(event.data);
                //   this.sgId = data.argument.sgid;
                //   axios({
                //     method: "get",
                //     url:
                //       baseURL.url +
                //       "/jhyjzh-server/screen_api/zhdd/zhddzx/evetVideo",
                //     params: {
                //       sgid: this.sgId,
                //       yjid: yjid,
                //       jl: jl,
                //       lx: lx,
                //     },
                //   }).then((res) => {
                //     this.getVideoList(res);
                //   });
                //   // common.funSearchInfoOnline22({
                //   //   url: 'zhddzx/evetVideo',
                //   //   argument: { "sgid": zhddzxRight.sgId, "yjid": yjid, "jl": jl, "lx": lx }
                //   // }, zhddzxRight.getVideoList);
                // }
              },
              false
            );
          },
          // 获取url参数
          getUrlParamValue(paramName) {
            const reg = new RegExp("(^|&)" + paramName + "=([^&]*)(&|$)", "i");
            const r = window.location.search.substr(1).match(reg);
            if (r != null) {
              // 解码uri
              return decodeURI(r[2]);
            } else {
              return null;
            }
          },
          setVideoDivLeft() {
            let width = top.window.document.body.clientWidth
            if(width<=1920){
              document
                .getElementById("zhddDom0")
                .setAttribute("style", "width:200px;height:99px;left:1425px;top:800px;");
              document
                .getElementById("zhddDom1")
                .setAttribute("style", "width:200px;height:99px;left:1200px;top:800px;");
              document
                .getElementById("zhddDom2")
                .setAttribute("style", "width:200px;height:99px;left:1425px;top:665px;");//10
              document
                .getElementById("zhddDom3")
                .setAttribute("style", "width:200px;height:99px;left:1200px;top:665px;");//30
            }else {
              if (window.location.ancestorOrigins.length <= 2) {
                document
                  .getElementById("zhddDom0")
                  .setAttribute("style", "left:2850px");
                document
                  .getElementById("zhddDom1")
                  .setAttribute("style", "left:2850px");
                document
                  .getElementById("zhddDom2")
                  .setAttribute("style", "left:2850px");
                document
                  .getElementById("zhddDom3")
                  .setAttribute("style", "left:2850px");
              } else {
                document
                  .getElementById("zhddDom0")
                  .setAttribute("style", "left:4800px;top:1520px");
                document
                  .getElementById("zhddDom1")
                  .setAttribute("style", "left:4800px;;top:1520px");
                document
                  .getElementById("zhddDom2")
                  .setAttribute("style", "left:4800px;;top:1520px");
                document
                  .getElementById("zhddDom3")
                  .setAttribute("style", "left:4800px;;top:1520px");
              }
            }
          },
          getVideoList(data) {
            this.showVideoList = data;
            this.create();
          },
          create() {
            let _this = this;
            // 关闭大华视频
            // top.DHWsInstance.destroyCtrl(['zhddDom0','zhddDom1','zhddDom2','zhddDom3']);
            var paramList = [];
            this.showVideoList.map((item, index) => {
              if (index <= 3) {
                // <span class="icon-cir icon-cir${index + 1}">${index + 1}</span>
                $(".text" + (index + 1)).html(`${item.name}`);
                $(".text" + (index + 1)).attr("title", item.name);
              }
            });

            let videoNum =
              this.showVideoList.length <= 4 ? this.showVideoList.length : 4;
            for (let index = 0; index < videoNum; index++) {
              let item = this.showVideoList[index];

              paramList[index] = {
                ctrlType: "playerWin",
                ctrlCode: "zhddDom" + index,
                ctrlProperty: {
                  displayMode: 1,
                  splitNum: 1,
                  channelList: [
                    {
                      channelId: item.code,
                    },
                  ],
                },
                visible: true,
                domId: "zhddDom" + index,
                dom: document.getElementById("zhddDom" + index),
              };
            }

            setTimeout(function () {
              _this.ws
                .createCtrl(paramList)
                .then((res) => {
                  console.log("::::::::::::::::::::", res);
                  var rightIframeShow = true;
                  // var rightIframeShow = window.sessionStorage.getItem("leftIframeShow");
                  if (rightIframeShow != "true") {
                    _this.etVisible(true);
                  }
                })
                .catch((e) => {
                  console.log(e);
                });
              _this.ws.on("createCtrlResult", (res) => {
                console.warn(res);
              });
            }, 2000);
          },
          etVisible(isVideo) {
            let _this = this;
            // 调用设置控件显隐接口
            let ctrlList = ["zhddDom0", "zhddDom1", "zhddDom2", "zhddDom3"];
            ctrlList.forEach((item) => {
              try {
                const params = [
                  {
                    ctrlCode: item,
                    visible: isVideo,
                  },
                ];
                _this.ws.setCtrlVisible(params);
              } catch (e) {}
            });
          },
        },
      });
    </script>
  </body>
</html>
