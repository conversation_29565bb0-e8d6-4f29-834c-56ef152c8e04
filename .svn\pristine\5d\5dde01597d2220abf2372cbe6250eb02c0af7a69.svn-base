function listenerFun(event) {
  let info = event.data
  if (
    ![
      'pointClick',
      'line',
      'Polygon',
      'getDatas',
      'bankuaiClick',
      'gifClick',
      'returnDrawArea',
      'layerClick',
      'fillExtrusionLayer',
    ].includes(info.type)
  ) {
    for (let i = 0; i < window.frames.length; i++) {
      window.frames[i].postMessage(info, '*')
    }
  } else {
    for (let i = 0; i < window.frames.length; i++) {
      $('iframe')[i].name != 'map' && $('iframe')[i].contentWindow.postMessage(info, '*')
    }
  }

  if (info instanceof Object) {
    if ('setPanelStyle' === info.action) {
      const ids = info.params.ids || []
      const names = info.params.names || []
      const style = info.params.style ?? {}
      ids.map((id) => {
        $(`#${id}`).css(style)
      })
      names.map((name) => {
        $(`iframe[name="${name}"]`).css(style)
      })
    }
    return
  }

  if (info.indexOf('}') !== -1) {
    let jsonInfo = JSON.parse(info)
    //隐藏显示iframe
    if (jsonInfo.type === 'fadeIframe') {
      let x = jsonInfo.data
      fadeIfr.run(...x)
    }

    if (jsonInfo.type == 'loaded') {
      setTimeout(() => {
        eventbus.emit('mapLoaded')
      }, 2000)
    }
  }
}
// window.removeEventListener('message', listenerFun)
window.addEventListener('message', listenerFun)
