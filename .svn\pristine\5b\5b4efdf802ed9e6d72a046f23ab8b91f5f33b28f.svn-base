/*
 * @Descripttion: 
 * @Author: <EMAIL>
 * @Date: 2022-12-11 11:51:52
 */
import Slice from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/widgets/Slice.js";

/**
 * 创建剖切微件
 * @param { MapView|SceneView} view 对象 必填
 * @param {HTMLElement} container dom元素 必填
 * @returns Slice 实例
 */

async function createSiceWidget(view, container) {
  console.log(view, container);

  if (!view) {
    throw new Error("参数view为必传！");
  }
  if (!container) {
    throw new Error("参数container为必传！");
  }
  const slice = new Slice({
    view,
    container,
  });
  return slice;
}

export default createSiceWidget;
