
let THREE=window.THREE_r123;

/**
 * @class
 * @anthor 蒋雪雪（email: <EMAIL>）
 * @param options {Object} 参数集
 *
 */
class Mover {
  constructor(options) {
    this.opts = {
    }

    this.position = new THREE.Vector3();
    this.velocity = new THREE.Vector3();
    this.acceleration = new THREE.Vector3();
    this.anchor = new THREE.Vector3();
    this.mass = 1;
    this.r = 0;
    this.g = 0;
    this.b = 0;
    this.a = 1;
    this.time = 0;
    this.is_active = false;
  }
  init(vector) {
    this.position = vector.clone();
    this.velocity = vector.clone();
    this.anchor = vector.clone();
    this.acceleration.set(0, 0, 0);
    this.a = 1;
    this.time = 0;
  }
  updatePosition() {
    this.time++;
    this.position.copy(this.velocity);
  }
  updateVelocity() {
    this.acceleration.divideScalar(this.mass);
    this.velocity.sub(this.acceleration);
  }
  applyForce(vector) {
    this.acceleration.add(vector);
  }
  activate() {
    this.is_active = true;
  }
  inactivate() {
    this.is_active = false;
  }
}

export default Mover;
