<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>执法态势-左</title>
    <link rel="stylesheet" href="/static/css/animate.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <link rel="stylesheet" href="/static/css/viewCss/zfts_left.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/echarts/echarts.min.js"></script>
    <script src="/echarts/echarts-gl.min.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/jslib/biz.min.js"></script>
    <style>
      .quan0 {
        animation: move infinite 5s;
      }
      .quan1 {
        animation: move infinite 5s 0.5s;
      }
      .quan2 {
        animation: move infinite 5s 1.2s;
      }
      .quan3 {
        animation: move infinite 5s 1s;
      }
      .quan4 {
        animation: move infinite 5s 0.5s;
      }
      @keyframes move {
        0% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(20px);
        }
        100% {
          transform: translateY(0px);
        }
      }
    </style>
  </head>

  <body>
    <div id="left" v-cloak>
      <div class="hearder_h1"><span>检查计划统筹</span></div>
      <div class="jcjh">
        <div class="jhzs" @click="showDialog('年度计划数')">
          <li class="text">{{jhwcjdData[0].label}}</li>
          <li class="value">
            <count-to
              :start-val="0"
              :end-val="Number(jhwcjdData[0].num)"
              :duration="3000"
              class="count-toNum s-c-yellow-gradient"
            >
            </count-to>
            <!-- {{jhwcjdData[0].num}} -->
            <span class="unit">{{jhwcjdData[0].unit}}</span>
          </li>
        </div>
        <div class="zdbm jhzs" @click="showDialog('未完成计划数')">
          <li class="text">{{jhwcjdData[1].label}}</li>
          <li class="value" style="color: #3cfdff">
            <count-to
              :start-val="0"
              :end-val="Number(jhwcjdData[1].num)"
              :duration="3000"
              class="count-toNum s-c-yellow-gradient"
            >
            </count-to>
            <!-- {{jhwcjdData[1].num}} -->
            <span class="unit"> {{jhwcjdData[1].unit}}</span>
          </li>
        </div>
      </div>
      <div class="hearder_h2"><span>计划完成进度</span></div>
      <div class="jhwcjd">
        <div class="jd-part">
          <div
            class="first jd"
            v-for="(item,index) in jhwcjdData.slice(2)"
            :key="index"
            @click="showPlanNumDialog(item.label)"
          >
            <li class="text" style="font-size: 28px">{{item.label}}</li>
            <li :class="`jd${index} value`" style="font-size: 40px">
              {{item.num}}<span class="unit" style="font-size: 28px"
                >{{item.unit}}
              </span>
            </li>
          </div>
        </div>
        <div class="jhjd-bg">
          <div id="jhjd"></div>
        </div>
      </div>
      <div class="hearder_h2"><span>检查计划类型</span></div>
      <div class="s-flex">
        <div class="jhlx-con">
          <div id="jhlx"></div>
          <div class="jhlx-bg"></div>
        </div>
        <div class="jhlx-con">
          <div id="jhlx1"></div>
          <div class="jhlx-bg"></div>
        </div>
      </div>
      <div class="hearder_h1"><span>综合查一次</span></div>
      <div class="zhcyc">
        <div
          class="quan"
          :class="`quan${index==0?2:index==2?0:index}`"
          v-for="(item,index) in zhcycData"
          :key="index"
          @click="showjcdxDialog(item)"
        >
          <li>{{item.name}}</li>
          <li :class="`txt${index}`">{{item.value}}{{item.unit}}</li>
        </div>
      </div>
      <div class="hearder_h1"><span>监管一件事</span></div>
      <div class="tj">
        <li v-for="(item,index) in tjList" :key="index" style="flex: 0.2">
          <img :src="item.icon" alt="" class="breath-light" />
          <div class="sp">
            <div class="sp-ys">
              {{item.value}}
              <span class="s-font-28">{{item.unit}}</span>
            </div>
            <div>{{item.name}}</div>
          </div>
        </li>
        <div class="table" style="height: 380px; width: 850px">
          <div class="th">
            <div class="th_td" style="flex: 0.2">序号</div>
            <div class="th_td" style="flex: 0.2">年度</div>
            <div class="th_td" style="flex: 0.2">地区</div>
            <div class="th_td" style="flex: 0.4">重点任务选题</div>
            <div class="th_td" style="flex: 0.4">上报部门</div>
          </div>
          <div
            class="tbody"
            id="box1"
            @mouseover="mouseenterEvent1()"
            @mouseleave="mouseleaveEvent1()"
          >
            <div class="tr" v-for="(item,index) in tableData1" :key="index">
              <div class="tr_td" style="flex: 0.2">{{index + 1}}</div>
              <div class="tr_td" style="flex: 0.2">{{item.nd}}</div>
              <div class="tr_td" style="flex: 0.2">{{item.dq}}</div>
              <div class="tr_td" style="flex: 0.4" :title="item.zdrwxt">
                {{item.zdrwxt}}
              </div>
              <div
                class="tr_td"
                style="flex: 0.4"
                :title="item.cydw"
                @click="openYJS(item.xh)"
              >
                {{item.cydw}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      window.parent.eventbus &&
      window.parent.eventbus.on("yearChange", (year) => {
        vm.initApi(localStorage.getItem("city"),year);
      });
      let vm = new Vue({
        el: "#left",
        data: {
          citylist:[
            { name: "婺城区", color: [40, 194, 254, 1], height: 500 },
            { name: "金东区", color: [40, 194, 254, 1], height: 500 },
            { name: "开发区", color: [40, 194, 254, 1], height: 500 },
            { name: "兰溪市", color: [40, 194, 254, 1], height: 500 },
            { name: "浦江县", color: [40, 194, 254, 1], height: 500 },
            { name: "义乌市", color: [40, 194, 254, 1], height: 500 },
            { name: "东阳市", color: [40, 194, 254, 1], height: 500 },
            { name: "磐安县", color: [40, 194, 254, 1], height: 500 },
            { name: "永康市", color: [40, 194, 254, 1], height: 500 },
            { name: "武义县", color: [40, 194, 254, 1], height: 500 },
          ],
          citylist1:[
            { name: "婺城区", color: [39, 179, 234, 0], height: 5000 },
            { name: "金东区", color: [121, 217, 255, 0], height: 5000 },
            { name: "开发区", color: [40, 194, 254, 0], height: 5000 },
            { name: "兰溪市", color: [0, 140, 195, 0], height: 5000 },
            { name: "浦江县", color: [45, 96, 184, 0], height: 5000 },
            { name: "义乌市", color: [60, 145, 177, 0], height: 5000 },
            { name: "东阳市", color: [50, 104, 195, 0], height: 5000 },
            { name: "磐安县", color: [7, 88, 231, 0], height: 5000 },
            { name: "永康市", color: [1, 56, 150, 0], height: 5000 },
            { name: "武义县", color: [16, 92, 216, 0], height: 5000 },
          ],
          maskUrl: {
            婺城区: "/static/data/mask/wcq.json",
            金东区: "/static/data/mask/jdq.json",
            东阳市: "/static/data/mask/dys.json",
            义乌市: "/static/data/mask/yws.json",
            永康市: "/static/data/mask/yks.json",
            兰溪市: "/static/data/mask/lxs.json",
            浦江县: "/static/data/mask/pjx.json",
            武义县: "/static/data/mask/wyx.json",
            磐安县: "/static/data/mask/pax.json",
            金华开发区: "/static/data/mask/kfq.json",
          },
          //计划完成进度
          jhwcjdData: [
            {
              name: "已完成检查计划",
              num: "000",
              unit: "%",
            },
            {
              name: "正在实施计划",
              num: "000",
              unit: "%",
            },
          ],
          //检查计划类型
          jhlxData: [
            {
              name: "双随机抽查计划",
              value: 651,
              zb: 94.48,
              itemStyle: {},
            },
            {
              name: "重点监管计划",
              value: 38,
              zb: 5.52,
              itemStyle: {},
            },
          ],
          jhlx1Data: [
            {
              name: "跨部门",
              value: 163,
              zb: 23.66,
              itemStyle: {},
            },
            {
              name: "单部门",
              value: 526,
              zb: 76.34,
              itemStyle: {},
            },
          ],
          //监管一件事
          tjList: [
            {
              icon: "/static/images/zfts/zfts_left.png",
              name: "重点任务",
              value: "20",
              unit: "件",
            },
            // {
            //   icon: "/static/images/zfts/dyzs.png",
            //   name: "监管一件事",
            //   value: "71",
            //   unit: "件",
            // },
            // {
            //   icon: "/static/images/zfts/jcdw.png",
            //   name: "应用场景",
            //   value: "3",
            //   unit: "个",
            // },
          ],
          //综合查一次
          zhcycData: [],
          tableData: [],
          tableData1: [],
        },
        mounted() {
          let initcity=localStorage.getItem("city");
          this.initApi(initcity,localStorage.getItem("year"));
          //下面是从驾驶舱点击执法态势
          window.onload = () => {
            let merge = window.frames['map']?window.frames['map'].view.map.findLayerById("VECTOR_POI_Merge"):window.parent.frames['map'].view.map.findLayerById("VECTOR_POI_Merge")
            if (merge) merge.visible = false
            let dem = window.frames['map']?window.frames['map'].view.map.findLayerById("JINHUA_DEM"):window.parent.frames['map'].view.map.findLayerById("JINHUA_DEM")
            if (dem) dem.visible = false
            let label = window.frames['map']?window.frames['map'].view.map.findLayerById("VECTOR_Road_Label"):window.parent.frames['map'].view.map.findLayerById("VECTOR_Road_Label")
            if (label) label.visible = false
            let vector = window.frames['map']?window.frames['map'].view.map.findLayerById("VECTOR"):window.parent.frames['map'].view.map.findLayerById("VECTOR")
            if (vector) vector.visible = false
            window.frames['map']?window.frames['map'].view.map.remove(window.frames['map'].xingzhenlayer):window.parent.frames['map'].view.map.remove(window.parent.frames['map'].xingzhenlayer)
            window.frames['map']?window.frames['map'].mapUtil.removeLayer("mask"):window.parent.frames['map'].mapUtil.removeLayer("mask")
            if (localStorage.getItem("city")!="金华市"){
              window.frames['map']?window.frames['map'].xingzhen(localStorage.getItem("city")):window.parent.frames['map'].xingzhen(localStorage.getItem("city"))
            }
            if (localStorage.getItem("adminCity")=="金华市"){
                this.banKuai(localStorage.getItem("city"));
            }else{
              this.banKuai(localStorage.getItem("adminCity"));
            }
            this.openBottom();
          }
          // 表格滚动s
          this.dom1 = document.getElementById("box1");
          this.mouseleaveEvent1();
          //监测县市区的切换
          window.parent.eventbus &&
            window.parent.eventbus.on("cityChange", (city) => {
              let filtName =
                city == "金义新区"
                  ? "金东区"
                  : city == "金华开发区"
                  ? "开发区"
                  : city;
              this.initApi(filtName,localStorage.getItem("year"));
              // 显示各个市区的板块
              window.frames['map']?window.frames['map'].mapUtil.removeLayer("bankuai"):window.parent.frames['map'].mapUtil.removeLayer("bankuai")
              window.frames['map']?window.frames['map'].mapUtil.removeLayer("map_text"):window.parent.frames['map'].mapUtil.removeLayer("map_text")
              this.banKuai(localStorage.getItem("city"));
            });

          window.parent.eventbus &&
          window.parent.eventbus.on("yearChange", (year) => {
            this.initApi(localStorage.getItem("city"),year);
          });
        },
        methods: {
          showDialog(name) {
            window.parent.lay.openIframe({
              type: "openIframe",
              name: "planDialog",
              id: "planDialog",
              src: baseURL.url + "/static/citybrain/commonts/zfts/planDialog.html",
              left: "1200px",
              top: "575px",
              width: "1515px",
              height: "866px",
              zIndex: "666",
              argument: {
                type: name
              },
            });
          },
          showjcdxDialog(item) {
            window.parent.lay.openIframe({
              type: "openIframe",
              name: "jcdxDialog",
              id: "jcdxDialog",
              src: baseURL.url + "/static/citybrain/commonts/zfts/jcdxDialog.html",
              left: "1074px",
              top: "575px",
              width: "1730px",
              height: "999px",
              zIndex: "666",
              argument: {
                type: item.name
              },
            });
          },
          showPlanNumDialog(item) {
            window.parent.lay.openIframe({
              type: "openIframe",
              name: "planNumDialog",
              id: "planNumDialog",
              src: baseURL.url + "/static/citybrain/commonts/zfts/planNumDialog.html",
              left: "1074px",
              top: "405px",
              width: "1730px",
              height: "1164px",
              zIndex: "666",
              argument: {
                type: item
              },
            });
          },
          mouseenterEvent1() {
            clearInterval(this.time1);
          },
          mouseleaveEvent1() {
            this.time1 = setInterval(() => {
              this.dom1.scrollBy({
                top: 71,
                behavior: "smooth",
              });
              if (
                this.dom1.scrollTop >=
                this.dom1.scrollHeight - this.dom1.offsetHeight
              ) {
                this.dom1.scrollTop = 0;
              }
            }, 1500);
          },
          /**
           * 在从驾驶舱点进执法态势页面时 增加页面的执法态势板块
           * @param url 当前县市区名称
           */
          banKuai(url){
            axios({
              method: "get",
              url: "/static/data/zfts/"+url+".json",
            }).then((res)=> {
              window.frames['map']?window.frames['map'].mapUtil.mask({
                layerid: "bankuai",
                data: res.data, // res 是要素集合
                style: {
                  strokeWidth: localStorage.getItem("city")=="金华市"?2:1,
                  strokeColor: '#60e4ff', //多边形轮廓颜色透明度
                  fillColor: '#10b3ff', //多边形填充色
                  height: 50,
                },
                onclick: null,
              }):window.parent.frames['map'].mapUtil.mask({
                layerid: "bankuai",
                data: res.data, // res 是要素集合
                style: {
                  strokeWidth: localStorage.getItem("city")=="金华市"?2:1,
                  strokeColor: '#60e4ff', //多边形轮廓颜色透明度
                  fillColor: '#10b3ff', //多边形填充色
                  height: 50,
                },
                onclick: null,
              });
            })
            let textData = [
              {pos:[119.602579, 29.070607,500],text: "开发区"},
              {pos:[119.514748, 28.964012, 500],text: "婺城区"},
              {pos:[119.799596, 29.149391, 500],text: "金东区"},
              {pos:[119.714529, 28.768287, 500],text: "武义县"},
              {pos:[119.903937, 29.520086, 500],text: "浦江县"},
              {pos:[120.609672, 29.007893, 500],text: "磐安县"},
              {pos:[119.526736, 29.278165, 500],text: "兰溪市"},
              {pos:[120.061011, 29.300614, 500],text: "义乌市"},
              {pos:[120.364678, 29.232405, 500],text: "东阳市"},
              {pos:[120.102417, 28.934317, 500],text: "永康市"},
            ]
            let textData1=[];
            let color=[];
            switch (url){
              case "婺城区": textData1 = [{pos: [119.514748, 28.964012],text: "婺城区"}];
                break
              case "金东区":  textData1 = [{pos: [119.799596, 29.149391],text: "金东区"}];
                break
              case "金华开发区":  textData1 = [{pos: [119.602579, 29.070607],text: "开发区"}];
                break
              case "兰溪市":  textData1 = [{pos: [119.526736, 29.278165],text: "兰溪市"}];
                break
              case "浦江县":  textData1 = [{pos: [119.903937, 29.520086],text: "浦江县"}];
                break
              case "义乌市":  textData1 = [{pos: [120.061011, 29.300614],text: "义乌市"}];
                break
              case "东阳市":  textData1 = [{pos: [120.375678, 29.232405],text: "东阳市"}];
                break
              case "磐安县":  textData1 = [{pos: [120.559672, 29.037893],text: "磐安县"}];
                break
              case "永康市":  textData1 = [{pos: [120.102417, 28.934317],text: "永康市"}];
                break
              case "武义县":  textData1 = [{pos: [119.714529, 28.768287],text: "武义县"}];
                break
            }
            window.frames['map']?window.frames['map'].mapUtil.loadTextLayer({
              layerid: "map_text",
              data: localStorage.getItem("city")=="金华市"?textData:textData1,
              style: {
                size: localStorage.getItem("city")=="金华市"?45:60,
                color: '#2b2d6a',
              },
            }):window.parent.frames['map'].mapUtil.loadTextLayer({
              layerid: "map_text",
              data: localStorage.getItem("city")=="金华市"?textData:textData1,
              style: {
                size: localStorage.getItem("city")=="金华市"?45:60,
                color: '#2b2d6a',
              },
            });
          },
          //将数组中指定下标的两个元素调换位置
          swapArrData(index1,index2,arr) {//index1,index2代表需要交换的数组索引
            const temp = arr.splice(index2, 1, arr[index1]);
            arr[index1] = temp[0];
            return arr;
          },
          initApi(city,year) {
            //综合查一次
            $api("/csdn_yjyp1", { area_code: city,sjwd2: year }).then((res) => {
              this.zhcycData = [];
              res.map((a) => {
                this.zhcycData.push({
                  name: a.label.split("-")[1],
                  value: a.num,
                  unit: a.unit,
                });
              });
            });
            //检查计划统筹
            $api("/csdn_yjyp4", { area_code: city,sjwd2: year }).then((res) => {
              // this.jhwcjdData = this.swapArrData(1,4,res);
              this.jhwcjdData = this.sortArr(res, "orderid");
              console.log(this.jhwcjdData);
              let obj = res.slice(2);
              this.getChart01(
                "jhjd",
                [Number(obj[0].num)],
                [Number(obj[1].num)],
                [Number(obj[2].num)]
              );
            });
            $api("/csdn_yjyp8", { area_code: city,sjwd2: year }).then((res) => {
              this.jhlxData[0].value = Number(
                res.find((a) => a.label.includes("双随机") && a.unit == "个")
                  .num
              );
              this.jhlxData[0].zb = Number(
                res.find((a) => a.label.includes("双随机") && a.unit == "%").num
              );
              this.jhlxData[1].value = Number(
                res.find((a) => a.label.includes("重点") && a.unit == "个").num
              );
              this.jhlxData[1].zb = Number(
                res.find((a) => a.label.includes("重点") && a.unit == "%").num
              );
              this.getChart02("jhlx", this.jhlxData, ["#00c0ff", "#ffd461"]);
            });
            $api("/csdn_yjyp9", { area_code: city,sjwd2: year }).then((res) => {
              this.jhlx1Data[0].value = Number(
                res.find((a) => a.label.includes("跨部门") && a.unit == "个")
                  .num
              );
              this.jhlx1Data[0].zb = Number(
                res.find((a) => a.label.includes("跨部门") && a.unit == "%").num
              );
              this.jhlx1Data[1].value = Number(
                res.find((a) => a.label.includes("单部门") && a.unit == "个")
                  .num
              );
              this.jhlx1Data[1].zb = Number(
                res.find((a) => a.label.includes("单部门") && a.unit == "%").num
              );
              this.getChart02("jhlx1", this.jhlx1Data, ["#22e8e8", "#a9db52"]);
            });
            // 监管表格
            $api("/csdn_yjyp23", { dq: city,nd: year }).then((res) => {
              this.tableData = res;
              const res1 = new Map();
              for (let i of res) {
                if (!res1.has(i.xh)) {
                  res1.set(i.xh, i);
                }
              }
              str = [...res1.values()];
              this.tableData1 = str;
              this.tjList[0].value = str.length;
            });
          },
          //将数组中的每个对象根据字段orderid进行排序
          sortArr(arr, field) {
            return arr.sort((a, b) => {
              return Number(a[field]) - Number(b[field]);
            });
          },
          openYJS(index) {
            window.parent.lay.openIframe({
              type: "openIframe",
              name: "jgyjs_table_diong",
              src: "/static/citybrain/commonts/zfts/jgyjs_table_diong.html",
              left: "calc(50% - 800px)",
              top: "840px",
              width: "1200px",
              height: "680px",
              zIndex: 667,
              argument: {
                jgyjs_table_diong: this.tableData.filter((a) => a.xh == index),
              },
            });
          },
          // 加载底部
          openBottom() {
            window.parent.lay.openIframe({
              type: "openIframe",
              name: "zfts_bottom",
              id: "zfts_bottom",
              src: baseURL.url + "/static/citybrain/zfts/zfts_bottom.html",
              width: "1760px",
              height: "525px",
              left: "calc(50% - 860px)",
              top: '73.3%',
              zIndex: "666",
            });
          },
          //计划完成进度
          getChart01(id, data1, data2, data3) {
            let myEc = echarts.init(document.getElementById(id));
            option = {
              // legend: {
              //   top: "16%",
              //   textStyle: {
              //     color: "#a4a8b4",
              //   },
              // },
              grid: {
                left: "2%",
                top: "25%",
                right: "2.8%",
                bottom: "3%",
                containLabel: true,
              },
              xAxis: {
                type: "value",
                splitLine: {
                  show: false,
                  lineStyle: {
                    color: "rgba(255,255,255,0.3)",
                  },
                },
                axisTick: {
                  show: false,
                },
                axisLine: {
                  show: false,
                },
                axisLabel: {
                  show: false,
                  // margin: 10,
                  color: "#a4a8b4",
                },
              },
              yAxis: {
                type: "category",
                data: ["E"],
                axisLine: {
                  show: false,
                  lineStyle: {
                    color: "rgba(255,255,255,0.3)",
                  },
                },
                splitLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  show: false,
                  // margin: 10,
                  color: "#a4a8b4",
                },
              },
              color: [
                new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  {
                    offset: 0,
                    color: "#18E1FF",
                  },
                  {
                    offset: 0.8,
                    color: "rgba(106,185,242,.8)",
                  },
                  {
                    offset: 1,
                    color: "rgba(106,185,242,0)",
                  },
                ]),
                new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  {
                    offset: 0,
                    color: "#FFC435",
                  },
                  {
                    offset: 0.8,
                    color: "rgba(106,185,242,.8)",
                  },
                  {
                    offset: 1,
                    color: "rgba(106,185,242,0)",
                  },
                ]),
                new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                  {
                    offset: 0,
                    color: "#18FFB6",
                  },
                  {
                    offset: 0.8,
                    color: "rgba(106,185,242,.8)",
                  },
                  {
                    offset: 1,
                    color: "rgba(106,185,242,0)",
                  },
                ]),
              ],
              series: [
                {
                  name: "已完成检查计划",
                  type: "bar",
                  stack: "Tik Tok",
                  barWidth: 30,
                  data: data1,
                },
                {
                  name: "正在实施计划",
                  type: "bar",
                  stack: "Tik Tok",
                  barWidth: 30,
                  data: data2,
                },
                {
                  name: "待实施计划",
                  type: "bar",
                  stack: "Tik Tok",
                  barWidth: 30,
                  data: data3,
                },
              ],
            };

            myEc.setOption(option);
            myEc.getZr().on("mousemove", (param) => {
              myEc.getZr().setCursorStyle("default");
            });
          },
          //计划类型
          getChart02(id, echartsData, colors) {
            let myChart = echarts.init(document.getElementById(id));
            let selectedIndex = "";
            let hoveredIndex = "";
            console.log("echartsData", echartsData);
            option = getPie3D(echartsData, 0.59);
            // 生成扇形的曲面参数方程
            function getParametricEquation(
              startRatio,
              endRatio,
              isSelected,
              isHovered,
              k,
              h
            ) {
              // 计算
              const midRatio = (startRatio + endRatio) / 2;

              const startRadian = startRatio * Math.PI * 2;
              const endRadian = endRatio * Math.PI * 2;
              const midRadian = midRatio * Math.PI * 2;

              // 如果只有一个扇形，则不实现选中效果。
              if (startRatio === 0 && endRatio === 1) {
                // eslint-disable-next-line no-param-reassign
                isSelected = false;
              }

              // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
              // eslint-disable-next-line no-param-reassign
              k = typeof k !== "undefined" ? k : 1 / 3;

              // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
              const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
              const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

              // 计算高亮效果的放大比例（未高亮，则比例为 1）
              const hoverRate = isHovered ? 1.05 : 1;

              // 返回曲面参数方程
              return {
                u: {
                  min: -Math.PI,
                  max: Math.PI * 3,
                  step: Math.PI / 32,
                },

                v: {
                  min: 0,
                  max: Math.PI * 2,
                  step: Math.PI / 20,
                },

                x(u, v) {
                  if (u < startRadian) {
                    return (
                      offsetX +
                      Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
                    );
                  }
                  if (u > endRadian) {
                    return (
                      offsetX +
                      Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
                    );
                  }
                  return (
                    offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
                  );
                },

                y(u, v) {
                  if (u < startRadian) {
                    return (
                      offsetY +
                      Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
                    );
                  }
                  if (u > endRadian) {
                    return (
                      offsetY +
                      Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
                    );
                  }
                  return (
                    offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
                  );
                },

                z(u, v) {
                  if (u < -Math.PI * 0.5) {
                    return Math.sin(u);
                  }
                  if (u > Math.PI * 2.5) {
                    return Math.sin(u) * h * 0.1;
                  }
                  // 当前图形的高度是Z根据h（每个value的值决定的）
                  return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
                },
              };
            }
            // 生成模拟 3D 饼图的配置项
            function getPie3D(pieData, internalDiameterRatio) {
              const series = [];
              // 总和
              let sumValue = 0;
              let startValue = 0;
              let endValue = 0;
              const legendData = [];
              let legend = [];
              const k =
                typeof internalDiameterRatio !== "undefined"
                  ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
                  : 1 / 3;

              // 为每一个饼图数据，生成一个 series-surface 配置
              for (let i = 0; i < pieData.length; i += 1) {
                sumValue += pieData[i].value;

                const seriesItem = {
                  name:
                    typeof pieData[i].name === "undefined"
                      ? `series${i}`
                      : pieData[i].name,
                  type: "surface",
                  parametric: true,
                  wireframe: {
                    show: false,
                  },
                  pieData: pieData[i],
                  pieStatus: {
                    selected: false,
                    hovered: false,
                    k,
                  },
                };

                if (typeof pieData[i].itemStyle !== "undefined") {
                  const { itemStyle } = pieData[i];

                  // eslint-disable-next-line no-unused-expressions
                  typeof pieData[i].itemStyle.color !== "undefined"
                    ? (itemStyle.color = pieData[i].itemStyle.color)
                    : null;
                  // eslint-disable-next-line no-unused-expressions
                  typeof pieData[i].itemStyle.opacity !== "undefined"
                    ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
                    : null;

                  seriesItem.itemStyle = itemStyle;
                }
                series.push(seriesItem);
              }
              // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
              // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
              for (let i = 0; i < series.length; i += 1) {
                endValue = startValue + series[i].pieData.value;

                series[i].pieData.startRatio = startValue / sumValue;
                series[i].pieData.endRatio = endValue / sumValue;
                series[i].parametricEquation = getParametricEquation(
                  series[i].pieData.startRatio,
                  series[i].pieData.endRatio,
                  false,
                  false,
                  k,
                  // 我这里做了一个处理，使除了第一个之外的值都是10
                  series[i].pieData.value === series[0].pieData.value ? 35 : 10
                );

                startValue = endValue;

                legendData.push(series[i].name);
              }

              // 准备待返回的配置项，把准备好的 legendData、series 传入。
              const option = {
                color: colors,
                // animation: false,
                tooltip: {
                  formatter: (params) => {
                    if (params.seriesName !== "mouseoutSeries") {
                      return `${
                        params.seriesName
                      }<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                        params.color
                      };"></span>${
                        option.series[params.seriesIndex].pieData.value
                      } ${option.series[params.seriesIndex].pieData.zb}%`;
                    }
                    return "";
                  },
                  textStyle: {
                    color: "#ffff",
                    fontSize: 24,
                  },
                  borderWidth: 0,
                  backgroundColor: "rgba(51, 51, 51, 0.7)",
                },
                legend: {
                  right: "0%",
                  top: "22%",
                  orient: "vertical",
                  textStyle: {
                    rich: {
                      name: {
                        fontSize: 30,
                        color: "#ffffff",
                        padding: [0, 0, 0, 15],
                      },
                      value: {
                        fontSize: 30,
                        color: "#e9d0ab",
                        padding: [10, 5, 0, 15],
                      },
                      value1: {
                        fontSize: 30,
                        color: "#e9d0ab",
                        padding: [10, 5, 0, 15],
                      },
                    },
                  },
                  formatter: function (name) {
                    var data = option.series; //获取series中的data
                    var total = 0;
                    var tarValue;
                    var zbValue;
                    for (var i = 0, l = data.length; i < l; i++) {
                      total += data[i].pieData.value;
                      if (data[i].pieData.name == name) {
                        tarValue = data[i].pieData.value;
                        zbValue = data[i].pieData.zb;
                      }
                    }
                    var p = ((tarValue / total) * 100).toFixed(2);
                    // return '{name|' + name + '}\n{value|' +p+"%  "+ tarValue + '件'+'}'
                    return (
                      "{name|" +
                      name +
                      "}\n{value1|" +
                      tarValue +
                      "}{value|" +
                      zbValue +
                      "%}"
                    );
                  },
                  // padding: [0, 600, 0, 200],
                },
                xAxis3D: {
                  min: -1,
                  max: 1,
                },
                yAxis3D: {
                  min: -1,
                  max: 1,
                },
                zAxis3D: {
                  min: -1,
                  max: 1,
                },
                grid3D: {
                  show: false,
                  z: 1,
                  boxHeight: 10,
                  top: "-10%",
                  left: "-25%",
                  viewControl: {
                    // 3d效果可以放大、旋转等，请自己去查看官方配置
                    alpha: 25,
                    // beta: 30,
                    rotateSensitivity: 1,
                    zoomSensitivity: 0,
                    panSensitivity: 0,
                    autoRotate: true,
                    distance: 190,
                  },
                  // 后处理特效可以为画面添加高光、景深、环境光遮蔽（SSAO）、调色等效果。可以让整个画面更富有质感。
                  postEffect: {
                    // 配置这项会出现锯齿，请自己去查看官方配置有办法解决
                    enable: false,
                    bloom: {
                      enable: true,
                      bloomIntensity: 0.1,
                    },
                    SSAO: {
                      enable: true,
                      quality: "medium",
                      radius: 2,
                    },
                    // temporalSuperSampling: {
                    //   enable: true,
                    // },
                  },
                },
                series,
              };
              return option;
            }
            myChart.setOption(option);
          },
        },
      });
    </script>
  </body>
</html>
