<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
  <title>面积测量微件</title>

  <link rel="stylesheet" href="https://dev.arcgisonline.cn/jsapi/4.24/esri/themes/light/main.css" />
  <!-- <script src="./libs/three-r79.min.js"></script> -->
  <script src="./index.js" type="module"></script>

  <style>
    html,
    body,
    #viewDiv {
      padding: 0;
      margin: 0;
      height: 100%;
      width: 100%;
    }

    .tools {
      position: absolute;
      top: 20px;
      right: 10px;
      display: flex;
    }

    .tools span {
      cursor: pointer;
      background-color: blue;
      width: 150px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 20px;
      color: white;
    }

    #area-box {
      position: absolute;
      top: 100px;
      right: 80px;
    }

    #distance-box {
      position: absolute;
      top: 100px;
      right: 80px;
    }
  </style>
</head>

<body>
  <div id="viewDiv">
    <div class="tools">
      <span onclick="mesureOnClick()">面积测量</span>
      <span onclick="mesureClearClick()">面积测量清除</span>
      <span onclick="distanceOnClick()">距离测量</span>
      <span onclick="distanceClearClick()">距离测量清除</span>
    </div>
    <div id="area-box"></div>
  </div>
</body>
<script type="text/javascript">
  let areaMeasurementWidget;

  function mesureOnClick() {
    areaMeasurementWidget = ArcGisUtils.createAreaMeasurementWidget(
      view,
      "area-box"
    );
  }

  function mesureClearClick() {
    areaMeasurementWidget.clear();
  }
  let distanceMeasurementWidget;

  function distanceOnClick() {
    distanceMeasurementWidget = ArcGisUtils.measuringDistance(
      view,
      "distance-box"
    );
  }

  function distanceClearClick() {
    distanceMeasurementWidget.clear();
  }
</script>

</html>