import Basemap from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/Basemap.js";
import { layerCreate } from "./core.js";
import { getLayerConfigById } from "./layerConfig.js";

/**
 * 天地图矢量底图 CGCS2000
 */
export function getTDTCGCS2000VectorLayer() {
  const lyr = layerCreate({
    type: "tile",
    url: "http://mps.arcgisonline.cn/tianditu/xmap2/Jinhua/ima/c/default/MapServer",
  });
  const baseMap = new Basemap({
    baseLayers: [lyr],
    title: "天地图矢量CGCS2000",
  });
  return baseMap;
}

/**
 * 天地图影像底图  CGCS2000
 */
export function getTDTCGCS2000ImageLayer() {
  const lyr = layerCreate({
    type: "tile",
    url: "http://mps.arcgisonline.cn/tianditu/xmap2/Jinhua/ima/c/default/MapServer",
  });
  const baseMap = new Basemap({
    baseLayers: [lyr],
    title: "天地图影像CGCS2000",
  });
  return baseMap;
}

/**
 * 天地图黑色底图  CGCS2000
 */
export function getTDTCGCS2000DarkLayer() {
  const lyr = layerCreate({
    type: "tile",
    url: "http://mps.arcgisonline.cn/tianditu/xmap2/Jinhua/vea/c/dark/MapServer",
  });
  const baseMap = new Basemap({
    baseLayers: [lyr],
    title: "天地图影像CGCS2000Dark",
  });
  return baseMap;
}

/**
 * 天地图市域底图  CGCS2000
 */
export function getTDTCGCS2000SYLayer() {
  const baseLayersConfig = getLayerConfigById("IMAGE_BASEMAP");
  const baseLayers = [];
  for (let i = 0; i < baseLayersConfig.length; i++) {
    const itemLayer = layerCreate(baseLayersConfig[i]);
    baseLayers.push(itemLayer);
  }

  let baseMap = new Basemap({
    baseLayers: baseLayers,
  });
  return baseMap;
}

/**
 *  矢量图
 */
export function getSzLightBasemap() {
  const baseLayersConfig = getLayerConfigById("VECTOR_BASEMAP");
  const baseLayers = [];
  for (let i = 0; i < baseLayersConfig.length; i++) {
    const itemLayer = layerCreate(baseLayersConfig[i]);
    baseLayers.push(itemLayer);
  }
  const baseMap = new Basemap({
    baseLayers: baseLayers,
    title: "底图",
  });
  return baseMap;
}
