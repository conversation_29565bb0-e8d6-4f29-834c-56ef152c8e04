<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Login</title>
    <script src="../js/ip.js"></script>
    <script src="../js/js_md5.js"></script>
    <script src="../js/qs.min.js"></script>
    <script src="../js/base64.min.js"></script>
    <link rel="stylesheet" href="../css/Login.css">
    <script src="../js/axios.min.js"></script>
</head>

<body>
    <div id="content">
        <div class="login">
            <h1>Login</h1>
            <input type="text" value="" placeholder="请输入用户名" id="username" class="item">
            <input type="password" value="" placeholder="请输入密码" id="password" class="item">
            <div id="code">
                <input type="text" value="" placeholder="请输入验证码" id="text" class="item">
                <br>
                <canvas id="canvas" width="100px" height="35px" onclick="checkCode()" style="margin-top:15px; border: 2px solid black;background: aqua;"></canvas>
                <br>
            </div>
            <button value="Login" class="log" onclick="check()">
                登录
            </button>
        </div>
    </div>
</body>
<script type="text/javascript">
    document.getElementById("code").style.display = "none";
    document.getElementById("username").value = '';
    document.getElementById("password").value = ''

    var check_code_flag = false;
    var token;
    getPCInfo()
    getInfo();



    function checkCode() {
        draw(check_code);
    }

    function getPCInfo(data) {
        axios({
            url: `${ip}/rest/index/unregistered/get_info`,
            method: "get",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            withCredentials: true
        }).then((res) => {
            var resInfo = JSON.parse(Base64.decode(res.data));
            console.log(resInfo, resInfo.data.play_versiton);
            if (resInfo.data.play_versiton == 'GB') {
                window.localStorage.setItem('isGB', 1)
            } else {
                window.localStorage.setItem('isGB', 0)
            }
            console.log(window.localStorage.getItem('isGB'), Boolean(Number(window.localStorage.getItem('isGB'))));
        })
    }

    function getInfo(data) {
        axios({
            url: `${ip}/rest/index/login/get?key=${data}`,
            method: "get",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            withCredentials: true
        }).then((res) => {
            var resInfo = JSON.parse(Base64.decode(res.data));
            console.log(resInfo);
            token = resInfo.data.page.token;
            if (resInfo.data.page.is_captcha == 1) {
                document.getElementById("code").style.display = "inline";
                check_code_flag = true;
                var check_code = [];
                draw(check_code);
            }
        })
    }

    function check() {
        var username = document.getElementById("username").value;
        var password = document.getElementById("password").value;

        var val = document.getElementById("text").value;
        if (check_code_flag) {
            var num = check_code.join("");
            if (val === '') {
                alert('请输入验证码！');
            } else if (val === num) {
                var password = md5(password);
                console.log(username, password);
                var key = '';
                var login_if = Base64.encode(JSON.stringify({
                    username,
                    password,
                    key
                }));
                console.log(login_if);
                var data = param_up({
                    login_info: login_if,
                    token,
                    captcha_code: val,
                    'withCredentials': true
                });
                console.log(data);
                axios({
                    url: `${ip}/rest/index/login/login`,
                    method: "POST",
                    header: {
                        "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
                    },
                    data: data,
                    withCredentials: true
                }).then((res) => {
                    console.log(res);
                    if (res.data.code == 200) {
                        window.localStorage.setItem("isLogin", 1);
                        window.location.href = "home.html";
                    } else {
                        alert(`${res.data.msg}`);
                    }
                })

            } else {
                alert('验证码错误！请重新输入！');
                document.getElementById("text").value = '';
                draw(check_code);
            }
        } else {
            var password = md5(password);
            console.log(username, password);
            var key = '';
            var login_if = Base64.encode(JSON.stringify({
                username,
                password,
                key
            }));
            console.log(login_if);
            var data = param_up({
                login_info: login_if,
                token,
                'withCredentials': true
            });
            console.log(data);
            axios({
                url: `${ip}/rest/index/login/login`,
                method: "POST",
                header: {
                    "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
                },
                data: data,
                withCredentials: true
            }).then((res) => {
                console.log(res);
                if (res.data.code == 200) {
                    window.localStorage.setItem("isLogin", 1);
                    window.location.href = "home.html";
                } else {
                    alert(`${res.data.msg}`);
                }
            })
        }

    }

    function param_up(param_arr) {
        var keys = Object.keys(param_arr).sort();
        var string = "";
        for (var i = 0; i < keys.length; i++) {
            var k = keys[i];
            string += k + "=" + param_arr[k] + ";";
        }
        string += md5("Pe2695jingyi");
        let str_encode = encodeURIComponent(string);
        //编码后MD5加密
        let str_md5 = md5(str_encode);
        param_arr.pe_signals = str_md5;
        return JSON.stringify(param_arr);
    }

    function draw(check_code) {
        const canvas = document.getElementById("canvas");
        var ctx = canvas.getContext("2d");
        canvas.width = document.getElementById('canvas').clientWidth;
        canvas.height = document.getElementById('canvas').clientHeight;
        var sCode = "A,B,C,E,F,G,H,J,K,L,M,N,P,Q,R,S,T,W,X,Y,Z,1,2,3,4,5,6,7,8,9,0,q,w,e,r,t,y,u,i,o,p,a,s,d,f,g,h,j,k,l,z,x,c,v,b,n,m";
        var aCode = sCode.split(",");
        var aLength = aCode.length; //获取到数组的长度

        for (var i = 0; i <= 3; i++) {
            var j = Math.floor(Math.random() * aLength); //获取到随机的索引值
            var deg = Math.random() * 30 * Math.PI / 180; //产生0~30之间的随机弧度
            var txt = aCode[j]; //得到随机的一个内容
            check_code[i] = txt;
            var x = 10 + i * 20; //文字在canvas上的x坐标
            var y = 20 + Math.random() * 8; //文字在canvas上的y坐标
            ctx.font = "bold 22px 微软雅黑";

            ctx.translate(x, y);
            ctx.rotate(deg);
            ctx.fillStyle = "#0000ff";
            ctx.fillText(txt, 0, 0);
            ctx.rotate(-deg);
            ctx.translate(-x, -y);
        }
    }
</script>

</html>