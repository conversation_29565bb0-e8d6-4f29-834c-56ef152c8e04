<!--
 * @Descripttion: 
 * @Author: <EMAIL>
 * @Date: 2022-12-11 19:06:29
-->
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
    <title>底图切换</title>

    <link rel="stylesheet" href="https://dev.arcgisonline.cn/jsapi/4.24/esri/themes/light/main.css" />
    <script src="./libs/three-r116.min.js"></script>
    <script src="./index.js" type="module"> </script>

    <style>
        html,
        body,
        #viewDiv {
            padding: 0;
            margin: 0;
            height: 100%;
            width: 100%;
        }

        .tools {
            position: absolute;
            top: 20px;
            left: 50%;
            width: 50%;
            height: 200px;
            display: flex;
        }

        .tools span {
            cursor: pointer;
            background-color: blue;
            width: 150px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 20px;
            color: white;
        }

        .description {
            position: absolute;
            right: 10px;
            top: 10px;
            background-color: white;
            border-radius: 5px;
            padding: 20px;
        }
    </style>
</head>

<body>
    <div id="viewDiv"></div>
    <div class="tools">
        <span onclick="ArcGisUtils.exchangeMap(view,'vector')">矢量底图</span>
        <span onclick="ArcGisUtils.exchangeMap(view,'image')">影像底图</span>
    </div>
    <div class="description">
        exchangeMap(view, type)
        <p>底图切换</p>
        <p>@param { MapView|SceneView } view 对象 必填</p>
        <p>@param {'vector'|'image'} type 底图类型 必填</p>
    </div>
</body>
</html>