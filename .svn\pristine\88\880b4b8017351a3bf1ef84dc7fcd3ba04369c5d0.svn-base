<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>图层-人员轨迹-时间选择</title>
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/elementui/js/index.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <style>
      .el-form-item {
        padding: 0 50px;
      }
      .el-form-item__label {
        line-height: 70px;
        font-size: 30px;
        color: #fff;
        white-space: nowrap;
      }
      .el-form-item__content {
        margin-left: 130px !important;
      }
      .el-date-editor--datetimerange.el-input,
      .el-date-editor--datetimerange.el-input__inner {
        width: 620px !important;
      }
      .el-input__icon {
        width: 35px;
      }
      .el-date-editor .el-range__icon {
        font-size: 25px;
        line-height: 60px;
      }
      .el-date-editor .el-range-separator {
        font-size: 28px;
        color: #fff;
        line-height: 56px;
      }
      .el-date-editor .el-range-input {
        background: transparent;
        font-size: 25px;
        color: #f0f0f0;
      }
      .el-input__inner {
        border: 2px solid #306972;
        border-radius: 10px;
        height: 70px;
        line-height: 65px;
        font-size: 28px;
        background: rgba(15, 43, 54, 0.8);
        width: 200px;
        color: #f0f0f0;
      }
      .el-input--small .el-input__inner {
        border: none !important;
        background: rgba(15, 43, 54, 0) !important;
        width: 145px !important;
        padding: 0 !important;
      }
      .el-date-range-picker__header div,
      .el-date-table th,
      .el-date-table {
        font-size: 30px;
      }
      .el-button--mini {
        font-size: 34px;
      }
      .el-button--default {
        background: #48a2ff;
        border-radius: 10px;
        border: none;
        color: #fff;
        padding: 10px 30px;
      }
      .el-button.is-disabled.is-plain,
      .el-button.is-disabled.is-plain:focus,
      .el-button.is-disabled.is-plain:hover {
        background: #48a2ff;
        color: #ebe7e7;
      }
      .el-form-item__content > .el-button {
        padding: 14px 20px;
        width: 94%;
        font-size: 30px;
        margin-left: -130px;
      }
      .el-picker-panel,
      .el-picker-panel__footer,
      .el-time-panel {
        border: 2px solid #409eff;
        background: rgba(15, 43, 54, 0.8);
      }
      .el-date-table td.in-range div,
      .el-date-table td.in-range div:hover,
      .el-date-table.is-week-mode .el-date-table__row.current div,
      .el-date-table.is-week-mode .el-date-table__row:hover div {
        background: transparent;
      }
      .el-date-table td span {
        width: 40px;
        height: 40px;
        line-height: 40px;
      }
      .el-time-spinner__item.active:not(.disabled) {
        color: #fff;
      }
      .el-time-spinner__item,
      .el-time-panel__btn {
        line-height: 28px;
        font-size: 25px;
        color: #545963;
      }
      .el-time-spinner__item:hover:not(.disabled):not(.active) {
        background: #0c3f8bb5;
      }
      .el-date-editor .el-range__close-icon {
        font-size: 25px;
        line-height: 60px;
        margin-left: 20px;
      }
      .el-scrollbar__wrap {
        overflow-y: scroll;
      }
      .el-scrollbar__wrap::-webkit-scrollbar {
        width: 6px;
      }
      .el-scrollbar__wrap::-webkit-scrollbar-thumb {
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: #00c0ff;
      }
      .el-scrollbar__wrap::-webkit-scrollbar-track {
        width: 5px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        border-radius: 10px;
        background: rgba(0, 192, 255, 0);
      }
      .el-date-range-picker__content .el-date-range-picker__header div,
      .el-date-table th {
        color: #d3d3d3;
      }
      .el-picker-panel__icon-btn:hover {
        color: #fff !important;
        font-weight: 700;
      }
      .el-picker-panel__icon-btn {
        font-size: 30px;
        color: #969494 !important;
        margin-top: 1px;
        float: right;
      }
      .el-date-table td.disabled div {
        background-color: #3e4045b0;
        color: #8a8d93;
      }
      .el-date-table td div {
        height: 40px;
        padding: 0px 0;
      }
      .el-time-spinner__wrapper {
        max-height: 220px;
      }
      .el-time-panel__content::after,
      .el-time-panel__content::before {
        top: 42%;
      }
      .el-time-spinner__item,
      .el-time-panel__btn {
        color: #81848b;
      }
      .el-date-range-picker {
        width: 800px;
      }
      /*   .el-date-table th {
        color: #fff;
        border-bottom: none;
        padding: 10px 0 25px;
      }
      .el-date-table td {
        padding: 20px 0;
      }
      .el-date-range-picker__header div {
        color: #000;
        font-weight: bold;
      }
      .el-date-range-picker__header {
        padding: 15px 0;
        background: #fff;
      }

      .el-date-range-picker__editors-wrap:nth-child(1) {
        position: relative;
        top: 130px;
        left: -140px;
      }
      .el-date-range-picker__editors-wrap.is-right {
        position: relative;
        top: 830px;
        left: -450px;
      }

      .el-picker-panel {
        width: 0;
        height: 0;
        color: #fff;
        background: transparent;
        box-shadow: none;
        -webkit-box-shadow: none;
        border: none;
      }
      .popper__arrow {
        display: none !important;
      }
      .el-date-range-picker__time-header {
        position: static;
        border-bottom: none;
      }
      .el-date-range-picker__time-header > .el-icon-arrow-right {
        display: none;
      }
      .el-date-range-picker .el-picker-panel__body {
        min-width: 0;
      }
      .el-date-range-picker__content {
        width: 765px;
      }
      .el-date-range-picker__content.is-left {
        position: relative;
        top: 120px;
        left: -140px;
        border-right: none;
      }
      .el-date-range-picker__content.is-right {
        position: relative;
        top: 205px;
        left: -140px;
        border-right: none;
      }
      .el-picker-panel__footer {
        border-top: none;
        background-color: transparent;
        width: 500px;
        display: flex;
        justify-content: space-between;
        position: absolute;
        top: 1500px;
      }

      .el-date-table td.in-range div,
      .el-date-table td.in-range div:hover,
      .el-date-table.is-week-mode .el-date-table__row.current div,
      .el-date-table.is-week-mode .el-date-table__row:hover div {
        background-color: #0c397cf5;
      }

      .el-date-table td span {
        width: 40px;
        height: 35px;
        line-height: 35px;
      }
      .el-date-editor .el-range__close-icon {
        font-size: 25px;
        line-height: 60px;
        margin-left: 20px;
      }
      .el-time-panel {
        background-color: #1c3441;
        border: none;
        border-radius: 20px;
        width: 230px;
      }
      .el-time-spinner {
        padding: 15px 0px;
      }
      .el-time-spinner__item {
        font-size: 25px;
        color: #9d9d9d;
      }
      .el-time-spinner__item.active:not(.disabled) {
        color: #fff;
      }

      .el-time-panel__content::after,
      .el-time-panel__content::before {
        top: 46%;
      }
      .el-time-panel__btn {
        font-size: 28px;
        color: #a7a7a7;
        margin: 0 12px;
      }
      .el-time-panel__footer {
        height: 45px;
      }
      .el-scrollbar__wrap {
        overflow-y: scroll;
      }
      .el-scrollbar__wrap::-webkit-scrollbar {
        width: 6px;
      }
      .el-scrollbar__wrap::-webkit-scrollbar-thumb {
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: #00c0ff;
      }
      .el-scrollbar__wrap::-webkit-scrollbar-track {
        width: 5px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        border-radius: 10px;
        background: rgba(0, 192, 255, 0);
      } */
    </style>
  </head>
  <body>
    <div id="left">
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="选择时间">
          <el-date-picker
            v-model="datas"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @click.native="autocompleteFocus"
            @change="initDate"
            value-format="yyyy-MM-dd HH:mm:ss"
            :key="autocompleteKey"
            :unlink-panels="true"
            :picker-options="setDate2"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <script>
      new Vue({
        el: "#left",
        data() {
          return {
            autocompleteKey: 0,
            showDateSelect: false,
            form: {},
            datas: [],
            setDate2: {
              disabledDate(time) {
                return time.getTime() > Date.now();
              },
            },
            zfryGjLayer: null,
            historyHostbody: null,
            county: null,
          };
        },
        mounted() {
          let this_ = this;
          window.addEventListener("message", (e) => {
            if (e.data && e.data.tcgl_ryglDate) {
              this_.historyHostbody = e.data.tcgl_ryglDate;
              this_.county = e.data.county;
            }
            if (e.data && e.data.gl_clear) {
              try {
                this_.zfryGjLayer.clear();
              } catch {}
            }
          });
        },
        methods: {
          autocompleteFocus(e) {
            console.log("触发", e);
            this.showDateSelect = !this.showDateSelect;
            if (
              this.showDateSelect ||
              document.getElementsByClassName("el-picker-panel")[0].style
                .display == ""
            ) {
              document.getElementsByClassName(
                "el-picker-panel"
              )[0].style.display = "block";
            } else {
              document.getElementsByClassName(
                "el-picker-panel"
              )[0].style.display = "none";
            }
          },
          initDate() {
            let startTime = new Date(this.datas[0]);
            let endTime = new Date(this.datas[1]);
            if (
              (endTime.getTime() - startTime.getTime()) / 1000 / (60 * 60) >=
              24
            ) {
              this.datas = [];
              this.$message({
                message: "轨迹查询仅支持24小时以内范围！",
                type: "warning",
              });
            }
          },
          onSubmit() {
            let this_ = this;
            if (this.datas.length == 0) return;
            // jobExecutor/zfy/getPaxGishistory?historyHostbody=T142510&startTime=2023-05-25 10:27:08&endTime=2023-05-25 17:27:08
            axios({
              method: "get",
              url: baseURL.url + "/jobExecutor/zfy/getGishistory",
              params: {
                historyHostbody: this_.historyHostbody,
                county: this_.county,
                startTime: this_.datas[0],
                endTime: this_.datas[1],
                // historyHostbody: "T142510",
                // startTime: "2023-05-25 10:27:08",
                // endTime: "2023-05-25 17:27:08",
              },
            }).then(function (res) {
              if (res.data.responsecode == 200) {
                if (res.data.data.data && res.data.data.gpsarray.length != 0  ) {
                  let glDatas = [];
                  res.data.data.gpsarray.map((a) => {
                    glDatas.push([a.lng, a.lat]);
                  });
                  window.parent.frames["tckz_tcgl3840"] &&
                    window.parent.frames["tckz_tcgl3840"].postMessage(
                      { glDatas: glDatas },
                      "*"
                    );
                  window.parent.frames["zhdd_right"] &&
                    window.parent.frames["zhdd_right"].postMessage(
                      { glDatas: glDatas },
                      "*"
                    );
                } else {
                  this_.$message({
                    message: "没有该用户当前时间内轨迹信息",
                    type: "warning",
                  });
                }
              }
            });
          },
        },
      });
    </script>
  </body>
</html>
