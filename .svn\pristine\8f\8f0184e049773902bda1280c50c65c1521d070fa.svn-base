<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>一键调度</title>
    <link rel="stylesheet" href="/static/fonts/iconfont.css" />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/Vue/vue.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/elementui/js/index.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/js/jslib/s.min.vue.js"></script>
  </head>

  <body>
    <div id="zhdddialog" style="top:1500px">
      <s-dialog
        title="一键调度"
        width="1800px"
        height="1000px"
        top="30px"
        left="25px"
      ></s-dialog>
      <div class="content">
        <div class="close" @click="close()">×</div>
        <div class="left">
          <p
            class="s-font-40 s-w7 s-c-blue-gradient title"
            id="DivBOx"
            style="cursor: all-scroll"
          >
            任务信息
            <span style="color:green">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;点我拖动</span>
          </p>
          <el-form
            ref="form"
            :model="form"
            label-width="170px"
            label-position="left"
          >
            <el-form-item label="任务来源 :">
              <div>
                <el-input
                  v-model="form.from"
                  placeholder="请输入任务来源"
                ></el-input>
              </div>
            </el-form-item>
            <el-form-item label="现场地址">
              <div class="s-flex" style="position: relative">
                <el-input
                  v-model="form.address"
                  @change="selectAddress"
                  placeholder="请输入地址查询"
                >
                </el-input>
                <el-button type="primary" @click="selectAddress">
                  <img src="/static/images/zhdd/ss.png" alt="" />
                </el-button>
                <div class="search_box" v-show="addressList.length!=0">
                  <span
                    @click="addPoint(e)"
                    v-for="e in addressList"
                    :title="e.dzqc||eqxmc+e.jdm+e.xq"
                  >
                    {{e.dzqc||eqxmc+e.jdm+e.xq}}
                  </span>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="范围半径(m)">
              <div class="s-flex">
                <el-input
                  type="number"
                  @input="changeInput"
                  :value="form.radius"
                ></el-input>
                <el-button @click="drawRound" @click="dispatchClick" style="margin-left: 10px">
                  <img src="/static/images/zhdd/yuan.png" alt=""
                /></el-button>
                <el-button @click="cleanRound">
                  <img src="/static/images/zhdd/clean.png" alt="" />
                </el-button>
              </div>
              <el-slider
                v-model="form.radius"
                :min="0"
                :max="10000"
                :show-tooltip="false"
                @change="changeSlider"
              ></el-slider>
            </el-form-item>
            <el-form-item label="响应级别 :">
              <el-select v-model="form.region" placeholder="请选择级别">
                <el-option
                  label="三级响应(一般)"
                  value="三级响应(一般)"
                ></el-option>
                <el-option
                  label="二级响应(中级)"
                  value="二级响应(中级)"
                ></el-option>
                <el-option
                  label="一级响应(最高级)"
                  value="一级响应(最高级)"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="内容描述">
              <el-input type="textarea" v-model="form.content"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <el-divider direction="vertical"></el-divider>
        <div class="right">
          <div class="s-flex">
            <p
              class="DefaultText title"
              :class="{ActiveText:tabChooseIndex == 0}"
              style="cursor: all-scroll;margin-right: 20px;"
              @click="tabChooseIndex = 0"
            >
              扁平指挥
            </p>
            <p
              class="DefaultText"
              :class="{ActiveText:tabChooseIndex == 1}"
              style="cursor: all-scroll"
              @click="tabChooseIndex = 1"
            >
              层级指挥
            </p>
          </div>
          <div v-show="tabChooseIndex == 0">
            <div class="tabs">
              <div
                class="tab_item"
                v-for="(item,index) in tabList"
                @click="changeTab(index)"
                :class="tabIndex==index?'tab_active':''"
              >
                {{item}}
              </div>
            </div>
            <div style="display: flex; margin-top: 25px">
              <div class="ryxx-box ryxx-left">
                <el-checkbox-group
                  v-model="zfryListChecked"
                  @change="handleCheckedCitiesChange"
                  v-show="tabIndex==0"
                >
                  <el-checkbox
                    v-for="item in zfryList"
                    :disabled="item.phone=='-'"
                    :label="item"
                  >
                    <div class="s-flex s-row-evenly">
                      <span>{{item.name}}</span>
                      <div class="ryBg" :class="{ryBg1:item.customizeDuties == '总指挥长',ryBg2:item.customizeDuties == '值班领导',ryBg3:item.customizeDuties == '值班长'}">{{item.customizeDuties}}</div>
                      <span
                        style="
                        color: #fff;
                        -webkit-text-fill-color: #fff;
                        font-size: 26px;
                      "
                      >{{item.lj}}</span
                      >
                    </div>
                  </el-checkbox>
                </el-checkbox-group>
                <el-tree
                  ref="tree1"
                  v-show="tabIndex==1"
                  :data="zfryData1"
                  show-checkbox
                  node-key="id"
                  lazy
                  :load="loadNode1"
                  :props="props1"
                  @check="handleCheckChange1"
                  :default-expanded-keys="defaultExpandedCids"
                >
                </el-tree>
                <el-tree
                  ref="tree2"
                  v-show="tabIndex==2"
                  :data="zfryData2"
                  show-checkbox
                  node-key="id"
                  lazy
                  :load="loadNode2"
                  :props="props2"
                  @check="handleCheckChange2"
                >
                </el-tree>
                <el-tree
                  ref="tree3"
                  v-show="tabIndex==3"
                  :data="zfryData3"
                  show-checkbox
                  node-key="id"
                  lazy
                  :load="loadNode3"
                  :props="props3"
                  @check="handleCheckChange3"
                >
                </el-tree>
              </div>
              <img
                class="box-img"
                src="/static/images/zhdd/zhdd3.png"
                alt=""
                @click="add()"
              />
              <div class="ryxx-box ryxx-right">
                <div class="itemcon" v-for="(item,i) in checkedData" :key="i">
                  <div class="box-name">{{item.name}}</div>
                  <div class="box-lj" v-if="item.lj">{{item.lj}}</div>
                  <div class="box-close" @click="del(item)" title="移除此条">
                    x
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-show="tabChooseIndex == 1">
            <div class="tabs">
              <div class="tab_item">周边县市区</div>
            </div>
            <div class="container">
              <el-radio-group v-model="labelName">
                <el-radio :label="item.deptName" v-for="(item,i) in departList" :key="i">{{item.deptName}}</el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>

        <div id="zhddSubmit" @click="onSubmit">发起</div>
      </div>
    </div>

    <script>
      var zhdd = new Vue({
        el: "#zhdddialog",
        data() {
          return {
            deptName:"",
            addressList: [],
            form: {
              from: "",
              region: "二级响应(中级)",
              address: null,
              radius: 1000,
              content: "",
              center_point: [],
              dataType: 2,
            },
            checkAll: false,
            zfryListChecked: [],
            zfryList: [],
            zfryData1: [],
            zfryData2: [],
            zfryData3: [],

            isIndeterminate: true,
            tabIndex: 0,
            tabList: [
              "周边执法人员",
              "镇街执法人员",
              "县级部门人员",
              "市级部门人员",
            ],
            checkedData: [],
            checkedData1: [],
            checkedData2: [],
            checkedData3: [],
            checkedData4: [],
            zjzfryList: [],
            xjbmryList: [],
            sjbmryList: [],
            props1: {
              children: "children",
              label: "name",
              isLeaf: "leaf",
            },
            props2: {
              label: "name",
              children: "children",
              isLeaf: "leaf",
            },
            props3: {
              label: "name",
              children: "children",
              isLeaf: "leaf",
            },
            defaultExpandedCids: [],
            node_had: [], // 触发 tree 的 :load=loadNode 重复触发  动态更新tree
            resolve_had: [], // 触发 tree 的 :load=loadNode 重复触发  动态更新tree
            node_had2: [],
            resolve_had2: [],
            node_had3: [],
            resolve_had3: [],
            gjxx: [],

            tabChooseIndex: 0,
            labelName:"",
            departList: [],
          };
        },
        mounted() {
          let this_ = this;
          this_.getDepartList()
          this_.getDepartment()
          window.addEventListener("message", (e) => {
            if (e.data && e.data.yjdd_clear) {
              this_.close();
            }
            if (e.data && e.data.yjdd_clear) {
              this_.close();
            }
            if (e.data.type == "openyjdd") {
              this_.form.dataType = 3;
              let result = e.data.data;
              this_.gjxx = e.data.data;
              if (result.id && result.yqmsg) {
                this_.form.from = "舆情中心";
                this_.form.content = this_.gjxx.yqmsg;
                return;
              }
              this_.form.from = result.city + "" + result.unitname + " SOS报警";
              this_.form.address = result.city + "" + result.unitname;
              this_.form.center_point = [result.lng, result.lat];
              window.parent.mapUtil.flyTo({
                destination: [result.lng, result.lat],
                zoom: 14,
              });
              setTimeout(() => {
                this_.node_had.childNodes = []; //把存起来的node的子节点清空，不然会界面会出现重复树！
                this_.loadNode1(this_.node_had, this_.resolve_had); //再次执行懒加载的方法
                this_.node_had2.childNodes = [];
                this_.loadNode2(this_.node_had2, this_.resolve_had2);
                this_.node_had3.childNodes = [];
                this_.loadNode3(this_.node_had3, this_.resolve_had3);
              }, 1000);
            }
          });
        },
        methods: {
          getDepartList() {
            $api2Get("/xzzfj/workNotice/getXzzfList",{area: localStorage.getItem("city")}).then(res => {
              if (res.data.code == 200) {
                this.departList = res.data.data
              }
            })
          },
          loadNode1(node, resolve) {
            let that = this;
            if (node.level === 0) {
              this.node_had = node; //这里是关键！在data里面定义一个变量，将node.level == 0的node存起来
              this.resolve_had = resolve; //同上，把node.level == 0的resolve也存起来
              return resolve([
                {
                  id: 1,
                  name: "金华市",
                },
              ]);
            }
            if (node.level == 1) {
              // that.form.address = '婺城区'
              console.log(that.form.address);
              if (!that.form.address) {
                that.$message.warning("请在左侧输入现场地址后查询!")
                return false;
              }
              let data = [];
              let list = [
                { name: "婺城区", id: 2 },
                { name: "金东区", id: 3 },
                { name: "兰溪市", id: 4 },
                { name: "东阳市", id: 5 },
                { name: "义乌市", id: 6 },
                { name: "永康市", id: 7 },
                { name: "武义县", id: 8 },
                { name: "浦江县", id: 9 },
                { name: "磐安县", id: 10 },
                { name: "开发区", id: 11 },
              ];
              list.forEach((item) => {
                if (that.form.address.indexOf(item.name) != -1) {
                  data.push(item);
                }
              });
              console.log(data);
              resolve(data);
            }
            if (node.level == 2) {
              // this.form.address = '婺城区'
              console.log(that.form.address);
              if (that.form.address) {
                let address = that.form.address;
                window.parent.mapUtil.findTask({
                  type: "street",
                  key: node.data.name, //这里传街道的名称
                  callback: (res) => {
                    console.log(res.features);
                    let data = [];
                    res.features.forEach((item) => {
                      let obj = {
                        name: item.properties.SZZ,
                        id: item.ADCODE,
                      };
                      data.push(obj);
                    });
                    resolve(data);
                  },
                });
              }
            }
            if (node.level == 3) {
              // xzzf_zfy
              $api("/xzzf_xzzfry", { street: node.data.name }).then((res) => {
                let data = [];
                res.forEach((item) => {
                  let obj = {
                    name: item.name,
                    phone: item.phone,
                    id: item.phone,
                    leaf: true,
                  };
                  data.push(obj);
                });
                resolve(data);
              });
            }
          },
          handleCheckChange1(a, val) {
            this.zjzfryList = [];
            this.zjzfryList = val.checkedNodes.filter((item) => {
              return item.leaf;
            });
          },
          loadNode2(node, resolve) {
            if (node.level === 0) {
              this.node_had2 = node; //这里是关键！在data里面定义一个变量，将node.level == 0的node存起来
              this.resolve_had2 = resolve; //同上，把node.level == 0的resolve也存起来
              if (this.form.address) {
                let data = [];
                let list = [
                  { name: "婺城区", id: 469 },
                  { name: "金东区", id: 2793 },
                  { name: "兰溪市", id: 5279 },
                  { name: "东阳市", id: 9768 },
                  { name: "义乌市", id: 14090 },
                  { name: "永康市", id: 19330 },
                  { name: "武义县", id: 37635 },
                  { name: "浦江县", id: 22157 },
                  { name: "磐安县", id: 41193 },
                ];
                list.forEach((item) => {
                  if (this.form.address.indexOf(item.name) != -1) {
                    data.push(item);
                  }
                });
                return resolve(data);
              }
            }
            if (node.level == 1) {
              axios({
                type: "get",
                url: baseURL.admApi + "/system/dept/list",
                params: { parentId: node.data.id },
                headers: {
                  "Content-Type": "application/json;charset=UTF-8",
                  Authorization: sessionStorage.getItem("Authorization"),
                  ptid: "PT0001",
                },
              }).then((res) => {
                let data = [];
                let result = res.data.data.filter((item) => {
                  return (
                    item.deptName.indexOf("政府") != -1 ||
                    item.deptName.indexOf("街道") != -1
                  );
                });
                result.forEach((item) => {
                  let obj = {
                    name: item.deptName,
                    id: item.deptId,
                  };
                  data.push(obj);
                });
                resolve(data);
              });
            }
            if (node.level > 1) {
              axios({
                type: "get",
                url: baseURL.admApi + "/system/dept/list",
                params: { parentId: node.data.id },
                headers: {
                  "Content-Type": "application/json;charset=UTF-8",
                  Authorization: sessionStorage.getItem("Authorization"),
                  ptid: "PT0001",
                },
              }).then((res) => {
                let data = [];
                if (res.data.data[0]) {
                  res.data.data.forEach((item) => {
                    let obj = {
                      name: item.deptName,
                      id: item.deptId,
                    };
                    data.push(obj);
                  });
                  resolve(data);
                } else {
                  axios({
                    type: "get",
                    url: baseURL.admApi + "/system/user/list",
                    params: { deptId: node.data.id },
                    headers: {
                      "Content-Type": "application/json;charset=UTF-8",
                      Authorization: sessionStorage.getItem("Authorization"),
                      ptid: "PT0001",
                    },
                  }).then((res) => {
                    res.data.rows.forEach((item) => {
                      let obj = {
                        name: item.nickName,
                        id: item.userId,
                        code: item.employeeCode,
                        leaf: true,
                      };
                      data.push(obj);
                    });
                    resolve(data);
                    console.log(data);
                  });
                }
              });
            }
          },
          handleCheckChange2(a, val) {
            // this.treeData = val
            this.xjbmryList = [];
            this.xjbmryList = val.checkedNodes.filter((item) => {
              return item.leaf;
            });
            console.log(this.xjbmryList);
          },
          loadNode3(node, resolve) {
            if (node.level === 0) {
              let list = [{ name: "金华市", id: 467 }];
              return resolve(list);
            }
            if (node.level === 1) {
              axios({
                type: "get",
                url: baseURL.admApi + "/system/dept/list",
                params: { parentId: node.data.id },
                headers: {
                  "Content-Type": "application/json;charset=UTF-8",
                  Authorization: sessionStorage.getItem("Authorization"),
                  ptid: "PT0001",
                },
              }).then((res) => {
                let data = [];
                let result = res.data.data.filter((item) => {
                  return (
                    item.deptName.indexOf("数字金华技术运营有限公司") == -1
                  );
                });
                result.forEach((item) => {
                  let obj = {
                    name: item.deptName,
                    id: item.deptId,
                  };
                  data.push(obj);
                });
                resolve(data);
              });
            }
            if (node.level === 2) {
              axios({
                type: "get",
                url: baseURL.admApi + "/system/dept/list",
                params: { parentId: node.data.id },
                headers: {
                  "Content-Type": "application/json;charset=UTF-8",
                  Authorization: sessionStorage.getItem("Authorization"),
                  ptid: "PT0001",
                },
              }).then((res) => {
                let data = [];
                let result = res.data.data;
                result.forEach((item) => {
                  let obj = {
                    name: item.deptName,
                    id: item.deptId,
                  };
                  data.push(obj);
                });
                resolve(data);
              });
            }
            if (node.level === 3) {
              axios({
                type: "get",
                url: baseURL.admApi + "/system/dept/list",
                params: { parentId: node.data.id },
                headers: {
                  "Content-Type": "application/json;charset=UTF-8",
                  Authorization: sessionStorage.getItem("Authorization"),
                  ptid: "PT0001",
                },
              }).then((res) => {
                let data = [];
                let county = [
                  "婺城区",
                  "金东区",
                  "兰溪市",
                  "东阳市",
                  "义乌市",
                  "永康市",
                  "浦江县",
                  "武义县",
                  "磐安县",
                ];
                let result = null;
                if (county.indexOf(node.data.name) != -1) {
                  result = res.data.data.filter((item) => {
                    return (
                      item.deptName.indexOf("政府") != -1  && item.deptId != 634 ||
                      item.deptName.indexOf("街道") != -1
                    );
                  });
                } else {
                  result = res.data.data;
                }
                result.forEach((item) => {
                  let obj = {
                    name: item.deptName,
                    id: item.deptId,
                  };
                  data.push(obj);
                });
                resolve(data);
              });
            }
            if (node.level > 3) {
              axios({
                type: "get",
                url: baseURL.admApi + "/system/dept/list",
                params: { parentId: node.data.id },
                headers: {
                  "Content-Type": "application/json;charset=UTF-8",
                  Authorization: sessionStorage.getItem("Authorization"),
                  ptid: "PT0001",
                },
              }).then((res) => {
                let data = [];
                if (res.data.data[0]) {
                  res.data.data.forEach((item) => {
                    let obj = {
                      name: item.deptName,
                      id: item.deptId,
                    };
                    data.push(obj);
                  });
                  resolve(data);
                } else {
                  axios({
                    type: "get",
                    url: baseURL.admApi + "/system/user/list",
                    params: { deptId: node.data.id },
                    headers: {
                      "Content-Type": "application/json;charset=UTF-8",
                      Authorization: sessionStorage.getItem("Authorization"),
                      ptid: "PT0001",
                    },
                  }).then((res) => {
                    res.data.rows.forEach((item) => {
                      let obj = {
                        name: item.nickName,
                        id: item.userId,
                        code: item.employeeCode,
                        leaf: true,
                      };
                      data.push(obj);
                    });
                    resolve(data);
                    console.log(data);
                  });
                }
              });
            }
          },
          /*loadNode3(node, resolve) {
            if (node.level === 0) {
              this.node_had3 = node; //这里是关键！在data里面定义一个变量，将node.level == 0的node存起来
              this.resolve_had3 = resolve; //同上，把node.level == 0的resolve也存起来
              if (this.form.address) {
                let data = [];
                let list = [
                  { name: "婺城区", id: 469 },
                  { name: "金东区", id: 2793 },
                  { name: "兰溪市", id: 5279 },
                  { name: "东阳市", id: 9768 },
                  { name: "义乌市", id: 14090 },
                  { name: "永康市", id: 19330 },
                  { name: "武义县", id: 37635 },
                  { name: "浦江县", id: 22157 },
                  { name: "磐安县", id: 41193 },
                ];
                /!*list.forEach((item) => {
                  if (this.form.address.indexOf(item.name) != -1) {
                    data.push(item);
                  }
                });*!/
                return resolve(list);
              }
            }
            if (node.level == 1) {
              axios({
                type: "get",
                url: baseURL.admApi + "/system/dept/list",
                params: { parentId: node.data.id },
                headers: {
                  "Content-Type": "application/json;charset=UTF-8",
                  Authorization: sessionStorage.getItem("Authorization"),
                  ptid: "PT0001",
                },
              }).then((res) => {
                let data = [];
                let result = res.data.data.filter((item) => {
                  return (
                          item.deptName.indexOf("政府") != -1 ||
                          item.deptName.indexOf("街道") != -1
                  );
                });
                result.forEach((item) => {
                  let obj = {
                    name: item.deptName,
                    id: item.deptId,
                  };
                  data.push(obj);
                });
                resolve(data);
              });
            }
            if (node.level > 1) {
              axios({
                type: "get",
                url: baseURL.admApi + "/system/dept/list",
                params: { parentId: node.data.id },
                headers: {
                  "Content-Type": "application/json;charset=UTF-8",
                  Authorization: sessionStorage.getItem("Authorization"),
                  ptid: "PT0001",
                },
              }).then((res) => {
                let data = [];
                if (res.data.data[0]) {
                  res.data.data.forEach((item) => {
                    let obj = {
                      name: item.deptName,
                      id: item.deptId,
                    };
                    data.push(obj);
                  });
                  resolve(data);
                } else {
                  axios({
                    type: "get",
                    url: baseURL.admApi + "/system/user/list",
                    params: { deptId: node.data.id },
                    headers: {
                      "Content-Type": "application/json;charset=UTF-8",
                      Authorization: sessionStorage.getItem("Authorization"),
                      ptid: "PT0001",
                    },
                  }).then((res) => {
                    res.data.rows.forEach((item) => {
                      let obj = {
                        name: item.nickName,
                        id: item.userId,
                        code: item.employeeCode,
                        leaf: true,
                      };
                      data.push(obj);
                    });
                    resolve(data);
                    console.log(data);
                  });
                }
              });
            }
          },*/
          handleCheckChange3(a, val) {
            // this.treeData = val
            this.sjbmryList = [];
            this.sjbmryList = val.checkedNodes.filter((item) => {
              return item.leaf;
            });
            console.log(this.sjbmryList);
          },
          del(item) {
            let that = this;
            console.log(item);
            console.log(that.checkedData);
            if (that.tabIndex == 0) {
              that.checkedData.forEach((m, n) => {
                if (item.id == m.id) {
                  that.checkedData.splice(n, 1);
                }
              });
              that.zfryListChecked.forEach((m, n) => {
                if (item.id == m.id) {
                  that.zfryListChecked.splice(n, 1);
                }
              });
            } else if (that.tabIndex == 1) {
              that.checkedData.forEach((m, n) => {
                if (item.id == m.id) {
                  that.checkedData.splice(n, 1);
                }
              });
              that.$refs.tree1.setChecked(item.id, false);
              that.zjzfryList.forEach((m, n) => {
                if (item.id == m.id) {
                  that.zjzfryList.splice(n, 1);
                }
              });
            } else if (that.tabIndex == 2) {
              that.checkedData.forEach((m, n) => {
                if (item.id == m.id) {
                  that.checkedData.splice(n, 1);
                }
              });
              that.$refs.tree2.setChecked(item.id, false);
              that.xjbmryList.forEach((m, n) => {
                if (item.id == m.id) {
                  that.xjbmryList.splice(n, 1);
                }
              });
            } else if (that.tabIndex == 3) {
              that.checkedData.forEach((m, n) => {
                if (item.id == m.id) {
                  that.checkedData.splice(n, 1);
                }
              });
              that.$refs.tree3.setChecked(item.id, false);
              that.sjbmryList.forEach((m, n) => {
                if (item.id == m.id) {
                  that.sjbmryList.splice(n, 1);
                }
              });
            }
          },
          add() {
            if (this.tabIndex == 0) {
              this.checkedData1 = [];
              this.checkedData1 = this.zfryListChecked;
            } else if (this.tabIndex == 1) {
              this.checkedData2 = [];
              this.checkedData2 = this.zjzfryList;
            } else if (this.tabIndex == 2) {
              this.checkedData3 = [];
              this.checkedData3 = this.xjbmryList;
            } else if (this.tabIndex == 3) {
              this.checkedData4 = [];
              this.checkedData4 = this.sjbmryList;
            }
            this.checkedData = this.checkedData1.concat(
              this.checkedData2,
              this.checkedData3,
              this.checkedData4
            );
            console.log(this.checkedData);
          },
          changeTab(index) {
            this.tabIndex = index;
            // if (this.tabIndex == 1) {
            //   if (this.form.address != null) {
            //     this.node_had.childNodes = []; //把存起来的node的子节点清空，不然会界面会出现重复树！
            //     this.loadNode1(this.node_had, this.resolve_had); //再次执行懒加载的方法
            //   }
            // } else if (this.tabIndex == 2) {
            //   if (this.form.address != null) {
            //     this.node_had2.childNodes = []; //把存起来的node的子节点清空，不然会界面会出现重复树！
            //     this.loadNode2(this.node_had2, this.resolve_had2); //再次执行懒加载的方法
            //   }
            // }else if (this.tabIndex == 3) {
            //   if (this.form.address != null) {
            //     this.node_had3.childNodes = []; //把存起来的node的子节点清空，不然会界面会出现重复树！
            //     this.loadNode3(this.node_had3, this.resolve_had3); //再次执行懒加载的方法
            //   }
            // }
          },
          addPoint(e) {
            this.form.address = e.dzqc;
            this.addressList = [];
            if (e.x) {
              this.form.center_point = [e.x, e.y];
              window.parent.mapUtil.flyTo({
                destination: [e.x, e.y], //飞行中心点
                zoom: 14, //飞行层级
              });
              let obj = {
                pointArr: [{ data: e, dzqc: e.dzqc, lng: e.x, lat: e.y }],
                layerId: "yjdd_point_address",
                icon: "zhdd_map_hdz",
                title: "地址详情",
                dict: { dzqc: "地址" },
              };
              this.addPoints(obj);
            }
          },
          selectAddress(val) {
            window.parent.mapUtil.removeLayer("yjdd_point_address");
            if (this.form.address == "" || val == "") {
              this.$message({
                message: "搜索内容不能为空",
                type: "warning",
              });
              return;
            }
            $post("/api2.0/solr-provider/api/data-sources/solr-search", {
              pageInfo: {
                current: 1,
                size: 300,
                totalSize: 0,
              },
              tableNames: "dmdz",
              text: val,
            }).then((res) => {
              this.addressList = res.data.data;
            });
            this.node_had.childNodes = []; //把存起来的node的子节点清空，不然会界面会出现重复树！
            this.loadNode1(this.node_had, this.resolve_had); //再次执行懒加载的方法
            this.node_had2.childNodes = [];
            this.loadNode2(this.node_had2, this.resolve_had2);
            this.node_had3.childNodes = [];
            this.loadNode3(this.node_had3, this.resolve_had3);
          },
          drawRound() {
            //下面这一行代码是获取父iframe的变量赋值
            parent.document.getElementById("zhddDispatch").style.top = "2200px";
            // window.parent.lay.closeIframeByNames(["zhddDispatch1"]);
            // window.postMessage(window.index,"zhdd_middle")
            let that = this;
            // 清除画圈
            window.parent.mapUtil.plotTool.close();
            window.parent.mapUtil.drawTool.clear();
            window.parent.mapUtil.removeAllLayers([
              "yjdd_point_address",
              "yjdd_point_zfry",
            ]);
            if (
              this.form.address &&
              this.form.address !== "" &&
              this.form.center_point !== []
            ) {
              window.parent.mapUtil.drawTool.draw("circle", {
                circles: [
                  {
                    center: this.form.center_point,
                    radius: this.form.radius, //m
                    fillColor: [255, 255, 255, 0.1],
                    strokeColor: [255, 255, 255, 1],
                  },
                ],
                layerid: "yjdd_draw_address",
              });
              this.getZFRY(this.form.center_point, this.form.radius / 1000);
            } else {
              window.parent.mapUtil.plotTool.active("circle", function (res) {
                that._onComplete(res); //km
              });

            }
          },
          _onComplete(res) {
            this.form.radius = parseInt(res.radius * 1000);
            this.form.center_point = [res.center.lng, res.center.lat];
            this.getZFRY(this.form.center_point, res.radius);
            //当画完圈时又回到之前的位置
            parent.document.getElementById("zhddDispatch").style.top = "380px";
          },
          getZFRY(point, radius) {
            let that = this;
            $api("/xzzf_zfy_new", {
              distance: radius,
              point: point[0] + "," + point[1],
            }).then((res) => {
              let datas = [];
              res.map((a) => {
                datas.push({
                  id: a.id || "",
                  name: a.name || "-",
                  phone: (a.phone && a.phone.toString()) || "-",
                  team_name: a.team_name || "-",
                  sys_depart: a.sys_depart || "-",
                  type: "zfry",
                  customizeDuties: a.customize_duties,
                  lng: a.lon,
                  lat: a.lat,
                  lj:
                    (
                      that.getFlatternDistance(
                        that.form.center_point[0],
                        that.form.center_point[1],
                        a.lon,
                        a.lat
                      ) / 1000
                    ).toFixed(2) + "km",
                });
              });
              this.zfryList = datas;
              let obj = {
                pointArr: datas,
                layerId: "yjdd_point_zfry",
                icon: "zhdd_zfry_static",
                title: "执法人员详情",
                dict: { name: "人员姓名", team_name: "所属支队" },
              };
              this.addPoints(obj);
            });
            // DWITHIN(the_geom, POINT(120.73440 29.22260), 0.01, kilometers)
            // let url = `/geoserver/poitest/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=poitest:zfry&maxFeatures=50000&outputFormat=application/json&cql_filter=DWITHIN(the_geom, POINT(${point[0]
            //   } ${point[1]}), ${radius / 111.325}, kilometers)`;
            // axios({
            //   method: "get",
            //   url: url,
            // }).then((res) => {
            //   if (res.data.features.length > 0) {
            //     let datas = [];
            //     res.data.features.map((a) => {
            //       datas.push({
            //         id: a.properties.id || "",
            //         name: a.properties.name || "-",
            //         phone:
            //           (a.properties.phone && a.properties.phone.toString()) ||
            //           "-",
            //         team_name: a.properties.team_name || "-",
            //         sys_depart: a.properties.sys_depart || "-",
            //         type: "zfry",
            //         lng: a.geometry.coordinates[0],
            //         lat: a.geometry.coordinates[1],
            //         lj:
            //           (
            //             that.getFlatternDistance(
            //               that.form.center_point[0],
            //               that.form.center_point[1],
            //               a.geometry.coordinates[0],
            //               a.geometry.coordinates[1]
            //             ) / 1000
            //           ).toFixed(2) + "km",
            //       });
            //     });
            //     this.zfryList = datas;
            //     let obj = {
            //       pointArr: datas,
            //       layerId: "yjdd_point_zfry",
            //       icon: "zhdd_zfry_static",
            //       title: "执法人员详情",
            //       dict: { name: "人员姓名", team_name: "所属支队" },
            //     };
            //     this.addPoints(obj);
            //   }
            // });
          },
          addPoints(params = {}) {
            const { pointArr, layerId, icon, title, dict } = params;
            window.parent.mapUtil.removeLayer(layerId);
            window.parent.mapUtil.loadPointLayer({
              data: pointArr, //点位数据
              layerid: layerId, //图层id
              iconcfg: {
                image: `/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${icon}.png`,
                iconSize: 0.6,
              }, //图标可绝对地址
              popcfg: {
                title: title,
                dict: dict,
                offset: [50, -100],
                show: false, //关闭按钮
              },
              cluster: false,
            });
          },
          cleanRound() {
            // 清除画圈
            window.parent.mapUtil.plotTool.close();
            window.parent.mapUtil.drawTool.clear();
            this.zfryList = [];
            window.parent.mapUtil.removeAllLayers([
              "yjdd_point_address",
              "yjdd_point_zfry",
            ]);
          },
          handleCheckAllChange(val) {
            this.zfryListChecked = val ? this.zfryList : [];
            this.isIndeterminate = false;
          },
          handleCheckedCitiesChange(value) {
            let checkedCount = value.length;
            this.checkAll = checkedCount === this.zfryList.length;
            this.isIndeterminate =
              checkedCount > 0 && checkedCount < this.zfryList.length;

            // if (value.length > 0) {
            //   let onceData = value[value.length - 1];
            //   window.parent.mapUtil.flyTo({
            //     destination: [onceData.lng, onceData.lat], //飞行中心点
            //     zoom: 16, //飞行层级
            //   });
            // }
            console.log(this.zfryListChecked);
          },
          changeInput(e) {
            this.form.radius = Number(e);
          },
          changeSlider(e) {},
          getDepartment() {
            $api2Get("/system/dept/getUserDept").then(res => {
              if (res.data.code == 200) {
                this.deptName = res.data.data.deptName
              }
            })
          },
          // 发起命令
          onSubmit() {
            let this_ = this;
            console.log({ zfry: this_.zfryListChecked });
            if (this_.form.content == "") {
              this_.$message({
                message: "指令内容不能为空",
                type: "warning",
              });
              return;
            }
            if (this_.form.region == "") {
              this_.$message({
                message: "请选择任务级别",
                type: "warning",
              });
              return;
            }
            if (this_.checkedData.length == 0 && this_.tabChooseIndex == 0) {
              this_.$message({
                message: "请至少选择一人",
                type: "warning",
              });
              return;
            }
            if (this_.labelName == "" && this_.tabChooseIndex == 1) {
              this_.$message({
                message: "请至少选择一个队伍",
                type: "warning",
              });
              return;
            }
            // let arr1 = this_.checkedData1.concat(this_.checkedData2);
            // let arr2 = this_.checkedData3
            let arr = this_.checkedData;
            // let phones = arr.map((a) => a.phone);
            const plist = arr.map((item) => {
              return {
                // codeAndPhone: item.phone,
                codeAndPhone: item.code?item.code:item.phone,
                type: item.code?"2":"1",
                name: item.name,
              };
            });
            let params = {
              source: "jinhuacsdn",
              list: plist,
              area: localStorage.getItem("adminCity"),
              level: this_.form.region,
              type: 2,
              msg: this_.form.content,
              taskSource: this_.form.from,
              dataType: this_.form.dataType,
              detailsId: this_.gjxx.id || null,
              url: "https://aijinhua.dsjj.jinhua.gov.cn:5443/xzzfzx/#/",
              deptName: this_.deptName
            };
            let params2 = {
              source: "jinhuacsdn",
              targetDeptName: this_.labelName,
              taskAddress: this_.form.address,
              area: localStorage.getItem("adminCity"),
              level: this_.form.region,
              type: 2,
              msg: this_.form.content,
              taskSource: this_.form.from,
              dataType: this_.form.dataType,
              detailsId: this_.gjxx.id || null,
              url: "https://aijinhua.dsjj.jinhua.gov.cn:5443/xzzfzx/#/",
              deptName: this_.deptName
            };
            if (this_.tabChooseIndex == 0) {
              axios({
                method: "post",
                url: baseURL.url + "/adm-api/pub/dingtalk/xzzfjWorkNotificationNew",
                data: params,
              }).then(function (response) {
                if (response.data.code == 200) {
                  console.log(response);
                  this_.$message({
                    message: response.data.msg,
                    type: "success",
                  });
                  window.parent.frames["zhdd_right"] &&
                  window.parent.frames["zhdd_right"].postMessage(
                    {
                      type: "refresh",
                      data: this_.gjxx,
                    },
                    "*"
                  );
                } else {
                  this_.$message({
                    message: response.data.msg,
                    type: "warning",
                  });
                }
              });
            } else {
              $api2Post("/xzzfj/workNotice/xzzfjWorkNotificationArea",params2).then(response => {
                if (response.data.code == 200) {
                  console.log(response);
                  this_.$message({
                    message: response.data.msg,
                    type: "success",
                  });
                  window.parent.frames["zhdd_right"] &&
                  window.parent.frames["zhdd_right"].postMessage(
                    {
                      type: "refresh",
                      data: this_.gjxx,
                    },
                    "*"
                  );
                } else {
                  this_.$message({
                    message: response.data.msg,
                    type: "warning",
                  });
                }
              })
            }

            // axios({
            //   method: "post",
            //   url: baseURL.url + "/adm-api/pub/dingtalk/workNotification",
            //   data: {
            //     source: "jinhuacsdn",
            //     phones: phones,
            //     msg: this.form.content,
            //   },
            // }).then(function (response) {
            //   if (response.data.code == 200) {
            //     console.log(response);
            //     this_.$message({
            //       message: response.data.msg,
            //       type: "success",
            //     });
            //   } else {
            //     this_.$message({
            //       message: response.data.msg,
            //       type: "warning",
            //     });
            //   }
            // });
            console.log(this_.form);
          },
          getFlatternDistance(lat11, lng12, lat21, lng22) {
            const lat1 = Number(lat11);
            const lng1 = Number(lng12);
            const lat2 = Number(lat21);
            const lng2 = Number(lng22);
            const PI = Math.PI;
            const EARTH_RADIUS = 6378137.0;

            function getRad(d) {
              return (d * PI) / 180.0;
            }

            let f = getRad((lat1 + lat2) / 2);
            let g = getRad((lat1 - lat2) / 2);
            let l = getRad((lng1 - lng2) / 2);
            let sg = Math.sin(g);
            let sl = Math.sin(l);
            let sf = Math.sin(f);

            let s, c, w, r, d, h1, h2;
            let a = EARTH_RADIUS;
            let fl = 1 / 298.257;

            sg = sg * sg;
            sl = sl * sl;
            sf = sf * sf;

            s = sg * (1 - sl) + (1 - sf) * sl;
            c = (1 - sg) * (1 - sl) + sf * sl;

            w = Math.atan(Math.sqrt(s / c));
            r = Math.sqrt(s * c) / w;
            d = 2 * w * a;
            h1 = (3 * r - 1) / 2 / c;
            h2 = (3 * r + 1) / 2 / s;

            return d * (1 + fl * (h1 * sf * (1 - sg) - h2 * (1 - sf) * sg));
          },
          close() {
            // 清除画圈
            try {
              window.parent.mapUtil.plotTool.close();
              window.parent.mapUtil.drawTool.clear();
              window.parent.mapUtil.removeAllLayers([
                "yjdd_point_address",
                "yjdd_point_zfry",
              ]);
            } catch {}
            window.parent.frames["zhdd_middle"].vmMiddle.dispatchStatus = false;
            window.parent.lay.closeIframeByNames(["zhddDispatch"]);
          },
        },
      });
      let oDiv = document.getElementById("DivBOx"); // 当前元素
      // 禁止选择网页上的文字
      document.onselectstart = function () {
        return false;
      };
      oDiv.onmousedown = function (e) {
        // 鼠标按下，计算当前元素距离可视区的距离
        let disX = e.clientX - oDiv.offsetLeft;
        let disY = e.clientY - oDiv.offsetTop;
        document.onmousemove = function (e) {
          // 通过事件委托，计算移动的距离
          let l =
            e.clientX -
            disX +
            Number(
              window.parent
                .$("iframe[name='zhddDispatch']")
                .css("left")
                .replace("px", "")
            );
          let t =
            e.clientY -
            disY +
            Number(
              window.parent
                .$("iframe[name='zhddDispatch']")
                .css("top")
                .replace("px", "")
            );
          // 移动当前元素
          parent.document.getElementById("zhddDispatch").style.left = l + "px";
          parent.document.getElementById("zhddDispatch").style.top = t + "px";
        };
        document.onmouseup = function (e) {
          document.onmousemove = null;
          document.onmouseup = null;
        };
        document.onmouseleave = function (e) {
          document.onmousemove = null;
          document.onmouseup = null;
        };
      };
    </script>

    <style>

      .DefaultText {
          color: #88a6c3;
          font-size: 36px;
          cursor: pointer !important;
      }

      .ActiveText {
          font-size: 40px;
          font-weight: 700;
          background: linear-gradient(to bottom, #ccf4ff, #ffffff, #00baf8, #ffffff);
          -webkit-background-clip: text;
          color: transparent;
      }

      .container {
          margin-top: 25px;
          width: 935px;
          height: 550px;
          background: rgba(10, 97, 158, 0.2);
          border-radius: 10px;
          border: 1px solid #004477;
          padding: 20px;
          box-sizing: border-box;
      }

      .el-radio__label {
         color: #ffffff;
          font-size: 26px;
      }

      .el-radio-group {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: flex-start;
          flex-wrap: wrap;
          height: 100%;
      }

      .el-radio {
          margin-bottom: 26px;
      }

      .ryBg {
          width: 147px;
          height: 36px;
          line-height: 36px;
          background-size: cover;
          text-align: center;
          font-size: 22px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-style: italic;
          color: rgba(255, 255, 255, 0.8);
      }

      .ryBg1 {
          background: url("../../../images/zhdd/中队长.png");
      }

      .ryBg2 {
          background: url("../../../images/zhdd/值班领导.png");
      }

      .ryBg3 {
          background: url("../../../images/zhdd/值班人员.png");
      }

      * {
        margin: 0;
        padding: 0;
      }

      body {
        height: max-content;
        position: relative;
      }

      .content {
        position: absolute;
        padding: 0 60px;
        top: 140px;
        width: 1790px;
        height: 720px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
      }

      .close {
        position: absolute;
        top: -95px;
        right: 50px;
        width: 46px;
        height: 78px;
        font-size: 60px;
        color: #fff;
        font-weight: 600;
        cursor: pointer;
      }

      .title::before {
        content: "";
        display: inline-block;
        background: url(/static/images/zhdd/icon_cir.png) no-repeat;
        width: 19px;
        height: 19px;
        margin-right: 8px;
      }

      .el-form {
        margin-top: 50px;
      }

      .el-form-item {
        margin-bottom: 30px;
      }

      .el-form-item__label,
      .el-checkbox__input.is-checked + .el-checkbox__label {
        font-size: 26px;
        color: #fff;
        line-height: 60px;
        /* background-image: linear-gradient(180deg, #00c0ff, #afdfff, #00c0ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent; */
      }

      .el-form-item__label::before {
        /* content: "";
        display: inline-block;
        background: url(/static/images/zhdd/icon_cir.png) no-repeat;
        width: 19px;
        height: 19px;
        margin-right: 8px; */
      }

      .el-input__inner {
        height: 60px;
        font-size: 28px;
        color: #fff;
        border: 1px solid #004477;
        background: rgba(10, 97, 158, 0.2);
        box-shadow: none;
        color: #fefefe;
      }

      .el-textarea__inner {
        resize: none;
        font-size: 28px;
        color: #fff;
        height: 200px;
        border: 1px solid #004477;
        background: rgba(10, 97, 158, 0.2);
      }

      .ryxx-box::-webkit-scrollbar,
      .el-textarea__inner::-webkit-scrollbar,
      .search_box::-webkit-scrollbar,
      .el-checkbox-group::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 5px;
        /*高宽分别对应横竖滚动条的尺寸*/
      }

      .ryxx-box::-webkit-scrollbar-thumb,
      .el-textarea__inner::-webkit-scrollbar-thumb,
      .search_box::-webkit-scrollbar-thumb,
      .el-checkbox-group::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 10px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        background: #00c0ff;
      }

      .ryxx-box::-webkit-scrollbar-thumb,
      .el-textarea__inner::-webkit-scrollbar-track,
      .search_box::-webkit-scrollbar-track,
      .el-checkbox-group::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        width: 5px;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
        border-radius: 10px;
        background: rgba(0, 192, 255, 0.5);
      }

      .el-button {
        width: 60px;
        font-size: 30px;
        padding: 10px 12px;
        border-radius: 10%;
        cursor: pointer;
        background: linear-gradient(#3476eb, #033faa);
      }

      [class*=" el-icon-"],
    [class^=el-icon-]

    /*这里有空格*/ {
        /* font-family: "iconfont" !important; */
        font-size: 32px;
        color: #fff;
        font-style: normal;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      .el-icon-aim:before {
        content: "\e629";
      }

      .el-icon-delete-solid:before {
        content: "\e60d";
      }

      .el-icon-search:before {
        content: "\e61a";
      }

      .el-form .el-form-item:nth-child(2) .el-button {
        background: #00396f;
        border-color: transparent;
      }

      .el-button--primary:focus,
      .el-button--primary:hover {
        background: #00396f !important;
      }

      .el-form .el-form-item:nth-child(2) .el-button:focus,
      .el-button:hover {
        background: rgba(4, 96, 182, 0.884) !important;
      }

      .tab_active {
        padding: 3px 15px 5px;
        width: max-content;
        color: #fff;
        font-size: 32px;
        font-weight: 500;
        background: url("/static/images/xzzfj/tab_bg.png");
        background-size: 100% 100%;
      }

      .el-checkbox {
        font-size: 20px;
        color: #aaadb3 !important;
        height: 60px;
        line-height: 60px;
      }

      .el-checkbox-group {
        font-size: 0;
        display: flex;
        flex-direction: column;
        max-height: 510px;
        overflow-y: scroll;
        overflow-x: hidden;
      }

      .el-checkbox__label {
        display: inline-block;
        width: 365px;
        font-size: 28px;
        line-height: inherit;
      }

      .el-checkbox__inner {
        width: 20px;
        height: 20px;
      }

      .el-checkbox__input {
        line-height: 2;
      }

      .el-checkbox__inner::after {
        height: 12px;
        left: 8px;
      }

      .el-input {
        font-size: 26px;
      }

      .el-input__inner {
        height: 60px;
        line-height: 60px;
        background-color: #052347;
        border-color: #314662 !important;
        border-radius: 5px;
        color: #fff;
      }

      .el-input--suffix .el-input__inner {
        padding-right: 130px;
      }

      .el-select .el-input .el-select__caret {
        color: #fff;
        font-size: 26px;
        margin-right: 10px;
        line-height: 60px;
      }

      .el-select-dropdown {
        background-color: #052347;
        border-color: #314662 !important;
      }

      .el-select-dropdown__item.hover,
      .el-select-dropdown__item:hover {
        border-color: #409eff;
        background-color: #052347;
        color: #409eff !important;
      }

      .el-select-dropdown__item.selected {
        color: #409eff;
        font-weight: normal;
      }

      .el-select-dropdown__item {
        font-size: 26px;
        color: #fff;
        height: 50px;
        line-height: 50px;
      }

      .popper__arrow {
        display: none !important;
      }

      .search_box {
        padding: 2px 10px;
        position: absolute;
        top: 60px;
        left: 0px;
        z-index: 1002;
        width: 400px;
        max-height: 400px;
        overflow-y: scroll;
        line-height: 29px;
        border: 1px solid #004477;
        background: rgba(3, 42, 88, 0.9);
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        border-radius: 4px;
      }

      /* .search_box::before {
        position: absolute;
        top: -20px;
        left: 10%;
        content: "";
        display: inline-block;
        border-color: transparent transparent #004477 transparent;
        border-width: 10px;
        border-style: solid;
      } */
      .search_box > span {
        cursor: pointer;
        display: inline-block;
        font-size: 24px;
        width: 100%;
        color: #b8b5b5;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .search_box > span:hover {
        color: #fff;
      }

      #zhddSubmit {
        position: absolute;
        top: 750px;
        left: calc(50% - 120px);
        text-align: center;
        color: #fff;
        width: 240px;
        height: 60px;
        line-height: 60px;
        cursor: pointer;
        background: url(/static/images/zhdd/addGroup.png) no-repeat;
        background-size: 100% 100%;
        font-size: 38px;
      }

      .left {
        width: 40%;
      }

      .right {
        width: 56%;
      }

      .el-divider--vertical {
        height: 100%;
      }

      .el-divider {
        background-color: #00c0ff;
      }

      .el-tree {
        background: none;
        color: #fff;
      }

      .el-tree-node__content {
        height: 45px;
        line-height: 45px;
      }

      .el-tree-node__content:hover {
        background: none;
      }

      .el-tree-node.is-current > .el-tree-node__content {
        background: none !important;
      }

      .el-tree-node.is-focusable > .el-tree-node__content {
        background: none !important;
      }

      .el-tree-node__label {
        font-size: 28px;
        color: #aaadb3;
      }

      .tabs {
        width: 100%;
        height: 60px;
        line-height: 60px;
        display: flex;
        color: #abceef;
        font-size: 32px;
        margin-top: 25px;
      }

      .tab_item {
        padding: 0 20px;
        white-space: nowrap;
      }

      .ryxx-box {
        background: rgba(10, 97, 158, 0.2);
        border-radius: 10px;
        border: 1px solid #004477;
        padding: 20px;
        box-sizing: border-box;
      }

      .ryxx-left {
        width: 550px;
        height: 550px;
        overflow-y: scroll;
      }

      .ryxx-right {
        width: 400px;
        height: 550px;
        overflow-y: scroll;
      }

      .box-img {
        width: 72px;
        height: 121px;
        margin: 216px 20px;
        cursor: pointer;
      }

      .itemcon {
        width: 100%;
        height: 60px;
        line-height: 60px;
        display: flex;
        justify-content: space-between;
        font-size: 26px;
        color: #b8d3f1;
        position: relative;
        padding-right: 40px;
        box-sizing: border-box;
      }

      .box-close {
        cursor: pointer;
        color: #fff;
        position: absolute;
        right: 0;
      }

      .node {
        font-size: 28px;
        color: #aaadb3;
      }

      .node-img {
        width: 25px;
        height: 25px;
        background: #cde7fe;
        border-radius: 6px;
        display: inline-block;
        line-height: 25px;
        text-align: center;
        content: "+";
      }
    </style>
  </body>
</html>
