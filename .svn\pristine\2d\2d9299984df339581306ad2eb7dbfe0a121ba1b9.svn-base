<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
    <title>效果</title>

    <link rel="stylesheet" href="https://dev.arcgisonline.cn/jsapi/4.24/esri/themes/light/main.css" />
    <script src="./libs/three-r116.min.js"></script>
    <script src="./libs/three-r123.min.js"></script>
    <script src="./libs/FBXLoader.r116.js"></script>
    <script src="./index.js" type="module"> </script>

    <style>
        html,
        body,
        #viewDiv {
            padding: 0;
            margin: 0;
            height: 100%;
            width: 100%;
        }

        .tools {
            position: absolute;
            top: 20px;
            left: 50%;
            width: 50%;
            height: 200px;
            display: flex;
        }

        .tools span {
            cursor: pointer;
            background-color: blue;
            width: 150px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 20px;
            color: white;
        }
    </style>

    <script>
         const locations = [{
                x: 119.64918672315562,
                y: 29.087981861943554
            }];
        function addFireEffect() {
            ArcGisUtils.addFireEffect(locations)
        }

        function addFireSmokeEffect(){
            ArcGisUtils.addFireSmokeEffect(locations);
        }

        function addFountainEffect(){
            ArcGisUtils.addFountainEffect(locations);
        }
    </script>

</head>

<body>
    <div id="viewDiv">
        <div class="tools">

            <span onclick="addFireEffect()">火焰</span>
            <span onclick="ArcGisUtils.removeFireEffect()">清除火焰</span>
            <span onclick="addFireSmokeEffect()">烟雾</span>
            <span onclick="ArcGisUtils.removeFireSmokeEffect()">移除烟雾</span>
            <span onclick="addFountainEffect()">喷泉</span>
            <span onclick="ArcGisUtils.removeFountainEffect()">移除喷泉</span>
        </div>
    </div>
</body>

</html>