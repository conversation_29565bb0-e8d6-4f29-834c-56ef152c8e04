import { heatMapData } from "./heatMapData.js";
import Heatmap3D from "./heatmap.js";
import EsriODLineLayer from "./esriODLineLayer.js";
import * as externalRenderers from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/views/3d/externalRenderers.js";
import { getLayerConfigById } from "../layerConfig.js";
import WallLayer from "./WallLayer.js";
import ScanRadar from "./ScanRadar.js";
import FireEmergencyTHREERenderer from './Fire/FireEmergencyTHREERenderer.js'
import FireSmokeTHREERenderer from './FireSmoke/FireSmokeEffect.js'
import FountainTHREERenderer from "./FountainTHREERenderer.js";

let heat3dEffect = null;
let lineLayerRenders = null;
let fireRenders = null;
let fireSmokeRenders = null;
let fountainRenders = null;
let dynamicWallRenders = []; //
let scanRadarEffect=null;

export const addHeatMap = function () {
  if (!heat3dEffect) {
    heat3dEffect = new Heatmap3D(window.view, {
      externalRenderers,
      pixelValueArray: heatMapData.data,
      extent: {
        xmax: 120.234458,
        xmin: 119.3300629,
        ymax: 29.5424113,
        ymin: 28.8343048,
      },
      overstate: 1200,
    });
    externalRenderers.add(window.view, heat3dEffect);
  }
};

export function removeHeatmap() {
  if (heat3dEffect) {
    externalRenderers.remove(window.view, heat3dEffect);
    heat3dEffect = null;
  }
}

//  ------------------------------------------交通流光图--------------------------------------------------------

export function addLineEffect(url = getLayerConfigById("RailwayLine").url) {
  if (lineLayerRenders === null) {
    lineLayerRenders = new EsriODLineLayer(window.view, {
      queryUrl: url,
      color: "#ffb506",
      size: 5, //宽度
      length: 0.2, //<1
      speed: 0.1, //<1
      isShow: true, //是否可见道路线
    });

    externalRenderers.add(view, lineLayerRenders);
  } else {
    return;
  }
}

export function removeLineEffect() {
  if (lineLayerRenders) {
    externalRenderers.remove(window.view, lineLayerRenders);
    lineLayerRenders = null;
  }
}



//  ------------------------------------------火焰特效--------------------------------------------------------
export function addFireEffect(locations) {
  if (fireRenders === null) {
    fireRenders = new FireEmergencyTHREERenderer();
    fireRenders.view = view;
    fireRenders.locations = locations;
    fireRenders.externalRenderers = externalRenderers;
    externalRenderers.add(window.view, fireRenders);
  } else {
    return;
  }
}

export function removeFireEffect() {
  if (fireRenders) {
    externalRenderers.remove(window.view, fireRenders);
    fireRenders = null;
  }
}

//  ------------------------------------------烟雾特效--------------------------------------------------------
export function addFireSmokeEffect(locations) {
  if (fireSmokeRenders === null) {
    fireSmokeRenders = new FireSmokeTHREERenderer();
    fireSmokeRenders.view = view;
    fireSmokeRenders.locations = locations;
    fireSmokeRenders.externalRenderers = externalRenderers;
    externalRenderers.add(window.view, fireSmokeRenders);
  } else {
    return;
  }
}

export function removeFireSmokeEffect() {
  if (fireSmokeRenders) {
    externalRenderers.remove(window.view, fireSmokeRenders);
    fireSmokeRenders = null;
  }
}

//  ------------------------------------------喷泉特效--------------------------------------------------------
export function addFountainEffect(locations) {
  if (fountainRenders === null) {
    fountainRenders = new FountainTHREERenderer();
    fountainRenders.view = view;
    fountainRenders.locations = locations;
    fountainRenders.externalRenderers = externalRenderers;
    externalRenderers.add(window.view, fountainRenders);
  } else {
    return;
  }
}

export function removeFountainEffect() {
  if (fountainRenders) {
    externalRenderers.remove(window.view, fountainRenders);
    fountainRenders = null;
  }
}






//#region 动态墙体

export function addDynamicWall({ view, points, height }) {
  for (var i = 1; i < points.length; i++) {
    const line = [points[i - 1], points[i]];
    const itemWallRender = new WallLayer({ view, points, height });
    externalRenderers.add(window.view, itemWallRender);
    dynamicWallRenders.push(itemWallRender);
  }
}

export function removeDynamicWall() {
  for (let i = 0, len = dynamicWallRenders.length; i < len; i++) {
    const item = dynamicWallRenders[i];
    externalRenderers.remove(window.view, item);
  }
  dynamicWallRenders = [];
}

//#endregion

//#region 动态扫描
export const addScanRadar = function () {
  if (!scanRadarEffect) {
    scanRadarEffect = new ScanRadar({
      view,
      queryUrl:
        "https://bim.arcgisonline.cn/server/rest/services/Hosted/dmLayer/FeatureServer/0",
      height: 5000,
      radius: 10000,
      position: [119.6242823104002, 29.05841639892346, 100],
    });
    externalRenderers.add(window.view, scanRadarEffect);
  }
};
//#endregion
