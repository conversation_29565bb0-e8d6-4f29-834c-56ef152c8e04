<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>添加Gif使用示例</title>

    <link
      rel="stylesheet"
      href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/dark/main.css"
    />
    <script src="./index.js" type="module"></script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        left: 20%;
        width: 50%;
        height: 200px;
        display: flex;
      }

      .tools span {
        cursor: pointer;
        background-color: blue;
        width: 150px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        color: white;
      }

      .description {
        position: absolute;
        right: 10px;
        top: 10px;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv">
      <div class="tools">
        <span id="add" onclick="load()">添加gif</span>
        <span id="remove" onclick="remove()">移除gif</span>
      </div>
      <div class="description">
        添加GIF图层，方法签名如下
        <p>——添加GIF 图层</p>
        <p>@param {object} {view,data} 传入参数对象</p>
        ArcGisUtils.addGifLayer({ view, data })
        <p>
          使用： 1. 将GIF图片转换为视频格式数据 （在线转换：https://www.aigei.com/tool/video/g2v）</br>
                 2. 调用addGifLayer方法
                 3. 参数data 数据格式如下：</br>
               
        </p>
        <p>  [{
          x: 119.94315399169922,
          y: 29.5630503845215,
          imgSize: [10, 10],
          video: "http://127.0.0.1:5500/utils/video/gif.mp4",
        }]</p>
      </div>
    </div>
  </body>
  <script>

  function load() { 
    ArcGisUtils.addGifLayer({
      view,
      data: [
        {
          x: 119.94315399169922,
          y: 29.5630503845215,
          imgSize: [10, 10],
          video: "http://127.0.0.1:5500/utils/video/gif.mp4",
        },
        {
          x: 119.46214447021484,
          y: 29.31345558166504,
          imgSize: [30, 30],
          video: "http://127.0.0.1:5500/utils/video/gif.mp4",
        },
        {
          x: 119.5569204711914,
          y: 29.00677101135254,
          imgSize: [32, 32],
          video: "http://127.0.0.1:5500/utils/video/gif.mp4",
        },
        {
          x: 119.8483056640625,
          y: 29.18855995178222711,
          imgSize: [32, 32],
          video: "http://127.0.0.1:5500/utils/video/gif.mp4",
        },
        {
          x: 120.08206787109375,
          y: 29.322123641967773,
          imgSize: [32, 32],
          video: "http://127.0.0.1:5500/utils/video/gif.mp4",
        },
        {
          x: 119.7269204711914,
          y: 28.79677101135254,
          imgSize: [32, 32],
          video: "http://127.0.0.1:5500/utils/video/gif.mp4",
        },
        {
          x: 120.1469204711914,
          y: 28.97677101135254,
          imgSize: [32, 32],
          video: "http://127.0.0.1:5500/utils/video/gif.mp4",
        },
        {
          x: 120.4169204711914,
          y: 29.24677101135254,
          imgSize: [50, 50],
          video: "http://127.0.0.1:5500/utils/video/gif.mp4",
        },
        {
          x: 120.6299204711914,
          y: 29.06677101135254,
          imgSize: [50, 50],
          video: "http://127.0.0.1:5500/utils/video/gif.mp4",
        },
      ],
    });
  }

  function remove() {
    ArcGisUtils.removeGifLayer(view);
  }
  </script>

</html>
