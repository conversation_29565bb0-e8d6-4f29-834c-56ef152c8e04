<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>加载倾斜摄影图层</title>

    <link
      rel="stylesheet"
      href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <script src="./index.js" type="module"></script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        right: 20px;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv"></div>
    <div class="tools">
      <button onclick="ArcGisUtils.addIntegratedMesh(view)">
        添加倾斜摄影（不控制一定比例下显示）
      </button>
      <button onclick="ArcGisUtils.addIntegratedMesh(view,12)">
        添加倾斜摄影（控制一定比例下显示）
      </button>
      <button onclick="ArcGisUtils.removeIntegratedMesh(view)">
        移除倾斜摄影
      </button>
    </div>
  </body>

  <script>
    function loadWeatherLayer() {
      ArcGisUtils.loadWeatherLayer("humidity", 3);
    }
  </script>
</html>
