<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>指挥调度底部</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/Vue/vue-count-to.min.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/elementui/js/index.js"></script>
    <style>
      [v-cloak] {
        display: none;
      }

      #bottom {
        width: 1760px;
        height: 525px;
        padding: 20px;
        box-sizing: border-box;
        background: url("/static/images/index/bottom-bg.png") no-repeat;
        background-size: 100% 100%;
        display: flex;
      }

      .left {
        width: 60%;
      }

      .right {
        width: 40%;
      }

      .tabs {
        display: flex;
        justify-content: space-evenly;
      }

      .tab_item {
        height: 59px;
        line-height: 59px;
        font-size: 30px;
        color: #abceef;
        cursor: pointer;
      }

      .tab_active {
        color: #fff;
        font-style: italic;
        font-weight: bold;
        background: url("/static/images/xzzfj/tab_bg.png");
        background-size: 100% 100%;
      }

      /* 表格 */
      .table {
        width: 100%;
        /* height: 500px; */
        padding: 10px 30px;
        box-sizing: border-box;
      }

      .table .th {
        width: 100%;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        font-weight: 700;
        font-size: 28px;
        line-height: 60px;
        color: #ffffff;
      }

      .table .th_td {
        letter-spacing: 0px;
        text-align: center;
      }

      .table .tbody {
        width: 100%;
        height: calc(100% - 59px);
        /* overflow-y: auto; */
        overflow: hidden;
      }

      .table .tbody:hover {
        overflow-y: auto;
      }

      .table .tbody::-webkit-scrollbar {
        width: 4px;
        /*滚动条整体样式*/
        height: 4px;
        /*高宽分别对应横竖滚动条的尺寸*/
      }

      .table .tbody::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: #20aeff;
        height: 8px;
      }

      .table .tr {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        height: 70px;
        line-height: 70px;
        font-size: 28px;
        color: #ffffff;
        cursor: pointer;
        border-top: 1px solid #959aa1;
        border-image: linear-gradient(to right, #e9f5ff3b, #f5ffffd4, #e9f5ff3b)
          1;
        box-sizing: border-box;
      }

      .table .tr:nth-child(2n) {
        background: rgba(50, 134, 248, 0.2);
      }

      .table .tr:nth-child(2n + 1) {
        background: rgba(50, 134, 248, 0.12);
      }

      .table .tr:hover {
        background-color: #0074da75;
      }

      .table .tr_td {
        letter-spacing: 0px;
        text-align: center;
        box-sizing: border-box;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    </style>
  </head>

  <body>
    <div id="bottom" v-cloak>
      <div class="left">
        <div class="hearder_h2"><span>处罚事项TOP10</span></div>
        <!-- <div class="tabs">
          <div
            class="tab_item"
            v-for="(item,index) in tabList"
            @click="tabClick(index)"
            :class="tabIndex==index?'tab_active':''"
          >
            {{item}}
          </div>
        </div> -->
        <div class="table" style="height: 430px">
          <div class="th">
            <div class="th_td" style="flex: 0.2">序号</div>
            <div class="th_td" style="flex: 0.4">事项名称</div>
            <div class="th_td" style="flex: 0.4">案件数</div>
          </div>
          <div
            class="tbody"
            id="box1"
            @mouseover="mouseenterEvent1()"
            @mouseleave="mouseleaveEvent1()"
          >
            <div class="tr" v-for="(item,index) in tableData1" :key="index">
              <div class="tr_td" style="flex: 0.2">{{index+1}}</div>
              <div class="tr_td" style="flex: 0.4">{{item.label}}</div>
              <div class="tr_td" style="flex: 0.4">{{item.num}}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="hearder_h2"><span>处罚案件</span></div>
        <div class="table" style="height: 430px">
          <div class="th">
            <div class="th_td" style="flex: 0.2">序号</div>
            <div class="th_td" style="flex: 0.4">领域</div>
            <div class="th_td" style="flex: 0.4">案件数</div>
          </div>
          <div
            class="tbody"
            id="box2"
            @mouseover="mouseenterEvent2()"
            @mouseleave="mouseleaveEvent2()"
          >
            <div class="tr" v-for="(item,index) in tableData2" :key="index">
              <div class="tr_td" style="flex: 0.2">{{index+1}}</div>
              <div class="tr_td" style="flex: 0.4">{{item.ywwd1}}</div>
              <div class="tr_td" style="flex: 0.4">{{item.num}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      window.parent.eventbus.on("cityChange", (city) => {
        let filtName =
          city == "金义新区"
            ? "金东区"
            : city == "金华开发区"
            ? "开发区"
            : city;
        vm.initApi(filtName,localStorage.getItem("year"));
      });

      window.parent.eventbus &&
      window.parent.eventbus.on("yearChange", (year) => {
        vm.initApi(localStorage.getItem("city"),year);
      });
      let vm = new Vue({
        el: "#bottom",
        data: {
          // tabIndex: 0,
          // tabList: ["综合执法事项", "专业执法事项", "乡镇(街道)执法事项"],
          time1: null,
          dom1: null,
          time2: null,
          dom2: null,
          tableData1: [],
          tableData2: [],
        },
        mounted() {
          this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
          // 表格滚动
          this.dom1 = document.getElementById("box1");
          this.mouseleaveEvent1();
          this.dom2 = document.getElementById("box2");
          this.mouseleaveEvent2();
        },
        methods: {
          initApi(city,year) {
            $api("/csdn_yjyp10", { area_code: city,sjwd2: year }).then((res) => {
              this.tableData2 = res.sort(function (a, b) {
                return b.num - a.num;
              });
            });
            $api("/csdn_yjyp17", { area_code: city,sjwd2: year }).then((res) => {
              this.tableData1 = res.sort(function (a, b) {
                return b.num - a.num;
              });
            });
          },
          // tabClick(index) {
          //   this.tabIndex = index;
          // },
          mouseenterEvent1() {
            clearInterval(this.time1);
          },
          mouseleaveEvent1() {
            this.time1 = setInterval(() => {
              this.dom1.scrollBy({
                top: 71,
                behavior: "smooth",
              });
              if (
                this.dom1.scrollTop >=
                this.dom1.scrollHeight - this.dom1.offsetHeight
              ) {
                this.dom1.scrollTop = 0;
              }
            }, 1500);
          },
          mouseenterEvent2() {
            clearInterval(this.time2);
          },
          mouseleaveEvent2() {
            this.time2 = setInterval(() => {
              this.dom2.scrollBy({
                top: 71,
                behavior: "smooth",
              });
              if (
                this.dom2.scrollTop >=
                this.dom2.scrollHeight - this.dom2.offsetHeight
              ) {
                this.dom2.scrollTop = 0;
              }
            }, 1500);
          },
        },
      });
    </script>
  </body>
</html>
