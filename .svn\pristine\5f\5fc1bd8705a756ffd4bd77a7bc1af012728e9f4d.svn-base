/* 表格样式================================== */
.table_box {
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  height: 600px;
  overflow: hidden;
}

.table {
  width: 100%;
  height: 100%;
  /* padding: 10px; */
  box-sizing: border-box;
  position: relative;
}

.table .tbody {
  width: 100%;
  height: 100%;
  /* overflow-y: auto; */
  overflow: hidden;
}

.table .tbody:hover {
  overflow-y: auto;
}

.table .tbody::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.table .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}


.table .tr {
  height: 136px;
  margin-top: 10px;
  cursor: pointer;
  background: url('/static/images/zhdd/bg.png') no-repeat;
  padding-top: 30px;
  box-sizing: border-box;
}

.icon-sj {
  display: inline-block;
  background: url('/static/images/zhdd/shijian.png') no-repeat;
  width: 31px;
  height: 30px;
  margin-right: 10px;
  vertical-align: middle;
}

.icon-zy {
  display: inline-block;
  background: url('/static/images/zhdd/ziyuan.png') no-repeat;
  width: 31px;
  height: 30px;
  margin-right: 10px;
  vertical-align: middle;
}

.table .time {
  font-size: 28px;
  font-weight: 400;
  color: #77B3F1;
  margin-top: 15px;
  display: flex;
  justify-content: space-between;
}

.table .text {
  font-size: 30px;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #fff;
  text-align: left;
}

/* 表格自动滚动 */
@keyframes rowUp {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  100% {
    transform: translate3d(0, -100%, 0);
    -webkit-transform: translate3d(0, -100%, 0);
    -moz-transform: translate3d(0, -100%, 0);
    -ms-transform: translate3d(0, -100%, 0);
    -o-transform: translate3d(0, -100%, 0);
  }
}

/* .tr {
  animation: 10s rowUp linear infinite normal;
  -webkit-animation: 10s rowUp linear infinite normal;
} */

.tbody:hover .tr {
  animation-play-state: paused;
}

/* 滚动文字 */
.count_box {
  display: flex;
  margin: 0 10px;
}

.count-toNum {
  font-weight: 700;
  width: 50px;
  height: 60px;
  text-align: center;
  line-height: 60px;
  font-size: 43px;
  background: url("/static/images/xzzfj/sz-bg.png");
  background-size: 100% 100%;
}

/* 值班 */
.el-carousel__container {
  margin-top: 35px;
  margin-bottom: 20px;
  /* height: 180px !important; */
  height: 90px !important;
}

.phone_icon {
  width: 27px;
  height: 27px;
  display: inline-block;
  background: url('/static/images/zhdd/dianhua.png') no-repeat;
}

.yyjc_item_title {
  width: 100%;
  line-height: 52px;
  width: 100%;
  background: url('/static/images/zhdd/zb_bg.png') no-repeat;
  background-position: 0 24px;
  background-size: 100%;
}

.yyjc_item_title>p {
  padding-left: 15px;
  font-style: italic;
  font-weight: 700;
  background: linear-gradient(to bottom, #7cb2ff, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.yyjc_item_title>span {
  font-size: 30px;
  color: #eeeaea;
}

.el-carousel__arrow {
  background-color: rgba(31, 45, 61, 0) !important;
  font-size: 30px !important;

}

/* 执法设备 */
.font-b {
  font-family: DINCondensed;
}

.yellow {
  color: #e4cd43;
}

.green {
  color: #33d9dd;
  font-style: italic;
}

.font-italic {
  font-style: italic;
}

.line {
  margin: 0 30px;
  width: 3px;
  height: 120px;
  background: linear-gradient(to bottom, #ffffff00, #fff, #ffffff00);
  ;
}

.tit_data {
  background: url('/static/images/zhdd/tit_bg.png') no-repeat;
  /* background-size: 100%; */
}

/* 天气 */
.weater_box {
  margin-bottom: 10px;
  padding: 10px 20px 0;
  box-sizing: border-box;
  background: url("/static/images/zhdd/tianqi_bg.png") no-repeat;
  background-position: center;
  background-size: 100%;
  width: 100%;
  height: 415px;
}

.weater_box .bg-zl {
  font-weight: 700;
  font-size: 30px;
  color: #000;
  height: 51px;
  line-height: 51px;
  text-align: center;
  background-image: linear-gradient(90deg, #3d97dd 0%, #3ddadd 100%);
  border-radius: 10px;
  padding: 0 14px;
}

.tq_item {
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 218px;
  height: 170px;
  font-size: 26px;
  justify-content: end;
}

.flex_column {
  display: flex;
  align-items: center;
  flex-direction: column;
}

/* 多维感知视频 */
.video_box {
  width: 50%;
  height: 280px;
  font-size: 30px;
  color: #fff;
}

.video_main {
  width: 80%;
  height: 80%;
  border: 2px solid #6db0ef;
  box-shadow: 0px 0px 25px #4282c4 inset;
}

.video_box>p {
  background: url('/static/images/zhdd/tit_bg.png') no-repeat;
  background-position: center;
  width: 100%;
  padding: 5px 0 9px;
  margin: 5px 0 20px;
  text-align: center;
}

.pz_icon {
  cursor: pointer;
  position: absolute;
  top: 1247px;
  right: 18px;
  display: inline-block;
  width: 68px;
  height: 68px;
  background: url('/static/images/zhdd/pz_btn.png') no-repeat;
  border-radius: 10px;
}

.pz_active{
  background-color: #7995b8;
}

.iconPhone{
  cursor: pointer;
  background: #51c422;
  padding: 8px 15px;
  border-radius: 4px;height: 40px;
    width: 34px;
    line-height: 50px;
}
.iconPhone::after{
  content: '';
    display: inline-block;
      width: 32px;
      height: 32px;background: url(/static/images/zhdd/icon_call2.png) no-repeat;
        background-size: cover;
}
.iconVideo {
  cursor: pointer;
  background: #22a0c4;
  padding: 8px 15px;
    line-height: 45px;
      border-radius: 4px;height: 40px;
        width: 34px;
        line-height: 50px;
}
.iconVideo::after {
  content: '';
  display: inline-block;
  width: 32px;
  height: 32px;
      background: url(/static/images/zhdd/video2.png) no-repeat;
        background-size: cover;
}
/* .iconPhone {
  display: inline-block;
  width: 32px;
  height: 32px;
  background: url('/static/images/zhdd/icon_call.png');
}

.iconVideo {
  display: inline-block;
  width: 32px;
  height: 32px;
  background: url('/static/images/zhdd/video.png');
} */

.table-bottom {
  width: 100%;
  height: 385px;
  padding: 10px;
  box-sizing: border-box;
}

.table-bottom .th {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  font-weight: 700;
  font-size: 28px;
  line-height: 60px;
  color: #ffffff;
}

.table-bottom .th_td {
  letter-spacing: 0px;
  text-align: left;
}

.table-bottom .tbody {
  width: 100%;
  height: calc(100% - 80px);
  /* overflow-y: auto; */
  overflow: hidden;
}

.table-bottom .tbody:hover {
  overflow-y: auto;
}

.table-bottom .tbody::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.table-bottom .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.table-bottom .tr {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  height: 95px;
  line-height: 95px;
  font-size: 28px;
  color: #ffffff;
  cursor: pointer;
  border-top: 1px solid #959aa1;
  border-image: linear-gradient(to right, #e9f5ff3b, #f5ffffd4, #e9f5ff3b) 1;
  box-sizing: border-box;
}

.table-bottom .tr:nth-child(2n) {
  background: rgba(50, 134, 248, 0.2);
}

.table-bottom .tr:nth-child(2n + 1) {
  background: rgba(50, 134, 248, 0.12);
}

.table-bottom .tr:hover {
  background-color: #0074da75;
}

.table-bottom .tr_td {
  letter-spacing: 0px;
  text-align: left;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}