@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('/static/fonts/YouSheBiaoTiHei-2.ttf');
}

@font-face {
  font-family: 'DINCondensed';
  src: url('/static/fonts/DIN-Condensed-Bold-2.ttf');
}
[v-cloak] {
  display: none;
}
* {
  margin: 0;
  padding: 0;
}
#left,
#right {
  padding: 25px 0;
  box-sizing: border-box;
  width: 1030px;
  height: 1900px;
  background: url('/static/images/index/left-bg.png') no-repeat;
  background-size: 100% 100%;
}

#right {
  background: url('/static/images/index/right-bg.png') no-repeat;
  background-size: 100% 100%;
}

/* 点击样式 */
.cursor {
  cursor: pointer;
}

.click-i {
  display: inline-block;
  width: 27px;
  height: 23px;
  margin: 0 5px;
  background: url('/static/images/common/header/click-1.png') no-repeat;
}

/* 一级标题 */
.hearder_h1 {
  height: 75px;
  line-height: 30px;
  font-size: 78px;
  font-weight: 500;
  color: #ffffff;
  margin-left: 20px;
  background: url('/static/images/index/header.png') no-repeat;
  background-size: 100% 100%;
}

.hearder_h1>span {
  margin-left: 70px;
  background: linear-gradient(to bottom, #7cb2ff, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
  font-family: YouSheBiaoTiHei;
}

/* 二级标题 */
.hearder_h2 {
  font-size: 36px;
  background: url('/static/images/xzzfj/header2.png') no-repeat;
  background-size: 100%;
  background-position: bottom;
  height: 64px;
  margin-left: 29px;
}

.hearder_h2>span {
  margin-left: 45px;
  background: linear-gradient(0deg, #acddff 0%, #ffffff 100%);
  -webkit-background-clip: text;
  color: transparent;
}

/* el警告信息 */
.el-message__content {
  font-size: 30px !important;
}