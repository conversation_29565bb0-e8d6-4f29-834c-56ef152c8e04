import Graphic from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/Graphic.js";
import FeatureLayer from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/layers/FeatureLayer.js";
import Polyline from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/geometry/Polyline.js";
import { addRoadLayer, removeRoadLayer } from "./loadRoadLayer.js";
import gcj02towgs84 from "./coordtransform.js";

async function getBaiduApiData(requestUrl, payload) {
  try {
    const keys = Object.keys(payload);
    const querys = [];
    for (let i = 0; i < keys.length; i++) {
      querys.push(`${keys[i]}=${payload[keys[i]]}`);
    }
    const url =
      querys.length > 0 ? `${requestUrl}?${querys.join("&")}` : requestUrl;
    const response = await fetch(url);
    const responseJson = await response.json();
    return responseJson.result;
  } catch (e) {
    throw new Error("请求百度接口出错！");
  }
}

function _getFields(objectId, attributes) {
  const fields = [{ name: objectId, alias: "OBJECTID", type: "oid" }];
  for (let key in attributes) {
    if (key.toUpperCase() !== objectId) {
      if (typeof attributes[key] === "string") {
        fields.push({
          name: key,
          alias: key,
          type: "string",
        });
      } else if (typeof attributes[key] === "number") {
        if (attributes[key] % 1 == 0) {
          fields.push({
            name: key,
            alias: key,
            type: "integer",
          });
        } else {
          fields.push({
            name: key,
            alias: key,
            type: "double",
          });
        }
      }
      // 日期格式设置为Date报错？
      else if (attributes[key] instanceof Date) {
        fields.push({
          name: key,
          alias: key,
          type: "string",
        });
      }
    }
  }

  return fields;
}

function _getAllPoints(linkStates) {
  const linkStatesClone = { ...linkStates };
  const keys = Object.keys(linkStatesClone);

  for (let i = 0; i < keys.length; i++) {
    const item = linkStatesClone[keys[i]];
    const roadLines = [];
    const roadPoints = item.split(";");
    for (let j = 0; j < roadPoints.length; j++) {
      roadLines.push(roadPoints[j]);
    }

    const paths = [];
    for (let k = 0; k < roadLines.length; k++) {
      const item = roadLines[k];
      const oneRoadPoints = item.split(",");
      const path = [];
      for (let l = 0; l < oneRoadPoints.length; l += 2) {
        const cordinate = gcj02towgs84(
          Number(oneRoadPoints[l]),
          Number(oneRoadPoints[l + 1])
        );
        path.push(cordinate);
        //path.push([oneRoadPoints[l], oneRoadPoints[l + 1]]);
      }
      path.push(path[0]);
      paths.push(path);
    }
    linkStatesClone[keys[i]] = paths;
  }

  return linkStatesClone;
}

function addRoadLayerToMap(view, list) {
  const graphics = [];
  for (let i = 0; i < list.length; i++) {
    const item = list[i];
    const { codeId, linkStates } = item;
    const points = _getAllPoints(linkStates);
    console.log(points);

    const keys = Object.keys(points);
    for (let j = 0; j < keys.length; j++) {
      const linkState = points[keys[j]];
      const polyline = new Polyline({
        paths: linkState,
      });
      const graphic = new Graphic({
        geometry: polyline,
        attributes: {
          OBJECTID: `${codeId}_${keys[j]}`,
          ...item,
          linkStatesType: Number(keys[j]),
        },
      });
      graphics.push(graphic);
    }
  }

  let renderer = {
    type: "unique-value",
    field: "linkStatesType",
    defaultSymbol: {
      type: "simple-line",
      color: "green",
      width: "5px",
    },
    uniqueValueInfos: [
      {
        value: "1",
        symbol: {
          type: "simple-line",
          color: "green",
          width: "5px",
        },
      },
      {
        value: "2",
        symbol: {
          type: "simple-line",
          color: "yellow",
          width: "5px",
        },
      },
      {
        value: "3",
        symbol: {
          type: "simple-line",
          width: "5px",
          color: [255, 0, 0, 0.7],
        },
      },
      {
        value: "4",
        symbol: {
          type: "simple-line",
          width: "5px",
          color: [255, 0, 0, 1],
        },
      },
    ],
  };

  const fields = _getFields("OBJECTID", graphics[0].attributes);
  const layer = new FeatureLayer({
    objectIdField: "OBJECTID",
    outFields: ["*"],
    fields,
    source: graphics, // 使用自定义的数据源
    renderer: renderer,
    elevationInfo: {
      mode: "on-the-ground",
    },
  });
  view.map.add(layer);
}

const clock = setInterval(async () => {
  if (view) {
    clearInterval(clock);
    const list = await getBaiduApiData(
      "https://jiaotong.baidu.com/openapi/v2/event/alarmlist",
      {
        nodeId: 947,
        roadType: "1,2,3,4,5",
        eventSource: "1,2,3",
        ak: "9EY1KFlfi8pZZIol7QnCLlpRfCZTb6Zh",
        retCoordType:"gcj02",
        returnType: 2,
      }
    );
    addRoadLayer();
    addRoadLayerToMap(view, list);
  }
}, 1000);
