<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>通话弹窗</title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/css/viewCss/index.css" />
  <link rel="stylesheet" href="/static/css/viewCss/commonObjzhdd.css" />
  <script src="/jquery/jquery-3.6.1.min.js"></script>
  <script src="/Vue/vue.js"></script>
  <script src="/static/js/layui/layui.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/static/js/jslib/Emiter.js"></script>
  <script src="cmscVccBar/vccbar/cmscvccbar.js"></script>

  <style>
      .container {
          width: 1515px;
          height: 866px;
          background: url("/static/images/zhdd/dialogBg.png") no-repeat;
          background-size: 100% 100%;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          flex-direction: column;
      }
      .tel_box {
          text-align: center;
          margin-top: 100px;
      }
      .tel_top {
          display: flex;
          align-content: center;
          align-items: center;
          justify-content: center;
          margin-top: 66px;
      }
      .remoteView_box {
          width: 297px;
          height: 516px;
          background: url('/static/assets/ajhf/video_left.png') 0 0 no-repeat;
          background-size: cover;
          display: flex;
          align-content: center;
          align-items: center;
          justify-content: center;
          margin-right: 88px;
      }
      .remoteView {
          width: 280px;
          height: 500px;
      }
      .tel_header {
          width: 169px;
          height: 193px;
          margin-right: 88px;
      }
      .tel {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 700;
          font-size: 40px;
          color: #fff;
          line-height: 48px;
          margin-bottom: 26px;
      }
      .status_box {
          display: flex;
          align-content: center;
          align-items: center;
      }
      .status {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 32px;
          color: #2cd7aa;
          line-height: 48px;
          margin-right: 20px;
      }
      .green_status {
          color: #b2d8ff;
      }
      .status_img1 {
          width: 99px;
          height: 38px;
      }
      .status_img2 {
          width: 32px;
          height: 32px;
      }
      .voice_box {
          display: flex;
          align-content: center;
          align-items: center;
          margin-top: 26px;
      }
      .voice_img {
          width: 48px;
          height: 48px;
          margin-right: 32px;
          cursor: pointer;
      }
      .voice_time {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 32px;
          color: #fff;
          line-height: 48px;
      }
      .selfView_box {
          width: 226px;
          height: 146px;
          background: url('/static/assets/ajhf/video_right.png') 0 0 no-repeat;
          background-size: cover;
          display: flex;
          align-content: center;
          align-items: center;
          justify-content: center;
          margin-top: 88px;
      }
      .selfView {
          width: 210px;
          height: 130px;
      }
      .tel_center {
          margin-top: 64px;
      }
      .tel_end {
          width: 96px;
          height: 96px;
          cursor: pointer;
      }
      .rw-title {
          padding: 46px 3% 0;
          width: 95%;
          height: 60px;
          line-height: 60px;
      }
      .close {
          background: url("/static/images/zhdd/close.png") no-repeat;
          width: 34px;
          height: 34px;
      }
  </style>
</head>
<body>
<div id="CallPhone" class="container">
  <div class="rw-title flex-between">
    <div class="fs-44 text-mid-yellow" id="rwTitle" style="margin-left: 20px;">语音通话</div>
<!--    <div class="close cursor" @click="close" style="margin-right: 20px;"></div>-->
  </div>
  <div class="tel_box">
    <div class="tel_top">
      <img class="tel_header" src="/static/assets/ajhf/phone_header.png" alt="" />
      <div class="info_box">
        <div class="tel">{{ phoneCode }}</div>
        <div class="status_box">
          <div class="status" :class="{ green_status: telStatus }">{{ telStatus ? '通话中' : '拨号中' }}</div>
          <img v-if="telStatus" class="status_img1" src="/static/assets/ajhf/phone_audio.png" alt="" />
          <img v-else class="status_img2" src="/static/assets/ajhf/phone_calling.png" alt="" />
        </div>
        <div class="voice_box">
          <img
            v-if="muteFlag"
            @click="changeMute(muteFlag)"
            class="voice_img"
            src="/static/assets/ajhf/phone_novoice.png"
            alt=""
          />
          <img v-else class="voice_img" @click="changeMute(muteFlag)" src="/static/assets/ajhf/phone_voice.png" alt="" />
          <div class="voice_time">{{ voiceTime }}</div>
        </div>
      </div>
    </div>
    <div class="tel_center">
      <img class="tel_end" @click="endCall" src="/static/assets/ajhf/phone_end.png" alt="" />
    </div>
  </div>
</div>

  <script>
    var CallVm = new Vue({
      el: "#CallPhone",
      data: {
        //网络电话
        phoneCode: '',
        telStatus: false,
        muteFlag: false,
        voiceTime: '00:00:00',
        config: {
          tenantType: 1,
          ip: 'ygf.xzzfj.jinhua.gov.cn',
          port: '443',
          // ip: 'iccs.pointlinkprox.com',
          // port: '9080',
          vccId: '100317',
          agentId: '1001',
          password: 'Zyzx@10086',
          loginKey: '3W4SS2MK1YJBBJHWQEWOSRFF',
          event: {}
        },
        VccBar: null,
        signIn: false,
        load: false,
        callTimer: null, // 通话计时器
      },
      created() {
        // 在创建实例后正确设置事件处理函数
        this.config.event = {
          OnInitalSuccess: this._OnInitalSuccess,
          OnInitalFailure: this._OnInitalFailure,
          OnAnswerCall: this._OnAnswerCall,
          OnCallEnd: this._OnCallEnd,
          OnReportBtnStatus: this._OnReportBtnStatus,
          OnCallRing: this._OnCallRing,
          OnBarExit: this._OnBarExit,
          OnUpdateVideoWindow: this._OnUpdateVideoWindow,
          OnAgentWorkReport: this._OnAgentWorkReport,
        };
      },
      mounted() {
        const that = this;
        that.handleSignIn()
        window.addEventListener("message", function (event) {
          //子获取父消息
          let newData;
          if (typeof event.data == "object") {
            newData = event.data;
          } else {
            newData = JSON.parse(event.data.argument);
          }
          that.phoneCode = newData.phone;
          if (newData.phone) {
            that.phoneCode = newData.phone
            setTimeout(() => {
              that.openCall(that.phoneCode)
            },1000)
          }
        });
      },
      methods: {
        //网络通话开始
        _OnInitalSuccess() {
          this.signIn = true
          console.log('初始化成功')
        },
        _OnInitalFailure() {},
        _OnAnswerCall(userNo, answerTime, serialID, serviceDirect, callID, userParam, taskID, av, tc, haveAsrEvent) {
          this.telStatus = true
          console.log('serialID1111', serialID)
          // 创建计时器，每秒更新一次通话时间
          this.callTimer = setInterval(() => {
            let seconds = parseInt(this.voiceTime.split(':')[2]) + 1
            let minutes = parseInt(this.voiceTime.split(':')[1])
            let hours = parseInt(this.voiceTime.split(':')[0])

            if (seconds >= 60) {
              seconds = 0
              minutes++
            }
            if (minutes >= 60) {
              minutes = 0
              hours++
            }

            this.voiceTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds
              .toString()
              .padStart(2, '0')}`
          }, 1000)
          if (av === 'video') {
            //这里控制视频窗口显示
            this.$refs['videoAndShare'].isVideo = true
          }
        },
        _OnCallEnd(
          callID,
          serialID,
          serviceDirect,
          userNo,
          bgnTime,
          endTime,
          agentAlertTime,
          userAlertTime,
          fileName,
          directory,
          disconnectType,
          userParam,
          taskID,
          serverName,
          networkInfo
        ) {
          console.log('通话结束')
          this.telStatus = false
          this.close()
          clearInterval(this.callTimer)
        },
        _OnReportBtnStatus(btnIDS) {
          console.log(btnIDS)
          var arrIDS = btnIDS.split('|')
          // for (var i = 0; i < arrIDS.length; i++) {
          //   this['btn' + parseInt(arrIDS[i])] = false
          // }
        },
        _OnCallRing(
          callingNo,
          calledNo,
          orgCalledNo,
          callData,
          serialID,
          serviceDirect,
          callID,
          userParam,
          taskID,
          userDn,
          agentDn,
          areaCode,
          fileName,
          networkInfo,
          queueTime,
          opAgentID,
          ringTime,
          projectID,
          accessCode,
          taskName,
          cityName,
          userType,
          lastServiceId,
          lastServiceName,
          accessNumber
        ) {},
        _OnBarExit(code, description) {
          this.signIn = false
          console.log('坐席迁出')
        },
        _OnUpdateVideoWindow(param) {
          console.log('OnUpdateVideoWindow-----', param)
          if (param.key_word == 'GetVideoViews') {
            param.param.SetVideoViews('selfView_cincc_5g', 'remoteView_cincc_5g')
          }
        },
        _OnAgentWorkReport(workStatus, description) {},
        handleSignIn() {
          if (this.signIn) {
            this.VccBar.UnInitial()
          } else {
            // 如果已经加载过cmscVccBar，不再重新加载
            if (!this.load) {
              this.VccBar = VccBar.setConfig(this.config).client()
              this.VccBar.load('cmscVccBar').then(() => {
                this.load = true
                this.VccBar.Initial()
              })
            } else {
              this.VccBar.Initial()
            }
          }
        },
        openCall(phone) {
          if (phone != '') {
            this.dialogVisible5 = true
            this.phoneCode = phone
            this.muteFlag = false
            this.telStatus = false
            this.VccBar.MakeCall(phone, 3, '', '', '', '', '', '', '', '', 1)
            this.voiceTime = '00:00:00'
          } else {
            this.$message.info('此人暂未录入手机号码')
          }
        },
        openVideo(phone) {
          if (phone != '') {
            this.dialogVisible6 = true
            this.phoneCode = phone
            this.telStatus = false
            this.VccBar.MakeCall(phone, 3, '', '', '', '', '', '', '', '', 2)
            this.voiceTime = '00:00:00'
          } else {
            this.$message.info('此人暂未录入手机号码')
          }
        },
        changeMute(mute) {
          this.muteFlag = !mute
          this.VccBar.Mute(this.muteFlag ? 2 : 1)
        },
        endCall() {
          this.VccBar.Disconnect()
          clearInterval(this.callTimer)
          this.close()
        },
        //网络通话结束
        close() {
          window.parent.lay.closeIframeByNames(["CallPhone"]);
        },
      },
    })
  </script>
</body>
</html>
