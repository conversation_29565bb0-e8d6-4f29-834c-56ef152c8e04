<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>右</title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/css/viewCss/index.css" />
  <link rel="stylesheet" href="/static/css/viewCss/zhdd_left.css" />
  <script src="/Vue/vue.js"></script>
  <script src="/jquery/jquery-3.6.1.min.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/Vue/vue-count-to.min.js"></script>
  <link rel="stylesheet" href="/elementui/css/index.css" />
  <script src="/static/js/jslib/Emiter.js"></script>
  <script src="/elementui/js/index.js"></script>
  <script src="/zhddjhaqcsztqd/static/js/jslib/turf.min.js"></script>
  <script src="/static/js/jslib/moment.js"></script>

  <style>
      .sos_box {
          position: absolute;
          right: 40px;
          top: 58px;
          cursor: pointer;
      }
      .icon_num {
          min-width: 30px;
          line-height: 18px;
          background: red;
          position: absolute;
          font-style: initial;
          text-align: center;
          border-radius: 50%;
          color: #fff;
          font-weight: 700;
          font-size: 25px;
          padding: 10px 5px;
          right: -9px;
          top: -11px;
      }
      [v-cloak] {
          display: none;
      }

      .el-carousel__container {
          height: 80px !important;
      }

      .el-carousel__indicators--outside {
          display: none;
      }
      .el-message-box__title {
          font-size: 38px;
          background: linear-gradient(to bottom, #6ca9fc, #ffffff);
          -webkit-background-clip: text;
          color: transparent;
          font-weight: 700;
          font-style: italic;
      }
      .el-message-box__message p {
          line-height: 100px;
      }
      .el-message-box__content {
          color: #fff;
      }
      .el-message-box {
          width: 650px;
          box-shadow: inset 0px 0px 16px 0px rgba(0, 145, 255, 1);
          background-color: #021037;
          border-radius: 21px;
          border: 1px solid#155181;
      }
      .el-message-box__headerbtn {
          font-size: 30px;
      }
      .el-message-box__headerbtn .el-message-box__close {
          color: #fff;
      }
      .el-message-box__headerbtn {
          font-size: 38px !important;
      }
      .el-button--small {
          font-size: 28px;
          padding: 10px 33px;
          border-radius: 14px;
      }

      .zfrw_box {
          width: 100%;
          display: flex;
      }

      /* 表格 */
      .table {
          width: 100%;
          height: 650px;
          padding: 10px;
          box-sizing: border-box;
      }
      .table .th {
          width: 100%;
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          font-weight: 700;
          font-size: 28px;
          line-height: 60px;
          color: #ffffff;
      }

      .table .th_td {
          letter-spacing: 0px;
          text-align: left;
      }

      .table .tbody {
          width: 100%;
          height: calc(100% - 65px);
          /* overflow-y: auto; */
          overflow: hidden;
      }

      .table .tbody:hover {
          overflow-y: auto;
      }

      .table .tbody::-webkit-scrollbar {
          width: 4px;
          /*滚动条整体样式*/
          height: 4px;
          /*高宽分别对应横竖滚动条的尺寸*/
      }

      .table .tbody::-webkit-scrollbar-thumb {
          border-radius: 10px;
          background: #20aeff;
          height: 8px;
      }

      .table .tr {
          display: flex;
          justify-content: space-evenly;
          align-items: center;
          height: 80px;
          line-height: 80px;
          font-size: 28px;
          color: #ffffff;
          border-top: 1px solid #959aa1;
          border-image: linear-gradient(to right, #e9f5ff3b, #f5ffffd4, #e9f5ff3b)
          1;
          box-sizing: border-box;
      }
      .tr_light {
          background-color: #0074da75 !important;
          border: 1px solid yellow !important;
          box-sizing: border-box;
      }

      .table .tr:nth-child(2n) {
          background: rgba(50, 134, 248, 0.2);
      }

      .table .tr:nth-child(2n + 1) {
          background: rgba(50, 134, 248, 0.12);
      }

      .table .tr:hover {
          background-color: #0074da75;
      }

      .table .tr_td {
          letter-spacing: 0px;
          text-align: left;
          box-sizing: border-box;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 25px;
      }

      .table .tr_td > img {
          position: relative;
          top: 25px;
      }
  </style>
  <style>
      .el-date-editor .el-range-input {
          color: #eee;
          width: 122%;
          background: transparent;
          font-size: 24px;
          z-index: 555;
      }
      .el-input__inner {
          height: 48px !important;
      }
      .el-date-range-picker__time-header .el-input__inner {
          background-color: transparent !important;
          border: none !important;
      }
      .el-picker-panel {
          color: #fff;
          background-color: #132c4e;
      }
      .el-date-editor .el-range-separator {
          color: #fff;
          line-height: 38px;
          font-size: 20px;
      }
      .el-month-table td.in-range div,
      .el-month-table td.in-range div:hover,
      .el-input__inner {
          background-color: #39537a;
      }
      .el-date-range-picker__content .el-date-range-picker__header div {
          font-size: 23px;
      }
      .el-month-table {
          font-size: 22px;
          white-space: nowrap;
      }
      .el-date-editor .el-range__icon {
          font-size: 20px;
          line-height: 39px;
      }
      .el-date-editor .el-range__close-icon {
          font-size: 20px;
          line-height: 40px;
      }
      .el-date-table {
          font-size: 23px;
      }
      .el-date-table td.in-range div,
      .el-date-table td.in-range div:hover,
      .el-date-table.is-week-mode .el-date-table__row.current div,
      .el-date-table.is-week-mode .el-date-table__row:hover div {
          background-color: #164996;
      }

      .el-picker-panel__icon-btn {
          font-size: 24px;
          color: #ffffff;
          border: 0;
          background: 0 0;
          cursor: pointer;
          outline: 0;
          margin-top: 8px;
      }

      .tabs {
          display: flex;
          justify-content: space-around;
          align-items: center;
      }

      .tab {
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-style: italic;
          color: rgba(171, 206, 239, 0.7);
      }

      .tabConActive {
          background: url("/static/images/zfts/tab-active.png") no-repeat;
          background-size: 110% 100%;
          font-size: 36px;
          font-family: Source Han Sans CN;
          font-weight: bold;
          font-style: italic;
          color: #ffffff;
      }
  </style>
</head>

<body>
<div id="right" v-cloak style="position: relative">
  <div class="hearder_h1 s-m-t-30 s-m-b-15"><span>告警信息</span></div>
  <div style="position: absolute; right: 230px; top: 58px">
    <el-date-picker
      v-model="datas1"
      type="daterange"
      value-format="yyyy-MM-dd"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      @change="queryData1"
    >
    </el-date-picker>
  </div>
  <div class="table-bottom">
    <div class="sos_box">
      <!-- :style="{cursor: sos.sosNOdata.length!=0?'pointer':'no-drop'}" -->
      <img
        v-show="showSOSno"
        src="/static/images/zhdd/back.png"
        width="50px"
        style="margin-right: 40px"
        @click="showSOSno=false;gjxxData=sos.sosNOdata"
      />
      <img
        src="/static/images/zhdd/bell.png"
        width="50px"
        @click="showSOSno=true;gjxxData=sos.sosISdata"
      />
      <i class="icon_num" @click="showSOSno=true;gjxxData=sos.sosISdata">
        {{sos.sosISdata.length}}
      </i>
    </div>
    <div class="th">
      <div class="th_td" style="flex: 0.1; text-align: center">序号</div>
      <div class="th_td" style="flex: 0.35; text-align: center">
        报警发生时间
      </div>
      <div class="th_td" style="flex: 0.3; text-align: center">
        告警单位
      </div>
      <div class="th_td" style="flex: 0.15; text-align: center">队员</div>
      <div
        class="th_td"
        style="flex: 0.2; text-align: center"
        v-show="showSOSno"
      >
        现场连线
      </div>
    </div>
    <div class="tr" v-show="gjxxData.length==0">暂无数据</div>
    <div
      class="tbody"
      id="box"
      @mouseenter="mouseenterEvent"
      @mouseleave="mouseleaveEvent"
    >
      <div
        class="tr"
        v-for="(item,index) in gjxxData"
        :key="index"
        :class="item.isLight===1?'tr_light':'tr_noLight'"
      >
        <div
          class="tr_td"
          :style="{color:item.isHave?'red':'',flex: '0.1', textAlign: 'center',cursor:'pointer'}"
          @click="SOSfun(item)"
        >
          {{index+1}}
        </div>
        <div class="tr_td" style="flex: 0.35; text-align: center">
          {{item.msgtime}}
        </div>
        <div
          class="tr_td"
          style="flex: 0.3; text-align: center"
          :title="item.unitname"
        >
          {{item.unitname}}
        </div>
        <div
          class="tr_td"
          style="flex: 0.15; text-align: center"
          :title="item.hostname"
        >
          {{item.hostname}}
        </div>
        <div
          class="tr_td"
          style="flex: 0.2; text-align: center"
          v-show="showSOSno"
        >
          <div
            style="
                  display: flex;
                  justify-content: space-around;
                  align-items: center;
                "
          >
                <span
                  :style="{cursor:item.deviceid?'pointer':'no-drop'}"
                  class="iconPhone"
                  @click="zfryPopFun({type:'调度呼叫',sys_depart:item.deviceid,county: item.county,
              hostcode: item.hostcode})"
                ></span>
            <span
              :style="{cursor:item.deviceid?'pointer':'no-drop'}"
              class="iconVideo"
              @click="zfryPopFun({type:'调度呼叫',sys_depart:item.deviceid,county: item.county,
              hostcode: item.hostcode})"
            ></span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="hearder_h1"><span>任务跟踪</span></div>
  <div style="position: absolute; right: 24px; top: 530px">
    <el-date-picker
      v-model="datas2"
      type="daterange"
      value-format="yyyy-MM-dd"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      @change="queryData2"
    >
    </el-date-picker>
  </div>
  <div class="zfrw_box">
    <div class="zfrw_right s-flex-1">
      <div class="tabs">
        <div
          class="tab"
          v-for="(item,i) in tabs"
          :class="{tabConActive:tabActive==i}"
          @click="queryTableData(i)"
        >
          {{item.name + "(" + item.value + ")"}}
        </div>
      </div>
      <div class="table">
        <div class="th">
          <div class="th_td" style="flex: 0.11; text-align: center">
            序号
          </div>
          <div
            class="th_td"
            style="flex: 0.21; text-align: center; white-space: nowrap"
          >
            任务来源
          </div>
          <div class="th_td" style="flex: 0.2; text-align: center">
            任务名称
          </div>
          <div class="th_td" style="flex: 0.18; text-align: center">
            接收对象
          </div>
          <div class="th_td" style="flex: 0.2; text-align: center">
            任务下达时间
          </div>
          <div class="th_td" style="flex: 0.16; text-align: center">
            任务详情
          </div>
        </div>
        <div class="tr" v-show="tableData.length==0">暂无数据</div>
        <div
          class="tbody"
          id="box2"
          @mouseenter="mouseenterEvent2"
          @mouseleave="mouseleaveEvent2"
        >
          <div
            class="tr"
            v-for="(item,index) in tableData"
            :key="index"
            :class="item.isLight==1?'tr_light':'tr_noLight'"
          >
            <div
              class="tr_td"
              style="flex: 0.11; text-align: center"
              :style="{color:item.taskSource=='舆情中心'?'yellow':item.detailsId?'red':''}"
              @click="linghtSos(item)"
            >
              {{index + 1}}
            </div>
            <div
              class="tr_td"
              style="flex: 0.21; text-align: center"
              :title="item.taskSource"
            >
              {{getStatusText(item.taskSource)}}
            </div>
            <div
              class="tr_td"
              style="flex: 0.2; text-align: center"
              :title="item.msg"
            >
              {{getStatusText(item.msg)}}
            </div>
            <div
              class="tr_td"
              style="flex: 0.18; text-align: center"
              :title="item.receiveObject"
            >
              {{getStatusText(item.receiveObject)}}
            </div>
            <div
              class="tr_td"
              style="flex: 0.2; text-align: center"
              :title="item.startTime"
            >
              {{item.startTime}}
            </div>
            <div
              class="tr_td"
              style="flex: 0.16; text-align: center; color: #3cfdff"
              @click="showTaskDetail(item)"
            >
              查看详情
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="hearder_h1"><span>多维感知</span></div>
  <i
    class="pz_icon"
    :class="showVideoFind?'pz_active':''"
    @click="openVideoFind()"
    :style="{cursor: sgVideoList.length==0?'no-drop':''}"
  ></i>
  <div style="position: relative" class="s-flex s-flex-wrap">
    <!-- src="/static/citybrain/commonts/zhdd/video-16-9.html?sgid=1682234227102&name=null&jl=3&lx=1" -->
    <span
      style="margin: 31px auto; font-size: 30px; color: #fff"
      v-show="!showVideoIframe"
    >暂无该区域视频</span
    >
    <iframe
      v-show="showVideoIframe"
      frameborder="0"
      id="videoCon"
      name="zhddzxRightVideo"
      style="
            width: 980px;
            height: 561px;
            margin-left: 30px;
            position: relative;
          "
      frameborder="no"
      border="0"
      marginwidth="0"
      marginheight="0"
      scrolling="no"
      allowtransparency="yes"
    ></iframe>
  </div>
</div>
<script>
  window.parent.eventbus &&
  window.parent.eventbus.on("cityChange", (city) => {
    let filtName = (zhddRightVm.city =
      city == "金义新区"
        ? "金东区"
        : city == "金华开发区"
          ? "开发区"
          : city);
    zhddRightVm.video_point = zhddRightVm.pointCenter.find(item => item.name == city).esX + ", " + zhddRightVm.pointCenter.find(item => item.name == city).esY
    zhddRightVm.init(filtName);
    zhddRightVm.getTaskStatistics();
    zhddRightVm.queryTableData(zhddRightVm.tabActive);
    zhddRightVm.initVideo()
  });
  var zhddRightVm = new Vue({
    el: "#right",
    data: {
      openorclose:true,
      initPoint: [],
      tabActive: 0,
      tabs: [
        {
          name: "全部",
          value: 0,
          status: 0,
        },
        {
          name: "待反馈",
          value: 0,
          status: 1,
        },
        {
          name: "待重做",
          value: 0,
          status: 2,
        },
        {
          name: "待结案",
          value: 0,
          status: 3,
        },
        {
          name: "已结案",
          value: 0,
          status: 4,
        },
      ],
      showVideoIframe: true,
      statusList: [
        {
          name: "未处理",
          value: 0,
        },
        {
          name: "处理中",
          value: 1,
        },
        {
          name: "已完成",
          value: 2,
        },
      ],
      qx: [
        { name: "金华市", code: 330703000000 },
        { name: "婺城区", code: 330702000000 },
        { name: "金东区", code: 330703000000 },
        { name: "开发区", code: 330751000000 },
        { name: "兰溪市", code: 330781000000 },
        { name: "义乌市", code: 330782000000 },
        { name: "东阳市", code: 330783000000 },
        { name: "永康市", code: 330784000000 },
        { name: "武义县", code: 330723000000 },
        { name: "浦江县", code: 330726000000 },
        { name: "磐安县", code: 330727000000 },
      ],
      xzzfzx_point: "119.642632, 29.082182", //金华市政府
      pointCenter: [
        { name: '金华市', esX: 119.642632, esY: 29.082182 },
        { name: '婺城区', esX: 119.509748, esY: 28.977012 },
        { name: '义乌市', esX: 120.061011, esY: 29.300614 },
        { name: '金东区', esX: 119.799596, esY: 29.149391 },
        { name: '东阳市', esX: 120.375678, esY: 29.232405 },
        { name: '永康市', esX: 120.102417, esY: 28.934317 },
        { name: '兰溪市', esX: 119.526736, esY: 29.278165 },
        { name: '武义县', esX: 119.714529, esY: 28.768287 },
        { name: '磐安县', esX: 120.559672, esY: 29.037893 },
        { name: '浦江县', esX: 119.903937, esY: 29.520086 },
        { name: '金华开发区', esX: 119.63356, esY: 29.089116 },
      ],
      video_point: "119.642632, 29.082182",
      sgId: null,
      sgLon: null,
      sgLat: null,
      sgVideoList: [],
      showVideoFind: false,
      tableData: [],
      showSOSno: false,
      sos: {
        sosNOdata: [], //未处理
        sosISdata: [], //已处理
      },
      gjxxData: [],
      dom1: null,
      time1: null,
      dom2: null,
      time2: null,
      showZfryGJ: false,
      gjType: null,
      jhGeojson: null, //金华市geojson
      rowClickIndex: null,
      rowrwClickIndex: null,
      showVideoAndZfryPoint: false,
      datas1: [
        moment().add("month", -2).format("YYYY-MM-DD"),
        moment(new Date()).format("YYYY-MM-DD"),
      ],
      datas2: [
        moment().add("month", -2).format("YYYY-MM-DD"),
        moment(new Date()).format("YYYY-MM-DD"),
      ],
      uavInfo: {
        account:"<EMAIL>",
        password:"lm5170159",
        device_sn:"7CTDLA900A0031"
      }
    },
    computed: {
      city() {
        return localStorage.getItem("city");
      },
    },
    created() {
      // this.initjh();
      this.init(localStorage.getItem("city"));
    },
    mounted() {
      this.video_point = this.pointCenter.find(item => item.name == localStorage.getItem("city")).esX + ", " + this.pointCenter.find(item => item.name == localStorage.getItem("city")).esY
      this.getTaskStatistics();
      this.queryTableData(this.tabActive);
      this.initVideo(); //初始市政府周边视频
      // 表格滚动
      this.dom1 = document.getElementById("box");
      this.mouseleaveEvent();
      this.dom2 = document.getElementById("box2");
      this.mouseleaveEvent2();
      let this_ = this;
      window.addEventListener("message", (e) => {
        if (e.data && e.data.glDatas) {
          this_.zfryGl(e.data.glDatas);
        }
        if (e.data && e.data.changeVideo) {
          this.sgVideoList = e.data.changeVideo;
          $("#videoCon").attr(
            "src",
            `/static/citybrain/commonts/zhdd/video-16-9.html?name=
                  ${JSON.stringify(e.data.changeVideo)}`
          );
        }
        if (e.data && e.data.type == "refresh") {
          if (e.data.data.yqmsg) {
            this_.getYq(e.data.data);
          } else {
            this_.getSOS(e.data.data);
          }
          this_.getTaskStatistics()
          this_.queryTableData(this_.tabActive);
        }
        if (e.data && e.data.舆情中心) {
          this_.zfryPopFun(e.data.舆情中心);
          this_.getTaskStatistics()
          this_.queryTableData(this_.tabActive);
        }
        if (e.data && e.data.glid) {
          this.linghtRwgz(e.data.glid);
        }
      });
    },
    methods: {
      StartUAVClick(obj) {
        parent.postMessage(JSON.stringify({data: obj,type:"uav"}))
      },
      async initjh() {
        this.jhGeojson = await $get("/jhBound");
        this.queryData1(this.datas1);
      },
      init(city) {
        let this_ = this;
        this.showSOSno = false;
        $api("/xzzf_zfy_sos", {
          county: city == "金华市" ? "" : city,
          endTime: (this_.datas1 && this_.datas1[1] + " 24:00:00") || "",
          startTime: (this_.datas1 && this_.datas1[0] + " 00:00:00") || "",
        }).then((res) => {
          this.sos.sosISdata = res.filter(
            (a) => a.alarmStatus == 1
            // &&
            // turf.booleanPointInPolygon(
            //   [a.longitude, a.latitude],
            //   this_.jhGeojson.features[0]
            // )
          );
          this.sos.sosNOdata = this.gjxxData = res
            .filter(
              (a) => a.alarmStatus == 0
              //  &&
              // turf.booleanPointInPolygon(
              //   [a.longitude, a.latitude],
              //   this_.jhGeojson.features[0]
              // )
            );

          this.gjxxData.forEach((item) => {
            this.tableData.forEach((value) => {
              if (value.detailsId == item.id) {
                item.isHave = true;
              }
            });
          });
        });
      },
      queryData1(e) {
        this.init("金华市");
      },
      queryData2(e) {
        this.getTaskStatistics()
        this.queryTableData(this.tabActive);
      },
      orderByLight(list) {
        list.sort((a, b) => {
          return b.isLight - a.isLight;
        });
      },
      linghtSos(obj) {
        if (obj.taskSource == "舆情中心") {
          if (obj.id != this.rowrwClickIndex) {
            this.rowrwClickIndex = obj.id;
            window.parent.frames["zhdd_bottom"].postMessage(
              {
                glid: obj.detailsId,
              },
              "*"
            );
          } else {
            this.rowrwClickIndex = null;
            window.parent.frames["zhdd_bottom"].postMessage(
              {
                glid: obj.detailsId,
              },
              "*"
            );
          }
          return;
        }
        if (obj.id != this.rowrwClickIndex) {
          this.rowrwClickIndex = obj.id;
          let isHave = false;
          this.gjxxData.forEach((item) => {
            if (item.id == obj.detailsId) {
              item.isLight = 1;
              isHave = true;
            } else {
              item.isLight = 0;
            }
          });
          if (!isHave) {
            this.gjxxData.forEach((item) => {
              item.isLight = 0;
            });
            this.$message({
              message: "该任务未关联sos告警",
              type: "warning",
            });
            // this.mouseleaveEvent();
            return;
          }
          this.orderByLight(this.gjxxData);
          this.dom1.scrollTop = 0;
          clearInterval(this.time1);
        } else {
          this.rowrwClickIndex = null;
          this.gjxxData.forEach((item) => {
            item.isLight = 0;
          });
          this.$forceUpdate();
          // this.mouseleaveEvent();
        }
      },
      linghtRwgz(id) {
        if (id != this.rowClickIndex) {
          let isHave = false;
          this.tableData.forEach((item) => {
            if (item.detailsId == id) {
              item.isLight = 1;
              isHave = true;
            } else {
              item.isLight = 0;
            }
          });
          if (!isHave) {
            this.tableData.forEach((item) => {
              item.isLight = 0;
            });
            this.$message({
              message: "该告警未产生关联任务",
              type: "warning",
            });
            // this.mouseleaveEvent2();
            return;
          }
          this.orderByLight(this.tableData);
          this.dom2.scrollTop = 0;
          clearInterval(this.time2);
        } else {
          this.tableData.forEach((item) => {
            item.isLight = 0;
          });
          this.$forceUpdate();
          // this.mouseleaveEvent2();
        }
      },
      zfryPopFun(item) {
        let typeName =
          item.type == "调度呼叫"
            ? "ddhjVideoTest"
            : item.type == "查看直播"
              ? "openVideoTest"
              : "";
        if (item.type == "人员轨迹") {
          this.showZfryGJ = !this.showZfryGJ;
          this.hideIframe();
          if (this.showZfryGJ) {
            window.parent.lay.openIframe({
              type: "openIframe", //指令
              name: "tcgl_ryglDate",
              src: "/static/citybrain/tcgl/commont/tcgl_ryglDate.html",
              left: "0px",
              top: "200px",
              width: "1030px",
              height: "1900px",
              zIndex: 997,
              argument: {
                tcgl_ryglDate: item.sys_depart,
                county: item.county,
              },
            });
          } else {
            window.parent.lay.closeIframeByNames(["tcgl_ryglDate"]);
            window.parent.lrFrameSHClick();
          }
        }
        if (item.type == "接收告警") {
          if (item.state == "0") return;
          let that = this;
          // that.gjType = null;
          this.$alert(
            `<form action="" style="font-size: 30px;margin: 20px 0px;">
                  <input type="radio" name="sex" value="一键通知" onclick="zhddRightVm.gjType='一键通知'" style="width: 20px;height: 20px;">
                  <span style="margin-left:5px">一键通知</span>
                  <input type="radio" name="sex" value="一键调度" onclick="zhddRightVm.gjType='一键调度'" style="width: 20px;height: 20px;">
                  <span style="margin-left:5px">一键调度</span>
                  <input type="radio" name="sex" value="取消预警" onclick="zhddRightVm.gjType='取消预警'" style="width: 20px;height: 20px;">
                  <span style="margin-left:5px">取消预警</span>
                </form>`,
            "请选择接受告警类型",
            {
              dangerouslyUseHTMLString: true,
              confirmButtonText: "确定",
              callback: (active) => {
                if (active == "cancel") {
                  // that.gjType = null;
                } else if (active == "confirm") {
                  if (that.gjType == "一键通知") {
                    that.yjtz(item);
                  } else if (that.gjType == "一键调度") {
                    that.yjdd(item);
                  } else if (that.gjType == "取消预警") {
                    if (item.id && item.yqmsg) {
                      that.getYq(item);
                    } else {
                      that.getSOS(item);
                    }
                  }
                }
              },
            }
          );
          //
        }
        if (typeName == "") return;
        this.zfryHj({
          name: typeName,
          sys_depart: item.sys_depart,
          county: item.county,
          hostcode: item.hostcode,
        });
      },
      yjtz(item) {
        window.parent.lay.openIframe({
          type: "openIframe",
          name: "zhddNotice",
          id: "zhddNotice",
          src:
            baseURL.url + "/static/citybrain/commonts/zhdd/zhddNotice.html",
          left: "1280px",
          top: "575px",
          width: "1300px",
          height: "800px",
          zIndex: "666",
          argument: {
            type: "openyjtz",
            city: localStorage.getItem("city"),
            data: item,
          },
        });
      },
      yjdd(item) {
        window.parent.lay.openIframe({
          type: "openIframe",
          name: "zhddDispatch",
          id: "zhddDispatch",
          src:
            baseURL.url +
            "/static/citybrain/commonts/zhdd/zhddDispatch1.html",
          left: "1020px",
          top: "380px",
          width: "1800px",
          height: "1000px",
          zIndex: "666",
          argument: {
            type: "openyjdd",
            city: localStorage.getItem("city"),
            data: item,
          },
        });
      },
      zfryGl(glDatas) {
        let this_ = this;
        window.parent.mapUtil.flyTo({
          destination: glDatas[0], //飞行中心点
          zoom: 15, //飞行层级
        });
        let endPoint = [
          {
            lng: glDatas[glDatas.length - 1][0],
            lat: glDatas[glDatas.length - 1][1],
          },
        ];
        this_.addPoint(endPoint, "zfry_point_终点", "zhdd_routeplan_end");
        let startPoint = [
          {
            lng: glDatas[0][0],
            lat: glDatas[0][1],
          },
        ];
        this_.addPoint(endPoint, "zfry_point_起点", "zhdd_routeplan_start");
        window.parent.mapUtil.loadPolylineLayer({
          layerid: "zfry_point_lines",
          lines: [glDatas], //新地图  接口参数lines
          style: {
            width: 10,
            color: [28, 214, 108, 1],
            // image:
            //   baseURL.url +
            //   "/static/citybrain/hjbh/img/rkzt/jiantou_Right.png",
          },
        });
        // try {
        //   this_.zfryGjLayer.clear();
        // } catch {}
        // let endPoint = [
        //   {
        //     lng: glDatas[glDatas.length - 1][0],
        //     lat: glDatas[glDatas.length - 1][1],
        //   },
        // ];
        // this_.addPoint(endPoint, "zfry_point_终点", "zhdd_routeplan_end");
        // let view = window.parent.view;
        // this_.zfryGjLayer = new window.parent.ArcGisUtils.RouteLayer({
        //   view,
        //   data: glDatas,
        //   lineStyle: {
        //     color: [226, 119, 40],
        //     width: 5,
        //   },
        //   effectStyle: {
        //     url:
        //       baseURL.url +
        //       "/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/zhdd_routeplan_start.png",
        //     width: "100px",
        //     height: "100px",
        //   },
        //   speed: 3, // 速度，默认1
        // });
        // this_.zfryGjLayer.start();
      },
      zfryHj(obj) {
        const { name, sys_depart, county, hostcode } = obj;
        let url = "";
        if (county === "永康市") {
          url = "/static/citybrain/zhdd/zfy/zfjly_yks/video.html";
        } else if (county === "婺城区") {
          url = "/static/citybrain/zhdd/zfy/jkVideo/test.html";
        } else if (county === "磐安县") {
          url = "/static/citybrain/zhdd/zfy/zfjly_pax/video.html";
        } else if(county === "义乌市") {
          url = "/static/citybrain/zhdd/zfy/jkVideo/test.html";
        } else if (county === "武义县") {
          if (hostcode == undefined || hostcode != null) {
            this.$message({
              message: "未绑定执法仪设备！",
              type: "warning",
            });
          } else {
            wyxZfy.startClient();
            wyxZfy.connectClient({
              hostcode: hostcode,
              sys_depart: sys_depart,
            });
          }
          return;
        }
        if (url === "") {
          this.$message({
            message: "暂未接入该县市区数据！",
            type: "warning",
          });
          return;
        }
        window.parent.lay.openIframe({
          type: "openIframe", //指令
          name: "videoTest",
          src: baseURL.url + url,
          left: "1120px",
          top: "572px",
          width: "1509px",
          height: "1009px",
          zIndex: 667,
          argument: {
            name: obj.name,
            videoCode: [obj.sys_depart],
          },
        });
      },
      // 收缩两边-隐藏
      hideIframe() {
        let lefthideCss =
          window.parent.document.getElementsByClassName("page_left")[0];
        if (
          lefthideCss != undefined &&
          lefthideCss.className.indexOf("fadeInLeft") != -1
        ) {
          window.parent.lrFrameSHClick && window.parent.lrFrameSHClick();
        }
      },
      // 收缩两边-显示
      showIframe() {
        let lefthideCss =
          window.parent.document.getElementsByClassName("page_left")[0];
        if (
          lefthideCss != undefined &&
          lefthideCss.className.indexOf("fadeOutLeft") != -1
        ) {
          window.parent.lrFrameSHClick && window.parent.lrFrameSHClick();
        }
      },
      // sos告警-地图弹窗关闭
      zfryPopClose() {
        window.parent.mapUtil.removeLayer("sos_pop");
        try {
          window.parent.mapUtil.removeAllLayers([
            "zfry_point_终点",
            "zfry_point_起点",
            "zfry_point_lines",
          ]);
          window.parent.lay.closeIframeByNames(["tcgl_ryglDate"]);
          this.showIframe();
          this.zfryGjLayer.clear();
        } catch {}
      },
      getYq(item) {
        let this_ = this;
        axios({
          method: "post",
          url:
            baseURL.url + "/jhyjzh-server/screen_api/xzzfyq_state_update",
          data: { id: item.id, status: 0 },
        }).then(function (res) {
          if (res.data.data == 1) {
            this_.$message({
              message: "舆情上报成功",
              type: "success",
            });
            window.parent.frames["yqxq"] &&
            window.parent.lay.closeIframeByNames(["yqxq"]);
            window.parent.frames["zhdd_bottom"] &&
            window.parent.frames["zhdd_bottom"].vm.initApi(
              window.parent.frames["zhdd_bottom"].vm.city
            );
          }
        });
      },
      // sos接收告警
      getSOS(item) {
        let this_ = this;
        axios({
          method: "get",
          url:
            baseURL.url +
            "/jhyjzh-server/screen_api/zhdd/xzzf/updateAlarmStatus",
          params: { id: item.id, county: item.city },
        }).then(function (res) {
          if (res.data.data == 1) {
            this_.$message({
              message: "接受告警成功",
              type: "success",
            });
            this_.zfryPopClose();
            this_.init(this_.city);
          }
        });
      },
      //sos告警列表-定位上点
      SOSfun(item) {
        this.linghtRwgz(item.id);
        if (item.id != this.rowClickIndex) {
          this.rowClickIndex = item.id;
          window.parent.mapUtil.flyTo({
            destination: [item.longitude, item.latitude], //飞行中心点
            zoom: 16, //飞行层级
          });
          this.SOSpoint(item);
          this.video_point = item.longitude + "," + item.latitude;
          console.log(this.video_point);
          // localStorage.setItem("point", this.video_point);
          this.findVideoByPoint(this.video_point, true);
          this.zfryPoint(this.video_point);
          this.qyopen(item);
        } else {
          window.parent.mapUtil.removeAllLayers(["zfry_point", "zfry_sos"]);
          window.parent.mapUtil.removeLayer("syr");
          window.parent.mapUtil.removeLayer("syr1");
          window.parent.mapUtil.removeLayer("camera-load-icon");
          this.rowClickIndex = null;
          this.openorclose=!this.openorclose
          this.qyopen(item);
          this.openorclose=!this.openorclose
        }
      },
      zfryPoint(point_center) {
        window.parent.mapUtil.removeLayer("zfry_point");
        let that = this;
        $api("/xzzf_zfy", { point: point_center, distance: 1 }).then(
          (res) => {
            let pointData = [];
            res.map((a) => {
              if (point_center != a.lat + "," + a.lon) {
                pointData.push({
                  name: (a.name && a.name) || "-",
                  phone: (a.phone && a.phone) || "-",
                  sys_depart: (a.sys_depart && a.sys_depart) || "-",
                  dept_name: (a.dept_name && a.dept_name) || "-",
                  lat: a.lat,
                  lng: a.lon,
                  county: (a.county && a.county) || "-",
                  lasttime: (a.lasttime && a.lasttime) || "-",
                  lineon: (a.lineon && a.lineon) || "-1",
                });
              }
            });
            window.parent.mapUtil.loadPointLayer({
              data: pointData,
              layerid: "zfry_point", //图层id
              iconcfg: {
                image:
                  baseURL.url +
                  `/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/zhdd_zfry_static.png`, //图标路径
                iconSize: 0.3, //图标尺寸
                iconlist: {
                  field: "lineon", //地图筛选字段依据 //将你需要分类的点按照你想要的分类处理好，并将分类传入地图
                  list: [
                    {
                      value: "1",
                      size: "80",
                      src:
                        baseURL.url +
                        `/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/zhdd_zfry_static.png`,
                    },
                    {
                      value: "0",
                      size: "80",
                      src:
                        baseURL.url +
                        `/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/xzzf_grey.png`,
                    },
                    {
                      value: "2",
                      size: "80",
                      src:
                        baseURL.url +
                        `/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/zhdd_map_zydd_jydw.png`,
                    },
                    {
                      value: "-1",
                      size: "80",
                      src:
                        baseURL.url +
                        `/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/zhdd_map_zydd_jydw.png`,
                    },
                  ],
                },
              }, //图标 // cluster: false,//是否聚合,默认聚合 // datacfg: datacfg,
              cluster: false,
              onclick: that.openPop, //点击事件
            });
          }
        );
      },
      zfryPop(e) {
        //       let objData = {
        //         layerid: "zfry_pop",
        //         position: [e.esX, e.esY],
        //         offset: [100, -40],
        //         content: `<div
        //   style="
        //     position: relative;
        //     background: url('/static/citybrain/csdn/img/du_bg2.png') no-repeat;
        //     background-size: 100% 100%;
        //     width: max-content;
        //     min-height: 250px;
        //   ">
        //   <nav
        //     style="
        //       display: flex;
        //       justify-content: space-between;
        //       align-items: flex-end;
        //       padding-bottom: 10px;
        //       margin: 0 20px;
        //       border-bottom: 1px solid;
        //       border-bottom: 2px solid;
        //       border-image: linear-gradient(-90deg, rgba(0, 216, 247, 0) 0%, #00afed 100%) 2 2 2 2;
        //       padding-left: 20px;
        //     ">
        //     <h2 style="margin-top: 20px; white-space: nowrap; color: #fff; font-size: 35px">执法人员详情</h2>
        //     <span style="cursor: pointer" onclick="this.parentNode.parentNode.style.display = 'none'">
        //       <img style="vertical-align: middle" src="/static/citybrain/csdn/img/close.png" alt="" />
        //     </span>
        //   </nav>
        //   <header class="s-m-l-20 s-m-r-20 s-m-t-10 s-flex s-row-between s-font-30" style="padding-bottom: 13%;">
        //                   <div class="s-m-l-40">
        //                     <p style="width: 700px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">队员姓名 ：<span style="color: #ffff;">${
        //                       e.name
        //                     }</span></p>
        //                     <p style="width: 700px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">所属部门 ：<span style="color: #ffff;">${
        //                       e.dept_name
        //                     }</span></p>
        //                     <p style="width: 700px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">队员编号 ：<span style="color: #ffff;">${
        //                       e.sys_depart || "--"
        //                     }</span></p>
        //                     <p style="width: 700px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">SOS告警时间 ：<span style="color: #ffff;">${
        //                       e.lasttime || "--"
        //                     }</span></p>
        //                     <p class="s-font-30 s-flex s-m-t-10 s-c-white s-flex-wrap s-row-evenly">
        //                       <span style="cursor:${
        //                         e.sys_depart ? "pointer" : "no-drop"
        //                       };background:#00c4fc;padding: 2px 20px;border-radius: 15px;" onclick="window.parent.frames['zhdd_right'].zhddRightVm.zfryPopFun({type:'调度呼叫',
        //                       sys_depart:'${e.sys_depart}',
        //                       county:'${e.county}',
        //                       hostcode:'${e.hostcode}'})"
        //                       id="ddhjBtn" >调度呼叫</span>
        //                       <span style="cursor:${
        //                         e.sys_depart ? "pointer" : "no-drop"
        //                       };background:#00c4fc;padding: 2px 20px;border-radius: 15px;" onclick="window.parent.frames['zhdd_right'].zhddRightVm.zfryPopFun({type:'查看直播',
        //                       sys_depart:'${e.sys_depart}',
        //                         county:'${e.county}',
        //                        hostcode:'${e.hostcode}'})"
        //                       id="ckzbBtn">查看直播</span>
        //                     </p>
        //                     <p  class="s-font-30 s-flex s-m-t-15 s-c-white s-flex-wrap s-row-evenly">
        //                       <span onclick="window.parent.frames['zhdd_right'].zhddRightVm.zfryPopFun({type:'人员轨迹',
        //                       sys_depart:'${e.sys_depart}',
        //                                           county:'${e.county}'})"
        //                         id="rygjBtn" style="cursor:pointer;background:#00c4fc;padding: 2px 20px;border-radius: 15px;">人员轨迹</span>
        //                       <span onclick="window.parent.frames['zhdd_right'].zhddRightVm.zfryPopFun({type:'接收告警',
        //                       city:'${e.county}',unitname:'${e.dept_name}',lng:'${
        //           e.lng
        //         }',lat:'${e.lat}',
        //                       id:'${e.data && e.data.id}',
        //                       state:'${e.state}'})" id="jsgjBtn" style="cursor:${
        //           e.state == "1" ? "pointer" : "no-drop"
        //         };background:#00c4fc;padding: 2px 20px;border-radius: 15px;">接受告警</span>
        //                     </p>
        //                   </div>
        //               </header>
        // </div>`,
        //       };
        //       window.parent.mapUtil._createPopup(objData);
      },
      //sos告警-打点
      SOSpoint(item) {
        let this_ = this;
        window.parent.mapUtil.removeAllLayers(["zfry_sos"]);
        let pointData = [];
        if (item.constructor === Object) {
          pointData = [
            {
              lng: (item.longitude && item.longitude) || "-",
              lat: (item.latitude && item.latitude) || "-",
              sys_depart: (item.deviceid && item.deviceid) || "-",
              name: (item.hostname && item.hostname) || "-",
              dept_name: (item.unitname && item.unitname) || "-",
              msgtime: (item.lasttime && item.lasttime) || "-",
              state: (item.alarmStatus && item.alarmStatus) || "-",
              county: (item.county && item.county) || "-",
              data: item,
              isSoS:"true"
            },
          ];
        } else if (item.constructor === Array) {
          pointData = item.map((a) => {
            return {
              lng: (a.longitude && a.longitude) || "-",
              lat: (a.latitude && a.latitude) || "-",
              sys_depart: (a.deviceid && a.deviceid) || "-",
              name: (a.hostname && a.hostname) || "-",
              dept_name: (a.unitname && a.unitname) || "-",
              msgtime: (a.lasttime && a.lasttime) || "-",
              state: (a.alarmStatus && a.alarmStatus) || "-",
              county: (a.county && a.county) || "-",
              data: a,
              isSoS:"true"
            };
          });
        }

        window.parent.mapUtil.loadPointLayer({
          data: pointData, //点位数据
          layerid: "zfry_sos", //图层id
          iconcfg: {
            image: `/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/红色预警事件.png`,
            iconSize: 0.6,
          },
          popcfg: {
            offset: [50, -100],
            show: false, //关闭按钮
          },
          cluster: false,
          onclick: this_.openPop,
        });
      },
      //区域情况
      qyopen(item) {
        if (this.openorclose){
          window.parent.lay.openIframe({
            type: "openIframe",
            name: "qyqk3840",
            src:
              baseURL.url + "/static/citybrain/commonts/zhdd/sou_range.html",
            width: "500px",
            height: "300px",
            right: "1200px",
            top: "208px",
            zIndex: 100,
            argument: { center_point: [item.longitude, item.latitude] },
          });
        }else{
          window.parent.lay.closeIframeByNames(["qyqk3840"]);
        }
      },
      // pop弹窗
      openPop(e) {
        console.log(e,"sos");
        let this_ = this;
        let isSos = e.layerid == "zfry_sos";
        let str = `
                      <div
                    style="
                      position: relative;
                      background: url('/static/citybrain/csdn/img/du_bg2.png') no-repeat;
                      background-size: 100% 100%;
                      width: max-content;
                      min-height: 250px;
                    "
                  >
                    <nav
                      class="s-flex s-m-l-20 s-m-r-20 s-row-between s-p-b-10 s-col-bottom"
                      style="
                        border-bottom: 1px solid;
                        border-bottom: 2px solid;
                        border-image: linear-gradient(-90deg, rgba(0, 216, 247, 0) 0%, #00afed 100%) 2 2 2 2;
                        padding-left: 20px;
                      "
                    >
                  <h2 style="margin-top: 20px; white-space: nowrap;color: #fff;font-size:35px;" class="s-flex">执法人员详情</h2>
                      <span
                        class="s-m-l-20 s-font-30 s-c-white"
                        style="cursor: pointer"
                        onclick="window.parent.frames['zhdd_right'].zhddRightVm.zfryPopClose()"
                      >
                        <img style="vertical-align: middle;" src="/static/citybrain/csdn/img/close.png" alt="" />
                      </span>
                    </nav>

                    <header class="s-m-l-20 s-m-r-20 s-m-t-10 s-flex s-row-between s-font-30" style="padding-bottom: 13%;">
                        <div class="s-m-l-40">
                          <p style="width: 700px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">队员姓名 ：<span style="color: #ffff;">${
          e.name
        }</span></p>
                          <p style="width: 700px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">所属部门 ：<span style="color: #ffff;">${
          e.dept_name
        }</span></p>
                          <p style="width: 700px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">设备编号 ：<span style="color: #ffff;">${
          e.sys_depart || "--"
        }</span></p>
                          <p style="width: 700px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">SOS告警时间 ：<span style="color: #ffff;">${
          e.lasttime || "--"
        }</span></p>
                          <p class="s-font-30 s-flex s-m-t-10 s-c-white s-flex-wrap s-row-evenly">
                            <span style="cursor:${
          e.sys_depart ? "pointer" : "no-drop"
        };background:#00c4fc;padding: 2px 20px;border-radius: 15px;" onclick="window.parent.frames['zhdd_right'].zhddRightVm.zfryPopFun({type:'调度呼叫',
                            sys_depart:'${e.sys_depart}',
                            county:'${e.county}',
                            hostcode:'${e.hostcode}'})"
                            id="ddhjBtn" >调度呼叫</span>
                            <span style="cursor:${
          e.sys_depart ? "pointer" : "no-drop"
        };background:#00c4fc;padding: 2px 20px;border-radius: 15px;" onclick="window.parent.frames['zhdd_right'].zhddRightVm.zfryPopFun({type:'查看直播',
                            sys_depart:'${e.sys_depart}',
                              county:'${e.county}',
                             hostcode:'${e.hostcode}'})"
                            id="ckzbBtn">查看直播</span>
                          </p>
                          <p  class="s-font-30 s-flex s-m-t-15 s-c-white s-flex-wrap s-row-evenly">
                            <span onclick="window.parent.frames['zhdd_right'].zhddRightVm.zfryPopFun({type:'人员轨迹',
                            sys_depart:'${e.sys_depart}',
                                                county:'${e.county}'})"
                              id="rygjBtn" style="cursor:pointer;background:#00c4fc;padding: 2px 20px;border-radius: 15px;">人员轨迹</span>
                            <span onclick="window.parent.frames['zhdd_right'].zhddRightVm.zfryPopFun({type:'接收告警',
                            city:'${e.county}',unitname:'${e.dept_name}',lng:'${
          e.lng
        }',lat:'${e.lat}',
                            id:'${e.data && e.data.id}',
                            state:'${e.state}'})" id="jsgjBtn" style="cursor:${
          e.state == "1" ? "pointer" : "no-drop"
        };display:${
          isSos ? "block" : "none"
        };background:#00c4fc;padding: 2px 20px;border-radius: 15px;">接受告警</span>

            <span id="jsgjBtn" style="display:${
          isSos ? "none" : "block"
        };cursor:no-drop;background:#7e8b8e;padding: 2px 20px;border-radius: 15px;">接受告警</span>
                          </p>
                        </div>
                    </header>
                  </div>
                `;
        let strSoS = `
                      <div
                    style="
                      position: relative;
                      background: url('/static/citybrain/csdn/img/du_bg2.png') no-repeat;
                      background-size: 100% 100%;
                      width: max-content;
                      min-height: 250px;
                    "
                  >
                    <nav
                      class="s-flex s-m-l-20 s-m-r-20 s-row-between s-p-b-10 s-col-bottom"
                      style="
                        border-bottom: 1px solid;
                        border-bottom: 2px solid;
                        border-image: linear-gradient(-90deg, rgba(0, 216, 247, 0) 0%, #00afed 100%) 2 2 2 2;
                        padding-left: 20px;
                      "
                    >
                  <h2 style="margin-top: 20px; white-space: nowrap;color: #fff;font-size:35px;" class="s-flex">执法人员详情</h2>
                      <span
                        class="s-m-l-20 s-font-30 s-c-white"
                        style="cursor: pointer"
                        onclick="window.parent.frames['zhdd_right'].zhddRightVm.zfryPopClose()"
                      >
                        <img style="vertical-align: middle;" src="/static/citybrain/csdn/img/close.png" alt="" />
                      </span>
                    </nav>

                    <header class="s-m-l-20 s-m-r-20 s-m-t-10 s-flex s-row-between s-font-30" style="padding-bottom: 13%;">
                        <div class="s-m-l-40">
                          <p style="width: 700px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">队员姓名 ：<span style="color: #ffff;">${
          e.name
        }</span></p>
                          <p style="width: 700px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">所属部门 ：<span style="color: #ffff;">${
          e.dept_name
        }</span></p>
                          <p style="width: 700px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">设备编号 ：<span style="color: #ffff;">${
          e.sys_depart || "--"
        }</span></p>
                          <p style="width: 700px;font-size: 30px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">SOS告警时间 ：<span style="color: #ffff;">${
          e.lasttime || "--"
        }</span></p>
                          <p class="s-font-30 s-flex s-m-t-10 s-c-white s-flex-wrap s-row-evenly">
                            <span style="cursor:${
          e.sys_depart ? "pointer" : "no-drop"
        };background:#00c4fc;padding: 2px 20px;border-radius: 15px;" onclick="window.parent.frames['zhdd_right'].zhddRightVm.zfryPopFun({type:'调度呼叫',
                            sys_depart:'${e.sys_depart}',
                            county:'${e.county}',
                            hostcode:'${e.hostcode}'})"
                            id="ddhjBtn" >调度呼叫</span>
                            <span style="cursor:${
          e.sys_depart ? "pointer" : "no-drop"
        };background:#00c4fc;padding: 2px 20px;border-radius: 15px;" onclick="window.parent.frames['zhdd_right'].zhddRightVm.zfryPopFun({type:'查看直播',
                            sys_depart:'${e.sys_depart}',
                              county:'${e.county}',
                             hostcode:'${e.hostcode}'})"
                            id="ckzbBtn">查看直播</span>
                          </p>
                          <p  class="s-font-30 s-flex s-m-t-15 s-c-white s-flex-wrap s-row-evenly">
                            <span onclick="window.parent.frames['zhdd_right'].zhddRightVm.zfryPopFun({type:'人员轨迹',
                            sys_depart:'${e.sys_depart}',
                                                county:'${e.county}'})"
                              id="rygjBtn" style="cursor:pointer;background:#00c4fc;padding: 2px 20px;border-radius: 15px;">人员轨迹</span>
                            <span onclick="window.parent.frames['zhdd_right'].zhddRightVm.zfryPopFun({type:'接收告警',
                            city:'${e.county}',unitname:'${e.dept_name}',lng:'${
          e.lng
        }',lat:'${e.lat}',
                            id:'${e.data && e.data.id}',
                            state:'${e.state}'})" id="jsgjBtn" style="cursor:${
          e.state == "1" ? "pointer" : "no-drop"
        };display:${
          isSos ? "block" : "none"
        };background:#00c4fc;padding: 2px 20px;border-radius: 15px;">接受告警</span>

            <span id="jsgjBtn" style="display:${
          isSos ? "none" : "block"
        };cursor:no-drop;background:#7e8b8e;padding: 2px 20px;border-radius: 15px;">接受告警</span>
                          </p>
                          <p  class="s-font-30 s-flex s-m-t-15 s-c-white s-flex-wrap s-row-evenly">
                            <span onclick="window.parent.frames['zhdd_right'].zhddRightVm.StartUAVClick({lng:'${e.lng}',lat:'${e.lat}'})"
                              id="rygjBtn" style="cursor:pointer;background:#00c4fc;padding: 2px 20px;border-radius: 15px;">启动无人机巡航</span>
                          </p>
                        </div>
                    </header>
                  </div>
                `;
        let objData = {
          layerid: "sos_pop",
          position: [e.esX, e.esY],
          offset: [100, -40],
          content: e.isSoS == "true"?strSoS:str,
        };
        window.parent.mapUtil._createPopup(objData);
      },
      mouseenterEvent() {
        clearInterval(this.time1);
      },
      mouseleaveEvent() {
        let dom1 = document.getElementById("box");
        this.time1 = setInterval(() => {
          dom1.scrollBy({
            top: 97,
            behavior: "smooth",
          });
          if (
            dom1.scrollTop >=
            dom1.scrollHeight - dom1.offsetHeight
          ) {
            dom1.scrollTop = 0;
          }
        }, 1500);
      },
      mouseenterEvent2() {
        clearInterval(this.time2);
      },
      mouseleaveEvent2() {
        let dom2 = document.getElementById("box2");
        this.time2 = setInterval(() => {
          dom2.scrollBy({
            top: 90,
            behavior: "smooth",
          });
          if (
            dom2.scrollTop >=
            dom2.scrollHeight - dom2.offsetHeight
          ) {
            dom2.scrollTop = 0;
          }
        }, 1500);
      },
      getStatusText(item) {
        return item && item.length > 4
          ? item.slice(0, 4) + "..."
          : item == null
            ? ""
            : item;
        // if (item.status === 0 || item.status === 1) {
        //   return "待处置";
        // }
        // if (item.status === 2) {
        //   return "已反馈";
        // }
        // return "";
      },
      //打开任务详情弹窗
      showTaskDetail(obj) {
        window.parent.lay.openIframe({
          type: "openIframe",
          name: "taskxq",
          id: "taskxq",
          src: baseURL.url + "/static/citybrain/commonts/zhdd/taskxq.html",
          left: "1168px",
          top: "360px",
          width: "1509px",
          height: "1009px",
          zIndex: "130",
          argument: {
            id: obj.id,
            idType: obj.idType
          },
        });
      },
      openVideoFind() {
        if (this.sgVideoList.length == 0) return;
        this.showVideoFind = !this.showVideoFind;
        if (this.showVideoFind) {
          window.parent.lay.openIframe({
            type: "openIframe",
            name: "video_find",
            id: "video_find",
            src:
              baseURL.url +
              "/static/citybrain/commonts/zhdd/video_find.html",
            left: "2900px",
            top: "900px",
            width: "900px",
            height: "570px",
            zIndex: "700",
            argument: {
              video_find: {
                sgid: this.sgId,
                point: this.video_point,
                videoList: this.sgVideoList,
              },
            },
          });
        } else {
          window.parent.lay.closeIframeByNames(["video_find"]);
        }
      },
      // initApi() {
      //   let this_ = this;
      //   axios({
      //     method: "get",
      //     url:
      //       baseURL.url + "/jhyjzh-server/screen_api/zhddzx/zhddzxLeft005",
      //     params: { name: "", qx: this.qx[0].code },
      //   }).then(function (data) {
      //     this_.zfdt = data.data.data.data;
      //   });
      //   // 获取事故id
      //   axios({
      //     method: "get",
      //     url:
      //       baseURL.url + "/jhyjzh-server/screen_api/zhddzx/zhddzxLeft002",
      //   }).then(function (data) {
      //     var sgobj = data.data.data.find(
      //       (a) => a.sgztcn != "完成" && a.sgid != ""
      //     );
      //     if (sgobj != null) {
      //       this_.sgLat = sgobj.point.split(",")[1];
      //       this_.sgLon = sgobj.point.split(",")[0];
      //       this_.sgId = sgobj.sgid;
      //       this_.findVideoByid(sgobj.sgid);
      //     } else {
      //       this_.flyToFun(this_.xzzfzx_point.split(","), 16);
      //       this_.addPoint(
      //         [
      //           {
      //             lng: this_.xzzfzx_point.split(",")[0],
      //             lat: this_.xzzfzx_point.split(",")[1],
      //           },
      //         ],
      //         "xzzfzx_point",
      //         "/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/zhdd_map_hdz_active.png",
      //         1
      //       );
      //       this_.findVideoByPoint();
      //     }
      //   });
      //   this.queryTableData();
      // },
      initVideo() {
        // 根据新需求（视角继续为驾驶舱的板块）注释掉
        // this.flyToFun(this.xzzfzx_point.split(","), 12);
        this.findVideoByPoint(this.getCityPointCenter(localStorage.getItem("city")), false);
      },
      getCityPointCenter(city) {
        let point = ""
        this.pointCenter.forEach((item,i) => {
          if (item.name == city) {
            point = item.esX + ", " + item.esY
          }
        })
        return point
      },
      //任务统计
      getTaskStatistics() {
        $api("/xzzfj_zhtx_rwzztj", {
          queryStartTime: (this.datas2 && this.datas2[0]) || "",
          queryEndTime: (this.datas2 && this.datas2[1]) || "",
          area:
            localStorage.getItem("city") == "金华市"
              ? ""
              : localStorage.getItem("city"),
        }).then((res) => {
          this.tabs[1].value = res[0].waitDispose;
          this.tabs[2].value = res[0].reDisposal;
          this.tabs[3].value = res[0].waitDisposal;
          this.tabs[4].value = res[0].endDisposal;
          this.tabs[0].value = 0;
          this.tabs.slice(1, this.tabs.length).forEach((item) => {
            this.tabs[0].value += item.value;
          });
        });
      },
      //任务跟踪列表
      queryTableData(i) {
        this.tabActive = i;
        $api("/xzzfj_zhdd_rwzzlb", {
          queryStartTime: (this.datas2 && this.datas2[0]) || "",
          queryEndTime: (this.datas2 && this.datas2[1]) || "",
          area:
            localStorage.getItem("city") == "金华市"
              ? ""
              : localStorage.getItem("city"),
          status: this.tabs[this.tabActive].status,
        }).then((res) => {
          this.tableData = res;
          this.tableData.forEach((item, index) => {
            item["isLight"] = 0;
          });
        });
        // axios({
        //   method: "get",
        //   url:
        //     baseURL.url +
        //     "/adm-api/pub/provincial/CommandXzzfj/getWorkNoticeAllNew",
        //   // "/adm-api/pub/provincial/CommandXzzfj/getWorkNoticeAll",
        //   params: {
        //     queryStartTime:
        //       (this.datas2 && this.datas2[0] + " 00:00:00") || "",
        //     queryEndTime:
        //       (this.datas2 && this.datas2[1] + " 24:00:00") || "",
        //     area: localStorage.getItem("city") == "金华市" ? "" : localStorage.getItem("city"),
        //     status:this.tabs[this.tabActive].status
        //   },
        // }).then((res) => {
        //   console.log(res,"hhhhh");
        //   this.tableData = res.data.data;
        //   this.tableData.forEach((item, index) => {
        //     item["isLight"] = 0;
        //   });
        // });
      },
      findVideoByPoint(point_center, isSOSRow) {
        let this_ = this;
        axios({
          method: "get",
          url: baseURL.url + "/jhyjzh-server/screen_api/home/<USER>",
          params: {
            type: "type=zbjk",
            distance: 3, //1km=0.1  2km=0.2  3km=0.3
            point: point_center,
          },
        }).then(function (data) {
          let datas = data.data.data.zbjk.pointData;
          if (datas.length != 0) {
            this_.showVideoIframe = true;
            this_.sgVideoList = datas.map((a) => {
              return {
                name: a.data.name || "-",
                code: a.data.addinfo.chncode || "-",
                bq:
                  a.data.addinfo.labels + "|" + a.data.addinfo.hymc || "-",
                jl: a.data.jl || "-",
              };
            });
            this_.videoIframe();
            if (isSOSRow) {
              window.parent.mapUtil.removeLayer("syr");
              window.parent.mapUtil.removeLayer("syr1");
              window.parent.mapUtil.removeLayer("camera-load-icon");
              // 如果sos行点击
              let dataArr = [];
              dataArr = data.data.data.zbjk.pointData.filter((item) => {
                return item.is_online != "离线";
              });
              arr = dataArr.map((item) => {
                return {
                  ...item,
                  pointType: this_.getPointType(
                    item.is_online,
                    item.cameraType
                  ),
                };
              });
              this_.getManyPoint(arr);
            }
          } else {
            window.parent.mapUtil.removeLayer("camera-load-icon");
            this_.showVideoIframe = false;
            frames["zhddzxRightVideo"].vm.etVisible(false);
          }
        });
      },
      //周边视频---一次绘制多种不同类型的点
      getPointType(is_online, cameraType) {
        let arr = is_online + "-" + cameraType;
        let obj = {
          枪机在线: "在线-枪机",
          球机在线: "在线-球机",
          半球机在线: "在线-半球机",
          高点在线: "在线-未知",
        };
        for (key in obj) {
          if (obj[key] == arr) {
            return key;
          }
        }
      },
      getManyPoint(pointData) {
        if (pointData.length > 0) {
          window.parent.mapUtil.loadPointLayer({
            data: pointData,
            layerid: "camera-load-icon",
            cluster: true, //是否定义为聚合点位：true/false
            iconcfg: {
              image: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
              iconSize: 0.5,
              iconlist: {
                field: "pointType",
                list: [
                  {
                    value: "枪机在线",
                    size: "50",
                    src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
                  },
                  {
                    value: "球机在线",
                    size: "50",
                    src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiuji.png`,
                  },
                  {
                    value: "半球机在线",
                    size: "50",
                    src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-banqiu.png`,
                  },
                  {
                    value: "高点在线",
                    size: "50",
                    src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-gaodian.png`,
                  },
                ],
              },
            },
            onclick: this.openPointMassage,
            // onblur: this.onblur
          });
        }
      },
      openPointMassage(abc, list) {
        window.parent.mapUtil.removeLayer("syr");
        window.parent.mapUtil.removeLayer("syr1");
        window.parent.mapUtil.flyTo({
          destination: [abc.esX, abc.esY],
          offset: [0, -666],
        });
        let item = {
          obj: {
            chn_name: abc.data.addinfo.labels,
            chn_url: abc.data.addinfo.chncode,
            pointList: list,
          },
          video_code: abc.data.addinfo.chncode,
          csrk: true,
        };
        window.parent.lay.openIframe({
          type: "openIframe",
          name: "video_main_code",
          src:
            baseURL.url +
            "/static/citybrain/commonts/tcgl/video_main_code.html",
          width: "100%",
          height: "100%",
          left: "0",
          top: "0",
          zIndex: "1000",
          argument: item,
        });
      },
      // findVideoByid(sgid) {
      //   let this_ = this;
      //   axios({
      //     method: "get",
      //     url: baseURL.url + "/jhyjzh-server/screen_api/zhddzx/evetVideo",
      //     params: { sgid: this_.sgId, yjid: null, jl: 3, lx: 2 },
      //   }).then((res) => {
      //     // 接口调用不通 临时数据
      //     var datas = [
      //       {
      //         bq: "建筑住宅/楼宇/金义新区/金东鸿泰豪苑楼顶",
      //         code: "33071007041321087394",
      //         jl: 0.89,
      //         name: "曹宅鸿泰豪苑楼顶高倍球_DH201912LT雪亮X94",
      //       },
      //       {
      //         code: "33070354001321081061",
      //         name: "金东鸿泰豪苑楼顶_DH201912LT雪亮X61",
      //         jl: 1.45,
      //         bq: "建筑住宅/楼宇/金义新区/金东鸿泰豪苑楼顶",
      //       },
      //       {
      //         code: "33070399001321047685",
      //         name: "金汇路葱草街西口南侧行闯_DH201912HS雪亮X85",
      //         jl: 1.49,
      //         name: "金汇路葱草街西口南侧行闯_DH201912HS雪亮X85",
      //       },
      //       {
      //         code: "33070354001321084078",
      //         name: "金东镇政府西侧路口_DH201912DX雪亮G78",
      //         jl: 1.52,
      //         name: "金东镇政府西侧路口_DH201912DX雪亮G78",
      //       },
      //       {
      //         code: "33070354001321085924",
      //         name: "金东杜宅工业园区黄金畈村_DH201912DX雪亮G24",
      //         jl: 1.57,
      //         bq: "-",
      //         name: "金东杜宅工业园区黄金畈村_DH201912DX雪亮G24",
      //       },
      //       {
      //         code: "33070354001321086645",
      //         name: "金东3省道金江路438号_DH201912LT雪亮X45",
      //         jl: 3,
      //         name: "金东3省道金江路438号_DH201912LT雪亮X45",
      //       },
      //       {
      //         code: "33070354001320080067",
      //         name: "金东大黄村工业区大维电子路口_2DX67",
      //         jl: 2.83,
      //         name: "金东大黄村工业区大维电子路口_2DX67",
      //       },
      //       {
      //         code: "33070354001321086538",
      //         name: "金东枧头村北村口_DH201912DX雪亮G38",
      //         jl: 2.7,
      //         bq: "-",
      //         name: "金东枧头村北村口_DH201912DX雪亮G38",
      //       },
      //       {
      //         code: "33070354001321088717",
      //         name: "金东六大山村南岔路口_DH201912DX雪亮G17",
      //         jl: 2.68,
      //         name: "金东六大山村南岔路口_DH201912DX雪亮G17",
      //       },
      //     ];
      //     this_.sgVideoList = res.data.data;
      //     this_.videoIframe();
      //   });
      // },

      // 视频显示iframe
      addPoint(pointData, layerid, icon, callBack) {
        let this_ = this;
        window.parent.mapUtil.loadPointLayer({
          data: pointData, //点位数据
          layerid: layerid, //图层id
          iconcfg: {
            image: `/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${icon}.png`,
            iconSize: 0.6,
          },
          popcfg: {
            offset: [50, -100],
            show: false, //关闭按钮
          },
          cluster: false,
        });
      },
      videoIframe() {
        this.sgVideoList =
          this.sgVideoList.length >= 4
            ? this.sgVideoList.slice(0, 4)
            : this.sgVideoList;

        $("#videoCon").attr(
          "src",
          `/static/citybrain/commonts/zhdd/video-16-9.html?name=
                  ${JSON.stringify(this.sgVideoList)}`
        );
      },
      flyToFun(point, zoom) {
        window.parent.mapUtil.flyTo({
          destination: point,
          zoom: zoom,
        });
      },
    },
  });
  window.parent.eventbus &&
  window.parent.eventbus.on("backHome", () => {
    try {
      zhddRightVm.zfryPopClose();
      frames["zhddzxRightVideo"].vm.etVisible(false);
    } catch (error) {}
  });
  window.parent.eventbus &&
  window.parent.eventbus.on("rightIframeShow", () => {
    try {
      frames["zhddzxRightVideo"].vm.etVisible(true);
    } catch (error) {}
  });
  window.parent.eventbus &&
  window.parent.eventbus.on("rightIframeHide", () => {
    try {
      frames["zhddzxRightVideo"].vm.etVisible(false);
    } catch (error) {}
  });
</script>
</body>
</html>
