<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <title>切换地图场景</title>
        <link rel="stylesheet" href="/static/css/animate.css" />
        <script src="/Vue/vue.js"></script>
        <style>
            * {
                margin: 0;
                padding: 0;
            }

            .mapClick {
                width: 244px;
                height: 160px;
                border-radius: 10px;
                background-image: linear-gradient(to bottom, rgb(111 133 228), rgb(14, 64, 109), rgb(113 125 203));
                color: #ccc;
                font-size: 22px;
                cursor: pointer;
                padding-top: 8px;
                box-sizing: border-box;
            }

            .mapClick .mapClick_img1,
            .mapClick .mapClick_img3,
            .mapClick .mapClick_img4,
            .mapClick .mapClick_img2,
            .mapClick .mapClick_img5 {
                position: relative;
                width: 110px;
                height: 66px;
            }

            .mapClick .mapClick_img1:hover span,
            .mapClick .mapClick_img2:hover span,
            .mapClick .mapClick_img3:hover span,
            .mapClick .mapClick_img4:hover span,
            .mapClick .mapClick_img5:hover span {
                color: #fff;
                background-color: #3c76de;
            }

            .mapClick .mapClick_img1 span,
            .mapClick .mapClick_img3 span,
            .mapClick .mapClick_img4 span,
            .mapClick .mapClick_img2 span,
            .mapClick .mapClick_img5 span {
                position: absolute;
                right: 0;
                top: 36px;
                background-color: #0006;
                /* padding: 0 0; */
                width: 100%;
                height: 30px;
                line-height: 30px;
                text-align: center;
            }

            /*  .mapClick .mapClick_img1 span .active,
     .mapClick .mapClick_img2 span .active {
      background-color: #3c76de;
    } */
            .mapClick .mapClick_img1 {
                background: url("/static/images/tcgl/commont/qianse.png") no-repeat;
            }

            .mapClick .mapClick_img2 {
                background: url("/static/images/tcgl/commont/shense.png") no-repeat;
            }

            .mapClick .mapClick_img3 {
                background: url("/static/images/tcgl/commont/bm.png") no-repeat;
                background-size: 100% 100%;
            }

            .mapClick .mapClick_img4 {
                background: url("/static/images/tcgl/commont/jm.png") no-repeat;
                background-size: 100% 100%;
            }

            .mapClick .mapClick_img4 {
                background: url("/static/images/tcgl/commont/jm.png") no-repeat;
                background-size: 100% 100%;
            }

            .mapClickNew {
                border-radius: 10px;
                background: #162b48;
                color: #ccc;
                font-size: 22px;
                cursor: pointer;
                padding: 5px;
                box-sizing: border-box;
            }

            .mapClickNew .mapClick_img1,
            .mapClickNew .mapClick_img3,
            .mapClickNew .mapClick_img4,
            .mapClickNew .mapClick_img2,
            .mapClickNew .mapClick_img5 {
                position: relative;
                width: 130px;
                height: 130px;
                border-radius: 10px;
                margin: 5px;
            }

            .mapClickNew .mapClick_img1:hover span,
            .mapClickNew .mapClick_img2:hover span,
            .mapClickNew .mapClick_img3:hover span,
            .mapClickNew .mapClick_img4:hover span,
            .mapClickNew .mapClick_img5:hover span {
                color: #fff;
                background-color: #3c76de;
            }

            .mapClickNew .mapClick_img1 span,
            .mapClickNew .mapClick_img3 span,
            .mapClickNew .mapClick_img4 span,
            .mapClickNew .mapClick_img2 span,
            .mapClickNew .mapClick_img5 span {
                position: absolute;
                right: 0;
                top: 100px;
                background-color: #0006;
                /* padding: 0 0; */
                width: 100%;
                height: 30px;
                line-height: 30px;
                text-align: center;
            }

            /*  .mapClick .mapClick_img1 span .active,
     .mapClick .mapClick_img2 span .active {
      background-color: #3c76de;
    } */
            .mapClickNew .mapClick_img1 {
                background: url("/static/images/tcgl/commont/qianse.png") no-repeat;
                background-size: 130px 130px;
            }

            .mapClickNew .mapClick_img2 {
                background: url("/static/images/tcgl/commont/shense.png") no-repeat;
                background-size: 130px 130px;
            }

            .mapClickNew .mapClick_img3 {
                background: url("/static/images/tcgl/commont/bm.png") no-repeat;
                background-size: 130px 130px;
            }

            .mapClickNew .mapClick_img4 {
                background: url("/static/images/tcgl/commont/qx.jpg") no-repeat;
                background-size: 130px 130px;
            }

            .mapClickNew .mapClick_img5 {
                background: url("/static/images/tcgl/commont/jm.png") no-repeat;
                background-size: 130px 130px;
            }
        </style>
    </head>

    <body>
        <div id="app">
            <div class="mapClickNew">
                <div style="display: flex; justify-content: space-evenly; align-items: center">
                    <div class="mapClick_img1" @click="changeMap1()">
                        <span :style="mapin_change==7 ? '' : 'background-color: #3c76de'">卫星影像</span>
                    </div>
                    <div class="mapClick_img2" @click="changeMap7()">
                        <span :style="mapin_change==1 ? '' : 'background-color: #3c76de'">矢量地图</span>
                    </div>
                    <div class="mapClick_img3" @click="changeMap('bm')">
                        <span :style="mapin_changes=='bm' ? 'background-color: #3c76de' : ''">白模</span>
                    </div>
                    <div class="mapClick_img4" @click="changeMap('qx')">
                        <span :style="mapin_changes=='qx' ? 'background-color: #3c76de' : ''">倾斜摄影</span>
                    </div>
                    <div class="mapClick_img5" @click="changeMap('jm')">
                        <span :style="mapin_changes=='jm' ? 'background-color: #3c76de' : ''">精模</span>
                    </div>
                </div>
            </div>
        </div>

        <script>
            var changeMap = new Vue({
                el: "#app",
                data: {
                    mapin_change: 7,
                    mapin_changes: "",
                    mapobj: {
                        bm: ["qxLayer", "jmLayer"],
                        jm: ["qxLayer", "bmLayer"],
                        qx: ["bmLayer", "jmLayer"],
                    },
                },
                mounted() {
                    //   this.mapin_change = top.main_map_iconVm.mapin_change;
                    this.mapin_change = localStorage.getItem("mapType3840") ? localStorage.getItem("mapType3840") : 7;
                    this.mapin_changes = localStorage.getItem("mapTypes3840")
                        ? localStorage.getItem("mapTypes3840")
                        : "";
                },
                methods: {
                    changeMap1() {
                        //切换卫星影像
                        this.mapin_change = 1;
                        localStorage.setItem("mapType3840", 1);
                        window.parent.mapUtil.tool.changeBaseMap("img");
                        this.closeIframe();
                    },
                    changeMap7() {
                        //切换矢量地图
                        this.mapin_change = 7;
                        localStorage.setItem("mapType3840", 7);
                        window.parent.mapUtil.tool.changeBaseMap("black");
                        this.closeIframe();
                    },
                    changeMap(type) {
                        var id = type + "Layer";
                        if (this.mapin_changes == type) {
                            this.mapin_changes = "";
                            localStorage.setItem("mapTypes3840", "");
                            window.parent.mapUtil.removeLayer(id);
                        } else {
                            // let removeId = type == 'bm' ? 'qx' : type == 'qx' ? 'bm' : ''
                            // window.parent.mapUtil.removeLayer(removeId + 'Layer')
                            window.parent.mapUtil.removeAllLayers(this.mapobj.type);
                            window.parent.mapUtil.loadModelLayer({
                                layerid: id,
                                type: type,
                            });
                            this.mapin_changes = type;
                            localStorage.setItem("mapTypes3840", this.mapin_changes);
                        }
                        this.closeIframe();
                    },
                    closeIframe() {
                        window.parent.frames["indexMapIcon3840"].mainIconVm.trunMapkuai = false;
                        window.parent.lay.closeIframeByNames(["main_changeMap3840"]);
                    },
                },
            });
        </script>
    </body>
</html>
