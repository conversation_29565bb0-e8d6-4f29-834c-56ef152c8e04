<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>执法态势-左</title>
    <link rel="stylesheet" href="/static/css/animate.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <link rel="stylesheet" href="/static/css/viewCss/zfts_left.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/echarts/echarts.min.js"></script>
    <script src="/echarts/echarts-gl.min.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/jslib/biz.min.js"></script>
    <style>
        /* 行政检查 */
        .zhcyc {
            width: 100%;
            height: 460px;
            padding: 20px 60px;
            box-sizing: border-box;
            position: relative;
            background: url("/static/images/zfts/zhcyc-bg.png") no-repeat;
            background-size: 100% 100%;
        }
        .quan {
            cursor: pointer;
        }

        .quan0 {
            position: absolute;
            left: 339px;
            top: -10px;
            width: 308px;
            height: 260px;
            background: url("/static/images/zfts/zrw.png") no-repeat;
            background-size: 100% 100%;
            font-size: 36px;
            color: #fff;
            text-align: center;
            line-height: 60px;
            padding-top: 50px;
            box-sizing: border-box;
        }

        .quan1 {white-space: nowrap;
            position: absolute;
            left: 100px;
            top: 30px;
            width: 155px;
            height: 155px;
            background: url("/static/images/zfts/rwzb.png") no-repeat;
            background-size: 100% 100%;
            font-size: 28px;
            color: #fff;
            text-align: center;
            line-height: 40px;
            padding-top: 40px;
            box-sizing: border-box;
        }

        .quan2 {white-space: nowrap;
            position: absolute;
            right: 170px;
            top: 30px;
            width: 155px;
            height: 155px;
            background: url("/static/images/zfts/cybm.png") no-repeat;
            background-size: 100% 100%;
            font-size: 28px;
            color: #fff;
            text-align: center;
            line-height: 40px;
            padding-top: 40px;
            box-sizing: border-box;
        }

        .quan3 {
            position: absolute;
            left: 150px;
            top: 200px;
            width: 186px;
            height: 186px;
            background: url("/static/images/zfts/jchc.png") no-repeat;
            background-size: 100% 100%;
            font-size: 28px;
            color: #fff;
            text-align: center;
            line-height: 40px;
            padding-top: 50px;
            box-sizing: border-box;
        }

        .quan4 {
            position: absolute;
            right: 210px;
            top: 195px;
            width: 179px;
            height: 179px;
            background: url("/static/images/zfts/jsqygr.png") no-repeat;
            background-size: 100% 100%;
            font-size: 28px;
            color: #fff;
            text-align: center;
            line-height: 40px;padding: 50px 0px;
            box-sizing: border-box;
        }

        .quan5 {
            position: absolute;
            left: 400px;
            top: 254px;
            width: 197px;
            height: 197px;
            background: url("/static/images/zfts/rwaswc.png") no-repeat;
            background-size: 100% 100%;
            font-size: 28px;
            color: #fff;
            text-align: center;
            line-height: 40px;
            padding: 40px;
            box-sizing: border-box;
        }

        .txt0 {
            color: #d958de;
            font-size: 30px;
            font-weight: bold;
        }

        .txt1 {
            color: #08a0f5;
            font-size: 30px;
            font-weight: bold;
        }

        .txt2 {
            color: #eed252;
            font-size: 40px;
            font-weight: bold;
        }

        .txt3 {
            color: #00fffc;
            font-size: 30px;
            font-weight: bold;
        }

        .txt4 {
            color: #45f95e;
            font-size: 30px;
            font-weight: bold;
        }

        .txt5 {
            color: #ffb436;
            font-size: 30px;
            font-weight: bold;
        }

        .quan0 {
            animation: move infinite 5s;
        }
        .quan1 {
            animation: move infinite 5s 0.5s;
        }
        .quan2 {
            animation: move infinite 5s 1.2s;
        }
        .quan3 {
            animation: move infinite 5s 1s;
        }
        .quan4 {
            animation: move infinite 5s 0.5s;
        }
        @keyframes move {
            0% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(20px);
            }
            100% {
                transform: translateY(0px);
            }
        }

        .ptzy_box {
            width: 100%;
            height: auto;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            padding: 20px 70px;
            margin: 40px 0 40px 0;
        }
        .ptzy_item {
            display: flex;
            width: 30%;
            margin-bottom: 10px;
        }
        .box {
            padding: 20px 40px;
            box-sizing: border-box;
            position: relative;
        }
        .item_name {
            width: 200px;
            height: auto;
            font-size: 28px;
            color: #d1d6df;
            margin-top: -10px;
        }
        .xt_font {
            font-family: DINCondensed;
            font-style: italic;
        }
        .s-yellow {
            color: #eed252;
        }
        .s-blue {
            color: #34dfe3;
        }
        .jcjh {
            width: 100%;
            height: 200px;
            display: flex;
            justify-content: space-evenly;
            flex-wrap: wrap;
            box-sizing: border-box;
            padding: 50px 60px;
        }
        .text {
            width: 252px;
            height: auto;
            font-size: 36px;
            font-family: Source Han Sans CN;
            font-weight: bold;
            font-style: italic;
            color: #d1d6df;
            line-height: 50px;
            background: linear-gradient(0deg, #acddff 0%, #ffffff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .value {
            width: 260px;
            height: 44px;
            font-size: 60px;
            font-family: DINCondensed;
            font-weight: bold;
            font-style: italic;
            color: #eed252;
        }

        .unit {
            font-size: 35px;
            margin-left: 10px;
        }
        .jhzs {
            width: 431px;
            height: auto !important;
            min-height: 169px !important;
            background-size: 100% 100%;
            padding-left: 150px;
            padding-top: 15px;
            padding-bottom: 45px;
            box-sizing: border-box;
        }
        .zdbm {
            width: 431px;
            height: 150px;
            background-image: url('/static/images/zfts/zdbm.png');
            background-size: 100% 100%;
        }
    </style>
  </head>

  <body>
    <div id="left" v-cloak>
      <div class="hearder_h1"><span>行政检查</span></div>
      <div class="zhcyc">
        <div
          class="quan"
          :class="`quan${index==0?2:index==2?0:index}`"
          v-for="(item,index) in xzjcData"
          :key="index"
        >
          <li>{{item.name}}</li>
          <li :class="`txt${index}`">{{item.value}}{{item.unit}}</li>
        </div>
      </div>
      <div class="hearder_h1"><span>检查计划统筹</span></div>
      <div class="ptzy_box box">
        <div class="ptzy_item" v-for="(item,index) in jcjhtcList">
          <img :src="item.icon" alt="" width="89" height="100" />
          <div style="margin-top: 20px;margin-left: 10px;">
            <div class="item_name">{{item.name}}</div>
            <div class="item_bottom">
              <span
                class="s-font-45 xt_font"
                :class="index%3==1?'s-blue':'s-yellow'"
              >{{item.value}}</span>
              <span
                style='margin-left: 10px;'
                class="s-font-25"
                :class="index%3==1?'s-blue':'s-yellow'"
              >{{item.unit}}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="hearder_h1"><span>检查任务统筹</span></div>
      <div class="ptzy_box box">
        <div class="ptzy_item" v-for="(item,index) in jcrwtcList">
          <img :src="item.icon" alt="" width="89" height="100" />
          <div style="margin-top: 20px;margin-left: 10px;">
            <div class="item_name">{{item.name}}</div>
            <div class="item_bottom">
              <span
                class="s-font-45 xt_font"
                :class="index%3==1?'s-blue':'s-yellow'"
              >{{item.value}}</span>
              <span
                style='margin-left: 10px;'
                class="s-font-25"
                :class="index%3==1?'s-blue':'s-yellow'"
              >{{item.unit}}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="hearder_h1"><span>涉企行政检查</span></div>
      <div class="jcjh">
        <div class="jhzs" v-for='(item,i) in sqxzjcData' :key='i' :style="{backgroundImage: 'url(' + item.icon + ')',marginTop: '30px'}">
          <li class="text">{{item.label}}</li>
          <li class="value">
            <count-to
              :start-val="0"
              :end-val="Number(item.number)"
              :duration="3000"
              class="count-toNum s-c-yellow-gradient"
            >
            </count-to>
            <span class="unit">{{item.unit}}</span>
          </li>
        </div>
      </div>
    </div>
    <script>
      window.parent.eventbus &&
      window.parent.eventbus.on("yearChange", (year) => {
        vm.initApi(localStorage.getItem("city"),year);
      });
      let vm = new Vue({
        el: "#left",
        data: {
          citylist:[
            { name: "婺城区", color: [40, 194, 254, 1], height: 500 },
            { name: "金东区", color: [40, 194, 254, 1], height: 500 },
            { name: "开发区", color: [40, 194, 254, 1], height: 500 },
            { name: "兰溪市", color: [40, 194, 254, 1], height: 500 },
            { name: "浦江县", color: [40, 194, 254, 1], height: 500 },
            { name: "义乌市", color: [40, 194, 254, 1], height: 500 },
            { name: "东阳市", color: [40, 194, 254, 1], height: 500 },
            { name: "磐安县", color: [40, 194, 254, 1], height: 500 },
            { name: "永康市", color: [40, 194, 254, 1], height: 500 },
            { name: "武义县", color: [40, 194, 254, 1], height: 500 },
          ],
          citylist1:[
            { name: "婺城区", color: [39, 179, 234, 0], height: 5000 },
            { name: "金东区", color: [121, 217, 255, 0], height: 5000 },
            { name: "开发区", color: [40, 194, 254, 0], height: 5000 },
            { name: "兰溪市", color: [0, 140, 195, 0], height: 5000 },
            { name: "浦江县", color: [45, 96, 184, 0], height: 5000 },
            { name: "义乌市", color: [60, 145, 177, 0], height: 5000 },
            { name: "东阳市", color: [50, 104, 195, 0], height: 5000 },
            { name: "磐安县", color: [7, 88, 231, 0], height: 5000 },
            { name: "永康市", color: [1, 56, 150, 0], height: 5000 },
            { name: "武义县", color: [16, 92, 216, 0], height: 5000 },
          ],
          maskUrl: {
            婺城区: "/static/data/mask/wcq.json",
            金东区: "/static/data/mask/jdq.json",
            东阳市: "/static/data/mask/dys.json",
            义乌市: "/static/data/mask/yws.json",
            永康市: "/static/data/mask/yks.json",
            兰溪市: "/static/data/mask/lxs.json",
            浦江县: "/static/data/mask/pjx.json",
            武义县: "/static/data/mask/wyx.json",
            磐安县: "/static/data/mask/pax.json",
            金华开发区: "/static/data/mask/kfq.json",
          },
          year: localStorage.getItem("year"),
          city: localStorage.getItem("city"),
          //行政检查
          xzjcData: [],
          name:"",
          dialogShow: false,

          jcjhtcList: [
            {
              icon: "/static/images/zfts/年度计划数icon.png",
              name: "年度计划数",
              value: 99,
              unit: "个",
            },
            {
              icon: "/static/images/zfts/跨部门计划数icon.png",
              name: "跨部门计划数",
              value: 75,
              unit: "个",
            },
            {
              icon: "/static/images/zfts/计划完成数icon.png",
              name: "计划完成数",
              value: 25,
              unit: "个",
            }
          ],
          jcrwtcList: [
            {
              icon: "/static/images/zfts/总任务数icon.png",
              name: "总任务数",
              value: 121,
              unit: "个",
            },
            {
              icon: "/static/images/zfts/综合查一次icon.png",
              name: "“综合查一次”任务数",
              value: 90,
              unit: "个",
            },
            {
              icon: "/static/images/zfts/任务完成数icon.png",
              name: "任务完成数",
              value: 62,
              unit: "个",
            }
          ],
          sqxzjcData: [
            {
              label:"涉企检查户次",
              number:"1359",
              unit: "次",
              icon: "/static/images/zfts/市区检查户次.png"
            },
            {
              label:"减少企业干扰户",
              number:"1076",
              unit: "次",
              icon: "/static/images/zfts/减少企业干扰户.png"
            },
            {
              label:"涉企“综合查一次”户次",
              number:"1142",
              unit: "次",
              icon: "/static/images/zfts/涉企“综合查一次”户次.png"
            },
            {
              label:"涉企“综合查一次”实施率",
              number:"84.03",
              unit: "%",
              icon: "/static/images/zfts/涉企“综合查一次”实施率.png"
            }
          ]
        },
        mounted() {
          let initcity=localStorage.getItem("city");
          this.initApi(initcity,localStorage.getItem("year"));
          //下面是从驾驶舱点击执法态势
          window.onload = () => {
            let merge = window.frames['map']?window.frames['map'].view.map.findLayerById("VECTOR_POI_Merge"):window.parent.frames['map'].view.map.findLayerById("VECTOR_POI_Merge")
            if (merge) merge.visible = false
            let dem = window.frames['map']?window.frames['map'].view.map.findLayerById("JINHUA_DEM"):window.parent.frames['map'].view.map.findLayerById("JINHUA_DEM")
            if (dem) dem.visible = false
            let label = window.frames['map']?window.frames['map'].view.map.findLayerById("VECTOR_Road_Label"):window.parent.frames['map'].view.map.findLayerById("VECTOR_Road_Label")
            if (label) label.visible = false
            let vector = window.frames['map']?window.frames['map'].view.map.findLayerById("VECTOR"):window.parent.frames['map'].view.map.findLayerById("VECTOR")
            if (vector) vector.visible = false
            window.frames['map']?window.frames['map'].view.map.remove(window.frames['map'].xingzhenlayer):window.parent.frames['map'].view.map.remove(window.parent.frames['map'].xingzhenlayer)
            window.frames['map']?window.frames['map'].mapUtil.removeLayer("mask"):window.parent.frames['map'].mapUtil.removeLayer("mask")
            if (localStorage.getItem("city")!="金华市"){
              window.frames['map']?window.frames['map'].xingzhen(localStorage.getItem("city")):window.parent.frames['map'].xingzhen(localStorage.getItem("city"))
            }
            if (localStorage.getItem("adminCity")=="金华市"){
                this.banKuai(localStorage.getItem("city"));
            }else{
              this.banKuai(localStorage.getItem("adminCity"));
            }
            this.openBottom();
          }
          //监测县市区的切换
          window.parent.eventbus &&
            window.parent.eventbus.on("cityChange", (city) => {
              let filtName =
                city == "金义新区"
                  ? "金东区"
                  : city == "金华开发区"
                  ? "开发区"
                  : city;
              this.initApi(filtName,localStorage.getItem("year"));
              // 显示各个市区的板块
              window.frames['map']?window.frames['map'].mapUtil.removeLayer("bankuai"):window.parent.frames['map'].mapUtil.removeLayer("bankuai")
              window.frames['map']?window.frames['map'].mapUtil.removeLayer("map_text"):window.parent.frames['map'].mapUtil.removeLayer("map_text")
              this.banKuai(localStorage.getItem("city"));
            });

          window.parent.eventbus &&
          window.parent.eventbus.on("yearChange", (year) => {
            this.initApi(localStorage.getItem("city"),year);
          });
        },
        methods: {
          /**
           * 在从驾驶舱点进执法态势页面时 增加页面的执法态势板块
           * @param url 当前县市区名称
           */
          banKuai(url){
            axios({
              method: "get",
              url: "/static/data/zfts/"+url+".json",
            }).then((res)=> {
              window.frames['map']?window.frames['map'].mapUtil.mask({
                layerid: "bankuai",
                data: res.data, // res 是要素集合
                style: {
                  strokeWidth: localStorage.getItem("city")=="金华市"?2:1,
                  strokeColor: '#60e4ff', //多边形轮廓颜色透明度
                  fillColor: '#10b3ff', //多边形填充色
                  height: 50,
                },
                onclick: null,
              }):window.parent.frames['map'].mapUtil.mask({
                layerid: "bankuai",
                data: res.data, // res 是要素集合
                style: {
                  strokeWidth: localStorage.getItem("city")=="金华市"?2:1,
                  strokeColor: '#60e4ff', //多边形轮廓颜色透明度
                  fillColor: '#10b3ff', //多边形填充色
                  height: 50,
                },
                onclick: null,
              });
            })
            let textData = [
              {pos:[119.602579, 29.070607,500],text: "开发区"},
              {pos:[119.514748, 28.964012, 500],text: "婺城区"},
              {pos:[119.799596, 29.149391, 500],text: "金东区"},
              {pos:[119.714529, 28.768287, 500],text: "武义县"},
              {pos:[119.903937, 29.520086, 500],text: "浦江县"},
              {pos:[120.609672, 29.007893, 500],text: "磐安县"},
              {pos:[119.526736, 29.278165, 500],text: "兰溪市"},
              {pos:[120.061011, 29.300614, 500],text: "义乌市"},
              {pos:[120.364678, 29.232405, 500],text: "东阳市"},
              {pos:[120.102417, 28.934317, 500],text: "永康市"},
            ]
            let textData1=[];
            let color=[];
            switch (url){
              case "婺城区": textData1 = [{pos: [119.514748, 28.964012],text: "婺城区"}];
                break
              case "金东区":  textData1 = [{pos: [119.799596, 29.149391],text: "金东区"}];
                break
              case "金华开发区":  textData1 = [{pos: [119.602579, 29.070607],text: "开发区"}];
                break
              case "兰溪市":  textData1 = [{pos: [119.526736, 29.278165],text: "兰溪市"}];
                break
              case "浦江县":  textData1 = [{pos: [119.903937, 29.520086],text: "浦江县"}];
                break
              case "义乌市":  textData1 = [{pos: [120.061011, 29.300614],text: "义乌市"}];
                break
              case "东阳市":  textData1 = [{pos: [120.375678, 29.232405],text: "东阳市"}];
                break
              case "磐安县":  textData1 = [{pos: [120.559672, 29.037893],text: "磐安县"}];
                break
              case "永康市":  textData1 = [{pos: [120.102417, 28.934317],text: "永康市"}];
                break
              case "武义县":  textData1 = [{pos: [119.714529, 28.768287],text: "武义县"}];
                break
            }
            window.frames['map']?window.frames['map'].mapUtil.loadTextLayer({
              layerid: "map_text",
              data: localStorage.getItem("city")=="金华市"?textData:textData1,
              style: {
                size: localStorage.getItem("city")=="金华市"?45:60,
                color: '#2b2d6a',
              },
            }):window.parent.frames['map'].mapUtil.loadTextLayer({
              layerid: "map_text",
              data: localStorage.getItem("city")=="金华市"?textData:textData1,
              style: {
                size: localStorage.getItem("city")=="金华市"?45:60,
                color: '#2b2d6a',
              },
            });
          },
          initApi(city,year) {
            //行政检查
            // indexApi("/csdn_yjyp1", { area_code: city,sjwd2: year }).then((res) => {
            //   this.xzjcData = res.data.map((a,i) => ({
            //     name: a.label.split("-")[1],
            //     value: a.num,
            //     unit: a.unit,
            //   }))
            // });


            $api("ygf_zfts_tmp", { qx: city }).then((res) => {
              console.log(res[0],"ygf_zfts_tmp");
              const result = res[0];
              this.xzjcData = [
                {
                  name: "“综合查一次”实施率",
                  value: result.zhcycssl,
                  unit: "%",
                },
                {
                  name: "现场检查户次",
                  value: result.xcjchc,
                  unit: "次",
                },
                {
                  name: "监管事项数",
                  value: result.jgsxs,
                  unit: "个",
                },
                {
                  name: "应用信用规则率",
                  value: result.yyxygzl,
                  unit: "%",
                },
                {
                  name: "亮码检查率",
                  value: result.lmjcl,
                  unit: "%",
                }
              ];
              this.jcjhtcList = [
                {
                  icon: "/static/images/zfts/年度计划数icon.png",
                  name: "年度计划数",
                  value: result.ndjhs,
                  unit: "个",
                },
                {
                  icon: "/static/images/zfts/跨部门计划数icon.png",
                  name: "跨部门计划数",
                  value: result.kbmjhs,
                  unit: "个",
                },
                {
                  icon: "/static/images/zfts/计划完成数icon.png",
                  name: "计划完成数",
                  value: result.jhwcs,
                  unit: "个",
                }
              ];
              this.jcrwtcList = [
                {
                  icon: "/static/images/zfts/总任务数icon.png",
                  name: "总任务数",
                  value: result.zdrws,
                  unit: "个",
                },
                {
                  icon: "/static/images/zfts/综合查一次icon.png",
                  name: "“综合查一次”任务数",
                  value: result.zhcycrws,
                  unit: "个",
                },
                {
                  icon: "/static/images/zfts/任务完成数icon.png",
                  name: "任务完成数",
                  value: result.cyrws,
                  unit: "个",
                }
              ];
              this.sqxzjcData = [
                {
                  label:"涉企检查户次",
                  number:result.sqjchc,
                  unit: "次",
                  icon: "/static/images/zfts/市区检查户次.png"
                },
                {
                  label:"减少企业干扰户",
                  number:result.jsqygrhc,
                  unit: "次",
                  icon: "/static/images/zfts/减少企业干扰户.png"
                },
                {
                  label:"涉企“综合查一次”户次",
                  number:result.sqzhcychc,
                  unit: "次",
                  icon: "/static/images/zfts/涉企“综合查一次”户次.png"
                },
                {
                  label:"涉企“综合查一次”实施率",
                  number:result.sqzhcycssl,
                  unit: "%",
                  icon: "/static/images/zfts/涉企“综合查一次”实施率.png"
                }
              ];
            });
          },
          // 加载底部
          openBottom() {
            window.parent.lay.openIframe({
              type: "openIframe",
              name: "zfts_bottom",
              id: "zfts_bottom",
              src: baseURL.url + "/static/citybrain/zfts/zfts_bottom.html",
              width: "1760px",
              height: "525px",
              left: "calc(50% - 860px)",
              top: '73.3%',
              zIndex: "666",
            });
          },
        },
      });
    </script>
  </body>
</html>
