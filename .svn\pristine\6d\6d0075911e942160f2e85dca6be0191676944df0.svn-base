.jcjh {
  width: 100%;
  height: 200px;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 20px 60px;
}

li {
  list-style: none;
}

.jhzs {
  width: 431px;
  height: 150px;
  background-image: url('/static/images/zfts/ndjhzs.png');
  background-size: 100% 100%;
  padding-left: 150px;
  padding-top: 15px;
  box-sizing: border-box;
}

.zdbm {
  width: 431px;
  height: 150px;
  background-image: url('/static/images/zfts/zdbm.png');
  background-size: 100% 100%;
}

.text {
  width: 216px;
  height: 45px;
  font-size: 36px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  font-style: italic;
  color: #d1d6df;
  line-height: 50px;
  background: linear-gradient(0deg, #acddff 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.value {
  width: 260px;
  height: 44px;
  font-size: 60px;
  font-family: DINCondensed;
  font-weight: bold;
  font-style: italic;
  color: #eed252;
}

.unit {
  font-size: 35px;
  margin-left: 10px;
}

.jhjd-bg {
  width: 960px;
  height: 48px;
  background-image: url('/static/images/zfts/jhjd-bg.png');
  background-size: 100% 100%;
  margin-top: 15px;
}

#jhjd {
  width: 960px;
  height: 40px;
}

/* 计划完成进度 */
.jhwcjd {
  width: 100%;
  height: 170px;
  box-sizing: border-box;
  padding: 0px 60px;
}

.jd-part {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.first {
  font-size: 35px !important;
}

.jd0 {
  color: #18dffe;
}

.jd2 {
  color: #18fcb5;
}

/* 进度类型 */
.jhlx-con {
  width: 100%;
  height: 280px;
  position: relative;
}

#jhlx,#jhlx1{
  width: 100%;
  height: 300px;
  position: absolute;
      z-index: 10;
}

.jhlx-bg {
  position: absolute;z-index: 1;
  /* left: 70px; */
  top: 100px;
  width: 389px;
  height: 163px;
  background: url("/static/images/zfts/chart-bg.png") no-repeat;background-size: 67% 77%;
}

/* 综合查一次 */
.zhcyc {
  width: 100%;
  height: 460px;
  padding: 20px 60px;
  box-sizing: border-box;
  position: relative;
  background: url("/static/images/zfts/zhcyc-bg.png") no-repeat;
  background-size: 100% 100%;
}

.quan {
  cursor: pointer;
}

.quan0 {
  position: absolute;
  left: 339px;
  top: -10px;
  width: 308px;
  height: 260px;
  background: url("/static/images/zfts/zrw.png") no-repeat;
  background-size: 100% 100%;
  font-size: 36px;
  color: #fff;
  text-align: center;
  line-height: 60px;
  padding-top: 50px;
  box-sizing: border-box;
}

.quan1 {white-space: nowrap;
  position: absolute;
  left: 100px;
  top: 30px;
  width: 155px;
  height: 155px;
  background: url("/static/images/zfts/rwzb.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;
  padding-top: 40px;
  box-sizing: border-box;
}

.quan2 {white-space: nowrap;
  position: absolute;
  right: 170px;
  top: 30px;
  width: 155px;
  height: 155px;
  background: url("/static/images/zfts/cybm.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;
  padding-top: 40px;
  box-sizing: border-box;
}

.quan3 {
  position: absolute;
  left: 150px;
  top: 200px;
  width: 186px;
  height: 186px;
  background: url("/static/images/zfts/jchc.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;
  padding-top: 50px;
  box-sizing: border-box;
}

.quan4 {
  position: absolute;
  right: 210px;
  top: 195px;
  width: 179px;
  height: 179px;
  background: url("/static/images/zfts/jsqygr.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;padding: 50px 0px;
  box-sizing: border-box;
}

.quan5 {
  position: absolute;
  left: 400px;
  top: 254px;
  width: 197px;
  height: 197px;
  background: url("/static/images/zfts/rwaswc.png") no-repeat;
  background-size: 100% 100%;
  font-size: 28px;
  color: #fff;
  text-align: center;
  line-height: 40px;
  padding: 40px;
  box-sizing: border-box;
}

.txt0 {
  color: #eed252;
  font-size: 40px;
  font-weight: bold;
}

.txt1 {
  color: #08a0f5;
  font-size: 30px;
  font-weight: bold;
}

.txt2 {
  color: #d958de;
  font-size: 30px;
  font-weight: bold;
}

.txt3 {
  color: #00fffc;
  font-size: 30px;
  font-weight: bold;
}

.txt4 {
  color: #45f95e;
  font-size: 30px;
  font-weight: bold;
}

.txt5 {
  color: #ffb436;
  font-size: 30px;
  font-weight: bold;
}

/* 监管一件事 */
.tj {
  width: 100%;
  height: 400px;
  display: flex;
  padding: 10px;
  box-sizing: border-box;
}

.tj li {
  width: 50%;
  height: 100%;
  list-style: none;
  position: relative;
  padding-top: 110px;
}

.tj li img {
  width: 190px;
  height: 174px;
}

.jdt {
  width: 100%;
  height: 350px;
  box-sizing: border-box;
}

.tj li:first-child .sp-ys {
  font-size: 65px;
  font-style: italic;
  color: #00fffc;
  font-family: DINCondensed;
}

.tj li:nth-child(2) .sp-ys {
  font-size: 65px;
  font-style: italic;
  color: #08a0f5 !important;
  font-family: DINCondensed;
}

.tj li:nth-child(3) .sp-ys {
  font-size: 48px;
  color: #ffb436 !important;
}

.sp {
  font-size: 36px;
  color: #fff;
  position: absolute;
  /* top: -13px;
  left: 155px;
  font-weight: bolder; */
  top: 45px;
  width: 190px;
  text-align: center;
}

 /* 表格 */
 .table {
   width: 100%;
   /* height: 500px; */
   padding: 10px 15px;
   box-sizing: border-box;
 }

 .table .th {
   width: 100%;
   height: 60px;
   display: flex;
   align-items: center;
   justify-content: space-evenly;
   font-weight: 700;
   font-size: 28px;
   line-height: 60px;
   color: #ffffff;
 }

 .table .th_td {
   letter-spacing: 0px;
   text-align: center;
 }

 .table .tbody {
   width: 100%;
   height: calc(100% - 59px);
   /* overflow-y: auto; */
   overflow: hidden;
 }

 .table .tbody:hover {
   overflow-y: auto;
 }

 .table .tbody::-webkit-scrollbar {
   width: 4px;
   /*滚动条整体样式*/
   height: 4px;
   /*高宽分别对应横竖滚动条的尺寸*/
 }

 .table .tbody::-webkit-scrollbar-thumb {
   border-radius: 10px;
   background: #20aeff;
   height: 8px;
 }

 .table .tr {
   display: flex;
   justify-content: space-evenly;
   align-items: center;
   height: 70px;
   line-height: 70px;
   font-size: 24px;
   color: #ffffff;
   cursor: pointer;
   border-top: 1px solid #959aa1;
   border-image: linear-gradient(to right, #e9f5ff3b, #f5ffffd4, #e9f5ff3b) 1;
   box-sizing: border-box;
 }

 .table .tr:nth-child(2n) {
   background: rgba(50, 134, 248, 0.2);
 }

 .table .tr:nth-child(2n + 1) {
   background: rgba(50, 134, 248, 0.12);
 }

 .table .tr:hover {
   background-color: #0074da75;
 }

 .table .tr_td {
   letter-spacing: 0px;
   text-align: center;
   box-sizing: border-box;
   white-space: nowrap;
   overflow: hidden;
   text-overflow: ellipsis;
 }
