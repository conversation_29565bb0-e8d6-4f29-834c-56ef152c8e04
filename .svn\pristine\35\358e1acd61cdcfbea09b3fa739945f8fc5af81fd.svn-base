<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  <title>金华市行政执法指挥中心</title>
  <script src="/Vue/vue.js"></script>
  <link rel="stylesheet" href="/static/css/common.css" />
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <style>
    @media only screen and (max-width: 1920px) {
      .choosePage {
        transform: scale(0.5) translate(-50%, -50%);
      }
    }
  </style>
</head>

<body>
  <div class="choosePage" id="choosePage">
    <div class="name">
      {{city + "行政执法指挥中心"}}
    </div>
    <div class="tops">
      <div class="topItem"
        v-for="(item,i) in topArr" :key="i" @click="pageJump(item)"
        :style="{background: 'url('+item.back+')'}">
      </div>
    </div>
    <div class="pages">
      <div class="pageItem" v-for="(item,i) in pageArr" :key="i" :style="{background: 'url('+item.img+')'}"
        @click="pageJump(item)">
        <div class="pageName">{{item.name}}</div>
      </div>
    </div>
  </div>
</body>
<script>
  var choosePage = new Vue({
    el: "#choosePage",
    data: {
      topArr:[
        {
          img: "/static/images/login/驾驶舱.png",
          back: "/static/images/login/驾驶舱_back.png",
          name: "驾驶舱",
          key: "home",
          components: [
            {
              name: "home_left",
              url: "/static/citybrain/home/<USER>",
              width: "1030px",
              height: "1900px",
              left: "20px",
              right: "unset",
              top: "calc(50% - 870px)"
            },
            {
              name: "home_right",
              url: "/static/citybrain/home/<USER>",
              width: "1030px",
              height: "1900px",
              left: "unset",
              right: "20px",
              top: "calc(50% - 870px)"
            },
            {
              name: "home_top",
              url: "/static/citybrain/home/<USER>",
              width: "828px",
              height: "200px",
              left: "calc(50% - 414px)",
              top: "200px",
            },
            {
              name: "home_bottom",
              url: "/static/citybrain/home/<USER>",
              width: "1760px",
              height: "525px",
              left: "calc(50% - 860px)",
              top: '73.3%'
            },
          ]
        },
        {
          img: "/static/images/login/后台管理.png",
          back: "/static/images/login/后台管理_back.png",
          name: "后台管理"
        }
      ],
      componentsList:[
        {
          key:"zhdd",
          components:[
            {
              name: "zhdd_left",
              url: "/static/citybrain/zhdd/zhdd_left.html",
              width: "1030px",
              height: "1900px",
              left: "20px",
              right: "unset",
              top: "calc(50% - 870px)"
            },
            {
              name: "zhdd_right",
              url: "/static/citybrain/zhdd/zhdd_right.html",
              width: "1030px",
              height: "1900px",
              left: "unset",
              right: "20px",
              top: "calc(50% - 870px)"
            },
            {
              name: "zhdd_middle",
              url: "/static/citybrain/zhdd/zhdd_middle.html",
              width: "1760px",
              height: "150px",
              left: "calc(50% - 860px)",
              top: "65%",
              zIndex: "666",
            },
            {
              name: "zhdd_bottom",
              url: "/static/citybrain/zhdd/zhdd_bottom.html",
              width: "1760px",
              height: "525px",
              left: "calc(50% - 860px)",
              top: '73.3%'
            },
          ]
        },
        {
          key:"zfts",
          components:[
            {
              name: "zfts_left",
              url: "/static/citybrain/zfts/zfts_left.html",
              width: "1030px",
              height: "1900px",
              left: "20px",
              right: "unset",
              top: "220px"
            },
            {
              name: "zfts_right",
              url: "/static/citybrain/zfts/zfts_right.html",
              width: "1030px",
              height: "1900px",
              left: "unset",
              right: "20px",
              top: "220px"
            },
            {
              name: "zfts_bottom",
              url: "/static/citybrain/zfts/zfts_bottom.html",
              width: "1760px",
              height: "525px",
              left: "calc(50% - 860px)",
              top: '73.3%'
            },
          ]
        },
        {
          key:"yyjc",
          components:[
            {
              name: "yyjc_index",
              url: "/static/citybrain/yyjc/yyjc_index.html",
              width: "100%",
              height: "100%",
              left: "0",
              top: "0"
            }
          ]
        },
        {
          key:"xjzx",
          components:[
            {
              name: "xjzx_index",
              url: "/static/citybrain/yyjc/xjzx_index.html",
              width: "100%",
              height: "100%",
              left: "0",
              top: "0"
            }
          ]
        },
        {
          key:"ajhf",
          components:[
            {
              name: "ajhf_left",
              url: "/static/citybrain/ajhf/ajhf_left.html",
              width: "1030px",
              height: "1900px",
              left: "20px",
              right: "unset",
              top: "calc(50% - 870px)"
            },
            {
              name: "ajhf_right",
              url: "/static/citybrain/ajhf/ajhf_right.html",
              width: "1030px",
              height: "1900px",
              left: "unset",
              right: "20px",
              top: "calc(50% - 870px)"
            },
            {
              name: "ajhf_bottom",
              url: "/static/citybrain/ajhf/ajhf_bottom.html",
              width: "1760px",
              height: "525px",
              left: "calc(50% - 860px)",
              top: '73.3%'
            },
          ]
        }
      ],
      pageArr: [
        {
          img: "/static/images/login/指挥调度.png",
          name: "指挥调度",
          key: "zhdd"
        },
        {
          img: "/static/images/login/执法态势.png",
          name: "执法态势",
          key: "zfts"
        },
        {
          img: "/static/images/login/三色预警.png",
          name: "三色预警",
          key:"ssyj"
        },
        {
          img: "/static/images/login/考核评价.png",
          name: "绩效评估",
          key:"khpj"
        },
        {
          img: "/static/images/login/县级中心.png",
          name: "县级中心",
          key: "xjzx"
        },
        {
          img: "/static/images/login/应用集成.png",
          name: "应用集成",
          key: "yyjc"
        },
        {
          img: "/static/images/login/案件回访.png",
          name: "案件回访",
          key: "ajhf"
        }
      ]
    },
    computed: {
      city() {
        return localStorage.getItem("adminCity")
      }
    },
    mounted() {
      this.getPageMenu()
    },
    methods: {
      getPageMenu() {
        $api("/xzzfj_sy_cd",{area:localStorage.getItem("adminCity")}).then(res => {
          this.pageArr = res.data
          this.pageArr.forEach((item,i) => {
            item.components = this.componentsList.find(item2 => item2.key == item.key)?this.componentsList.find(item2 => item2.key == item.key).components:""
          })
        })
      },
      pageJump(item) {
        switch (item.name) {
          case "绩效评估":
            // $api2Get("/token/getTokenInfo",{jmppage:"ks"}).then(res => {
            $api2Get("/token/getTokenInfo1",{ type: 'dashboard', module: 'xzzfpjzb' }).then(res => {
              if (res.data.code == 200) {
                this.openHtmlByMode(res.data.data.url)
              }
            })
            // this.openHtmlByMode('http://10.24.161.237/index20230526.html#/feedback')
            break;
          case "三色预警":
            // $api2Get("/token/getTokenInfo",{jmppage:"home"}).then(res => {
            $api2Get("/token/getTokenInfo1",{ type: 'dashboard', module: 'sy' }).then(res => {
              if (res.data.code == 200) {
                this.openHtmlByMode(res.data.data.url)
              }
            })
            // this.openHtmlByMode('http://10.24.161.237/index20230526.html')
            break;
          case "驾驶舱":
            this.getPage(item)
            break;
          case "指挥调度":
            this.getPage(item)
            break;
          case "执法态势":
            this.getPage(item)
            break;
          case "县级中心":
            this.getPage(item)
            break;
          case "应用集成":
            this.getPage(item)
            break;
          case "后台管理":
            if (this.getCurrentPortWithDefault() == "8300") {
              this.openHtmlByMode(localStorage.getItem("env") === 'test' ? 'http://192.168.110.174:9121' : 'https://csdn.dsjj.jinhua.gov.cn:8303')
            } else {
              this.openHtmlByMode(localStorage.getItem("env") === 'test' ? 'http://192.168.110.174:9121' : 'https://csdn.dsjj.jinhua.gov.cn:8305')
            }
            break;
          case "案件回访":
            this.getPage(item)
            break;
        }
      },
      getPage(item) {
        localStorage.setItem("currentPage", JSON.stringify(item))
        this.openHtmlByMode(baseURL.url + "/index.html")
      },
      openHtmlByMode(url) {
        window.open(url);
      },
      //获取端口号
      getCurrentPortWithDefault() {
        let port = window.location.port;
        if (port === '') {
          if (window.location.protocol === 'http:') {
            port = '80';
          } else if (window.location.protocol === 'https:') {
            port = '443';
          }
        }
        return port;
      }
    }
  })
</script>
<style>
  body {
    margin: 0 auto;
  }

  .choosePage {
    width: 3840px;
    height: 2160px;
    background: url("/static/images/login/bg.png");
    background-size: cover;
    overflow: hidden;
    position: relative;
  }

  .name {
      position: absolute;
      left: calc(50% - 340px);
      top: 70px;
      font-size: 72px;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      color: #E3F3FF;
  }

  .pages {
    /* width: 3222px; */
    height: 878px;
    margin: 641px 100px 0 100px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }

  .pageItem {
    width: 472px;
    height: 878px;
    background-size: cover;
    display: flex;
    justify-content: center;
    cursor: pointer;
  }

  .pageName {
    font-size: 72px;
    font-family: YouSheBiaoTiHei;
    font-weight: 400;
    color: #E3F3FF;
    margin-top: 586px;
    letter-spacing: 5px;
  }

  .tops{
    width:100%;
    height:191px;
    padding:0 171px;
    box-sizing: border-box;
    position: absolute;
    top:186px;
    display: flex;
    justify-content: right;
  }
  .topItem{
    width:466px;
    height:191px;
    margin-left:80px;
    cursor: pointer;
  }
  @font-face {
    font-family: YouSheBiaoTiHei;
    src: url("/static/fonts/YouSheBiaoTiHei-2.ttf");
    font-weight: normal;
    font-style: normal;
  }

</style>

</html>
