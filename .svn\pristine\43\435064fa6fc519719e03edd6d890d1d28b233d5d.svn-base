<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>案件回访-左</title>
  <link rel="stylesheet" href="/static/css/animate.css" />
  <link rel="stylesheet" href="/static/css/animate_dn.css" />
  <link rel="stylesheet" href="/static/css/viewCss/index.css" />
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/css/viewCss/index.css" />
  <link rel="stylesheet" href="/elementui/css/index.css" />
  <script src="/Vue/vue.js"></script>
  <script src="/jquery/jquery-3.6.1.min.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/echarts/echarts.min.js"></script>
  <script src="/echarts/echarts-gl.min.js"></script>
  <script src="/static/js/jslib/vue-count-to.min.js"></script>
  <script src="/static/js/jslib/biz.min.js"></script>
  <script src="/elementui/js/index.js"></script>
  <script src="/static/js/jslib/moment.js"></script>
  <style>
      .unit {
          margin-left: -20px;
          font-size: 50px;
      }
    @keyframes move {
      0% {
        transform: translateY(0px);
      }

      50% {
        transform: translateY(20px);
      }

      100% {
        transform: translateY(0px);
      }
    }

    .el-range-editor .el-range-input {
      background: transparent;
    }

    .el-picker-panel__icon-btn {
      color: #fff !important;
    }

    .el-input__inner {
      background-color: rgba(19, 44, 78, 0.8) !important;
      border: 1px solid #359cf8 !important;
    }

    .el-date-editor .el-range-input,
    .el-date-editor .el-range-separator {
      font-size: 25px !important;
    }

    .el-date-editor .el-range__icon {
      font-size: 22px !important;
    }

    .el-date-editor .el-range-input,
    .el-month-table td .cell {
      color: #fff !important;
    }

    .el-picker-panel {
      border: 1px solid #00b7f3;
      box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%) !important;
      background: #0f233e !important;
    }

    .el-month-table,
    .el-year-table,
    .el-date-range-picker__header div {
      font-size: 20px !important;
    }

    .el-month-table td.in-range div,
    .el-month-table td.in-range div:hover {
      background-color: #264068 !important;
    }

    .el-date-editor .el-range__close-icon {
      font-size: 20px !important;
    }

    .el-date-editor .el-range-separator {
      color: #e6e9ee !important;
    }

    .hfgk-container {
      width: 100%;
      height: 728px;
      margin-bottom: 49px;
    }

    .hfgk-container-tabs,
    .hfjd-container-tabs {
      width: 100%;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin-top: 27px;
    }

    .hfgk-container-tab,
    .hfjd-container-tab {
      width: 210.3px;
      height: 59px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 36px;
      color: #ABCEEF;
      font-style: italic;
      text-align: center;
      cursor: pointer;
    }

    .hfgkContainerActiveTab {
      width: 210.3px;
      height: 59px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      font-size: 36px;
      color: #DAEDFF;
      font-style: italic;
      background: url('/static/images/ajhf/activeBg.png') no-repeat;
      background-size: cover;
      text-align: center;
    }

    .hfgk-container-indexs {
      width: 100%;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin-top: 40px;
    }

    .hfgk-container-index {
      width: 431px;
      height: 149.3px;
      background-size: cover;
    }

    .hfgk-container-index-inner {
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      align-items: flex-start;
      margin-left: 164px;
    }

    .hfgk-container-index-inner-name {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 36px;
      color: #D1D6DF;
      line-height: 64px;
      font-style: italic;
      background: linear-gradient(0deg, #ACDDFF 0%, #FFFFFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .hfgk-container-index-inner-value-ajzs {
      font-family: DINCond-Bold;
      font-weight: 400;
      font-size: 60px;
      color: #EED252;
      font-style: italic;
    }

    .hfgk-container-index-inner-value-hfzs {
      font-family: DINCond-Bold;
      font-weight: 400;
      font-size: 60px;
      color: #3CFDFF;
      font-style: italic;
    }

    .ajzs {
      background: url("/static/images/ajhf/ajzs.png") no-repeat;
    }

    .hfzs {
      background: url("/static/images/ajhf/hfzs.png") no-repeat;
    }

    .hfgk-container-indexs-ballLine {
      width: 100%;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
    }

    .hfgk-container-indexs-ball {
      width: 464px;
      height: 470px;
      background: url("/static/images/ajhf/ball.png") no-repeat;
      background-size: cover;
      text-align: center;
    }

    .hfgk-container-indexs-ball-percent {
      font-family: DINCond-Bold;
      font-weight: 400;
      font-size: 80px;
      color: #FFFFFF;
      text-shadow: 0px 2px 8px rgba(5, 28, 55, 0.42);
      background: linear-gradient(0deg, rgba(14, 197, 236, 1) 0%, rgba(49, 190, 255, 1) 0%, rgba(239, 252, 254, 1) 58.7646484375%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-top: 163px;
    }

    .hfgk-container-indexs-ball-text {
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 32px;
      color: #FEFFFF;
      margin-top: 15px;
    }

    .hfjd-container {
      width: 100%;
      height: 1010px;
    }

    .hfjdCharts {
      width: 100%;
      height: 800px;
    }
  </style>
</head>

<body>
  <div id="left" v-cloak>
    <div class="hearder_h1"><span>回访概况</span></div>
    <div class="hfgk-container">
      <div style="position: absolute; top: 26px; left: 680px; z-index: 2" v-show="year != '2023'">
        <el-date-picker v-model="value1" type="monthrange" @change="(range) => getHfgk(range,city,year)"
          range-separator="-" start-placeholder="开始月份" value-format="yyyy-MM" end-placeholder="结束月份">
        </el-date-picker>
      </div>
      <!-- <div class="hfgk-container-tabs">
          <div class="hfgk-container-tab" v-for="(item,i) in HfgkTabList" :key="i" :class="{hfgkContainerActiveTab: currentHfgk == i}" @click="hfgkTabClick(i,item)">
            {{ item.type }}
          </div>
        </div> -->
      <div class="hfgk-container-indexs">
        <div class="hfgk-container-index ajzs">
          <div class="hfgk-container-index-inner" @click="showDialog('案件总数')">
            <div class="hfgk-container-index-inner-name">案件总数（件）</div>
            <div class="hfgk-container-index-inner-value-ajzs">{{ currentHfgkData.ajzs }}</div>
          </div>
        </div>
        <div class="hfgk-container-index hfzs">
          <div class="hfgk-container-index-inner" @click="showDialog('回访总数')">
            <div class="hfgk-container-index-inner-name">回访总数（件）</div>
            <div class="hfgk-container-index-inner-value-hfzs">{{ currentHfgkData.fhzs }}</div>
          </div>
        </div>
      </div>
      <div class="hfgk-container-indexs-ballLine">
        <div class="hfgk-container-indexs-ball">
          <div class="hfgk-container-indexs-ball-percent"><span>{{ currentHfgkData.fhbl }}</span> <span class="unit">%</span></div>
          <div class="hfgk-container-indexs-ball-text">回访比例</div>
        </div>
        <div class="hfgk-container-indexs-ball">
          <div class="hfgk-container-indexs-ball-percent"><span>{{ currentHfgkData.myd }}</span> <span class="unit">%</span></div>
          <div class="hfgk-container-indexs-ball-text">满意度</div>
        </div>
      </div>
    </div>
    <div class="hearder_h1"><span>回访进度</span></div>
    <div class="hfjd-container">
      <div style="position: absolute; top: 920px; left: 680px; z-index: 2" v-show="year != '2023'">
        <el-date-picker v-model="value2" type="monthrange" @change="(range) => getHfjd(range,city,year)"
          range-separator="-" start-placeholder="开始月份" value-format="yyyy-MM" end-placeholder="结束月份">
        </el-date-picker>
      </div>
      <!-- <div class="hfjd-container-tabs">
          <div class="hfjd-container-tab" v-for="(item,i) in HfjdTabList" :key="i" :class="{hfgkContainerActiveTab: currentHfjd == i}" @click="HfjdTabClick(i,item)">
            {{ item.type }}
          </div>
        </div> -->
      <div class="hfjdCharts" id="hfjdCharts"></div>
    </div>
  </div>
  <script>
    window.parent.eventbus &&
      window.parent.eventbus.on("yearChange", (year) => {
        vm.initApi(localStorage.getItem("city"), year);
      });
    let vm = new Vue({
      el: "#left",
      data: {
        year: "",
        city: "",
        value1: [
          new Date().getFullYear() + "-01",
          moment(new Date()).format("YYYY-MM"),
        ],
        value2: [
          new Date().getFullYear() + "-01",
          moment(new Date()).format("YYYY-MM"),
        ],
        currentHfgk: 0,
        currentHfgkData: {},
        HfgkTabList: [{
            type: "全部",
            value: "1"
          },
          {
            type: "重大案件",
            value: "2"
          },
          {
            type: "普通程序",
            value: "3"
          },
          {
            type: "涉企案件",
            value: "4"
          },
          {
            type: "投诉举报",
            value: "5"
          }
        ],
        currentHfjd: 0,
        currentHfjdData: {},
        HfjdTabList: [{
            type: "全部",
            value: "1"
          },
          {
            type: "重大案件",
            value: "2"
          },
          {
            type: "普通程序",
            value: "3"
          },
          {
            type: "涉企案件",
            value: "4"
          },
          {
            type: "投诉举报",
            value: "5"
          }
        ]
      },
      mounted() {
        this.year = localStorage.getItem("year");
        this.city = localStorage.getItem("city");
        this.initApi(this.city, this.year);
        //监测县市区的切换
        window.parent.eventbus &&
          window.parent.eventbus.on("cityChange", (city) => {
            let filtName =
              city == "金义新区" ?
              "金东区" :
              city == "金华开发区" ?
              "开发区" :
              city;
            this.initApi(filtName, this.year);
          });
      },
      methods: {
        initApi(city, year) {
          if (year && year != '') {
            if (year == "2023") {
              this.value1 = ["2023-01", "2023-12"]
              this.value2 = ["2023-01", "2023-12"]
            } else {
              this.value1 = ["2024-01", moment(new Date()).format("YYYY-MM")]
              this.value2 = ["2024-01", moment(new Date()).format("YYYY-MM")]
            }
            this.getHfgk(this.value1, city);
            this.getHfjd(this.value2, city);
          }
        },
        hfgkTabClick(i, item) {
          this.currentHfgk = i;
          this.getHfgk(this.value1, this.city)
        },
        HfjdTabClick(i, item) {
          this.currentHfjd = i;
          this.getHfjd(this.value1, this.city)
        },
        //获取回访概况信息
        getHfgk(range, city, ) {
          // console.log(range, city);
          $api2Get("/ajhf/zftsZfrw/dpHfgk", {
            xsq: city,
            startTime: range[0],
            endTime: range[1],
          }).then(res => {
            if (res.data.code == 200) {
              this.currentHfgkData = res.data.data
            }
          })
        },
        //获取回访进度信息
        getHfjd(range, city) {
          $api2Get("/ajhf/zftsZfrw/hfjd", {
            xsq: city,
            startTime: range[0],
            endTime: range[1],
          }).then(res => {
            if (res.data.code == 200) {
              this.currentHfjdData = res.data.data
              this.initHfjdCharts(this.currentHfjdData)
            }
          })

        },
        initHfjdCharts(currentHfjdData) {
          let myChart = echarts.init(document.getElementById("hfjdCharts"));
          let option = {
            tooltip: {
              trigger: "axis",
              borderWidth: 0,
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
              },
              formatter: function (params) {
                return params.find(item => item.seriesName != "背景").axisValue + ": " + params.find(item => item
                  .seriesName != "背景").value + "%";
              },
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '32',
              },
            },
            grid: {
              top: "5%",
              left: "5%",
              right: "12%",
              bottom: "0",
              containLabel: true,
            },
            xAxis: {
              type: "value",
              show: false,
            },
            yAxis: {
              name: "",
              type: "category",
              triggerEvent: false,
              inverse: true,
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
                length: 10,
              },
              axisLabel: {
                interval: 0,
                textStyle: {
                  color: "#FFFFFF",
                  fontSize: 32,
                },
              },
              data: currentHfjdData.map(item => item.xsq),
            },
            series: [{
              type: "bar",
              name: "",
              showBackground: true,
              backgroundStyle: {
                color: 'transparent'
              },
              itemStyle: {
                normal: {
                  barBorderRadius: 30,
                  color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [{
                      offset: 0,
                      color: "rgba(60,253,255,0.2)",
                    },
                    {
                      offset: 1,
                      color: "#3CFDFF",
                    },
                  ]),
                }
              },
              label: {
                show: true,
                position: [760, -3],
                color: "#fff",
                formatter: function (params) {
                  return params.value + "%";
                },
                fontSize: 32,
                textStyle: {
                  color: '#FFFFFF',
                  fontWeight: 'bold',
                  fontFamily: 'Source Han Sans CN'
                }
              },
              barWidth: 20,
              color: "#539FF7",
              data: currentHfjdData.map(item => item.jd),
            }, {
              name: "背景",
              type: "bar",
              barWidth: 20,
              barGap: "-100%",
              data: currentHfjdData.map(item => 100),
              itemStyle: {
                normal: {
                  barBorderRadius: 30,
                  color: "#094471",
                },
              },
              z: 0,
            }]
          }
          myChart.setOption(option)
        },
        showDialog(name) {
          window.parent.lay.openIframe({
            type: "openIframe",
            name: "ajhfDialog",
            id: "ajhfDialog",
            src: baseURL.url + "/static/citybrain/ajhf/ajhf_Dialog.html",
            left: "1160px",
            top: "255px",
            width: "1515px",
            height: "1186px",
            zIndex: "666",
            argument: {
              type: name
            },
          });
        },
      },
    });
  </script>
</body>

</html>
