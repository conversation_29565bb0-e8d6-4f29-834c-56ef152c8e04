<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>添加水波纹效果图层</title>

    <link
      rel="stylesheet"
      href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <script src="./index.js" type="module"></script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        right: 20px;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }
      .color-btn {
        border: 1px solid rgb(173, 172, 172);
        width: 40px;
        height: 20px;
        cursor: pointer;
      }

      #navy {
        background-color: #25427c;
      }

      #green {
        background-color: #039962;
      }

      #turqoise {
        background-color: #a2f9f5;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv"></div>
    <div class="tools">
      <div>
        <p>步骤1：添加图层</p>
        <button onclick="addLayer();">添加水波纹图层</button>
        <p>步骤2：设置降水时长</p>
        <h4>降水时长</h4>
        <input
          type="radio"
          name="waveStrengthRadio"
          value="10"
          id="calm"
        /><label for="calm">10s</label><br />
        <input
          type="radio"
          name="waveStrengthRadio"
          value="15"
          id="rippled"
        /><label for="rippled">15s</label><br />
        <input
          type="radio"
          name="waveStrengthRadio"
          value="20"
          id="slight"
        /><label for="slight">20s</label><br />
        <input
          type="radio"
          name="waveStrengthRadio"
          value="25"
          id="moderate"
        /><label for="moderate">25s</label><br />
        <p></p>
        <button onclick="removeLayer();">停止分析</button>
      </div>
    </div>
  </body>
  <script>
    let layer;
    async function addLayer() {
      ArcGisUtils.addWaterRenderLayer();
    }
    async function removeLayer() {
      ArcGisUtils.addWaterRenderLayer();
    }

    const waveStrengthRadio = document.getElementsByName("waveStrengthRadio");

    for (let i = 0; i < waveStrengthRadio.length; i++) {
      const element = waveStrengthRadio[i];
      element.addEventListener("change", (event) => {
        let time = Number(event.target.value);
        ArcGisUtils.addWaterRenderLayerOffset(time);
      });
    }
  </script>
</html>
