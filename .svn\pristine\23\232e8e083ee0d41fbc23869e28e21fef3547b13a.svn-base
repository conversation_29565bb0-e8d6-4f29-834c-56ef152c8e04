<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>前端生成热力图</title>

    <link
      rel="stylesheet"
      href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/base16/dracula.min.css"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/go.min.js"></script>
    <script src="./index.js" type="module"></script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        left: 50%;
        width: 50%;
        height: 200px;
        display: flex;
      }

      .tools span {
        cursor: pointer;
        background-color: blue;
        width: 150px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        color: white;
      }
      .description {
        position: absolute;
        right: 10px;
        top: 10px;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv">
      <div class="tools">
        <span onclick="add()">添加</span>
        <span onclick="remove()">移除</span>
      </div>
    </div>
    <div class="description">
      添加：
      <pre><code class="language-javascript">
        heatmapLayer = ArcGisUtils.loadHeatmapLayer({
            data,
            colorStops: [
              { ratio: 0, color: "rgba(0, 0, 255, .1)" },
              { ratio: 0.2, color: "rgba(0, 0, 255, .3)" },
              { ratio: 0.4, color: "rgba(0, 0, 255, .6)" },
              { ratio: 0.6, color: "rgba(0, 0, 255, 1)" },
              { ratio: 0.8, color: "rgba(255, 0, 0, .6)" },
              { ratio: 1, color: "rgba(255, 0, 0, 1)" },
            ],
            maxDensity: 1000,
            radius: 5,
          });
          </code></pre>
      移除：
      <pre><code class="language-javascript">
            view.map.remove(layer);
          </code></pre>
    </div>
  </body>

  <script>
    let heatmapLayer;
    async function add() {
      const response = await fetch("./utils/population.json");
      const { data } = await response.json();
      const partData = data.slice(0, 10000);
      heatmapLayer = ArcGisUtils.loadHeatmapLayer({
        data,
        colorStops: [
          { ratio: 0, color: "rgba(0, 0, 255, .1)" },
          { ratio: 0.2, color: "rgba(0, 0, 255, .3)" },
          { ratio: 0.4, color: "rgba(0, 0, 255, .6)" },
          { ratio: 0.6, color: "rgba(0, 0, 255, 1)" },
          { ratio: 0.8, color: "rgba(255, 0, 0, .6)" },
          { ratio: 1, color: "rgba(255, 0, 0, 1)" },
        ],
        maxDensity: 1000,
        radius: 5,
      });
    }

    async function remove() {
        view.map.remove(heatmapLayer)
    }
  </script>
  <script>
    hljs.highlightAll();
  </script>
</html>
