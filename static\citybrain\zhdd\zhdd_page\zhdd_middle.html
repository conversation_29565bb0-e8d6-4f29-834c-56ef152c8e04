<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>中</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/js/jslib/layer.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/elementui/js/index.js"></script>
    <link rel="stylesheet" href="/static/css/viewCss/zhdd_middle.css" />
  </head>

  <body>
    <div id="center">
      <div class="mapIcon">
        <div class="iconItem" @click="personClick" style="cursor: pointer">
          <div :class="personStatus?'active':'icon-bg'">
            <img src="/static/images/zhdd/relitu.png" title="人群热力" />
          </div>
          <div :class="personStatus?'active-title':'title'">人群热力</div>
        </div>

        <!-- <div class="iconItem" @click="discussClick" style="cursor: pointer">
          <div :class="discussStatus?'active':'icon-bg'">
            <img src="/static/images/zhdd/一键会商.png" title="一键会商" />
          </div>
          <div :class="discussStatus?'active-title':'title'">一键会商</div>
        </div> -->

        <div class="iconItem" @click="giveNotice" style="cursor: pointer">
          <div :class="noticeStatus?'active':'icon-bg'">
            <img src="/static/images/zhdd/一键通知.png" title="一键通知" />
          </div>
          <div :class="noticeStatus?'active-title':'title'">一键通知</div>
        </div>

        <div @click="dispatchClick" style="cursor: pointer">
          <div :class="dispatchStatus?'active':'icon-bg'">
            <img src="/static/images/zhdd/一键调度.png" title="一键调度" />
          </div>
          <div :class="dispatchStatus?'active-title':'title'">一键调度</div>
        </div>
      </div>
    </div>
    <script>
      var vmMiddle = new Vue({
        el: "#center",
        data: {
          personStatus: false,
          discussStatus: false,
          noticeStatus: false,
          dispatchStatus: false,
          sgId: "",
        },
        mounted() {},
        methods: {
          // 人群热力
          personClick() {
            this.personStatus = !this.personStatus;
            if (this.personStatus) {
              const mapData = {
                layerid: "rkztHot0",
                type: "dynamic",
              };
              window.parent.mapUtil.loadHeatmapLayer(mapData);
            } else {
              window.parent.mapUtil.removeLayer("rkztHot0");
            }
          },
          // 一键会商
          discussClick() {
            this.discussStatus = !this.discussStatus;
            if (this.discussStatus) {
              window.parent.lay.openIframe({
                type: "openIframe",
                name: "zhddDiscuss",
                id: "zhddDiscuss",
                src:
                  baseURL.url +
                  "/static/citybrain/commonts/zhdd/zhddDiscuss.html?sgid=" +
                  this.sgId +
                  "&office=''",
                left: "1020px",
                top: "380px",
                width: "1800px",
                height: "1000px",
                zIndex: "666",
                argument: { name: "", sgid: this.sgid },
              });
            } else {
              window.parent.lay.closeIframeByNames(["zhddDiscuss"]);
            }
          },
          // 一键通知
          giveNotice() {
            this.noticeStatus = !this.noticeStatus;
            if (this.noticeStatus) {
              window.parent.lay.openIframe({
                type: "openIframe",
                name: "zhddNotice",
                id: "zhddNotice",
                src:
                  baseURL.url +
                  "/static/citybrain/commonts/zhdd/zhddNotice.html",
                left: "1280px",
                top: "575px",
                width: "1300px",
                height: "800px",
                zIndex: "666",
              });
            } else {
              window.parent.lay.closeIframeByNames(["zhddNotice"]);
            }
          },
          // 一键调度
          dispatchClick() {
            this.dispatchStatus = !this.dispatchStatus;
            if (this.dispatchStatus) {
              window.parent.lay.openIframe({
                type: "openIframe",
                name: "zhddDispatch",
                id: "zhddDispatch",
                src:
                  baseURL.url +
                  "/static/citybrain/commonts/zhdd/zhddDispatch1.html",
                // left: "1910px",
                // top: "190px",
                // width: "730px",
                // height: "1160px",
                left: "1020px",
                top: "380px",
                width: "1800px",
                height: "1000px",
                zIndex: "666",
              });
            } else {
              window.parent.frames["zhddDispatch"].postMessage(
                { yjdd_clear: "清除指挥调度操作" },
                "*"
              );
              window.parent.lay.closeIframeByNames(["zhddDispatch"]);
            }
          },
        },
      });
      window.parent.eventbus &&
        window.parent.eventbus.on("leftIframeShow", () => {
          try {
            parent.document.getElementById("zhdd_middle").style.top = "1400px";
          } catch (error) {}
        });
      window.parent.eventbus &&
        window.parent.eventbus.on("leftIframeHide", () => {
          try {
            parent.document.getElementById("zhdd_middle").style.top = "1960px";
          } catch (error) {}
        });
    </script>
  </body>
</html>
