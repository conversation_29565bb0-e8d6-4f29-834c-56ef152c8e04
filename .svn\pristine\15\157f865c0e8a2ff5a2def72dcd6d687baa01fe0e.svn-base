<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
    <title>效果</title>

    <link rel="stylesheet" href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css" />
    <script src="./libs/three-r79.min.js"></script>
    <script src="./libs/three-r116.min.js"></script>
    <script src="./index.js" type="module"> </script>

    <style>
        html,
        body,
        #viewDiv {
            padding: 0;
            margin: 0;
            height: 100%;
            width: 100%;
        }

        .tools {
            position: absolute;
            top: 20px;
            left: 50%;
            width: 50%;
            height: 200px;
            display: flex;
        }

        .tools span {
            cursor: pointer;
            background-color: blue;
            width: 150px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 20px;
            color: white;
        }
    </style>

</head>

<body>
    <div id="viewDiv">
        <div class="tools">
            <span onclick="ArcGisUtils.addHeatMap()">三维热力图</span>
            <span onclick="ArcGisUtils.removeHeatmap()">清除三维热力图</span>
            <span onclick="ArcGisUtils.addLineEffect()">添加道路效果</span>
            <span onclick="ArcGisUtils.removeLineEffect()">移除道路效果</span>
            <span onclick="ArcGisUtils.addRoadLayer()">添加路网</span>
            <span onclick="ArcGisUtils.removeRoadLayer()">移除路网</span>
        </div>
    </div>
</body>

</html>