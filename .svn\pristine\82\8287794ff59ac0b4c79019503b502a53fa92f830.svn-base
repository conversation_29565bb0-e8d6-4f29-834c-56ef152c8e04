<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>省域sdi类型添加</title>

    <link
      rel="stylesheet"
      href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <script src="./index.js" type="module"></script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        left: 50%;
        width: 50%;
        height: 200px;
        display: flex;
      }

      .tools span {
        cursor: pointer;
        background-color: blue;
        width: 150px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        color: white;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv">
      <div class="tools">
        <span onclick="add()">添加</span>
      </div>
    </div>
  </body>
  <script>
    function add() {
      ArcGisUtils.loadArcgisLayer( view,{
        type: "web-tile",
        title: "科技版矢量地图-面",
        urlTemplate:"https://sdi.zjzwfw.gov.cn/mapserver/vmap/WMTS/1.0/zjvmap/tdt_kejiganyangshi_2017?layer=imgmap&style=default&tilematrixset=default028mm&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image/jpgpng&TileMatrix={z}&TileCol={x}&TileRow={y}&token=sy-6786f525-a9bd-477f-ab4a-f26d29524077",
        subDomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7"],
        tileInfo: view.map.basemap.baseLayers._items[0].tileInfo,
        spatialReference: { wkid: 4490 },
        fullExtent: {
          xmin: -180,
          ymin: -90,
          xmax: 180,
          ymax: 90,
          spatialReference: { wkid: 4490 },
        },
      })
    }
  </script>
</html>
