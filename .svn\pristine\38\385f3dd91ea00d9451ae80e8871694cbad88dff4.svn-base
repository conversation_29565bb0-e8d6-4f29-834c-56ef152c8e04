import { getLayerConfigById } from "./layerConfig.js";
import layerCreatAsync from "./layerCreatAsync.js";

const LAYER_ID = "TRA_NET_LN";
async function addRoadLayer() {
  const layerConfig = getLayerConfigById(LAYER_ID);
  const layer = await layerCreatAsync(layerConfig);
  view.map.add(layer);
}

function removeRoadLayer() {
  let layer = view.map.findLayerById(LAYER_ID);
  view.map.remove(layer);
}

export { addRoadLayer, removeRoadLayer };
