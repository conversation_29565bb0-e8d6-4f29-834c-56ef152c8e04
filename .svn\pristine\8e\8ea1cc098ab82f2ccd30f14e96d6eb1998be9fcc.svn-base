import Emitter from '../emitter.js'
import Tween from '../tween.js'
import { Shape } from '../const.js'

const THREE = window.THREE_r123;

class SmokeEmitter extends Emitter {

  constructor(location) {
    super({
      positionShape: Shape.CUBE,
      position: new THREE.Vector3(location[0], location[1], location[2]),
      positionRange: new THREE.Vector3(10, 10, 10),

      velocityShape: Shape.CUBE,
      velocity: new THREE.Vector3(0, 0, 40),
      velocityRange: new THREE.Vector3(20, 40, 20),

      angle: 0,
      angleRange: 720,
      angleVelocity: 10,
      angleVelocityRange: 0,

      textureSmoke: new THREE.TextureLoader().load('./img/smoke.png'),

      sizeTween: new Tween([0, 1], [16, 64]),
      opacityTween: new Tween([0.8, 2], [0.5, 0]),
      colorTween: new Tween([0.4, 1], [new THREE.Vector3(0, 0, 0.2), new THREE.Vector3(0, 0, 0.5)]),

      // size: 1.0,
      // sizeRange: 2.0,
      // color: new THREE.Vector3(0.1, 0.05, 0.05),
      // colorRange: new THREE.Vector3(0.5, 0.25, 0.01),
      // opacity: 1,
      // blendMode: THREE.AdditiveBlending,

      particlesPerSecond: 500,
      particleDeathAge: 1,
      deathAge: 60
    })
  }

}

export default SmokeEmitter
