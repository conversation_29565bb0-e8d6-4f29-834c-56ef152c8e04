/**
 * ccbar_plugin.callControl为对外调用方法
 */

 function ccbar_debugger(msg,type){
    if(_ccbarDebug){
        if(type&&type=='error'){
            console.error(msg);
        }else{
            console.log(msg);
        }
    }
}
 
//ccbar自定义弹层
var ccbar_popup = {
    zIndex:1001,
    setTimeout:null,
    closeAll: function(type) {
        if (type == "loading") {
            $(".ccbar_mask").fadeOut()
        }
    },
    open: function(el) {
        $(el).fadeIn()
    },
    close: function(el) {
        $(el).fadeOut()                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                
    },
    load: function() {
        if ($(".ccbar_loading").length) {
            $(".ccbar_mask").fadeIn()
        } else {
            var html = '<div class="ccbar_mask"><span class="ccbar_loading"></span></div>';
            $(".ccbar-system-mod").append(html)
        }
    },
    alert: function(msg,callback) {
        $('.ccbar_alert').remove();
        ccbar_popup.zIndex++;
        var id = 'ccbarAlert' + ccbar_popup.zIndex;
        var html = '<div id="'+id+'" class="ccbar_panel_login ccbar_alert" style="min-height:inherit;display:block"><a href="javascript:void(0)" onclick="$(\'#'+id+'\').remove()" class="ccbarbtn_close"></a><form class="ccbar-form"><div><div class="ccbar-form-input-group">'+msg+'</div><div style="text-align:center;padding-top:10px"><button type="button" style="width:120px" onclick="$(\'#'+id+'\').remove()" class="ccbar_btn_login">'+ccbarI18nText('确定','btn')+'</button></div></div></form></div>'
        $('body').append(html)
    },
    msg: function(msg, time) {
        // $(".ccbar_msg").hide();
        var msgTips = null
        if($('.ccbar_msg').length>0){
            msgTips = $('.ccbar_msg');
            msgTips.html(msg);
            clearTimeout(ccbar_popup.setTimeout)
        }else{
            var id = "msg" + new Date().getTime();
            var html = "<span id=" + id + ' class="ccbar_msg">' + msg + "</span>";
            $("body").append(html);
            var msgTips = $("#" + id);
        }
        var t = time || 1500;
        
        msgTips.css("margin-left", 0 - msgTips.outerWidth() / 2);
        ccbar_popup.setTimeout = setTimeout(function() {
            msgTips.fadeOut("100",
            function() {
                msgTips.remove()
            })
        },t)
    }
};
var ccbarCallClock = null; //定时器,防止某些窗口一直不关闭
var ccbarCallClockTime = 60*1e3;//定时器超时时间
var lastConsultedInfo = {};
var _bpoFlag = 0;
var _bpoPrefix ='';
var ccbar_status = {
    funcMask:{},
    agent_state:{},
    callEvent:null,
    current:{
        caller:null,
        called:null,
        custPhone:null,
        displayCustPhone:null
    }
};


var ccbar = (function() {
    var _action_ = 'click';
    //按钮绑定data事件
    var dataType = 'ccbartype';
    //data属性对应表
    var dataInfo = {
            agentUserName: 'ccbarusername', //用户工号
            agentUserPSW: 'ccbaruserpsw', //用户密码
            agentPhone: 'ccbaruserphone', //座机
            curStatus: 'ccbarcurstatus', //坐席当前状态
            makeCallNum: 'ccbarcallnum', //外呼输入框
        }
        // 暴露的容器
    var _ccbar_ = {};
    _ccbar_Event = null;
    var phoneDialPopup = null;
    var loginPopup = null;
    var isMakeCall = false;

    var needcallPhobne = null; //拉起话机的回调

    function callerphoneNeedLogin(){
        tipsMsg('请签入后再尝试拉起话机');
    }

    /*状态整理*/

    /**
     * 链接或按钮添加data-ccbartype属性,值为下面respStatus或customStatus时,表明为相关按钮,
     * respStatus内的类型,受SDK
     */

    var status = {
        respStatus: {
            'logon': '登录',
            'logoff': '登出',
            'makecall': '外呼',
            'answercall': '应答',
            'clearcall': '挂机',
            'agentready': '置闲',
            'agentnotready': '置忙',
            'workNotReady':'进入话后整理',
            'workReady':'完成话后整理',
            "clearconnection": '结果指定号码的话路',
            "holdcall": '客户话路保持',
            "unholdcall": '客户话路恢复',
            "consultationcall": '咨询',
            "transfercall": '呼叫转移',
            "conferencecall": '三方会议',
            "listencall": '监听',
            "interventcall": '强插',
            "sstransfer": '单步转',
            "workMode":'工作模式',
            "startMonitor":'开始监听',
            "stopMonitor":'结束监听',
            'startInvent':'cmdInterventCall',//强插
            'stopInvent':'cmdStopInterventCall',//结束强插
            'stopSecretlyTalk':'stopSecretlyTalk',//结束私语
            'monitor':'监控',
            'mutecall':'静音',
        },
        customStatus: {
            'fastmakecall': '快速拨号'
        },
        callStatus: {
            'remote_delivered': '来电振铃',
            'remote_established': '开始通话',
            'callcleared': '呼叫结束',
            'INCALL':'呼入',
            'OUTCALL':'呼出'
        },
        funcMaskStatus: {
            'ready': '闲',
            'notready': '忙',
            'alerting': '振铃中',
            'talking': '通话中',
            'logoff': '未签入',
            'workNotReady': '话后整理',
            'unknow':'未知状态',
            'logout':'未登录',
            'occupy': '预占',
            'talk':'咨询',
            'held':'保持',
            'consulted':'咨询',
            'conferenced':'三方通话',
            'monitored':'监听'
        },
        agentinfo:{

        }
    };

    var curFuncStatus = null;
    
    var agentPhone = null;

    /*私有方法*/

    /**
     * [customCallcontrol ccbarAPI整合]
     * @type {Object}
     */
    var customCallcontrol = {
            /*登录*/
            logon: function(loginAcct,loginPwd,phone) {
                debugger
                if(loginAcct == null || loginAcct == undefined){
                    loginAcct = $("#ccbarLogin [data-" + dataInfo.agentUserName + "]").eq(0).val();
                    loginPwd = $("#ccbarLogin [data-" + dataInfo.agentUserPSW + "]").eq(0).val();
                    phone = $("#ccbarLogin [data-" + dataInfo.agentPhone + "]").eq(0).val();    
                }
                
                var t = $("#ccbarLogin [data-ccbartype='logon']");
                t.addClass('disabled').text('签入中...');
                loginPwd = hex_md5(loginPwd);
                ccbarsdk.logon(loginAcct,loginPwd,phone,function(rsp){
              

                	ccbarDebugger('签入回调', rsp);
                    if(rsp.data.code == 'succ'){
                        //自动拉起话机
                        if(rsp.data.result.phoneType == '3'){
                            if(rsp.data.result.sbcAddr == ''){
                                tipsMsg('签入失败，缺少SIP话机的接入地址');
                                var t = $("#ccbarLogin [data-ccbartype='logon']");
                                t.removeClass('disabled').text('签入');
                                return false; //没有sip话机,中止签入流程
                            }
                            
                        }
                        agentPhone = rsp.data.agentId;

                        callerphone = function(rs){
                            if(rs.resultCode=='000'){
                                CallControl.wakeupSIP();
                            }
                        }
                        //签入计时器,防止失去响应
                        clearTimeout(ccbarCallClock);
                        ccbarCallClock = setTimeout(function(){
                            var t = $("#ccbarLogin [data-ccbartype='logon']");
                            t.removeClass('disabled').text('签入');
                        },ccbarCallClockTime);
                        
                    }else{
                        tipsMsg(rsp.data.content);
                        var t = $("#ccbarLogin [data-ccbartype='logon']");
                        t.removeClass('disabled').text('签入');
                    }
                });
            },
            /*登出*/
            logoff: function() {
               CallControl.logoff(function(result){
                    try{
                        if(result.state != '1'){
                            tipsMsg(result.msg);
                        }
                    }catch(e){}
                    
                });
            },
            /*挂机*/
            clearCall: function() {
                CallControl.clearCall();
            },
            /*外呼*/
            callNumber: function(isvideo) {
                var callNumber_val = $("#_ccbar_callNumber").val();

                this.makeCall(callNumber_val,null,isvideo);
            },
            /*呼叫转移等功能的坐席选择面板*/
            agentSel:function(type){
                $("#ccbar_hidden_sel_type").val(type);
                CallControl.agentList('',function(result){
                    if(result.state) {
                        var groups = result.data.result.groups;
                        var agents = result.data.result.agents;
                        var groupsHtml = '<option value="">'+ccbarI18nText('请选择技能组','content')+'</option>';
                        for (var i = 0; i < groups.length; i++) {
                          groupsHtml+='<option value="'+groups[i].SKILL_GROUP_ID+'">'+groups[i].SKILL_GROUP_NAME+'</option>'
                        }
                        var agentsHtml = '<option>---</option>';
                        for (var j = 0; j < agents.length; j++) {
                          agentsHtml+='<option value="'+agents[j].AGENT_ID+'">'+agents[j].AGENT_NAME+'</option>'
                        }
                        $("#ccbar_select_groups").val('');
                        $("#ccbar_select_agents").val('');
                        $("#ccbar_select_agents_callers").val('');
                        $("#ccbar_select_groups").html(groupsHtml);
                        $("#ccbar_select_agents").html(agentsHtml);
                        CallControl.callerList(function(result){
                            var tempOptions = '';
                            var realCustPhone = ccbar_status.current.realCustPhone?ccbar_status.current.realCustPhone:ccbar_status.current.custPhone;

                            if(result.data.result.callerList){
                                if(ccbar_status.current.custPhone && CallControl.resultConfig.displayCustPhone == '1'){
                                    tempOptions += '<option data-displaynum="cust" value="'+realCustPhone+'">'+ccbarI18nText('客户','contebt')+'-'+replacePhoneNum(ccbar_status.current.custPhone,ccbar_status.current.displayCustPhone)+'</option>'

                                }
                                for (var i = 0; i < result.data.result.callerList.length; i++) {
                                    tempOptions += '<option value="'+result.data.result.callerList[i].prefixNum+'">'+result.data.result.callerList[i].prefixName+'</option>'
                                }

                            }else if(result.data.result.callers){
                                if(ccbar_status.current.custPhone && CallControl.resultConfig.displayCustPhone == '1'){
                                    tempOptions += '<option data-displaynum="cust" value="'+realCustPhone+'">'+ccbarI18nText('客户','contebt')+'-'+replacePhoneNum(ccbar_status.current.custPhone,ccbar_status.current.displayCustPhone)+'</option>'

                                }
                                for (var i = 0; i < result.data.result.callers.length; i++) {
                                    tempOptions += '<option value="'+result.data.result.callers[i]+'">'+result.data.result.callers[i]+'</option>'
                                }

                            }else{
                                tipsMsg(ccbarI18nText('无外显号码','content'));
                            }
                            $("#ccbar_select_agents_callers").html(tempOptions);
                            var v = $("#agentsel input[name=trantype]:checked").val()
                            if(v=='3'){//外线号码默认最后
                                $("#ccbar_select_agents_callers").append($('#ccbar_select_agents_callers [data-displaynum="cust"]'))
                            }else{
                                $("#ccbar_select_agents_callers").prepend($('#ccbar_select_agents_callers [data-displaynum="cust"]'))
                            }
                            $("#ccbar_select_agents_callers").val($('#ccbar_select_agents_callers option').eq(0).val())
                            $("#agentsel").fadeIn();
                        });
                        
                    }
                });
            },
            canCall :function(){
                var sname = $("._ccbar_curStatus").text();
                var callingStatusList = ['通话中','振铃中'];
                if($.inArray(sname,callingStatusList)>=0){
                    return true;
                }else{
                    return false;
                }
            },
            makeCall: function(num,userData,isvideo) {
                if(!CallControl.getFunc('makecall')) {tipsMsg(ccbarI18nText('当前不可外呼','content')); return;}
                num=$.trim(num).replace(/[^\dA-Za-z@+#=]/g,'');//替换掉非法字符
                var reg = /(^((\d{0,50})|(0)(\d{11})|(\d{7,8})|(\d{4}|\d{3})(\d{7,8}))$)/;
                var reg2 = /(^((1)(\d{2,5})|(9)(\d{2,5}))$)/;//紧急号码或服务电话
                if (new RegExp(reg).test(num)||new RegExp(reg2).test(num)||num.indexOf('@')!=-1 ||num.indexOf('+')!=-1||String(num).substr(0, 1) == "#") {
                    if(!userData) userData = {}
                    var callType = 2;
                    if(num.indexOf('@')!=-1){callType = 1;}
                    //调用busi,num为电话号码,后面的方法为原发起呼叫方法,作为busi的回调执行
                    var caller = $("#ccbar_phoneCallerList option:selected").val()||'';
                    if(isvideo){
                        CallControl.videoMakeCall(num,caller,JSON.stringify(userData),function(result){
                            if(CallControl.config.hidePhoneNum){
                              tipsMsg(ccbarI18nText('正在呼叫','content'));  
                            }else{
                              tipsMsg(ccbarI18nText('正在呼叫','content')+num);
                            }
                            ccbar_popup.close('#ccbarCustomPhone');
                            if(result.data.content !='succ'){
                                tipsMsg(result.data.content);
                            }
                        },callType);

                    }else{
                        CallControl.makeCall(num,caller,JSON.stringify(userData),function(result){
                            if(CallControl.config.hidePhoneNum){
                              tipsMsg(ccbarI18nText('正在呼叫','content'));  
                            }else{
                              tipsMsg(ccbarI18nText('正在呼叫','content')+num);
                            }
                            ccbar_popup.close('#ccbarCustomPhone');
                            if(result.data.content !='succ'){
                                tipsMsg(result.data.content);
                            }
                        },callType);
                    }
                
                } else {
                    tipsMsg(ccbarI18nText('请输入正确的电话号码','content'));
                }
            },
            setReady: function() {
                CallControl.agentReady();
            },
            setNotReady: function(busytype) {
                CallControl.agentNotReady(busytype);
            },
            //应答
            answercall: function(){
                CallControl.answerCall();
            },
            workNotReady : function() {// 进入话后整理
                CallControl.workNotReady();
            },
            workReady : function() {//完成话后整理
                CallControl.workReady();
            },
            //呼叫转移

            //呼叫转移
            transfer:function(called,displaynum,skillId, callType, userData){
                CallControl.transfer(called,displaynum, skillId,callType, userData)
            },
            //呼叫转移
            sstransfer:function(called, displaynum ,skillId, callType, userData,callback){
                var _userData = {laeveMsg:$('#ccbarTranferText').val()}
                userData = $.extend({},_userData,{orderData:tranferCache})
                CallControl.sstransfer(called, displaynum,skillId, callType, userData,function(result){
                    if(result && result.data == 'fail'){ tipsMsg(result.msg);}
                })
            },
            //咨询
            consultationcall:function(called,displaynum,skillId, callType, userData,callback){
                lastConsultedInfo = {
                    called:called,
                    displaynum:displaynum,
                    skillId:skillId,
                    callType:callType,
                    userData:userData
                }
                CallControl.consultation(called,displaynum,skillId, callType, userData,function(result){
                    if(result && result.data == 'fail'){ tipsMsg(result.msg);}
                })
            },
            //客户话路保持
            holdCall:function(){
                CallControl.holdCall();
            },
            //客户话路恢复
            unholdCall:function(){
                CallControl.unholdCall();
            },
            //三方通话
            conferencecall:function(called,displaynum, callType, userData,callback){
                CallControl.conference('',function(result){
                    if(result.state=='1' && result.data!='fail'){
                        tipsMsg(ccbarI18nText('已发起三方请求','content'))
                    }else{
                        tipsMsg(result.msg);
                    }
                });
            },
            //监听
            listenCall:function(listenedAgentId){
                CallControl.listenCall(listenedAgentId);
            },
            workMode:function(type){
                if(CallControl.isLogoned){
                    var typeName = {
                        'inbound':'呼入',
                        'outbound':'呼出',
                        'pdsbound':'智能外呼',
                        'all':'自动'
                    }
                    tipsMsg(ccbarI18nText('切换到'+typeName[type]+'模式','content'));
                    
                    CallControl.workMode(type);
                }
            },
        }
    /**
     * [callControl 暴露的接口操作]
     * @type {[object]}
     */
    _ccbar_.callControl = customCallcontrol;

        /**
         * @description 同意根据接口操作当前页面功能按钮元素
         * @param       {[string]}      type   [状态]
         * @param       {[string]}      status [状态值,1为可用]
         * @return      {[type]}             [description]
         */
    var _clockTimeout = null;
    _ccbar_.clock = {
        t: 0,
        reset: function() {
            clearInterval(_clockTimeout);
            this.t = 0;
        },
        start: function() {
            var el = $('[data-ccbar-text="clock"]');
            var s = this;
            clearInterval(_clockTimeout);
            _clockTimeout = setInterval(updateTime, 1000)

            function updateTime(str) {
                s.t++;
                var hours = Math.floor(s.t / 3600);
                var minutes = Math.floor((s.t % 3600) / 60);
                var seconds = s.t % 60;
                hours = hours < 10 ? '0' + hours : hours;
                minutes = minutes < 10 ? '0' + minutes : minutes;
                seconds = seconds < 10 ? '0' + seconds : seconds;
                el.text(hours + ":" + minutes + ":" + seconds);
            }
        }
    }

    /*私有方法end*/
    //更新拨号信息
    //界面更新
    _ccbar_.updateLayout = function(type, status) {
        var btn = $('[data-' + dataType + '=' + type + ']');
        var flag = status ? false : true;
        btn.prop('disabled', flag);
        if (flag) {
            btn.addClass('disabled');
        } else {
            btn.removeClass('disabled');
        }
    }

    //功能更新
    _ccbar_.funcMaskUpdate = function(obj) {
        var self = this;
        var newobj = typeof obj === "object" ? obj : {};
        var type;
        isMakeCall = obj.makecall;
        for (type in newobj) {
            self.updateLayout(type, newobj[type]);
        }
    }
    //更新坐席信息
    _ccbar_.updateAgentInfo  = function(agentInfo) {
        var self = this;
        var newobj = typeof agentInfo === "object" ? agentInfo : {};
        var type;
        for (type in newobj) {
            updateAgentInfoText(type, newobj[type]);
        }
        var directionType = agentInfo.directionType;
        if (directionType=='OUTCALL') {directionType='呼出'}else if(directionType=='INCALL'){directionType='呼出'}
        $("#_ccbar_clock .calltype").text(directionType);

        function updateAgentInfoText(type, status) {
            var btn = $('[data-ccbaragentinfo=' + type + ']').text(status);
        }
    }
    //坐席当前状态更新
   //坐席当前状态更新
    _ccbar_.agentStatusUpdate = function(agent_state,agent_state_name) {
            var sname = $("[data-agentinfo='curstatus']");
            agent_state_name  = ccbarI18nText(agent_state_name,'agentState')
            sname.text(agent_state_name);
            
            switch (agent_state) {
                case 'IDLE':
                    sname.css('color', '#11f95a');
                    break;
                case 'BUSY':
                    sname.css('color', '#f5cf55');
                    break;
                case 'ALERTING':
                    sname.css('color', '#f5cf55');
                    break;
                case 'TALK':
                    sname.css('color', '#f5cf55');
                    break;
                default:
                    sname.css('color', '#f4d0d0');
            }

        }
        //绑定事件
    _ccbar_.initBind = function() {
        //选择技能组
        $("select[name='ccbar_select_groups']").off('change').on('change',function(e){
            var groupId = $(this).val();
            if(groupId=='') {$("#ccbar_select_groups_agent").html(''); return};
            
            CallControl.agentList(groupId,function(result){
                  if (result.state) {
                    var groups = result.data.result.groups;
                    var agents = result.data.result.agents;
                    var agentsHtml = '<option value="">'+ccbarI18nText('系统自动选择','content')+'</option>';
                    for (var j = 0; j < agents.length; j++) {
                      agentsHtml+='<option value="'+agents[j].AGENT_ID+'">'+agents[j].AGENT_NAME+'</option>'
                    }
                    $("#ccbar_select_groups_agent").html(agentsHtml);
                }
            });
        });

        $("#flushAgentList").off("click").on("click",function(e){
            var groupId = '';
            CallControl.agentList(groupId,function(result){
                  if (result.state) {
                    var groups = result.data.result.groups;
                    var agents = result.data.result.agents;
                    var agentsHtml = '<option value="">---</option>';
                    for (var j = 0; j < agents.length; j++) {
                      agentsHtml+='<option value="'+agents[j].AGENT_ID+'">'+agents[j].AGENT_NAME+'</option>'
                    }
                    $("#ccbar_select_agents").html(agentsHtml);
                }
            });
        });

        $("#flushAgentList2").off("click").on("click",function(e){
            var groupId = $("#ccbar_select_groups").val();
            if(groupId=='')return;
            CallControl.agentList(groupId,function(result){
                  if (result.state) {
                    var groups = result.data.result.groups;
                    var agents = result.data.result.agents;
                    var agentsHtml = '<option value="">'+ccbarI18nText('系统自动选择','content')+'</option>';
                    for (var j = 0; j < agents.length; j++) {
                      agentsHtml+='<option value="'+agents[j].AGENT_ID+'">'+agents[j].AGENT_NAME+'</option>'
                    }
                    $("#ccbar_select_groups_agent").html(agentsHtml);
                }
            });
        });

        //绑定坐席选择功能
        $("#agentsel input[name=trantype]").off("click").on("click",function(){
            var v= $(this).val();
            if(v=='1'){
              $("#agentsel [data-trantype]").hide();
              $("#agentsel [data-trantype=1]").show();

              $('option[data-displaynum="cust"]').prop('disabled',false).show();

            }else if(v=='4'){
              $("#agentsel [data-trantype]").hide();
              $("#agentsel [data-trantype=4]").show();

              $('option[data-displaynum="cust"]').prop('disabled',false).show();
            }else if(v=='3'){
              $("#agentsel [data-trantype]").hide();
              $("#agentsel [data-trantype=3]").show();

              // $('option[data-displaynum="cust"]').hide();
              // $('option[data-displaynum="cust"]:selected').prop({"selected":false,"disabled":true});
              // $('#ccbar_select_agents_callers option').eq(0).prop('selected',true);
            }

            if(v=='3'){//外线号码默认最后
                $("#ccbar_select_agents_callers").append($('#ccbar_select_agents_callers [data-displaynum="cust"]'))
            }else{
                $("#ccbar_select_agents_callers").prepend($('#ccbar_select_agents_callers [data-displaynum="cust"]'))
            }
            $("#ccbar_select_agents_callers").val($('#ccbar_select_agents_callers option').eq(0).val())
            
        });

        $("#ccbar_agent_sel").off("click").on("click",function(){
            
            var type = $("#agentsel input[name=trantype]:checked").val();
            var called = '';
            switch (type) {
                //坐席
                case '1':
                    called = $("#ccbar_select_agents option:selected").val();
                break;
                //外线
                case '3':
                    called = $("#ccbar_select_num").val();
                break;
                //坐席组
                case '4':
                    called = $("#ccbar_select_groups option:selected").val();
                break;
            }
            if(called == ''){tipsMsg(ccbarI18nText('请选择目标坐席或呼叫对象','content')); return false;}
            var caller = ccbar_status.current.called;
            var eventType = $("#ccbar_hidden_sel_type").val();
            var displaynum = $("#ccbar_select_agents_callers").val();
            var skillId = $("#ccbar_select_groups option:selected").val();
            if(eventType == "") return;
            if(type==4){
                var skillGroupAgent = $("#ccbar_select_groups_agent option:selected").val();
                if(skillGroupAgent!=''){
                    type = 1;
                    called = skillGroupAgent;
                    customCallcontrol[eventType](called,displaynum,skillId,type,{},function(res){
                if(res.state!='1'){
                    tipsMsg(res.msg);
                }
            });
                    return false;
                }
            }
            customCallcontrol[eventType](called,displaynum,skillId,type,{});
        });


        //功能按钮绑定
        $(document).on(_action_, '[data-' + dataType + ']', function(event) {
            var t = $(this);
            if (t.hasClass('disabled')) return false;
            if (t.data('tag')==true) return false;
            var type = t.data(dataType);
            if (type in status.respStatus || type in status.customStatus) {
            var logData = {
                action:type,
                startTime:new Date().format('hh:mm:ss')
            }
            CallControl.logger && CallControl.logger.send('click',logData)

            ccbarDebugger("点击的ccbar按钮绑定了事件======>"+type);

                switch (type) {
                    //快速拨号
                    case 'fastmakecall':
                        var callNum = t.data('ccbarcallnum');
                        $('.number-area .numbers').val(callNum).trigger('blur');
                        break;

                    case 'makecall':
                        var isvideo = t.data('makecallType')=="video"?true:false;

                        if (t.data('toggle') && t.data('toggle') == 'ccbarphone') {
                           CallControl.callerList(function(result){
                                var tempOptions = '';
                                    if(result.data.result.callerList){
                                        if(ccbar_status.current.custPhone && CallControl.resultConfig.displayCustPhone == '1'){
                                            tempOptions += '<option data-displaynum="cust" value="'+ccbar_status.current.realCustPhone+'">'+ccbarI18nText('客户','content')+'-'+replacePhoneNum(ccbar_status.current.custPhone,ccbar_status.current.displayCustPhone)+'</option>'

                                        }
                                        for (var i = 0; i < result.data.result.callerList.length; i++) {
                                            tempOptions += '<option value="'+result.data.result.callerList[i].prefixNum+'">'+result.data.result.callerList[i].prefixName+'</option>'
                                        }

                                    }else if(result.data.result.callers){
                                        for (var i = 0; i < result.data.result.callers.length; i++) {
                                            tempOptions += '<option value="'+result.data.result.callers[i]+'">'+result.data.result.callers[i]+'</option>'
                                        }
                                        
                                    }else{
                                        tipsMsg(ccbarI18nText('无外显号码,不允许呼叫','content'));
                                    }
                                    $("#ccbar_phoneCallerList").html(tempOptions);
                                    phoneDialPopup =  ccbar_popup.open("#ccbarCustomPhone");
                                });
                            return;
                        } else if (t.data('calltype') && t.data('calltype') == 'fastmakecall') {
                            if($('.disabled[data-ccbartype="makecall"]').length>0) return;
                            var callNum = t.data('ccbarcallnum');
                            customCallcontrol.makeCall(callNum,null,isvideo);
                        } else {
                            customCallcontrol.callNumber(isvideo);
                        }

                        break;
                        //登出
                    case 'logoff':
                        customCallcontrol.logoff();
                        break;
                        //登录
                    case 'logon':
                        if (t.data('toggle') && t.data('toggle') == 'ccbarlogon') {
                            loginPopup =  ccbar_popup.open('#ccbarLogin');
                            return;
                        }else{
                            customCallcontrol.logon();
                        }
                        break;
                        //挂机
                    case 'clearcall':
                        customCallcontrol.clearCall();
                        break;
                        //应答
                    case 'answercall':
                        customCallcontrol.answercall();
                        break;
                    case 'agentnotready':
                        var busytype = $(this).data('busytype')
                        customCallcontrol.setNotReady(busytype);
                        break;
                    case 'agentready':
                        CallControl.agentReady();
                        break;
                        
                    case 'workNotReady':
                        customCallcontrol.workNotReady();
                        break;
                    case 'workReady':
                        customCallcontrol.workReady();
                        break;

                    //呼叫转移
                    case 'sstransfer':
                        //咨询状态直接转移
                       if(CallControl.getState()=='CONSULTED' && lastConsultedInfo){
                            CallControl.sstransfer(
                                lastConsultedInfo.called,
                                lastConsultedInfo.displaynum,
                                lastConsultedInfo.skillId,
                                lastConsultedInfo.callType,
                                lastConsultedInfo.userData
                            )
                            
                        }else{
                            customCallcontrol.agentSel('sstransfer');
                        }
                        break;
                     
                    //咨询
                    case 'consultationcall':
                        customCallcontrol.agentSel('consultationcall');
                        break;
                    //客户话路保持
                    case 'holdcall':
                        customCallcontrol.holdCall();
                        break;
                    //客户话路恢复
                    case 'unholdcall':
                        customCallcontrol.unholdCall();
                        break;
                    //三方通话
                    case 'conferencecall':
                        customCallcontrol.conferencecall();
                        break;
                    //监听
                    case 'listencall':
                        customCallcontrol.agentSel('listencall');
                        break;
                    //模式变更
                    case 'workMode':
                        customCallcontrol.workMode(t.data('type'));
                        break;
                    case 'stopMonitor':
                        CallControl.stopMonitor();
                        break;
                    case 'stopInvent':
                        CallControl.stopInvent();
                        break;
                    case 'startInvent':
                        CallControl.startInvent();
                        break;
                    // case 'startSecretlyTalk'://私语
                    //     CallControl.secretlyTalk();
                    //     break;
                    case 'stopSecretlyTalk'://结束私语
                        CallControl.stopSecretlyTalk();
                        break;
                    case 'interventcall':
                        CallControl.interventcall();
                        break;
                    case 'monitor':
                        if($('.ccbar-monitor-panel').length==0){
                            ccbar_monitor.init();
                            ccbar_monitor.open();
                        }else{
                            var flag = CallControl.getFunc('monitor')
                            if($("#ccbar_monitor_panel").is(":visible")){
                                ccbar_monitor.close();
                            }else{
                                ccbar_monitor.open();
                            }
                        }
                    break;

                    case 'mutecall':
                        CallControl.mute();
                        break;
                        
                };
            } else {
                ccbarDebugger('未定义的操作类型:'+type);
            }
        });

        //自定义绑定
        
        ccbarEvent.addEvent('logon', function(event) {
            if((typeof(event.state)!='undefined' && event.state!=1) || event.result == 'Fail' || event.result == 'fail'){
                tipsMsg(event.msg);
                return
            }
            ccbar_popup.close("#ccbarLogin");
            tipsMsg(ccbarI18nText('签入成功','content'));
        });
        ccbarEvent.addEvent('logoff', function(event) {
            callerphone = callerphoneNeedLogin;//改写话机事件
            tipsMsg(ccbarI18nText('已签出','content'));
        });
    };
    
    _ccbar_.initLayout = function(type) {
        var loginDIV = '<div id="ccbarLogin" class="ccbar_panel_login" style="display:none"><a href="javascript:void(0)" data-popup-close="(\'#ccbarLogin\')" class="ccbarbtn_close"></a><form class="ccbar-form"><div><div class="ccbar-form-input-group"><span class="input-group-addon">坐席工号</span><input type="text" data-ccbar-input="agentId" data-ccbarusername name="groupName" placeholder="请输入坐席工号..." value="" class="form-control"></div><div class="ccbar-form-input-group"><span class="input-group-addon">坐席密码</span><input type="password" data-ccbar-input="psw" data-ccbaruserpsw name="groupName" placeholder="请输入坐席登录密码..." value="" class="form-control"></div><div class="ccbar-form-input-group"><span class="input-group-addon">话机号码</span><input type="text" data-ccbar-input="dn" data-ccbaruserphone name="groupName" placeholder="请输入话机号码..." value="" class="form-control"></div><div style="text-align:center;padding-top:10px"><button type="button" style="width:120px" data-ccbartype="logon" data-placement="bottom" class="ccbar_btn_login">签入</button></div></div></form></div>';

        var dial = '<div id="ccbarCustomPhone" class="ccbar-phone ccbar-form" style="display:none;"><div class="ccbar-phone-panel ccbar-phone-panel-left"><a href="javascript:void(0)" data-popup-close="(\'#ccbarCustomPhone\')" class="ccbarbtn_close"></a><div class="ccbar-phone-panel-content"><div id="ccbar_agentSel" class="ccbar-form-input-group" style="display: table;padding: 0 35px;"><span class="input-group-addon">外显号码</span><select name="" id="ccbar_phoneCallerList" class="form-control"></select></div><div id="" class="ccbar-form-input-group" style="display: table;padding: 0 35px 10px;"><span class="input-group-addon">客户号码</span><input data-ccbartype="ccbarcallnum" placeholder="请输入号码..." autofocus="" id="_ccbar_callNumber" class="form-control"></div><ul class="numbers-container clearfix"><li><span data-pushnum="1" class="pusher">1</span></li><li><span data-pushnum="2" class="pusher">2</span></li><li><span data-pushnum="3" class="pusher">3</span></li><li><span data-pushnum="4" class="pusher">4</span></li><li><span data-pushnum="5" class="pusher">5</span></li><li><span data-pushnum="6" class="pusher">6</span></li><li><span data-pushnum="7" class="pusher">7</span></li><li><span data-pushnum="8" class="pusher">8</span></li><li><span data-pushnum="9" class="pusher">9</span></li><li><span data-pushnum="*" class="pusher">*</span></li><li><span data-pushnum="0" class="pusher">0</span></li><li><span data-pushnum="#" class="pusher">#</span></li></ul><div class="call-btn"><div href="javascript:void(0)" data-ccbartype="makecall" class="pusher-makecall" style="font-size:14px; width:40%">呼叫</div><div href="javascript:void(0)" data-ccbartype="makecall" data-makecall-type="video" class="pusher-makecall" style="font-size:14px; width:40%;margin-left:5px">视频呼叫</div></div></div></div></div>';

        var kefuBtn = '<li class="dropdown funcbtn"><a href="#" class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><span class="color-white text-xs block">更多 <b class="caret"></b></span></a><ul class="dropdown-menu dropdown-icon"><li class=""><a href="javascript:void(0)" class="disabled" data-ccbartype="mutecall">静音</a></li><li class=""><a href="javascript:void(0)" class="disabled" data-ccbartype="holdcall">保持</a></li><li class=""><a href="javascript:void(0)" class="disabled" data-ccbartype="unholdcall">恢复</a></li><li class=""><a href="javascript:void(0)" class="disabled" data-ccbartype="consultationcall">咨询</a></li><li class=""><a href="javascript:void(0)" class="disabled" data-ccbartype="conferencecall">三方通话</a></li><li class="" style="display:none"><a href="javascript:void(0)" class="disabled" data-ccbartype="listencall">监听</a></li><li class=""><a href="javascript:void(0)" class="disabled" data-ccbartype="sstransfer">呼叫转移</a></li></ul></li>';
        var extendBtn = '<li class="funcbtn"><a href="javascript:void(0)" class="disabled" data-ccbartype="workNotReady">进入话后处理</a></li><li class="funcbtn"><a href="javascript:void(0)" class="disabled" data-ccbartype="workReady">完成话后处理</a></li>';
        var iframe = "<iframe id='oprIfm' name='oprIfm' border='0' frameborder='0' width='0' height='0' style='display:none'></iframe>";
        var agentSel = '<div id="agentsel" class="ccbar_panel_login" style="display:none;height:inherit;"><a href="javascript:void(0)" onclick="agentSelHide()" class="ccbarbtn_close"></a><form action="javascript:;" class="ccbar-form"><input type="hidden" id="ccbar_hidden_sel_type" /><div><div id="ccbar-agentsel-list" class="ccbar-form-input-group"><span class="input-group-addon">外显号码</span><select name="" id="ccbar_select_agents_callers" class="form-control"></select></div><div class="ccbar-form-input-group"><span class="input-group-addon">类型</span><div class="form-control"><label for="trantype1" class="ccbar-label" style="display:none"><input type="radio" id="trantype1" name="trantype" value="1"><span>坐席</span></label><label for="trantype4" class="ccbar-label"><input type="radio" id="trantype4" checked name="trantype" value="4"><span>坐席</span></label><label for="trantype3" class="ccbar-label"><input type="radio" id="trantype3" name="trantype" value="3"><span>外线</span></label></div></div><div data-trantype="3" class="ccbar-form-input-group" style="display: none"><span class="input-group-addon">被叫号码</span><input type="text" id="ccbar_select_num" name="groupName" placeholder="请输入被叫号码..." value="" class="form-control"></div><div data-trantype="4" class="ccbar-form-input-group"><span class="input-group-addon">技能组</span><select name="ccbar_select_groups" id="ccbar_select_groups" class="form-control"></select></div><div class="ccbar-form-input-group" data-trantype="4"><span class="input-group-addon">坐席</span><select class="form-control" id="ccbar_select_groups_agent" name=""></select><span id="flushAgentList2" class="input-group-addon" style=" border-radius: 0 3px 3px 0;    cursor: pointer;border-right: 1px solid #ccc; border-left: 0;">刷新</span></div><div class="ccbar-form-input-group" data-trantype="1" style="display: none"><span class="input-group-addon">坐席</span><select class="form-control" id="ccbar_select_agents" name=""></select><span id="flushAgentList" class="input-group-addon" style=" border-radius: 0 3px 3px 0;    cursor: pointer;border-right: 1px solid #ccc; border-left: 0;">刷新</span></div><div class="ccbar-form-input-group" style="display:none"><textarea id="ccbarTranferText" maxlength="150" placeholder="请输入备注..." class="form-control" style="border-width-left:1px"></textarea></div></div><div style="text-align:center;padding-top:10px"><button type="button" style="width:120px" id="ccbar_agent_sel" data-placement="bottom" class="ccbar_btn_login">选择</button></div></form></div>';
        var answercallpanel = '<div id="ccbar_answercallpanel" class="ccbar_panel_login" style="display: none;height: auto;border-radius: 10px;"><div class="ccbar-form"><div class="ccbar-inbound-caller text-center" data-ccbaragentinfo="caller" style="font-size: 20px"></div>           <div class="ccbar-inbound-area text-center" style="margin-bottom: 10px;"><span id="createCauseArea" ></span>             <span id="createCauseName" ></span>         </div>          <div class="text-center" style="margin-bottom: 20px;">              <span data-ccbar-text="clock"></span>           </div>      </div>      <div style="text-align:center;margin-top:-15px;margin-bottom: 20px;">           <button type="button" style="width:100px" id="" data-ccbartype="answercall" data-placement="bottom" class="ccbar_btn_login answercall disabled">接听</button></div>   </div>';
        
        $('body').on('click','[data-popup-close]',function(e){
            var target = $(this).data('popup-Close')
            target = target.replace('(','')
            target = target.replace(')','')
            target = target.replace("'",'')
            target = target.replace("'",'')
            ccbar_popup.close(target)
        })

        var baseBtnJson = [];
        var kefuBtnJson  = [
            {
                name:'保持',
                icon:'holdcall',
                data:'holdcall',
                isHide:true

            },
            {
                name:'静音',
                icon:'holdcall',
                data:'mutecall',
                isHide:true

            },
            {
                name:'恢复',
                icon:'unholdcall',
                data:'unholdcall',
                isHide:true

            },
            {
                name:'咨询',
                icon:'consultationcall',
                data:'consultationcall',
                isHide:true

            },
            {
                name:'三方通话',
                icon:'conferencecall',
                data:'conferencecall',
                isHide:true

            },
            
            {
                name:'呼叫转移',
                icon:'sstransfer',
                data:'sstransfer',
                isHide:true

            },
            {
                name:'监控',
                icon:'monitor',
                data:'monitor',
                isHide:true
            },
            {
                name:'结束监听',
                icon:'clearcall',
                data:'stopMonitor',
                isHide:true
            },
            {
                name:'结束强插',
                icon:'clearcall',
                data:'stopInvent',
                isHide:true
            }

        ];

        var ccbar = '<div class="ccbar-mask"></div><div class="ccbar-system-mod"><div class="system-btns"><button data-ccbartype="logon" class="function-btn" data-toggle="ccbarlogon"><i class="func-btn-icon _ccbar_icon _ccbar_icon-agentready"></i><span class="func-btn-label">签入</span></button><button data-ccbartype="logoff" class="function-btn disabled"><i class="func-btn-icon _ccbar_icon _ccbar_icon-logoff"></i><span class="func-btn-label">签出</span></button><div class="call-info"><div id="_ccbar_clock" class="agent-clock" style="display:none"><div class="calltype"></div><div class="calldetail" style="float:left"><div><label>主叫：<span class="color-main" data-ccbaragentinfo="caller">-</span></label><label>被叫：<span class="color-main" data-ccbaragentinfo="called">-</span></label></div><div><label>计时：<span data-ccbar-text="clock" class="clocktext">00:00:00</span></label></div></div></div></div></div><div class="system-info"><div class="agent-info"><a href="javascript:toggleWorkMode1()" title="切换工作模式" class="ccbar-system-toggle"><span data-curworkmode=""></span></a>  坐席状态：<span data-agentinfo="curstatus" class="curstatus">未签入</span> <span data-ccbar-text="clock"></span></div><div class="agent-info"><div id="ccbarMonitorInfo" class="ccbr-login-input ccbar-monitor-box" style=""><div data-ccbar-info="groupMonitors"></div></div><span class="ccbar-queue-tag" data-ccbar-text="totalQueueCount" title="当前排队数" onclick="$(\'#ccbarMonitorInfo\').toggle()">排队[0]</span><label for="ccbar_autoanswercheck" class="ccbar_autoanswercheck ccbar-checkbox" style="display:none"><input type="checkbox" id="ccbar_autoanswercheck" /><i class="ccbar-checkbox-icon"></i>自动应答</label> <a title="话机" class="callerBtn" onclick="CallControl.wakeupSIP()"><i class=" _ccbar_icon _ccbar_icon-agent3"></i> 话机</a><span style="margin:0 10px">|</span><a title="下载" onclick="CallControl.downloadSIP()" class="callerBtn">下载</a></div></div></div>';
        var ccbarBase = '<button data-ccbartype="makecall" data-toggle="ccbarphone" class="function-btn disabled"><i class="func-btn-icon _ccbar_icon _ccbar_icon-huchu"></i><span class="func-btn-label">外呼</span></button><button data-ccbartype="answercall" class="function-btn disabled"><i class="func-btn-icon _ccbar_icon _ccbar_icon-answercall"></i><span class="func-btn-label">应答</span></button><button data-ccbartype="clearcall" class="function-btn disabled"><i class="func-btn-icon _ccbar_icon _ccbar_icon-clearcall"></i><span class="func-btn-label">挂机</span></button><button data-ccbartype="agentnotready" class="function-btn disabled"><i class="func-btn-icon _ccbar_icon _ccbar_icon-agentnotready"></i><span class="func-btn-label">置忙</span></button><button data-ccbartype="agentready" class="function-btn disabled"><i class="func-btn-icon _ccbar_icon _ccbar_icon-agent"></i><span class="func-btn-label">置闲</span></button><button data-ccbartype="workNotReady" class="function-btn funcbtn-hide disabled"><i class="func-btn-icon _ccbar_icon _ccbar_icon-wenjian"></i><span class="func-btn-label">进入话后整理</span></button><button data-ccbartype="workReady" class="function-btn funcbtn-hide disabled"><i class="func-btn-icon _ccbar_icon _ccbar_icon-wendang"></i><span class="func-btn-label">完成话后整理</span></button>';
        
        

        if(type!='undefined' && type!='false'){
            $("#ccbar").html('').append(ccbar);
            if($("#ccbar_answercallpanel").length == 0){
                $('body').append(answercallpanel);
                //自动应答
                $("#ccbar_autoanswercheck").on("change",function(){
                    var val = $(this).prop('checked');
                    changeAuto(val)
                });

                //自动应答
                $("#ccbar_transfercheck").on("change",function(){
                    var val = $(this).prop('checked');
                    CallControl.voiceTransferConfig(val)
                });
                
                function changeAuto(val){
                    CallControl.autoAnswer(val,function(result){
                        if(result.state){
                            $("#ccbar_autoanswercheck").prop('checked',val);
                            localStorage.setItem('isAutoAnswer',val);
                        }else{
                            $("#ccbar_autoanswercheck").prop('checked',!val);

                        }

                    });
                }
                /*振铃*/
                ccbarEvent.addEvent('evtAltering', function(event) {
                    if (event.event.createCause == 6 || event.event.createCause == 8) { return; }
                    if ($("#autoanswercall").prop('checked')) {
                        CallControl.answerCall();
                        console.log('平台自动应答')
                    } else {
                        $("#answercallpanel").fadeIn();
                        $("#createCauseName").text('');
                        $("#createCauseName").text(ccbar_config['createCause'][event.event.createCause]);
                        $("#createCauseArea").text('');
                        var areaName=event.event.areaInfo&&event.event.areaInfo.name?event.event.areaInfo.name+'  ':'';
                        $("#createCauseArea").text(areaName);
                        $("#createCauseCustPhone").text(replacePhoneNum(event.event.custPhone,event.event.displayCustPhone));
                    }
                });
                // 接通 
                ccbarEvent.addEvent('evtConnected', function(callEvent) {
                    $("#answercallpanel").fadeOut();
                });
                //挂机
                ccbarEvent.addEvent('evtDisConnected', function(callEvent) {
                    $("#answercallpanel").fadeOut();
                });

            }
            if(CallControl.ccbarMode!=1){
                 $("#ccbar .system-btns").append(ccbarBase);
            }

            if(type && type=="advanced"||type =='kefu'||CallControl.ccbarMode==3){
                var kefuBtnHtml = getBtnHtml(kefuBtnJson)
                $("#ccbar .system-btns").append(kefuBtnHtml);
            }
        }

        
        $("#ccbarLogin").remove();
        $("body").append(loginDIV+dial+iframe+agentSel);

        // $("#ccbar .funcbtn-group").append(extendBtn+kefuBtn);
        $("#ccbar .agentLogoff").addClass('disabled').removeAttr('style');
        
        $('.ccbar-form input').on("keyup",function(event){
            if(event.keyCode == "13") {//keyCode=13是回车键)
                event.preventDefault()
                $("#ccbarLogin .ccbar-form button").click();
            }
        });

        function getBtnHtml(list){
            var html= '';
            for (var i = 0; i < list.length; i++) {
                var extendBtnCls = list[i].isHide?'funcbtn-hide':'';
                html+='<button data-ccbartype="'+list[i].data+'" class="function-btn '+extendBtnCls+' disabled"><i class="func-btn-icon _ccbar_icon _ccbar_icon-'+list[i].icon+'"></i> <span class="func-btn-label">'+list[i].name+'</span></button>'
            }
            return html;
        }
        
    }
    _ccbar_.initPhone = function() {
        var numInput = $('#_ccbar_callNumber');
        $('#ccbarCustomPhone').on('click', '.del-btn', function() {
            var numbers = TrimAll(numInput.val(),'g');
            var numbers2 = numbers.length;
            numInput.val(numbers.substr(0, numbers2 - 1));
            document.getElementById("_ccbar_callNumber").focus();
        });

        $("#ccbarCustomPhone").on('click', '.pusher', function() {
            var n = $(this).data('pushnum');
            var numbers = TrimAll(numInput.val(),'g');
            numInput.val(numbers + n)
            
            document.getElementById("_ccbar_callNumber").focus();
        });

        numInput.on("keyup",function(event){
            if(event.keyCode == "13") {//keyCode=13是回车键)
                $("#ccbarCustomPhone").find(".pusher-makecall").click();
            }
        });

        
    }
    _ccbar_.initSDK = function(ip, port) {
        // 重写相关CTI事件
        CallControl.onEventLoginFromOther = onEventLoginFromOther;
        CallControl.onRespAgentState = onRecvAgentStateEvent;
        CallControl.onRespCallEvent = onRecvCallEvent;
        CallControl.onRespNotify = onRecvNotifyEvent;
        //启动sdk

        CallControl.start(ip, port);
        ccbarDebugger('SDK启动')
    };
    var isCCbarInit = false;
    _ccbar_.init = function(ip, port,type) {
        if(isCCbarInit) return;
        this.initLayout(type);
        this.initBind();
        this.initSDK(ip, port);
        //拨号盘初始化
        this.initPhone();
        //重写拉起事件
        callerphone=callerphoneNeedLogin;
        ccbar_plugin.clock.start();

        //添加mask
        CallControl.setting.beforeSend = function(){
            if(typeof(layer)!='undefined'){layer.load(1, {shade: [0.1,'#fff']})}else{
                $("#ccbar .ccbar-mask").fadeIn()
            }
        }
        CallControl.setting.complete = function(){
            if(typeof(layer)!='undefined'){layer.closeAll('loading');}else{
                $("#ccbar .ccbar-mask").fadeOut()
            }
        }
        isCCbarInit = true;
    };


    return _ccbar_;
});
//初始化
var ccbar_plugin = new ccbar();

/*SDK应用方法复写*/
//业务提示
function onAgentEvent(event){
    if(event.header.notifyContent){
        tipsMsg(event.header.notifyContent);
    }
}


Date.prototype.format = function(format) {
    var o = {
        "M+": this.getMonth() + 1, // month
        "d+": this.getDate(), // day
        "h+": this.getHours(), // hour
        "m+": this.getMinutes(), // minute
        "s+": this.getSeconds(), // second
        "q+": Math.floor((this.getMonth() + 3) / 3), // quarter
        "S": this.getMilliseconds() // millisecond
    };
    if (/(y+)/.test(format))
        format = format.replace(RegExp.$1, (this.getFullYear() + "")
            .substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(format))
            format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
    return format;
};

function jsonpCallback() {}


function TrimAll(str, is_global) { 
    var result;  
    result = str.replace(/[^\d*#]/g,'');
    return result;  
} 
function tipsMsg(msg,time) {
    time = time || 2000;
    ccbar_popup.msg(msg,time);
}

// 收到坐席相关事件
function onRecvAgentStateEvent(agentEvent){
	ccbarDebugger('坐席状态同步===>');
    //处理功能状态变化
    ccbar_plugin.agentStatusUpdate(agentEvent.state,agentEvent.stateDesc);
    ccbar_plugin.funcMaskUpdate(agentEvent.funcMask);

    $("[data-ccbartype='workMode']").removeClass("active");
    $("[data-ccbartype='workMode'][data-type='"+agentEvent.workMode+"']").addClass("active");

    var typeName = {
            'inbound':'呼入',
            'outbound':'呼出',
            'pdsbound':'智能外呼',
            'all' :'自动'
        }
    var workModeName = ccbarI18nText(typeName[agentEvent.workMode],'btn')
    $("[data-curworkmode]").text(workModeName);
    $("[data-curworkmode]").data('curworkmode',agentEvent.workMode);
    $("[data-curworkmode]").attr('data-curworkmode',agentEvent.workMode);

    ccbar_plugin.clock.reset();
    ccbar_plugin.clock.start();

    if(agentEvent.autoAnswer!=undefined){
        var flag = agentEvent.autoAnswer == 'true'?true:false;
        $("#ccbar_autoanswercheck").prop('checked',flag);
    }
    //状态变更时重置话后整理倒计时
    if(_workNotReadyTimeout) clearTimeout(_workNotReadyTimeout)

    if (agentEvent.state =='LOGOFF'){
     CallControl.isLogoned = false;
     $("[data-ccbartype='workMode']").removeClass("active");
       
    }else if(agentEvent.state == 'WORKNOTREADY'  && agentEvent.workReadyTimeout >0){
        workNotReadyTimeoutClock = Number(agentEvent.workReadyTimeout);
        workNotReadyTimeout();
    }

    //根据状态码进行提示
    if(agentEvent.notifyContent){
        var tips = ccbar_config['error'][agentEvent.notifyContent];
        tips = ccbarI18nText(agentEvent.notifyContent,'resultCode')
        if(tips== undefined){tips = '错误代码:'+agentEvent.notifyContent}
        if(agentEvent.notifyContent == '119'){
            ccbar_popup.alert(tips)
        }else{
            tipsMsg(tips)
        }
    }
}


//功能事件消息相关
var workNotReadyTimeoutClock = null,_workNotReadyTimeout= null,workNotReadyText = null;
function workNotReadyTimeout(){
    workNotReadyTimeoutClock --;
    var btn = $('[data-ccbartype=workReady]');
    if(!workNotReadyText){
        workNotReadyText = btn.html();
    }
    btn.html(workNotReadyText+'('+workNotReadyTimeoutClock+')')
    if(workNotReadyTimeoutClock>0) {
        _workNotReadyTimeout = setTimeout(workNotReadyTimeout,1000)
    }else{btn.html(workNotReadyText)}
}

// 收到与呼叫相关事件
function onRecvCallEvent(callEvent){
    ccbarDebugger("接收到呼叫事件====>");
    ccbarDebugger(callEvent);
    clearTimeout(ccbarCallClock);//有呼叫返回了,停止定时器
    

    function updateAgentInfoText(type, status) {
        var btn = $('[data-ccbaragentinfo=' + type + ']').text(status);
    }
    function getTrueNum(num){
        if (_bpoFlag) {
            var n = String(num);
            return n.replace(_bpoPrefix,'');
        }else{
            return num;
        }
    }
    if (callEvent.callEventId == 'evtAltering') {
        ccbarDebugger('callEvent.callEventId=' + callEvent.callEventId + ' 振铃事件');
        //修改呼叫信息
        var _caller = _bpoFlag?getTrueNum(callEvent.event.caller):callEvent.event.caller;
        var _called = _bpoFlag?getTrueNum(callEvent.event.called):callEvent.event.called;
        updateAgentInfoText('caller', _caller);
        updateAgentInfoText('called', _called);
        // updateAgentInfoText('custid', callEvent.custId);
        var num2 =  callEvent.event.createCause=='6'?callEvent.event.called:callEvent.event.caller;
        num2 = callEvent.event.displayCustPhone||callEvent.event.custPhone||num2;
        if(CallControl.config.hidePhoneNum){
          tipsMsg(ccbarI18nText('振铃中','content'));  
        }else{
          tipsMsg(ccbarI18nText('振铃中','content')+'：'+num2);  
        } 
        
        
    } else if (callEvent.callEventId == 'evtConnected') {
        ccbarDebugger('callEvent.callEventId=' + callEvent.callEventId + ' 开始通话');
        //修改呼叫信息
        var _caller = _bpoFlag?getTrueNum(callEvent.event.caller):callEvent.event.caller;
        var _called = _bpoFlag?getTrueNum(callEvent.event.called):callEvent.event.called;
        updateAgentInfoText('caller', _caller);
        updateAgentInfoText('called', _called);

        ccbar_status.callevent = callEvent; //缓存当前的callEvent
        ccbar_status.current.caller = _caller;
        ccbar_status.current.called = _called;
        ccbar_status.current.custPhone = callEvent.event.custPhone;
        ccbar_status.current.displayCustPhone = callEvent.event.displayCustPhone;
        ccbar_status.current.realCustPhone = callEvent.event.userData.custPhone;
        //智能外呼,调用进入话务状态
        // if(callEvent.event.createCause == '8'){
        //     ccbar_plugin.callControl.workNotReady()
        // }
    } else if (callEvent.callEventId == 'evtDisConnected') {
        ccbarDebugger('callEvent.callEventId=' + callEvent.callEventId + '   呼叫结束');
        agentSelHide();
        if(callEvent.clearCause && callEvent.clearCause!='0'){
            var clearCauseName = ccbar_config['clearCause'][callEvent.clearCause]||'['+callEvent.clearCause+']';
            tipsMsg(ccbarI18nText('呼叫失败','content')+'：'+clearCauseName)
        }
    }
}

var audio_player = {
	  audio:null,
      playTimeout:null,
      stopTimeout:null,
      inited:false,
      init:function(){
        if(audio_player.inited) return;

        audio_player.audio = document.createElement("audio");
        audio_player.audio.controls = false;
        audio_player.inited = true;
      },
      play:function(src,loop,timeout) { 
        timeout = timeout||60
        if(!audio_player.inited) {audio_player.init();return;}
        audio_player.stop();
        audio_player.audio.src = src;
        audio_player.audio.loop = loop ? true:false;

        audio_player.playTimeout && clearTimeout(audio_player.playTimeout)
        audio_player.stopTimeout && clearTimeout(audio_player.stopTimeout)
        audio_player.playTimeout = setTimeout(function(){
            try {

                audio_player.audio.play();
                audio_player.audio.addEventListener('ended', function () {  
                }, false);
            } catch (err) {
                console.error(err);                
            }
            
        },100)

        audio_player.stopTimeout = setTimeout(audio_player.stop,1000*timeout)
        
      },
      stop:function() {
        if(!audio_player.inited) return;
        audio_player.stopTimeout && clearTimeout(audio_player.stopTimeout)
        audio_player.playTimeout = setTimeout(function(){
            try {
               !audio_player.audio.paused? audio_player.audio.pause():'';
            } catch (err) {
                console.error(err);                
            }
        },100); 
        
      }
}

function onEventLoginFromOther(){}
//功能事件消息相关
function onRecvNotifyEvent(Notify){
    ccbarDebugger('消息响应===>');

    var respFuncList = {
        respLogin:'logon',
        respLogout:'logoff',
        respMakeCall:'makecall',
        respAnswerCall:'answercall',
        respClearCall:'clearcall',
        respReady:'setready',
        respNotReady:'setnotready',
        respWorkReady:'workReady',
        respWorkNotReady:'workNotReady',
    }
    if(Notify.result == 'Succ'){
        
        switch(Notify.srcMessageId){
            //签入
            case 'respLogin' : 
                //tipsMsg('签入成功');
                var t = $("#ccbarLogin [data-ccbartype='logon']");
                t.removeClass('disabled').text('签入');
                clearTimeout(ccbarCallClock);
             break;
            case 'respLogout' : CallControl.closeSIP();  break;
            case 'respTransferCall' : 
                if(Notify.notifyContent == '000'){
                    tipsMsg(ccbarI18nText('发起呼叫转移成功',content));
                }
                agentSelHide();
                break;
            case 'respConsultCall' : 
                if(Notify.notifyContent == '000'){
                    tipsMsg(ccbarI18nText('发起咨询成功',content));
                }
                agentSelHide(); 
                
                break;
            case 'respConferenceCall' : 
                if(Notify.notifyContent == '000'){
                    tipsMsg(ccbarI18nText('发起三方通话成功',content));
                }
                agentSelHide();
                break;
        }
        
    }else{
        switch(Notify.srcMessageId){
            //签入
            case 'respLogin' : 
                var t = $("#ccbarLogin [data-ccbartype='logon']");
                tipsMsg('签入失败：'+Notify.resultDesc);
                t.removeClass('disabled').text('签入');
                clearTimeout(ccbarCallClock);
             break;
             default :
                if(Notify.resultDesc!=null){
                    tipsMsg(Notify.resultDesc);
                }
        }
    }
}

function toggleWorkMode1(workMode){
    var workModeType = workMode||CallControl.workModeType;
    if(workModeType == 'inbound'){
        CallControl.workMode('outbound',function(result){
            if(result.state){
                CallControl.workModeType = 'outbound';
               tipsMsg(ccbarI18nText('切换到呼出模式',content));
                localStorage&&localStorage.setItem('rememberWorkMode','outbound');
            }else{
                tipsMsg(result.data.content)
            }
        });

    }else if (workModeType == 'all' ||workModeType == 'undefined'){
        CallControl.workMode('inbound',function(result){
            if(result.state){
             tipsMsg(ccbarI18nText('切换到呼入模式',content));
             CallControl.workModeType = 'inbound';
             localStorage&&localStorage.setItem('rememberWorkMode','inbound');
            }else{
                tipsMsg(result.data.content)
            }
        });
    }else if (workModeType == 'outbound'){
        CallControl.workMode('all',function(result){
            if(result.state){
             tipsMsg(ccbarI18nText('切换到自动模式',content));
             localStorage&&localStorage.setItem('rememberWorkMode','all');
            }else{
                tipsMsg(result.data.content)
            }
        });
    }
}

//添加自定义事件
ccbarEvent.addEvent('logoff', function(event) {
    if( event.result && event.result == 'Fail'){return}
    CallControl.isLogoned = false;
    $("[data-ccbartype='workMode']").removeClass("active");
    $('.ccbar_autoanswercheck').hide();

});
ccbarEvent.addEvent('logon', function(event) {
    if(event.result && event.result == 'Fail'){return}
        CallControl.isLogoned = true;
    $('.ccbar_autoanswercheck').show();
});
var ccbarConsultedInfo =null;
ccbarEvent.addEvent('evtConsultedBegin', function(event) {
    ccbarConsultedInfo = event;
});
ccbarEvent.addEvent('evtConsultedEnd', function(event) {
    ccbarConsultedInfo = null;
});

//排队相关
ccbarEvent.addEvent('monitor',handelMonitor);
function handelMonitor(event){
    $('[data-ccbar-text="totalQueueCount"]').text(ccbarI18nText('排队','content')+'['+event.totalQueueCount+']');
    var html = '';
    for (var i = 0; i < event.groupMonitors.length; i++) {
        var data = event.groupMonitors[i];
        html+= getTd(data);
    }
    var thead = '<tr><th data-type="skillGroupName" style="width: 100px">'+ccbarI18nText('技能组','content')+'</th><th data-type="logonAgentCount">'+ccbarI18nText('在线','content')+'</th><th data-type="idleAgentCount">'+ccbarI18nText('闲','content')+'</th><th data-type="busyAgentCount">'+ccbarI18nText('忙','content')+'</th><th data-type="talkAgentCount">'+ccbarI18nText('通话','content')+'</th><th data-type="alertAgentCount">'+ccbarI18nText('振铃','content')+'</th><th data-type="workNotReadyAgentCount">'+ccbarI18nText('话后','content')+'</th><th data-type="queueCallCount">'+ccbarI18nText('排队','content')+'</th><th data-type="aveQueueLen">'+ccbarI18nText('排队均长','content')+'</th></tr>'
    var table = '<table data-ccbar-info="groupMonitors" border="1px" class="monitor-table"><thead>'+thead+'</thead><tbody>'+html+'</tbody></table>'
    $('[data-ccbar-info="groupMonitors"]').html(table);
    
    function getTd(data){
        var html = '';
        var list = ['skillGroupName','logonAgentCount','idleAgentCount','busyAgentCount','talkAgentCount','alertAgentCount','workNotReadyAgentCount','queueCallCount','aveQueueLen'];
        for (var j = 0; j < list.length; j++) {
            if(list[j] == 'aveQueueLen'){
                data[list[j]] = parseInt(data[list[j]])+'s';
            }
           html+= '<td data-type="'+list[j]+'">'+data[list[j]]+'</td>';
        }
        return '<tr>'+html+'</tr>';
    }
}

//监控
var ccbar_monitor = {
    config:{
        '置闲':{color:''}
    },
    init:function(){
        var _self = this;

        var html = '<div id="ccbar_monitor_panel" class="ccbar-monitor-panel" style="display: none"><a href="javascript:void(0)" data-popup-close="(\'#ccbar_monitor_panel\')" class="ccbarbtn_close"></a><div class="ccbar-monitor-panel-header"><span>'+ccbarI18nText('语音坐席监控','content')+'</span><div class="tools ccbar-form" style="padding: 0;float: right"><div class="ccbar-form-input-group" style="display: table;width: 200px"><span class="input-group-addon">'+ccbarI18nText('技能组','content')+'</span><select name="ccbar_select_monitor_groups" id="ccbar_select_monitor_groups" class="form-control"><option value="">'+ccbarI18nText('请选择技能组','content')+'</option><</select></div></div></div><div class="ccbar-monitor-panel-content"><div class="ccbar-monitor-update-info"><span style="float: right">'+ccbarI18nText('更新时间','content')+':<span data-monitor-type="updateTime">----</span></span><label for="monitor_auto_update"><input type="checkbox" checked id="monitor_auto_update"><span>'+ccbarI18nText('每10秒自动刷新','content')+'</span></label><a id="ccbar_monitor_flush" href="javascript:;">'+ccbarI18nText('立即刷新','content')+'</a></div><div class="ccbar-monitor-skillGroup"><li class="border"><span class="monitor-tag busy">小休</span><span data-monitor-type="busyAgentCount" class="monitor-info">0</span></li><li class="border"><span class="monitor-tag idle">空闲</span><span data-monitor-type="idleAgentCount" class="monitor-info">0</span></li><li class="border"><span class="monitor-tag talk">通话中</span><span data-monitor-type="talkAgentCount" class="monitor-info">0</span></li><li class="border"><span class="monitor-tag workNotReady">话后整理</span><span data-monitor-type="workNotReadyAgentCount" class="monitor-info">0</span></li><li class="border"><span class="monitor-tag alter">振铃</span><span data-monitor-type="alertAgentCount" class="monitor-info">0</span></li><br><li>呼入空闲<span data-monitor-type="inboundIdleAgentCount" class="monitor-info">0</span></li><li>呼出空闲<span data-monitor-type="outboundIdleAgentCount" class="monitor-info">0</span></li><li>排队数<span data-monitor-type="queueCallCount" class="monitor-info">0</span></li><li>平均排队时长<span data-monitor-type="aveQueueLen" class="monitor-info">0</span></li><li>最大排队时长<span data-monitor-type="maxQueueLen" class="monitor-info">0</span></li></div><div class="ccbar-monitor-agents"><div class="monitor-agents"><ul id="monitor-agents"></ul></div></div></div></div>';
        $('body').append(html);
        //绑定
        $("#ccbar_select_monitor_groups").off("change").on("change",function(event){
            var skillGroupId = $(this).val();
            _self.getAgents(skillGroupId)
        });
        $("#ccbar_monitor_panel").on("click",".monitor-btn",function(){
            var type = $(this).data('type');
            var agentId = $(this).data('agentId');
            _self.handelClick(type,agentId);
        });

        $("#ccbar_monitor_flush").on("click",function(){
            var skillGroupId = $("#ccbar_select_monitor_groups").val();
            _self.getAgents(skillGroupId);
        });
        ccbarEvent.addEvent('logoff',_self.close);
        ccbarEvent.addEvent('logon',_self.getSkillGroups);

        _self.getSkillGroups();

        _intervalMonitor = setInterval(_self.interval,10000);
    },
    handelClick:function(type,agentId){
        switch(type){
            case 'makecall':
                if(CallControl.getFunc('makecall')){
                    ccbar_plugin.callControl.makeCall(agentId);
                }else{
                    top.layer && layer.alert(ccbarI18nText('外呼功能受限','content'));   
                }
            break;

            case 'startMonitor':
                if(CallControl.getFunc('startMonitor')){
                    CallControl.startMonitor(agentId,function(result){
                        if(result.data&&result.data=='fail'){
                            tipsMsg(result.msg);
                        }else{
                            tipsMsg(ccbarI18nText('发起监听','content'))
                        }
                    });

                }else{
                    tipsMsg(ccbarI18nText('监听功能受限','content'));
                }

            break;

            case 'startInvent':
                if(CallControl.getFunc('startInvent')){
                    CallControl.startInvent(agentId,function(result){
                        if(result.data&&result.data=='fail'){
                            tipsMsg(result.msg);
                        }else{
                            tipsMsg(ccbarI18nText('发起强插','content'))
                        }
                    });
                }else{
                    tipsMsg(ccbarI18nText('强插功能受限','content'));
                }

            break;

            case 'forcelogoff':
                if(CallControl.isLogoned){
                    CallControl.forceLogoff(agentId,function(result){
                        if(result.data&&result.data=='fail'){
                            tipsMsg(result.msg);
                        }else{
                            tipsMsg(ccbarI18nText('强制签出功能','content'))
                        }
                    });
                }else{
                    tipsMsg(ccbarI18nText('强制签出功能受限','content'));
                }

            break;
        }
    },
    getSkillGroups:function(){
        if(CallControl.getFunc('monitor')){
            CallControl.monitor.getSkillGroup(function(result){
                if(result.state){
                    var groups = result.data.result.groups;
                    var html = '<option value="">'+ccbarI18nText('请选择技能组','content')+'</option>'
                    for(var i in groups){
                        html += '<option value="'+groups[i].skillGroupId+'">'+groups[i].skillGroupName+'</option>'
                    }

                    $("#ccbar_select_monitor_groups").html(html);
                    $("#monitor-agents").html('')
                }else{
                        tipsMsg(result.msg);
                    }
            });
        }
    },
    getAgents:function(skillGroupId){
        if(skillGroupId == ''||!CallControl.isLogoned){
            $("#monitor-agents").html('');
            $(".ccbar-monitor-skillGroup .monitor-info").text(0)

        }else{
            CallControl.monitor.getAgents(skillGroupId,function(result){
                if(result.state){
                    // var aHtml = ccbar_template('monitor_agent',result.data.result.agents);
                    var agentsInfo = result.data.result.agents;
                    var html = '';
                    for (var i = 0; i < agentsInfo.length; i++) {
                        html+= '<li data-state="'+agentsInfo[i].agentState+'" class="monitor-agent-info"><div class="_agent-name">'+agentsInfo[i].agentName+'</div><div class="_agent-time">'+agentsInfo[i].workModel+'</div><div class="_agent-time">'+agentsInfo[i].stateTime+'</div><div class="monitor-agents-extend"><p>'+agentsInfo[i].agentName+'</p><p class="_agent-id">'+agentsInfo[i].agentId+'</p><ul class="monitor-agents-menu"><li class="monitor-btn" data-agent-id="'+agentsInfo[i].agentId+'" data-type="makecall" href="javascript:;">呼出</li><li class="monitor-btn" data-agent-id="'+agentsInfo[i].agentId+'" data-type="startMonitor" href="javascript:;">监听</li><li class="monitor-btn" data-agent-id="'+agentsInfo[i].agentId+'" data-type="startInvent" href="javascript:;">强插</li><li class="monitor-btn" data-agent-id="'+agentsInfo[i].agentId+'" data-type="forcelogoff" href="javascript:;">强制签出</li></ul></div></li>';
                    }

                    $("#monitor-agents").html(html);

                    var groupInfo = result.data.result.group;
                    for(var i in groupInfo){
                        $(".ccbar-monitor-panel [data-monitor-type="+i+"]").text(groupInfo[i]);
                    }
                }else{
                    tipsMsg(result.msg);
                }
                
            });
        }
    },
    interval:function(){
        var _self = this;
        if($("#ccbar_monitor_panel").length>0 && $("#ccbar_monitor_panel").is(":visible") && $("#monitor_auto_update").prop("checked")){
            var skillGroupId = $("#ccbar_select_monitor_groups").val();
            ccbar_monitor.getAgents(skillGroupId);
        }
    },
    open:function(){
        $("#ccbar_monitor_panel").fadeIn();
    },
    close:function(){
        $("#ccbar_monitor_panel").fadeOut();
    },

};

function replacePhoneNum(custPhone,displayCustPhone){
    if(String(custPhone).startWith('#')){
        return advReplace(displayCustPhone,3,4)
    }else{
        return displayCustPhone;
    }
}

var advReplace = function (text,start,length,placeText){
    if(text==null || text==undefined) return '';
    return replace(text,start,length,placeText);
    function replace(text,start,length,placeText){
        var newText = null;
        if(placeText==null || placeText==undefined) placeText = '*';
        text = text.toString();
        //截取处比文字长,直接返回
        if(Math.abs(start)>text.length) return text;
        //截取
        if(start>0){
            var textArr1 = text.substr(0,start);
            var end = start+length;
            //判断是起点+替换长度是否超过文字长度
            var longText = end>text.length?true:false; 
            var textArr2 = longText?"":text.substr(end);
            var replaceArrLength = longText? text.length-start : length;
            return textArr1 + new Array(replaceArrLength+1).join(placeText) + textArr2
        }else{
            var end = text.substr(start);
            var startlen =  text.length + start - length;//
            var replacelen = startlen>0?length:length + startlen;
            var startIndex = startlen>0?startlen:0;
            var startText = text.substr(0,startIndex);
            return startText + new Array(replacelen+1).join(placeText) + end
        }
    };
}

function numOrigin(num,createCause){
    //中国电信
    var dx = ['133','153','173','177','180','181','189','191','193','199'];
    //中国联通号段
    var lt = ['130','131','132','155','156','166','175','176','185','186','166']
    //中国移动号段
    var yd = ['134','135','136','137','138','139','147','150','151','152','157','158','159','172','178','182','183','184','187','188','198']
    //虚拟运营商
    var xn = ['170','171'];

    var test_yd = /^1(3[456789]|47|5[012789]|7[28]|8[23478]|98)\d{8}$/;
    var test_lt = /^1(3[012]|5[56]|66|7[56]|8[56])\d{8}$/;
    var test_dx = /^1(3[3]|5[3]|7[37]|8[019]|9[139])\d{8}$/;
    var text_xn = /^(170|171)\d{8}$/;
    var test_gh = /^(0\d{2,3}-\d{7,8})$/;
    if(new RegExp(test_yd).test(num)){return '中国移动'}
    else if(new RegExp(test_lt).test(num)){return '中国联通'}
    else if(new RegExp(test_dx).test(num)){return '中国电信'}
    else if(new RegExp(text_xn).test(num)){return '虚拟运营商'}
    else if(new RegExp(test_gh).test(num)){return '固话'}
    else if('14' == createCause ||'28'==createCause){return '坐席'}
    else{
        return '其他'
    }
    
}

var tranferCache = {};
function addTranferUserData(userData){
    tranferCache = userData || {};
    if(!CallControl.getFunc('sstransfer')){
        tipsMsg(ccbarI18nText('转移功能当前不可用','content'));
        return;
    }
    ccbar_plugin.callControl.agentSel('sstransfer');
}

function agentSelHide(){
    $('#agentsel').fadeOut();
    tranferCache = {}
    $('#ccbarTranferText').val('');
    $("#ccbar_select_groups_agent").html('');
}