<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>顶部</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/Vue/vue-count-to.min.js"></script>
    <style>
      [v-cloak] {
        display: none;
      }

      #top {
        width: 828px;
        height: 185px;
        padding: 20px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        background: linear-gradient(
          90deg,
          rgba(5, 36, 64, 0.5) 0%,
          rgba(5, 36, 64, 0.99) 45%,
          rgba(5, 36, 64, 0.49) 50%
        );
      }

      .line {
        width: 2px;
        height: 130px;
        background: linear-gradient(
          0deg,
          rgba(255, 255, 255, 0) 0%,
          rgba(211, 234, 255, 0.99) 48%,
          rgba(255, 255, 255, 0) 100%
        );
      }

      .item {
        width: 414px;
        height: 120px;
        text-align: center;
        line-height: 0px;
      }

      .title {
        color: #e3edff;
        font-size: 40px;
      }

      .number {
        display: inline-block;
        font-size: 45px;
      }

      .number .numbg {
        display: inline-block;
        width: 20px;
        height: 48px;
        font-weight: 700;
        line-height: 48px;
        text-align: center;
        margin: 0 4px;
        border-radius: 8px;
      }
    </style>
  </head>

  <body>
    <div id="top" v-cloak>
      <div class="item">
        <p class="title">管辖面积(平方千米)</p>
        <div
          class="number"
          v-for="(item, i) in num1"
          :key="i"
          style="color: #1dfcfe"
        >
          <span class="numbg" v-if="item!=',' && item!='.'">
            <count-to
              :start-val="0"
              :end-val="Number(item)"
              :duration="3000"
            ></count-to>
          </span>
          <span v-else>{{item}}</span>
        </div>
      </div>
      <div class="line"></div>
      <div class="item">
        <p class="title">实时人口(人)</p>
        <div
          class="number"
          v-for="(item, i) in num2"
          :key="i"
          style="color: #1dfcfe"
        >
          <span class="numbg" v-if="item!=',' && item!='.'">
            <count-to
              :start-val="0"
              :end-val="Number(item)"
              :duration="3000"
            ></count-to>
          </span>
          <span v-else>{{item}}</span>
        </div>
      </div>
    </div>
    <script type="module">
      import { setAct } from "/static/js/jslib/util.js";
      window.parent.eventbus &&
        window.parent.eventbus.on("cityChange", (city) => {
          if (city == "金华市") {
            vm.initApi("金华市");
          } else {
            let filtName =
              city == "金义新区"
                ? "金东区"
                : city == "金华开发区"
                ? "开发区"
                : city;
            vm.initApi(filtName);
          }
        });
      let vm = new Vue({
        el: "#top",
        data: {
          num1: "10,942",
          num2: "7,816,503",
          topname: [
            { name: "金华市", code: 330700 },
            { name: "婺城区", code: 330702 },
            { name: "金东区", code: 330703 },
            { name: "兰溪市", code: 330781 },
            { name: "东阳市", code: 330783 },
            { name: "义乌市", code: 330782 },
            { name: "永康市", code: 330784 },
            { name: "浦江县", code: 330726 },
            { name: "武义县", code: 330723 },
            { name: "磐安县", code: 330727 },
          ],
        },

        mounted() {
          this.initApi(localStorage.getItem("city"));
        },
        methods: {
          initApi(city) {
            $api("/csdn_yjyp11", { area_name: city }).then((res) => {
              this.num1 = setAct(res[0].area_num);
            });
            let code = this.topname.find((a) => a.name == city).code;
            if (code) {
              $api("/csrk_ssrsldrs", { addressCode: code }).then((res) => {
                this.num2 = setAct(Number(res[0].population_count));
              });
            } else {
              this.num2 = "00000";
            }
          },
        },
      });
    </script>
  </body>
</html>
