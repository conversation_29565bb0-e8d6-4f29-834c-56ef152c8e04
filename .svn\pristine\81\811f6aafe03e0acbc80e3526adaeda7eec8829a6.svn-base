<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>效果</title>

    <link
      rel="stylesheet"
      href="https://dev.arcgisonline.cn/jsapi/4.24/esri/themes/light/main.css"
    />
    <!-- <script src="./libs/three-r79.min.js"></script> -->
    <script src="./index.js" type="module"></script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        left: 50%;
        width: 50%;
        height: 200px;
        display: flex;
      }

      .tools span {
        cursor: pointer;
        background-color: blue;
        width: 150px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        color: white;
      }

      #weather-box {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv">
      <div class="tools">
        <span
          onclick="ArcGisUtils.createWeatherWidget(window.view,'weather-box')"
          >天气效果</span
        >
      </div>
      <div id="weather-box"></div>
    </div>
  </body>
</html>
