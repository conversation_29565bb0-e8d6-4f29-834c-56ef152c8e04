<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>动态墙体使用示例</title>

    <link
      rel="stylesheet"
      href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/base16/dracula.min.css"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/go.min.js"></script>
    <script src="./libs/three-r116.min.js"></script>
    <script src="./index.js" type="module"></script>
    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        left: 50%;
        width: 50%;
        height: 200px;
        display: flex;
      }

      .tools span {
        cursor: pointer;
        background-color: blue;
        width: 150px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        color: white;
      }

      #lineOfSight {
        width: 200px;
        height: 200px;
        position: absolute;
        bottom: 10px;
        right: 10px;
        z-index: 1;
      }

      .description {
        position: absolute;
        right: 10px;
        top: 10px;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv"></div>
    <div class="tools">
      <span onclick="add()">添加</span>
      <span onclick="remove()">移除</span>
    </div>
    <div class="description">
      使用：
      <pre><code class="language-javascript">
        const layer = ArcGisUtils.addDynamicWall({
          view,
          points: [
            [119.6242823104002, 29.05841639892346], // 坐标1
            [119.66836050390893, 29.063576778449313], // 坐标2
            [119.66998019509869, 29.04526670697448],
            [119.62461573696973, 29.03951982737351],
            [119.6242823104002, 29.05841639892346], 
          ],
          height: 1000,
        });
      </code></pre>
      移除
      <pre><code class="language-javascript">
        ArcGisUtils.removeDynamicWall();
      </code></pre>
    </div>
    <div id="lineOfSight"></div>
  </body>
  <script>
    function add() {
      const layer = ArcGisUtils.addDynamicWall({
        view,
        points: [
          [119.6242823104002, 29.05841639892346], // 坐标1
          [119.66836050390893, 29.063576778449313], // 坐标2
          [119.66998019509869, 29.04526670697448],
          [119.62461573696973, 29.03951982737351],
          [119.6242823104002, 29.05841639892346], 
        ],
        height: 1000, // 墙体高度
      });
    }
    function remove() {
      ArcGisUtils.removeDynamicWall();
    }
  </script>
  <script>
    hljs.highlightAll();
  </script>
</html>
