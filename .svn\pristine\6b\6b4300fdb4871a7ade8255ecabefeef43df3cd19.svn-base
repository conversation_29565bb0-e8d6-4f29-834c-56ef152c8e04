<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>指挥调度底部</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <style>
      [v-cloak] {
        display: none;
      }

      #bottom {
        width: 1760px;
        height: 525px;
        padding: 20px;
        box-sizing: border-box;
        background: url("/static/images/index/bottom-bg.png") no-repeat;
        background-size: 100% 100%;
        position: relative;
      }
      /* 表格 */
      .table {
        width: 100%;
        height: 500px;
        padding: 10px;
        box-sizing: border-box;
      }

      .table .th {
        width: 100%;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        font-weight: 700;
        font-size: 28px;
        line-height: 60px;
        color: #ffffff;
      }

      .table .th_td {
        letter-spacing: 0px;
        text-align: left;
      }

      .table .tbody {
        width: 100%;
        height: calc(100% - 80px);
        overflow: hidden;
      }

      .table .tbody:hover {
        overflow-y: auto;
      }

      .table .tbody::-webkit-scrollbar {
        width: 4px;
        height: 4px;
      }

      .table .tbody::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: #20aeff;
        height: 8px;
      }

      .table .tr {
        height: 75px;
        margin-top: 5px;
        cursor: pointer;
        background: url("/static/images/zhdd/bg.png") no-repeat;
        background-size: 100% 100%;
        /* padding: 15px 60px; */
        box-sizing: border-box;
        display: flex;
      }

      .table .tr:hover {
        background-color: #0074da75;
      }

      .table .tr_td {
        letter-spacing: 0px;
        text-align: left;
        box-sizing: border-box;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .table .tr_td > img {
        position: relative;
        top: 25px;
      }
      .table .text {
        font-size: 28px;
        width: 60%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #fff;
        text-align: left;
        line-height: 90px;
      }
      .table .time {
        width: 40%;
        font-size: 25px;
        font-weight: 400;
        color: #77b3f1;
        display: flex;
        justify-content: space-between;
        line-height: 90px;
      }
      .icon-sj {
        display: inline-block;
        background: url("/static/images/zhdd/shijian.png") no-repeat;
        width: 31px;
        height: 30px;
        margin-right: 10px;
        vertical-align: middle;
      }

      .icon-zy {
        display: inline-block;
        background: url("/static/images/zhdd/ziyuan.png") no-repeat;
        width: 31px;
        height: 30px;
        margin-right: 10px;
        vertical-align: middle;
      }
      .icon-dw {
        display: inline-block;
        background: url("/static/images/zhdd/dingwei.png") no-repeat;
        width: 31px;
        height: 30px;
        margin-right: 10px;
        vertical-align: text-top;
      }
      .buttons {
        width: 236px;
        height: 80px;
        background: url("/static/images/zhdd/btn.png");
        background-size: 100% 100%;
        line-height: 76px;
        color: #9fc9e9;
        cursor: pointer;
        font-size: 28px;
        text-align: center;
        position: absolute;
        top: 10px;
        right: 70px;
      }
      .tr_light {
        background-color: #0074da75 !important;
        border: 1px solid yellow !important;
        box-sizing: border-box;
      }
    </style>
  </head>
  <body>
    <div id="bottom" v-cloak>
      <div class="hearder_h1">
        <span>舆情中心</span>
        <div class="buttons" @click="openDdsj">调度事件</div>
      </div>
      <div class="table_box">
        <div class="table">
          <div
            class="tbody"
            id="box0"
            @mouseenter="mouseenterEvent"
            @mouseleave="mouseleaveEvent"
          >
            <div
              class="tr"
              v-for="(item ,i) in zfdt"
              @click="showYqzxDetail(item)"
              :class="item.isLight==1?'tr_light':''"
            >
              <div class="text" :style="{color:item.state==0?'yellow':''}">
                {{item.text}}
              </div>
              <div class="time" style="white-space: nowrap">
                <div>
                  <span class="icon-zy"></span
                  ><span>{{item.source || '腾讯网'}}</span>
                </div>
                <div>
                  <span class="icon-dw"></span
                  ><span
                    >{{item.dept_name.split(',')[0]&&item.dept_name.split(',')[0]||'-'}}</span
                  >
                  <span class="icon-sj" style="margin-left: 40px"></span
                  ><span>{{item.time}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      window.parent.eventbus &&
        window.parent.eventbus.on("cityChange", (city) => {
          let filtName = (vm.city =
            city == "金义新区"
              ? "金东区"
              : city == "金华开发区"
              ? "开发区"
              : city);
          vm.initApi(filtName);
        });
      var vm = new Vue({
        el: "#bottom",
        data: {
          city: "金华市",
          zfdt: [],
          qx: [
            { name: "金华市", code: "" },
            { name: "婺城区", code: 330702000000 },
            { name: "金东区", code: 330703000000 },
            { name: "开发区", code: 330751000000 },
            { name: "兰溪市", code: 330781000000 },
            { name: "义乌市", code: 330782000000 },
            { name: "东阳市", code: 330783000000 },
            { name: "永康市", code: 330784000000 },
            { name: "武义县", code: 330723000000 },
            { name: "浦江县", code: 330726000000 },
            { name: "磐安县", code: 330727000000 },
          ],
          dom1: null,
          time1: null,
          glid: null,
        },
        mounted() {
          this.initApi(localStorage.getItem("city"));
          // 表格滚动
          this.dom1 = document.getElementById("box0");
          this.mouseleaveEvent();
          let that = this;
          window.addEventListener("message", (e) => {
            if (e.data && e.data.glid) {
              that.glChange(e.data.glid);
            }
          });
        },
        methods: {
          initApi(city) {
            this.city = city;
            // let code = this.qx.find((a) => a.name == city).code;
            let this_ = this;
            $api("/xzzf_qylb", { qx: city == "金华市" ? "" : city }).then(
              (res) => {
                this_.zfdt = res;
              }
            );
            // axios({
            //   method: "get",
            //   url:
            //     baseURL.url + "/jhyjzh-server/screen_api/zhddzx/zhddzxLeft005",
            //   params: { name: "", qx: code },
            // }).then(function (data) {
            //   this_.zfdt = data.data.data.data;
            // });
          },
          mouseenterEvent() {
            clearInterval(this.time1);
          },
          mouseleaveEvent() {
            this.time1 = setInterval(() => {
              this.dom1.scrollBy({
                top: (0, 80),
                behavior: "smooth",
              });
              if (
                this.dom1.scrollTop >=
                this.dom1.scrollHeight - this.dom1.offsetHeight
              ) {
                this.dom1.scrollTop = 0;
              }
            }, 1500);
          },
          // 关联任务
          glChange(id) {
            let findI = this.zfdt.findIndex((a) => a.id == id);
            if (findI != -1) {
              if (this.glid != id) {
                this.glid = id;
                this.zfdt.forEach((item) => {
                  if (item.id == id) {
                    item.isLight = 1;
                  } else {
                    item.isLight = 0;
                  }
                });
                this.zfdt.sort(function (a, b) {
                  return b.isLight - a.isLight;
                });
                this.dom1.scrollTop = 0;
                this.mouseenterEvent();
              } else {
                this.glid = null;
                this.zfdt.forEach((item) => {
                  item.isLight = 0;
                });
                this.$forceUpdate();
                this.mouseleaveEvent();
              }
            } else {
              this.$message({
                message: "该任务未关联舆情信息",
                type: "warning",
              });
            }
          },
          //打开舆情中心详情
          showYqzxDetail(item) {
            if (item.state == 0) {
              window.parent.frames["zhdd_right"].postMessage(
                {
                  glid: item.id,
                },
                "*"
              );
            }
            window.parent.lay.openIframe({
              type: "openIframe",
              name: "yqxq",
              id: "yqxq",
              src: baseURL.url + "/static/citybrain/commonts/zhdd/yqxq.html",
              left: "1168px",
              top: "360px",
              width: "1509px",
              height: "1009px",
              zIndex: "130",
              argument: item,
            });
          },
          openDdsj() {
            axios({
              method: "get",
              url: baseURL.url + "/xzzfj-api/token/InfoAccount",
            }).then(function (res) {
              let token = res.data.msg;
              window.cookieStore.set("Admin-Token", token);
              window.open(
                "https://csdn.dsjj.jinhua.gov.cn:8303/direct/direction"
              );
            });
          },
        },
      });
    </script>
  </body>
</html>
