if (!poc.isWebRTCSupported || !poc.isWebAssemblySupported) {
  console.log("WebRTC or WebAssembly unsupported!");
} else {
  poc.ptt.init(function (err) {
    if (err != null) {
      if (err.name == "NotFoundError") {
        alert(err + ". PTT Listen Only!");
      } else {
        alert(
          err + ". POC PTT init has error! Some functions may not be available."
        );
      }
    }
    poc.ptt.setLog(true);
  });
}
var ywsVm = {
  personList: null,
  dataList: [],
  gVideoSession: 0,
  queryAuth() {
    console.log("yws");
    var addr = "https://************:4010/dm-http-interface";
    gUID = "3315210019";
    gPWD = "12qwQC--";
    poc.ptt.doLogin(addr, gUID, gPWD);
    poc.ptt.onLogin = function (result, secret) {
      if (result == 0) {
        //登陆成功,记住用户名密码
        document.cookie = "uid=" + gUID;
        document.cookie = "pwd=" + gPWD;
      }
    };
    // $get("/zhdd/yws_zfy_person").then((res) => {
    //   ywsVm.personList = res;
    // });
    var obj = {
      Url: "https://************:4482/station/mobile/serverapi.action",
      HostIp: "", // (传空字符串即可)
      CustomId: "POC-1551",
      CustomPwd: hex_md5("YTyt55!!"),
      Callback: function (res) {
        ywsVm.getSession(res);
      },
    };
    poc.data.auth(obj);
  },
  async getSession(e) {
    var obj = {
      ServiceCode: e.ServiceCode,
      DispatcherId: "3315210019",
      DispatcherPwd: hex_md5("12qwQC--"),
      LoginType: 0,
      Callback: function (res) {
        ywsVm.getUserList(res);
      },
    };
    poc.data.getSession(obj);
  },
  getUserList(e) {
    var obj = {
      SessionId: e.SessionId,
      Callback: function (res) {
          ywsVm.personList = res.Users;
          ywsVm.getLocation(e);
      },
    }
    poc.data.orgMemberAll(obj);
  },
  getLocation(e) {
    poc.ptt.onContactPresence = function (userList) {
      let ids = [];
      userList.length > 0 &&
        userList.forEach((item) => {
          ids.push(item.ipocid);
          let i = ywsVm.personList.findIndex((a) => a.Uid == item.ipocid);
          i != -1 && (ywsVm.personList[i].lineon = item.userstate == 2 ? 1 : 0);
        });
      var obj = {
        SessionId: e.SessionId,
        Uids: ids,
        Callback: function (res) {
            res.Locations.map((item1) => {
              ywsVm.personList.map((item2) => {
                if (item1.Uid == item2.Uid) {
                  Object.assign(item1, item2);
                  ywsVm.dataList.push(item1);
                }
              });
            });
            ywsVm.dataList.forEach((obj) => Object.assign(obj, { county: "义乌市" }));
            ywsVm.dataList = JSON.parse(
              JSON.stringify(ywsVm.dataList)
                .replace(/GpsLongitude/g, "lon")
                .replace(/GpsLatitude/g, "lat")
                .replace(/Uid/g, "sys_depart")
                .replace(/Time/g, "lasttime")
                .replace(/Type/g, "lineon")
                .replace(/Name/g, "name")
                .replace(/Phone/g, "phone")
                .replace(/Orgname/g, "dept_name")
            );
            console.log(ywsVm.dataList,"ywsList");
        },
      };
      poc.data.locationGet(obj);
    };
  },

  dialogBye() {
    var session = ywsVm.gVideoSession;
    poc.ptt.doLeaveCall(session);
    console.log("dialogBye: session=" + session);
  },
};
