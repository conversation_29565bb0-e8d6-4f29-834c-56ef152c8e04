<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>一局一屏</title>
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link rel="stylesheet" href="/static/css/animate.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/elementui/js/index.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
      }
      .container {
        position: relative;
        width: 3840px;
        height: 2160px;
        background: url("/static/images/yyjc/yyjc2_bg.png") no-repeat;
        background-size: cover;
        transform: translateX(0px);
      }
      .center {
        font-size: 223px;
        position: absolute;
        left: 1286px;
        top: 349px;
        width: 1285px;
        height: 1082px;
        background: url("/static/images/yyjc/yyjc2_cen.png") no-repeat;
        background-size: cover;
      }
      .center div > span {
        font-family: DINCondensed;
      }
      .box,
      .el-carousel--horizontal,
      .el-carousel__container {
        width: 100%;
        height: 88% !important;
      }
      .box {
        position: absolute;
        top: 390px;
      }
      .el-carousel {
      }
      .yyjc_left,
      .yyjc_right {
        max-width: 1050px;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
      }
      .yyjc_left {
        position: absolute;
        left: 120px;
      }
      .yyjc_right {
        position: absolute;
        right: 120px;
        top: 20px;
      }
      .yyjc_item {
        font-style: italic;
        width: 450px;
        height: 400px;
        text-align: center;
        font-size: 32px;
        margin: 30px;
        color: #fff;
      }
      .el-carousel__indicator .el-carousel__button {
        width: 80px;
        height: 20px;
        background: #36bfff;
        border-radius: 10px;
      }
      .el-carousel__indicator--horizontal {
        padding: 12px;
      }
      .el-carousel__indicators--outside button {
        background-color: #36bfff;
      }
      /* 下拉 */
      .el-input__inner {
        height: 80px;
        background-color: #132c4e;
        border: 1px solid #afdcfb;
        color: #fff;
        border-radius: 10px;
      }
      .el-select-dropdown__item {
        font-size: 30px;
        height: 50px;
        color: #cfcfd6 !important;
        line-height: 50px;
      }
      .el-input {
        width: 300px;
        font-size: 30px;
      }
      .el-select-dropdown {
        background-color: #132c4e;
        border: 1px solid #afdcfb;
      }
      .el-select-dropdown__item.hover,
      .el-select-dropdown__item:hover {
        background-color: #27508f !important;
      }
      .el-carousel__button {
        width: 10px;
        height: 10px;
        border-radius: 50%;
      }
      .el-carousel__arrow {
        width: 50px;
        height: 50px;
        font-size: 30px;
        background-color: rgba(31, 45, 61, 0.5);
      }
      .citySel {
        position: absolute;
        top: 246px;
        left: 184px;
      }
      .departmentSel {
        position: absolute;
        top: 246px;
        left: 534px;
      }
    </style>
  </head>
  <body>
    <div class="container" id="bmjr-main">
      <div class="center">
        <div
          class="s-flex-col s-col-center"
          style="margin-top: 325px; line-height: 170px"
        >
          <span class="s-w7 s-c-blue-gradient">{{total}}</span>
          <span class="s-w7 s-font-80 s-c-white">总应用数</span>
        </div>
      </div>
      <div class="box">
        <el-carousel
          indicator-position="outside"
          height="700px"
          :autoplay="false"
        >
          <el-carousel-item v-for="(el,i) in newYyjcList" :key="i">
            <div class="yyjc_left">
              <div
                class="yyjc_item"
                v-for="(item,index) in newYyjcList[i].slice(0,6)"
                @click="openWebWin(item)"
              >
                <img
                :src="item.img"
                alt="" width="379px" height="213px" />
                <div class="yyjc_item_title">{{item.name}}</div>
              </div>
            </div>
            <div class="yyjc_right">
              <div
                class="yyjc_item"
                v-for="(item,index) in newYyjcList[i].slice(6)"
                @click="openWebWin(item)"
              >
                <img
                :src="item.img"
                alt="" width="379px" height="213px" />
                <div class="yyjc_item_title">{{item.name}}</div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>

    <script>
      new Vue({
        el: "#bmjr-main",
        data: {
          total: null,
          yyjcList: [],
          city: localStorage.getItem('adminCity'),
          departmentVal: "全部门",
          department: [
            { value: "1", label: "全部门" },
            { value: "2", label: "办公室" },
          ],
        },
        computed: {
          newYyjcList() {
            let newArr = [];
            for (let i = 0; i < this.yyjcList.length; i += 12) {
              newArr.push(this.yyjcList.slice(i, i + 12));
            }
            return newArr;
          },
        },
        mounted() {
          top.xzzfzx.cityList = localStorage.getItem("city") == "开发区"?top.xzzfzx.cityList = [{ name: "金华开发区", code: 330751 }]:top.xzzfzx.cityList.filter(item => item.name == localStorage.getItem("city"))
          $api("/xzzfj_yyji_list", { area: localStorage.getItem("city") }).then(res => {
            console.log(res);
            this.total = res.length
            this.yyjcList = res.map(item => {return {
              name: item.name,
              url: item.url,
              city: item.area,
              img: item.picture_url
            }})
          })
        },
        methods: {
          openWebWin(item) {
            let that = this;
            let name = item.name;
            let url = item.url;
            if (url == "") {
              this.$message.error("暂无该大屏信息");
              return false;
            }

            if (url == "currentUrlProject") {
              $api2Post("/xzzfj/mdUser/getUrl?area="+item.city).then(res => {
                top.window.location = res.data.data.url
              })
            } else if (name == "金华永康市永城数治系统") {
              // 金华永康市永城数治系统
              $.ajax({
                type: "get",
                url:
                  baseURL.url +
                  baseURL.admApi +
                  "/mis/system/linkdetails/tempAuthCode?appId=330700_tsapp_056&userId=" +
                  sessionStorage.getItem("role"),
                dataType: "json",
                headers: {
                  Authorization: sessionStorage
                    .getItem("Authorization")
                    .replaceAll('"', ""),
                },
                success: function (res) {
                  if (res.code === 200) {
                    that.openHtmlByMode(
                      url + "&csdnCode=" + res.tempAuthCode,
                      3840,
                      2160
                    );
                  } else {
                    this.$message.error("暂无权限");
                  }
                },
              });
            } else {
              let type = url.substring(0, 5).indexOf("s") != -1 ? true : false;
              // if (type) {
              //   window.parent.lay.openWinHtml("3840", "2160", url);
              // } else {
              that.openHtmlByMode(url, 3840, 2160);
              // }
            }
          },
          openHtmlByMode(url, width, higth) {
            let moveLeft = (7680 - width) / 2;
            let moveHigth = (2160 - higth) / 2;
            window.open(
              url,
              "项目接入系统",
              "directories=no, location=no, toolbar=no,scrollbars=yes, resizable=yes, height=" +
                higth +
                ", width=" +
                width +
                ", top=" +
                moveHigth +
                ", left=" +
                moveLeft +
                ""
            );
          },
        },
      });
    </script>
  </body>
</html>
