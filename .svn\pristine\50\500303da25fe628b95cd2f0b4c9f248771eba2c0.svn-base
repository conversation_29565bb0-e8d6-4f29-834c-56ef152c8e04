<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>左</title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/css/viewCss/index.css" />
  <link rel="stylesheet" href="/static/css/viewCss/zhdd_left.css" />
  <script src="/Vue/vue.js"></script>
  <script src="/jquery/jquery-3.6.1.min.js"></script>
  <script src="/echarts/echarts.min.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/Vue/vue-count-to.min.js"></script>
  <link rel="stylesheet" href="/elementui/css/index.css" />
  <link rel="stylesheet" href="/elementui/css/index.css" />
  <script src="/elementui/js/index.js"></script>
  <script src="/static/js/jslib/cookie.js"></script>
  <style>
      .iconPhone::after {
          background: url(/static/images/zhdd/icon_call.png) no-repeat;
          background-size: cover;
      }

      .iconVideo::after {
          background: url(/static/images/zhdd/video.png) no-repeat;
          background-size: cover;
      }
      .zhtx {
          width: 100%;
          padding: 0 20px;
          box-sizing: border-box;
          height: 505px;
          overflow: hidden;
      }
      /* 表格 */
      .table {
          width: 100%;
          height: 100%;
          padding: 10px;
          box-sizing: border-box;
      }

      .table .th {
          width: 100%;
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          font-weight: 700;
          font-size: 28px;
          line-height: 60px;
          color: #ffffff;
      }

      .table .th_td {
          letter-spacing: 0px;
          text-align: left;
      }

      .table .tbody {
          width: 100%;
          height: calc(100% - 80px);
          overflow: hidden;
      }

      .table .tbody:hover {
          overflow-y: auto;
      }

      .table .tbody::-webkit-scrollbar {
          width: 4px;
          height: 4px;
      }

      .table .tbody::-webkit-scrollbar-thumb {
          border-radius: 10px;
          background: #20aeff;
          height: 8px;
      }

      .table .tr {
          display: flex;
          justify-content: space-evenly;
          align-items: center;
          height: 88px;
          line-height: 88px;
          padding-top: 0px;
          font-size: 28px;
          color: #ffffff;
          cursor: pointer;
          border-top: 1px solid #959aa1;
          border-image: linear-gradient(to right, #e9f5ff3b, #f5ffffd4, #e9f5ff3b)
          1;
          box-sizing: border-box;
      }

      .table .tr:nth-child(2n) {
          background: rgba(50, 134, 248, 0.2);
      }

      .table .tr:nth-child(2n + 1) {
          background: rgba(50, 134, 248, 0.12);
      }

      .table .tr:hover {
          background-color: #0074da75;
      }

      .table .tr_td {
          letter-spacing: 0px;
          text-align: left;
          box-sizing: border-box;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
      }

      .table .tr_td > img {
          position: relative;
          top: 25px;
      }
      .tabChange {
          display: flex;
          font-size: 30px;
          position: absolute;
          top: 30px;
          left: 450px;
          color: #fff;
      }

      .tabItem {
          cursor: pointer;
          margin-left: 40px;
          padding-bottom: 5px;
          border-bottom: 4px solid transparent;
          box-sizing: border-box;
          white-space: nowrap;
      }

      .tabActive {
          color: #e6c804;
          border-bottom-color: #e6c804;
      }

      .tabIndex {
          width: 250px !important;
      }
      .tit1 {
          flex: 0.85;
          margin-right: 10%;
          background: url("/static/images/zhdd/tit1_bg.png") no-repeat;
          /* background-position: top; */
          background-position: -100px 25px;
          font-size: 32px;
          color: #fff;
          display: flex;
          justify-content: space-between;
          line-height: 70px;
          /* line-height: 115px; */
          height: 162px;
          text-align: center;
          padding-top: 20px;
      }
      .header3 {
          font-size: 40px;
          margin: -35px 0 0 20px;
          font-weight: 700;
          font-style: italic;
          color: #d1d6df;
          background: linear-gradient(
                  180deg,
                  rgba(14, 197, 236, 1) 0%,
                  rgba(49, 190, 255, 1) 0%,
                  rgba(239, 252, 254, 1) 58.7646484375%
          );
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
      } /* 下拉 */
      .el-input__inner {
          height: 60px;
          background-color: #132c4e;
          border: 1px solid #afdcfb;
          color: #fff;
          border-radius: 10px;
      }
      .el-select-dropdown__item {
          font-size: 30px;
          height: 50px;
          color: #cfcfd6 !important;
          line-height: 50px;
      }
      .el-input {
          width: 220px;
          font-size: 30px;
      }
      .el-select-dropdown {
          background-color: #132c4e;
          border: 1px solid #afdcfb;
      }
      .el-select-dropdown__item.hover,
      .el-select-dropdown__item:hover {
          background-color: #27508f !important;
      }
      .el-carousel__button {
          width: 10px;
          height: 10px;
          border-radius: 50%;
      }
      .el-carousel__arrow {
          width: 50px;
          height: 50px;
          font-size: 30px;
          background-color: rgba(31, 45, 61, 0.5);
      }
      .citySel {
          position: absolute;
          top: 1250px;
          left: 720px;
      }

      .zfll-box {
        display: flex;
          justify-content: space-around;
          flex-wrap: wrap;
          height: 570px;
          margin-top: 20px;
      }

      .mleft {
          margin-left: 419px;
      }

      .zfll-box-item {
          width: 209px;
          height: 216px;
          background-size: cover;
          background: url("../../../images/zhdd/zfllBase.png");
          cursor: pointer;
      }

      .zfll-box-center {
          position: absolute;
          top: 1440px;
          width: 407px;
          height: 338px;
          background-size: cover;
          background: url("../../../images/zhdd/zfllCenter.png");
          text-align: center;
      }

      .zfll-box-item-num {
          text-align: center;
          font-size: 72px;
          font-family: DINCond-Bold;
          font-weight: 400;
          color: #3CFDFF;
          display: flex;
          justify-content: center;
          align-items: center;
      }

      .zfll-box-item-name {
          text-align: center;
          font-size: 36px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #F3FFFF;
      }

      .unit {
          font-size: 36px;
          margin: 20px 0 0 10px;
      }

      .tooltip {
          font-size: 30px;
      }
  </style>
</head>

<body>
<div id="left">
  <div class="hearder_h1 cursor">
        <span @click="openIfram()">
          指挥体系<i class="click-i"></i>
        </span>
    <img src="../../../images/common/edit.png" alt="" style="cursor: pointer" @click="openManage(1)">
  </div>
  <div class="tabChange">
    <div
      class="tabItem"
      v-for="(item,index) in tab"
      :class="{tabActive:activeIndex===index}"
      @click="changeTab(item,index)"
    >
      {{item}}
    </div>
  </div>
  <div class="zhtx">
    <div class="table">
      <div class="th">
        <div class="th_td" style="flex: 0.25; text-align: center">{{activeItem == '乡镇街道指挥中心'?"乡镇街道":"部门"}}</div>
        <div class="th_td" style="flex: 0.15; text-align: center">职务</div>
        <div class="th_td" style="flex: 0.15; text-align: center">姓名</div>
        <div class="th_td" style="flex: 0.4; text-align: center">
          联系方式
        </div>
      </div>
      <div
        class="tbody"
        id="box2"
        @mouseenter="mouseenterEvent2"
        @mouseleave="mouseleaveEvent2"
      >
        <div v-show="activeItem == '市直部门'">
          <div class="tr" v-for="(item,index) in szbmTable" :key="index">
            <div
              class="tr_td"
              style="flex: 0.25; text-align: center"
              :title="item.bm"
            >
              {{item.bm}}
            </div>
            <div class="tr_td" style="flex: 0.15; text-align: center">
              {{item.zw}}
            </div>
            <div class="tr_td" style="flex: 0.15; text-align: center">
              {{item.name}}
            </div>
            <div class="tr_td" style="flex: 0.4; text-align: center">
              <div
                style="
                    display: flex;
                    justify-content: space-around;
                    align-items: center;
                  "
              >
                <div>{{toPhone(item.phone)}}</div>
                <span class="iconPhone" @click="openCall(item.phone)"></span>
                <span class="iconVideo" @click="openVideo(item.phone)"></span>
              </div>
            </div>
          </div>
        </div>
        <div v-show="activeItem == '县市区指挥中心'">
          <div class="tr" v-for="(item,index) in xsqTableData" :key="index">
            <div
              class="tr_td"
              style="flex: 0.25; text-align: center"
              :title="item.bm"
            >
              {{item.bm}}
            </div>
            <div class="tr_td" style="flex: 0.15; text-align: center">
              {{item.zw}}
            </div>
            <div class="tr_td" style="flex: 0.15; text-align: center">
              {{item.name}}
            </div>
            <div class="tr_td" style="flex: 0.4; text-align: center">
              <div
                style="
                    display: flex;
                    justify-content: space-around;
                    align-items: center;
                  "
              >
                <div>{{toPhone(item.phone)}}</div>
                <span class="iconPhone" @click="openCall(item.phone)"></span>
                <span class="iconVideo" @click="openVideo(item.phone)"></span>
              </div>
            </div>
          </div>
        </div>
        <div v-show="activeItem == '乡镇街道指挥中心'">
          <div class="tr" v-for="(item,index) in xzjdTableData" :key="index">
            <div
              class="tr_td"
              style="flex: 0.25; text-align: center"
              :title="item.bm"
            >
              {{item.bm}}
            </div>
            <div class="tr_td" style="flex: 0.15; text-align: center">
              {{item.zw}}
            </div>
            <div class="tr_td" style="flex: 0.15; text-align: center">
              {{item.name}}
            </div>
            <div class="tr_td" style="flex: 0.4; text-align: center">
              <div
                style="
                    display: flex;
                    justify-content: space-around;
                    align-items: center;
                  "
              >
                <div>{{toPhone(item.phone)}}</div>
                <span class="iconPhone" @click="openCall(item.phone)"></span>
                <span class="iconVideo" @click="openVideo(item.phone)"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="hearder_h1">
    <span>执法设备</span>
    <img src="../../../images/common/edit.png" alt="" style="cursor: pointer" @click="openManage(2)">
  </div>
  <div
    class="s-flex s-c-grey-light s-m-b-20 s-m-t-20 s-m-l-10"
    style="width: 100%; position: relative"
  >
    <div class="s-flex s-flex-1">
      <div style="line-height: 24px; margin-top: -50px">
        <img src="/static/images/zhdd/left_car.png" alt="" />
        <p class="font-b s-font-60 font-italic">
          <span class="yellow">{{jdcData.numjd}}</span>/{{fjdcData.numfjd}}
        </p>
      </div>
      <div style="width: 320px; margin-left: 30px;cursor: pointer">
        <p class="tit_data s-font-35" @click="showZfsbDialog(jdcData)">{{jdcData.tit}}<img src="../../../images/common/sbDetail.png" alt="" style="margin-left: 10px"></p>
        <p class="s-flex s-row-between s-font-30 s-m-t-15">
              <span v-for="item in jdcData.son" @click="showZfsbDialog(item)">
                {{item.tit}}<br />
                <span class="green s-font-60 font-b">{{item.num}}</span>
              </span>
        </p>
      </div>
    </div>
    <i class="line" style="position: absolute; left: 50%"></i>
    <div style="width: 440px;cursor: pointer">
      <p class="tit_data s-font-35" @click="showZfsbDialog(fjdcData)">{{fjdcData.tit}}<img src="../../../images/common/sbDetail.png" alt="" style="margin-left: 10px"></p>
      <p class="s-flex s-row-between s-font-30 s-p-r-55 s-m-t-15">
            <span v-for="item in fjdcData.son" @click="showZfsbDialog(item)">
              {{item.tit}}<br />
              <span class="green s-font-60 font-b">{{item.num}}</span>
            </span>
      </p>
    </div>
  </div>
  <div class="s-flex s-flex-wrap s-m-l-10 s-row-between">
    <div class="s-flex" v-for="(item,index) in zfsbList" @click="showZfsbDialog(item)">
      <img :src="`/static/images/zhdd/zfsb${index+1}.png`" alt="" />
      <div class="s-c-grey-light s-m-l-30" style="width: 300px;cursor: pointer">
<!--        <p-->
<!--          class="tit_data s-font-35"-->
<!--          @click="openWRJ(item)"-->
<!--          :style="{cursor: item.tit=='无人机'?'pointer':'no-repeat'}"-->
<!--        >-->
<!--          {{item.tit}}-->
<!--        </p>-->
        <p
          class="tit_data s-font-35"
          :style="{cursor: item.tit=='无人机'?'pointer':'no-repeat'}"
        >
          {{item.tit}}
          <img src="../../../images/common/sbDetail.png" alt="">
        </p>

        <p class="font-b s-font-60 font-italic">
          <span class="yellow">{{item.num1}}</span>/{{item.num}}
        </p>
      </div>
    </div>
  </div>
  <div class="hearder_h1" style="margin-top: 20px">
    <span>执法力量</span>
  </div>
  <div class="zfll-box">
        <div v-for="(item,i) in zfll" :key="i" class="zfll-box-item" :class="{mleft:i % 2 != 0}" @click="item.name != '其他'?showDialog(item.name):false">
          <el-tooltip class="item" effect="dark" :content="item.info" placement="top-start" popper-class="tooltip">-->
            <div class="zfll-box-item-num">{{item.num}} <div class="unit">{{item.unit}}</div> </div>
          </el-tooltip>
          <div class="zfll-box-item-name">{{item.name}}</div>
        </div>
      <div class="zfll-box-center"></div>
  </div>
<!--  <div>-->
<!--    <el-select class="citySel" v-model="typeValue" @change="typeChange">-->
<!--      <el-option-->
<!--        v-for="item in typeOptions"-->
<!--        :key="item"-->
<!--        :label="item"-->
<!--        :value="item"-->
<!--      >-->
<!--      </el-option>-->
<!--    </el-select>-->
<!--    <div class="s-flex s-row-around">-->
<!--      <img src="/static/images/zhdd/yybz.png" alt="" />-->
<!--      <div class="tit1">-->
<!--        <p-->
<!--          v-for="item in yybz"-->
<!--          style="display: flex; flex-direction: column"-->
<!--        >-->
<!--          <span>{{item.tit}}</span>-->
<!--          &lt;!&ndash; <br /> &ndash;&gt;-->
<!--          <span class="green font-b s-font-60">-->
<!--                {{item.num==''?'/':item.num}}-->
<!--                <span class="s-font-30">{{item.unit=='%'?item.unit:''}}</span>-->
<!--              </span>-->
<!--        </p>-->
<!--      </div>-->
<!--    </div>-->
<!--    <h3 class="header3">高发事项TOP5</h3>-->
<!--    <div id="yybzEc" style="width: 100%; height: 350px"></div>-->
<!--  </div>-->




  <!-- <div class="weater_box">
    <div class="s-flex">
      <span class="s-font-60 font-b s-c-grey-light">
        {{nowTq.wd}}
        <span class="s-font-30">℃</span>
      </span>
      <span class="s-m-l-30 s-m-r-30 s-c-grey-light s-font-30">
        <img
          :src="`/static/images/weather/${nowTq.img}.png`"
          style="vertical-align: bottom"
          width="45px"
        />
        {{nowTq.tq}}
      </span>
      <span class="bg-zl">{{nowTq.kq}} {{nowTq.kqzl}}</span>
    </div>
    <div class="s-flex s-row-between s-m-b-30" style="width: 98%">
      <div class="tq_item" v-for="(item,i) in noewKq">
        <img :src="`${item.img}`" width="40px" alt="" />
        <span class="s-font-28 s-c-white">{{item.val}}</span>
        <span class="s-font-24" style="color: #b8d3f1">{{item.name}}</span>
      </div>
    </div>
    <el-carousel
      indicator-position="outside"
      :autoplay="false"
      style="width: 95%; margin: 0 auto"
    >
      <el-carousel-item v-for="(el,i) in newnowSjtq" :key="i">
        <div class="yyjc s-flex s-row-evenly">
          <div class="flex_column" v-for="(item,index) in newnowSjtq[i]">
            <img
              :src="`/static/images/weather/${item.img}.png`"
              width="45px"
            />
            <span
              v-if="item.sj!=new Date().getHours()"
              class="s-font-30 s-c-grey-light"
              >{{item.sj}}时</span
            >
            <span
              v-if="item.sj==new Date().getHours()"
              style="color: #d6bd4e; font-size: 32px"
            >
              现在
            </span>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div> -->
</div>
<script>
  window.parent.eventbus &&
  window.parent.eventbus.on("cityChange", (city) => {
    let filtName = (vm.city =
      city == "金义新区"
        ? "金东区"
        : city == "金华开发区"
          ? "开发区"
          : city);
    vm.initApi(filtName);
  });
  var vm = new Vue({
    el: "#left",
    data: {
      typeValue: "全年",
      typeOptions: ["全年", "本月", "上月"],
      city: "金华市",
      zfdt: [],
      qx: [
        { name: "金华市", code: 330703000000 },
        { name: "婺城区", code: 330702000000 },
        { name: "金东区", code: 330703000000 },
        { name: "开发区", code: 330751000000 },
        { name: "兰溪市", code: 330781000000 },
        { name: "义乌市", code: 330782000000 },
        { name: "东阳市", code: 330783000000 },
        { name: "永康市", code: 330784000000 },
        { name: "武义县", code: 330723000000 },
        { name: "浦江县", code: 330726000000 },
        { name: "磐安县", code: 330727000000 },
      ],
      jdcData: {
        tit: "机动车辆",
        numjd: "",
        son: [
          { tit: "汽车", num: 40 },
          { tit: "摩托车", num: 60 },
        ],
      },
      fjdcData: {
        tit: "非机动车辆",
        numfjd: "",
        son: [
          { tit: "四轮电瓶车", num: 40 },
          { tit: "两轮电瓶车", num: 60 },
        ],
      },
      zfsbList: [
        { tit: "执法记录仪", num1: 156, num: 200 },
        { tit: "对讲机", num1: 113, num: 122 },
        { tit: "PDA", num1: 98, num: 122 },
        { tit: "无人机", num1: 30, num: 30 },
      ],

      tableData2: [],
      szbmTable: [],
      xsqTableData: [],
      xzjdTableData: [],
      activeIndex: 0,
      activeItem:"市直部门",
      tab: ["市直部门", "县市区指挥中心"],
      dom2: null,
      time2: null,

      nowTq: {},
      noewKq: [
        {
          img: "/static/images/zhdd/icon_sd.png",
          name: "湿度",
          val: "",
        },
        {
          img: "/static/images/zhdd/icon_qy.png",
          name: "气压",
          val: "",
        },
        {
          img: "/static/images/zhdd/icon_js.png",
          name: "小时雨量",
          val: "",
        },
        {
          img: "/static/images/zhdd/icon_fl.png",
          name: "风向风速",
          val: "",
        },
      ],
      nowSjtq: [],
      yybz: [
        { tit: "应处置数", num: 1.82, unit: "万" },
        { tit: "处置数", num: "", unit: "" },
        { tit: "按期处置率", num: "", unit: "" },
      ],
      zfll:[
        {
          name:"综合执法",
          num:0,
          unit:"人",
          info:""
        },
        {
          name:"专业领域",
          num:0,
          unit:"人",
          info:""
        },
        {
          name:"乡镇执法",
          num:0,
          unit:"人",
          info:""
        },
        {
          name:"其他",
          num:0,
          unit:"人",
          info:""
        }
      ],
    },
    // computed: {
    //   newnowSjtq() {
    //     let newArr = [];
    //     for (let i = 0; i < this.nowSjtq.length; i += 6) {
    //       newArr.push(this.nowSjtq.slice(i, i + 6));
    //     }
    //     return newArr;
    //   },
    // },
    mounted() {
      this.initApi(localStorage.getItem("city"));
      this.typeChange(this.typeValue);
      this.openMiddle();
      // 表格滚动
      this.dom2 = document.getElementById("box2");
      this.mouseleaveEvent2();
    },
    methods: {
      getZfll(city) {
        $api("/csdn_yjyp24",{qxwd:city}).then((res) => {
          this.zfll[0].num = this.getNumber(res[2].tjz); //综合
          this.zfll[1].num = this.getNumber(res[0].tjz); //专业
          this.zfll[2].num = this.getNumber(res[1].tjz); //乡镇
          this.zfll[3].num = res[3].tjz; //其它

          this.zfll[0].info = res[2].tjz
          this.zfll[1].info = res[0].tjz
          this.zfll[2].info = res[1].tjz
          this.zfll[3].info = res[3].tjz
        });
      },
      getNumber(str) {
        return str.split(",").length > 1 ? Number(str.split(",")[0].split(":")[1]) + Number(str.split(",")[1].split(":")[1])  :   Number(str.split(",")[0].split(":")[1])
      },
      showDialog(type) {
        window.parent.lay.openIframe({
          type: "openIframe",
          name: "zfllDialog",
          id: "zfllDialog",
          src:
            baseURL.url + "/static/citybrain/commonts/zhdd/zfllDialog.html",
          left: "1330px",
          top: "575px",
          width: "1215px",
          height: "726px",
          zIndex: "666",
          argument: {
            type: type,
          },
        });
      },
      showZfsbDialog(item) {
        window.parent.lay.openIframe({
          type: "openIframe",
          name: "zfsbDialog",
          id: "zfsbDialog",
          src:
            baseURL.url + "/static/citybrain/commonts/zhdd/zfsbDialog.html",
          left: "1230px",
          top: "575px",
          width: "1387px",
          height: "726px",
          zIndex: "666",
          argument: {
            type: item.tit,
          },
        });
      },
      typeChange(type) {
        $api("/csdn_yjyp21").then((res) => {
          let data = res.filter((a) => a.label.includes(type));
          this.yybz[0].num = data.find(
            (a) => a.label.indexOf("处置总数") != -1
          ).num;
          this.yybz[0].unit = data.find(
            (a) => a.label.indexOf("处置总数") != -1
          ).unit;
          this.yybz[1].num = data.find(
            (a) => a.label.indexOf("处置数") != -1
          ).num;
          this.yybz[1].unit = data.find(
            (a) => a.label.indexOf("处置数") != -1
          ).unit;
          this.yybz[2].num = data.find(
            (a) => a.label.indexOf("处置率") != -1
          ).num;
          this.yybz[2].unit = data.find(
            (a) => a.label.indexOf("处置率") != -1
          ).unit;
        });
        $api("/csdn_yjyp20").then((res) => {
          let data = res.filter((a) => a.label.includes(type));
          this.getXbar("yybzEc", data);
        });
      },
      toPhone(phone) {
        var reg = /(\d{3})\d{4}(\d{4})/; //正则表达式
        return phone.replace(reg, "$1****$2");
      },
      openCall(phones) {
        if (phones != "") {
          // window.parent.lay.openIframe({
          //   type: "openIframe",
          //   name: "zbPhone",
          //   id: "zbPhone",
          //   src: "/static/citybrain/zhdd/zhdd_page/zhtx_callPhone/zbPhone.html",
          //   left: "1200px",
          //   top: "469px",
          //   width: "1500px",
          //   height: "920px",
          //   zIndex: "666",
          //   argument: { phone: phones },
          // });

          window.parent.lay.openIframe({
            type: "openIframe",
            name: "CallPhone",
            id: "CallPhone",
            src: baseURL.url + "/static/citybrain/commonts/CallPhone/CallPhone.html",
            left: "1200px",
            top: "575px",
            width: "1515px",
            height: "866px",
            zIndex: "666",
            argument: { phone: phones },
          });
        }
      },
      openVideo(phones) {
        if (phones != "") {
          // window.parent.lay.openIframe({
          //   type: "openIframe",
          //   name: "zbVideo",
          //   id: "zbVideo",
          //   src: "/static/citybrain/zhdd/zhdd_page/zhtx_callPhone/zbVideo.html",
          //   left: "500px",
          //   top: "100px",
          //   width: "3250px",
          //   height: "1920px",
          //   zIndex: "666",
          //   argument: { phone: phones },
          // });

          window.parent.lay.openIframe({
            type: "openIframe",
            name: "CallVideo",
            id: "CallVideo",
            src: baseURL.url + "/static/citybrain/commonts/CallVideo/CallVideo.html",
            left: "0px",
            top: "0px",
            width: "3840px",
            height: "2160px",
            zIndex: "666",
            argument: { phone: phones },
          });
        }
      },
      openIfram() {
        window.parent.lay.openIframe({
          type: "openIframe",
          name: "zhtx_diong",
          id: "zhtx_diong",
          src:
            baseURL.url + "/static/citybrain/commonts/zhdd/zhtx_diong.html",
          left: "1150px",
          top: "500px",
          width: "1600px",
          height: "880px",
          zIndex: "666",
        });
      },
      openWRJ(item) {
        console.log(item.tit);
        if (item.tit == "无人机") {
          window.parent.lay.openIframe({
            type: "openIframe",
            name: "wrj_diong",
            src: "/static/citybrain/commonts/zhdd/wrj_diong.html",
            left: "calc(50% - 600px)",
            top: "725px",
            width: "1200px",
            height: "680px",
            zIndex: 667,
          });
        }
      },
      mouseenterEvent2() {
        clearInterval(this.time2);
      },
      mouseleaveEvent2() {
        this.time2 = setInterval(() => {
          // this.dom1.scrollTop += 1.5
          this.dom2.scrollBy({
            top: 100,
            behavior: "smooth",
          });
          if (
            this.dom2.scrollTop >=
            this.dom2.scrollHeight - this.dom2.offsetHeight
          ) {
            this.dom2.scrollTop = 0;
          }
        }, 1500);
      },
      openWin(item) {
        window.open(item.url);
      },
      openMiddle() {
        window.parent.lay.openIframe({
          type: "openIframe",
          name: "zhdd_middle",
          id: "zhdd_middle",
          src: baseURL.url + "/static/citybrain/zhdd/zhdd_page/zhdd_middle.html",
          width: "1760px",
          height: "150px",
          left: "calc(50% - 860px)",
          top: "65%",
          zIndex: "666",
        });
        window.parent.lay.openIframe({
          type: "openIframe",
          name: "zhdd_bottom",
          id: "zhdd_bottom",
          src: baseURL.url + "/static/citybrain/zhdd/zhdd_page/zhdd_bottom.html",
          width: "1760px",
          height: "525px",
          left: "calc(50% - 860px)",
          top: '73.3%',
          zIndex: "666",
        });
      },
      //初始化指挥体系
      initZhtx(city) {
        this.getSzbm(city)
        this.getXsqzhzx(city)
        this.getXzjdzhzx(city)
      },
      //市直部门
      getSzbm(city) {
        $api2Get("/xzzfj/dutyPersonnel/dutyList", {area:city,type:"1"}).then(res => {
          if (res.data.code == 200) {
            this.szbmTable = res.data.data.map(item => {
              return {
                bm: item.deptName,
                name: item.name,
                zw: item.duties,
                phone: item.phone,
              }
            })
          }
        })
      },
      //县市区指挥中心
      getXsqzhzx(city) {
        $api2Get("/xzzfj/dutyPersonnel/dutyList", {area:city,type:"2"}).then(res => {
          if (res.data.code == 200) {
            this.xsqTableData = res.data.data.map(item => {
              return {
                bm: item.deptName,
                name: item.name,
                zw: item.duties,
                phone: item.phone,
              }
            })
          }
        })
      },
      //乡镇街道指挥中心
      getXzjdzhzx(city) {
        $api2Get("/xzzfj/dutyPersonnel/dutyList", {area:city,type:"3"}).then(res => {
          if (res.data.code == 200) {
            this.xzjdTableData = res.data.data.map(item => {
              return {
                bm: item.town,
                name: item.name,
                zw: item.duties,
                phone: item.phone,
              }
            })
          }
        })
      },
      changeTab(item,index) {
        this.activeIndex = index;
        this.activeItem = item;
      },
      initApi(city) {
        let this_ = this;
        this_.getZfll(city)
        this.initZhtx(city);
        if (city == "金华市") {
          this.tab = ["市直部门", "县市区指挥中心"]
        } else {
          this.tab = ["县市区指挥中心", "乡镇街道指挥中心"]
        }
        $api("/csdn_yjyp12_new", { area_name: city }).then((res) => {
          // this.jdcData.numjd = this.fjdcData.numfjd = Math.abs(
          //   -res[0].qc + -res[0].mtc + -res[0].sldpc + -res[0].lldpc
          // );
          this.jdcData.numjd = this.fjdcData.numfjd = res[0].gg;
          this.jdcData.son[0].num = res[0].qc;
          this.jdcData.son[1].num = res[0].mtc;
          // this.fjdcData.numfjd = Math.abs(-res[0].sldpc + -res[0].lldpc);
          this.fjdcData.son[0].num = res[0].sldpc;
          this.fjdcData.son[1].num = res[0].lldpc;
          this.zfsbList[0].num1 = res[0].jly;
          this.zfsbList[0].num = res[0].jly_xjr;
          this.zfsbList[1].num1 = this.zfsbList[1].num = res[0].djj;
          this.zfsbList[2].num1 = this.zfsbList[2].num = res[0].pad;
          this.zfsbList[3].num1 = this.zfsbList[3].num = res[0].wrj;
        });



      },
      openManage(type) {
        type == 1?window.open('https://csdn.dsjj.jinhua.gov.cn:8303/dutyPersonnel'):window.open('https://csdn.dsjj.jinhua.gov.cn:8303/lawEquip')
      },
      getXbar(id, echartsData) {
        let myEc = echarts.init(document.getElementById(id));
        option = {
          tooltip: {
            trigger: "axis",
            backgroundColor: "rgba(51, 51, 51, 0.7)",
            borderWidth: 0,
            axisPointer: {
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            textStyle: {
              color: "white",
              fontSize: "24",
            },
            formatter: "{b} : {c}" + echartsData[0].unit,
          },
          grid: {
            top: 20,
            left: "3%",
            right: "4%",
            bottom: "5%",
            containLabel: true,
          },
          xAxis: {
            type: "value",
            boundaryGap: [0, 0.01],
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: ["#2a8bb0"], // 分隔线颜色。
                width: 1, // 分隔线线宽
                type: "dashed", // 线的类型
                opacity: 1, // 图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。
              },
            },
            axisLabel: {
              textStyle: {
                color: "#D6E7F9",
                fontSize: 20,
              },
            },
          },
          yAxis: {
            type: "category",
            data: echartsData.reverse().map((a) => a.ywwd1),
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              fontSize: 24,
              color: "#FFF",
            },
          },
          series: [
            {
              type: "bar",
              data: echartsData.map((a) => a.num),
              itemStyle: {
                normal: {
                  // barBorderRadius: [0, 0, 20, 0],
                  color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                    {
                      offset: 0,
                      color: "rgb(0, 186, 247,0.1)",
                    },
                    {
                      offset: 1,
                      color: "rgb(0, 191, 254,1)",
                    },
                  ]),
                },
              },
              barWidth: 20,
            },
          ],
        };
        myEc.setOption(option);
        myEc.getZr().on("mousemove", (param) => {
          myEc.getZr().setCursorStyle("default");
        });
      },
    },
    watch: {
      tab(val) {
        this.activeItem = val[this.activeIndex]
      }
    }
  });
  window.parent.eventbus &&
  window.parent.eventbus.on("leftIframeShow", () => {
    try {
      window.parent.lay.closeIframeByNames(["tcgl_ryglDate"]);
    } catch (error) {}
  });
</script>
</body>
</html>
