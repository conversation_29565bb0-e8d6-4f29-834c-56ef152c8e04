<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>视频弹窗</title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/css/viewCss/index.css" />
  <link rel="stylesheet" href="/static/css/viewCss/commonObjzhdd.css" />
  <script src="/jquery/jquery-3.6.1.min.js"></script>
  <script src="/Vue/vue.js"></script>
  <script src="/static/js/layui/layui.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/static/js/jslib/Emiter.js"></script>
  <script src="cmscVccBar/vccbar/cmscvccbar.js"></script>

  <style>
      /* 全屏遮罩容器 */
      .container {
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          background: transparent;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          z-index: 9999;
          overflow: hidden;
      }
      /* 主要内容区域 */
      .tel_box {
          position: relative;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
      }

      .tel_top {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
      }

      /* 通话中面板 - 左侧样式，居中显示 */
      .calling-panel {
          width: 750px;
          height: 1332px;
          background: rgba(0, 0, 0, 0.85);
          border-radius: 20px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px 30px;
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
          backdrop-filter: blur(15px);
          border: 1px solid rgba(255, 255, 255, 0.1);
      }

      /* 视频通话面板 - 右侧样式，居中显示 */
      .video-panel {
          width: 750px;
          height: 1332px;
          background: rgba(0, 0, 0, 0.9);
          border-radius: 20px;
          position: relative;
          overflow: hidden;
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4);
          border: 2px solid rgba(255, 255, 255, 0.2);
      }

      /* 远程视频区域 */
      .remoteView_box {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
          border-radius: 18px;
      }

      .remoteView {
          width: 100%;
          height: 100%;
          object-fit: cover;
      }
      /* 信息显示区域 - 通话中面板内部 */
      .info_box {
          color: white;
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 25px;
          width: 100%;
          text-align: center;
      }

      .tel_header {
          width: 150px;
          height: 150px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.15);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 30px;
          border: 3px solid rgba(255, 255, 255, 0.2);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
      }

      .tel {
          font-family: 'Microsoft YaHei', sans-serif;
          font-weight: 700;
          font-size: 28px;
          color: #fff;
          text-align: center;
          margin-bottom: 15px;
      }

      .status_box {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 15px;
          font-size: 25px;
      }

      .status {
          font-family: 'Microsoft YaHei', sans-serif;
          font-weight: 400;
          font-size: 25px;
          color: #2cd7aa;
          margin-right: 10px;
      }

      .green_status {
          color: #b2d8ff;
      }

      .status_img1 {
          width: 60px;
          height: 24px;
      }

      .status_img2 {
          width: 30px;
          height: 30px;
      }

      .voice_box {
          display: flex;
          align-items: center;
          justify-content: center;
      }

      .voice_img {
          width: 34px;
          height: 34px;
          margin-right: 10px;
      }

      .voice_time {
          font-family: 'Microsoft YaHei', sans-serif;
          font-weight: 400;
          font-size: 25px;
          color: #fff;
      }

      /* 本地视频区域 - 右上角显示 */
      .selfView_box {
          position: absolute;
          top: 20px;
          right: 20px;
          width: 180px;
          height: 120px;
          background: rgba(0, 0, 0, 0.9);
          border: 2px solid rgba(255, 255, 255, 0.4);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 15;
          overflow: hidden;
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
      }

      .selfView {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 10px;
      }
      /* 控制按钮区域 - 底部居中 */
      .tel_center {
          position: absolute;
          bottom: 50px;
          left: 50%;
          transform: translateX(-50%);
          z-index: 10;
      }

      .tel_end {
          width: 70px;
          height: 70px;
          cursor: pointer;
          border-radius: 50%;
          transition: all 0.3s ease;
          box-shadow: 0 8px 25px rgba(255, 0, 0, 0.4);
          filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
      }

      .tel_end:hover {
          transform: scale(1.1);
          box-shadow: 0 12px 35px rgba(255, 0, 0, 0.6);
      }

      /* 标题栏 */
      .rw-title {
          position: absolute;
          top: 20px;
          left: 50%;
          transform: translateX(-50%);
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 30px;
          background: rgba(0, 0, 0, 0.7);
          backdrop-filter: blur(15px);
          z-index: 20;
          border-radius: 30px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
      }

      .close {
          position: absolute;
          top: 20px;
          right: 20px;
          background: url("/static/images/zhdd/close.png") no-repeat;
          background-size: 20px 20px;
          background-position: center;
          width: 40px;
          height: 40px;
          cursor: pointer;
          transition: all 0.3s ease;
          z-index: 25;
          border-radius: 50%;
          background-color: rgba(0, 0, 0, 0.6);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .close:hover {
          transform: scale(1.1);
          background-color: rgba(255, 255, 255, 0.1);
      }

      /* 添加动画效果 */
      @keyframes fadeIn {
          from { opacity: 0; transform: scale(0.9); }
          to { opacity: 1; transform: scale(1); }
      }

      @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
      }

      .container {
          animation: fadeIn 0.3s ease-out;
      }

      .status_img2 {
          animation: pulse 1.5s infinite;
      }

      /* 美化滚动条 */
      ::-webkit-scrollbar {
          width: 8px;
      }

      ::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.3);
          border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
          background: rgba(255, 255, 255, 0.5);
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
          .selfView_box {
              width: 200px;
              height: 130px;
              top: 20px;
              right: 20px;
          }

          .info_box {
              left: 20px;
              padding: 20px;
          }

          .tel {
              font-size: 30px;
          }

          .status, .voice_time {
              font-size: 25px;
          }

          .tel_end {
              width: 60px;
              height: 60px;
          }
      }

      /* 添加毛玻璃效果 */
      .info_box, .rw-title {
          backdrop-filter: blur(15px);
          -webkit-backdrop-filter: blur(15px);
      }
  </style>
</head>
<body>
<div id="CallVideo" class="container">
  <!-- 标题栏 -->
  <div class="rw-title">
    <div class="fs-44 text-mid-yellow" id="rwTitle">视频通话</div>
  </div>

  <!-- 关闭按钮 -->
  <div class="close cursor" @click="close"></div>

  <!-- 主要内容区域 -->
  <div class="tel_box">
    <!-- 通话中面板 - 未接通时显示 -->
    <div v-if="!telStatus" class="calling-panel">
      <div class="info_box">
        <div class="tel_header">
          <!-- 使用默认头像样式，如果没有头像图片 -->
          <div style="width: 150px; height: 150px; border-radius: 50%; background: rgba(255, 255, 255, 0.2); display: flex; align-items: center; justify-content: center; font-size: 48px; color: #fff;">
            👤
          </div>
        </div>
        <div class="tel">{{ phoneCode }}</div>
        <div class="status_box">
          <div class="status">拨号中</div>
          <img class="status_img2" src="/static/assets/ajhf/phone_calling.png" alt="" />
        </div>
        <div class="voice_box">
          <img class="voice_img" src="/static/assets/ajhf/phone_video.png" alt="" />
          <div class="voice_time">{{ voiceTime }}</div>
        </div>
      </div>
      <!-- 挂断按钮 -->
      <div class="tel_center" style="margin-top: 30px;">
        <img class="tel_end" @click="endCall" src="/static/assets/ajhf/phone_end.png" alt="挂断" />
      </div>
    </div>

    <!-- 视频通话面板 - 接通后显示 -->
    <div v-if="telStatus" class="video-panel">
      <!-- 远程摄像头画面 -->
      <div class="remoteView_box">
        <div class="remoteView" id="remoteView_cincc_5g"></div>
      </div>

      <!-- 本地摄像头画面 - 右上角小窗 -->
      <div class="selfView_box">
        <div class="selfView" id="selfView_cincc_5g"></div>
      </div>

      <!-- 通话状态信息 - 左上角 -->
      <div style="position: absolute; top: 20px; left: 20px; color: white; z-index: 10;">
        <div style="display: flex; align-items: center; gap: 10px; background: rgba(0, 0, 0, 0.6); padding: 10px 15px; border-radius: 20px; backdrop-filter: blur(10px);">
          <div class="status green_status">视频中</div>
          <img class="status_img1" src="/static/assets/ajhf/phone_audio.png" alt="" style="width: 20px; height: 20px;" />
          <div class="voice_time">{{ voiceTime }}</div>
        </div>
      </div>

      <!-- 挂断按钮 - 底部居中 -->
      <div style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); z-index: 10;">
        <img class="tel_end" @click="endCall" src="/static/assets/ajhf/phone_end.png" alt="挂断" />
      </div>
    </div>
  </div>
</div>
  <script>
    var CallVm = new Vue({
      el: "#CallVideo",
      data: {
        //网络电话
        phoneCode: '',
        telStatus: false,
        muteFlag: false,
        voiceTime: '00:00:00',
        config: {
          tenantType: 1,
          ip: 'ygf.xzzfj.jinhua.gov.cn',
          port: '443',
          // ip: 'iccs.pointlinkprox.com',
          // port: '9080',
          vccId: '100317',
          agentId: '1001',
          password: 'Zyzx@10086',
          loginKey: '3W4SS2MK1YJBBJHWQEWOSRFF',
          event: {},
        },
        VccBar: null,
        signIn: false,
        load: false,
        callTimer: null, // 通话计时器
      },
      created() {
        // 在创建实例后正确设置事件处理函数
        this.config.event = {
          OnInitalSuccess: this._OnInitalSuccess,
          OnInitalFailure: this._OnInitalFailure,
          OnAnswerCall: this._OnAnswerCall,
          OnCallEnd: this._OnCallEnd,
          OnReportBtnStatus: this._OnReportBtnStatus,
          OnCallRing: this._OnCallRing,
          OnBarExit: this._OnBarExit,
          OnUpdateVideoWindow: this._OnUpdateVideoWindow,
          OnAgentWorkReport: this._OnAgentWorkReport,
        };
      },
      mounted() {
        const that = this;
        that.handleSignIn()
        window.addEventListener("message", function (event) {
          //子获取父消息
          let newData;
          if (typeof event.data == "object") {
            newData = event.data;
          } else {
            newData = JSON.parse(event.data.argument);
          }
          that.phoneCode = newData.phone;
          if (newData.phone) {
            that.phoneCode = newData.phone
            setTimeout(() => {
              that.openVideo(that.phoneCode)
            },1000)
          }
        });
      },
      methods: {
        //网络通话开始
        _OnInitalSuccess() {
          this.signIn = true
          console.log('初始化成功')
        },
        _OnInitalFailure() {},
        _OnAnswerCall(userNo, answerTime, serialID, serviceDirect, callID, userParam, taskID, av, tc, haveAsrEvent) {
          this.telStatus = true
          console.log('serialID1111', serialID)
          // 创建计时器，每秒更新一次通话时间
          this.callTimer = setInterval(() => {
            let seconds = parseInt(this.voiceTime.split(':')[2]) + 1
            let minutes = parseInt(this.voiceTime.split(':')[1])
            let hours = parseInt(this.voiceTime.split(':')[0])

            if (seconds >= 60) {
              seconds = 0
              minutes++
            }
            if (minutes >= 60) {
              minutes = 0
              hours++
            }

            this.voiceTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds
              .toString()
              .padStart(2, '0')}`
          }, 1000)
          if (av === 'video') {
            //这里控制视频窗口显示
            this.$refs['videoAndShare'].isVideo = true
          }
        },
        _OnCallEnd(
          callID,
          serialID,
          serviceDirect,
          userNo,
          bgnTime,
          endTime,
          agentAlertTime,
          userAlertTime,
          fileName,
          directory,
          disconnectType,
          userParam,
          taskID,
          serverName,
          networkInfo
        ) {
          console.log('通话结束')
          this.telStatus = false
          this.close()
          clearInterval(this.callTimer)
        },
        _OnReportBtnStatus(btnIDS) {
          console.log(btnIDS)
          var arrIDS = btnIDS.split('|')
          // for (var i = 0; i < arrIDS.length; i++) {
          //   this['btn' + parseInt(arrIDS[i])] = false
          // }
        },
        _OnCallRing(
          callingNo,
          calledNo,
          orgCalledNo,
          callData,
          serialID,
          serviceDirect,
          callID,
          userParam,
          taskID,
          userDn,
          agentDn,
          areaCode,
          fileName,
          networkInfo,
          queueTime,
          opAgentID,
          ringTime,
          projectID,
          accessCode,
          taskName,
          cityName,
          userType,
          lastServiceId,
          lastServiceName,
          accessNumber
        ) {},
        _OnBarExit(code, description) {
          this.signIn = false
          console.log('坐席迁出')
        },
        _OnUpdateVideoWindow(param) {
          console.log('OnUpdateVideoWindow-----', param)
          if (param.key_word == 'GetVideoViews') {
            param.param.SetVideoViews('selfView_cincc_5g', 'remoteView_cincc_5g')
          }
        },
        _OnAgentWorkReport(workStatus, description) {},
        handleSignIn() {
          if (this.signIn) {
            this.VccBar.UnInitial()
          } else {
            // 如果已经加载过cmscVccBar，不再重新加载
            if (!this.load) {
              this.VccBar = VccBar.setConfig(this.config).client()
              this.VccBar.load('cmscVccBar').then(() => {
                this.load = true
                this.VccBar.Initial()
              })
            } else {
              this.VccBar.Initial()
            }
          }
        },
        openCall(phone) {
          if (phone != "") {
            this.dialogVisible5 = true
            this.phoneCode = phone
            this.muteFlag = false
            this.telStatus = false
            this.VccBar.MakeCall(phone, 3, '', '', '', '', '', '', '', '', 1)
            this.voiceTime = '00:00:00'
          } else {
            this.$message.info('此人暂未录入手机号码')
          }
        },
        openVideo(phone) {
          if (phone != "") {
            this.dialogVisible6 = true
            this.phoneCode = phone
            this.telStatus = false
            this.VccBar.MakeCall(phone, 3, '', '', '', '', '', '', '', '', 2)
            this.voiceTime = '00:00:00'
          } else {
            this.$message.info('此人暂未录入手机号码')
          }
        },
        changeMute(mute) {
          this.muteFlag = !mute
          this.VccBar.Mute(this.muteFlag ? 2 : 1)
        },
        endCall() {
          this.VccBar.Disconnect()
          clearInterval(this.callTimer)
          this.close()
        },
        //网络通话结束
        close() {
          window.parent.lay.closeIframeByNames(["CallVideo"]);
        },
      },
    })
  </script>
</body>
</html>
