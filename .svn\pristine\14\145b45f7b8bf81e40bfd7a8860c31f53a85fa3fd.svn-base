<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>透明度使用示例</title>

    <link
      rel="stylesheet"
      href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <script src="./index.js" type="module"></script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        right: 20px;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv"></div>
    <div class="tools">
      <div>
        <p>步骤1：添加图层</p>
        <button onclick="addLayer();">添加气象图层</button>
        <p>步骤2：设置值（0-1）</p>
        <div>
          <button onclick="opacityReduce()">-</button
          ><button onclick="opacityAdd()">+</button>
        </div>
      </div>
    </div>
  </body>
  <script>
    let layer;
    async function addLayer() {
      layer = await ArcGisUtils.loadWeatherLayer("humidity", 1);
    }
    function opacityAdd() {
      let newOpacity;
      if (layer.opacity <= 1) {
        newOpacity = layer.opacity + 0.1;
      }
      layer.opacity = newOpacity; // 设置透明度方法
    }
    function opacityReduce() {
      let newOpacity;
      if (layer.opacity > 0) {
        newOpacity = layer.opacity - 0.1;
      }
      layer.opacity = newOpacity; // 设置透明度方法
    }
  </script>
</html>
