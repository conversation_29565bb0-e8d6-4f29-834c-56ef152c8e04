import Daylight from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/@arcgis/core/widgets/Daylight.js";

/**
 * 日照分析微件
 * @param {SceneView} view
 * @param {string} container 容器ID
 * @returns
 */
function createDaylightWidget(view, container) {
  if (!view) {
    throw new Error("参数view为必传！");
  }
  if (!container) {
    throw new Error("参数container为必传！");
  }
  const daylight = new Daylight({
    view,
    container,
  });
  return daylight;
}

export default createDaylightWidget;
