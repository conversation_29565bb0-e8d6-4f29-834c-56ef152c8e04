﻿<html>
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=gb2312">
    <title>vccbar-全功能</title>
    <meta http-equiv="X-UA-Compatible" content="IE=8"/>
    <link rel="stylesheet" type="text/css" href="./vccbar/ui/css/style.css">
    <link rel="stylesheet" type="text/css" href="./vccbar/ui/jquery-easyui-1.4/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="./vccbar/ui/jquery-easyui-1.4/themes/icon.css">
    <script src="./vccbar/ui/jquery-easyui-1.4/jquery.min.js" type="text/javascript"></script>
    <script src="./vccbar/ui/jquery-easyui-1.4/jquery.easyui.min.js" type="text/javascript"></script>
    <script src="./vccbar/ui/jbardisplay.js" type="text/javascript"></script>
    <script src="./vccbar/ui/jbarextent.js" type="text/javascript"></script>
    <script src="./vccbar/cmscvccbar.js" type="text/javascript"></script>
    <style> BODY {
        FONT-FAMILY: "Verdana";
        FONT-SIZE: 12px;
        SCROLLBAR-HIGHLIGHT-COLOR: #f5f9ff;
        SCROLLBAR-SHADOW-COLOR: #828282;
        SCROLLBAR-3DLIGHT-COLOR: #828282;
        SCROLLBAR-ARROW-COLOR: #797979;
        SCROLLBAR-TRACK-COLOR: #ffffff;
        SCROLLBAR-FACE-COLOR: #66b7ef;
        SCROLLBAR-DARKSHADOW-COLOR: #ffffff
    }
    </style>
</head>
<body onload="window_onload();">
<div style='position:absolute;border:0px solid #ff3900; left:10px;top:60px;'>
    <span>服务器环境: </span>
    <input type="text" id="envName" value="" size="20"/><br><br>
    <span style="margin-left: 35px;">工号: </span>
    <input type="text" id="agentCode" value="" size="20"/><br><br>
    <span style="margin-left: 10px;">坐席密码: </span>
    <input type="text" id="password" value="" size="20"/><br>
</select>
    <br>
    <div style="margin-top: 20px">
        <input id='btnInitial' type="button" onclick='funInitial();' value="初始化" title="执行方法:Initial()"/>
        <input id='btnUnInitial' type="button" onclick='_VccBar.UnInitial();' value="注销"
               title="执行方法:UnInitial()"/>
    </div>
</div>

<div class="divframe" id="divVedioDlg"
     style="position:absolute;top:60px;left:950px;width:460px;height:370px;background:#CCE6FF;color:#333;z-index: 999; display: none;">
    <div id="divVedioWindow" class="divStatus"
         style="margin-left:2px;margin-top:2px;padding-top: 10px;width:460px;height:357px;background:#99cdff;color:#333;">
        &nbsp;&nbsp;&nbsp;视频窗口:
        <span id="Vdelay" class="color0"> 延时：0ms </span>
        <div style="position:absolute;border:0px solid #ff00ff;left:440px; top:8px">
            <img src="vccbar\ui\jquery-easyui-1.4\themes\icons\cancel.png" onclick="DisplayDiv('divVedioDlg',0);"/>
        </div>
        <div id="agent_webcam"
             style="cursor: move; position: relative; border: 2px solid rgb(70, 163, 255); border-radius: 10px; left: 10px; top: 5px; width: 430px; height: 300px; background: rgb(130, 130, 130); display: block;">
            <div id="agent_remoteView" style="width:430px; height: 300px;background-color: transparent;"></div>
            <div id="agent_shareView"
                 style="position:absolute;left:150px;bottom: 6px;width: 133px;height: 100px;background-color: #aaaaaa;border-top: 2px solid #aaaaaa;border-right: 2px solid #aaaaaa;display:none"></div>
            <div id="agent_selfView"
                 style="position:absolute;left:8px;bottom: 6px;width: 100px;height: 133px;background-color: #aaaaaa;border-top: 2px solid #aaaaaa;border-right: 2px solid #aaaaaa;"></div>
        </div>
    </div>
</div>
<TEXTAREA id="TextareaInfo" name="TextareaInfo" rows="32" cols="150" value="" spellcheck="false"
          style="overflow:auto;font-family:verdana;font-size:12px;position:absolute;top:270px;"></TEXTAREA>
</body>
<script type="text/javascript">
    window._VccBar = null;
    function handleInput() {
        let result = {};
        let envName = document.getElementById("envName").value;
        let agentCode = document.getElementById("agentCode").value;
        let password = document.getElementById("password").value;

        result.vccId = agentCode.slice(6, agentCode.length - 4);
        result.agentId = agentCode.slice(agentCode.length - 4);
        result.password = password;

        switch (envName) {
        case '移动云自研呼叫中心':
            result.ip = 'iccs.pointlinkprox.com';
            result.port = '9080';
            result.loginKey = '3W4SS2MK1YJBBJHWQEWOSRFF';
            result.tenantType = 1;
          break;
        case '移动云呼叫中心':
            result.ip = 'iccs.pointlinkprox.com';
            result.port = '5049';
            result.loginKey = '';
            result.tenantType = 0;
            break;
        case '淮安呼叫中心':
            result.ip = 'cinccagentlogin.deskpro.cn';
            result.port = '5049';
            result.loginKey = '';
            result.tenantType = 0;
            break;
        case '洛阳呼叫中心':
            result.ip = 'www.deskpro.cn';
            result.port = '5049';
            result.loginKey = '';
            result.tenantType = 0;
            break;
         default:
            result.ip = '';
            result.port = '';
            result.loginKey = '';
            result.tenantType = '';
      }
      return result
    }
    function funInitial() {
        let result = handleInput();
        var config = {
            tenantType: result.tenantType,
            ip: result.ip,
            port: result.port,
            vccId: result.vccId,
            agentId: result.agentId,
            password: result.password,
            loginKey: result.loginKey,
            event: {
                OnInitalSuccess: onOnInitalSuccess,
                OnInitalFailure: onOnInitalFailure,
                OnAnswerCall: onOnAnswerCall,
                OnCallEnd: onOnCallEnd,
                OnReportBtnStatus: onReportBtnStatus,
                OnCallRing: onOnCallRing,
                OnBarExit: onOnBarExit,
                OnUpdateVideoWindow: onOnUpdateVideoWindow,
                OnAgentWorkReport: onOnAgentWorkReport,
                OnMethodResponseEvent: onOnMethodResponseEvent,
                OnPrompt: onOnPrompt,
                OnQueueReport: onOnQueueReport
            }
        }
        _VccBar = VccBar.setConfig(config).client();
        _VccBar.load().then(() => {
            _VccBar.Initial();
        });
    }

    function window_onload() {
        if (document.readyState == "complete") {

        }
        applicationUILoad(50, 10, 1300, 40, "");
    }


</script>

</html>

