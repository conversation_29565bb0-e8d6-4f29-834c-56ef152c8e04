// 退出登录
function logout() {
    return new Promise((resolve, reject) => {
        console.log('退出登录');
        axios({
            url: `${ip}/rest/index/login/logout`,
            method: "GET",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            withCredentials: true
        }).then((res) => {
            resolve()
        })
    })
}

// 心跳
function heart() {
    return new Promise((resolve, reject) => {
        axios({
            url: `${ip}/rest/other/user/online`,
            method: "GET",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            withCredentials: true
        }).then((e) => {
            resolve()
        });
    })
}

// 连接websocket
function createWebSocket() {
    return new Promise((resolve, reject) => {
        axios({
            url: `${ip}/rest/user/user/get_info`,
            method: "GET",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            withCredentials: true
        }).then((res) => {
            resolve(res)
        })
    })
}

// 获取设备树顶级单位信息 私有
function getTreeInfo(id) {
    return new Promise((resolve, reject) => {
        var data = {
            "id": id,
            "bh": 'bh',
            "text": 'dname',
            "type": 0,
        };
        axios({
            url: `${ip}/rest/other/unitjson/gdlist`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: data,
            withCredentials: true
        }).then((response) => {
            resolve(response)
        }).catch((err) => {
            console.log(err);
            reject()
        })
    })
}

// 国标
function getTreeInfo_gb() {
    return new Promise((resolve, reject) => {
        axios({
            url: `${ip}/rest/monitor/videopatrols/get`,
            method: "GET",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            withCredentials: true
        }).then((response) => {
            resolve(response)
        }).catch((err) => {
            console.log(err);
            reject()
        })
    })
}

// 获取设备树-设备-国标
function unitEquipTreeGB(unit = '', key = '', recorder_type_arr = []) {
    return new Promise((resolve, reject) => {
        let data = { unit, key, recorder_type_arr };
        axios({
            url: `${ip}/rest/monitor/videopatrols/getdevice`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: data,
            withCredentials: true
        }).then((response) => {
            resolve(response)
        }).catch((err) => {
            console.log(err);
            reject()
        })
    })
}

// 开启拉流
function startLive(dataInfo) {
    return new Promise((resolve, reject) => {
        console.log(dataInfo);
        var send = {
            hostbody_arr: [dataInfo.hostbody],
        };
        axios({
            url: `${ip}/rest/live/chrome/startLive`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: send,
            withCredentials: true
        }).then((res) => {
            console.log(res.data);
            resolve(res.data)
        })
    })
}

// 关闭拉流
function stopLive(dataInfo) {
    return new Promise((resolve, reject) => {
        console.log(dataInfo);
        var send = {
            hostbody_arr: [dataInfo.hostbody],
        };
        axios({
            url: `${ip}/rest/live/chrome/stopLive`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: send,
            withCredentials: true
        }).then((res) => {
            console.log(res);
            resolve()
        })
    })
}

// 控制设备静音(true:startmute;false:stopmute)
function sendMute(imei, type) {
    return new Promise((resolve, reject) => {
        var send = {
            imei: imei,
            type: type ? "startmute" : "stopmute",
        };
        axios({
            url: `${ip}/rest/gis/gismoni/send_cmd`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: send,
            withCredentials: true
        }).then((res) => {
            console.log(res);
            resolve(res.data)
        }).catch((err) => {
            console.log(err);
            reject()
        })
    })
}

// 控制设备录像(true:startvideo;false:stopvideo)
function sendVideo(imei, type) {
    return new Promise((resolve, reject) => {
        var send = {
            imei: imei,
            type: type ? "startvideo" : "stopvideo",
        };
        axios({
            url: `${ip}/rest/gis/gismoni/send_cmd`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: send,
            withCredentials: true
        }).then((res) => {
            console.log(res);
            resolve(res.data)
        }).catch((err) => {
            console.log(err);
            reject()
        })
    })
}

// 控制设备拍照
function sendVideo(imei, type) {
    return new Promise((resolve, reject) => {
        var send = {
            imei: imei,
            type: "takephoto",
        };
        axios({
            url: `${ip}/rest/gis/gismoni/send_cmd`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: send,
            withCredentials: true
        }).then((res) => {
            console.log(res);
            resolve(res.data)
        }).catch((err) => {
            console.log(err);
            reject()
        })
    })
}

// 拉流中的对讲
function startAudioInVideo(hostbody) {
    return new Promise((resolve, reject) => {
        let sent = {
            hostbody: hostbody,
        };
        axios({
            url: `${ip}/rest/live/chrome/startAudioInVideo`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: sent,
            withCredentials: true
        }).then((res) => {
            console.log(res);
            resolve(res.data)
        }).catch((err) => {
            console.log(err);
            reject()
        })
    })
}

// 开始对讲
function startAudio(hostbody) {
    return new Promise((resolve, reject) => {
        let sent = {
            hostbody_arr: hostbody,
        };
        axios({
            url: `${ip}/rest/live/chrome/startAudio`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: sent,
            withCredentials: true
        }).then((res) => {
            console.log(res);
            resolve(res.data)
        }).catch((err) => {
            console.log(err);
            reject()
        })
    })
}

// 关闭对讲
function stopAudio(hostbody) {
    return new Promise((resolve, reject) => {
        let sent = {
            hostbody_arr: hostbody,
        };
        axios({
            url: `${ip}/rest/live/chrome/stopAudio`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: sent,
            withCredentials: true
        }).then((res) => {
            console.log(res);
            resolve(res.data)
        }).catch((err) => {
            console.log(err);
            reject()
        })
    })
}

// 获取平台推流地址
function getStreamUrl(data) {
    return new Promise((resolve, reject) => {
        let send = {
            getvideo_path: null,
            getpic_path: null,
            getvideo_switch: null,
            getvideo_time: null,
            getvideo_num: null,
            type: 'startlive',
            issos: '0',
            isconfer: '1',
            messid: ['2'],
            groupbh: "", //param = {}
        };
        var sent = Object.assign(send, data)
        axios({
            url: `${ip}/rest/gis/gisvideo/palyerVideo`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: sent,
            withCredentials: true
        }).then((res) => {
            console.log(res);
            resolve(res.data)
        }).catch((err) => {
            console.log(err);
            reject()
        })
    })
}

// 开始双人执法
function startTalk(val) {
    return new Promise((resolve, reject) => {
        let sent = {
            callId: val.callId,
        };
        axios({
            url: `${ip}/rest/gis/talk/start`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: sent,
            withCredentials: true
        }).then((res) => {
            console.log(res);
            resolve(res.data)
        }).catch((err) => {
            console.log(err);
            reject()
        })
    })
}

// 开始双人执法
function stopTalk(data) {
    return new Promise((resolve, reject) => {
        axios({
            url: `${ip}/rest/gis/talk/stop`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: data,
            withCredentials: true
        }).then((res) => {
            console.log(res);
            resolve(res.data)
        }).catch((err) => {
            console.log(err);
            reject()
        })
    })
}

// 获取调度配置
function getPlayerSetting() {
    return new Promise((resolve, reject) => {
        axios({
            url: `${ip}/rest/system/config/play_get`,
            method: "get",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            withCredentials: true
        }).then((res) => {
            console.log(res);
            resolve(res.data)
        }).catch((err) => {
            console.log(err);
            reject()
        })
    })
}

// 呼叫双人执法
function startCall(data) {
    return new Promise((resolve, reject) => {
        let send = {
            hostbody: data.hostbody,
        };
        axios({
            url: `${ip}/rest/gis/talk/call`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: send,
            withCredentials: true
        }).then((res) => {
            console.log(res);
            resolve(res.data)
        }).catch((err) => {
            console.log(err);
            reject()
        })
    })
}

// 结束双人执法
function stopTalk(data) {
    return new Promise((resolve, reject) => {
        axios({
            url: `${ip}/rest/gis/talk/stop`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: data,
            withCredentials: true
        }).then((res) => {
            console.log(res);
            resolve(res.data)
        }).catch((err) => {
            console.log(err);
            reject()
        })
    })
}

// 国标双人执法中的拉流
function startLiveGB(data) {
    return new Promise((resolve, reject) => {
        console.log(data);
        axios({
            url: `${ip}/rest/live/chrome/startLive`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: data,
            withCredentials: true
        }).then((res) => {
            console.log(res.data);
            resolve(res.data)
        }).catch((err) => {
            console.log(err);
            reject(res.data)
        })
    })
}

// 国标双人执法中获取指挥台的信息
function assistEnforcerInfo() {
    return new Promise((resolve, reject) => {
        axios({
            url: `${ip}/rest/gis/talk/assistEnforcerInfo`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            withCredentials: true
        }).then((res) => {
            console.log(res.data);
            resolve(res.data)
        }).catch((err) => {
            console.log(err);
            reject(res.data)
        })
    })
}

// 关闭拉流
function stopLiveGB(dataInfo) {
    return new Promise((resolve, reject) => {
        console.log(dataInfo);
        var send = {
            hostbody_arr: [dataInfo.hostbody],
            sn_arr: dataInfo.sn ? [dataInfo.sn] : [],
        };
        axios({
            url: `${ip}/rest/live/chrome/stopLive`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: send,
            withCredentials: true
        }).then((res) => {
            console.log(res);
            resolve()
        })
    })
}

// 接受开始双人执法接口
function startTalk(data) {
    return new Promise((resolve, reject) => {
        axios({
            url: `${ip}/rest/gis/talk/start`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: data,
            withCredentials: true
        }).then((res) => {
            console.log(res.data);
            resolve(res.data)
        }).catch((err) => {
            console.log(err);
            reject(res.data)
        })
    })
}

// 控制设备静音国标双人执法(true:startmute;false:stopmute)
function sendMuteGB(data) {
    return new Promise((resolve, reject) => {
        axios({
            url: `${ip}/rest/gis/gismoni/send_cmd`,
            method: "POST",
            header: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            data: data,
            withCredentials: true
        }).then((res) => {
            console.log(res);
            resolve(res.data)
        }).catch((err) => {
            console.log(err);
            reject()
        })
    })
}