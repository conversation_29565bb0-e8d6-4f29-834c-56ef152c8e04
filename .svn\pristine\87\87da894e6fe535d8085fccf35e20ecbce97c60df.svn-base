<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>治理协同弹窗</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <link rel="stylesheet" href="/static/css/viewCss/commonObjzhdd.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/Vue/vue-count-to.min.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/static/js/jslib/Emiter.js"></script>
    <script src="/elementui/js/index.js"></script>
    <style>

        ::-webkit-scrollbar {
             display: none;
         }


      ul,
      ul li {
        list-style: none;
      }

      .rwgz-tc {
        width: 1387px;
        height: 726px;
        background: url("/static/images/zhdd/dialogBg.png") no-repeat;
        background-size: 100% 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: column;
      }

      .rw-title {
        padding: 46px 3% 0;
        width: 95%;
        height: 60px;
        line-height: 60px;
      }

      .close {
        background: url("/static/images/zhdd/close.png") no-repeat;
        width: 34px;
        height: 34px;
      }

      .ql-indent-1 img {
        width: 100%;
      }

      img {
        width: 100%;
      }

      .content {
          height: 850px;
          margin-top: 10px;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
          flex-direction: column;
      }

      ::-webkit-scrollbar {
          width: 0;
          height: 0;
      }

      .scrollTab {
          width:928px;
          height:70px;
      }

      .table-container {
          height: 482px;
          overflow-y: scroll;
      }

        .tableContainer2 {
            height: 400px;
            overflow-y: scroll;
        }

      .table-line {
          width: 1296px;
          height: 80px;
          display: flex;
          justify-content: space-evenly;
          align-items: center;
          background: rgba(50,134,248,0.15);
      }

      .title-line {
          background: transparent !important;
      }

      .table-title {
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: bold;
          color: #FFFFFF;
          text-align: left;
      }

      .table-column {
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #FFFFFF;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
      }
      .activeTableLine {
          background: rgba(50,134,248,0.25);
      }

        .el-input {
            /*position: absolute;*/
            /*top: 229px;*/
            /*left: 1630px;*/
            width: 537px;
            height: 72px;
        }

        .el-input__icon {
            height: 100%;
            width: 25px;
            text-align: center;
            -webkit-transition: all .3s;
            transition: all .3s;
            line-height: 75px;
        }

        .el-scrollbar {
            overflow: hidden;
            /*position: relative;*/
            height: 500px;
            background: #020b28;
            border-radius: 10px;
        }

        .el-autocomplete-suggestion__wrap {
            max-height: unset !important;
            padding: 10px 0;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
        }

        .el-input--suffix .el-input__inner {
            border: 1px solid #359CF8;
            border-radius: 8px;
            padding-right: 30px;
            height: 70px;
            font-family: MicrosoftYaHei;
            font-size: 28px;
            font-weight: normal;
            font-stretch: normal;
            letter-spacing: 1px;
            color: #bbe5fd !important;
            background: #020b28;
        }

        .el-input.is-active .el-input__inner,
        .el-input__inner:focus {
            border: 1px solid #bbe5fd;
            outline: 0;
        }

        .el-input__suffix-inner {
            pointer-events: all;
            font-size: 28px;
            margin: 15px 20px 0 0;
            color: #bbe5fd !important;
        }

        .el-autocomplete-suggestion li {
            padding: 0 20px;
            line-height: 34px;
            cursor: pointer;
            color: #bbe5fd;
            font-size: 28px;
            list-style: none;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin: 10px 0 25px 0;
        }

        .el-autocomplete-suggestion li:hover {
            background: unset !important;
        }

        .search {
            width: 100px;
            height: 65px;
            line-height: 65px;
            text-align: center;
            background: #0A619E;
            border: 1px solid #359CF8;
            border-radius: 8px;
            font-size: 28px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FEFEFE;
            margin-left: 10px;
            cursor: pointer;
        }
    </style>
  </head>

  <body>
    <div id="zlxt" class="rwgz-tc">
      <div class="rw-title flex-between">
        <div class="fs-44 text-mid-yellow" id="rwTitle" style="margin-left: 20px;">{{name == "参与处置部门数"?"参与部门处置协同事件数":"已协同事项数"}}</div>
        <div class="close cursor" @click="close" style="margin-right: 20px;"></div>
      </div>
      <div class="content">
        <div class="table">
          <div class="table-line title-line" v-show="name == '参与处置部门数'">
            <div class="table-column table-title" style="flex: 1;margin-left: 30px">序号</div>
            <div class="table-column table-title" style="flex: 3">部门</div>
            <div class="table-column table-title" style="flex: 2">协同数</div>
            <div class="table-column table-title" style="flex: 2">办结数</div>
            <div class="table-column table-title" style="flex: 2">超期办结数</div>
          </div>
          <div class="table-container" v-show="name == '参与处置部门数'">
            <div class="table-line" v-for="(item,i) in czbmTableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
              <div class="table-column" style="flex: 1;margin-left: 30px">{{i + 1}}</div>
              <div class="table-column" style="flex: 3" :title="item.depart">{{item.depart}}</div>
              <div class="table-column" style="flex: 2">{{item.xts}}</div>
              <div class="table-column" style="flex: 2">{{item.bjs}}</div>
              <div class="table-column" style="flex: 2">{{item.cqbjs}}</div>
            </div>
          </div>

          <div class="table-line title-line" v-show="name == '已协同事项数'">
            <div class="table-column table-title" style="flex: 1;margin-left: 30px">序号</div>
            <div class="table-column table-title" style="flex: 3">事项类别</div>
            <div class="table-column table-title" style="flex: 2">协同数</div>
            <div class="table-column table-title" style="flex: 2">办结数</div>
            <div class="table-column table-title" style="flex: 2">超期办结数</div>
            <div class="table-column table-title" style="flex: 2">参与处置部门数</div>
          </div>
          <div class="table-container" v-show="name == '已协同事项数'">
            <div class="table-line" v-for="(item,i) in gtsxTableData" :key="i" :class="{activeTableLine:i % 2 == 0}">
              <div class="table-column" style="flex: 1;margin-left: 30px">{{i + 1}}</div>
              <div class="table-column" style="flex: 3" :title="item.sxlb">{{item.sxlb}}</div>
              <div class="table-column" style="flex: 2">{{item.xts}}</div>
              <div class="table-column" style="flex: 2">{{item.bjs}}</div>
              <div class="table-column" style="flex: 2">{{item.cqbjs}}</div>
              <div class="table-column" style="flex: 2">{{item.cybms}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      let vm = new Vue({
        el: "#zlxt",
        data: {
          name:"",
          czbmTableData: [],
          gtsxTableData: []
        },
        computed:{
          city() {
            return localStorage.getItem("city")
          }
        },
        mounted() {
          const that = this;
          window.addEventListener("message", function (event) {
            //子获取父消息
            let newData;
            if (typeof event.data == "object") {
              newData = event.data;
            } else {
              newData = JSON.parse(event.data.argument);
            }
            that.name = newData.type;
            that.getDetail(newData.type,newData.time);
          });
        },
        methods: {
          //获取数据
          getDetail(name,time) {
            switch (name) {
              case "参与处置部门数":
                this.getCzbmist(time);
                break;
              case "已协同事项数":
                this.getGtsxList(time);
                break;
            }
          },
          //参与处置部门数
          getCzbmist(time) {
            $api("csdn_yjyp_cybm1",{area_name:this.city,sj1:time[0],sj2:time[1]}).then(res => {
              this.czbmTableData = res.map(item => {return {
                depart:item.cybm,
                bjs:item.bjs,
                cqbjs:item.cqbjs,
                xts:item.xts
              }})
            })
          },
          //已协同事项数
          getGtsxList(time) {
            $api("csdn_yjyp_gtsx1",{area_name:this.city,sj1:time[0],sj2:time[1]}).then(res => {
              console.log(res);
              this.gtsxTableData = res.map(item => {return {
                sxlb:item.sx,
                bjs: item.bjs,
                cqbjs: item.cqbjs,
                xts: item.xts,
                cybms: item.cybms
              }})
            })
          },
          close() {
            window.parent.lay.closeIframeByNames(["zlxtDialog"]);
          }
        },
      });
    </script>
  </body>
</html>
