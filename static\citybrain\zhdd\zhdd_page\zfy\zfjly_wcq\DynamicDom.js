// 封装动态添加dom的构造函数
// 私标拉流
class DynamicElement {
    constructor(parentDom, dataInfo) {
        this.parentDom = parentDom;
        this.dataInfo = dataInfo;
        this.hideTimeout = null;
        // 创建元素
        this.newPlayerDiv_0 = document.createElement("div");
        this.newPlayerDiv_1 = document.createElement("div");
        this.newPlayerDiv_2 = document.createElement("div");
        this.newPlayerDiv_3 = document.createElement("video");
        this.newPlayerDiv_4 = document.createElement("div"); //父级
        this.newCloseDiv = document.createElement("div"); //关闭
        this.newVoiceDiv = document.createElement("div"); //声音
        this.newVoiceIntercomDiv = document.createElement("div"); //麦克风
        this.newVideotapeDiv = document.createElement("div"); //记录仪录像
        this.newPhotographDiv = document.createElement("div"); //记录仪拍照
        this.newScreenShotDiv = document.createElement("div"); //截屏
        this.init = this.init.bind(this);
        this.player = null;
        this.isAudioFlag = false; //判断是否开启对讲
        this.push_flag = false; //音频对讲开关
        this.Volume = false; //判断是否开启声音
        this.stopTape = false; //判断是否开启录像
        this.webrtc_audio = null;
    }

    init() {
        this.newPlayerDiv_0.className = 'chromeMain';
        this.newPlayerDiv_0.id = `chromeDiv${this.dataInfo.hostbody}`;
        this.newPlayerDiv_4.id = 'myControls';
        this.newPlayerDiv_4.className = 'myControls';
        this.newCloseDiv.className = 'end control';
        this.newVoiceDiv.className = 'voiceClose control';
        this.newVoiceDiv.id = `voice${this.dataInfo.hostbody}`;
        this.newVoiceIntercomDiv.className = 'voice_intercom control';
        this.newVoiceIntercomDiv.id = `voice_intercom${this.dataInfo.hostbody}`;
        this.newVideotapeDiv.className = 'videotape control';
        this.newPhotographDiv.className = 'photograph control';
        this.newScreenShotDiv.className = 'screenshot control';
        this.newPlayerDiv_1.className = "video_mian";
        this.newPlayerDiv_1.id = `bigBox${this.dataInfo.hostbody}`;
        this.newPlayerDiv_2.className = "playerVideoBox";
        this.newPlayerDiv_2.id = `fullscreen${this.dataInfo.hostbody}`;
        this.newPlayerDiv_3.className = "chrome";
        this.newPlayerDiv_3.id = `video${this.dataInfo.hostbody}`;
        // this.newPlayerDiv_3.src = 'http://vjs.zencdn.net/v/oceans.mp4';
        this.newPlayerDiv_3.controls = true;
        this.newPlayerDiv_3.autoplay = true;
        this.newPlayerDiv_3.width = 640;
        this.newPlayerDiv_3.height = 480;

        // 将子节点添加到父节点上
        this.newPlayerDiv_2.appendChild(this.newPlayerDiv_3);
        this.newPlayerDiv_1.appendChild(this.newPlayerDiv_2);
        this.newPlayerDiv_4.appendChild(this.newCloseDiv);
        this.newPlayerDiv_4.appendChild(this.newVoiceDiv);
        this.newPlayerDiv_4.appendChild(this.newVoiceIntercomDiv);
        this.newPlayerDiv_4.appendChild(this.newVideotapeDiv);
        this.newPlayerDiv_4.appendChild(this.newPhotographDiv);
        this.newPlayerDiv_4.appendChild(this.newScreenShotDiv);

        // 添加事件监听器
        // 绑定点击事件处理函数的上下文
        this.newPlayerDiv_1_dblclick = this.newPlayerDiv_1_dblclick.bind(this);
        this.newPlayerDiv_1_mouseover = this.newPlayerDiv_1_mouseover.bind(this);
        this.newPlayerDiv_1_mouseout = this.newPlayerDiv_1_mouseout.bind(this);
        this.newPlayerDiv_1_mousemove = this.newPlayerDiv_1_mousemove.bind(this);
        this.newPlayerDiv_4_mouseover = this.newPlayerDiv_4_mouseover.bind(this);
        this.newPlayerDiv_4_mouseout = this.newPlayerDiv_4_mouseout.bind(this);
        this.newCloseDiv_click = this.newCloseDiv_click.bind(this);
        this.newVoiceDiv_click = this.newVoiceDiv_click.bind(this);
        this.newVoiceIntercomDiv_click = this.newVoiceIntercomDiv_click.bind(this);
        this.newVideotapeDiv_click = this.newVideotapeDiv_click.bind(this);
        this.newPhotographDiv_click = this.newPhotographDiv_click.bind(this);
        this.newScreenShotDiv_click = this.newScreenShotDiv_click.bind(this);
        this.newPlayerDiv_1.addEventListener("dblclick", this.newPlayerDiv_1_dblclick);
        this.newPlayerDiv_1.addEventListener("mouseover", this.newPlayerDiv_1_mouseover);
        this.newPlayerDiv_1.addEventListener("mouseout", this.newPlayerDiv_1_mouseout);
        this.newPlayerDiv_1.addEventListener("mousemove", this.newPlayerDiv_1_mousemove);
        this.newPlayerDiv_4.addEventListener("mouseover", this.newPlayerDiv_4_mouseover);
        this.newPlayerDiv_4.addEventListener("mouseout", this.newPlayerDiv_4_mouseout);
        this.newCloseDiv.addEventListener("click", this.newCloseDiv_click);
        this.newVoiceDiv.addEventListener("click", this.newVoiceDiv_click);
        this.newVoiceIntercomDiv.addEventListener("click", this.newVoiceIntercomDiv_click);
        this.newVideotapeDiv.addEventListener("click", this.newVideotapeDiv_click);
        this.newPhotographDiv.addEventListener("click", this.newPhotographDiv_click);
        this.newScreenShotDiv.addEventListener("click", this.newScreenShotDiv_click);

        // 将div元素添加到容器中
        this.newPlayerDiv_0.appendChild(this.newPlayerDiv_1);
        this.newPlayerDiv_0.appendChild(this.newPlayerDiv_4);
        this.parentDom.appendChild(this.newPlayerDiv_0);

        this.startPlayVideo()
    }
    newPlayerDiv_1_dblclick(e) {
        console.log("点击了新添加的元素。", e);
        console.log(e.currentTarget.parentElement.id);
        var elVideo = document.getElementById(e.currentTarget.id).parentElement;
        console.log(elVideo);
        elVideo.οndblclick = "return false;";
        var isFull = !!(
            document.webkitIsFullScreen ||
            document.mozFullScreen ||
            document.msFullscreenElement ||
            document.fullscreenElement
        );
        var iselVideo = !!(
            elVideo.webkitIsFullScreen ||
            elVideo.mozFullScreen ||
            elVideo.msFullscreenElement ||
            elVideo.fullscreenElement
        );
        console.log(isFull, iselVideo);
        if (!isFull) {
            var fullscreenEle =
                document.fullscreenElement ||
                document.mozFullScreenElement ||
                document.webkitFullscreenElement;
            //注意：要在用户授权全屏后才能获取全屏的元素，否则 fullscreenEle为null
            console.log("全屏元素1：" + fullscreenEle);
            // 判断是否是全屏
            if (elVideo.requestFullscreen) {
                console.log(1);
                elVideo.requestFullscreen();
            }
            // FireFox
            else if (elVideo.mozRequestFullscreen) {
                console.log(2);
                elVideo.mozRequestFullscreen();
            }
            // Chrome等
            else if (elVideo.webkitRequestFullscreen) {
                console.log(3);
                elVideo.webkitRequestFullscreen();
            }
        } else {
            var fullscreenEle =
                document.fullscreenElement ||
                document.mozFullScreenElement ||
                document.webkitFullscreenElement;
            //注意：要在用户授权全屏后才能获取全屏的元素，否则 fullscreenEle为null
            console.log("全屏元素2：" + fullscreenEle.id);
            // e.currentTarget.id e.currentTarget.parentElement.id
            if (fullscreenEle.id == e.currentTarget.parentElement.id) {
                if (document.exitFullscreen) {
                    console.log(4);
                    document.exitFullscreen();
                } else if (document.msExitFullscreen) {
                    console.log(5);
                    document.msExitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    console.log(6);
                    document.mozCancelFullScreen();
                } else if (document.webkitExitFullscreen) {
                    console.log(7);
                    document.webkitExitFullscreen();
                }
            } else {
                var fullscreenEle =
                    document.fullscreenElement ||
                    document.mozFullScreenElement ||
                    document.webkitFullscreenElement;
                //注意：要在用户授权全屏后才能获取全屏的元素，否则 fullscreenEle为null
                console.log("全屏元素1：" + fullscreenEle);
                // 判断是否是全屏
                if (elVideo.requestFullscreen) {
                    console.log(1);
                    elVideo.requestFullscreen();
                }
                // FireFox
                else if (elVideo.mozRequestFullscreen) {
                    console.log(2);
                    elVideo.mozRequestFullscreen();
                }
                // Chrome等
                else if (elVideo.webkitRequestFullscreen) {
                    console.log(3);
                    elVideo.webkitRequestFullscreen();
                }
            }
        }
    }

    newPlayerDiv_1_mouseover(event) {
        // console.log("移入元素中",event);
        if (event.currentTarget.id == `bigBox${this.dataInfo.hostbody}` || event.currentTarget.id == "myControls") {
            var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
            if (dom.nextElementSibling) {
                dom.nextElementSibling.style.display = "block";
            }
        }
    }

    newPlayerDiv_1_mouseout(event) {
        // console.log("移出元素中",event);
        var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
        if (dom.nextElementSibling) {
            dom.nextElementSibling.style.display = "none";
        }
    }

    newPlayerDiv_1_mousemove(event) {
        // console.log("在元素中移动",event);
        var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
        clearTimeout(this.hideTimeout);
        if (dom.nextElementSibling) {
            dom.nextElementSibling.style.display = "block";
            dom.style.cursor = "pointer";
        }

        // 重新启动定时器，在2秒后再次隐藏控制栏
        this.hideTimeout = setTimeout(function() {
            if (dom.nextElementSibling) {
                dom.nextElementSibling.style.display = "none";
            }
        }, 2000);
    }

    newPlayerDiv_4_mouseover(event) {
        // console.log("移入元素中",event);
        if (event.currentTarget.id == `bigBox${this.dataInfo.hostbody}` || event.currentTarget.id == "myControls") {
            var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
            if (dom.nextElementSibling) {
                dom.nextElementSibling.style.display = "block";
            }
        }
    }

    newPlayerDiv_4_mouseout(event) {
        // console.log("移出元素中",event);
        var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
        if (dom.nextElementSibling) {
            dom.nextElementSibling.style.display = "none";
        }
    }

    // 关闭拉流
    newCloseDiv_click(event) {
        if (this.isAudioFlag) {
            stopAudio([this.dataInfo.hostbody]).then(() => {
                isStartAudio--;
                if (isStartAudio <= 0) {
                    // 开启了对讲
                    // this.webrtc_audio.close_play();
                    // this.webrtc_audio = null;
                    pcStartAudio.close_play();
                    pcStartAudio = null;
                }
                stopLive(this.dataInfo).then(() => {
                    var videoDom = document.getElementById(`video${this.dataInfo.hostbody}`);
                    this.player.close_play(videoDom);
                    this.removeDom()
                })
            })
        } else {
            stopLive(this.dataInfo).then(() => {
                var videoDom = document.getElementById(`video${this.dataInfo.hostbody}`);
                this.player.close_play(videoDom);
                this.removeDom()
            })
        }
    }

    // 声音按钮
    newVoiceDiv_click() {
        console.log("我被点击了");
        var voice = document.getElementById(this.newVoiceDiv.id);
        var video = document.getElementById(this.newPlayerDiv_3.id)
        if (!this.Volume) {
            console.log("我是来关闭声音");
            voice.setAttribute("class", "voiceClose control");
            video.muted = true;
        } else {
            console.log("我是来开启声音");
            voice.setAttribute("class", "voiceOpen control");
            video.muted = false;
        }
        this.Volume = !this.Volume;
    }

    // 麦克风按钮
    newVoiceIntercomDiv_click() {
        var dom = document.getElementById(this.newVoiceIntercomDiv.id)
        if (!this.push_flag) {
            if (!this.isAudioFlag) {
                startAudioInVideo(this.dataInfo.hostbody).then((res) => {
                    if (res.code == 200) {
                        // res.data.platform_webrtc_push_url 平台推流地址
                        // 如果其他窗口推了麦克风的流，就不用走这里啦
                        if (!isStartAudio) {
                            var InitialData = {
                                element: "",
                                debug: true,
                                zlmsdpUrl: res.data.platform_webrtc_push_url,
                                simulcast: false,
                                useCamera: false,
                                audioEnable: true,
                                videoEnable: false,
                                recvOnly: false,
                                resolution: { w: 3840, h: 2160 },
                                usedatachannel: false,
                            };
                            // this.webrtc_audio = new Webrtc(InitialData);
                            pcStartAudio = new Webrtc(InitialData);
                            // pcStartAudio = this.webrtc_audio;
                            pcStartAudio.start_play().then(() => {
                                this.isAudioFlag = true;
                                this.push_flag = true;
                                isStartAudio++;
                                console.log(isStartAudio);
                                dom.setAttribute("class", "video_open control");
                            }).catch((err) => {
                                dom.setAttribute("class", "voice_intercom control");
                                this.push_flag = false;
                                this.isAudioFlag = false;
                            });
                        } else {
                            this.push_flag = true;
                            this.isAudioFlag = true;
                            isStartAudio++;
                            dom.setAttribute("class", "video_open control");
                        }
                    }
                })
            } else {
                sendMute(this.dataInfo.imei, false).then(() => {
                    dom.setAttribute("class", "video_open control");
                    this.push_flag = true;
                });
            }
        } else {
            // 关闭对讲（控制设备开启静音）
            sendMute(this.dataInfo.imei, true).then(() => {
                dom.setAttribute("class", "voice_intercom control");
                this.push_flag = false;
            })
        }
    }

    // 记录仪录像按钮
    newVideotapeDiv_click() {
        if (!this.stopTape) {
            sendVideo(this.dataInfo.imei, true).then(() => {
                // dom.setAttribute("class", "voice_intercom control");
                this.stopTape = true;
            })
        } else {
            sendVideo(this.dataInfo.imei, false).then(() => {
                // dom.setAttribute("class", "voice_intercom control");
                this.stopTape = false;
            })
        }
    }

    // 记录仪拍照按钮
    newPhotographDiv_click() {
        sendVideo(this.dataInfo.imei).then(() => {
            // dom.setAttribute("class", "voice_intercom control");
        })
    }

    // 截屏按钮
    newScreenShotDiv_click() {
        var id = this.newPlayerDiv_3.id;
        // 获取视频元素
        const videoElement = document.getElementById(id);

        // 创建一个 <canvas> 元素
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');

        // 设置 canvas 的宽高与视频流一致
        canvas.width = videoElement.videoWidth;
        canvas.height = videoElement.videoHeight;

        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

        const dataUrl = canvas.toDataURL('image/png');

        // 创建一个链接元素并设置其属性
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = '图片.png';

        // 模拟点击链接以触发下载
        link.click();
    }

    startPlayVideo() {
        console.log(this.dataInfo);
        var videoDom = document.getElementById(this.newPlayerDiv_3.id);
        console.log(videoDom);
        var InitialData = {
            element: videoDom,
            debug: true,
            zlmsdpUrl: this.dataInfo.webrtc_url,
            simulcast: false,
            useCamera: false,
            audioEnable: true,
            videoEnable: true,
            recvOnly: true,
            resolution: { w: 3840, h: 2160 },
            usedatachannel: false,
        };
        console.log(InitialData, 'InitialData');
        this.player = new Webrtc(InitialData);
        this.player.start_play().then(() => {
            var voice = document.getElementById(this.newVoiceDiv.id);
            var video = document.getElementById(this.newPlayerDiv_3.id)
            if (voice) {
                console.log("我是来关闭声音");
                voice.setAttribute("class", "voiceClose control");
            }
            video.muted = true;
            this.Volume = true;
            console.log(this.video);

            this.player.on("failed", () => {
                console.log("网络断开");
                this.newCloseDiv_click();
            });
        }).catch(() => {
            this.newCloseDiv_click();
        })
    }

    removeDom() {
        var index = dataExample.findIndex((obj) => {
            return obj.hostbody === this.dataInfo.hostbody; // 查找id属性等于idToRemove的对象的索引
        });
        if (index !== -1) {
            dataExample.splice(index, 1); // 删除找到的对象
        }
        console.log(dataExample, '数组');
        this.newPlayerDiv_2.removeChild(this.newPlayerDiv_3);
        this.newPlayerDiv_1.removeChild(this.newPlayerDiv_2);
        this.newPlayerDiv_1.removeEventListener("dblclick", this.newPlayerDiv_1_dblclick);
        this.newPlayerDiv_1.removeEventListener("mouseover", this.newPlayerDiv_1_mouseover);
        this.newPlayerDiv_1.removeEventListener("mouseout", this.newPlayerDiv_1_mouseout);
        this.newPlayerDiv_1.removeEventListener("mousemove", this.newPlayerDiv_1_mousemove);
        this.newPlayerDiv_0.removeChild(this.newPlayerDiv_1);
        this.newPlayerDiv_4.removeChild(this.newCloseDiv);
        this.newPlayerDiv_4.removeChild(this.newVoiceDiv);
        this.newPlayerDiv_4.removeChild(this.newVoiceIntercomDiv);
        this.newPlayerDiv_4.removeChild(this.newVideotapeDiv);
        this.newPlayerDiv_4.removeChild(this.newPhotographDiv);
        this.newPlayerDiv_4.removeChild(this.newScreenShotDiv);
        this.newPlayerDiv_0.removeChild(this.newPlayerDiv_4);
        this.newPlayerDiv_4.removeEventListener("mouseover", this.newPlayerDiv_4_mouseover);
        this.newPlayerDiv_4.removeEventListener("mouseout", this.newPlayerDiv_4_mouseout);
        this.newCloseDiv.removeEventListener("click", this.newCloseDiv_click);
        this.newVoiceDiv.removeEventListener("click", this.newVoiceDiv_click);
        this.newVoiceIntercomDiv.removeEventListener("click", this.newVoiceIntercomDiv_click);
        this.newVideotapeDiv.removeEventListener("click", this.newVideotapeDiv_click);
        this.newPhotographDiv.removeEventListener("click", this.newPhotographDiv_click);
        this.newScreenShotDiv.removeEventListener("click", this.newScreenShotDiv_click);
        // this.parentDom.removeChild(this.newPlayerDiv_0);
        document.getElementById(this.newPlayerDiv_0.id).parentNode.removeChild(this.newPlayerDiv_0);
    }
}

// 私标对讲
class DynamicElement_audio {
    constructor(parentDom, dataInfo) {
        this.parentDom = parentDom;
        this.dataInfo = dataInfo;
        this.hideTimeout = null;
        // 创建元素
        this.newPlayerDiv_0 = document.createElement("div");
        this.newPlayerDiv_1 = document.createElement("div");
        this.newPlayerDiv_2 = document.createElement("div");
        this.newPlayerDiv_2_1 = document.createElement("div");
        this.newPlayerDiv_3 = document.createElement("audio");
        this.newPlayerDiv_4 = document.createElement("div"); //父级
        this.newCloseDiv = document.createElement("div"); //关闭
        this.newVoiceDiv = document.createElement("div"); //声音
        this.newVoiceIntercomDiv = document.createElement("div"); //麦克风
        this.init = this.init.bind(this);
        this.player = null;
        this.isAudioFlag = false; //判断是否开启对讲
        this.Volume = false; //判断是否开启声音
    }

    init() {
        this.newPlayerDiv_0.className = 'chromeMain';
        this.newPlayerDiv_0.id = 'chromeDiv';
        this.newPlayerDiv_4.id = 'myControls';
        this.newPlayerDiv_4.className = 'myControls';
        this.newCloseDiv.className = 'end control';
        this.newVoiceDiv.className = 'voiceClose control';
        this.newVoiceDiv.id = `voice${this.dataInfo.hostbody}`;
        this.newVoiceIntercomDiv.className = 'voice_intercom control';
        this.newVoiceIntercomDiv.id = `voice_intercom${this.dataInfo.hostbody}`;
        this.newPlayerDiv_1.className = "video_mian";
        this.newPlayerDiv_1.id = `bigBox${this.dataInfo.hostbody}`;
        this.newPlayerDiv_2.className = "playerAudioBox";
        this.newPlayerDiv_2.id = `fullscreen${this.dataInfo.hostbody}`;
        this.newPlayerDiv_2_1.className = 'textName';
        this.newPlayerDiv_2_1.textContent = `${this.dataInfo.hostname? this.dataInfo.hostname: this.dataInfo.hostcode}`;
        this.newPlayerDiv_3.className = "chrome";
        this.newPlayerDiv_3.id = `audio${this.dataInfo.hostbody}`;
        // this.newPlayerDiv_3.src = 'http://vjs.zencdn.net/v/oceans.mp4';
        this.newPlayerDiv_3.controls = true;
        this.newPlayerDiv_3.autoplay = true;
        // this.newPlayerDiv_3.width = 640;
        // this.newPlayerDiv_3.height = 480;

        // 将子节点添加到父节点上
        this.newPlayerDiv_2.appendChild(this.newPlayerDiv_2_1);
        this.newPlayerDiv_2.appendChild(this.newPlayerDiv_3);
        this.newPlayerDiv_1.appendChild(this.newPlayerDiv_2);
        this.newPlayerDiv_4.appendChild(this.newCloseDiv);
        this.newPlayerDiv_4.appendChild(this.newVoiceDiv);
        this.newPlayerDiv_4.appendChild(this.newVoiceIntercomDiv);

        // 添加事件监听器
        // this.newPlayerDiv_1.addEventListener("dblclick", (event)=>  {
        //     console.log("点击了新添加的元素。",event);
        // });
        // 绑定点击事件处理函数的上下文
        this.newPlayerDiv_1_mouseover = this.newPlayerDiv_1_mouseover.bind(this);
        this.newPlayerDiv_1_mouseout = this.newPlayerDiv_1_mouseout.bind(this);
        this.newPlayerDiv_1_mousemove = this.newPlayerDiv_1_mousemove.bind(this);
        this.newPlayerDiv_4_mouseover = this.newPlayerDiv_4_mouseover.bind(this);
        this.newPlayerDiv_4_mouseout = this.newPlayerDiv_4_mouseout.bind(this);
        this.newCloseDiv_click = this.newCloseDiv_click.bind(this);
        this.newVoiceDiv_click = this.newVoiceDiv_click.bind(this);
        this.newVoiceIntercomDiv_click = this.newVoiceIntercomDiv_click.bind(this);
        this.newPlayerDiv_1.addEventListener("mouseover", this.newPlayerDiv_1_mouseover);
        this.newPlayerDiv_1.addEventListener("mouseout", this.newPlayerDiv_1_mouseout);
        this.newPlayerDiv_1.addEventListener("mousemove", this.newPlayerDiv_1_mousemove);
        this.newPlayerDiv_4.addEventListener("mouseover", this.newPlayerDiv_4_mouseover);
        this.newPlayerDiv_4.addEventListener("mouseout", this.newPlayerDiv_4_mouseout);
        this.newCloseDiv.addEventListener("click", this.newCloseDiv_click);
        this.newVoiceDiv.addEventListener("click", this.newVoiceDiv_click);
        this.newVoiceIntercomDiv.addEventListener("click", this.newVoiceIntercomDiv_click);

        // 将div元素添加到容器中
        this.newPlayerDiv_0.appendChild(this.newPlayerDiv_1);
        this.newPlayerDiv_0.appendChild(this.newPlayerDiv_4);
        this.parentDom.appendChild(this.newPlayerDiv_0);

        this.startPlayVideo()
    }

    newPlayerDiv_1_mouseover(event) {
        // console.log("移入元素中",event);
        if (event.currentTarget.id == `bigBox${this.dataInfo.hostbody}` || event.currentTarget.id == "myControls") {
            var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
            if (dom.nextElementSibling) {
                dom.nextElementSibling.style.display = "block";
            }
        }
    }

    newPlayerDiv_1_mouseout(event) {
        // console.log("移出元素中",event);
        var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
        if (dom.nextElementSibling) {
            dom.nextElementSibling.style.display = "none";
        }
    }

    newPlayerDiv_1_mousemove(event) {
        // console.log("在元素中移动",event);
        var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
        clearTimeout(this.hideTimeout);
        if (dom.nextElementSibling) {
            dom.nextElementSibling.style.display = "block";
            dom.style.cursor = "pointer";
        }

        // 重新启动定时器，在2秒后再次隐藏控制栏
        this.hideTimeout = setTimeout(function() {
            if (dom.nextElementSibling) {
                dom.nextElementSibling.style.display = "none";
            }
        }, 2000);
    }

    newPlayerDiv_4_mouseover(event) {
        // console.log("移入元素中",event);
        if (event.currentTarget.id == `bigBox${this.dataInfo.hostbody}` || event.currentTarget.id == "myControls") {
            var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
            if (dom.nextElementSibling) {
                dom.nextElementSibling.style.display = "block";
            }
        }
    }

    newPlayerDiv_4_mouseout(event) {
        // console.log("移出元素中",event);
        var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
        if (dom.nextElementSibling) {
            dom.nextElementSibling.style.display = "none";
        }
    }

    // 关闭拉流
    newCloseDiv_click(event) {
        stopAudio([this.dataInfo.hostbody]).then(() => {
            isStartAudio--;
            this.player.close_play();
            this.player = null;
            if (isStartAudio <= 0) {
                // 开启了对讲
                pcStartAudio.close_play();
                pcStartAudio = null;
            }
            this.removeDom()
        })
    }

    // 声音按钮
    newVoiceDiv_click() {
        console.log("我被点击了");
        var voice = document.getElementById(this.newVoiceDiv.id);
        var video = document.getElementById(this.newPlayerDiv_3.id)
        if (!this.Volume) {
            console.log("我是来关闭声音");
            voice.setAttribute("class", "voiceClose control");
            video.muted = true;
        } else {
            console.log("我是来开启声音");
            voice.setAttribute("class", "voiceOpen control");
            video.muted = false;
        }
        this.Volume = !this.Volume;
    }

    // 麦克风按钮
    newVoiceIntercomDiv_click() {
        var dom = document.getElementById(this.newVoiceIntercomDiv.id)
        if (!this.isAudioFlag) {
            sendMute(this.dataInfo.imei, false).then(() => {
                dom.setAttribute("class", "video_open control");
                this.isAudioFlag = true;
            });
        } else {
            // 关闭对讲（控制设备开启静音）
            sendMute(this.dataInfo.imei, true).then(() => {
                dom.setAttribute("class", "voice_intercom control");
                this.isAudioFlag = false;
            })
        }
    }

    startPlayVideo() {
        console.log(this.dataInfo);
        var videoDom = document.getElementById(this.newPlayerDiv_3.id);
        var boxs = document.getElementById(this.newPlayerDiv_2.id);
        // var boxs = document.getElementById(this.newPlayerDiv_0.id);
        console.log(videoDom);
        var InitialData = {
            element: videoDom,
            debug: true,
            zlmsdpUrl: this.dataInfo.webrtc_url,
            simulcast: false,
            useCamera: false,
            audioEnable: true,
            videoEnable: false,
            recvOnly: true,
            resolution: { w: 3840, h: 2160 },
            usedatachannel: false,
        };
        this.player = new Webrtc(InitialData);
        this.player.start_play().then(() => {
            isStartAudio++;
            boxs.setAttribute("class", "boxs box_audio_bg"); //拉起流显示背景
            var voice = document.getElementById(this.newVoiceDiv.id);
            var video = document.getElementById(this.newVoiceIntercomDiv.id)
            if (voice) {
                console.log("我是来关闭声音");
                voice.setAttribute("class", "voiceOpen control");
            }
            if (video) {
                console.log("我是来开启声音");
                video.setAttribute("class", "video_open control");
            }
            videoDom.muted = false;
            this.Volume = false;

            this.player.on("failed", () => {
                console.log("设备网络断开");
                this.newCloseDiv_click();
            });
        }).catch(() => {
            this.newCloseDiv_click();
        })
    }

    removeDom() {
        var index = dataExample.findIndex((obj) => {
            return obj.hostbody === this.dataInfo.hostbody; // 查找id属性等于idToRemove的对象的索引
        });
        if (index !== -1) {
            dataExample.splice(index, 1); // 删除找到的对象
        }
        console.log(dataExample, '数组');
        this.newPlayerDiv_2.removeChild(this.newPlayerDiv_3);
        this.newPlayerDiv_1.removeChild(this.newPlayerDiv_2);
        this.newPlayerDiv_1.removeEventListener("mouseover", this.newPlayerDiv_1_mouseover);
        this.newPlayerDiv_1.removeEventListener("mouseout", this.newPlayerDiv_1_mouseout);
        this.newPlayerDiv_1.removeEventListener("mousemove", this.newPlayerDiv_1_mousemove);
        this.newPlayerDiv_0.removeChild(this.newPlayerDiv_1);
        this.newPlayerDiv_4.removeChild(this.newCloseDiv);
        this.newPlayerDiv_4.removeChild(this.newVoiceDiv);
        this.newPlayerDiv_4.removeChild(this.newVoiceIntercomDiv);
        this.newPlayerDiv_0.removeChild(this.newPlayerDiv_4);
        this.newPlayerDiv_4.removeEventListener("mouseover", this.newPlayerDiv_4_mouseover);
        this.newPlayerDiv_4.removeEventListener("mouseout", this.newPlayerDiv_4_mouseout);
        this.newCloseDiv.removeEventListener("click", this.newCloseDiv_click);
        this.newVoiceDiv.removeEventListener("click", this.newVoiceDiv_click);
        this.newVoiceIntercomDiv.removeEventListener("click", this.newVoiceIntercomDiv_click);
        // this.parentDom.removeChild(this.newPlayerDiv_0);
        document.getElementById(this.newPlayerDiv_0.id).parentNode.removeChild(this.newPlayerDiv_0);
    }
}

// 私标双人执法
class DynamicElement_talk {
    constructor(parentDom, dataInfo) {
        this.parentDom = parentDom;
        this.dataInfo = dataInfo;
        this.hideTimeout = null;
        // 创建元素
        this.newPlayerDiv_0 = document.createElement("div");
        this.newPlayerDiv_1 = document.createElement("div");
        this.newPlayerDiv_2 = document.createElement("div");
        this.newPlayerDiv_3_0 = document.createElement("div");
        this.newPlayerDiv_3_0_0 = document.createElement("div");
        this.newPlayerDiv_3_0_1 = document.createElement("video");
        this.newPlayerDiv_3_1 = document.createElement("div");
        this.newPlayerDiv_3_1_0 = document.createElement("div");
        this.newPlayerDiv_3_1_1 = document.createElement("video");
        this.newPlayerDiv_4 = document.createElement("div"); //父级
        this.newCloseDiv = document.createElement("div"); //关闭
        this.newVoiceDiv = document.createElement("div"); //声音
        this.newVoiceIntercomDiv = document.createElement("div"); //麦克风
        this.newVideotapeDiv = document.createElement("div"); //记录仪录像
        this.newPhotographDiv = document.createElement("div"); //记录仪拍照
        this.newScreenShotDiv = document.createElement("div"); //截屏
        this.newScreenOpenIsTwo = document.createElement("div"); //双人执法标识
        this.init = this.init.bind(this);
        this.player = null;
        this.isAudioFlag = false; //判断是否开启设备静音
        this.Volume = false; //判断是否开启声音
        this.stopTape = false; //判断是否开启录像
        this.webrtc_audio = null;

        this.isTwo = false; // 是否显示摄像头
    }

    init() {
        console.log(this.dataInfo);
        this.newPlayerDiv_0.className = 'chromeMain';
        this.newPlayerDiv_0.id = `chromeDiv${this.dataInfo.hostbody}`;
        this.newPlayerDiv_0.setAttribute("data-node", `${this.dataInfo.hostbody}`);
        this.newPlayerDiv_4.id = 'myControls';
        this.newPlayerDiv_4.className = 'myControls';
        this.newCloseDiv.className = 'end control';
        this.newVoiceDiv.className = 'voiceClose control';
        this.newVoiceDiv.id = `voice${this.dataInfo.hostbody}`;
        this.newVoiceIntercomDiv.className = 'voice_intercom control';
        this.newVoiceIntercomDiv.id = `voice_intercom${this.dataInfo.hostbody}`;
        this.newVideotapeDiv.className = 'videotape control';
        this.newPhotographDiv.className = 'photograph control';
        this.newScreenShotDiv.className = 'screenshot control';
        this.newScreenOpenIsTwo.className = 'control';
        this.newScreenOpenIsTwo.id = `isTwo${this.dataInfo.hostbody}`;
        this.newPlayerDiv_1.className = "video_mian";
        this.newPlayerDiv_1.id = `bigBox${this.dataInfo.hostbody}`;
        this.newPlayerDiv_2.className = "playerVideoBox";
        this.newPlayerDiv_2.id = `fullscreen${this.dataInfo.hostbody}`;
        this.newPlayerDiv_3_0.className = "videoOne";
        this.newPlayerDiv_3_0.id = `videoFirst${this.dataInfo.hostbody}`;

        this.newPlayerDiv_3_0_0.id = `id1${this.dataInfo.hostbody}`;

        this.newPlayerDiv_3_0_1.className = "chrome";
        this.newPlayerDiv_3_0_1.id = `video1${this.dataInfo.hostbody}`;
        // this.newPlayerDiv_3_0_1.src = 'http://vjs.zencdn.net/v/oceans.mp4';
        this.newPlayerDiv_3_0_1.controls = true;
        this.newPlayerDiv_3_0_1.autoplay = true;
        this.newPlayerDiv_3_0_1.width = 640;
        this.newPlayerDiv_3_0_1.height = 480;

        this.newPlayerDiv_3_1.className = "videoTwo";
        this.newPlayerDiv_3_1.id = `videoSecond${this.dataInfo.hostbody}`;

        this.newPlayerDiv_3_1_0.id = `id1${this.dataInfo.hostbody}`;

        this.newPlayerDiv_3_1_1.className = "video_two";
        this.newPlayerDiv_3_1_1.id = `video${this.dataInfo.hostbody}`;
        // this.newPlayerDiv_3_1_1.src = 'http://vjs.zencdn.net/v/oceans.mp4';
        this.newPlayerDiv_3_1_1.controls = true;
        this.newPlayerDiv_3_1_1.autoplay = true;
        this.newPlayerDiv_3_1_1.width = 640;
        this.newPlayerDiv_3_1_1.height = 480;

        // 将子节点添加到父节点上
        this.newPlayerDiv_3_1.appendChild(this.newPlayerDiv_3_1_0);
        this.newPlayerDiv_3_1.appendChild(this.newPlayerDiv_3_1_1);
        this.newPlayerDiv_3_0.appendChild(this.newPlayerDiv_3_0_0);
        this.newPlayerDiv_3_0.appendChild(this.newPlayerDiv_3_0_1);
        this.newPlayerDiv_2.appendChild(this.newPlayerDiv_3_0);
        this.newPlayerDiv_2.appendChild(this.newPlayerDiv_3_1);
        this.newPlayerDiv_1.appendChild(this.newPlayerDiv_2);
        this.newPlayerDiv_4.appendChild(this.newCloseDiv);
        this.newPlayerDiv_4.appendChild(this.newVoiceDiv);
        this.newPlayerDiv_4.appendChild(this.newVoiceIntercomDiv);
        this.newPlayerDiv_4.appendChild(this.newVideotapeDiv);
        this.newPlayerDiv_4.appendChild(this.newPhotographDiv);
        this.newPlayerDiv_4.appendChild(this.newScreenShotDiv);
        this.newPlayerDiv_4.appendChild(this.newScreenOpenIsTwo);

        // 添加事件监听器
        // 绑定点击事件处理函数的上下文
        this.newPlayerDiv_1_dblclick = this.newPlayerDiv_1_dblclick.bind(this);
        this.newPlayerDiv_1_mouseover = this.newPlayerDiv_1_mouseover.bind(this);
        this.newPlayerDiv_1_mouseout = this.newPlayerDiv_1_mouseout.bind(this);
        this.newPlayerDiv_1_mousemove = this.newPlayerDiv_1_mousemove.bind(this);
        this.newPlayerDiv_4_mouseover = this.newPlayerDiv_4_mouseover.bind(this);
        this.newPlayerDiv_4_mouseout = this.newPlayerDiv_4_mouseout.bind(this);
        this.newCloseDiv_click = this.newCloseDiv_click.bind(this);
        this.newVoiceDiv_click = this.newVoiceDiv_click.bind(this);
        this.newVoiceIntercomDiv_click = this.newVoiceIntercomDiv_click.bind(this);
        this.newVideotapeDiv_click = this.newVideotapeDiv_click.bind(this);
        this.newPhotographDiv_click = this.newPhotographDiv_click.bind(this);
        this.newScreenShotDiv_click = this.newScreenShotDiv_click.bind(this);
        this.newScreenOpenIsTwo_click = this.newScreenOpenIsTwo_click.bind(this);
        this.newPlayerDiv_3_1_click = this.newPlayerDiv_3_1_click.bind(this);
        this.newPlayerDiv_1.addEventListener("dblclick", this.newPlayerDiv_1_dblclick);
        this.newPlayerDiv_1.addEventListener("mouseover", this.newPlayerDiv_1_mouseover);
        this.newPlayerDiv_1.addEventListener("mouseout", this.newPlayerDiv_1_mouseout);
        this.newPlayerDiv_1.addEventListener("mousemove", this.newPlayerDiv_1_mousemove);
        this.newPlayerDiv_4.addEventListener("mouseover", this.newPlayerDiv_4_mouseover);
        this.newPlayerDiv_4.addEventListener("mouseout", this.newPlayerDiv_4_mouseout);
        this.newPlayerDiv_3_1.addEventListener("click", this.newPlayerDiv_3_1_click);
        this.newCloseDiv.addEventListener("click", this.newCloseDiv_click);
        this.newVoiceDiv.addEventListener("click", this.newVoiceDiv_click);
        this.newVoiceIntercomDiv.addEventListener("click", this.newVoiceIntercomDiv_click);
        this.newVideotapeDiv.addEventListener("click", this.newVideotapeDiv_click);
        this.newPhotographDiv.addEventListener("click", this.newPhotographDiv_click);
        this.newScreenShotDiv.addEventListener("click", this.newScreenShotDiv_click);
        this.newScreenOpenIsTwo.addEventListener("click", this.newScreenOpenIsTwo_click);

        // 将div元素添加到容器中
        this.newPlayerDiv_0.appendChild(this.newPlayerDiv_1);
        this.newPlayerDiv_0.appendChild(this.newPlayerDiv_4);
        this.parentDom.appendChild(this.newPlayerDiv_0);

        this.initPlay()
    }

    getCamera() {
        return new Promise((resolve, reject) => {
            if (isStartVideo == 0) {
                // 已经获取到了就不用再获取了
                var InitialData = {
                    element: "",
                    debug: true,
                    zlmsdpUrl: pcStartVideoUrl,
                    simulcast: false,
                    useCamera: true,
                    audioEnable: true,
                    videoEnable: true,
                    recvOnly: false,
                    resolution: { w: 1920, h: 1080 }, //采集
                    usedatachannel: false,
                };
                pcStartVideo = new Webrtc(InitialData);
                pcStartVideo.start_play().then((url) => {
                    pcStartVideoWebRtcUrl = url;
                    isStartVideo++;
                    pcStartVideo.on("failed", () => {
                        console.log("网络断开");
                        // this.abnormalShutdown();
                    });
                    pcStartVideo.on("disconnected", () => {
                        console.log("网络信号不好");
                    });
                    console.log('采集成功');
                    resolve({ type: true });
                }).catch((err) => {
                    reject({ type: false, code: 503 });
                });
            } else {
                isStartVideo++;
                resolve({ type: true });
            }
        });
    }

    newPlayerDiv_1_dblclick(e) {
        console.log("点击了新添加的元素。", e);
        console.log(e.currentTarget.parentElement.id);
        var elVideo = document.getElementById(e.currentTarget.id).parentElement;
        console.log(elVideo);
        elVideo.οndblclick = "return false;";
        var isFull = !!(
            document.webkitIsFullScreen ||
            document.mozFullScreen ||
            document.msFullscreenElement ||
            document.fullscreenElement
        );
        var iselVideo = !!(
            elVideo.webkitIsFullScreen ||
            elVideo.mozFullScreen ||
            elVideo.msFullscreenElement ||
            elVideo.fullscreenElement
        );
        console.log(isFull, iselVideo);
        if (!isFull) {
            var fullscreenEle =
                document.fullscreenElement ||
                document.mozFullScreenElement ||
                document.webkitFullscreenElement;
            //注意：要在用户授权全屏后才能获取全屏的元素，否则 fullscreenEle为null
            console.log("全屏元素1：" + fullscreenEle);
            // 判断是否是全屏
            if (elVideo.requestFullscreen) {
                console.log(1);
                elVideo.requestFullscreen();
            }
            // FireFox
            else if (elVideo.mozRequestFullscreen) {
                console.log(2);
                elVideo.mozRequestFullscreen();
            }
            // Chrome等
            else if (elVideo.webkitRequestFullscreen) {
                console.log(3);
                elVideo.webkitRequestFullscreen();
            }
        } else {
            var fullscreenEle =
                document.fullscreenElement ||
                document.mozFullScreenElement ||
                document.webkitFullscreenElement;
            //注意：要在用户授权全屏后才能获取全屏的元素，否则 fullscreenEle为null
            console.log("全屏元素2：" + fullscreenEle.id);
            // e.currentTarget.id e.currentTarget.parentElement.id
            if (fullscreenEle.id == e.currentTarget.parentElement.id) {
                if (document.exitFullscreen) {
                    console.log(4);
                    document.exitFullscreen();
                } else if (document.msExitFullscreen) {
                    console.log(5);
                    document.msExitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    console.log(6);
                    document.mozCancelFullScreen();
                } else if (document.webkitExitFullscreen) {
                    console.log(7);
                    document.webkitExitFullscreen();
                }
            } else {
                var fullscreenEle =
                    document.fullscreenElement ||
                    document.mozFullScreenElement ||
                    document.webkitFullscreenElement;
                //注意：要在用户授权全屏后才能获取全屏的元素，否则 fullscreenEle为null
                console.log("全屏元素1：" + fullscreenEle);
                // 判断是否是全屏
                if (elVideo.requestFullscreen) {
                    console.log(1);
                    elVideo.requestFullscreen();
                }
                // FireFox
                else if (elVideo.mozRequestFullscreen) {
                    console.log(2);
                    elVideo.mozRequestFullscreen();
                }
                // Chrome等
                else if (elVideo.webkitRequestFullscreen) {
                    console.log(3);
                    elVideo.webkitRequestFullscreen();
                }
            }
        }
    }

    newPlayerDiv_1_mouseover(event) {
        // console.log("移入元素中",event);
        if (event.currentTarget.id == `bigBox${this.dataInfo.hostbody}` || event.currentTarget.id == "myControls") {
            var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
            if (dom.nextElementSibling) {
                dom.nextElementSibling.style.display = "block";
            }
        }
    }

    newPlayerDiv_1_mouseout(event) {
        // console.log("移出元素中",event);
        var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
        if (dom.nextElementSibling) {
            dom.nextElementSibling.style.display = "none";
        }
    }

    newPlayerDiv_1_mousemove(event) {
        // console.log("在元素中移动",event);
        var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
        clearTimeout(this.hideTimeout);
        if (dom.nextElementSibling) {
            dom.nextElementSibling.style.display = "block";
            dom.style.cursor = "pointer";
        }

        // 重新启动定时器，在2秒后再次隐藏控制栏
        this.hideTimeout = setTimeout(function() {
            if (dom.nextElementSibling) {
                dom.nextElementSibling.style.display = "none";
            }
        }, 2000);
    }

    newPlayerDiv_4_mouseover(event) {
        // console.log("移入元素中",event);
        if (event.currentTarget.id == `bigBox${this.dataInfo.hostbody}` || event.currentTarget.id == "myControls") {
            var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
            if (dom.nextElementSibling) {
                dom.nextElementSibling.style.display = "block";
            }
        }
    }

    newPlayerDiv_4_mouseout(event) {
        // console.log("移出元素中",event);
        var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
        if (dom.nextElementSibling) {
            dom.nextElementSibling.style.display = "none";
        }
    }

    // 大小画面切换
    newPlayerDiv_3_1_click() {
        var bjNode = document.getElementById(this.newPlayerDiv_3_0.id).children[1];
        var bjNode0 = document.getElementById(this.newPlayerDiv_3_0.id).children[0];
        var rlNode = document.getElementById(this.newPlayerDiv_3_1.id).children[1];
        var rlNode0 = document.getElementById(this.newPlayerDiv_3_1.id).children[0];
        console.log(bjNode, rlNode, bjNode0, rlNode0);
        this.swap(bjNode, rlNode, bjNode0, rlNode0);
    }

    swap(aNode, bNode, aNode0, bNode0) {
        var aParent = aNode.parentNode;
        var bParent = bNode.parentNode;
        var aNode2 = aNode.cloneNode(true); //拷贝
        var aNode_id = aNode0.cloneNode(true); //拷贝
        var bNode_id = bNode0.cloneNode(true); //拷贝

        bParent.replaceChild(aNode, bNode0); //替换节点 前者新 后者旧
        aParent.replaceChild(bNode, aNode0);
        aParent.prepend(aNode_id);
        bParent.prepend(bNode_id);
    }

    // 关闭拉流
    newCloseDiv_click(event) {
        let send = {
            status: 0,
            callId: this.dataInfo.callId,
        };
        stopTalk(send).then(() => {
            var videoDom = document.getElementById(this.newPlayerDiv_3_0_1.id);
            var VideoTwo = document.getElementById(this.newPlayerDiv_3_1_1.id);
            this.player.close_play(videoDom).then(() => {
                VideoTwo.srcObject = null;
                VideoTwo.load();
            });
            isStartVideo--;
            if (isStartVideo == 0) {
                pcStartVideo.close_play()
                pcStartVideo = null;
                pcStartVideoUrl = null;
                pcStartVideoWebRtcUrl = null;
            }
            this.removeDom()
        })
    }

    // 声音按钮
    newVoiceDiv_click() {
        console.log("我被点击了");
        var voice = document.getElementById(this.newVoiceDiv.id);
        var video = document.getElementById(this.newPlayerDiv_3_0_1.id)
        if (!this.Volume) {
            console.log("我是来关闭声音");
            voice.setAttribute("class", "voiceClose control");
            video.muted = true;
        } else {
            console.log("我是来开启声音");
            voice.setAttribute("class", "voiceOpen control");
            video.muted = false;
        }
        this.Volume = !this.Volume;
    }

    // 麦克风按钮
    newVoiceIntercomDiv_click() {
        var domId = document.getElementById(this.newVoiceIntercomDiv.id);
        if (this.isAudioFlag) {
            sendMute(this.dataInfo.imei, false).then((res) => {
                console.log(res);
                domId.setAttribute("class", "video_open control");
                this.isAudioFlag = false;
            });
        } else {
            sendMute(this.dataInfo.imei, true).then((res) => {
                console.log(res);
                domId.setAttribute("class", "voice_intercom control");
                this.isAudioFlag = true;
            });
        }
    }

    // 记录仪录像按钮
    newVideotapeDiv_click() {
        if (!this.stopTape) {
            sendVideo(this.dataInfo.imei, true).then(() => {
                // dom.setAttribute("class", "voice_intercom control");
                this.stopTape = true;
            })
        } else {
            sendVideo(this.dataInfo.imei, false).then(() => {
                // dom.setAttribute("class", "voice_intercom control");
                this.stopTape = false;
            })
        }
    }

    // 记录仪拍照按钮
    newPhotographDiv_click() {
        sendVideo(this.dataInfo.imei).then(() => {
            // dom.setAttribute("class", "voice_intercom control");
        })
    }

    // 截屏按钮
    newScreenShotDiv_click() {
        var id = this.newPlayerDiv_3_0.id;
        var minId = this.newPlayerDiv_3_1.id;
        if (this.isTwo) {
            // 获取视频元素
            const videoElement = document.getElementById(id).children[1];

            // 创建一个 <canvas> 元素
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');

            // 设置 canvas 的宽高与视频流一致
            canvas.width = videoElement.videoWidth;
            canvas.height = videoElement.videoHeight;

            context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

            // 获取视频元素
            const videoElement_min = document.getElementById(minId).children[1];

            context.drawImage(videoElement_min, (canvas.width / 3) * 2, 0, canvas.width / 3, canvas.width * videoElement_min.videoHeight / (3 * videoElement_min.videoWidth));

            const dataUrl = canvas.toDataURL('image/png');

            // 创建一个链接元素并设置其属性
            const link = document.createElement('a');
            link.href = dataUrl;
            link.download = '图片.png';

            // 模拟点击链接以触发下载
            link.click();
        } else {
            // 获取视频元素
            const videoElement = document.getElementById(id).children[1];

            // 创建一个 <canvas> 元素
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');

            // 设置 canvas 的宽高与视频流一致
            canvas.width = videoElement.videoWidth;
            canvas.height = videoElement.videoHeight;

            context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

            const dataUrl = canvas.toDataURL('image/png');

            // 创建一个链接元素并设置其属性
            const link = document.createElement('a');
            link.href = dataUrl;
            link.download = '图片.png';

            // 模拟点击链接以触发下载
            link.click();
        }
    }

    // 双人执法窗口展示
    newScreenOpenIsTwo_click() {
        var dom = document.getElementById(this.newScreenOpenIsTwo.id)
        var videoDom = document.getElementById(this.newPlayerDiv_3_1.id)
        if (this.isTwo) {
            dom.className = 'control voice_isTwo_open';
            videoDom.className = 'control videoTwo_close';
            this.isTwo = false;
        } else {
            dom.className = 'control voice_isTwo_close';
            videoDom.className = 'control videoTwo';
            this.isTwo = true;
        }
    }

    // 双人执法初始
    initPlay() {
        this.getCamera().then((obj) => {
            console.log(obj);
            if (obj.type) {
                this.getIsTwo().then(() => {
                    var VideoTwo = document.getElementById(this.newPlayerDiv_3_1_1.id);
                    console.log(VideoTwo);
                    this.playPlatform(VideoTwo).then(() => {
                        this.startPlayVideo()
                    }).catch((err) => {
                        console.log(err);
                        let send = {
                            status: 4,
                            callId: this.dataInfo.callId,
                        };
                        stopTalk(send).then(() => {
                            this.removeDom()
                        })
                    })
                })
            } else {
                let send = {
                    status: 4,
                    callId: this.dataInfo.callId,
                };
                stopTalk(send).then(() => {
                    this.removeDom()
                })
            }
        })
    }

    // 是否显示摄像头
    getIsTwo() {
        return new Promise((resolve, reject) => {
            getPlayerSetting().then((res) => {
                var dom = document.getElementById(this.newScreenOpenIsTwo.id)
                var videoDom = document.getElementById(this.newPlayerDiv_3_1.id)
                console.log(res, "---------------");
                if (res.data.assist_enforcer_picture_in_picture == 1) {
                    this.isTwo = true;
                    dom.className = 'control voice_isTwo_close';
                    videoDom.className = 'control videoTwo';
                    resolve();
                } else {
                    this.isTwo = false;
                    dom.className = 'control voice_isTwo_open'
                    videoDom.className = 'control videoTwo_close';
                    resolve();
                }
            });
        });
    }

    playPlatform(dom) {
        return new Promise((resolve, reject) => {
            console.log(dom);
            // var boxs = document.getElementById(this.ids);
            // console.log(boxs);
            // boxs.setAttribute("class", "boxs boxs_color"); //拉流的时候不要显示背景
            // 指挥台画面
            if (pcStartVideoWebRtcUrl != null) {
                dom.srcObject = pcStartVideoWebRtcUrl;
                dom.muted = true; //双人执法，采集平台声音，但是平台播放默认不放声音，故此静音
                resolve();
            } else {
                reject();
            }
        });
    }

    startPlayVideo() {
        console.log(this.dataInfo);
        var videoDom = document.getElementById(this.newPlayerDiv_3_0_1.id);
        console.log(videoDom);
        var InitialData = {
            element: videoDom,
            debug: true,
            zlmsdpUrl: this.dataInfo.webrtc_url,
            simulcast: false,
            useCamera: false,
            audioEnable: true,
            videoEnable: true,
            recvOnly: true,
            resolution: { w: 3840, h: 2160 },
            usedatachannel: false,
        };
        this.player = new Webrtc(InitialData);
        this.player.start_play().then(() => {
            var voice = document.getElementById(this.newVoiceDiv.id);
            var voiceIntercom = document.getElementById(this.newVoiceIntercomDiv.id);
            var video = document.getElementById(this.newPlayerDiv_3_0_1.id)
            if (voiceIntercom) {
                voiceIntercom.setAttribute("class", "video_open control");
            }
            if (voice) {
                console.log("我是来关闭声音");
                voice.setAttribute("class", "voiceOpen control");
            }
            video.muted = false;
            this.Volume = false;
            console.log(this.video);

            this.player.on("failed", () => {
                console.log("网络断开");
                this.newCloseDiv_click();
            });
        }).catch((err) => {
            console.log(err);
            this.newCloseDiv_click();
        })
    }

    removeDom() {
        var index = dataExample.findIndex((obj) => {
            return obj.callId === this.dataInfo.callId; // 查找id属性等于idToRemove的对象的索引
        });
        if (index !== -1) {
            dataExample.splice(index, 1); // 删除找到的对象
        }
        console.log(dataExample, '数组');
        // this.newPlayerDiv_3_1.removeChild(this.newPlayerDiv_3_1_0);
        // this.newPlayerDiv_3_1.removeChild(this.newPlayerDiv_3_1_1);
        // this.newPlayerDiv_3_0.removeChild(this.newPlayerDiv_3_0_0);
        // this.newPlayerDiv_3_0.removeChild(this.newPlayerDiv_3_0_1);
        this.newPlayerDiv_2.removeChild(this.newPlayerDiv_3_0);
        this.newPlayerDiv_2.removeChild(this.newPlayerDiv_3_1);
        this.newPlayerDiv_1.removeChild(this.newPlayerDiv_2);
        this.newPlayerDiv_4.removeChild(this.newCloseDiv);
        this.newPlayerDiv_4.removeChild(this.newVoiceDiv);
        this.newPlayerDiv_4.removeChild(this.newVoiceIntercomDiv);
        this.newPlayerDiv_4.removeChild(this.newVideotapeDiv);
        this.newPlayerDiv_4.removeChild(this.newPhotographDiv);
        this.newPlayerDiv_4.removeChild(this.newScreenShotDiv);
        this.newPlayerDiv_1.removeEventListener("dblclick", this.newPlayerDiv_1_dblclick);
        this.newPlayerDiv_1.removeEventListener("mouseover", this.newPlayerDiv_1_mouseover);
        this.newPlayerDiv_1.removeEventListener("mouseout", this.newPlayerDiv_1_mouseout);
        this.newPlayerDiv_1.removeEventListener("mousemove", this.newPlayerDiv_1_mousemove);
        this.newPlayerDiv_4.removeEventListener("mouseover", this.newPlayerDiv_4_mouseover);
        this.newPlayerDiv_4.removeEventListener("mouseout", this.newPlayerDiv_4_mouseout);
        this.newCloseDiv.removeEventListener("click", this.newCloseDiv_click);
        this.newVoiceDiv.removeEventListener("click", this.newVoiceDiv_click);
        this.newVoiceIntercomDiv.removeEventListener("click", this.newVoiceIntercomDiv_click);
        this.newVideotapeDiv.removeEventListener("click", this.newVideotapeDiv_click);
        this.newPhotographDiv.removeEventListener("click", this.newPhotographDiv_click);
        this.newScreenShotDiv.removeEventListener("click", this.newScreenShotDiv_click);
        this.newScreenOpenIsTwo.removeEventListener("click", this.newScreenOpenIsTwo_click);
        this.newPlayerDiv_0.removeChild(this.newPlayerDiv_1);
        this.newPlayerDiv_0.removeChild(this.newPlayerDiv_4);
        // this.parentDom.removeChild(this.newPlayerDiv_0);
        document.getElementById(this.newPlayerDiv_0.id).parentNode.removeChild(this.newPlayerDiv_0);
    }
}

// 国标拉流
class DynamicElement_gb {
    constructor(parentDom, dataInfo) {
        this.parentDom = parentDom;
        this.dataInfo = dataInfo;
        this.hideTimeout = null;
        // 创建元素
        this.newPlayerDiv_0 = document.createElement("div");
        this.newPlayerDiv_1 = document.createElement("div");
        this.newPlayerDiv_2 = document.createElement("div");
        this.newPlayerDiv_3 = document.createElement("video");
        this.newPlayerDiv_4 = document.createElement("div"); //父级
        this.newCloseDiv = document.createElement("div"); //关闭
        this.newVoiceDiv = document.createElement("div"); //声音
        this.newVoiceIntercomDiv = document.createElement("div"); //麦克风
        this.newScreenShotDiv = document.createElement("div"); //截屏
        this.init = this.init.bind(this);
        this.player = null;
        this.isAudioFlag = false; //判断是否开启对讲
        this.Volume = false; //判断是否开启声音
        this.webrtc_audio = null;
    }

    init() {
        this.newPlayerDiv_0.className = 'chromeMain';
        this.newPlayerDiv_0.id = `chromeDiv${this.dataInfo.hostbody}`;
        this.newPlayerDiv_4.id = 'myControls';
        this.newPlayerDiv_4.className = 'myControls';
        this.newCloseDiv.className = 'end control';
        this.newVoiceDiv.className = 'voiceClose control';
        this.newVoiceDiv.id = `voice${this.dataInfo.hostbody}`;
        this.newVoiceIntercomDiv.className = 'voice_intercom control';
        this.newVoiceIntercomDiv.id = `voice_intercom${this.dataInfo.hostbody}`;
        this.newScreenShotDiv.className = 'screenshot control';
        this.newPlayerDiv_1.className = "video_mian";
        this.newPlayerDiv_1.id = `bigBox${this.dataInfo.hostbody}`;
        this.newPlayerDiv_2.className = "playerVideoBox";
        this.newPlayerDiv_2.id = `fullscreen${this.dataInfo.hostbody}`;
        this.newPlayerDiv_3.className = "chrome";
        this.newPlayerDiv_3.id = `video${this.dataInfo.hostbody}`;
        // this.newPlayerDiv_3.src = 'http://vjs.zencdn.net/v/oceans.mp4';
        this.newPlayerDiv_3.controls = true;
        this.newPlayerDiv_3.autoplay = true;
        this.newPlayerDiv_3.width = 640;
        this.newPlayerDiv_3.height = 480;

        // 将子节点添加到父节点上
        this.newPlayerDiv_2.appendChild(this.newPlayerDiv_3);
        this.newPlayerDiv_1.appendChild(this.newPlayerDiv_2);
        this.newPlayerDiv_4.appendChild(this.newCloseDiv);
        this.newPlayerDiv_4.appendChild(this.newVoiceDiv);
        this.newPlayerDiv_4.appendChild(this.newVoiceIntercomDiv);
        this.newPlayerDiv_4.appendChild(this.newScreenShotDiv);

        // 添加事件监听器
        // 绑定点击事件处理函数的上下文
        this.newPlayerDiv_1_dblclick = this.newPlayerDiv_1_dblclick.bind(this);
        this.newPlayerDiv_1_mouseover = this.newPlayerDiv_1_mouseover.bind(this);
        this.newPlayerDiv_1_mouseout = this.newPlayerDiv_1_mouseout.bind(this);
        this.newPlayerDiv_1_mousemove = this.newPlayerDiv_1_mousemove.bind(this);
        this.newPlayerDiv_4_mouseover = this.newPlayerDiv_4_mouseover.bind(this);
        this.newPlayerDiv_4_mouseout = this.newPlayerDiv_4_mouseout.bind(this);
        this.newCloseDiv_click = this.newCloseDiv_click.bind(this);
        this.newVoiceDiv_click = this.newVoiceDiv_click.bind(this);
        this.newVoiceIntercomDiv_click = this.newVoiceIntercomDiv_click.bind(this);
        this.newScreenShotDiv_click = this.newScreenShotDiv_click.bind(this);
        this.newPlayerDiv_1.addEventListener("dblclick", this.newPlayerDiv_1_dblclick);
        this.newPlayerDiv_1.addEventListener("mouseover", this.newPlayerDiv_1_mouseover);
        this.newPlayerDiv_1.addEventListener("mouseout", this.newPlayerDiv_1_mouseout);
        this.newPlayerDiv_1.addEventListener("mousemove", this.newPlayerDiv_1_mousemove);
        this.newPlayerDiv_4.addEventListener("mouseover", this.newPlayerDiv_4_mouseover);
        this.newPlayerDiv_4.addEventListener("mouseout", this.newPlayerDiv_4_mouseout);
        this.newCloseDiv.addEventListener("click", this.newCloseDiv_click);
        this.newVoiceDiv.addEventListener("click", this.newVoiceDiv_click);
        this.newVoiceIntercomDiv.addEventListener("click", this.newVoiceIntercomDiv_click);
        this.newScreenShotDiv.addEventListener("click", this.newScreenShotDiv_click);

        // 将div元素添加到容器中
        this.newPlayerDiv_0.appendChild(this.newPlayerDiv_1);
        this.newPlayerDiv_0.appendChild(this.newPlayerDiv_4);
        this.parentDom.appendChild(this.newPlayerDiv_0);

        this.startPlayVideo()
    }
    newPlayerDiv_1_dblclick(e) {
        console.log("点击了新添加的元素。", e);
        console.log(e.currentTarget.parentElement.id);
        var elVideo = document.getElementById(e.currentTarget.id).parentElement;
        console.log(elVideo);
        elVideo.οndblclick = "return false;";
        var isFull = !!(
            document.webkitIsFullScreen ||
            document.mozFullScreen ||
            document.msFullscreenElement ||
            document.fullscreenElement
        );
        var iselVideo = !!(
            elVideo.webkitIsFullScreen ||
            elVideo.mozFullScreen ||
            elVideo.msFullscreenElement ||
            elVideo.fullscreenElement
        );
        console.log(isFull, iselVideo);
        if (!isFull) {
            var fullscreenEle =
                document.fullscreenElement ||
                document.mozFullScreenElement ||
                document.webkitFullscreenElement;
            //注意：要在用户授权全屏后才能获取全屏的元素，否则 fullscreenEle为null
            console.log("全屏元素1：" + fullscreenEle);
            // 判断是否是全屏
            if (elVideo.requestFullscreen) {
                console.log(1);
                elVideo.requestFullscreen();
            }
            // FireFox
            else if (elVideo.mozRequestFullscreen) {
                console.log(2);
                elVideo.mozRequestFullscreen();
            }
            // Chrome等
            else if (elVideo.webkitRequestFullscreen) {
                console.log(3);
                elVideo.webkitRequestFullscreen();
            }
        } else {
            var fullscreenEle =
                document.fullscreenElement ||
                document.mozFullScreenElement ||
                document.webkitFullscreenElement;
            //注意：要在用户授权全屏后才能获取全屏的元素，否则 fullscreenEle为null
            console.log("全屏元素2：" + fullscreenEle.id);
            // e.currentTarget.id e.currentTarget.parentElement.id
            if (fullscreenEle.id == e.currentTarget.parentElement.id) {
                if (document.exitFullscreen) {
                    console.log(4);
                    document.exitFullscreen();
                } else if (document.msExitFullscreen) {
                    console.log(5);
                    document.msExitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    console.log(6);
                    document.mozCancelFullScreen();
                } else if (document.webkitExitFullscreen) {
                    console.log(7);
                    document.webkitExitFullscreen();
                }
            } else {
                var fullscreenEle =
                    document.fullscreenElement ||
                    document.mozFullScreenElement ||
                    document.webkitFullscreenElement;
                //注意：要在用户授权全屏后才能获取全屏的元素，否则 fullscreenEle为null
                console.log("全屏元素1：" + fullscreenEle);
                // 判断是否是全屏
                if (elVideo.requestFullscreen) {
                    console.log(1);
                    elVideo.requestFullscreen();
                }
                // FireFox
                else if (elVideo.mozRequestFullscreen) {
                    console.log(2);
                    elVideo.mozRequestFullscreen();
                }
                // Chrome等
                else if (elVideo.webkitRequestFullscreen) {
                    console.log(3);
                    elVideo.webkitRequestFullscreen();
                }
            }
        }
    }

    newPlayerDiv_1_mouseover(event) {
        // console.log("移入元素中",event);
        if (event.currentTarget.id == `bigBox${this.dataInfo.hostbody}` || event.currentTarget.id == "myControls") {
            var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
            if (dom.nextElementSibling) {
                dom.nextElementSibling.style.display = "block";
            }
        }
    }

    newPlayerDiv_1_mouseout(event) {
        // console.log("移出元素中",event);
        var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
        if (dom.nextElementSibling) {
            dom.nextElementSibling.style.display = "none";
        }
    }

    newPlayerDiv_1_mousemove(event) {
        // console.log("在元素中移动",event);
        var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
        clearTimeout(this.hideTimeout);
        if (dom.nextElementSibling) {
            dom.nextElementSibling.style.display = "block";
            dom.style.cursor = "pointer";
        }

        // 重新启动定时器，在2秒后再次隐藏控制栏
        this.hideTimeout = setTimeout(function() {
            if (dom.nextElementSibling) {
                dom.nextElementSibling.style.display = "none";
            }
        }, 2000);
    }

    newPlayerDiv_4_mouseover(event) {
        // console.log("移入元素中",event);
        if (event.currentTarget.id == `bigBox${this.dataInfo.hostbody}` || event.currentTarget.id == "myControls") {
            var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
            if (dom.nextElementSibling) {
                dom.nextElementSibling.style.display = "block";
            }
        }
    }

    newPlayerDiv_4_mouseout(event) {
        // console.log("移出元素中",event);
        var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
        if (dom.nextElementSibling) {
            dom.nextElementSibling.style.display = "none";
        }
    }

    // 关闭拉流
    newCloseDiv_click(event) {
        if (this.isAudioFlag) {
            stopAudio([this.dataInfo.hostbody]).then(() => {
                isStartAudioNum--;
                pcStartAudioGB = null;
                this.webrtc_audio.close_play();
                this.webrtc_audio = null;
                stopLive(this.dataInfo).then(() => {
                    var videoDom = document.getElementById(`video${this.dataInfo.hostbody}`);
                    this.player.close_play(videoDom);
                    this.player = null;
                    this.removeDom()
                })
            })
        } else {
            stopLive(this.dataInfo).then(() => {
                var videoDom = document.getElementById(`video${this.dataInfo.hostbody}`);
                this.player.close_play(videoDom);
                this.player = null;
                this.removeDom()
            })
        }
    }

    // 声音按钮
    newVoiceDiv_click() {
        console.log("我被点击了");
        var voice = document.getElementById(this.newVoiceDiv.id);
        var video = document.getElementById(this.newPlayerDiv_3.id)
        if (!this.Volume) {
            console.log("我是来关闭声音");
            voice.setAttribute("class", "voiceClose control");
            video.muted = true;
        } else {
            console.log("我是来开启声音");
            voice.setAttribute("class", "voiceOpen control");
            video.muted = false;
        }
        this.Volume = !this.Volume;
    }

    // 麦克风按钮
    newVoiceIntercomDiv_click() {
        var domId = document.getElementById(this.newVoiceIntercomDiv.id);
        if (!this.isAudioFlag) {
            if (isStartAudioNum == 0) {
                this.newStartAudio()
            } else {
                // 先关闭其他窗口的对讲
                stopAudio([pcStartAudioGB.hostbody]).then((res) => {
                    if (res.code == 200) {
                        if (pcStartAudioGB.webrtc) {
                            pcStartAudioGB.webrtc.close_play();
                            pcStartAudioGB.webrtc = null;
                        }
                        var data = dataExample.find((item) => {
                            return item.hostbody == pcStartAudioGB.hostbody
                        })
                        console.log(data);
                        var domId = document.getElementById(data.example
                            .newVoiceIntercomDiv.id);
                        data.example.newVoiceIntercomDiv.isAudioFlag = false;
                        domId.setAttribute("class", "voice_intercom control");
                        pcStartAudioGB = null;
                        isStartAudioNum--;
                        this.newStartAudio()
                    }
                })
            }
        } else {
            stopAudio([this.dataInfo.hostbody]).then((res) => {
                console.log(res);
                if (res.code == 200) {

                } else {
                    // 报错给提示
                    console.log(res.data.err_msg[0]);
                }
                this.webrtc_audio.close_play();
                this.webrtc_audio = null;
                pcStartAudioGB = null;
                isStartAudioNum--;
                domId.setAttribute("class", "voice_intercom control");
                this.isAudioFlag = false;
            });
        }
    }

    newStartAudio() {
        var domId = document.getElementById(this.newVoiceIntercomDiv.id);
        startAudio([this.dataInfo.hostbody]).then((res) => {
            if (res.code == 200) {
                var timer = setInterval(() => {
                    if (wsAudioMessage) {
                        clearInterval(timer);
                        console.log(wsAudioMessage, 'wsAudioMessage');
                        if (wsAudioMessage && wsAudioMessage.start_audio) {
                            const wsAudioMessageCopy = JSON.parse(JSON.stringify(wsAudioMessage));
                            wsAudioMessage = null;
                            if (wsAudioMessageCopy.start_audio.code == 200) {
                                domId.setAttribute("class", "video_open control");
                                var InitialData = {
                                    element: "",
                                    debug: true,
                                    zlmsdpUrl: wsAudioMessageCopy.start_audio.play_info.webrtc_push_url,
                                    simulcast: false,
                                    useCamera: false,
                                    audioEnable: true,
                                    videoEnable: false,
                                    recvOnly: false,
                                    resolution: { w: 3840, h: 2160 },
                                    usedatachannel: false,
                                };
                                this.webrtc_audio = new Webrtc(InitialData);
                                this.webrtc_audio.start_play().then((url) => {
                                    isStartAudioNum++;
                                    pcStartAudioGB = {
                                        hostbody: this.dataInfo.hostbody,
                                        webrtc: this.webrtc_audio
                                    };
                                    this.isAudioFlag = true;
                                    console.log(url);
                                    domId.setAttribute("class", "video_open control");
                                }).catch(() => {
                                    this.webrtc_audio = null;
                                    stopAudio([this.dataInfo.hostbody]).then(() => {
                                        this.isAudioFlag = false;
                                    });
                                })
                            } else {
                                stopAudio([this.dataInfo.hostbody]).then(() => {
                                    this.isAudioFlag = false;
                                });
                            }
                        }
                    }
                }, 10)
            } else {
                this.isAudioFlag = false;
                console.log(res.data.err_msg[0]);
            }
        })
    }

    // 截屏按钮
    newScreenShotDiv_click() {
        var id = this.newPlayerDiv_3.id;
        // 获取视频元素
        const videoElement = document.getElementById(id);

        // 创建一个 <canvas> 元素
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');

        // 设置 canvas 的宽高与视频流一致
        canvas.width = videoElement.videoWidth;
        canvas.height = videoElement.videoHeight;

        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

        const dataUrl = canvas.toDataURL('image/png');

        // 创建一个链接元素并设置其属性
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = '图片.png';

        // 模拟点击链接以触发下载
        link.click();
    }

    startPlayVideo() {
        console.log(this.dataInfo);
        var videoDom = document.getElementById(this.newPlayerDiv_3.id);
        console.log(videoDom);
        var InitialData = {
            element: videoDom,
            debug: true,
            zlmsdpUrl: this.dataInfo.play_info.webrtc_url,
            simulcast: false,
            useCamera: false,
            audioEnable: true,
            videoEnable: true,
            recvOnly: true,
            resolution: { w: 3840, h: 2160 },
            usedatachannel: false,
        };
        console.log(InitialData, 'InitialData');
        this.player = new Webrtc(InitialData);
        this.player.start_play().then(() => {
            var voice = document.getElementById(this.newVoiceDiv.id);
            var video = document.getElementById(this.newPlayerDiv_3.id)
            if (voice) {
                console.log("我是来关闭声音");
                voice.setAttribute("class", "voiceClose control");
            }
            video.muted = true;
            this.Volume = true;
            console.log(this.video);

            this.player.on("failed", () => {
                console.log("网络断开");
                this.newCloseDiv_click();
            });
        }).catch(() => {
            this.newCloseDiv_click();
        })
    }

    removeDom() {
        var index = dataExample.findIndex((obj) => {
            return obj.hostbody === this.dataInfo.hostbody; // 查找id属性等于idToRemove的对象的索引
        });
        if (index !== -1) {
            dataExample.splice(index, 1); // 删除找到的对象
        }
        console.log(dataExample, '数组');
        this.newPlayerDiv_2.removeChild(this.newPlayerDiv_3);
        this.newPlayerDiv_1.removeChild(this.newPlayerDiv_2);
        this.newPlayerDiv_1.removeEventListener("dblclick", this.newPlayerDiv_1_dblclick);
        this.newPlayerDiv_1.removeEventListener("mouseover", this.newPlayerDiv_1_mouseover);
        this.newPlayerDiv_1.removeEventListener("mouseout", this.newPlayerDiv_1_mouseout);
        this.newPlayerDiv_1.removeEventListener("mousemove", this.newPlayerDiv_1_mousemove);
        this.newPlayerDiv_0.removeChild(this.newPlayerDiv_1);
        this.newPlayerDiv_4.removeChild(this.newCloseDiv);
        this.newPlayerDiv_4.removeChild(this.newVoiceDiv);
        this.newPlayerDiv_4.removeChild(this.newVoiceIntercomDiv);
        this.newPlayerDiv_4.removeChild(this.newScreenShotDiv);
        this.newPlayerDiv_0.removeChild(this.newPlayerDiv_4);
        this.newPlayerDiv_4.removeEventListener("mouseover", this.newPlayerDiv_4_mouseover);
        this.newPlayerDiv_4.removeEventListener("mouseout", this.newPlayerDiv_4_mouseout);
        this.newCloseDiv.removeEventListener("click", this.newCloseDiv_click);
        this.newVoiceDiv.removeEventListener("click", this.newVoiceDiv_click);
        this.newVoiceIntercomDiv.removeEventListener("click", this.newVoiceIntercomDiv_click);
        this.newScreenShotDiv.removeEventListener("click", this.newScreenShotDiv_click);
        document.getElementById(this.newPlayerDiv_0.id).parentNode.removeChild(this.newPlayerDiv_0);
        // this.parentDom.removeChild(this.newPlayerDiv_0);
    }
}

// 国标双人执法
class DynamicElement_talk_gb {
    constructor(parentDom, dataInfo) {
        this.parentDom = parentDom;
        this.dataInfo = dataInfo;
        this.hideTimeout = null;
        // 创建元素
        this.newPlayerDiv_0 = document.createElement("div");
        this.newPlayerDiv_1 = document.createElement("div");
        this.newPlayerDiv_2 = document.createElement("div");
        this.newPlayerDiv_3_0 = document.createElement("div");
        this.newPlayerDiv_3_0_0 = document.createElement("div");
        this.newPlayerDiv_3_0_1 = document.createElement("video");
        this.newPlayerDiv_3_1 = document.createElement("div");
        this.newPlayerDiv_3_1_0 = document.createElement("div");
        this.newPlayerDiv_3_1_1 = document.createElement("video");
        this.newPlayerDiv_4 = document.createElement("div"); //父级
        this.newCloseDiv = document.createElement("div"); //关闭
        this.newVoiceDiv = document.createElement("div"); //声音
        this.newVoiceIntercomDiv = document.createElement("div"); //麦克风
        this.newScreenShotDiv = document.createElement("div"); //截屏
        this.newScreenOpenIsTwo = document.createElement("div"); //双人执法标识
        this.init = this.init.bind(this);
        this.player = null;
        this.isAudioFlag = false; //判断是否开启设备静音
        this.Volume = false; //判断是否开启声音
        this.stopTape = false; //判断是否开启录像
        this.webrtc_audio = null;

        this.isTwo = false; // 是否显示摄像头
    }

    init() {
        console.log(this.dataInfo);
        this.newPlayerDiv_0.className = 'chromeMain';
        this.newPlayerDiv_0.id = `chromeDiv${this.dataInfo.hostbody}`;
        this.newPlayerDiv_0.setAttribute("data-node", `${this.dataInfo.hostbody}`);
        this.newPlayerDiv_4.id = 'myControls';
        this.newPlayerDiv_4.className = 'myControls';
        this.newCloseDiv.className = 'end control';
        this.newVoiceDiv.className = 'voiceClose control';
        this.newVoiceDiv.id = `voice${this.dataInfo.hostbody}`;
        this.newVoiceIntercomDiv.className = 'voice_intercom control';
        this.newVoiceIntercomDiv.id = `voice_intercom${this.dataInfo.hostbody}`;
        this.newScreenShotDiv.className = 'screenshot control';
        this.newScreenOpenIsTwo.className = 'control';
        this.newScreenOpenIsTwo.id = `isTwo${this.dataInfo.hostbody}`;
        this.newPlayerDiv_1.className = "video_mian";
        this.newPlayerDiv_1.id = `bigBox${this.dataInfo.hostbody}`;
        this.newPlayerDiv_2.className = "playerVideoBox";
        this.newPlayerDiv_2.id = `fullscreen${this.dataInfo.hostbody}`;
        this.newPlayerDiv_3_0.className = "videoOne";
        this.newPlayerDiv_3_0.id = `videoFirst${this.dataInfo.hostbody}`;

        this.newPlayerDiv_3_0_0.id = `id1${this.dataInfo.hostbody}`;

        this.newPlayerDiv_3_0_1.className = "chrome";
        this.newPlayerDiv_3_0_1.id = `video1${this.dataInfo.hostbody}`;
        // this.newPlayerDiv_3_0_1.src = 'http://vjs.zencdn.net/v/oceans.mp4';
        this.newPlayerDiv_3_0_1.controls = true;
        this.newPlayerDiv_3_0_1.autoplay = true;
        this.newPlayerDiv_3_0_1.width = 640;
        this.newPlayerDiv_3_0_1.height = 480;

        this.newPlayerDiv_3_1.className = "videoTwo";
        this.newPlayerDiv_3_1.id = `videoSecond${this.dataInfo.hostbody}`;

        this.newPlayerDiv_3_1_0.id = `id1${this.dataInfo.hostbody}`;

        this.newPlayerDiv_3_1_1.className = "video_two";
        this.newPlayerDiv_3_1_1.id = `video${this.dataInfo.hostbody}`;
        // this.newPlayerDiv_3_1_1.src = 'http://vjs.zencdn.net/v/oceans.mp4';
        this.newPlayerDiv_3_1_1.controls = true;
        this.newPlayerDiv_3_1_1.autoplay = true;
        this.newPlayerDiv_3_1_1.width = 640;
        this.newPlayerDiv_3_1_1.height = 480;

        // 将子节点添加到父节点上
        this.newPlayerDiv_3_1.appendChild(this.newPlayerDiv_3_1_0);
        this.newPlayerDiv_3_1.appendChild(this.newPlayerDiv_3_1_1);
        this.newPlayerDiv_3_0.appendChild(this.newPlayerDiv_3_0_0);
        this.newPlayerDiv_3_0.appendChild(this.newPlayerDiv_3_0_1);
        this.newPlayerDiv_2.appendChild(this.newPlayerDiv_3_0);
        this.newPlayerDiv_2.appendChild(this.newPlayerDiv_3_1);
        this.newPlayerDiv_1.appendChild(this.newPlayerDiv_2);
        this.newPlayerDiv_4.appendChild(this.newCloseDiv);
        this.newPlayerDiv_4.appendChild(this.newVoiceDiv);
        this.newPlayerDiv_4.appendChild(this.newVoiceIntercomDiv);
        this.newPlayerDiv_4.appendChild(this.newScreenShotDiv);
        this.newPlayerDiv_4.appendChild(this.newScreenOpenIsTwo);

        // 添加事件监听器
        // 绑定点击事件处理函数的上下文
        this.newPlayerDiv_1_dblclick = this.newPlayerDiv_1_dblclick.bind(this);
        this.newPlayerDiv_1_mouseover = this.newPlayerDiv_1_mouseover.bind(this);
        this.newPlayerDiv_1_mouseout = this.newPlayerDiv_1_mouseout.bind(this);
        this.newPlayerDiv_1_mousemove = this.newPlayerDiv_1_mousemove.bind(this);
        this.newPlayerDiv_4_mouseover = this.newPlayerDiv_4_mouseover.bind(this);
        this.newPlayerDiv_4_mouseout = this.newPlayerDiv_4_mouseout.bind(this);
        this.newCloseDiv_click = this.newCloseDiv_click.bind(this);
        this.newVoiceDiv_click = this.newVoiceDiv_click.bind(this);
        this.newVoiceIntercomDiv_click = this.newVoiceIntercomDiv_click.bind(this);
        this.newScreenShotDiv_click = this.newScreenShotDiv_click.bind(this);
        this.newScreenOpenIsTwo_click = this.newScreenOpenIsTwo_click.bind(this);
        this.newPlayerDiv_3_1_click = this.newPlayerDiv_3_1_click.bind(this);
        this.newPlayerDiv_1.addEventListener("dblclick", this.newPlayerDiv_1_dblclick);
        this.newPlayerDiv_1.addEventListener("mouseover", this.newPlayerDiv_1_mouseover);
        this.newPlayerDiv_1.addEventListener("mouseout", this.newPlayerDiv_1_mouseout);
        this.newPlayerDiv_1.addEventListener("mousemove", this.newPlayerDiv_1_mousemove);
        this.newPlayerDiv_4.addEventListener("mouseover", this.newPlayerDiv_4_mouseover);
        this.newPlayerDiv_4.addEventListener("mouseout", this.newPlayerDiv_4_mouseout);
        this.newPlayerDiv_3_1.addEventListener("click", this.newPlayerDiv_3_1_click);
        this.newCloseDiv.addEventListener("click", this.newCloseDiv_click);
        this.newVoiceDiv.addEventListener("click", this.newVoiceDiv_click);
        this.newVoiceIntercomDiv.addEventListener("click", this.newVoiceIntercomDiv_click);
        this.newScreenShotDiv.addEventListener("click", this.newScreenShotDiv_click);
        this.newScreenOpenIsTwo.addEventListener("click", this.newScreenOpenIsTwo_click);

        // 将div元素添加到容器中
        this.newPlayerDiv_0.appendChild(this.newPlayerDiv_1);
        this.newPlayerDiv_0.appendChild(this.newPlayerDiv_4);
        this.parentDom.appendChild(this.newPlayerDiv_0);

        this.initPlay()
    }

    getCamera() {
        return new Promise((resolve, reject) => {
            if (isStartVideo == 0) {
                // 已经获取到了就不用再获取了
                var InitialData = {
                    element: "",
                    debug: true,
                    zlmsdpUrl: pusVideo.play_info.webrtc_push_url,
                    simulcast: false,
                    useCamera: true,
                    audioEnable: true,
                    videoEnable: true,
                    recvOnly: false,
                    resolution: { w: 1920, h: 1080 }, //采集
                    usedatachannel: false,
                };
                pcStartVideo = new Webrtc(InitialData);
                pcStartVideo.start_play().then((url) => {
                    pcStartVideoWebRtcUrl = url;
                    isStartVideo++;
                    pcStartVideo.on("failed", () => {
                        console.log("网络断开");
                        // this.abnormalShutdown();
                    });
                    pcStartVideo.on("disconnected", () => {
                        console.log("网络信号不好");
                    });
                    console.log('采集成功');
                    resolve({ type: true });
                }).catch((err) => {
                    reject({ type: false, code: 503 });
                });
            } else {
                isStartVideo++;
                resolve({ type: true });
            }
        });
    }

    EqWindow(data) {
        return new Promise((resolve, reject) => {
            if (isStartVideo == 0) {
                var InitialData = {
                    element: null,
                    debug: true,
                    zlmsdpUrl: data.play_info.webrtc_url,
                    simulcast: false,
                    useCamera: false,
                    audioEnable: true,
                    videoEnable: true,
                    recvOnly: true,
                    resolution: { w: 3840, h: 2160 }, //采集
                    usedatachannel: false,
                };
                pcStartVideo = new Webrtc(InitialData);
                pcStartVideo.start_play().then((url) => {
                    pcStartVideoWebRtcUrl = url;
                    isStartVideo++;
                    pcStartVideo.on("failed", () => {
                        console.log("网络断开");
                        // this.abnormalShutdown();
                    });
                    pcStartVideo.on("disconnected", () => {
                        console.log("网络信号不好");
                    });
                    console.log('采集成功');
                    resolve({ type: true });
                }).catch((err) => {
                    reject({ type: false, code: 503 });
                });
            }
        })
    }

    newPlayerDiv_1_dblclick(e) {
        console.log("点击了新添加的元素。", e);
        console.log(e.currentTarget.parentElement.id);
        var elVideo = document.getElementById(e.currentTarget.id).parentElement;
        console.log(elVideo);
        elVideo.οndblclick = "return false;";
        var isFull = !!(
            document.webkitIsFullScreen ||
            document.mozFullScreen ||
            document.msFullscreenElement ||
            document.fullscreenElement
        );
        var iselVideo = !!(
            elVideo.webkitIsFullScreen ||
            elVideo.mozFullScreen ||
            elVideo.msFullscreenElement ||
            elVideo.fullscreenElement
        );
        console.log(isFull, iselVideo);
        if (!isFull) {
            var fullscreenEle =
                document.fullscreenElement ||
                document.mozFullScreenElement ||
                document.webkitFullscreenElement;
            //注意：要在用户授权全屏后才能获取全屏的元素，否则 fullscreenEle为null
            console.log("全屏元素1：" + fullscreenEle);
            // 判断是否是全屏
            if (elVideo.requestFullscreen) {
                console.log(1);
                elVideo.requestFullscreen();
            }
            // FireFox
            else if (elVideo.mozRequestFullscreen) {
                console.log(2);
                elVideo.mozRequestFullscreen();
            }
            // Chrome等
            else if (elVideo.webkitRequestFullscreen) {
                console.log(3);
                elVideo.webkitRequestFullscreen();
            }
        } else {
            var fullscreenEle =
                document.fullscreenElement ||
                document.mozFullScreenElement ||
                document.webkitFullscreenElement;
            //注意：要在用户授权全屏后才能获取全屏的元素，否则 fullscreenEle为null
            console.log("全屏元素2：" + fullscreenEle.id);
            // e.currentTarget.id e.currentTarget.parentElement.id
            if (fullscreenEle.id == e.currentTarget.parentElement.id) {
                if (document.exitFullscreen) {
                    console.log(4);
                    document.exitFullscreen();
                } else if (document.msExitFullscreen) {
                    console.log(5);
                    document.msExitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    console.log(6);
                    document.mozCancelFullScreen();
                } else if (document.webkitExitFullscreen) {
                    console.log(7);
                    document.webkitExitFullscreen();
                }
            } else {
                var fullscreenEle =
                    document.fullscreenElement ||
                    document.mozFullScreenElement ||
                    document.webkitFullscreenElement;
                //注意：要在用户授权全屏后才能获取全屏的元素，否则 fullscreenEle为null
                console.log("全屏元素1：" + fullscreenEle);
                // 判断是否是全屏
                if (elVideo.requestFullscreen) {
                    console.log(1);
                    elVideo.requestFullscreen();
                }
                // FireFox
                else if (elVideo.mozRequestFullscreen) {
                    console.log(2);
                    elVideo.mozRequestFullscreen();
                }
                // Chrome等
                else if (elVideo.webkitRequestFullscreen) {
                    console.log(3);
                    elVideo.webkitRequestFullscreen();
                }
            }
        }
    }

    newPlayerDiv_1_mouseover(event) {
        // console.log("移入元素中",event);
        if (event.currentTarget.id == `bigBox${this.dataInfo.hostbody}` || event.currentTarget.id == "myControls") {
            var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
            if (dom.nextElementSibling) {
                dom.nextElementSibling.style.display = "block";
            }
        }
    }

    newPlayerDiv_1_mouseout(event) {
        // console.log("移出元素中",event);
        var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
        if (dom.nextElementSibling) {
            dom.nextElementSibling.style.display = "none";
        }
    }

    newPlayerDiv_1_mousemove(event) {
        // console.log("在元素中移动",event);
        var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
        clearTimeout(this.hideTimeout);
        if (dom.nextElementSibling) {
            dom.nextElementSibling.style.display = "block";
            dom.style.cursor = "pointer";
        }

        // 重新启动定时器，在2秒后再次隐藏控制栏
        this.hideTimeout = setTimeout(function() {
            if (dom.nextElementSibling) {
                dom.nextElementSibling.style.display = "none";
            }
        }, 2000);
    }

    newPlayerDiv_4_mouseover(event) {
        // console.log("移入元素中",event);
        if (event.currentTarget.id == `bigBox${this.dataInfo.hostbody}` || event.currentTarget.id == "myControls") {
            var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
            if (dom.nextElementSibling) {
                dom.nextElementSibling.style.display = "block";
            }
        }
    }

    newPlayerDiv_4_mouseout(event) {
        // console.log("移出元素中",event);
        var dom = document.getElementById(`bigBox${this.dataInfo.hostbody}`);
        if (dom.nextElementSibling) {
            dom.nextElementSibling.style.display = "none";
        }
    }

    // 大小画面切换
    newPlayerDiv_3_1_click() {
        var bjNode = document.getElementById(this.newPlayerDiv_3_0.id).children[1];
        var bjNode0 = document.getElementById(this.newPlayerDiv_3_0.id).children[0];
        var rlNode = document.getElementById(this.newPlayerDiv_3_1.id).children[1];
        var rlNode0 = document.getElementById(this.newPlayerDiv_3_1.id).children[0];
        console.log(bjNode, rlNode, bjNode0, rlNode0);
        this.swap(bjNode, rlNode, bjNode0, rlNode0);
    }

    swap(aNode, bNode, aNode0, bNode0) {
        var aParent = aNode.parentNode;
        var bParent = bNode.parentNode;
        var aNode2 = aNode.cloneNode(true); //拷贝
        var aNode_id = aNode0.cloneNode(true); //拷贝
        var bNode_id = bNode0.cloneNode(true); //拷贝

        bParent.replaceChild(aNode, bNode0); //替换节点 前者新 后者旧
        aParent.replaceChild(bNode, aNode0);
        aParent.prepend(aNode_id);
        bParent.prepend(bNode_id);
    }

    // 关闭拉流
    newCloseDiv_click(event) {
        let send = {
            status: 0,
            callId: this.dataInfo.callId,
        };
        stopTalk(send).then(() => {
            stopLiveGB(this.dataInfo).then((res) => {
                if (push_device_type != 0) {
                    var data = {
                        hostbody: assist_enforcer_hostbody,
                        sn: this.dataInfo.sn
                    }
                    stopLiveGB(data)
                }
                var videoDom = document.getElementById(this.newPlayerDiv_3_0_1.id);
                var VideoTwo = document.getElementById(this.newPlayerDiv_3_1_1.id);
                this.player.close_play(videoDom).then(() => {
                    VideoTwo.srcObject = null;
                    VideoTwo.load();
                });
                isStartVideo--;
                if (isStartVideo == 0) {
                    pcStartVideo.close_play()
                    pcStartVideo = null;
                    pcStartVideoUrl = null;
                    pcStartVideoWebRtcUrl = null;
                }
                this.removeDom()
            })
        })
    }

    // 声音按钮
    newVoiceDiv_click() {
        console.log("我被点击了");
        var voice = document.getElementById(this.newVoiceDiv.id);
        var video = document.getElementById(this.newPlayerDiv_3_0_1.id)
        if (!this.Volume) {
            console.log("我是来关闭声音");
            voice.setAttribute("class", "voiceClose control");
            video.muted = true;
        } else {
            console.log("我是来开启声音");
            voice.setAttribute("class", "voiceOpen control");
            video.muted = false;
        }
        this.Volume = !this.Volume;
    }

    // 麦克风按钮
    newVoiceIntercomDiv_click() {
        console.log(this.dataInfo, 'this.dataInfo');
        var domId = document.getElementById(this.newVoiceIntercomDiv.id);
        if (this.isAudioFlag) {
            let send = {
                callId: this.dataInfo.callId,
                srcDeviceID: this.dataInfo.gbbm,
                gbbm: this.dataInfo.gbbm,
                flg: "0",
            };
            sendMuteGB(send).then((res) => {
                console.log(res);
                if (res.code == 200) {
                    var timer = setInterval(() => {
                        if (saveMuteData) {
                            clearInterval(timer);
                            if (saveMuteData.gbbh = this.dataInfo.gbbm) {
                                if (saveMuteData.status == 1) {
                                    saveMuteData = null;
                                    domId.setAttribute("class", "video_open control");
                                    this.isAudioFlag = false;
                                } else {
                                    // 取消静音失败
                                }
                            }
                        }
                    }, 100)
                }

            });
        } else {
            let send = {
                callId: this.dataInfo.callId,
                srcDeviceID: this.dataInfo.gbbm,
                gbbm: this.dataInfo.gbbm,
                flg: "1",
            };
            sendMuteGB(send).then((res) => {
                console.log(res);
                var timer = setInterval(() => {
                        if (saveMuteData) {
                            clearInterval(timer);
                            if (saveMuteData.gbbh = this.dataInfo.gbbm) {
                                if (saveMuteData.status == 1) {
                                    saveMuteData = null;
                                    domId.setAttribute("class", "voice_intercom control");
                                    this.isAudioFlag = true;
                                } else {
                                    // 取消静音失败
                                }
                            }
                        }
                    }, 100)
                    // domId.setAttribute("class", "voice_intercom control");
                    // this.isAudioFlag = true;
            });
        }
    }


    // 截屏按钮
    newScreenShotDiv_click() {
        var id = this.newPlayerDiv_3_0.id;
        var minId = this.newPlayerDiv_3_1.id;
        if (this.isTwo) {
            // 获取视频元素
            const videoElement = document.getElementById(id).children[1];

            // 创建一个 <canvas> 元素
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');

            // 设置 canvas 的宽高与视频流一致
            canvas.width = videoElement.videoWidth;
            canvas.height = videoElement.videoHeight;

            context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

            // 获取视频元素
            const videoElement_min = document.getElementById(minId).children[1];

            context.drawImage(videoElement_min, (canvas.width / 3) * 2, 0, canvas.width / 3, canvas.width * videoElement_min.videoHeight / (3 * videoElement_min.videoWidth));

            const dataUrl = canvas.toDataURL('image/png');

            // 创建一个链接元素并设置其属性
            const link = document.createElement('a');
            link.href = dataUrl;
            link.download = '图片.png';

            // 模拟点击链接以触发下载
            link.click();
        } else {
            // 获取视频元素
            const videoElement = document.getElementById(id).children[1];

            // 创建一个 <canvas> 元素
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');

            // 设置 canvas 的宽高与视频流一致
            canvas.width = videoElement.videoWidth;
            canvas.height = videoElement.videoHeight;

            context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

            const dataUrl = canvas.toDataURL('image/png');

            // 创建一个链接元素并设置其属性
            const link = document.createElement('a');
            link.href = dataUrl;
            link.download = '图片.png';

            // 模拟点击链接以触发下载
            link.click();
        }
    }

    // 双人执法窗口展示
    newScreenOpenIsTwo_click() {
        var dom = document.getElementById(this.newScreenOpenIsTwo.id)
        var videoDom = document.getElementById(this.newPlayerDiv_3_1.id)
        if (this.isTwo) {
            dom.className = 'control voice_isTwo_open';
            videoDom.className = 'control videoTwo_close';
            this.isTwo = false;
        } else {
            dom.className = 'control voice_isTwo_close';
            videoDom.className = 'control videoTwo';
            this.isTwo = true;
        }
    }

    // 双人执法初始
    initPlay() {
        console.log(push_device_type, 'push_device_type');
        if (push_device_type == 0) {
            this.getCamera().then((obj) => {
                console.log(obj);
                if (obj.type) {
                    this.getIsTwo().then(() => {
                        var VideoTwo = document.getElementById(this.newPlayerDiv_3_1_1.id);
                        console.log(VideoTwo);
                        this.playPlatform(VideoTwo).then(() => {
                            this.startPlayVideo()
                        }).catch((err) => {
                            console.log(err);
                            let send = {
                                status: 4,
                                callId: this.dataInfo.callId,
                            };
                            stopTalk(send).then(() => {
                                this.removeDom()
                            })
                        })
                    })
                } else {
                    let send = {
                        status: 4,
                        callId: this.dataInfo.callId,
                    };
                    stopTalk(send).then(() => {
                        this.removeDom()
                    })
                }
            })
        } else {
            var send = {
                hostbody_arr: [assist_enforcer_hostbody],
                callId: this.dataInfo.callId,
            };
            startLiveGB(send).then((res) => {
                if (res.data[0].is_existed) {
                    this.initPlay1(res.data[0])
                }
            })
        }
    }

    initPlay1(data) {
        this.EqWindow(data).then((obj) => {
            console.log(obj);
            if (obj.type) {
                this.getIsTwo().then(() => {
                    var VideoTwo = document.getElementById(this.newPlayerDiv_3_1_1.id);
                    console.log(VideoTwo);
                    this.playPlatform(VideoTwo).then(() => {
                        this.startPlayVideo()
                    }).catch((err) => {
                        console.log(err);
                        let send = {
                            status: 4,
                            callId: this.dataInfo.callId,
                        };
                        stopTalk(send).then(() => {
                            this.removeDom()
                        })
                    })
                })
            } else {
                let send = {
                    status: 4,
                    callId: this.dataInfo.callId,
                };
                stopTalk(send).then(() => {
                    this.removeDom()
                })
            }
        })
    }

    // 是否显示摄像头
    getIsTwo() {
        return new Promise((resolve, reject) => {
            getPlayerSetting().then((res) => {
                var dom = document.getElementById(this.newScreenOpenIsTwo.id)
                var videoDom = document.getElementById(this.newPlayerDiv_3_1.id)
                console.log(res, "---------------");
                if (res.data.assist_enforcer_picture_in_picture == 1) {
                    this.isTwo = true;
                    dom.className = 'control voice_isTwo_close';
                    videoDom.className = 'control videoTwo';
                    resolve();
                } else {
                    this.isTwo = false;
                    dom.className = 'control voice_isTwo_open'
                    videoDom.className = 'control videoTwo_close';
                    resolve();
                }
            });
        });
    }

    playPlatform(dom) {
        return new Promise((resolve, reject) => {
            console.log(dom);
            // var boxs = document.getElementById(this.ids);
            // console.log(boxs);
            // boxs.setAttribute("class", "boxs boxs_color"); //拉流的时候不要显示背景
            // 指挥台画面
            if (pcStartVideoWebRtcUrl != null) {
                dom.srcObject = pcStartVideoWebRtcUrl;
                dom.muted = true; //双人执法，采集平台声音，但是平台播放默认不放声音，故此静音
                resolve();
            } else {
                reject();
            }
        });
    }

    startPlayVideo() {
        console.log(this.dataInfo);
        var videoDom = document.getElementById(this.newPlayerDiv_3_0_1.id);
        console.log(videoDom);
        var InitialData = {
            element: videoDom,
            debug: true,
            zlmsdpUrl: this.dataInfo.play_info.webrtc_url,
            simulcast: false,
            useCamera: false,
            audioEnable: true,
            videoEnable: true,
            recvOnly: true,
            resolution: { w: 3840, h: 2160 },
            usedatachannel: false,
        };
        this.player = new Webrtc(InitialData);
        this.player.start_play().then(() => {
            var voice = document.getElementById(this.newVoiceDiv.id);
            var voiceIntercom = document.getElementById(this.newVoiceIntercomDiv.id);
            var video = document.getElementById(this.newPlayerDiv_3_0_1.id)
            if (voiceIntercom) {
                voiceIntercom.setAttribute("class", "video_open control");
            }
            if (voice) {
                console.log("我是来关闭声音");
                voice.setAttribute("class", "voiceOpen control");
            }
            video.muted = false;
            this.Volume = false;
            console.log(this.video);

            this.player.on("failed", () => {
                console.log("网络断开");
                this.newCloseDiv_click();
            });
        }).catch((err) => {
            console.log(err);
            this.newCloseDiv_click();
        })
    }

    removeDom() {
        var index = isGBTwoInfo.findIndex((obj) => {
            return obj.callId === this.dataInfo.callId; // 查找id属性等于idToRemove的对象的索引
        });
        if (index !== -1) {
            isGBTwoInfo.splice(index, 1); // 删除找到的对象
        }
        console.log(isGBTwoInfo, '数组');
        // this.newPlayerDiv_3_1.removeChild(this.newPlayerDiv_3_1_0);
        // this.newPlayerDiv_3_1.removeChild(this.newPlayerDiv_3_1_1);
        // this.newPlayerDiv_3_0.removeChild(this.newPlayerDiv_3_0_0);
        // this.newPlayerDiv_3_0.removeChild(this.newPlayerDiv_3_0_1);
        this.newPlayerDiv_2.removeChild(this.newPlayerDiv_3_0);
        this.newPlayerDiv_2.removeChild(this.newPlayerDiv_3_1);
        this.newPlayerDiv_1.removeChild(this.newPlayerDiv_2);
        this.newPlayerDiv_4.removeChild(this.newCloseDiv);
        this.newPlayerDiv_4.removeChild(this.newVoiceDiv);
        this.newPlayerDiv_4.removeChild(this.newVoiceIntercomDiv);
        this.newPlayerDiv_4.removeChild(this.newScreenShotDiv);
        this.newPlayerDiv_1.removeEventListener("dblclick", this.newPlayerDiv_1_dblclick);
        this.newPlayerDiv_1.removeEventListener("mouseover", this.newPlayerDiv_1_mouseover);
        this.newPlayerDiv_1.removeEventListener("mouseout", this.newPlayerDiv_1_mouseout);
        this.newPlayerDiv_1.removeEventListener("mousemove", this.newPlayerDiv_1_mousemove);
        this.newPlayerDiv_4.removeEventListener("mouseover", this.newPlayerDiv_4_mouseover);
        this.newPlayerDiv_4.removeEventListener("mouseout", this.newPlayerDiv_4_mouseout);
        this.newCloseDiv.removeEventListener("click", this.newCloseDiv_click);
        this.newVoiceDiv.removeEventListener("click", this.newVoiceDiv_click);
        this.newVoiceIntercomDiv.removeEventListener("click", this.newVoiceIntercomDiv_click);
        this.newScreenShotDiv.removeEventListener("click", this.newScreenShotDiv_click);
        this.newScreenOpenIsTwo.removeEventListener("click", this.newScreenOpenIsTwo_click);
        this.newPlayerDiv_0.removeChild(this.newPlayerDiv_1);
        this.newPlayerDiv_0.removeChild(this.newPlayerDiv_4);
        document.getElementById(this.newPlayerDiv_0.id).parentNode.removeChild(this.newPlayerDiv_0);
        // this.parentDom.removeChild(this.newPlayerDiv_0);
    }
}