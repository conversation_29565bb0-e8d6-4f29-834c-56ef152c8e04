<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>扫描特效使用示例</title>

    <link
      rel="stylesheet"
      href="https://dev.arcgisonline.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/base16/dracula.min.css"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/go.min.js"></script>
    <script src="./libs/three-r116.min.js"></script>
    <script src="./index.js" type="module"></script>
    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        left: 50%;
        width: 50%;
        height: 200px;
        display: flex;
      }

      .tools span {
        cursor: pointer;
        background-color: blue;
        width: 150px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        color: white;
      }

      #lineOfSight {
        width: 200px;
        height: 200px;
        position: absolute;
        bottom: 10px;
        right: 10px;
        z-index: 1;
      }

      .description {
        position: absolute;
        right: 10px;
        top: 10px;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv"></div>
    <div class="tools">
      <span onclick="add()">添加</span>
      <span onclick="remove()">移除</span>
    </div>
    <div class="description">
      添加：
      <pre><code class="language-javascript">
        ArcGisUtils.addScanRadar(view, [
        {
          radius: 500, // 半径
          position: [119.62531873137596,29.04850281353716, 10], // 中心点坐标
        },
        {
          radius: 700, 
          position: [119.64001493974088,29.058967685656043,5],
        },
      ]);
      </code></pre>
      移除：
      <pre><code class="language-javascript">
        ArcGisUtils.removeScanRadar(view);
      </code></pre>
    </div>
    <div id="lineOfSight"></div>
  </body>
  <script>
    let lineOfSightWidget;
    function add() {
      const layer = ArcGisUtils.addCylinderLayer(view, [
        {
          radius: 500, // 半径
          position: [119.62531873137596, 29.04850281353716, 1000], // 中心点坐标
        },
        // {
        //   radius: 700,
        //   position: [119.64001493974088, 29.058967685656043, 5],
        // },
      ]);
    }
    function remove() {
      ArcGisUtils.removeCylinderLayer(view);
    }
  </script>
  <script>
    hljs.highlightAll();
  </script>
</html>
