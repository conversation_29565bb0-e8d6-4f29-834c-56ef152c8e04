﻿// 说明：使用时要先引入Aes加密js文件（共两个aes.js和pad-zeropadding.js，先引入aes.js再引入pad-zeropadding.js）,最后引入axios.min.js文件,axios支持IE9+浏览器，使用是直接引入当前dataAction.js文件即可使用,注意引入顺序
//axios异步请求(默认),同步处理，与同$.ajax()方法，调用函数后带参数执行aixos请求，axios请求成功后并带返回参数调用Callback
//用法：函数名(对象方式传参)   poc.data.gets_the_dispatcher_list({SessionId:sessionId,Callback:fun})  //调用后返回的结果都会同时传给fun
//使用下面axios请求,使用时传入对象，需要传哪个只需要传入对应形参即可，如果需要更多参数可自行添加，语法fn({a:valA,b:valB})

function pad(num, n,type) {  
    var len = num.toString().length;  
    while(len < n) { 
      if(type == 0){
        num = "0" + num;  
      }else{
        num = num+"0" ;  
      }
        len++;
    }  
    return num;  
} 

var key = CryptoJS.enc.Utf8.parse("YiDongHeDuiJiang"); 
var iv = CryptoJS.enc.Utf8.parse('4561237967814523'); 

function setAes(loginId, newpwd) {
  
    var data=newpwd;
    var numBefore=pad(loginId,16,0);
    var numAfter=pad(loginId,16,1);
    var key  = CryptoJS.enc.Latin1.parse(numBefore);
    var iv   = CryptoJS.enc.Latin1.parse(numAfter);  
    var encrypted = CryptoJS.AES.encrypt(
      data,
      key,
      {
      iv:iv,
      mode:CryptoJS.mode.CBC,
      padding:CryptoJS.pad.ZeroPadding
    });
    
   var encryptedStr = encrypted+"";
   var pwdBase64 = toBase64(encryptedStr);
   //处理URL传输中存在加号的问题
   pwdBase64  = pwdBase64 .replace(/\+/g, '%2B');
   return pwdBase64;
}

function toBase64(base_k) {
  var Base64_name = new Base64();
  var base_str = Base64_name.encode(base_k);
  return base_str;
}


function Base64() {
	// private property  
	_keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";

	// public method for encoding  
	this.encode = function(input) {
		var output = "";
		var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
		var i = 0;
		input = _utf8_encode(input);
		while (i < input.length) {
			chr1 = input.charCodeAt(i++);
			chr2 = input.charCodeAt(i++);
			chr3 = input.charCodeAt(i++);
			enc1 = chr1 >> 2;
			enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
			enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
			enc4 = chr3 & 63;
			if (isNaN(chr2)) {
				enc3 = enc4 = 64;
			} else if (isNaN(chr3)) {
				enc4 = 64;
			}
			output = output +
				_keyStr.charAt(enc1) + _keyStr.charAt(enc2) +
				_keyStr.charAt(enc3) + _keyStr.charAt(enc4);
		}
		return output;
	}

	// public method for decoding  
	this.decode = function(input) {
		var output = "";
		var chr1, chr2, chr3;
		var enc1, enc2, enc3, enc4;
		var i = 0;
		input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
		while (i < input.length) {
			enc1 = _keyStr.indexOf(input.charAt(i++));
			enc2 = _keyStr.indexOf(input.charAt(i++));
			enc3 = _keyStr.indexOf(input.charAt(i++));
			enc4 = _keyStr.indexOf(input.charAt(i++));
			chr1 = (enc1 << 2) | (enc2 >> 4);
			chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
			chr3 = ((enc3 & 3) << 6) | enc4;
			output = output + String.fromCharCode(chr1);
			if (enc3 != 64) {
				output = output + String.fromCharCode(chr2);
			}
			if (enc4 != 64) {
				output = output + String.fromCharCode(chr3);
			}
		}
		output = _utf8_decode(output);
		return output;
	}

	// private method for UTF-8 encoding  
	_utf8_encode = function(string) {
		string = string.replace(/\r\n/g, "\n");
		var utftext = "";
		for (var n = 0; n < string.length; n++) {
			var c = string.charCodeAt(n);
			if (c < 128) {
				utftext += String.fromCharCode(c);
			} else if ((c > 127) && (c < 2048)) {
				utftext += String.fromCharCode((c >> 6) | 192);
				utftext += String.fromCharCode((c & 63) | 128);
			} else {
				utftext += String.fromCharCode((c >> 12) | 224);
				utftext += String.fromCharCode(((c >> 6) & 63) | 128);
				utftext += String.fromCharCode((c & 63) | 128);
			}
		}
		return utftext;
	}

	// private method for UTF-8 decoding  
	_utf8_decode = function(utftext) {
		var string = "";
		var i = 0;
		var c = c1 = c2 = 0;
		while (i < utftext.length) {
			c = utftext.charCodeAt(i);
			if (c < 128) {
				string += String.fromCharCode(c);
				i++;
			} else if ((c > 191) && (c < 224)) {
				c2 = utftext.charCodeAt(i + 1);
				string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
				i += 2;
			} else {
				c2 = utftext.charCodeAt(i + 1);
				c3 = utftext.charCodeAt(i + 2);
				string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
				i += 3;
			}
		}
		return string;
	}
}


// 加密密码
function AESencrypt(str) { 
    var srcs = CryptoJS.enc.Utf8.parse(str); 
    var encrypted = CryptoJS.AES.encrypt(srcs,key, 
    { 
        iv: iv, 
        mode: CryptoJS.mode.CBC, 
        padding: CryptoJS.pad.Pkcs7
    }
    );
   var hexStr = encrypted.ciphertext.toString().toUpperCase(); 
   var oldHexStr = CryptoJS.enc.Hex.parse(hexStr); 
   var base64Str = CryptoJS.enc.Base64.stringify(oldHexStr); 
   //这里根据需求返回 base64Str 或 hexStr(解密时有小小差别) 
   return base64Str; 
}


function toBaseText(base_k) {
    var Base64_name = new Base64();
    var base_str = Base64_name.decode(base_k);

    return base_str;
}

function getDAes(str) { 
    var srcs = str; 
    var decrypt = CryptoJS.AES.decrypt(srcs,key,
      { 
          iv: iv, 
          mode: CryptoJS.mode.CBC, 
          padding: CryptoJS.pad.Pkcs7 
      }); 
    var decryptedStr = decrypt.toString(CryptoJS.enc.Utf8); 
    var value = decryptedStr.toString(); 
    return value; 
}

function getDAesInPkey(pwd,pkey,pvi) {  
   var srcs = pwd; 
   var decrypt = CryptoJS.AES.decrypt(srcs,pkey,
      { 
          iv: pvi, 
          mode: CryptoJS.mode.CBC, 
          padding: CryptoJS.pad.ZeroPadding
      }); 
   var decryptedStr = decrypt.toString(CryptoJS.enc.Utf8); 
   var value = decryptedStr.toString(); 
   return value; 
}

function formatToken(uid,token)
{
    var user = uid;
    var numBefore = pad(user,16,0);
    var numAfter = pad(user,16,1);
    var pkey  = CryptoJS.enc.Latin1.parse(numBefore);
    var pvi   = CryptoJS.enc.Latin1.parse(numAfter);  
    var pwdStr = toBaseText(token);
    var tokenFormat = getDAesInPkey(pwdStr,pkey,pvi);
    return tokenFormat;
}

var PostMsgUrl = 'http://112.35.28.140:4480/station/mobile/serverapi.action'; 

var requestFailed = {Result:400};
var axiosPost = ({
        Url,
        Code,
        Body,
        Callback,
        } = {}) => {
        axios.post(Url,{
            Code,
            Body,
        }).then((res)=>{
           if(typeof Callback == "function"){
            Callback(res.data);
           }
        }).catch((error)=>{
            var errorObj = {Result:504,Error:error};
            if(typeof Callback == "function"){
                Callback(errorObj);
            }
        })
    }
var poc = poc || {}
poc.data = poc.data || {}
//三方鉴权
poc.data.auth = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            PostMsgUrl = objParameters.Url;
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10000",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }
    
//调度员获取Session信息,调度台通过入驻平台登录PoC平台之前需要调用此鉴权接口，获取登录后所有HTTP接口调用的SessionId参数，作为确定其会话有效性的标识。
//=========================      提供给第三方厂商登录鉴权接口           ========================
poc.data.getSession = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10010",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }
	    
//调度员获取Session信息,调度台通过入驻平台登录PoC平台之前需要调用此鉴权接口，获取登录后所有HTTP接口调用的SessionId参数，作为确定其会话有效性的标识。
//=========================      提供给本和对讲登录鉴权接口           ========================
poc.data.getSessionPlatform = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"100101",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取调度员列表,获取其管辖的调度员列表
poc.data.dispatcherList = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10011",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 设定/取消调度员身份,设定或取消所管辖的调度员身份。
poc.data.dispatcherSetIdentity = (objParameters)=>{
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10012",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 设定/取消同级别用户调度员身份
poc.data.dispatcherSetPeerIdentity = (objParameters)=>{
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10013",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }
    
//未登录状态下通过厂商ID及密码修改某一个调度员的登录密码。
//适用于密码过期
poc.data.dispatcherPwdChangeByCustomerID = (objParameters) =>{
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            objParameters.OldPwd = setAes(objParameters.Uid,objParameters.OldPwd);
            objParameters.Pwd = setAes(objParameters.Uid,objParameters.Pwd);
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10119",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取短信验证码,此接口执行成功，平台侧会已短信的方式将验证码（6位数字）下发至所指定的手机号码上。验证码产生后60秒后过期
poc.data.dispatcherGetSmsCode = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10120",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//绑定手机号码
poc.data.dispatcherBindPhoneNumber = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10121",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取短信验证码和RSA公钥,此接口执行成功，平台侧会已短信的方式将验证码（6位数字）下发至所指定的手机号码上。验证码产生后120秒后过期。
poc.data.dispatcherGetSmsCodeAndRSA = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10122",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//通过验证码修改本账户密码
poc.data.dispatcherPwdChangeBySmsCode = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
			objParameters.Pwd = setAes(objParameters.Uid,objParameters.Pwd);
			objParameters.Uid = setAes("1000110123",objParameters.Uid);
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10123",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }
	
	
//校验忘记密码手机上接收到得短信验证码
poc.data.dispatcherPwdCheckSmsCode = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10124",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }	

//短信登陆检验,调度员使用手机号和验证码登录
poc.data.dispatcherLoginBySmsCode = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11603",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//短信登陆获取验证码,根据输入的的手机号码下发验证码
poc.data.dispatcherGetLoginSmsCode = (objParameters) =>{
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10140",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
   }

//调度员未登录状态下验证验证码【废弃】
poc.data.dispatcherVerifyCode = (objParameters) =>{
    if(objParameters && objParameters.constructor == Object){
        objParameters={...objParameters};
        var Body = JSON.stringify(objParameters);
        axiosPost({Body,Url:PostMsgUrl,Code:"10141",Callback:objParameters.Callback});
    }
    else
        {
        if(objParameters.Callback){
            objParameters.Callback(requestFailed);
        }else
            return requestFailed;
    }
}

//调度员未登录状态下验证验证码【新增】
poc.data.administrator_does_not_log_verify = (objParameters) =>{
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10141",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//调度员未登录状态下通过验证码修改本账户密码,调度员未登录通过验证码修改本账户密码
poc.data.dispatcherPwdChangeUseCode = (objParameters) =>{
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10142",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//查询自定义企业名称和logo,查询用户自定义企业名称和logo
poc.data.etpInfo = (objParameters) =>{
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11606",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//设置客户端回传状态及频率【新增】,设定客户端回传状态及频率
poc.data.etpSetLocationSetting = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10204",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//设置存储空间管理【新增】
poc.data.etpSetStoreSpace = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11602",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//创建意见反馈【新增】
poc.data.etpFeedbackCreate = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11900",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取系统公告【新增】,获取系统公告
poc.data.noticeGet = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11600",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取组织节点树,获取有权限看到的组织层级节点
poc.data.orgTree = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10100",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//增加组织节点,在指定的组织节点下增加一个子组织节点。
poc.data.orgNodeAdd = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10101",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//删除组织节点,修改指定的组织节点，删除组织节点后，其下的所有用户以及子组织将会自动解散，并自动归属于父组织（上级节点），且子组织内调度员用户被取消调度员身份
poc.data.orgNodeDel = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10101",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//修改组织名称,修改指定的组织名称
poc.data.orgNodeUpdateName = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10103",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//修改成员归属,将一个或多个用户归属到制定的组织中
poc.data.orgMemberUpdate = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10110",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取用户列表（获取某组织下的成员）,获取制定组织节点下的用户列表
poc.data.orgMemberListInNode = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10111",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取某组织下的成员并携带遥毙，呼叫限制状态
poc.data.orgMemberListInNodeWithMore = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10114",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取组织下的成员,获取登录调度员所在组织下面所有人员及其子组织下面人员
poc.data.orgMemberAll = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10115",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取组织下的成员（树形结构）【调整】,获取登录调度员所在组织下面所有人员及其子组织节点和下面人员
poc.data.orgMemberTree = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10116",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//加载指定组织用户成员的时候及组织的直接子组织节点,加载指定组织用户成员的时候及组织的直接子组织节点
poc.data.orgNodeAndMembers = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10313",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//列表排序【调整】,此接口可针对组织列表、用户列表、频道成员列表进行排序操作，仅支持同级列表操作，即列表中的成员必须是同一级别的元素。
poc.data.orgSort = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11420",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


//组织转移接口,将某些组织转移到另一组织下面。
poc.data.orgMove = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11607",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//重置用户密码,修改某一个用户的登录密码
poc.data.userPwdReset = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10107",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//重置用户密码 - 密码加密接口,采用RSA加密的方式修改某一个用户的登录密码
poc.data.userGetServerRsa = (objParameters) => {
    if(objParameters && objParameters.constructor == Object){
        objParameters={...objParameters};
        var Body = JSON.stringify(objParameters);
        axiosPost({Body,Url:PostMsgUrl,Code:"10108",Callback:objParameters.Callback});
    }
    else
        {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
}

//修改用户的密码
poc.data.userPwdResetByRsa = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10109",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取用户基本信息,获取某一个用户基本信息。
poc.data.userInfo = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10112",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//修改某一个用户基本信息,修改某一个用户基本信息
poc.data.userInfoUpdate = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10113",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//批量修改用户基本信息【新增】,批量修改用户基本信息。
poc.data.userInfoBatchUpdate = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10130",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//登录状态下，向调度员绑定的手机下发验证码
poc.data.userSmsCodeSend = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10135",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


//验证输入的验证码
poc.data.userSmsCodeVerify = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10136",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//批量下发密码短信【新增】
poc.data.userSmsSendPwd = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10137",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

    
//遥闭/复活设置,调度员可对可复活遥毙的用户进行遥毙/复活设置。执行遥毙后，终端暂时无法再使用指定通信功能，系统也可通过执行复活操作而使其恢复到正常状态。
poc.data.userPermissionSetCloseAndRevive = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11202",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取用户遥闭、呼叫限制状态,调度员可获取指定用户的遥闭/复活状态、呼叫限制状态（临时呼叫呼入限制、临时呼叫呼出限制、频道呼叫限制）。
poc.data.userPermissionGet = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11205",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//呼入和呼出限制,调度员可对用户的对讲会话呼叫能力的进行限制
poc.data.userPermissionSetCallLimit = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11203",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//限制用户呼入指定群组,调度员可对用户的对讲群组会话呼叫的能力进行限制,限制后不允许指定用户呼入指定群组
poc.data.userPermissionSetChannelLimit = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11204",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//调度员批量修改成员权限【新增】
poc.data.userPermissionBatchSet = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11209",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//位置信息,获取指定用户的当前位置。
poc.data.locationGet = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10200",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//查询历史位置，通过条件查询某一用户的历史坐标位置列表。
poc.data.locationHistory = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10201",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取锁定位置
poc.data.locationLockGet = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10202",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//修改锁定位置
poc.data.locationLockUpdate = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10203",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//创建频道,创建一个新的频道
poc.data.channelCreate = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10300",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//删除频道,删除一个频道，只有频道的创建者才有权限删除此频道。
poc.data.channelDel = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10301",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//修改频道属性,修改一个频道的属性，只有频道的创建者才有权限修改此频道。
poc.data.channelUpdate = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10302",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


//增加/删除频道成员,向一个指定的频道添加删除成员
poc.data.channelMemberChange = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10303",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//修改频道成员属性，修改频道指定成员的属性。
poc.data.channelMemberInfoUpdate = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10304",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取频道列表，获取可见的频道列表
poc.data.channelList = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10305",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取频道成员，获取频道成员列表
poc.data.channelMemberList = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10311",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取指定频道详细属性,获取指定频道的详细属性信息
poc.data.channelInfo = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10314",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取其它频道列表,获取其它人创建的频道列表,可用于监听
poc.data.channelOthersList = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10315",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

    
//获取分页频道列表，获取可见的频道列表并分页返回
poc.data.channelListPaged = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10318",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


//分页按照查询条件获取频道列表，输入频道或创建者名称获取可见的频道列表并分页返回
poc.data.channelListPagedWithCondition = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10319",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 创建会话,创建一个新的会话
poc.data.sessionCreate = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10306",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

    
// 删除会话,删除一个会话，只有会话的创建者才有权限删除此会话
poc.data.sessionDel = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10307",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 修改会话名称,修改一个会话的名称，只有会话的创建者才有权限修改此会话名称
poc.data.sessionUpdateName = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10308",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 增加/删除会话成员,向一个指定的会话添加删除成员
poc.data.sessionMemberChange = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10309",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 获取会话列表,获取可见的会话列表
poc.data.sessionList = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10310",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 获取会话成员,获取会话成员列表
poc.data.sessionMemberList = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10312",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }
	
	
// 会话成员退出
poc.data.sessionMemberExit = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10320",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 存储空间用量统计【调整】,通过此接口可查询当前企业媒体记录和实时视频存储的空间占用情况
poc.data.mediaStoreStat = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11400",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 查询图文列表【调整】,通过条件查询所管辖用户的历史上报图文列表
poc.data.mediaReportList = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10400",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 删除图文记录,删除一个制定的图文记录
poc.data.mediaReportRemove = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10401",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 编辑指定的图文备注,编辑指定的图文备注
poc.data.mediaReportRemarkModify = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10402",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 设置指定的图文记录为已读状态
poc.data.mediaReportSetRead = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10403",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 批量删除媒体记录,批量删除媒体记录
poc.data.mediaRecordBatchRemove = (objParameters) => {
    if(objParameters && objParameters.constructor == Object){
        objParameters={...objParameters};
        var Body = JSON.stringify(objParameters);
        axiosPost({Body,Url:PostMsgUrl,Code:"10405",Callback:objParameters.Callback});
    }
    else
        {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
}


// 获取媒体记录,获取媒体记录信息，返回信息中包含的类型应该和“和助手”中（11411）的相同(不包含电子围栏告警记录，广播记录，并且包含已读信息)
poc.data.mediaRecordList = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11412",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 批量设置媒体记录为收藏状态【新增】,批量设置媒体记录为收藏状态
poc.data.mediaRecordSetFavorites = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10420",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 获取媒体记录详情,获取指定的某一个媒体记录的详情
poc.data.mediaRecordInfo = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11410",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 查询“和助手”记录列表,查询“和助手”相关的未读记录，包括：“上报图文”、“抓拍图片”、“实时视频录制片段”、“围栏告警信息”、“广播”等
poc.data.mediaAssistantList = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11411",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 查询广播列表【调整】，通过条件查询广播列表
poc.data.broadcastList = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10500",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 删除广播,删除指定的广播记录
poc.data.broadcastDel = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10501",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 发送广播,发送一个编辑好的广播
poc.data.broadcastSend = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10502",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 获取广播内容,获取指定内容的广播
poc.data.broadcastContent = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10503",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 设置指定广播记录为已读状态
poc.data.broadcastSetRead = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10504",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 广播呼叫权限用户列表获取,通过此接口可获取当前所设置的哪些用户具有广播呼叫权限。
poc.data.userPermissionGetBroadcastCall = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11320",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 广播呼叫权限用户添加和删除,通过此接口可添加或删除具有广播呼叫权限的用户
poc.data.userPermissionSetBroadcastCall = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11321",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 搜索IM记录,通过条件检索IM消息记录
poc.data.msgSearch = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10600",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 删除IM记录,删除指定的IM消息记录
poc.data.msgDel = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10601",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


    
// 设置IM消息为收藏状态,设置IM消息为收藏状态
poc.data.msgSetFavorites = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10610",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 创建电子围栏,创建新的电子围栏记录
poc.data.geofenceCreate = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10700",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 设定电子围栏限定人员列表,设定电子围栏指定的成员列表，将覆盖当前已存在的成员列表
poc.data.geofenceMemberSet = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10701",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 修改电子围栏名称,修改指定的电子围栏指定的名称及生效时间
poc.data.geofenceNameModify = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10702",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 修改电子围栏坐标集,修改的电子围栏记录的坐标集，将覆盖已存在的坐标集
poc.data.geofencePositionsModify = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10703",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 删除电子围栏,删除指定的电子围栏。
poc.data.geofenceDel = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10704",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 查询电子围栏,按条件查询电子围栏记录
poc.data.geofenceSearch = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10705",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 查询电子围栏告警历史记录,按条件查询电子围栏告警记录
poc.data.geofenceAlarmHistory = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10706",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }



// 删除电子围栏告警记录,删除指定的电子围栏告警记录
poc.data.geofenceAlarmDel = (objParameters) => {
    if(objParameters && objParameters.constructor == Object){
        objParameters={...objParameters};
        var Body = JSON.stringify(objParameters);
        axiosPost({Body,Url:PostMsgUrl,Code:"10707",Callback:objParameters.Callback});
    }
    else
        {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
}


// 修改围栏成员禁入和禁出状态,修改电子围栏成员禁入，禁出状态
poc.data.geofenceMemberInOutSet = (objParameters) => {
    if(objParameters && objParameters.constructor == Object){
        objParameters={...objParameters};
        var Body = JSON.stringify(objParameters);
        axiosPost({Body,Url:PostMsgUrl,Code:"10708",Callback:objParameters.Callback});
    }
    else
        {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
}


// 设置指定围栏告警记录为已读状态,设置指定围栏告警记录为已读状态
poc.data.geofenceSetRead  = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10709",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 查询电子围栏告警记录详情,查询某一个电子围栏告警记录的详情。
poc.data.geofenceAlarmInfo = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10710",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//更新电子围栏状态
poc.data.geofenceUpdateStatus = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10711",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }
	
//按照条件查询围栏告警记录
poc.data.geofenceAlarmBatch = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10715",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }	
	
//更新电子围栏提醒状态
poc.data.geofenceUpdateReminder = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10712",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }	
	

// 增加地图标注,增加新的地图标注。
poc.data.mapMarkAdd = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10800",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 删除地图标注,删除指定的地图标注。
poc.data.mapMarkDel = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10801",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


    
// 修改地图标注内容,修改的地图标注的名称和内容。
poc.data.mapMarkInfoModify = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10802",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 查询地图标注,按条件查询地图标注记录
poc.data.mapMarkSearch = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10803",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 批量添加跨企业请求,批量向多个企业发起跨企业申请
poc.data.crossEtpRequest = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10900",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 更新跨企业状态,更新跨企业状态
poc.data.crossEtpStateUpdate = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10901",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 获取跨企业列表及成员,获取跨企业列表及成员
poc.data.crossEtpList = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10903",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 添加或删除跨企业成员,添加或删除跨企业成员
poc.data.crossEtpMemberChange = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10904",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 验证企业ID和名称,在关联企业时，通过输入企业ID和企业名称来验证是否正确
poc.data.crossEtpVerify = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10905",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 检索历史实时视频记录列表,按条件检索查询视频录制记录。
poc.data.videoRecordQuery = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11100",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 删除历史视频,删除选定的视频录制记录。
poc.data.videoRecordDel = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11101",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 打开/关闭企业级实时视频存储,打开或关闭本企业级实时视频存储，仅顶级调度员有此操作权限。
poc.data.videoStoreSwitch = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11102",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 实时视频转发,向某个指定会话或用户转发当前正在播放的实时视频
poc.data.videoForward = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var internalInfo = poc.video.__internal_get_info(objParameters.VideoSession);
			var rtspURL  = internalInfo.rtsp_url;
			objParameters.Url = rtspURL;
            objParameters.InternalInfo = internalInfo; 			
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11500",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


    
// 实时视频远端抓拍指令,向某个指定用下发视频抓怕指令。
poc.data.videoRemoteCapture = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11501",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 设置实时视频查看权限,取消或设置某一个用户查看实时视频的权限（默认为允许）。
poc.data.videoPremissionViewSet = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11502",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 打开/关闭企业级录音,打开或关闭本企业级对讲录音能力，仅顶级调度员有此操作权限。
poc.data.recordSwitch = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11200",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 检索录音,按条件检索查询对讲录音记录。
poc.data.recordQuery = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11201",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 获取监控列表,获取监控列表
poc.data.monitorList = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11206",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 获取监控列表【调整】,获取监控列表(包含监听频道中的用户)
poc.data.monitorMembers = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11207",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 添加或删除监控成员,添加或删除监听用户/频道
poc.data.monitorMemberChange = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11208",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 外勤任务派发,通过此接口可以派发下达外勤任务工单
poc.data.taskSend = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11300",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


    
// 外勤任务列表获取,通过此接口获取当前调度员所管理的任务列表。
poc.data.taskList = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11301",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 外勤任务修改,通过此接口修改当前调度员所管理的任务。
poc.data.taskModify = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11302",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 外勤任务取消和删除,接口当前调度员可以对任务进行取消或删除。
poc.data.taskDel = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11303",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 外勤任务成员添加和删除,通过此接口对指定任务的成员进行添加或删除。
poc.data.taskMemberChange = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11304",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


// 获取外勤任务成员上报记录,通过此接口可获取任务相关成员的上报记录
poc.data.taskReports = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11305",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 水印模板设定,根级调度员可以设置企业下的水印模板格式内容，以及应用场景。
poc.data.watermarkTemplateSet = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
			var waterMark = objParameters.WaterMark;
			waterMark.Txt = encodeURIComponent(waterMark.Txt);
			var waterMarkStr = JSON.stringify(waterMark);
			objParameters.WaterMark = toBase64(waterMarkStr);
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11503",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 水印模板获取,根级调度员可以获取企业下的水印模板格式内容和应用场景。
poc.data.watermarkTemplateGet = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11504",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }
	
	
	// 水印预览
poc.data.watermarkImgUrlGet = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
			var waterMark = objParameters.WaterMark;
			waterMark.Txt = encodeURIComponent(waterMark.Txt);
			var waterMarkStr = JSON.stringify(waterMark);
			objParameters.WaterMark = toBase64(waterMarkStr);
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10014",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }
	

//短信登陆下发验证码	
poc.data.loginSendSMSCode = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11604",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }	

	
//获取用户在线状态
poc.data.userOnlineStatus = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10150",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }		

//批量重置成员密码
poc.data.userPasswordBatchUpdate = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10131",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//设置调度员登陆调度台语言
poc.data.dispatcherSetLangage = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11210",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//打开或关闭调度台围栏告警提示音
poc.data.dispatcherSetWarningTone = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11211",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }
	
	
//设置企业名称和定义logo
poc.data.etpSetInfo = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11605",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }	

//批量删除指定的IM消息记录
poc.data.msgBatchDel = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10611",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }
	
	
//批量设置IM消息为已读状态
poc.data.msgBatchRead
 = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10612",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }	
	
//检索历史实时视频抓拍和视频记录
poc.data.videoAndImageRecordQuery = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11103",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }
	
//批量设置指定围栏得告警记录为已读状态
poc.data.geoWarningSetReadByFenceId = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10713",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }


//查询未读得电子围栏告警记录
poc.data.geofenceUnReadAlarm = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10714",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }	


//调度员添加收藏
poc.data.dispatcherAddCollect = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11416",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//调度员删除收藏
poc.data.dispatcherDeleteCollect = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11417",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//随机产生账号密码登录验证码
poc.data.loginGetVerificationCode = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11800",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//企业合并验证来源地/目的地企业
poc.data.groupUnionVerify = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11811",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//添加企业合并
poc.data.groupUnionAdd = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11812",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//更新企业合并状态
poc.data.groupUnionUpdate = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11813",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取企业合并列表
poc.data.groupUnionQuery = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11814",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取系统通知
poc.data.sysNotifQuery = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11911",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//添加系统通知
poc.data.sysNotifAdd = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11912",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//删除系统通知
poc.data.sysNotifDelete = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"11913",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 验证企业ID和名称
poc.data.crossEtpVerify4 = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10906",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 修改跨企业成员
poc.data.crossEtpMemberChange4 = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10907",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 修改跨企业权限
poc.data.crossEtpPermissionUpdate = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10908",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 获取跨企业权限
poc.data.crossEtpPermissionQuery = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10909",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 获取跨企业详细记录
poc.data.crossEtpRecordList = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10910",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 更新跨企业详细记录状态
poc.data.crossEtpRecordStateUpdate = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10911",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 更新跨企业状态
poc.data.crossEtpStateUpdate4 = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10912",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

// 获取跨企业成员列表
poc.data.crossEtpUserQuery = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10913",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//修改跨企业成员监控权限
poc.data.crossEtpMonitorUpdate = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10914",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

//获取跨企业成员监控权限
poc.data.crossEtpMonitorQuery = (objParameters) => {
        if(objParameters && objParameters.constructor == Object){
            objParameters={...objParameters};
            var Body = JSON.stringify(objParameters);
            axiosPost({Body,Url:PostMsgUrl,Code:"10915",Callback:objParameters.Callback});
        }
        else
            {
            if(objParameters.Callback){
                objParameters.Callback(requestFailed);
            }else
                return requestFailed;
        }
    }

	