<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Title</title>
  <script src="/Vue/vue.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="./static/js/jslib/http.interceptor.js"></script>
  <script src="https://download.agora.io/sdk/release/AgoraRTC_N.js"></script>
  <link rel="stylesheet" href="/elementui/css/elementui.css" />
  <script src="/elementui/js/elementui.js"></script>
  <style>
    #app {
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .player {
        width: 700px;
        height: 450px;
        background: rgba(0,0,0,0);
    }
  </style>
</head>
<body>
  <div id="app">
    <button @click="init">login</button>
    <button @click="leave">leave</button>

    <div :id="idName" class="player" v-loading="loading"></div>
  </div>
  <script>
    new Vue({
      el: "#app",
      data: {
        info: {
          account:"<EMAIL>",
          password:"lm5170159",
          device_sn:"7CTDLA900A0031"
        },

        loading:false,
        // create Agora client
        client: AgoraRTC.createClient({
          mode: "live",
          codec: "vp8"
        }),
        localTracks: {
          videoTrack: null,
          audioTrack: null
        },
        remoteUsers: {},
        // Agora client options
        options: {
          appid: null,
          channel: null,
          uid: null,
          token: null,
          role: "audience",
          // host or audience
          audienceLatency: 2
        },
        uid:""
      },
      computed: {
        idName() {
          return "player-" + this.uid
        }
      },
      methods: {
        async init() {
          await this.login();
          // await this.getAirportVideo();
          // await this.getUAVVideo();
          await this.getStart();
        },
        login() {
          $uavPost("/login",{force:true,account: this.info.account,password:this.info.password}).then(res => {
            if (res.data.code == 0) {
              localStorage.setItem("uav-token", res.data.data.token);
            }
          })
        },
        //机场视频
        getAirportVideo() {
          $uavGet(`/${this.info.device_sn}/stream/airport`).then(res => {
            if (res.data.code == 0) {
              console.log(res.data.data.url);
              this.play(res.data.data.url)
            }
          })
        },
        //无人机视频
        getUAVVideo() {
          $uavGet(`/${this.info.device_sn}/stream/payload`).then(res => {
            this.loading = false
            if (res.data.code == 0) {
              console.log(res.data.data.url);
              this.play(res.data.data.url)
            }
          })
        },
        //起飞直达
        getStart() {
          $api2Post("/wrj/yjzd",{longitude:"119.66458",latitude:"29.072045"}).then(res => {
            console.log(res);
            this.loading = true;
            setTimeout(() => {
              this.getUAVVideo()
            },40000)
          })
        },
        //无人机返航
        getBack() {
          $api2Post("/wrj/fh").then(res => {
            console.log(res);
          })
        },
        play(url) {
          let arr = url.split("&").map(item => {
            return item.split("=")
          })
          var urlParam = this.arrayToObject(arr);

          this.options.appid = urlParam.app_id;
          this.options.channel = urlParam.channel;
          this.options.token = decodeURIComponent(urlParam.token);
          this.options.uid = Number(urlParam.uid);
          this.options.role = "audience";
          this.options.audienceLatency = 1;

          this.join()
        },
        async join() {
          // create Agora client

          if (this.options.role === "audience") {
            this.client.setClientRole(this.options.role, {
              level: this.options.audienceLatency
            });
            // add event listener to play remote tracks when remote user publishs.
            this.client.on("user-published", this.handleUserPublished);
            this.client.on("user-unpublished", this.handleUserUnpublished);
          } else {
            this.client.setClientRole(this.options.role);
          }

          // join the channel
          this.options.uid = await this.client.join(this.options.appid, this.options.channel, this.options.token || null, this.options.uid || null);
          if (this.options.role === "host") {
            // create local audio and video tracks
            if (!this.localTracks.audioTrack) {
              this.localTracks.audioTrack = await AgoraRTC.createMicrophoneAudioTrack({
                encoderConfig: "music_standard"
              });
            }
            if (!this.localTracks.videoTrack) {
              this.localTracks.videoTrack = await AgoraRTC.createCameraVideoTrack();
            }
            // play local video track
            this.localTracks.videoTrack.play("local-player");
            // publish local tracks to channel
            await this.client.publish(Object.values(this.localTracks));
            console.log("publish success");
          }
        },
        async leave() {
          for (trackName in this.localTracks) {
            var track = this.localTracks[trackName];
            if (track) {
              track.stop();
              track.close();
              this.localTracks[trackName] = undefined;
            }
          }

          // remove remote users and player views
          this.remoteUsers = {};
          // leave the channel
          await this.client.leave();
          console.log("client leaves channel success");
        },
        handleUserPublished(user, mediaType) {
          //print in the console log for debugging
          console.log('"user-published" event for remote users is triggered.');
          const id = user.uid;
          this.remoteUsers[id] = user;
          this.subscribe(user, mediaType);
        },
        handleUserUnpublished(user, mediaType) {
          //print in the console log for debugging
          console.log('"user-unpublished" event for remote users is triggered.');
          if (mediaType === 'video') {
            const id = user.uid;
            delete this.remoteUsers[id];
          }
        },
        async subscribe(user, mediaType) {
          const uid = user.uid;
          this.uid = uid;
          // subscribe to a remote user
          await this.client.subscribe(user, mediaType);
          console.log("subscribe success");
          if (mediaType === 'video') {
            user.videoTrack.play(`player-${uid}`, {
              fit: "contain"
            });
          }
          if (mediaType === 'audio') {
            user.audioTrack.play();
          }
        },
        arrayToObject(arr) {
          var obj = {};
          for (var i = 0; i < arr.length; i++) {
            obj[arr[i][0]] = arr[i][1];
          }
          return obj;
        }
      }
    })
  </script>
</body>
</html>
