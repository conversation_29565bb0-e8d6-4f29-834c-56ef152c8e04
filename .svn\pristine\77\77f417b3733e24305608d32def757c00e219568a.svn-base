<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>指挥体系弹窗</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/static/js/jslib/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <style>
      /* 表格 */
      .table {
        display: block;
        width: 100%;
        /* height: 500px; */
        padding: 10px 15px;
        box-sizing: border-box;
      }

      .table .th {
        width: 100%;
        height: 60px;
        /* display: flex; */
        align-items: center;
        justify-content: space-evenly;
        font-weight: 700;
        font-size: 28px;
        line-height: 60px;
        color: #ffffff;
      }

      .table .th_td {
        letter-spacing: 0px;
        text-align: center;
        width: 270px;
      }

      .table .tbody {
        width: 100%;
        height: calc(100% - 59px);
        /* overflow-y: auto; */
        overflow: hidden;
      }

      .table .tbody:hover {
        overflow-y: auto;
      }

      .table .tbody::-webkit-scrollbar {
        width: 4px;
        /*滚动条整体样式*/
        height: 4px;
        /*高宽分别对应横竖滚动条的尺寸*/
      }

      .table .tbody::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: #20aeff;
        height: 8px;
      }

      .table .tr {
        /* display: flex; */
        justify-content: space-evenly;
        align-items: center;
        height: 70px;
        line-height: 70px;
        font-size: 24px;
        color: #ffffff;
        cursor: pointer;
        border-top: 1px solid #959aa1;
        border-image: linear-gradient(to right, #e9f5ff3b, #f5ffffd4, #e9f5ff3b)
          1;
        box-sizing: border-box;
      }

      .table .tr:nth-child(2n) {
        background: rgba(50, 134, 248, 0.2);
      }

      .table .tr:nth-child(2n + 1) {
        background: rgba(50, 134, 248, 0.12);
      }

      .table .tr:hover {
        background-color: #0074da75;
      }

      .table .tr_td {
        /* letter-spacing: 0px; */
        text-align: center;
        /* box-sizing: border-box;
        white-space: nowrap; */
        overflow: hidden;
        text-overflow: ellipsis;
        width: 270px;
        line-height: 40px;
        padding: 20px;
      }
      body {
        margin: 0;
        padding: 0;
      }
      #app {
        width: 1200px;
        height: 680px;
        position: relative;
      }
      .close {
        position: absolute;
        right: -30px;
        top: -90px;
        color: #fff;
        font-size: 68px;
        margin-right: 40px;
        cursor: pointer;
      }
      .content {
        width: 94%;
        position: absolute;
        top: 135px;
        left: 45px;
        height: 80%;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <s-dialog :title="tit" width="1200px" height="680px"></s-dialog>
      <div class="content">
        <div class="close" @click="close()">×</div>
        <table class="table" style="height: 485px">
          <thead style="display: block">
            <tr class="th">
              <th class="th_td">参与单位</th>
              <th class="th_td">量化指标</th>
              <th class="th_td">指标值</th>
              <th class="th_td">非量化指标</th>
            </tr>
          </thead>
          <tbody style="display: block; color: #fff" class="tbody" id="box">
            <tr class="tr" v-for="(item,index) in tableData" :key="index">
              <td class="tr_td" :rowspan="tableData.length" v-if="index==0">
                {{item.cydw}}
              </td>
              <td class="tr_td">{{item.lhzb}}</td>
              <td class="tr_td">{{item.zbz}}</td>
              <td class="tr_td">{{item.flhzb}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <script>
      let vm = new Vue({
        el: "#app",
        data() {
          return {
            tit: "",
            tableData: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
          };
        },
        mounted() {
          let this_ = this;
          window.addEventListener("message", (e) => {
            if (e.data && e.data.jgyjs_table_diong) {
              this_.tit = e.data.jgyjs_table_diong[0].zdrwxt;
              this_.tableData = JSON.parse(
                JSON.stringify(e.data.jgyjs_table_diong).replace(/\n/g, "<br/>")
              );
            }
          });
        },
        methods: {
          close() {
            window.parent.lay.closeIframeByNames(["jgyjs_table_diong"]);
          },
        },
      });
    </script>
  </body>
</html>
