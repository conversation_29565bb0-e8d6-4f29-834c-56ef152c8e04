/*
 * @Descripttion: 添加精膜，移除精膜
 * @Author: <EMAIL>
 * @Date: 2022-12-10 15:02:16
 */
import { getLayerConfigById } from "./layerConfig.js";
import layerCreatAsync from "./layerCreatAsync.js";

const LAYER_ID = "PURE_MODEL";

/**
 * 添加 精膜 方法
 * @param { MapView|SceneView} view 对象 必填
 * @returns
 */

async function addPureModalLayer(view) {
  const layerConfig = getLayerConfigById(LAYER_ID);
  const layer = await layerCreatAsync(layerConfig);
  view.map.add(layer);
}

/**
 * 添加 精膜 方法
 * @param { MapView|SceneView} view 对象 必填
 * @returns
 */
function removePureModalLayer(view) {
  let layer = view.map.findLayerById(LAYER_ID);
  view.map.remove(layer);
}

export { addPureModalLayer, removePureModalLayer };
