import LineOfSight from "https://dev.arcgisonline.cn/jsapi/4.25/@arcgis/core/widgets/LineOfSight.js";

/**
 * 创建通视分析微件
 * @param {SceneView} view
 * @param {string} container 容器ID
 * @returns
 */
function createLineOfSightWidget(view, container) {
  if (!view) {
    throw new Error("参数view为必传！");
  }
  if (!container) {
    throw new Error("参数container为必传！");
  }
  const lineOfSight = new LineOfSight({
    view,
    container,
  });
  return lineOfSight;
}

export default createLineOfSightWidget;
