// key => item.ssid
// val =>
//     item.cls
//     item.sid
//     item.ipocid
//     item.ssid
//     item.rtsp_url
//     @item.start
//     @item.accept
//     item.janus.url
//     item.janus.pin
//     item.janus.room
//     item.janus.pub
//     item.janus.vw
//     item.janus.vh
//     @item.janus.videoStream
//     @item.janus.videoList
//     @item.janus.mdsVideo
//     @item.janus.mdsVideoChanging
//     @item.janus.mdsVideoIndex
//     item.janus.branch[0].vw
//     item.janus.branch[0].vh
//     item.janus.branch[0].vkbps
//     item.janus.branch[0].url
//     item.janus.branch[0].pin
//     item.janus.branch[0].room

//json:
// {
//     "cls": 1,
//     "sid": "C1001",
//     "ipocid": "13600000059",
//     "ssid": 499155765,
//     "rtsp_url": "rtsp://*************:554/stream-play-real/RT-C1001-499155765-13600000058",
//     "janus": {
//         "url": "wss://vd1.ap-cn-beijing.weptt.com:8989/janus",
//         "pin": 1994250223,
//         "room": 1,
//         "pub": "MDS0001",
//         "vw": 2048,
//         "vh": 4096,
//         "branch": [
//             {
//                 "vw": 480,
//                 "vh": 640,
//                 "vkbps": 800,
//                 "url": "wss://vd1.ap-cn-beijing.weptt.com:8989/janus",
//                 "pin": 5462342134,
//                 "room": 3435,
//             },
//             {
//                 "vw": 1080,
//                 "vh": 1920,
//                 "vkbps": 1002,
//                 "url": "wss://vd1.ap-cn-beijing.weptt.com:8989/janus",
//                 "pin": 3421231231,
//                 "room": 6543,
//             }
//         ],
//     },
// }

var gJanusMap = new HashMap(); //全局janus list
var gMdsVideoPrintTimer = null;
const gPrintTimerInterval = 1000 * 5;//5s
var gMdsRetryTimer = null;
var gMdsRetryList = [];

poc.video.pushVideoShareEvent = function (event) {
    if (event && event.janus && event.janus.pub && event.janus.pub != "") {
        //开始发布
        event.start = Math.round(new Date() / 1000);
        event.janus.videoList = new Array();
        gJanusMap.put(event.ssid, event);

        poc.video.onVideoShare(event.ssid, event.sid, event.ipocid, true/*valid*/);

        if (!gMdsVideoPrintTimer) {
            mdsVideoTimerRun();
        }
    } else {
        //结束发布
        poc.video.stop(event.ssid);
        //20201116: 不管之前有没有接受都要通知UI视频结束
        poc.video.onVideoShare(event.ssid, event.sid, event.ipocid, false/*valid*/);
    }
}

poc.video.play = function (session, video) {
    return new Promise(function (resolve, reject) {
        var item = gJanusMap.get(session);
        if (item == null) {
            reject("session not found: " + session);
            return;
        }
        var i = item.janus.videoList.push(video);
        if (item.accept) {//已经有其他人开始尝试订阅,防止重复订阅
            if (item.janus.mdsVideo == null) {
                reject("janus not init.");
                return;
            }

            if (item.janus.videoStream != null) {
                Janus.log("attach media stream(copied) #remotevideo(" + i + ")" + video);
                Janus.attachMediaStream(video, item.janus.videoStream);
            }
            resolve(session);
            return;
        }

        item.accept = Math.round(new Date() / 1000);
        item.janus.videoStream = null;

        subscribeFromJanus(item, -1).then(function (session) {
            resolve(session)
        }).catch(function (err) {
            item.accept = null;//留给用户再次重试的机会
            reject(err)
        });
    })
}

poc.video.unref = function (session, video) {
    var item = gJanusMap.get(session);
    if (item == null) {
        return -1;
    }

    for (var i = item.janus.videoList.length - 1; i >= 0; i--) {
        if (item.janus.videoList[i] === video) {
            item.janus.videoList[i].srcObject = null;
            item.janus.videoList.splice(i, 1);
        }
    }

    return 0;
}

// ret: {vw:原画宽, vh:原画高, branch: [{vw:高清宽,vh:高清高,vkbps:清晰度},{vw:标清宽,vh:标清高,vkbps:清晰度}]}
poc.video.getResolutionList = function (session) {
    var item = gJanusMap.get(session);
    if (item == null) {
        return null;
    }

    if (!item.janus.branch || item.janus.branch.length <= 0) {
        return null;
    }

    var resolutionList = {
        vw: item.janus.vw,
        vh: item.janus.vh,
    };

    var branch = [];
    for (var i = 0; i < item.janus.branch.length; i++) {
        branch.push({
            vw: item.janus.branch[i].vw,
            vh: item.janus.branch[i].vh,
            vkbps: item.janus.branch[i].vkbps,
        });
    }

    resolutionList.branch = branch;

    return resolutionList;
}

// resolutionIndex 为poc.video.getResolutionList中branch数组的索引,传-1表示原画
poc.video.changeResolution = function (session, resolutionIndex) {
    var item = gJanusMap.get(session);
    if (item == null) {
        return;
    }

    changeBranchDo(item, resolutionIndex);

    return;
}

// ret -1表示原画,其他为poc.video.getResolutionList中branch数组的索引
poc.video.getCurrentResolutionIndex = function (session) {
    var item = gJanusMap.get(session);
    if (item == null) {
        return undefined;
    }

    return item.janus.mdsVideoIndex;
}


// 计算清晰度
//! OBSOLETE
function calcBranch(item) {
    if (!item || !item.janus || item.janus.videoList.length <= 0) {
        return null;
    }

    var janusIndex = -1;

    if (item.janus.branch) {
        //获取设备信息resolution
        var resolution = {
            vw: 0,
            vh: 0,
        }
        for (var i = 0; i < item.janus.videoList.length; i++) {
            if (item.janus.videoList[i].clientHeight > 0 && item.janus.videoList[i].clientHeight > resolution.vh &&
                item.janus.videoList[i].clientWidth > 0 && item.janus.videoList[i].clientWidth > resolution.vw) {
                resolution.vh = item.janus.videoList[i].clientHeight;
                resolution.vw = item.janus.videoList[i].clientWidth;
            }
        }
        if (resolution.vw == 0 || resolution.vh == 0) {
            return janusIndex;
        }

        //根据resolution计算janusIndex
        for (var i = 0; i < item.janus.branch.length; i++) {
            if ((item.janus.branch[i].vw >= resolution.vw && item.janus.branch[i].vh >= resolution.vh)/*分辨率符合 */ &&
                (janusIndex < 0 || item.janus.branch[janusIndex].vw > item.janus.branch[i].vw || item.janus.branch[janusIndex].vh > item.janus.branch[i].vh)/*确保是最小符合*/) {
                janusIndex = i;
            }
        }
    }

    if ((item.janus.mdsVideoIndex != undefined) && (janusIndex == item.janus.mdsVideoIndex || (janusIndex < 0 && item.janus.mdsVideoIndex < 0))) {
        return null; //need not
    }

    return janusIndex;
}

// 修改清晰度
//! OBSOLETE
function changeBranch(item) {
    //调用前须要过滤: 已经暂停的不处理!
    if (!item || !item.janus || !item.janus.mdsVideo || !item.accept || item.janus.videoList.length <= 0 || item.janus.mdsVideo.isPaused() || item.janus.mdsVideoIndex == undefined || !item.janus.branch || item.janus.mdsVideoChanging) {
        return;
    }

    //计算所需的index
    var janusIndex = calcBranch(item);
    if (item == null) {
        return;
    }

    //需要切换
    changeBranchDo(item, janusIndex);
}

function changeBranchDo(item, janusIndex, force) {
    if (!force && item.janus.mdsVideoChanging) {//已经在切换
        console.log("changeBranchDo is true!");
        return;
    }
    item.janus.mdsVideoChanging = true;

    var newVideoItem = JSON.parse(JSON.stringify(item, function (key, value) {
        if (key == "videoList" || key == "videoStream" || key == "mdsVideo") {
            return;// delete
        }
        return value;
    }));

    newVideoItem.janus.videoStream = null;
    newVideoItem.janus.videoList = new Array();

    subscribeFromJanus(newVideoItem, janusIndex).then(function (session) {
        // setTimeout(function () {
        {//覆盖旧的
            gJanusMap.put(newVideoItem.ssid, newVideoItem);

            // newVideoItem.janus.videoStream.started
            //给所有标签附着新流
            newVideoItem.janus.videoList = item.janus.videoList;
            for (var index = 0; index < newVideoItem.janus.videoList.length; index++) {
                var video = newVideoItem.janus.videoList[index];
                Janus.log("attach media stream(change) #remotevideo(" + index + ")" + video);
                Janus.attachMediaStream(video, newVideoItem.janus.videoStream);
            }

            //释放旧的
            mdsVideoStopDo(item);
            console.log("changeBranchDo: then");
        }
        // }, 1000);
    }).catch(function (err) {
        console.log("changeBranchDo: catch", err);
        item.janus.mdsVideoChanging = false;

        //重试链接时,失败继续重试
        if (force)
            videoRetry(item.ssid);
    });
}

//janusIndex: 
// < 0或数组越界: 默认级别(原画);
// >= 0        : 从branch中取;
function subscribeFromJanus(item, janusIndex) {
    if (janusIndex == null || janusIndex == undefined || !item.janus.branch || janusIndex >= item.janus.branch.length)
        janusIndex = -1;

    return new Promise(function (resolve, reject) {
        //resolution切换↓
        var realPin = item.janus.pin;
        var realRoom = item.janus.room;
        var realURL = item.janus.url;
        var realPub = item.janus.pub;
        if (item.janus.branch && janusIndex >= 0 && janusIndex < item.janus.branch.length) {
            realPin = item.janus.branch[janusIndex].pin;
            realRoom = item.janus.branch[janusIndex].room;
            realURL = item.janus.branch[janusIndex].url;
        }
        if (!realPub || realPub.length <= 0) {
            realPub = "MDS" + realRoom;
        }

        //resolution切换↑
        item.janus.mdsVideo = new MdsVideoClass();
        item.janus.mdsVideoChanging = false;
        item.janus.mdsVideo.init(realURL, function () {
            videoRetry(item.ssid);
        }).then(function () {
            //================subscribe==========

            //* 修复pin码不为string的bug
            if (realPin) {
                realPin = "" + realPin;
            }

            var pubId = "";
            item.janus.mdsVideo.subscribe(realRoom, "" + item.ssid, realPin, function (event, room, id, display) {
                if (event == 'joined') {
                    console.log("Janus connection joined", room, id, display, item.ssid);
                    pubId = id + "";
                }
                if (event == 'leaving') {
                    console.log("Janus connection leaving", room, id, pubId, realPub, item.ssid);
                    if (id + "" == pubId)
                        videoRetry(item.ssid);
                }
            });

            console.log("mdsVideo.subscribeFromJanus: " + "session=" + item.ssid + ",room=" + realRoom + ",username=" + item.ipocid + ",pin=" + realPin + ",pub=" + realPub);

            var pollyRetry = 0;
            var polly = window.setInterval(function () {
                if (pollyRetry++ > 20) {
                    window.clearInterval(polly);
                    item.janus.mdsVideo.destroy();
                    item.janus.mdsVideo = null;
                    reject("webrtc connect timeout. " + item.ssid);
                    return;
                }

                var id = item.janus.mdsVideo.findMatchPluginHandle(realRoom, false);
                if ((id != -1) && item.janus.mdsVideo.remotestreams[id][realPub]) {
                    window.clearInterval(polly);
                    item.janus.videoStream = item.janus.mdsVideo.remotestreams[id][realPub];

                    //给所有标签附着流
                    for (var index = 0; index < item.janus.videoList.length; index++) {
                        var video = item.janus.videoList[index];
                        Janus.log("attach media stream(init) #remotevideo(" + index + ")" + video);
                        Janus.attachMediaStream(video, item.janus.videoStream);
                    }
                    item.janus.mdsVideoIndex = janusIndex;
                    resolve(item.ssid);

                    //pubId未正确赋值时重试链接
                    if ("" == pubId)
                        videoRetry(item.ssid);
                    return;
                }
            }, 500);
            //================subscribe==========

        }).catch(function (err) {
            console.error("mdsVideo.init: " + err);
            item.janus.mdsVideo.destroy();
            item.janus.mdsVideo = null;
            reject("mdsVideo.init: " + err);
        });
    });
}

// var gMdsRetryTimer = null;
// var gMdsRetryList = [];

function videoRetry(ssid) {
    if (!gMdsRetryList.includes(ssid)) {
        console.log("Janus retry put ssid=" + ssid);
        gMdsRetryList.push(ssid);
    }

    if (gMdsRetryTimer == null) {
        gMdsRetryTimer = window.setInterval(function () {
            if (gMdsRetryList.length > 0)
                console.log("Janus retry!! count=" + gMdsRetryList.length);
            gMdsRetryList.forEach(function (_ssid) {
                var item = gJanusMap.get(_ssid);
                if (item && item.accept && item.janus) {
                    console.log("Janus retry: ssid=" + item.ssid + " uid=" + item.ipocid);
                    try {
                        var index = item.janus.mdsVideoIndex;
                        if (index == undefined || index < -1)
                            index = -1;
                        changeBranchDo(item, index, true);
                        console.log("videoRetry then", item.ssid)
                    }
                    catch (e) {
                        console.error("videoRetry catch", item.ssid, e)
                    }
                }
            })
            gMdsRetryList = [];

        }, 1000 * 5);
    }
}

poc.video.pause = function (session) {
    var item = gJanusMap.get(session);
    if (item == null) {
        return -1;
    }

    if (!item.janus || !item.janus.mdsVideo) {
        return -2;
    }

    if (item.accept && !item.janus.mdsVideo.isPaused()) {
        //给所有标签摘流
        for (var index = 0; index < item.janus.videoList.length; index++) {
            var video = item.janus.videoList[index];
            Janus.log("detach media stream(pause) #remotevideo(" + index + ")" + video);
            Janus.attachMediaStream(video, null);
        }
        item.janus.mdsVideo.pause(item.janus.room, item.janus.pub);
    }
    return 0;
}

poc.video.resume = function (session) {
    var item = gJanusMap.get(session);
    if (item == null) {
        return -1;
    }

    if (!item.janus.mdsVideo) {
        return -2;
    }

    if (item.accept && item.janus.mdsVideo.isPaused()) {
        //给所有标签附着流
        for (var index = 0; index < item.janus.videoList.length; index++) {
            var video = item.janus.videoList[index];
            Janus.log("attach media stream(resume) #remotevideo(" + index + ")" + video);
            Janus.attachMediaStream(video, item.janus.videoStream);
        }
        item.janus.mdsVideo.resume(item.janus.room, item.janus.pub);
    }
    return 0;
}

poc.video.stop = function (session) {
    var item = gJanusMap.get(session);
    if (item == null) {
        return -1;
    }
    //给所有标签摘流
    for (var index = 0; index < item.janus.videoList.length; index++) {
        var video = item.janus.videoList[index];
        Janus.log("detach media stream(stop) #remotevideo(" + index + ")" + video);
        Janus.attachMediaStream(video, null);
    }

    mdsVideoStopDo(item);
    gJanusMap.remove(item.ssid);
}

function mdsVideoStopDo(item) {
    if (item.accept && item.janus.mdsVideo) {
        item.janus.mdsVideo.leave(item.janus.room, item.janus.pub);
        item.janus.mdsVideo.destroy();
    }
    return 0;
}

poc.video.mute = function (session, isAudio) {
    var item = gJanusMap.get(session);
    if (item == null) {
        return -1;
    }
    if (item.accept && item.janus.mdsVideo) {
        item.janus.mdsVideo.muteLocal(item.janus.room, isAudio);
        item.janus.mdsVideo.muteRemote(item.janus.room, item.janus.pub, isAudio);
    }
    return 0;
}

poc.video.unmute = function (session, isAudio) {
    var item = gJanusMap.get(session);
    if (item == null) {
        return -1;
    }
    if (item.accept && item.janus.mdsVideo) {
        item.janus.mdsVideo.unmuteRemote(item.janus.room, item.janus.pub, isAudio);
        item.janus.mdsVideo.unmuteLocal(item.janus.room, isAudio);
    }
    return 0;
}

poc.video.snapshot = function (session) {
    var item = gJanusMap.get(session);
    if (item == null) {
        return null;
    }

    if (item.janus.videoList.length <= 0) {
        return null;
    }

    var canvas = document.createElement('canvas');
    var playerVideo = item.janus.videoList[0];
    canvas.width = playerVideo.videoWidth;
    canvas.height = playerVideo.videoHeight;
    var ctx = canvas.getContext('2d');
    ctx.drawImage(playerVideo, 0, 0, canvas.width, canvas.height);
    //convert to desired file format
    // var data = canvas.toDataURL('image/jpeg').replace("data:image/jpeg;base64,", ""); // can also use 'image/png'
    var data = canvas.toDataURL('image/jpeg'); // can also use 'image/png'
    // console.log(data);
    return data;
}

poc.video.__internal_get_info = function (session) {
    var item = gJanusMap.get(session);
    if (item == null) {
        return null;
    }

    var info = JSON.parse(JSON.stringify(item, ["cls", "sid", "ipocid", "ssid", "rtsp_url", "start", "accept", "janus", "branch", "url", "pin", "room", "vw", "vh", "vkbps", "pub"]));

    //* 20201231 兼容旧版
    if (info.janus.pub) {
        info.janus.publisher = info.janus.pub;
    }

    return info;
}

function clearUnuseItem() {
    var expire = Math.round(new Date() / 1000) - 300; //300s
    gJanusMap.keySet().forEach(function (key) {
        var item = gJanusMap.get(key);
        if (item && item.start <= expire && !item.accept) {
            gJanusMap.remove(key);
        }
    });
}

function mdsVideoTimerRun() {
    if (!gMdsVideoPrintTimer) {
        gMdsVideoPrintTimer = window.setInterval(function () {
            //补丁特性: 定时清理无用项
            clearUnuseItem();

            //定时打印分辨率码率
            gJanusMap.keySet().forEach(function (key) {
                var item = gJanusMap.get(key);
                if (item) {
                    if (item.janus && item.accept && item.janus.videoList.length > 0) {
                        console.log("[" + item.ssid + "] video: " + item.janus.videoList[0].videoWidth + "x" + item.janus.videoList[0].videoHeight + "  " + item.janus.mdsVideo.getBitrate());
                    }
                }
            });
        }, gPrintTimerInterval);
    }
}