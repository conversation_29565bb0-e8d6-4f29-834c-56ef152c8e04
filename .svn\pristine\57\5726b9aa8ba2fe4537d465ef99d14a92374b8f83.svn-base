/*
 * @Descripttion:
 * @Author: <EMAIL>
 * @Date: 2022-12-11 17:54:19
 */
import Map from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/@arcgis/core/Map.js";
import SceneView from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/@arcgis/core/views/SceneView.js";

import { getTDTCGCS2000DarkLayer, getTDTCGCS2000SYLayer } from "./basemap.js";
import layerCreatAsync from "./layerCreatAsync.js";
import { getLayerConfigById } from "./layerConfig.js";
import addWaterWaveEffect from "./waterWaveEffect.js";

/**
 * 加载SceneView
 * @param {*} urlTemplate
 * @param {*} divId
 */
async function initSceneView(divId, urlTemplate) {
  const map = new Map({
    basemap:
      window.location.hostname === "127.0.0.1"
        ? getTDTCGCS2000DarkLayer()
        : getTDTCGCS2000SYLayer(),
  });
  map.ground.surfaceColor = "#08294a"; // 设置球体颜色
  const view = new SceneView({
    container: divId || "viewDiv",
    map: map,
    camera: {
      position: {
        spatialReference: {
          wkid: 4490,
        },
        x: 119.65842342884746,
        y: 28.97890877935061,
        z: 10280.48295974452,
      },
      heading: 354.2661149152386,
      tilt: 47.902020858006175,
    },
    constraints: {
      altitude: {
        min: 128,
      },
    },
  });
  view.qualitySettings.memoryLimit = 4096;
  window.view = view;
  // goHome方法
  view.goHome = () => {
    view.goTo({
      position: {
        spatialReference: {
          wkid: 4490,
        },
        x: 119.65842342884746,
        y: 28.97890877935061,
        z: 10280.48295974452,
      },
      heading: 354.2661149152386,
      tilt: 47.902020858006175,
    });
  };
  // // 加载影像注记
  // const imageMarkConfig = getLayerConfigById("IMAGE_MARK");
  // const imageMarkLayer = await layerCreatAsync(imageMarkConfig);
  // view.map.add(imageMarkLayer);

  // 添加行政区切片
  const rangeConfig = getLayerConfigById("shiliangqiepian");
  const rangeConfigLayer = await layerCreatAsync(rangeConfig);
  view.map.add(rangeConfigLayer);

  addWaterWaveEffect();

  return view;
}

export default initSceneView;
