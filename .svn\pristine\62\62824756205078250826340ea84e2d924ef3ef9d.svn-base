<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      http-equiv="origin-trial"
      content="AtOhr2NvRTD4rPvKdQBaVfFcVEQiDeBR97NMxxYIFp2F+FdWsKpROhrE1lUKhib4bVcJyxBNAOy1+90xRk3cyAYAAABgeyJvcmlnaW4iOiJodHRwOi8vbG9jYWxob3N0OjMwMDEiLCJmZWF0dXJlIjoiVW5yZXN0cmljdGVkU2hhcmVkQXJyYXlCdWZmZXIiLCJleHBpcnkiOjE2NzUyOTU5OTl9"
    />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>执法记录测试</title>
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script type="text/javascript" src="/static/js/jslib/md5.js"></script>
    <script type="text/javascript" src="./poc_sdk.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <style>
      .rwgz-tc {
        position: relative;
        width: 1500px;
        height: 1000px;
        background-image: url(/static/images/zhdd/bg_panel.png);
        background-size: 100% 100%;
        /* border: 2px solid #3a9ff8;
        background: rgba(3,24,39,.8); */
        border-radius: 57px;
      }

      .rw-title {
        position: absolute;
        top: 50px;
        z-index: 888;
        /* background: linear-gradient(#00aae2, #064b65); */
        border-radius: 57px 57px 0 0;
        width: 100%;
        height: 60px;
        line-height: 60px;
        padding: 1% 3%;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
      }

      .close {
        background: url("/static/images/zhdd/close.png") no-repeat;
        width: 34px;
        height: 34px;
        cursor: pointer;
        float: right;
      }
    </style>
  </head>

  <body>
    <div id="zfy_video" class="rwgz-tc">
      <div class="rw-title flex-between">
        <div class="fs-44 font-syBold" id="rwTitle"></div>
        <div class="close" style="z-index: 9999" @click="closeWin"></div>
      </div>
      <video
        id="videoPlay"
        controls="controls"
        autoplay="autoplay"
        muted="true"
        name="media"
        style="
          width: 94%;
          height: 90%;
          position: absolute;
          top: 75px;
          left: 50px;
        "
        ref="video"
      ></video>
      <div
        style="
          z-index: 1000;
          position: absolute;
          top: 50px;
          left: 50px;
          display: flex;
        "
      >
        <button
          style="width: 70px; height: 30px; margin-bottom: 10px; display: none"
          onclick=""
        >
          登录
        </button>
        <button
          style="width: 70px; height: 30px; margin-bottom: 10px; display: none"
          onclick=""
        >
          打开视频
        </button>
        <button
          style="width: 70px; height: 30px; margin-bottom: 10px; display: none"
          onclick=""
        >
          呼叫
        </button>
        <button
          id="pttButton"
          style="
            width: 180px;
            height: 40px;
            margin-bottom: 10px;
            font-size: 24px;
          "
          @click="ptt()"
        >
          *
        </button>
      </div>
    </div>
  </body>
  <script type="module">
    window.addEventListener(
      "message",
      function (event) {
        let that = this;
        //子获取父消息
        let newData;
        if (typeof event.data == "object") {
          newData = event.data;
        } else {
          newData = JSON.parse(event.data.argument);
        }
        if (newData.name === "openVideoTest") {
          zhddRightVm.gVideoUser = newData.videoCode[0];
        } else if (newData.name === "ddhjVideoTest") {
          zhddRightVm.gVideoUser = newData.videoCode[0];
        }
      },
      false
    );
  </script>
  <script>
    var zhddRightVm = new Vue({
      el: "#zfy_video",
      data: {
        pttButtonOnTalk: " 结束讲话 ",
        pttButtonOnIdle: " 点击开始讲话 ",
        pttButtonOnFail: " 连接失败 ",

        videoButtonMute: " Mute ",
        videoButtonUnmute: " Unmute ",
        videoButtonPause: " Pause ",
        videoButtonResume: " Resume ",

        gVideoSession: 0,
        gVideoUser: "1440641527868",
        // gVideoUser: "1440641527868",
        gVideoOwner: "",

        gUID: "",
        gPWD: "",

        personList: [],
        dataList: [],
      },
      mounted() {
        let that = this;
        //==========检测webrtc/wasm
        if (poc.isWebRTCSupported) {
          console.log("[POC] Info: WebRTC is supported!");
        } else {
          console.log("[POC] Warning: WebRTC is unsupported!");
        }

        if (poc.isWebAssemblySupported) {
          console.log("[POC] Info: WebAssembly is supported!");
        } else {
          console.log("[POC] Warning: WebAssembly is unsupported!");
        }
        if (!poc.isWebRTCSupported || !poc.isWebAssemblySupported) {
          console.log("WebRTC or WebAssembly unsupported!");
        } else {
          poc.ptt.init(function (err) {
            if (err != null) {
              if (err.name == "NotFoundError") {
                console.log(err + ". PTT Listen Only!");
              } else {
                console.log(
                  err +
                    ". POC PTT init has error! Some functions may not be available."
                );
              }
            }
            poc.ptt.setLog(true);
            that.init();
          });
        }
      },
      methods: {
        init() {
          this.login();

          // 生成会话
          setTimeout(() => {
            this.dialogCall();
          }, 2000);

          //生成视频画面
          setTimeout(() => {
            this.onVideoShare();
          }, 6000);

          //callback
          this.onContactPresence();
          this.onLocation();

          //查询位置信息
          // $get("/zhdd/lxs_zfy_person").then((res) => {
          //   this.personList = res;
          // });
          // this.queryAuth();
        },
        closeWin() {
          console.log("关闭视频");
          this.dialogBye();
          window.parent.lay.closeIframeByNames(["videoTest"]);
        },

        login() {
          let that = this;
          var addr = "https://************:4010/dm-http-interface";
          gUID = "9883210002";
          gPWD = sessionStorage.getItem("ywsPassword");
          // gUID = "11268810001";
          // gPWD = "ZJhd@1234";
          poc.ptt.doLogin(addr, gUID, gPWD);
          poc.ptt.onLogin = function (result, secret) {
            if (result == 0) {
              //登陆成功,记住用户名密码
              document.cookie = "uid=" + gUID;
              document.cookie = "pwd=" + gPWD;
            }
            console.log("onLogin: result=" + result + " secret=" + secret);
          };
        },
        logout() {
          poc.ptt.doLoginOut();
          poc.ptt.onLogout = function (result, reason) {
            console.log("onLogout: result=" + result + " reason=" + reason);
          };
        },
        // queryAuth() {
        //   let that = this;
        //   var obj = {
        //     Url: "https://************:4482/station/mobile/serverapi.action",
        //     HostIp: "", // (传空字符串即可)
        //     CustomId: "POC-1370",
        //     CustomPwd: hex_md5("Ddfk13!@"),
        //     Callback: function (res) {
        //       console.log("auth", res);
        //       that.getSession(res);
        //     },
        //   };
        //   poc.data.auth(obj);
        // },
        // getSession(e) {
        //   let that = this;
        //   var obj = {
        //     ServiceCode: e.ServiceCode,
        //     DispatcherId: "9879310001",
        //     DispatcherPwd: hex_md5("Zhcg@2023"),
        //     LoginType: 0,
        //     Callback: function (res) {
        //       console.log("getSession", res);
        //       that.getLocation(res);
        //     },
        //   };
        //   poc.data.getSession(obj);
        // },
        // getLocation(e) {
        //   let that = this;
        //   poc.ptt.onContactPresence = function (userList) {
        //     console.log("userList", userList);
        //     let ids = [];
        //     userList.forEach((item) => {
        //       ids.push(item.ipocid);
        //     });
        //     var obj = {
        //       SessionId: e.SessionId,
        //       Uids: ids,
        //       Callback: function (res) {
        //         if (res) {
        //           console.log("getLocation", res.Locations);
        //           console.log("personList", that.personList);
        //           res.Locations.map((item1) => {
        //             that.personList.map((item2) => {
        //               if (item1.Uid == item2.Uid) {
        //                 Object.assign(item1, item2);
        //                 that.dataList.push(item1);
        //               }
        //             });
        //           });
        //           debugger;
        //           console.log("dataList", that.dataList);
        //         }
        //       },
        //     };
        //     poc.data.locationGet(obj);
        //   };
        // },
        dialogCall() {
          let that = this;
          var inputStr = this.gVideoUser;
          if (inputStr == null || inputStr.length <= 0) {
            return;
          }
          var t = "tempuser";
          if (t == "sid" || t == "tempsid") {
            poc.ptt.doSessionTempCall("", inputStr, 0, function (session) {
              console.log(
                "doSessionTempCall: sid=" + inputStr + " session=" + session
              );
            });
          } else if (t == "tempuser") {
            var users = inputStr.split(",");
            poc.ptt.doSessionTempCall(users, "", 0, function (session) {
              console.log(
                "doSessionTempCall: users=" + users + " session=" + session
              );
              that.gVideoSession = session;

              // callback
              that.onSessionDialogPresence();
              that.onSessionRelease();
            });
          } else if (t == "tempinvite") {
            var users = inputStr.split(",");
            that.gVideoSession = "";
            poc.ptt.doSessionCallInvite(session, users);
            console.log(
              "doSessionCallInvite: users=" + users + " session=" + session
            );
          }
        },
        dialogBye() {
          window.parent.lay.closeIframeByNames(["videoTest"]);
          var session = this.gVideoSession;
          poc.ptt.doLeaveCall(session);
          console.log("dialogBye: session=" + session);
        },
        ptt() {
          var session = this.gVideoSession;
          var users = this.gVideoUser;
          if (
            document.getElementById("pttButton").innerHTML ==
            this.pttButtonOnIdle
          ) {
            if (users.length > 0) {
              //广播带用户
              document.getElementById("pttButton").innerHTML =
                this.pttButtonOnTalk;
              poc.ptt.doTalkRequestWithUserlist(session, users.split(","));
            } else {
              //out queue or release
              document.getElementById("pttButton").innerHTML =
                this.pttButtonOnTalk;
              poc.ptt.doTalkRequest(session);
            }
          } else {
            document.getElementById("pttButton").innerHTML =
              this.pttButtonOnIdle;
            poc.ptt.doTalkRelease(session);
          }
        },
        onVideoShare() {
          let that = this;
          poc.video.onVideoShare = function (session, sid, ipocid, valid) {
            console.log(session, sid, ipocid, valid);
            // var r = false;
            if (valid) {
              // r = confirm("onVideoShare: sid =" + sid + "ipocid=" + ipocid);
              // if (r == true) {
                that.gVideoOwner = ipocid;
                that.gVideoSession = session;
                // that.videoDup();
                var v = document.getElementById("videoPlay");
                v.autoplay = true;
                v.className = "videoDup";
                document.getElementById("videoPlay").after(v);
                poc.video.play(that.gVideoSession, v);
              // }
            } else {
              //清理多余标签
              that.cleanVideoDup();
            }

            // console.log(
            //   "onVideoShare:  session=" +
            //     session +
            //     " accept=" +
            //     r +
            //     " sid=" +
            //     sid +
            //     " ipocid=" +
            //     ipocid +
            //     " valid=" +
            //     valid
            // );
          };
        },
        videoDup() {
          var v = document.getElementById("videoPlay");
          v.autoplay = true;
          v.className = "videoDup";
          document.getElementById("videoPlay").after(v);
          poc.video.play(this.gVideoSession, v);
        },

        // callback
        onContactPresence() {
          poc.ptt.onContactPresence = function (userList) {
            console.log("userList", userList);
          };
        },

        //callback
        onLocation() {
          poc.ptt.onLocation = function (loc) {
            console.log("onLocation:  loc=" + JSON.stringify(loc));
          };
        },

        //callback
        onSessionDialogPresence() {
          let that = this;
          poc.ptt.onSessionDialogPresence = function (userList) {
            document.getElementById("pttButton").innerHTML =
              that.pttButtonOnIdle;
            console.log("onSessionDialogPresence", userList);
          };
        },

        //callback
        onSessionRelease() {
          let that = this;
          poc.ptt.onSessionRelease = function (session, reason) {
            document.getElementById("pttButton").innerHTML =
              that.pttButtonOnFail;
            console.log("onSessionRelease: " + session + " reason: " + reason);
          };
        },

        cleanVideoDup() {
          //清理多余标签
          var tags = null;
          do {
            tags = document.getElementsByClassName("videoDup");
            console.log("remove " + tags.length);
            for (i = 0; i < tags.length; i++) {
              tags[i].removeAttribute("src");
              tags[i].removeAttribute("srcObject");
              console.log(tags[i]);
              tags[i].parentNode.removeChild(tags[i]);
            }
          } while (tags != null && tags.length > 0);
        },
      },
      beforeDestroy() {},
    });
  </script>
</html>
