<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8"/>
    <title>地图iframe</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/xzzfzx_index.css"/>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/layer.js"></script>
    <script src="/static/js/jslib/Emiter.js"></script>
    <script type="text/javascript" src="/static/js/jslib/md5.js"></script>
    <link rel="stylesheet" href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css"/>
    <script type="module" src="https://csdnwlgz.dsjj.jinhua.gov.cn/ArcGisUtils/index.js"></script>
    <script src="/static/js/jslib/ArcGisUtils/libs/three-r79.min.js"></script>
    <script src="/static/js/jslib/ArcGisUtils/libs/three-r116.min.js"></script>
    <script src="/static/js/jslib/arcgis-to-geojson.js"></script>
    <script src="/static/js/jslib/biz.min.js"></script>
    <script src="/static/js/jslib/turf.js"></script>
    <script type="module" src="https://csdn.dsjj.jinhua.gov.cn:8101/static/js/jslib/gisHelper.js"></script>

    <script src="/static/js/jslib/axios.min.js"></script>
    <!-- api -->
    <script src="https://g.alicdn.com/gdt/jsapi/1.9.22/index.js"></script>
    <!-- 视频 -->
    <script src="/static/js/jslib/meeting.js"></script>
    <script src="/static/js/jslib/DHWs_tc.js"></script>
    <style>
        .esri-view-surface {
            height: 2160px !important;
        }

        [v-cloak] {
            display: none;
        }

        html,
        body {
            width: 100%;
            height: 100%;
            font: 20px "微软雅黑", Arial !important;
            user-select: none;
        }
    </style>
</head>

<body>
    <div class="map_center" id="viewDiv">
    </div>
<script src="/static/js/jslib/hideAndShow.js"></script>
<script>
  //根据县市区的切换变化地图的中心点
  let citylist = [
    {
      name: "婺城区",
      destination: [119.51328272762952, 28.905759961785844],
      zoom: 10.7,
      url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/wuchengqu/MapServer",
      id: "e242d35fdc8748b1aa12774c3d08ea04",
      urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/wucheng_szz_1/FeatureServer",
      idv: "7772867eaa16410780c68af71baa70e4",
    },
    {
      name: "金东区",
      destination: [119.79687141288449, 29.102065894175603],
      zoom: 11.3,
      url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/jinyixinqu/MapServer",
      id: "204cc0ef48a4488682f19e09b7790fca",
      urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/jinyi_szz_1/FeatureServer",
      idv: "bcc7fd7d72b745aa9b443ed9e08a6abc",
    },
    {
      name: "开发区",
      destination: [119.49024650108194, 29.0043553135286],
      zoom: 11.5,
      url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/kaifaqu/MapServer",
      id: "92decdc702c8491fa8e41fa07ccebee9",
      urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/kaifa_szz_1/FeatureServer",
      idv: "4df9f2133484421880de32eb1e6effad",
    },
    {
      name: "金华开发区",
      destination: [119.49024650108194, 29.0043553135286],
      zoom: 11.5,
      url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/kaifaqu/MapServer",
      id: "92decdc702c8491fa8e41fa07ccebee9",
      urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/kaifa_szz_1/FeatureServer",
      idv: "4df9f2133484421880de32eb1e6effad",
    },
    {
      name: "兰溪市",
      destination: [119.55667006174536, 29.20872154955791],
      zoom: 10.7,
      url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/lanxishi/MapServer",
      id: "3866fa38ae304c8e9c7fa7e5baa429f1",
      urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/lanxi_szz_1/FeatureServer",
      idv: "2f8e8266fcb9445da32e629ddda0f88b",
    },
    {
      name: "浦江县",
      destination: [119.89688053673072, 29.471222523091836],
      zoom: 11.2,
      url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/pujiangxian/MapServer",
      id: "128af7c9d45e4813ba31b1d767aa6286",
      urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/pujiang_szz_1/FeatureServer",
      idv: "aaa1adcc079a433a8b9bff1f8ade3e6a",
    },
    {
      name: "义乌市",
      destination: [120.06448988478343, 29.223500274752382],
      zoom: 10.65,
      url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/yiwushi/MapServer",
      id: "ff477c9986b442b98141d58d673053a3",
      urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/yiwu_szz_1/FeatureServer",
      idv: "4776c51c14b44cf2b26dd2707e5f1b39",
    },
    {
      name: "东阳市",
      destination: [120.38385520588402, 29.201613535927276],
      zoom: 10.667,
      url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/dongyangshi/MapServer",
      id: "69f0b9c4267e4419ae99bcffcfa9033c",
      urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/dongyang_szz_1/FeatureServer",
      idv: "59d462413a2c4321bec17ddfa8cdddc6",
    },
    {
      name: "磐安县",
      destination: [120.53881217398691, 28.996132529782322],
      zoom: 10.75,
      url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/pananxian/MapServer",
      id: "8c3e0e4d222f4a51a9429d5fb3175f1f",
      urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/panan_szz_1/FeatureServer",
      idv: "93975ebae4e84a32b4bb1e01e947f5dd",
    },
    {
      name: "永康市",
      destination: [120.1242150759098, 28.88507934117054],
      zoom: 11.2,
      url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/yongkangshi/MapServer",
      id: "0a1db3cf96854170b63e9a0c1e6b9ee4",
      urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/yongkang_szz_1/FeatureServer",
      idv: "d227a0eb749148b3b31c7024f5629bb9",
    },
    {
      name: "武义县",
      destination: [119.6912335854188, 28.707478067993392],
      zoom: 10.65,
      url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/wuyixian/MapServer",
      id: "0542c20e062344dd875900db1593a41d",
      urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/wuyi_szz_1/FeatureServer",
      idv: "96c51787c1b24f5c878321f02e6ea19e",
    },
    {
      name: "金华市",
      destination: [119.96596514308335, 29.03677381816546],
      zoom: 9.5,
      urlv: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/jinhua_szz/FeatureServer",
      idv: "079d63b2cc604baf95e1d8dd80d3cb7a",
    }
  ];
  let maskUrl11 = {
    婺城区: "/static/data/mask/wcq.json",
    金东区: "/static/data/mask/jdq.json",
    东阳市: "/static/data/mask/dys.json",
    义乌市: "/static/data/mask/yws.json",
    永康市: "/static/data/mask/yks.json",
    兰溪市: "/static/data/mask/lxs.json",
    浦江县: "/static/data/mask/pjx.json",
    武义县: "/static/data/mask/wyx.json",
    磐安县: "/static/data/mask/pax.json",
    金华开发区: "/static/data/mask/kfq.json",
  };
  window.onload = () => {
    let opts_rk = {
      x: 120.15279422339653,
      y: 27.975945853865934,
      z: 382124.06796104554,
      heading: 352.23326288174616,
      tilt: 16.407909305989698,
      basemap: "vector",
      onload: () => {
        setTimeout(() => {
          console.log();
          // localStorage.setItem("isFull", "true");
          // xzzfzx.getWeather();
          // xzzfzx.openDefaultPage();
          // eventbus.emit("cityChange", "金华市");
        }, 2000);
      },
    };
    mapUtil.initMap(opts_rk);
    if (localStorage.getItem("adminCity") == "金华市") {
      // 加载矢量边界
      window.xingzhen("金华市")
    }
    // }else{
    if (localStorage.getItem("adminCity") != "金华市") {
      citylist.forEach((item) => {
        if (item.name == localStorage.getItem('adminCity')) {
          console.log(item);
          //加载各自底图
          const view = window.ArcGisUtils.initSceneView({
            divId: 'viewDiv',
            basemap: {
              title: '地图',
              layerConfigs: {
                type: 'tile',
                id: item.id,
                url: item.url,
              },
            },
            viewingModeExtend: 'global',
            isDefaultGoToFromGlobal: false,
            camera: {
              position: {
                spatialReference: {
                  latestWkid: 4490,
                  wkid: 4490,
                },
                x: item.destination[0],
                y: item.destination[1],
                z: 157992.386058662087,
              },
              heading: 352.20391187199436,
              tilt: 0,
            }
          })
          //加载矢量边界
          window.xingzhen(localStorage.getItem('adminCity'))
          window.mapMask(item.name, [8, 40, 73, 1]);
        }
      })
    }
    //下面的view.when是为了从home页面直接点击执法态势
    view.when(()=>{
        // 执法态势页面
        if (localStorage.getItem('currentPage2')=="执法态势"){
            //以下图层不显示
            let merge = view.map.findLayerById("VECTOR_POI_Merge")
            if (merge) merge.visible = false
            let dem = view.map.findLayerById("JINHUA_DEM")
            if (dem) dem.visible = false
            let label = view.map.findLayerById("VECTOR_Road_Label")
            if (label) label.visible = false
            let vector = view.map.findLayerById("VECTOR")
            if (vector) vector.visible = false
            if (localStorage.getItem("adminCity")=="金华市"){
                window.xingzhenlayer.visible=false
            }else{
                mapUtil.removeLayer("mask")
            }
            //加载板块图层
            window.banKuai(localStorage.getItem("adminCity"));
        }
    })
    view.watch('zoom', () => {
      if (view.zoom < 9.5) view.zoom = 9.5
      if (view.map.findLayerById("WATER_M")) {
        let layerWM = view.map.findLayerById("WATER_M");
        view.map.remove(layerWM);
      }
      if (localStorage.getItem('currentPage2')!="执法态势")
      {
          if (view.zoom > 11) {
              window.xingzhenlayer.visible = true
          }
          if (view.zoom < 11) {
              window.xingzhenlayer.visible = false
          }
      }
    })
    //  监听view中鼠标动作，高亮图像
    let graphic
    let highlightedList = [];
    view
        .on("pointer-move",(event) => {
            if (localStorage.getItem("currentPage2")=="执法态势"){
                view
                    .hitTest(event,{exclude:[view.graphics]})
                    .then((hitTestResult) => {
                        if (hitTestResult.results[0]) {
                            if (hitTestResult.results.length>1){
                                graphic = hitTestResult.results[1].graphic
                            }else{
                                graphic = hitTestResult.results[0].graphic;
                            }
                            // highlight the hit object
                            view.whenLayerView(graphic.layer).then((layerView) => {
                                highlightedList.push(layerView.highlight(graphic));
                            });
                            highlightedList.forEach((highlightObject) => {
                                highlightObject.remove();
                                mapUtil.removeLayer("text1");
                            });
                            if (localStorage.getItem("city")!="金华市"){
                                mapUtil.loadTextLayer({
                                    layerid: "text1",
                                    data: [{
                                        pos:[hitTestResult.results[0].mapPoint.x,hitTestResult.results[0].mapPoint.y,501],
                                        text:graphic.attributes.name,
                                        id:1
                                    }],
                                    style: {
                                        size: 40,
                                        color: '#2b2d6a',
                                    },
                                });
                            }
                        } else {
                            highlightedList.forEach((highlightObject) => {
                                highlightObject.remove();
                                mapUtil.removeLayer("text1");
                            });
                        }
                    })
            }
        })
  }
  //根据县市区的切换更新地图视角
  window.parent.eventbus &&
  window.parent.eventbus.on("cityChange", (city) => {
    citylist.forEach((item) => {
      if (item.name == city) {
          //在执法态势页面不需要加载mask
          if (localStorage.getItem("currentPage2")!="执法态势"){
              mapUtil.removeLayer("mask")
              window.mapMask(city,[8, 40, 73, 0.9]);

          }else{
              view.map.remove(window.xingzhenlayer)
              if (localStorage.getItem("city")!="金华市"){
                  window.xingzhen(localStorage.getItem('city'))
              }
          }
        mapUtil.flyTo(item)
      }
    })
  });
  // 监听esc键并将一键调度弹框复位
  document.addEventListener('keydown', function(e) {
    // Esc按键是27
    if (e.keyCode == 27) {
      parent.postMessage("keydown")
      //在这里写你的操作逻辑
      console.log("打印了");
      // document.getElementById("zhddDispatch").style.top = "380px";
    }
  });
  /**
   * 添加执法态势页面的板块
   * @param url 当前县市区名称
   */
  function banKuai(url){
      axios({
          method: "get",
          url: "/static/data/zfts/" + url + ".json",

      }).then((res)=> {
          mapUtil.mask({
              layerid: "bankuai",
              data: res.data, // res 是要素集合
              style: {
                  strokeWidth: localStorage.getItem("city")=="金华市"?2:1.5,
                  strokeColor: '#60e4ff', //多边形轮廓颜色透明度
                  fillColor: '#10b3ff', //多边形填充色
                  height: 50,
              },
              onclick: null,
          });
      })
      let textData = [
          {pos:[119.602579, 29.070607,500],text: "开发区"},
          {pos:[119.514748, 28.964012, 500],text: "婺城区"},
          {pos:[119.799596, 29.149391, 500],text: "金东区"},
          {pos:[119.714529, 28.768287, 500],text: "武义县"},
          {pos:[119.903937, 29.520086, 500],text: "浦江县"},
          {pos:[120.609672, 29.007893, 500],text: "磐安县"},
          {pos:[119.526736, 29.278165, 500],text: "兰溪市"},
          {pos:[120.061011, 29.300614, 500],text: "义乌市"},
          {pos:[120.364678, 29.232405, 500],text: "东阳市"},
          {pos:[120.102417, 28.934317, 500],text: "永康市"},
      ]
      let textData1=[];
      let color=[];
      switch (url){
          case "婺城区": textData1 = [{pos: [119.514748, 28.964012],text: "婺城区"}];
              break
          case "金东区":  textData1 = [{pos: [119.799596, 29.149391],text: "金东区"}];
              break
          case "金华开发区":  textData1 = [{pos: [119.602579, 29.070607],text: "开发区"}];
              break
          case "兰溪市":  textData1 = [{pos: [119.526736, 29.278165],text: "兰溪市"}];
              break
          case "浦江县":  textData1 = [{pos: [119.903937, 29.520086],text: "浦江县"}];
              break
          case "义乌市":  textData1 = [{pos: [120.061011, 29.300614],text: "义乌市"}];
              break
          case "东阳市":  textData1 = [{pos: [120.375678, 29.232405],text: "东阳市"}];
              break
          case "磐安县":  textData1 = [{pos: [120.559672, 29.037893],text: "磐安县"}];
              break
          case "永康市":  textData1 = [{pos: [120.102417, 28.934317],text: "永康市"}];
              break
          case "武义县":  textData1 = [{pos: [119.714529, 28.768287],text: "武义县"}];
              break
      }
      mapUtil.loadTextLayer({
          layerid: "map_text",
          data: localStorage.getItem("city")=="金华市"?textData:textData1,
          style: {
              size: localStorage.getItem("city")=="金华市"?45:60,
              color: '#2b2d6a',
          },
      });
  }
  /**
   * 添加行政区划图层
   * @param city 当前的县市区
   */
  function xingzhen(city) {
      citylist.forEach((item) => {
          if (item.name == city) {
              window.ArcGisUtils.loadArcgisLayer(view, {
                  layerid: "xingzhenlayer",
                  type: 'feature',
                  id: item.idv || "",
                  renderer: {
                      type: "simple", // autocasts as new SimpleRenderer()
                      symbol: {
                          type: "simple-fill", // autocasts as new SimpleFillSymbol()
                          outline: {
                              // makes the outlines of all features consistently light gray
                              color: "lightgray",
                              width: 1
                          }
                      }
                  },
                  labelingInfo: [
                      {
                          labelExpressionInfo: {expression: '$feature.szz'},
                          symbol: {
                              type: 'text', // autocasts as new TextSymbol()
                              color: '#007cc9',
                              haloSize: 0,
                              haloColor: 'white',
                              font: {
                                  size: 20,
                                  weight: 'normal',
                              },
                          },
                      },
                  ],
                  url: item.urlv || "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/jinhua_szz/FeatureServer",
              }).then((xingzhenlayer) => {
                  window.xingzhenlayer = xingzhenlayer
                  window.xingzhenlayer.visible = localStorage.getItem("currentPage2")!="执法态势"?false:true
              })
          }
      })
  }
  /**
   * 添加掩膜
   * @param name
   * @param color
   */
  function mapMask(name,color) {
    axios({
      method: "get",
      url: maskUrl11[name],
    }).then((res) => {
      console.log(res.data)
      mapUtil.mask({
        layerid: "mask",
        data: res.data.data, // res 是要素集合
        style: {
          strokeWidth: 1.5,
          strokeColor: [29, 252, 254, 1], //多边形轮廓颜色透明度
          fillColor: color, //多边形填充色
          height: 50,
        },
        onclick: null,
      });
    })
  };
</script>
</body>

</html>
