// import registerEchartCoordinateSystem, {
//   COORD_SYSTEM_NAME,
// } from "./GeoSecneCoordinateSystem.js";

import SpatialReference from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/@arcgis/core/geometry/SpatialReference.js";

export const COORD_SYSTEM_NAME = "geoScene";
let isRegistered = false;

export function registerEchartCoordinateSystem(view) {
  if (!view) {
    console.error("没有传入view，或者view的值不生效");
  }

  if (!isRegistered) {
    echarts.registerCoordinateSystem(
      COORD_SYSTEM_NAME,
      getCoordinateSystem(view, ["x", "y"], COORD_SYSTEM_NAME)
    );
    isRegistered = true;
  }

  return isRegistered;
}

function getCoordinateSystem(
  view,
  dimensions = ["lng", "lat"],
  coordinateSystemName = COORD_SYSTEM_NAME
) {
  function CoordSystem(view) {
    this.view = view;
    this._mapOffset = [0, 0];
  }
  CoordSystem.create = function (ecModel) {
    ecModel.eachSeries(function (seriesModel) {
      if (seriesModel.get("coordinateSystem") === coordinateSystemName) {
        seriesModel.coordinateSystem = new CoordSystem(view);
      }
    });
  };
  CoordSystem.getDimensionsInfo = function () {
    return dimensions;
  };
  CoordSystem.dimensions = dimensions;
  CoordSystem.prototype.dimensions = dimensions;
  CoordSystem.prototype.setMapOffset = function setMapOffset(mapOffset) {
    this._mapOffset = mapOffset;
  };
  CoordSystem.prototype.dataToPoint = function dataToPoint(data) {
    var point = {
      type: "point",
      x: data[0],
      y: data[1],
      spatialReference: new SpatialReference(4326),
      // spatialReference: new SpatialReference({ wkid: 4326 }),
      // spatialReference: { wkid: 4326 },
    };
    var px = view.toScreen(point);
    var mapOffset = this._mapOffset;
    return [px.x - mapOffset[0], px.y - mapOffset[1]];
  };
  CoordSystem.prototype.pointToData = function pointToData(pt) {
    var mapOffset = this._mapOffset;
    var screenPoint = {
      x: pt[0] + mapOffset[0],
      y: pt[1] + mapOffset[1],
    };
    var data = view.toMap(screenPoint);
    return [data.x, data.y];
  };
  CoordSystem.prototype.getViewRect = function getViewRect() {
    return new echarts.graphic.BoundingRect(
      0,
      0,
      this.view.width,
      this.view.height
    );
  };
  CoordSystem.prototype.getRoamTransform = function getRoamTransform() {
    return echarts.matrix.create();
  };
  return CoordSystem;
}

const data = [
  { name: "菏泽", value: 19, id: 1, type: 0, x: 115.480656, y: 35.23375 },
  { name: "合肥", value: 22, id: 2, type: 1, x: 117.27, y: 31.86 },
  { name: "武汉", value: 27, id: 3, type: 1, x: 114.31, y: 30.52 },
  { name: "大庆", value: 27, id: 4, type: 2, x: 125.03, y: 46.58 },
];

function convertData(e) {
  return {
    name: e.name,
    value: [e.x || e.lon, e.y || e.lat, e.value],
    _item: e,
  };
}

function batchConvertData(data) {
  var res = [];
  for (var i = 0; i < data.length; i++) {
    res.push(convertData(data[i]));
  }
  return res;
}

export const option = {
  tooltip: {
    trigger: "item",
  },
  series: [
    {
      name: "activeCity",
      type: "effectScatter",
      coordinateSystem: COORD_SYSTEM_NAME,
      data: batchConvertData(data.slice(0, 1)),
      symbolSize: function (val) {
        return val[2]; // 大小
      },
      encode: {
        value: 2,
      },
      showEffectOn: "render",
      rippleEffect: {
        brushType: "stroke",
      },
      label: {
        formatter: "{b}",
        position: "right",
        show: true,
        color: "#F3AC3D",
      },
      tooltip: {
        show: false,
      },
      itemStyle: {
        shadowBlur: 10,
        shadowColor: "#F3AC3D",
        color: "#F3AC3D",
      },
      emphasis: {
        scale: true,
      },
      zlevel: 1,
    },
  ],
};

export class ScatterToMap {
  /**
   *
   * @param {*} option { dataset, legend, maxRadius, minRadius, pieConfig }
   * @param {*} view
   */
  constructor(option, view) {
    registerEchartCoordinateSystem(view);
    // 配置参数
    this.chart = null; // echart实例
    this.chartOption = null;
    this.box = null; // echarts挂载容器
    this.view = view;

    this.minValue = 10000000000;
    this.maxValue = 0;

    this._isUpdating = false;

    // 地图事件监听函数
    this.viewMoveListener = null;
    this.viewClickListener = null;

    this.debounceTimer = null;
    this.isDebounce = true;

    this.option = option;
  }

  // 触发重渲染的属性 ============================================================

  // 设置新的option
  get option() {
    return this._option;
  }

  set option(val) {
    if (!val) {
      console.error("有重要的初始化参数没传入，请检查");
      return;
    }

    let isInit = !this._option;
    this._option = val;
    this.chartOption = val;
    // this._calcRange();
    if (isInit) {
      this._init();
    } else {
      this._visible = true;
      this._updateChartAndLocation();
    }
  }

  // 设置图层是否可见
  get visible() {
    return this._visible;
  }

  set visible(val) {
    if (this._visible === !!val) {
      return;
    }
    this._visible = !!val;
    let isInit = !this._option;
    if (!!val) {
      !isInit && this._updateChartAndLocation();
    } else {
      this.chart && this.chart.clear();
    }
  }

  _init() {
    this._visible = true;
    this._createChart();
  }

  // _calcRange() {
  //   if (this._option?.series) {
  //     this._option.series.forEach((seriesItem) => {
  //       seriesItem.data.forEach(({ value }) => {
  //         this.minValue = Math.min(value, this.minValue);
  //         this.maxValue = Math.max(value, this.maxValue);
  //       });
  //     });
  //   }
  // }

  /*创建layer的容器，添加到map的layers下面*/
  _createChart() {
    // 创建并挂载echarts容器
    let box = (this.box = document.createElement("div"));
    box.className = "scatter_chart2map";
    // box.style.pointerEvents = 'none';
    box.setAttribute("id", Date.now());
    box.style.width = this.view.width + "px";
    box.style.height = this.view.height + "px";
    box.style.position = "absolute";
    box.style.top = 0;
    box.style.left = 0;
    const parent =
      this.view.container.getElementsByClassName("esri-view-surface")[0];
    parent.appendChild(box);

    // 创建echarts实例
    this.chart = echarts.init(box);
    this.chart.setOption.call(this.chart, this.chartOption);
    this._startMapEventListeners();
  }

  /*监听地图事件，根据图层是否显示，判断是否重绘echarts*/
  _startMapEventListeners() {
    let view = this.view;
    let self = this;
    this.viewMoveListener = view.watch(
      ["interacting", "animation", "viewpoint"],
      function (evt) {
        // todo 得加一个地球背后hide的功能
        if (!self._visible) return;
        // 地图移动时停止渲染
        if (self.isDebounce) {
          self.chart.clear.call(self.chart);
          self.debounceTimer && clearTimeout(self.debounceTimer);
          self.debounceTimer = setTimeout(() => {
            self._updateChartAndLocation(true);
          }, 300);
        } else {
          self._updateChartAndLocation();
        }
        // self.chart.resize();
        // self.box.hidden = false;
      }
    );
  }

  // 设置图表参数
  _updateChartAndLocation(withAnimation) {
    const self = this;
    if (self._isUpdating) {
      return;
    }
    self._isUpdating = true;

    requestAnimationFrame(() => {
      if (self._visible && self.chartOption && self.chart) {
        let baseExtent = self.view.extent;

        // 修改配置中的饼图中心点
        try {
          self.chartOption.xAxis = {
            show: false,
            min: baseExtent.xmin,
            max: baseExtent.xmax,
          };
          self.chartOption.yAxis = {
            show: false,
            min: baseExtent.ymin,
            max: baseExtent.ymax,
          };
          let chartOption = self.chartOption;
          self.chart.clear.call(self.chart);
          self.chart.setOption.call(self.chart, chartOption);
          chartOption.animation = !!withAnimation;
        } catch (error) {
          console.error(error);
        }
      }

      if (!self._visible && self.chart) {
        self.chart.clear.call(self.chart);
      }

      self._isUpdating = false;
    });
  }

  // 注册echarts事件
  on(eventName, handler, context) {
    this.chart.on(eventName, handler, context);
  }
  // 清除echarts事件
  off(eventName, handler, context) {
    this.chart.off(eventName, handler, context);
  }

  // 销毁
  destroy() {
    if (this.box?.outerHTML) {
      this.box.outerHTML = "";
    }
    this.view = null;
    this.box = null;
    this.originLyr = null;
    this.features = null;
    this.screenData = [];
    this.chart = null;
    this.chartOption = null;
    this.viewMoveListener && this.viewMoveListener.remove();
    // this.viewClickListener && this.viewClickListener.remove();
  }
}

ScatterToMap.create = (view, data) => {
  let _option = JSON.parse(JSON.stringify(option));
  _option.series[0].data = data;
  return new ScatterToMap(_option, view);
};
