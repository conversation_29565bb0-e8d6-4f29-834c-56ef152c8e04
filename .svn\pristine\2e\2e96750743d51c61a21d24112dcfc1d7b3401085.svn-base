<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>效果</title>

    <link
      rel="stylesheet"
      href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <!-- <script src="./libs/three-r79.min.js"></script> -->
    <script src="./index.js" type="module"></script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        left: 50%;
        width: 50%;
        height: 200px;
        display: flex;
      }

      .tools span {
        cursor: pointer;
        background-color: blue;
        width: 150px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        color: white;
      }

      #weather-box {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv">
      <div class="tools">
        <span class="set-cam">定位</span>
        <!-- <span
          id="'set-cam"
          onclick="ArcGisUtils.setXYPos(window.view,{x: 119.7048288269463,y: 27.99616488279615})"
          >定位</span
        > -->
      </div>
      <div
        class="input-pos"
        style="
          width: 230px;
          position: absolute;
          top: 0;
          left: 80px;
          z-index: 99;
          background: white;
        "
      >
        <label for="cheese">视宽: </label>
        <input type="text" class="c-fov" value="" />

        <label for="cheese">俯角: </label>
        <input type="text" class="c-tilt" value="" />
        <label for="cheese">航向: </label>
        <input type="text" class="c-heading" value="" />
      </div>
    </div>
  </body>
  <script>
    function handleCamClick() {
      let fov = document.querySelector(".c-fov").value;
      let tilt = document.querySelector(".c-tilt").value;
      let heading = document.querySelector(".c-heading").value;

      ArcGisUtils.setCam(window.view, {
        fov,
        tilt,
        heading,
      });
      ArcGisUtils.updateCam(window.view, {
        heading: ".c-heading",
        tilt: ".c-tilt",
        fov: ".c-fov",
      });
    }

    document
      .querySelector(".set-cam")
      .addEventListener("click", handleCamClick);
  </script>
</html>
