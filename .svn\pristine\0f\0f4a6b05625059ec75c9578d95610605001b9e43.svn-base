// 路径图层
import * as externalRenderers from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/@arcgis/core/views/3d/externalRenderers.js";
import * as projection from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/@arcgis/core/geometry/projection.js";
import Graphic from "https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/@arcgis/core/Graphic.js";
import { layerCreate } from "../core.js";

const VS = /* glsl */ `
attribute vec4 a_Position;

uniform mat4 u_Camera;

varying lowp float v_Depth; 

void main(){
  gl_Position = u_Camera * a_Position;
  v_Depth = a_Position.z;
}`;
// 车底盘碎片着色器
const FS = /* glsl */ `
precision mediump float;

varying lowp float v_Depth; 

void main(){
  gl_FragColor = vec4(vec3(1.0, 0.0, 0.0) * v_Depth, 1.0);
}`;


// 轮胎顶点
const lunzi_position = [
  1.40159, -0.57848, 0.00972, 1.25398, -0.57848, 0.00972, 1.32778, -0.57848,
  0.0, 1.25398, -0.57848, 0.00972, 1.40159, -0.57848, 0.00972, 1.47036,
  -0.57848, 0.0382, 1.40159, -0.57848, 0.00972, 1.47036, -0.79848, 0.0382,
  1.47036, -0.57848, 0.0382, 1.47036, -0.79848, 0.0382, 1.40159, -0.57848,
  0.00972, 1.40159, -0.79848, 0.00972, 1.32778, -0.57848, 0.0, 1.40159,
  -0.79848, 0.00972, 1.40159, -0.57848, 0.00972, 1.40159, -0.79848, 0.00972,
  1.32778, -0.57848, 0.0, 1.32778, -0.79848, 0.0, 1.25398, -0.57848, 0.00972,
  1.32778, -0.79848, 0.0, 1.32778, -0.57848, 0.0, 1.32778, -0.79848, 0.0,
  1.25398, -0.57848, 0.00972, 1.25398, -0.79848, 0.00972, 1.18521, -0.57848,
  0.0382, 1.25398, -0.79848, 0.00972, 1.25398, -0.57848, 0.00972, 1.25398,
  -0.79848, 0.00972, 1.18521, -0.57848, 0.0382, 1.18521, -0.79848, 0.0382,
  1.12615, -0.57848, 0.08352, 1.18521, -0.79848, 0.0382, 1.18521, -0.57848,
  0.0382, 1.18521, -0.79848, 0.0382, 1.12615, -0.57848, 0.08352, 1.12615,
  -0.79848, 0.08352, 1.12615, -0.57848, 0.08352, 1.08084, -0.79848, 0.14257,
  1.12615, -0.79848, 0.08352, 1.08084, -0.79848, 0.14257, 1.12615, -0.57848,
  0.08352, 1.08084, -0.57848, 0.14257, 1.12615, -0.57848, 0.08352, 1.57473,
  -0.57848, 0.14257, 1.08084, -0.57848, 0.14257, 1.12615, -0.57848, 0.08352,
  1.52941, -0.57848, 0.08352, 1.57473, -0.57848, 0.14257, 1.18521, -0.57848,
  0.0382, 1.52941, -0.57848, 0.08352, 1.12615, -0.57848, 0.08352, 1.18521,
  -0.57848, 0.0382, 1.47036, -0.57848, 0.0382, 1.52941, -0.57848, 0.08352,
  1.25398, -0.57848, 0.00972, 1.47036, -0.57848, 0.0382, 1.18521, -0.57848,
  0.0382, 1.47036, -0.57848, 0.0382, 1.52941, -0.79848, 0.08352, 1.52941,
  -0.57848, 0.08352, 1.52941, -0.79848, 0.08352, 1.47036, -0.57848, 0.0382,
  1.47036, -0.79848, 0.0382, 1.47036, -0.79848, 0.0382, 1.12615, -0.79848,
  0.08352, 1.52941, -0.79848, 0.08352, 1.47036, -0.79848, 0.0382, 1.18521,
  -0.79848, 0.0382, 1.12615, -0.79848, 0.08352, 1.40159, -0.79848, 0.00972,
  1.18521, -0.79848, 0.0382, 1.47036, -0.79848, 0.0382, 1.40159, -0.79848,
  0.00972, 1.25398, -0.79848, 0.00972, 1.18521, -0.79848, 0.0382, 1.25398,
  -0.79848, 0.00972, 1.40159, -0.79848, 0.00972, 1.32778, -0.79848, 0.0,
  1.52941, -0.79848, 0.08352, 1.12615, -0.79848, 0.08352, 1.08084, -0.79848,
  0.14257, 1.52941, -0.79848, 0.08352, 1.08084, -0.79848, 0.14257, 1.57473,
  -0.79848, 0.14257, 1.57473, -0.79848, 0.14257, 1.08084, -0.79848, 0.14257,
  1.05235, -0.79848, 0.21135, 1.08084, -0.57848, 0.14257, 1.05235, -0.79848,
  0.21135, 1.08084, -0.79848, 0.14257, 1.05235, -0.79848, 0.21135, 1.08084,
  -0.57848, 0.14257, 1.05235, -0.57848, 0.21135, 1.08084, -0.57848, 0.14257,
  1.57473, -0.57848, 0.14257, 1.05235, -0.57848, 0.21135, 1.05235, -0.57848,
  0.21135, 1.57473, -0.57848, 0.14257, 1.60322, -0.57848, 0.21135, 1.57473,
  -0.79848, 0.14257, 1.60322, -0.57848, 0.21135, 1.57473, -0.57848, 0.14257,
  1.60322, -0.57848, 0.21135, 1.57473, -0.79848, 0.14257, 1.60322, -0.79848,
  0.21135, 1.57473, -0.79848, 0.14257, 1.05235, -0.79848, 0.21135, 1.60322,
  -0.79848, 0.21135, 1.60322, -0.79848, 0.21135, 1.05235, -0.79848, 0.21135,
  1.04263, -0.79848, 0.28515, 1.05235, -0.57848, 0.21135, 1.04263, -0.79848,
  0.28515, 1.05235, -0.79848, 0.21135, 1.04263, -0.79848, 0.28515, 1.05235,
  -0.57848, 0.21135, 1.04263, -0.57848, 0.28515, 1.05235, -0.57848, 0.21135,
  1.60322, -0.57848, 0.21135, 1.04263, -0.57848, 0.28515, 1.04263, -0.57848,
  0.28515, 1.60322, -0.57848, 0.21135, 1.61293, -0.57848, 0.28515, 1.60322,
  -0.79848, 0.21135, 1.61293, -0.57848, 0.28515, 1.60322, -0.57848, 0.21135,
  1.61293, -0.57848, 0.28515, 1.60322, -0.79848, 0.21135, 1.61293, -0.79848,
  0.28515, 1.60322, -0.79848, 0.21135, 1.04263, -0.79848, 0.28515, 1.61293,
  -0.79848, 0.28515, 1.61293, -0.79848, 0.28515, 1.04263, -0.79848, 0.28515,
  1.05235, -0.79848, 0.35895, 1.04263, -0.57848, 0.28515, 1.05235, -0.79848,
  0.35895, 1.04263, -0.79848, 0.28515, 1.05235, -0.79848, 0.35895, 1.04263,
  -0.57848, 0.28515, 1.05235, -0.57848, 0.35895, 1.04263, -0.57848, 0.28515,
  1.60322, -0.57848, 0.35895, 1.05235, -0.57848, 0.35895, 1.04263, -0.57848,
  0.28515, 1.61293, -0.57848, 0.28515, 1.60322, -0.57848, 0.35895, 1.61293,
  -0.79848, 0.28515, 1.60322, -0.57848, 0.35895, 1.61293, -0.57848, 0.28515,
  1.60322, -0.57848, 0.35895, 1.61293, -0.79848, 0.28515, 1.60322, -0.79848,
  0.35895, 1.61293, -0.79848, 0.28515, 1.05235, -0.79848, 0.35895, 1.60322,
  -0.79848, 0.35895, 1.60322, -0.79848, 0.35895, 1.05235, -0.79848, 0.35895,
  1.08084, -0.79848, 0.42772, 1.05235, -0.57848, 0.35895, 1.08084, -0.79848,
  0.42772, 1.05235, -0.79848, 0.35895, 1.08084, -0.79848, 0.42772, 1.05235,
  -0.57848, 0.35895, 1.08084, -0.57848, 0.42772, 1.05235, -0.57848, 0.35895,
  1.60322, -0.57848, 0.35895, 1.08084, -0.57848, 0.42772, 1.08084, -0.57848,
  0.42772, 1.60322, -0.57848, 0.35895, 1.57473, -0.57848, 0.42772, 1.60322,
  -0.79848, 0.35895, 1.57473, -0.57848, 0.42772, 1.60322, -0.57848, 0.35895,
  1.57473, -0.57848, 0.42772, 1.60322, -0.79848, 0.35895, 1.57473, -0.79848,
  0.42772, 1.60322, -0.79848, 0.35895, 1.08084, -0.79848, 0.42772, 1.57473,
  -0.79848, 0.42772, 1.57473, -0.79848, 0.42772, 1.08084, -0.79848, 0.42772,
  1.12615, -0.79848, 0.48678, 1.08084, -0.57848, 0.42772, 1.12615, -0.79848,
  0.48678, 1.08084, -0.79848, 0.42772, 1.12615, -0.79848, 0.48678, 1.08084,
  -0.57848, 0.42772, 1.12615, -0.57848, 0.48678, 1.08084, -0.57848, 0.42772,
  1.57473, -0.57848, 0.42772, 1.12615, -0.57848, 0.48678, 1.12615, -0.57848,
  0.48678, 1.57473, -0.57848, 0.42772, 1.52941, -0.57848, 0.48678, 1.57473,
  -0.79848, 0.42772, 1.52941, -0.57848, 0.48678, 1.57473, -0.57848, 0.42772,
  1.52941, -0.57848, 0.48678, 1.57473, -0.79848, 0.42772, 1.52941, -0.79848,
  0.48678, 1.57473, -0.79848, 0.42772, 1.12615, -0.79848, 0.48678, 1.52941,
  -0.79848, 0.48678, 1.52941, -0.79848, 0.48678, 1.12615, -0.79848, 0.48678,
  1.18521, -0.79848, 0.5321, 1.12615, -0.79848, 0.48678, 1.18521, -0.57848,
  0.5321, 1.18521, -0.79848, 0.5321, 1.18521, -0.57848, 0.5321, 1.12615,
  -0.79848, 0.48678, 1.12615, -0.57848, 0.48678, 1.12615, -0.57848, 0.48678,
  1.52941, -0.57848, 0.48678, 1.18521, -0.57848, 0.5321, 1.18521, -0.57848,
  0.5321, 1.52941, -0.57848, 0.48678, 1.47036, -0.57848, 0.5321, 1.52941,
  -0.57848, 0.48678, 1.47036, -0.79848, 0.5321, 1.47036, -0.57848, 0.5321,
  1.47036, -0.79848, 0.5321, 1.52941, -0.57848, 0.48678, 1.52941, -0.79848,
  0.48678, 1.52941, -0.79848, 0.48678, 1.18521, -0.79848, 0.5321, 1.47036,
  -0.79848, 0.5321, 1.47036, -0.79848, 0.5321, 1.18521, -0.79848, 0.5321,
  1.25398, -0.79848, 0.56058, 1.18521, -0.79848, 0.5321, 1.25398, -0.57848,
  0.56058, 1.25398, -0.79848, 0.56058, 1.25398, -0.57848, 0.56058, 1.18521,
  -0.79848, 0.5321, 1.18521, -0.57848, 0.5321, 1.18521, -0.57848, 0.5321,
  1.47036, -0.57848, 0.5321, 1.25398, -0.57848, 0.56058, 1.25398, -0.57848,
  0.56058, 1.47036, -0.57848, 0.5321, 1.40159, -0.57848, 0.56058, 1.47036,
  -0.57848, 0.5321, 1.40159, -0.79848, 0.56058, 1.40159, -0.57848, 0.56058,
  1.40159, -0.79848, 0.56058, 1.47036, -0.57848, 0.5321, 1.47036, -0.79848,
  0.5321, 1.47036, -0.79848, 0.5321, 1.25398, -0.79848, 0.56058, 1.40159,
  -0.79848, 0.56058, 1.40159, -0.79848, 0.56058, 1.25398, -0.79848, 0.56058,
  1.32778, -0.79848, 0.5703, 1.25398, -0.79848, 0.56058, 1.32778, -0.57848,
  0.5703, 1.32778, -0.79848, 0.5703, 1.32778, -0.57848, 0.5703, 1.25398,
  -0.79848, 0.56058, 1.25398, -0.57848, 0.56058, 1.25398, -0.57848, 0.56058,
  1.40159, -0.57848, 0.56058, 1.32778, -0.57848, 0.5703, 1.40159, -0.57848,
  0.56058, 1.32778, -0.79848, 0.5703, 1.32778, -0.57848, 0.5703, 1.32778,
  -0.79848, 0.5703, 1.40159, -0.57848, 0.56058, 1.40159, -0.79848, 0.56058,
  1.57473, -0.57848, 0.14257, 1.52941, -0.79848, 0.08352, 1.57473, -0.79848,
  0.14257, 1.52941, -0.79848, 0.08352, 1.57473, -0.57848, 0.14257, 1.52941,
  -0.57848, 0.08352, -1.52765, -0.79848, 0.35895, -1.53737, -0.57848, 0.28515,
  -1.52765, -0.57848, 0.35895, -1.53737, -0.57848, 0.28515, -1.52765, -0.79848,
  0.35895, -1.53737, -0.79848, 0.28515, -0.96707, -0.79848, 0.28515, -1.53737,
  -0.79848, 0.28515, -1.52765, -0.79848, 0.35895, -0.97678, -0.79848, 0.21135,
  -1.53737, -0.79848, 0.28515, -0.96707, -0.79848, 0.28515, -0.97678, -0.79848,
  0.21135, -1.52765, -0.79848, 0.21135, -1.53737, -0.79848, 0.28515, -1.00527,
  -0.79848, 0.14257, -1.52765, -0.79848, 0.21135, -0.97678, -0.79848, 0.21135,
  -1.00527, -0.79848, 0.14257, -1.49916, -0.79848, 0.14257, -1.52765, -0.79848,
  0.21135, -1.05059, -0.79848, 0.08352, -1.49916, -0.79848, 0.14257, -1.00527,
  -0.79848, 0.14257, -1.05059, -0.79848, 0.08352, -1.45385, -0.79848, 0.08352,
  -1.49916, -0.79848, 0.14257, -1.10964, -0.79848, 0.0382, -1.45385, -0.79848,
  0.08352, -1.05059, -0.79848, 0.08352, -1.10964, -0.79848, 0.0382, -1.39479,
  -0.79848, 0.0382, -1.45385, -0.79848, 0.08352, -1.17841, -0.79848, 0.00972,
  -1.39479, -0.79848, 0.0382, -1.10964, -0.79848, 0.0382, -1.17841, -0.79848,
  0.00972, -1.32602, -0.79848, 0.00972, -1.39479, -0.79848, 0.0382, -1.32602,
  -0.79848, 0.00972, -1.17841, -0.79848, 0.00972, -1.25222, -0.79848, 0.0,
  -1.17841, -0.79848, 0.00972, -1.25222, -0.57848, 0.0, -1.25222, -0.79848, 0.0,
  -1.25222, -0.57848, 0.0, -1.17841, -0.79848, 0.00972, -1.17841, -0.57848,
  0.00972, -1.10964, -0.79848, 0.0382, -1.17841, -0.57848, 0.00972, -1.17841,
  -0.79848, 0.00972, -1.17841, -0.57848, 0.00972, -1.10964, -0.79848, 0.0382,
  -1.10964, -0.57848, 0.0382, -1.05059, -0.79848, 0.08352, -1.10964, -0.57848,
  0.0382, -1.10964, -0.79848, 0.0382, -1.10964, -0.57848, 0.0382, -1.05059,
  -0.79848, 0.08352, -1.05059, -0.57848, 0.08352, -1.05059, -0.79848, 0.08352,
  -1.00527, -0.57848, 0.14257, -1.05059, -0.57848, 0.08352, -1.00527, -0.57848,
  0.14257, -1.05059, -0.79848, 0.08352, -1.00527, -0.79848, 0.14257, -1.00527,
  -0.79848, 0.14257, -0.97678, -0.57848, 0.21135, -1.00527, -0.57848, 0.14257,
  -0.97678, -0.57848, 0.21135, -1.00527, -0.79848, 0.14257, -0.97678, -0.79848,
  0.21135, -0.97678, -0.79848, 0.21135, -0.96707, -0.57848, 0.28515, -0.97678,
  -0.57848, 0.21135, -0.96707, -0.57848, 0.28515, -0.97678, -0.79848, 0.21135,
  -0.96707, -0.79848, 0.28515, -0.96707, -0.79848, 0.28515, -0.97678, -0.57848,
  0.35895, -0.96707, -0.57848, 0.28515, -0.97678, -0.57848, 0.35895, -0.96707,
  -0.79848, 0.28515, -0.97678, -0.79848, 0.35895, -0.96707, -0.79848, 0.28515,
  -1.52765, -0.79848, 0.35895, -0.97678, -0.79848, 0.35895, -0.97678, -0.79848,
  0.35895, -1.52765, -0.79848, 0.35895, -1.49916, -0.79848, 0.42772, -1.52765,
  -0.57848, 0.35895, -1.49916, -0.79848, 0.42772, -1.52765, -0.79848, 0.35895,
  -1.49916, -0.79848, 0.42772, -1.52765, -0.57848, 0.35895, -1.49916, -0.57848,
  0.42772, -1.52765, -0.57848, 0.35895, -0.97678, -0.57848, 0.35895, -1.49916,
  -0.57848, 0.42772, -1.53737, -0.57848, 0.28515, -0.97678, -0.57848, 0.35895,
  -1.52765, -0.57848, 0.35895, -1.53737, -0.57848, 0.28515, -0.96707, -0.57848,
  0.28515, -0.97678, -0.57848, 0.35895, -1.53737, -0.57848, 0.28515, -0.97678,
  -0.57848, 0.21135, -0.96707, -0.57848, 0.28515, -1.52765, -0.57848, 0.21135,
  -0.97678, -0.57848, 0.21135, -1.53737, -0.57848, 0.28515, -1.52765, -0.57848,
  0.21135, -1.00527, -0.57848, 0.14257, -0.97678, -0.57848, 0.21135, -1.49916,
  -0.57848, 0.14257, -1.00527, -0.57848, 0.14257, -1.52765, -0.57848, 0.21135,
  -1.45385, -0.57848, 0.08352, -1.00527, -0.57848, 0.14257, -1.49916, -0.57848,
  0.14257, -1.45385, -0.57848, 0.08352, -1.05059, -0.57848, 0.08352, -1.00527,
  -0.57848, 0.14257, -1.39479, -0.57848, 0.0382, -1.05059, -0.57848, 0.08352,
  -1.45385, -0.57848, 0.08352, -1.39479, -0.57848, 0.0382, -1.10964, -0.57848,
  0.0382, -1.05059, -0.57848, 0.08352, -1.32602, -0.57848, 0.00972, -1.10964,
  -0.57848, 0.0382, -1.39479, -0.57848, 0.0382, -1.32602, -0.57848, 0.00972,
  -1.17841, -0.57848, 0.00972, -1.10964, -0.57848, 0.0382, -1.17841, -0.57848,
  0.00972, -1.32602, -0.57848, 0.00972, -1.25222, -0.57848, 0.0, -1.32602,
  -0.57848, 0.00972, -1.25222, -0.79848, 0.0, -1.25222, -0.57848, 0.0, -1.25222,
  -0.79848, 0.0, -1.32602, -0.57848, 0.00972, -1.32602, -0.79848, 0.00972,
  -1.39479, -0.57848, 0.0382, -1.32602, -0.79848, 0.00972, -1.32602, -0.57848,
  0.00972, -1.32602, -0.79848, 0.00972, -1.39479, -0.57848, 0.0382, -1.39479,
  -0.79848, 0.0382, -1.45385, -0.57848, 0.08352, -1.39479, -0.79848, 0.0382,
  -1.39479, -0.57848, 0.0382, -1.39479, -0.79848, 0.0382, -1.45385, -0.57848,
  0.08352, -1.45385, -0.79848, 0.08352, -1.45385, -0.57848, 0.08352, -1.49916,
  -0.79848, 0.14257, -1.45385, -0.79848, 0.08352, -1.49916, -0.79848, 0.14257,
  -1.45385, -0.57848, 0.08352, -1.49916, -0.57848, 0.14257, -1.49916, -0.57848,
  0.14257, -1.52765, -0.79848, 0.21135, -1.49916, -0.79848, 0.14257, -1.52765,
  -0.79848, 0.21135, -1.49916, -0.57848, 0.14257, -1.52765, -0.57848, 0.21135,
  -1.52765, -0.57848, 0.21135, -1.53737, -0.79848, 0.28515, -1.52765, -0.79848,
  0.21135, -1.53737, -0.79848, 0.28515, -1.52765, -0.57848, 0.21135, -1.53737,
  -0.57848, 0.28515, -1.49916, -0.57848, 0.42772, -0.97678, -0.57848, 0.35895,
  -1.00527, -0.57848, 0.42772, -0.97678, -0.79848, 0.35895, -1.00527, -0.57848,
  0.42772, -0.97678, -0.57848, 0.35895, -1.00527, -0.57848, 0.42772, -0.97678,
  -0.79848, 0.35895, -1.00527, -0.79848, 0.42772, -0.97678, -0.79848, 0.35895,
  -1.49916, -0.79848, 0.42772, -1.00527, -0.79848, 0.42772, -1.00527, -0.79848,
  0.42772, -1.49916, -0.79848, 0.42772, -1.45385, -0.79848, 0.48678, -1.49916,
  -0.57848, 0.42772, -1.45385, -0.79848, 0.48678, -1.49916, -0.79848, 0.42772,
  -1.45385, -0.79848, 0.48678, -1.49916, -0.57848, 0.42772, -1.45385, -0.57848,
  0.48678, -1.49916, -0.57848, 0.42772, -1.00527, -0.57848, 0.42772, -1.45385,
  -0.57848, 0.48678, -1.45385, -0.57848, 0.48678, -1.00527, -0.57848, 0.42772,
  -1.05059, -0.57848, 0.48678, -1.00527, -0.79848, 0.42772, -1.05059, -0.57848,
  0.48678, -1.00527, -0.57848, 0.42772, -1.05059, -0.57848, 0.48678, -1.00527,
  -0.79848, 0.42772, -1.05059, -0.79848, 0.48678, -1.00527, -0.79848, 0.42772,
  -1.45385, -0.79848, 0.48678, -1.05059, -0.79848, 0.48678, -1.05059, -0.79848,
  0.48678, -1.45385, -0.79848, 0.48678, -1.39479, -0.79848, 0.5321, -1.45385,
  -0.79848, 0.48678, -1.39479, -0.57848, 0.5321, -1.39479, -0.79848, 0.5321,
  -1.39479, -0.57848, 0.5321, -1.45385, -0.79848, 0.48678, -1.45385, -0.57848,
  0.48678, -1.45385, -0.57848, 0.48678, -1.05059, -0.57848, 0.48678, -1.39479,
  -0.57848, 0.5321, -1.39479, -0.57848, 0.5321, -1.05059, -0.57848, 0.48678,
  -1.10964, -0.57848, 0.5321, -1.05059, -0.57848, 0.48678, -1.10964, -0.79848,
  0.5321, -1.10964, -0.57848, 0.5321, -1.10964, -0.79848, 0.5321, -1.05059,
  -0.57848, 0.48678, -1.05059, -0.79848, 0.48678, -1.05059, -0.79848, 0.48678,
  -1.39479, -0.79848, 0.5321, -1.10964, -0.79848, 0.5321, -1.10964, -0.79848,
  0.5321, -1.39479, -0.79848, 0.5321, -1.32602, -0.79848, 0.56058, -1.39479,
  -0.79848, 0.5321, -1.32602, -0.57848, 0.56058, -1.32602, -0.79848, 0.56058,
  -1.32602, -0.57848, 0.56058, -1.39479, -0.79848, 0.5321, -1.39479, -0.57848,
  0.5321, -1.39479, -0.57848, 0.5321, -1.10964, -0.57848, 0.5321, -1.32602,
  -0.57848, 0.56058, -1.32602, -0.57848, 0.56058, -1.10964, -0.57848, 0.5321,
  -1.17841, -0.57848, 0.56058, -1.10964, -0.57848, 0.5321, -1.17841, -0.79848,
  0.56058, -1.17841, -0.57848, 0.56058, -1.17841, -0.79848, 0.56058, -1.10964,
  -0.57848, 0.5321, -1.10964, -0.79848, 0.5321, -1.10964, -0.79848, 0.5321,
  -1.32602, -0.79848, 0.56058, -1.17841, -0.79848, 0.56058, -1.17841, -0.79848,
  0.56058, -1.32602, -0.79848, 0.56058, -1.25222, -0.79848, 0.5703, -1.32602,
  -0.79848, 0.56058, -1.25222, -0.57848, 0.5703, -1.25222, -0.79848, 0.5703,
  -1.25222, -0.57848, 0.5703, -1.32602, -0.79848, 0.56058, -1.32602, -0.57848,
  0.56058, -1.32602, -0.57848, 0.56058, -1.17841, -0.57848, 0.56058, -1.25222,
  -0.57848, 0.5703, -1.17841, -0.57848, 0.56058, -1.25222, -0.79848, 0.5703,
  -1.25222, -0.57848, 0.5703, -1.25222, -0.79848, 0.5703, -1.17841, -0.57848,
  0.56058, -1.17841, -0.79848, 0.56058, 1.16528, 0.57848, 0.0382, 1.23406,
  0.79848, 0.00972, 1.16528, 0.79848, 0.0382, 1.23406, 0.79848, 0.00972,
  1.16528, 0.57848, 0.0382, 1.23406, 0.57848, 0.00972, 1.45043, 0.57848, 0.0382,
  1.23406, 0.57848, 0.00972, 1.16528, 0.57848, 0.0382, 1.38166, 0.57848,
  0.00972, 1.23406, 0.57848, 0.00972, 1.45043, 0.57848, 0.0382, 1.23406,
  0.57848, 0.00972, 1.38166, 0.57848, 0.00972, 1.30786, 0.57848, 0.0, 1.38166,
  0.79848, 0.00972, 1.30786, 0.57848, 0.0, 1.38166, 0.57848, 0.00972, 1.30786,
  0.57848, 0.0, 1.38166, 0.79848, 0.00972, 1.30786, 0.79848, 0.0, 1.38166,
  0.79848, 0.00972, 1.23406, 0.79848, 0.00972, 1.30786, 0.79848, 0.0, 1.23406,
  0.79848, 0.00972, 1.38166, 0.79848, 0.00972, 1.45043, 0.79848, 0.0382,
  1.38166, 0.57848, 0.00972, 1.45043, 0.79848, 0.0382, 1.38166, 0.79848,
  0.00972, 1.45043, 0.79848, 0.0382, 1.38166, 0.57848, 0.00972, 1.45043,
  0.57848, 0.0382, 1.45043, 0.57848, 0.0382, 1.50949, 0.79848, 0.08352, 1.45043,
  0.79848, 0.0382, 1.50949, 0.79848, 0.08352, 1.45043, 0.57848, 0.0382, 1.50949,
  0.57848, 0.08352, 1.45043, 0.57848, 0.0382, 1.10623, 0.57848, 0.08352,
  1.50949, 0.57848, 0.08352, 1.45043, 0.57848, 0.0382, 1.16528, 0.57848, 0.0382,
  1.10623, 0.57848, 0.08352, 1.16528, 0.79848, 0.0382, 1.10623, 0.57848,
  0.08352, 1.16528, 0.57848, 0.0382, 1.10623, 0.57848, 0.08352, 1.16528,
  0.79848, 0.0382, 1.10623, 0.79848, 0.08352, 1.16528, 0.79848, 0.0382, 1.50949,
  0.79848, 0.08352, 1.10623, 0.79848, 0.08352, 1.16528, 0.79848, 0.0382,
  1.45043, 0.79848, 0.0382, 1.50949, 0.79848, 0.08352, 1.23406, 0.79848,
  0.00972, 1.45043, 0.79848, 0.0382, 1.16528, 0.79848, 0.0382, 1.10623, 0.79848,
  0.08352, 1.50949, 0.79848, 0.08352, 1.55481, 0.79848, 0.14257, 1.50949,
  0.57848, 0.08352, 1.55481, 0.79848, 0.14257, 1.50949, 0.79848, 0.08352,
  1.55481, 0.79848, 0.14257, 1.50949, 0.57848, 0.08352, 1.55481, 0.57848,
  0.14257, 1.50949, 0.57848, 0.08352, 1.06091, 0.57848, 0.14257, 1.55481,
  0.57848, 0.14257, 1.50949, 0.57848, 0.08352, 1.10623, 0.57848, 0.08352,
  1.06091, 0.57848, 0.14257, 1.10623, 0.57848, 0.08352, 1.06091, 0.79848,
  0.14257, 1.06091, 0.57848, 0.14257, 1.06091, 0.79848, 0.14257, 1.10623,
  0.57848, 0.08352, 1.10623, 0.79848, 0.08352, 1.10623, 0.79848, 0.08352,
  1.55481, 0.79848, 0.14257, 1.06091, 0.79848, 0.14257, 1.06091, 0.79848,
  0.14257, 1.55481, 0.79848, 0.14257, 1.58329, 0.79848, 0.21135, 1.55481,
  0.79848, 0.14257, 1.58329, 0.57848, 0.21135, 1.58329, 0.79848, 0.21135,
  1.58329, 0.57848, 0.21135, 1.55481, 0.79848, 0.14257, 1.55481, 0.57848,
  0.14257, 1.55481, 0.57848, 0.14257, 1.06091, 0.57848, 0.14257, 1.58329,
  0.57848, 0.21135, 1.58329, 0.57848, 0.21135, 1.06091, 0.57848, 0.14257,
  1.03242, 0.57848, 0.21135, 1.06091, 0.79848, 0.14257, 1.03242, 0.57848,
  0.21135, 1.06091, 0.57848, 0.14257, 1.03242, 0.57848, 0.21135, 1.06091,
  0.79848, 0.14257, 1.03242, 0.79848, 0.21135, 1.06091, 0.79848, 0.14257,
  1.58329, 0.79848, 0.21135, 1.03242, 0.79848, 0.21135, 1.03242, 0.79848,
  0.21135, 1.58329, 0.79848, 0.21135, 1.59301, 0.79848, 0.28515, 1.58329,
  0.57848, 0.21135, 1.59301, 0.79848, 0.28515, 1.58329, 0.79848, 0.21135,
  1.59301, 0.79848, 0.28515, 1.58329, 0.57848, 0.21135, 1.59301, 0.57848,
  0.28515, 1.58329, 0.57848, 0.21135, 1.03242, 0.57848, 0.21135, 1.59301,
  0.57848, 0.28515, 1.59301, 0.57848, 0.28515, 1.03242, 0.57848, 0.21135,
  1.02271, 0.57848, 0.28515, 1.03242, 0.79848, 0.21135, 1.02271, 0.57848,
  0.28515, 1.03242, 0.57848, 0.21135, 1.02271, 0.57848, 0.28515, 1.03242,
  0.79848, 0.21135, 1.02271, 0.79848, 0.28515, 1.03242, 0.79848, 0.21135,
  1.59301, 0.79848, 0.28515, 1.02271, 0.79848, 0.28515, 1.02271, 0.79848,
  0.28515, 1.59301, 0.79848, 0.28515, 1.58329, 0.79848, 0.35895, 1.59301,
  0.57848, 0.28515, 1.58329, 0.79848, 0.35895, 1.59301, 0.79848, 0.28515,
  1.58329, 0.79848, 0.35895, 1.59301, 0.57848, 0.28515, 1.58329, 0.57848,
  0.35895, 1.59301, 0.57848, 0.28515, 1.03242, 0.57848, 0.35895, 1.58329,
  0.57848, 0.35895, 1.59301, 0.57848, 0.28515, 1.02271, 0.57848, 0.28515,
  1.03242, 0.57848, 0.35895, 1.02271, 0.79848, 0.28515, 1.03242, 0.57848,
  0.35895, 1.02271, 0.57848, 0.28515, 1.03242, 0.57848, 0.35895, 1.02271,
  0.79848, 0.28515, 1.03242, 0.79848, 0.35895, 1.02271, 0.79848, 0.28515,
  1.58329, 0.79848, 0.35895, 1.03242, 0.79848, 0.35895, 1.03242, 0.79848,
  0.35895, 1.58329, 0.79848, 0.35895, 1.55481, 0.79848, 0.42772, 1.58329,
  0.57848, 0.35895, 1.55481, 0.79848, 0.42772, 1.58329, 0.79848, 0.35895,
  1.55481, 0.79848, 0.42772, 1.58329, 0.57848, 0.35895, 1.55481, 0.57848,
  0.42772, 1.58329, 0.57848, 0.35895, 1.03242, 0.57848, 0.35895, 1.55481,
  0.57848, 0.42772, 1.55481, 0.57848, 0.42772, 1.03242, 0.57848, 0.35895,
  1.06091, 0.57848, 0.42772, 1.03242, 0.79848, 0.35895, 1.06091, 0.57848,
  0.42772, 1.03242, 0.57848, 0.35895, 1.06091, 0.57848, 0.42772, 1.03242,
  0.79848, 0.35895, 1.06091, 0.79848, 0.42772, 1.03242, 0.79848, 0.35895,
  1.55481, 0.79848, 0.42772, 1.06091, 0.79848, 0.42772, 1.06091, 0.79848,
  0.42772, 1.55481, 0.79848, 0.42772, 1.50949, 0.79848, 0.48678, 1.55481,
  0.57848, 0.42772, 1.50949, 0.79848, 0.48678, 1.55481, 0.79848, 0.42772,
  1.50949, 0.79848, 0.48678, 1.55481, 0.57848, 0.42772, 1.50949, 0.57848,
  0.48678, 1.55481, 0.57848, 0.42772, 1.06091, 0.57848, 0.42772, 1.50949,
  0.57848, 0.48678, 1.50949, 0.57848, 0.48678, 1.06091, 0.57848, 0.42772,
  1.10623, 0.57848, 0.48678, 1.06091, 0.79848, 0.42772, 1.10623, 0.57848,
  0.48678, 1.06091, 0.57848, 0.42772, 1.10623, 0.57848, 0.48678, 1.06091,
  0.79848, 0.42772, 1.10623, 0.79848, 0.48678, 1.06091, 0.79848, 0.42772,
  1.50949, 0.79848, 0.48678, 1.10623, 0.79848, 0.48678, 1.10623, 0.79848,
  0.48678, 1.50949, 0.79848, 0.48678, 1.45043, 0.79848, 0.5321, 1.50949,
  0.79848, 0.48678, 1.45043, 0.57848, 0.5321, 1.45043, 0.79848, 0.5321, 1.45043,
  0.57848, 0.5321, 1.50949, 0.79848, 0.48678, 1.50949, 0.57848, 0.48678,
  1.50949, 0.57848, 0.48678, 1.10623, 0.57848, 0.48678, 1.45043, 0.57848,
  0.5321, 1.45043, 0.57848, 0.5321, 1.10623, 0.57848, 0.48678, 1.16528, 0.57848,
  0.5321, 1.10623, 0.57848, 0.48678, 1.16528, 0.79848, 0.5321, 1.16528, 0.57848,
  0.5321, 1.16528, 0.79848, 0.5321, 1.10623, 0.57848, 0.48678, 1.10623, 0.79848,
  0.48678, 1.10623, 0.79848, 0.48678, 1.45043, 0.79848, 0.5321, 1.16528,
  0.79848, 0.5321, 1.16528, 0.79848, 0.5321, 1.45043, 0.79848, 0.5321, 1.38166,
  0.79848, 0.56058, 1.45043, 0.79848, 0.5321, 1.38166, 0.57848, 0.56058,
  1.38166, 0.79848, 0.56058, 1.38166, 0.57848, 0.56058, 1.45043, 0.79848,
  0.5321, 1.45043, 0.57848, 0.5321, 1.45043, 0.57848, 0.5321, 1.16528, 0.57848,
  0.5321, 1.38166, 0.57848, 0.56058, 1.38166, 0.57848, 0.56058, 1.16528,
  0.57848, 0.5321, 1.23406, 0.57848, 0.56058, 1.16528, 0.57848, 0.5321, 1.23406,
  0.79848, 0.56058, 1.23406, 0.57848, 0.56058, 1.23406, 0.79848, 0.56058,
  1.16528, 0.57848, 0.5321, 1.16528, 0.79848, 0.5321, 1.16528, 0.79848, 0.5321,
  1.38166, 0.79848, 0.56058, 1.23406, 0.79848, 0.56058, 1.23406, 0.79848,
  0.56058, 1.38166, 0.79848, 0.56058, 1.30786, 0.79848, 0.5703, 1.38166,
  0.79848, 0.56058, 1.30786, 0.57848, 0.5703, 1.30786, 0.79848, 0.5703, 1.30786,
  0.57848, 0.5703, 1.38166, 0.79848, 0.56058, 1.38166, 0.57848, 0.56058,
  1.38166, 0.57848, 0.56058, 1.23406, 0.57848, 0.56058, 1.30786, 0.57848,
  0.5703, 1.23406, 0.57848, 0.56058, 1.30786, 0.79848, 0.5703, 1.30786, 0.57848,
  0.5703, 1.30786, 0.79848, 0.5703, 1.23406, 0.57848, 0.56058, 1.23406, 0.79848,
  0.56058, 1.23406, 0.57848, 0.00972, 1.30786, 0.79848, 0.0, 1.23406, 0.79848,
  0.00972, 1.30786, 0.79848, 0.0, 1.23406, 0.57848, 0.00972, 1.30786, 0.57848,
  0.0, -1.32602, 0.57848, 0.00972, -1.17841, 0.57848, 0.00972, -1.25222,
  0.57848, 0.0, -1.17841, 0.57848, 0.00972, -1.32602, 0.57848, 0.00972,
  -1.10964, 0.57848, 0.0382, -1.10964, 0.57848, 0.0382, -1.32602, 0.57848,
  0.00972, -1.39479, 0.57848, 0.0382, -1.32602, 0.79848, 0.00972, -1.39479,
  0.57848, 0.0382, -1.32602, 0.57848, 0.00972, -1.39479, 0.57848, 0.0382,
  -1.32602, 0.79848, 0.00972, -1.39479, 0.79848, 0.0382, -1.32602, 0.79848,
  0.00972, -1.10964, 0.79848, 0.0382, -1.39479, 0.79848, 0.0382, -1.32602,
  0.79848, 0.00972, -1.17841, 0.79848, 0.00972, -1.10964, 0.79848, 0.0382,
  -1.17841, 0.79848, 0.00972, -1.32602, 0.79848, 0.00972, -1.25222, 0.79848,
  0.0, -1.32602, 0.57848, 0.00972, -1.25222, 0.79848, 0.0, -1.32602, 0.79848,
  0.00972, -1.25222, 0.79848, 0.0, -1.32602, 0.57848, 0.00972, -1.25222,
  0.57848, 0.0, -1.25222, 0.57848, 0.0, -1.17841, 0.79848, 0.00972, -1.25222,
  0.79848, 0.0, -1.17841, 0.79848, 0.00972, -1.25222, 0.57848, 0.0, -1.17841,
  0.57848, 0.00972, -1.17841, 0.57848, 0.00972, -1.10964, 0.79848, 0.0382,
  -1.17841, 0.79848, 0.00972, -1.10964, 0.79848, 0.0382, -1.17841, 0.57848,
  0.00972, -1.10964, 0.57848, 0.0382, -1.10964, 0.57848, 0.0382, -1.05059,
  0.79848, 0.08352, -1.10964, 0.79848, 0.0382, -1.05059, 0.79848, 0.08352,
  -1.10964, 0.57848, 0.0382, -1.05059, 0.57848, 0.08352, -1.10964, 0.57848,
  0.0382, -1.45385, 0.57848, 0.08352, -1.05059, 0.57848, 0.08352, -1.10964,
  0.57848, 0.0382, -1.39479, 0.57848, 0.0382, -1.45385, 0.57848, 0.08352,
  -1.39479, 0.79848, 0.0382, -1.45385, 0.57848, 0.08352, -1.39479, 0.57848,
  0.0382, -1.45385, 0.57848, 0.08352, -1.39479, 0.79848, 0.0382, -1.45385,
  0.79848, 0.08352, -1.39479, 0.79848, 0.0382, -1.05059, 0.79848, 0.08352,
  -1.45385, 0.79848, 0.08352, -1.39479, 0.79848, 0.0382, -1.10964, 0.79848,
  0.0382, -1.05059, 0.79848, 0.08352, -1.45385, 0.79848, 0.08352, -1.05059,
  0.79848, 0.08352, -1.00527, 0.79848, 0.14257, -1.05059, 0.57848, 0.08352,
  -1.00527, 0.79848, 0.14257, -1.05059, 0.79848, 0.08352, -1.00527, 0.79848,
  0.14257, -1.05059, 0.57848, 0.08352, -1.00527, 0.57848, 0.14257, -1.05059,
  0.57848, 0.08352, -1.49916, 0.57848, 0.14257, -1.00527, 0.57848, 0.14257,
  -1.05059, 0.57848, 0.08352, -1.45385, 0.57848, 0.08352, -1.49916, 0.57848,
  0.14257, -1.45385, 0.57848, 0.08352, -1.49916, 0.79848, 0.14257, -1.49916,
  0.57848, 0.14257, -1.49916, 0.79848, 0.14257, -1.45385, 0.57848, 0.08352,
  -1.45385, 0.79848, 0.08352, -1.45385, 0.79848, 0.08352, -1.00527, 0.79848,
  0.14257, -1.49916, 0.79848, 0.14257, -1.49916, 0.79848, 0.14257, -1.00527,
  0.79848, 0.14257, -0.97678, 0.79848, 0.21135, -1.00527, 0.79848, 0.14257,
  -0.97678, 0.57848, 0.21135, -0.97678, 0.79848, 0.21135, -0.97678, 0.57848,
  0.21135, -1.00527, 0.79848, 0.14257, -1.00527, 0.57848, 0.14257, -1.00527,
  0.57848, 0.14257, -1.49916, 0.57848, 0.14257, -0.97678, 0.57848, 0.21135,
  -0.97678, 0.57848, 0.21135, -1.49916, 0.57848, 0.14257, -1.52765, 0.57848,
  0.21135, -1.49916, 0.79848, 0.14257, -1.52765, 0.57848, 0.21135, -1.49916,
  0.57848, 0.14257, -1.52765, 0.57848, 0.21135, -1.49916, 0.79848, 0.14257,
  -1.52765, 0.79848, 0.21135, -1.49916, 0.79848, 0.14257, -0.97678, 0.79848,
  0.21135, -1.52765, 0.79848, 0.21135, -1.52765, 0.79848, 0.21135, -0.97678,
  0.79848, 0.21135, -0.96707, 0.79848, 0.28515, -0.97678, 0.57848, 0.21135,
  -0.96707, 0.79848, 0.28515, -0.97678, 0.79848, 0.21135, -0.96707, 0.79848,
  0.28515, -0.97678, 0.57848, 0.21135, -0.96707, 0.57848, 0.28515, -0.97678,
  0.57848, 0.21135, -1.52765, 0.57848, 0.21135, -0.96707, 0.57848, 0.28515,
  -0.96707, 0.57848, 0.28515, -1.52765, 0.57848, 0.21135, -1.53737, 0.57848,
  0.28515, -1.52765, 0.79848, 0.21135, -1.53737, 0.57848, 0.28515, -1.52765,
  0.57848, 0.21135, -1.53737, 0.57848, 0.28515, -1.52765, 0.79848, 0.21135,
  -1.53737, 0.79848, 0.28515, -1.52765, 0.79848, 0.21135, -0.96707, 0.79848,
  0.28515, -1.53737, 0.79848, 0.28515, -1.53737, 0.79848, 0.28515, -0.96707,
  0.79848, 0.28515, -0.97678, 0.79848, 0.35895, -0.96707, 0.57848, 0.28515,
  -0.97678, 0.79848, 0.35895, -0.96707, 0.79848, 0.28515, -0.97678, 0.79848,
  0.35895, -0.96707, 0.57848, 0.28515, -0.97678, 0.57848, 0.35895, -0.96707,
  0.57848, 0.28515, -1.52765, 0.57848, 0.35895, -0.97678, 0.57848, 0.35895,
  -0.96707, 0.57848, 0.28515, -1.53737, 0.57848, 0.28515, -1.52765, 0.57848,
  0.35895, -1.53737, 0.79848, 0.28515, -1.52765, 0.57848, 0.35895, -1.53737,
  0.57848, 0.28515, -1.52765, 0.57848, 0.35895, -1.53737, 0.79848, 0.28515,
  -1.52765, 0.79848, 0.35895, -1.53737, 0.79848, 0.28515, -0.97678, 0.79848,
  0.35895, -1.52765, 0.79848, 0.35895, -1.52765, 0.79848, 0.35895, -0.97678,
  0.79848, 0.35895, -1.00527, 0.79848, 0.42772, -0.97678, 0.57848, 0.35895,
  -1.00527, 0.79848, 0.42772, -0.97678, 0.79848, 0.35895, -1.00527, 0.79848,
  0.42772, -0.97678, 0.57848, 0.35895, -1.00527, 0.57848, 0.42772, -0.97678,
  0.57848, 0.35895, -1.52765, 0.57848, 0.35895, -1.00527, 0.57848, 0.42772,
  -1.00527, 0.57848, 0.42772, -1.52765, 0.57848, 0.35895, -1.49916, 0.57848,
  0.42772, -1.52765, 0.79848, 0.35895, -1.49916, 0.57848, 0.42772, -1.52765,
  0.57848, 0.35895, -1.49916, 0.57848, 0.42772, -1.52765, 0.79848, 0.35895,
  -1.49916, 0.79848, 0.42772, -1.52765, 0.79848, 0.35895, -1.00527, 0.79848,
  0.42772, -1.49916, 0.79848, 0.42772, -1.49916, 0.79848, 0.42772, -1.00527,
  0.79848, 0.42772, -1.05059, 0.79848, 0.48678, -1.00527, 0.57848, 0.42772,
  -1.05059, 0.79848, 0.48678, -1.00527, 0.79848, 0.42772, -1.05059, 0.79848,
  0.48678, -1.00527, 0.57848, 0.42772, -1.05059, 0.57848, 0.48678, -1.00527,
  0.57848, 0.42772, -1.49916, 0.57848, 0.42772, -1.05059, 0.57848, 0.48678,
  -1.05059, 0.57848, 0.48678, -1.49916, 0.57848, 0.42772, -1.45385, 0.57848,
  0.48678, -1.49916, 0.79848, 0.42772, -1.45385, 0.57848, 0.48678, -1.49916,
  0.57848, 0.42772, -1.45385, 0.57848, 0.48678, -1.49916, 0.79848, 0.42772,
  -1.45385, 0.79848, 0.48678, -1.49916, 0.79848, 0.42772, -1.05059, 0.79848,
  0.48678, -1.45385, 0.79848, 0.48678, -1.45385, 0.79848, 0.48678, -1.05059,
  0.79848, 0.48678, -1.10964, 0.79848, 0.5321, -1.05059, 0.79848, 0.48678,
  -1.10964, 0.57848, 0.5321, -1.10964, 0.79848, 0.5321, -1.10964, 0.57848,
  0.5321, -1.05059, 0.79848, 0.48678, -1.05059, 0.57848, 0.48678, -1.05059,
  0.57848, 0.48678, -1.45385, 0.57848, 0.48678, -1.10964, 0.57848, 0.5321,
  -1.10964, 0.57848, 0.5321, -1.45385, 0.57848, 0.48678, -1.39479, 0.57848,
  0.5321, -1.45385, 0.57848, 0.48678, -1.39479, 0.79848, 0.5321, -1.39479,
  0.57848, 0.5321, -1.39479, 0.79848, 0.5321, -1.45385, 0.57848, 0.48678,
  -1.45385, 0.79848, 0.48678, -1.45385, 0.79848, 0.48678, -1.10964, 0.79848,
  0.5321, -1.39479, 0.79848, 0.5321, -1.39479, 0.79848, 0.5321, -1.10964,
  0.79848, 0.5321, -1.17841, 0.79848, 0.56058, -1.10964, 0.79848, 0.5321,
  -1.17841, 0.57848, 0.56058, -1.17841, 0.79848, 0.56058, -1.17841, 0.57848,
  0.56058, -1.10964, 0.79848, 0.5321, -1.10964, 0.57848, 0.5321, -1.10964,
  0.57848, 0.5321, -1.39479, 0.57848, 0.5321, -1.17841, 0.57848, 0.56058,
  -1.17841, 0.57848, 0.56058, -1.39479, 0.57848, 0.5321, -1.32602, 0.57848,
  0.56058, -1.39479, 0.57848, 0.5321, -1.32602, 0.79848, 0.56058, -1.32602,
  0.57848, 0.56058, -1.32602, 0.79848, 0.56058, -1.39479, 0.57848, 0.5321,
  -1.39479, 0.79848, 0.5321, -1.39479, 0.79848, 0.5321, -1.17841, 0.79848,
  0.56058, -1.32602, 0.79848, 0.56058, -1.32602, 0.79848, 0.56058, -1.17841,
  0.79848, 0.56058, -1.25222, 0.79848, 0.5703, -1.17841, 0.79848, 0.56058,
  -1.25222, 0.57848, 0.5703, -1.25222, 0.79848, 0.5703, -1.25222, 0.57848,
  0.5703, -1.17841, 0.79848, 0.56058, -1.17841, 0.57848, 0.56058, -1.17841,
  0.57848, 0.56058, -1.32602, 0.57848, 0.56058, -1.25222, 0.57848, 0.5703,
  -1.32602, 0.57848, 0.56058, -1.25222, 0.79848, 0.5703, -1.25222, 0.57848,
  0.5703, -1.25222, 0.79848, 0.5703, -1.32602, 0.57848, 0.56058, -1.32602,
  0.79848, 0.56058,
].map((item) => item * 20);
// 汽车顶点
const car_position = [
  1.26125, 0.61739, 0.89992, -0.23087, 0.54045, 0.95101, -0.33371, 0.61739,
  0.89992, -0.23087, 0.54045, 0.95101, 1.26125, 0.61739, 0.89992, 1.17165,
  0.54045, 0.95101, 1.26125, -0.61739, 0.89992, 1.17165, 0.54045, 0.95101,
  1.26125, 0.61739, 0.89992, 1.17165, -0.54045, 0.95101, 1.17165, 0.54045,
  0.95101, 1.26125, -0.61739, 0.89992, 1.12081, -0.216, 0.98, 1.17165, 0.54045,
  0.95101, 1.17165, -0.54045, 0.95101, 1.17165, 0.54045, 0.95101, 1.12081,
  -0.216, 0.98, 1.12081, 0.216, 0.98, -0.17251, -0.216, 0.98, 1.12081, 0.216,
  0.98, 1.12081, -0.216, 0.98, 1.12081, 0.216, 0.98, -0.17251, -0.216, 0.98,
  -0.17251, 0.216, 0.98, -0.23087, -0.54045, 0.95101, -0.17251, 0.216, 0.98,
  -0.17251, -0.216, 0.98, -0.23087, -0.54045, 0.95101, -0.23087, 0.54045,
  0.95101, -0.17251, 0.216, 0.98, -0.23087, -0.54045, 0.95101, -0.33371,
  0.61739, 0.89992, -0.23087, 0.54045, 0.95101, -0.33371, -0.61739, 0.89992,
  -0.33371, 0.61739, 0.89992, -0.23087, -0.54045, 0.95101, -1.00851, -0.83245,
  0.56471, -0.33371, 0.61739, 0.89992, -0.33371, -0.61739, 0.89992, -1.00851,
  -0.83245, 0.56471, -1.00851, 0.83245, 0.56471, -0.33371, 0.61739, 0.89992,
  -1.01808, -0.83447, 0.55995, -1.00851, 0.83245, 0.56471, -1.00851, -0.83245,
  0.56471, -1.00851, 0.83245, 0.56471, -1.01808, -0.83447, 0.55995, -1.01808,
  0.83447, 0.55995, -1.35528, -0.86238, 0.49438, -1.01808, 0.83447, 0.55995,
  -1.01808, -0.83447, 0.55995, -1.35528, -0.86238, 0.49438, -1.35528, 0.86238,
  0.49438, -1.01808, 0.83447, 0.55995, -1.52473, -0.8605, 0.46142, -1.35528,
  0.86238, 0.49438, -1.35528, -0.86238, 0.49438, -1.52473, -0.8605, 0.46142,
  -1.52473, 0.8605, 0.46142, -1.35528, 0.86238, 0.49438, -1.79168, -0.79554,
  0.40951, -1.52473, 0.8605, 0.46142, -1.52473, -0.8605, 0.46142, -1.52473,
  0.8605, 0.46142, -1.79168, -0.79554, 0.40951, -1.79168, 0.79554, 0.40951,
  -1.79168, -0.79554, 0.40951, -1.84537, 0.78247, 0.38946, -1.79168, 0.79554,
  0.40951, -1.84537, -0.78247, 0.38946, -1.84537, 0.78247, 0.38946, -1.79168,
  -0.79554, 0.40951, -2.00541, -0.70008, 0.32972, -1.84537, 0.78247, 0.38946,
  -1.84537, -0.78247, 0.38946, -2.00541, -0.70008, 0.32972, -2.00541, 0.70008,
  0.32972, -1.84537, 0.78247, 0.38946, -2.00541, -0.70008, 0.32972, -2.09509,
  0.59258, 0.29623, -2.00541, 0.70008, 0.32972, -2.09509, -0.59258, 0.29623,
  -2.09509, 0.59258, 0.29623, -2.00541, -0.70008, 0.32972, -2.09523, -0.59203,
  0.29618, -2.09509, 0.59258, 0.29623, -2.09509, -0.59258, 0.29623, -2.09509,
  0.59258, 0.29623, -2.09523, -0.59203, 0.29618, -2.09523, 0.59203, 0.29618,
  -2.09523, -0.59203, 0.29618, -2.11079, 0.52897, 0.18019, -2.09523, 0.59203,
  0.29618, -2.11079, 0.52897, 0.18019, -2.09523, -0.59203, 0.29618, -2.11079,
  -0.52897, 0.18019, -2.1053, -0.55124, -0.20097, -2.11079, -0.52897, 0.18019,
  -2.09523, -0.59203, 0.29618, -2.21012, -0.12657, -0.04576, -2.11079, -0.52897,
  0.18019, -2.1053, -0.55124, -0.20097, -2.11079, -0.52897, 0.18019, -2.21012,
  -0.12657, -0.04576, -2.21012, -0.12657, 0.07983, -2.21012, 0.12657, -0.04576,
  -2.21012, -0.12657, 0.07983, -2.21012, -0.12657, -0.04576, -2.21012, -0.12657,
  0.07983, -2.21012, 0.12657, -0.04576, -2.21012, 0.12657, 0.07983, -2.21012,
  0.12657, -0.04576, -2.11079, 0.52897, 0.18019, -2.21012, 0.12657, 0.07983,
  -2.1053, 0.55124, -0.20097, -2.11079, 0.52897, 0.18019, -2.21012, 0.12657,
  -0.04576, -2.1053, 0.55124, -0.20097, -2.09523, 0.59203, 0.29618, -2.11079,
  0.52897, 0.18019, -2.1053, 0.55124, -0.20097, -2.09509, 0.59258, 0.29623,
  -2.09523, 0.59203, 0.29618, -2.09509, 0.59258, 0.29623, -2.1053, 0.55124,
  -0.20097, -2.09509, 0.59258, -0.20097, -2.1053, 0.55124, -0.20097, -2.09509,
  -0.59258, -0.20097, -2.09509, 0.59258, -0.20097, -2.09509, -0.59258, -0.20097,
  -2.1053, 0.55124, -0.20097, -2.1053, -0.55124, -0.20097, -2.21012, 0.12657,
  -0.04576, -2.1053, -0.55124, -0.20097, -2.1053, 0.55124, -0.20097, -2.1053,
  -0.55124, -0.20097, -2.21012, 0.12657, -0.04576, -2.21012, -0.12657, -0.04576,
  -2.09509, -0.59258, -0.20097, -2.09509, -0.59258, -0.20102, -2.1053, -0.55124,
  -0.20097, -2.09509, -0.59258, -0.20097, -2.00541, -0.70008, -0.20145,
  -2.09509, -0.59258, -0.20102, -2.00541, -0.70008, -0.20145, -2.09509,
  -0.59258, -0.20097, -2.00541, -0.70008, -0.20097, -2.09509, 0.59258, -0.20097,
  -2.09509, -0.59258, -0.20097, -2.00541, -0.70008, -0.20097, -2.09509,
  -0.59258, -0.20097, -2.00541, -0.70008, 0.32972, -2.00541, -0.70008, -0.20097,
  -2.00541, -0.70008, 0.32972, -2.09509, -0.59258, -0.20097, -2.09509, -0.59258,
  0.29623, -2.09509, -0.59258, -0.20097, -2.09523, -0.59203, 0.29618, -2.09509,
  -0.59258, 0.29623, -2.1053, -0.55124, -0.20097, -2.09523, -0.59203, 0.29618,
  -2.09509, -0.59258, -0.20097, -2.00541, -0.70008, 0.32972, -1.87434, -0.76756,
  -0.20097, -2.00541, -0.70008, -0.20097, -1.87434, -0.76756, -0.20097,
  -2.00541, -0.70008, 0.32972, -1.84537, -0.78247, -0.19377, -1.84537, -0.78247,
  -0.19377, -2.00541, -0.70008, 0.32972, -1.84537, -0.78247, 0.38946, -1.84537,
  -0.78247, 0.38946, -1.63844, -0.83283, -0.16943, -1.84537, -0.78247, -0.19377,
  -1.63844, -0.83283, -0.16943, -1.84537, -0.78247, 0.38946, -1.63125, -0.83458,
  -0.16459, -1.63125, -0.83458, -0.16459, -1.84537, -0.78247, 0.38946, -1.63125,
  -0.83458, -0.13193, -1.63125, -0.83458, -0.13193, -1.84537, -0.78247, 0.38946,
  -1.62534, -0.83602, -0.03291, -1.62534, -0.83602, -0.03291, -1.84537,
  -0.78247, 0.38946, -1.59401, -0.84364, 0.06121, -1.59401, -0.84364, 0.06121,
  -1.84537, -0.78247, 0.38946, -1.53938, -0.85694, 0.14402, -1.53938, -0.85694,
  0.14402, -1.84537, -0.78247, 0.38946, -1.52473, -0.8605, 0.15702, -1.52473,
  -0.8605, 0.15702, -1.84537, -0.78247, 0.38946, -1.52473, -0.8605, 0.46142,
  -1.52473, -0.8605, 0.46142, -1.84537, -0.78247, 0.38946, -1.79168, -0.79554,
  0.40951, -1.52473, -0.8605, 0.46142, -1.46519, -0.86116, 0.20986, -1.52473,
  -0.8605, 0.15702, -1.46519, -0.86116, 0.20986, -1.52473, -0.8605, 0.46142,
  -1.37648, -0.86214, 0.25426, -1.37648, -0.86214, 0.25426, -1.52473, -0.8605,
  0.46142, -1.2793, -0.86322, 0.27418, -1.2793, -0.86322, 0.27418, -1.52473,
  -0.8605, 0.46142, 1.3007, -0.89173, 0.27418, 1.3007, -0.89173, 0.27418,
  -1.52473, -0.8605, 0.46142, 1.52509, -0.89421, 0.41958, 1.52509, -0.89421,
  0.41958, -1.52473, -0.8605, 0.46142, -1.35528, -0.86238, 0.49438, -0.75137,
  -0.86091, 0.49782, 1.52509, -0.89421, 0.41958, -1.35528, -0.86238, 0.49438,
  1.52509, -0.89421, 0.41958, -0.75137, -0.86091, 0.49782, 1.83476, -0.83245,
  0.56471, 1.83476, -0.83245, 0.56471, -0.75137, -0.86091, 0.49782, -0.75226,
  -0.85958, 0.50094, -0.75137, -0.86091, 0.49782, -0.75226, -0.85958, 0.60516,
  -0.75226, -0.85958, 0.50094, -0.75226, -0.85958, 0.60516, -0.75137, -0.86091,
  0.49782, -0.75076, -0.86182, 0.5942, -0.75076, -0.86182, 0.5942, -0.75137,
  -0.86091, 0.49782, -0.75076, -0.86182, 0.57227, -0.73488, -0.88546, 0.49782,
  -0.75076, -0.86182, 0.57227, -0.75137, -0.86091, 0.49782, -0.75076, -0.86182,
  0.57227, -0.73488, -0.88546, 0.49782, -0.73578, -0.88411, 0.50732, -0.73578,
  -0.88411, 0.50732, -0.73488, -0.88546, 0.49782, -0.72295, -0.90321, 0.50732,
  -0.72295, -0.90321, 0.50732, -0.73488, -0.88546, 0.49782, -0.72295, -0.93849,
  0.50732, -0.73488, -0.94103, 0.49782, -0.72295, -0.93849, 0.50732, -0.73488,
  -0.88546, 0.49782, -0.72295, -0.93849, 0.50732, -0.73488, -0.94103, 0.49782,
  -0.67687, -1.02116, 0.54398, -0.73488, -0.94103, 0.49782, -0.67687, -1.04069,
  0.55524, -0.67687, -1.02116, 0.54398, -0.73488, -0.94103, 0.49782, -0.74492,
  -1.04069, 0.55524, -0.67687, -1.04069, 0.55524, -0.73488, -0.94103, 0.49782,
  -0.80355, -1.00465, 0.53448, -0.74492, -1.04069, 0.55524, -0.80355, -1.00465,
  0.53448, -0.73488, -0.94103, 0.49782, -0.86088, -0.94103, 0.49782, -0.75137,
  -0.86091, 0.49782, -0.86088, -0.94103, 0.49782, -0.73488, -0.94103, 0.49782,
  -0.75137, -0.86091, 0.49782, -0.8638, -0.93779, 0.49782, -0.86088, -0.94103,
  0.49782, -0.8638, -0.93779, 0.49782, -0.75137, -0.86091, 0.49782, -0.88098,
  -0.86091, 0.49782, -0.75137, -0.86091, 0.49782, -1.35528, -0.86238, 0.49438,
  -0.88098, -0.86091, 0.49782, -1.01808, -0.83447, 0.55995, -0.88098, -0.86091,
  0.49782, -1.35528, -0.86238, 0.49438, -0.88098, -0.86091, 0.49782, -1.01808,
  -0.83447, 0.55995, -0.88125, -0.85972, 0.50062, -0.88125, -0.85972, 0.50062,
  -1.01808, -0.83447, 0.55995, -0.92036, -0.83245, 0.56471, -0.92036, -0.83245,
  0.56471, -1.01808, -0.83447, 0.55995, -1.00851, -0.83245, 0.56471, -1.00851,
  -0.83245, 0.56471, -0.92226, -0.83112, 0.56677, -0.92036, -0.83245, 0.56471,
  -0.92226, -0.83112, 0.56677, -1.00851, -0.83245, 0.56471, -0.33371, -0.61739,
  0.89992, -0.92226, -0.83112, 0.56677, -0.33371, -0.61739, 0.89992, -0.80942,
  -0.83112, 0.56677, -0.80942, -0.83112, 0.56677, -0.33371, -0.61739, 0.89992,
  1.7087, -0.78106, 0.64481, 1.7087, -0.78106, 0.64481, -0.33371, -0.61739,
  0.89992, 1.26125, -0.61739, 0.89992, -0.23087, -0.54045, 0.95101, 1.26125,
  -0.61739, 0.89992, -0.33371, -0.61739, 0.89992, 1.26125, -0.61739, 0.89992,
  -0.23087, -0.54045, 0.95101, 1.17165, -0.54045, 0.95101, -0.17251, -0.216,
  0.98, 1.17165, -0.54045, 0.95101, -0.23087, -0.54045, 0.95101, 1.17165,
  -0.54045, 0.95101, -0.17251, -0.216, 0.98, 1.12081, -0.216, 0.98, 1.26125,
  -0.61739, 0.89992, 1.7087, 0.78106, 0.64481, 1.7087, -0.78106, 0.64481,
  1.26125, -0.61739, 0.89992, 1.26125, 0.61739, 0.89992, 1.7087, 0.78106,
  0.64481, -0.33371, 0.61739, 0.89992, 1.7087, 0.78106, 0.64481, 1.26125,
  0.61739, 0.89992, -1.00851, 0.83245, 0.56471, 1.7087, 0.78106, 0.64481,
  -0.33371, 0.61739, 0.89992, -1.00851, 0.83245, 0.56471, 2.03, 0.79351, 0.6254,
  1.7087, 0.78106, 0.64481, 2.03, 0.79351, 0.6254, -1.00851, 0.83245, 0.56471,
  1.83476, 0.83245, 0.56471, -1.00851, 0.83245, 0.56471, -0.92226, 0.83377,
  0.56159, 1.83476, 0.83245, 0.56471, -1.01808, 0.83447, 0.55995, -0.92226,
  0.83377, 0.56159, -1.00851, 0.83245, 0.56471, -1.01808, 0.83447, 0.55995,
  -0.88334, 0.86091, 0.49782, -0.92226, 0.83377, 0.56159, -1.35528, 0.86238,
  0.49438, -0.88334, 0.86091, 0.49782, -1.01808, 0.83447, 0.55995, -0.88334,
  0.86091, 0.49782, -1.35528, 0.86238, 0.49438, -0.76048, 0.86091, 0.49782,
  1.52509, 0.89421, 0.41958, -0.76048, 0.86091, 0.49782, -1.35528, 0.86238,
  0.49438, -0.76048, 0.86091, 0.49782, 1.52509, 0.89421, 0.41958, 1.83476,
  0.83245, 0.56471, 1.52509, 0.89421, 0.41958, 2.08537, 0.78247, 0.62205,
  1.83476, 0.83245, 0.56471, 1.52509, 0.89421, 0.21633, 2.08537, 0.78247,
  0.62205, 1.52509, 0.89421, 0.41958, 1.57665, 0.88392, 0.18232, 2.08537,
  0.78247, 0.62205, 1.52509, 0.89421, 0.21633, 1.6425, 0.87079, 0.10812,
  2.08537, 0.78247, 0.62205, 1.57665, 0.88392, 0.18232, 1.68689, 0.86194,
  0.01941, 2.08537, 0.78247, 0.62205, 1.6425, 0.87079, 0.10812, 1.68689,
  0.86194, -0.08895, 2.08537, 0.78247, 0.62205, 1.68689, 0.86194, 0.01941,
  1.68689, 0.86194, -0.08895, 2.08537, 0.78247, -0.09009, 2.08537, 0.78247,
  0.62205, 1.68689, 0.86194, -0.08895, 2.05947, 0.78763, -0.13066, 2.08537,
  0.78247, -0.09009, 1.83285, 0.83283, -0.16943, 2.05947, 0.78763, -0.13066,
  1.68689, 0.86194, -0.08895, 2.05947, 0.78763, -0.13066, 1.83285, 0.83283,
  -0.16943, 1.87917, 0.82359, -0.17389, 1.83285, 0.83283, -0.16943, 1.76624,
  0.76756, -0.20097, 1.87917, 0.82359, -0.17389, 1.68689, 0.83283, -0.16943,
  1.76624, 0.76756, -0.20097, 1.83285, 0.83283, -0.16943, 1.76624, 0.76756,
  -0.20097, 1.68689, 0.83283, -0.16943, 1.68689, 0.76756, -0.20097, 1.68689,
  0.83283, -0.16943, 1.68689, -0.86194, 0.01941, 1.68689, 0.76756, -0.20097,
  1.68689, 0.86194, -0.08895, 1.68689, -0.86194, 0.01941, 1.68689, 0.83283,
  -0.16943, 1.68689, -0.86194, 0.01941, 1.68689, 0.86194, -0.08895, 1.68689,
  0.86194, 0.01941, 1.68689, 0.86194, 0.01941, 1.6425, -0.87079, 0.10812,
  1.68689, -0.86194, 0.01941, 1.6425, -0.87079, 0.10812, 1.68689, 0.86194,
  0.01941, 1.6425, 0.87079, 0.10812, 1.6425, 0.87079, 0.10812, 1.57665,
  -0.88392, 0.18232, 1.6425, -0.87079, 0.10812, 1.57665, -0.88392, 0.18232,
  1.6425, 0.87079, 0.10812, 1.57665, 0.88392, 0.18232, 1.52509, 0.89421,
  0.21633, 1.57665, -0.88392, 0.18232, 1.57665, 0.88392, 0.18232, 1.52509,
  0.89421, 0.21633, 1.52509, -0.89421, 0.21633, 1.57665, -0.88392, 0.18232,
  1.49385, 0.89386, 0.23694, 1.52509, -0.89421, 0.21633, 1.52509, 0.89421,
  0.21633, 1.52509, -0.89421, 0.21633, 1.49385, 0.89386, 0.23694, 1.49385,
  -0.89386, 0.23694, 1.39973, 0.89282, 0.26828, 1.49385, -0.89386, 0.23694,
  1.49385, 0.89386, 0.23694, 1.49385, -0.89386, 0.23694, 1.39973, 0.89282,
  0.26828, 1.39973, -0.89282, 0.26828, 1.3007, 0.89173, 0.27418, 1.39973,
  -0.89282, 0.26828, 1.39973, 0.89282, 0.26828, 1.39973, -0.89282, 0.26828,
  1.3007, 0.89173, 0.27418, 1.3007, -0.89173, 0.27418, 1.20352, 0.89065,
  0.25426, 1.3007, -0.89173, 0.27418, 1.3007, 0.89173, 0.27418, 1.3007,
  -0.89173, 0.27418, 1.20352, 0.89065, 0.25426, 1.20352, -0.89065, 0.25426,
  1.11481, 0.88967, 0.20986, 1.20352, -0.89065, 0.25426, 1.20352, 0.89065,
  0.25426, 1.20352, -0.89065, 0.25426, 1.11481, 0.88967, 0.20986, 1.11481,
  -0.88967, 0.20986, 1.04062, 0.88885, 0.14402, 1.11481, -0.88967, 0.20986,
  1.11481, 0.88967, 0.20986, 1.11481, -0.88967, 0.20986, 1.04062, 0.88885,
  0.14402, 1.04062, -0.88885, 0.14402, 0.98599, 0.88825, 0.06121, 1.04062,
  -0.88885, 0.14402, 1.04062, 0.88885, 0.14402, 1.04062, -0.88885, 0.14402,
  0.98599, 0.88825, 0.06121, 0.98599, -0.88825, 0.06121, 0.95995, 0.88796,
  -0.01701, 0.98599, -0.88825, 0.06121, 0.98599, 0.88825, 0.06121, 0.98599,
  -0.88825, 0.06121, 0.95995, 0.88796, -0.01701, 0.95465, 0.88221, -0.03291,
  -0.87415, 0.86769, -0.07304, 0.95465, 0.88221, -0.03291, 0.95995, 0.88796,
  -0.01701, -0.87318, 0.86598, -0.07777, 0.95465, 0.88221, -0.03291, -0.87415,
  0.86769, -0.07304, -0.87318, 0.86598, -0.07777, 0.94875, 0.84639, -0.13193,
  0.95465, 0.88221, -0.03291, -0.87318, 0.86598, -0.07777, 0.94875, 0.83283,
  -0.16943, 0.94875, 0.84639, -0.13193, 0.94875, 0.83283, -0.16943, -0.87318,
  0.86598, -0.07777, -0.87318, 0.83283, -0.16943, -0.87318, -0.86598, -0.07777,
  -0.87318, 0.83283, -0.16943, -0.87318, 0.86598, -0.07777, -0.87318, 0.83283,
  -0.16943, -0.87318, -0.86598, -0.07777, -0.87318, 0.76756, -0.20097, -0.87318,
  0.76756, -0.20097, -0.87318, -0.86598, -0.07777, -0.87318, -0.76756, -0.20097,
  -0.87318, -0.76756, -0.20097, -0.87318, -0.86598, -0.07777, -0.87318,
  -0.83283, -0.16943, 0.94875, -0.84639, -0.13193, -0.87318, -0.83283, -0.16943,
  -0.87318, -0.86598, -0.07777, -0.87318, -0.83283, -0.16943, 0.94875, -0.84639,
  -0.13193, 0.94875, -0.83283, -0.16943, 0.94875, -0.84639, -0.13193, 0.94875,
  -0.76756, -0.20097, 0.94875, -0.83283, -0.16943, 0.94875, -0.84639, -0.13193,
  0.94875, 0.76756, -0.20097, 0.94875, -0.76756, -0.20097, 0.94875, -0.84639,
  -0.13193, 0.94875, 0.83283, -0.16943, 0.94875, 0.76756, -0.20097, 0.94875,
  0.83283, -0.16943, 0.94875, -0.84639, -0.13193, 0.94875, 0.84639, -0.13193,
  0.95465, -0.88221, -0.03291, 0.94875, 0.84639, -0.13193, 0.94875, -0.84639,
  -0.13193, 0.94875, 0.84639, -0.13193, 0.95465, -0.88221, -0.03291, 0.95465,
  0.88221, -0.03291, 0.98599, -0.88825, 0.06121, 0.95465, 0.88221, -0.03291,
  0.95465, -0.88221, -0.03291, 0.98599, -0.88825, 0.06121, 0.95465, -0.88221,
  -0.03291, 0.95995, -0.88796, -0.01701, 0.95465, -0.88221, -0.03291, -0.87415,
  -0.86769, -0.07304, 0.95995, -0.88796, -0.01701, 0.95465, -0.88221, -0.03291,
  -0.87318, -0.86598, -0.07777, -0.87415, -0.86769, -0.07304, 0.94875, -0.84639,
  -0.13193, -0.87318, -0.86598, -0.07777, 0.95465, -0.88221, -0.03291, -0.87318,
  -0.86598, -0.07777, -0.89311, -0.86748, 0.01941, -0.87415, -0.86769, -0.07304,
  -0.87318, 0.86598, -0.07777, -0.89311, -0.86748, 0.01941, -0.87318, -0.86598,
  -0.07777, -0.87318, 0.86598, -0.07777, -0.89311, 0.86748, 0.01941, -0.89311,
  -0.86748, 0.01941, -0.89311, 0.86748, 0.01941, -0.87318, 0.86598, -0.07777,
  -0.87415, 0.86769, -0.07304, 0.95995, 0.88796, -0.01701, -0.89311, 0.86748,
  0.01941, -0.87415, 0.86769, -0.07304, -0.89311, 0.86748, 0.01941, 0.95995,
  0.88796, -0.01701, 0.98599, 0.88825, 0.06121, -0.89311, 0.86748, 0.01941,
  0.98599, 0.88825, 0.06121, -0.93751, 0.86699, 0.10812, -0.93751, 0.86699,
  0.10812, 0.98599, 0.88825, 0.06121, 1.04062, 0.88885, 0.14402, -0.93751,
  0.86699, 0.10812, 1.04062, 0.88885, 0.14402, -1.00335, 0.86627, 0.18232,
  -1.00335, 0.86627, 0.18232, 1.04062, 0.88885, 0.14402, 1.11481, 0.88967,
  0.20986, -1.00335, 0.86627, 0.18232, 1.11481, 0.88967, 0.20986, -1.08615,
  0.86535, 0.23694, -1.08615, 0.86535, 0.23694, 1.11481, 0.88967, 0.20986,
  1.20352, 0.89065, 0.25426, -1.08615, 0.86535, 0.23694, 1.20352, 0.89065,
  0.25426, -1.18027, 0.86431, 0.26828, -1.18027, 0.86431, 0.26828, 1.20352,
  0.89065, 0.25426, 1.3007, 0.89173, 0.27418, -1.18027, 0.86431, 0.26828,
  1.3007, 0.89173, 0.27418, -1.2793, 0.86322, 0.27418, -1.52473, 0.8605,
  0.46142, -1.2793, 0.86322, 0.27418, 1.3007, 0.89173, 0.27418, -1.52473,
  0.8605, 0.46142, -1.37648, 0.86214, 0.25426, -1.2793, 0.86322, 0.27418,
  -1.52473, 0.8605, 0.46142, -1.46519, 0.86116, 0.20986, -1.37648, 0.86214,
  0.25426, -1.46519, 0.86116, 0.20986, -1.52473, 0.8605, 0.46142, -1.52473,
  0.8605, 0.15702, -1.84537, 0.78247, 0.38946, -1.52473, 0.8605, 0.15702,
  -1.52473, 0.8605, 0.46142, -1.84537, 0.78247, 0.38946, -1.53938, 0.85694,
  0.14402, -1.52473, 0.8605, 0.15702, -1.84537, 0.78247, 0.38946, -1.59401,
  0.84364, 0.06121, -1.53938, 0.85694, 0.14402, -1.84537, 0.78247, 0.38946,
  -1.62534, 0.83602, -0.03291, -1.59401, 0.84364, 0.06121, -1.84537, 0.78247,
  0.38946, -1.63125, 0.83458, -0.13193, -1.62534, 0.83602, -0.03291, -1.84537,
  0.78247, 0.38946, -1.63125, 0.83458, -0.16459, -1.63125, 0.83458, -0.13193,
  -1.84537, 0.78247, 0.38946, -1.63844, 0.83283, -0.16943, -1.63125, 0.83458,
  -0.16459, -1.63844, 0.83283, -0.16943, -1.84537, 0.78247, 0.38946, -1.84537,
  0.78247, -0.19377, -2.00541, 0.70008, 0.32972, -1.84537, 0.78247, -0.19377,
  -1.84537, 0.78247, 0.38946, -2.00541, 0.70008, -0.20097, -1.84537, 0.78247,
  -0.19377, -2.00541, 0.70008, 0.32972, -1.84537, 0.78247, -0.19377, -2.00541,
  0.70008, -0.20097, -1.87434, 0.76756, -0.20097, -2.00541, 0.70008, -0.20097,
  -2.00541, -0.70008, -0.20097, -1.87434, 0.76756, -0.20097, -2.09509, 0.59258,
  -0.20097, -2.00541, -0.70008, -0.20097, -2.00541, 0.70008, -0.20097, -2.00541,
  0.70008, 0.32972, -2.09509, 0.59258, -0.20097, -2.00541, 0.70008, -0.20097,
  -2.09509, 0.59258, -0.20097, -2.00541, 0.70008, 0.32972, -2.09509, 0.59258,
  0.29623, -2.09509, 0.59258, -0.20097, -2.00541, 0.70008, -0.20145, -2.00541,
  0.70008, -0.20097, -2.00541, 0.70008, -0.20145, -2.09509, 0.59258, -0.20097,
  -2.09509, 0.59258, -0.20102, -2.1053, 0.55124, -0.20097, -2.09509, 0.59258,
  -0.20102, -2.09509, 0.59258, -0.20097, -1.87434, 0.76756, -0.20097, -2.00541,
  -0.70008, -0.20097, -1.87434, -0.76756, -0.20097, -1.87434, 0.76756, -0.20097,
  -1.87434, -0.76756, -0.20097, -1.63125, 0.76756, -0.20097, -1.63125, 0.76756,
  -0.20097, -1.87434, -0.76756, -0.20097, -1.63125, -0.76756, -0.20097,
  -1.84537, -0.78247, -0.19377, -1.63125, -0.76756, -0.20097, -1.87434,
  -0.76756, -0.20097, -1.63125, -0.76756, -0.20097, -1.84537, -0.78247,
  -0.19377, -1.63844, -0.83283, -0.16943, -1.63125, -0.76756, -0.20097,
  -1.63844, -0.83283, -0.16943, -1.63125, -0.83283, -0.16943, -1.63125,
  -0.83458, -0.16459, -1.63125, -0.83283, -0.16943, -1.63844, -0.83283,
  -0.16943, -1.63125, -0.83458, -0.13193, -1.63125, -0.83283, -0.16943,
  -1.63125, -0.83458, -0.16459, -1.63125, -0.83458, -0.13193, -1.63125,
  -0.76756, -0.20097, -1.63125, -0.83283, -0.16943, -1.63125, -0.83458,
  -0.13193, -1.63125, 0.76756, -0.20097, -1.63125, -0.76756, -0.20097, -1.63125,
  -0.83458, -0.13193, -1.63125, 0.83283, -0.16943, -1.63125, 0.76756, -0.20097,
  -1.63125, 0.83458, -0.13193, -1.63125, 0.83283, -0.16943, -1.63125, -0.83458,
  -0.13193, -1.63125, 0.83283, -0.16943, -1.63125, 0.83458, -0.13193, -1.63125,
  0.83458, -0.16459, -1.63125, 0.83458, -0.16459, -1.63844, 0.83283, -0.16943,
  -1.63125, 0.83283, -0.16943, -1.63844, 0.83283, -0.16943, -1.63125, 0.76756,
  -0.20097, -1.63125, 0.83283, -0.16943, -1.84537, 0.78247, -0.19377, -1.63125,
  0.76756, -0.20097, -1.63844, 0.83283, -0.16943, -1.63125, 0.76756, -0.20097,
  -1.84537, 0.78247, -0.19377, -1.87434, 0.76756, -0.20097, -1.62534, -0.83602,
  -0.03291, -1.63125, 0.83458, -0.13193, -1.63125, -0.83458, -0.13193, -1.63125,
  0.83458, -0.13193, -1.62534, -0.83602, -0.03291, -1.62534, 0.83602, -0.03291,
  -1.59401, -0.84364, 0.06121, -1.62534, 0.83602, -0.03291, -1.62534, -0.83602,
  -0.03291, -1.62534, 0.83602, -0.03291, -1.59401, -0.84364, 0.06121, -1.59401,
  0.84364, 0.06121, -1.53938, -0.85694, 0.14402, -1.59401, 0.84364, 0.06121,
  -1.59401, -0.84364, 0.06121, -1.59401, 0.84364, 0.06121, -1.53938, -0.85694,
  0.14402, -1.53938, 0.85694, 0.14402, -1.52473, -0.8605, 0.15702, -1.53938,
  0.85694, 0.14402, -1.53938, -0.85694, 0.14402, -1.53938, 0.85694, 0.14402,
  -1.52473, -0.8605, 0.15702, -1.52473, 0.8605, 0.15702, -1.52473, 0.8605,
  0.15702, -1.52473, -0.8605, 0.15702, -1.46519, 0.86116, 0.20986, -1.46519,
  0.86116, 0.20986, -1.52473, -0.8605, 0.15702, -1.46519, -0.86116, 0.20986,
  -1.37648, -0.86214, 0.25426, -1.46519, 0.86116, 0.20986, -1.46519, -0.86116,
  0.20986, -1.46519, 0.86116, 0.20986, -1.37648, -0.86214, 0.25426, -1.37648,
  0.86214, 0.25426, -1.2793, -0.86322, 0.27418, -1.37648, 0.86214, 0.25426,
  -1.37648, -0.86214, 0.25426, -1.37648, 0.86214, 0.25426, -1.2793, -0.86322,
  0.27418, -1.2793, 0.86322, 0.27418, -1.18027, -0.86431, 0.26828, -1.2793,
  0.86322, 0.27418, -1.2793, -0.86322, 0.27418, -1.2793, 0.86322, 0.27418,
  -1.18027, -0.86431, 0.26828, -1.18027, 0.86431, 0.26828, -1.08615, -0.86535,
  0.23694, -1.18027, 0.86431, 0.26828, -1.18027, -0.86431, 0.26828, -1.18027,
  0.86431, 0.26828, -1.08615, -0.86535, 0.23694, -1.08615, 0.86535, 0.23694,
  -1.00335, -0.86627, 0.18232, -1.08615, 0.86535, 0.23694, -1.08615, -0.86535,
  0.23694, -1.08615, 0.86535, 0.23694, -1.00335, -0.86627, 0.18232, -1.00335,
  0.86627, 0.18232, -0.93751, -0.86699, 0.10812, -1.00335, 0.86627, 0.18232,
  -1.00335, -0.86627, 0.18232, -1.00335, 0.86627, 0.18232, -0.93751, -0.86699,
  0.10812, -0.93751, 0.86699, 0.10812, -0.89311, -0.86748, 0.01941, -0.93751,
  0.86699, 0.10812, -0.93751, -0.86699, 0.10812, -0.93751, 0.86699, 0.10812,
  -0.89311, -0.86748, 0.01941, -0.89311, 0.86748, 0.01941, 0.98599, -0.88825,
  0.06121, -0.89311, -0.86748, 0.01941, -0.93751, -0.86699, 0.10812, 0.95995,
  -0.88796, -0.01701, -0.89311, -0.86748, 0.01941, 0.98599, -0.88825, 0.06121,
  -0.89311, -0.86748, 0.01941, 0.95995, -0.88796, -0.01701, -0.87415, -0.86769,
  -0.07304, 0.98599, -0.88825, 0.06121, -0.93751, -0.86699, 0.10812, 1.04062,
  -0.88885, 0.14402, 1.04062, -0.88885, 0.14402, -0.93751, -0.86699, 0.10812,
  -1.00335, -0.86627, 0.18232, 1.04062, -0.88885, 0.14402, -1.00335, -0.86627,
  0.18232, 1.11481, -0.88967, 0.20986, 1.11481, -0.88967, 0.20986, -1.00335,
  -0.86627, 0.18232, -1.08615, -0.86535, 0.23694, 1.11481, -0.88967, 0.20986,
  -1.08615, -0.86535, 0.23694, 1.20352, -0.89065, 0.25426, 1.20352, -0.89065,
  0.25426, -1.08615, -0.86535, 0.23694, -1.18027, -0.86431, 0.26828, 1.20352,
  -0.89065, 0.25426, -1.18027, -0.86431, 0.26828, 1.3007, -0.89173, 0.27418,
  1.3007, -0.89173, 0.27418, -1.18027, -0.86431, 0.26828, -1.2793, -0.86322,
  0.27418, -1.84537, 0.78247, 0.38946, -1.52473, 0.8605, 0.46142, -1.79168,
  0.79554, 0.40951, -1.52473, 0.8605, 0.46142, 1.3007, 0.89173, 0.27418,
  1.52509, 0.89421, 0.41958, 1.39973, 0.89282, 0.26828, 1.52509, 0.89421,
  0.41958, 1.3007, 0.89173, 0.27418, 1.49385, 0.89386, 0.23694, 1.52509,
  0.89421, 0.41958, 1.39973, 0.89282, 0.26828, 1.52509, 0.89421, 0.41958,
  1.49385, 0.89386, 0.23694, 1.52509, 0.89421, 0.21633, -1.52473, 0.8605,
  0.46142, 1.52509, 0.89421, 0.41958, -1.35528, 0.86238, 0.49438, -0.87318,
  -0.8677, -0.07777, -0.87318, -0.86598, -0.07777, -0.87415, -0.86769, -0.07304,
  -0.87318, 0.83283, -0.16943, 0.94875, 0.76756, -0.20097, 0.94875, 0.83283,
  -0.16943, 0.94875, 0.76756, -0.20097, -0.87318, 0.83283, -0.16943, -0.87318,
  0.76756, -0.20097, -0.87318, 0.76756, -0.20097, 0.94875, -0.76756, -0.20097,
  0.94875, 0.76756, -0.20097, 0.94875, -0.76756, -0.20097, -0.87318, 0.76756,
  -0.20097, -0.87318, -0.76756, -0.20097, -0.87318, -0.76756, -0.20097, 0.94875,
  -0.83283, -0.16943, 0.94875, -0.76756, -0.20097, 0.94875, -0.83283, -0.16943,
  -0.87318, -0.76756, -0.20097, -0.87318, -0.83283, -0.16943, 1.52509, -0.89421,
  0.41958, 1.39973, -0.89282, 0.26828, 1.3007, -0.89173, 0.27418, 1.52509,
  -0.89421, 0.41958, 1.49385, -0.89386, 0.23694, 1.39973, -0.89282, 0.26828,
  1.49385, -0.89386, 0.23694, 1.52509, -0.89421, 0.41958, 1.52509, -0.89421,
  0.21633, 2.08537, -0.78247, 0.62205, 1.52509, -0.89421, 0.21633, 1.52509,
  -0.89421, 0.41958, 2.08537, -0.78247, 0.62205, 1.57665, -0.88392, 0.18232,
  1.52509, -0.89421, 0.21633, 2.08537, -0.78247, 0.62205, 1.6425, -0.87079,
  0.10812, 1.57665, -0.88392, 0.18232, 2.08537, -0.78247, 0.62205, 1.68689,
  -0.86194, 0.01941, 1.6425, -0.87079, 0.10812, 2.08537, -0.78247, 0.62205,
  1.68689, -0.86194, -0.08895, 1.68689, -0.86194, 0.01941, 2.08537, -0.78247,
  -0.09009, 1.68689, -0.86194, -0.08895, 2.08537, -0.78247, 0.62205, 2.05947,
  -0.78763, -0.13066, 1.68689, -0.86194, -0.08895, 2.08537, -0.78247, -0.09009,
  2.05947, -0.78763, -0.13066, 1.83285, -0.83283, -0.16943, 1.68689, -0.86194,
  -0.08895, 1.83285, -0.83283, -0.16943, 2.05947, -0.78763, -0.13066, 1.87917,
  -0.82359, -0.17389, 1.87917, 0.82359, -0.17389, 1.87917, -0.82359, -0.17389,
  2.05947, -0.78763, -0.13066, 1.87917, 0.82359, -0.17389, 1.76624, -0.76756,
  -0.20097, 1.87917, -0.82359, -0.17389, 1.76624, -0.76756, -0.20097, 1.87917,
  0.82359, -0.17389, 1.76624, 0.76756, -0.20097, 1.68689, 0.76756, -0.20097,
  1.76624, -0.76756, -0.20097, 1.76624, 0.76756, -0.20097, 1.76624, -0.76756,
  -0.20097, 1.68689, 0.76756, -0.20097, 1.68689, -0.76756, -0.20097, 1.68689,
  0.76756, -0.20097, 1.68689, -0.86194, 0.01941, 1.68689, -0.76756, -0.20097,
  1.68689, -0.76756, -0.20097, 1.68689, -0.86194, 0.01941, 1.68689, -0.83283,
  -0.16943, 1.68689, -0.83283, -0.16943, 1.68689, -0.86194, 0.01941, 1.68689,
  -0.86194, -0.08895, 1.68689, -0.86194, -0.08895, 1.83285, -0.83283, -0.16943,
  1.68689, -0.83283, -0.16943, 1.83285, -0.83283, -0.16943, 1.68689, -0.76756,
  -0.20097, 1.68689, -0.83283, -0.16943, 1.68689, -0.76756, -0.20097, 1.83285,
  -0.83283, -0.16943, 1.76624, -0.76756, -0.20097, 1.76624, -0.76756, -0.20097,
  1.83285, -0.83283, -0.16943, 1.87917, -0.82359, -0.17389, 1.87917, 0.82359,
  -0.17389, 2.05947, -0.78763, -0.13066, 2.05947, 0.78763, -0.13066, 2.05947,
  -0.78763, -0.13066, 2.08537, 0.78247, -0.09009, 2.05947, 0.78763, -0.13066,
  2.08537, 0.78247, -0.09009, 2.05947, -0.78763, -0.13066, 2.16561, 0.68511,
  0.03559, 2.16561, 0.68511, 0.03559, 2.05947, -0.78763, -0.13066, 2.21012,
  0.41455, 0.10532, 2.21012, 0.41455, 0.10532, 2.05947, -0.78763, -0.13066,
  2.21012, -0.41455, 0.10532, 2.21012, -0.41455, 0.10532, 2.05947, -0.78763,
  -0.13066, 2.16561, -0.68511, 0.03559, 2.16561, -0.68511, 0.03559, 2.05947,
  -0.78763, -0.13066, 2.08537, -0.78247, -0.09009, 2.15205, -0.70156, 0.29269,
  2.16561, -0.68511, 0.03559, 2.08537, -0.78247, -0.09009, 2.16561, -0.68511,
  0.03559, 2.15205, -0.70156, 0.29269, 2.16561, -0.68511, 0.27873, 2.15205,
  -0.70156, 0.29269, 2.21012, -0.41455, 0.23287, 2.16561, -0.68511, 0.27873,
  2.15205, -0.70156, 0.29269, 2.21012, 0.41455, 0.23287, 2.21012, -0.41455,
  0.23287, 2.15205, -0.70156, 0.29269, 2.16561, 0.68511, 0.27873, 2.21012,
  0.41455, 0.23287, 2.16561, 0.68511, 0.27873, 2.15205, -0.70156, 0.29269,
  2.15205, 0.70156, 0.29269, 2.14453, -0.71068, 0.58221, 2.15205, 0.70156,
  0.29269, 2.15205, -0.70156, 0.29269, 2.15205, 0.70156, 0.29269, 2.14453,
  -0.71068, 0.58221, 2.14453, 0.71068, 0.58221, 2.11492, -0.74661, 0.62027,
  2.14453, 0.71068, 0.58221, 2.14453, -0.71068, 0.58221, 2.14453, 0.71068,
  0.58221, 2.11492, -0.74661, 0.62027, 2.11492, 0.74661, 0.62027, 2.11492,
  -0.74661, 0.62027, 2.08537, 0.78247, 0.62205, 2.11492, 0.74661, 0.62027,
  2.08537, -0.78247, 0.62205, 2.08537, 0.78247, 0.62205, 2.11492, -0.74661,
  0.62027, 2.08537, -0.78247, 0.62205, 2.03, 0.79351, 0.6254, 2.08537, 0.78247,
  0.62205, 2.03, -0.79351, 0.6254, 2.03, 0.79351, 0.6254, 2.08537, -0.78247,
  0.62205, 2.03, -0.79351, 0.6254, 1.7087, 0.78106, 0.64481, 2.03, 0.79351,
  0.6254, 1.7087, 0.78106, 0.64481, 2.03, -0.79351, 0.6254, 1.7087, -0.78106,
  0.64481, -0.80942, -0.83112, 0.56677, 1.7087, -0.78106, 0.64481, 2.03,
  -0.79351, 0.6254, 2.03, -0.79351, 0.6254, -0.80921, -0.83245, 0.56471,
  -0.80942, -0.83112, 0.56677, -0.80921, -0.83245, 0.56471, 2.03, -0.79351,
  0.6254, 1.83476, -0.83245, 0.56471, 2.08537, -0.78247, 0.62205, 1.83476,
  -0.83245, 0.56471, 2.03, -0.79351, 0.6254, 2.08537, -0.78247, 0.62205,
  1.52509, -0.89421, 0.41958, 1.83476, -0.83245, 0.56471, 1.83476, -0.83245,
  0.56471, -0.80628, -0.85087, 0.52141, -0.80921, -0.83245, 0.56471, 1.83476,
  -0.83245, 0.56471, -0.75226, -0.85958, 0.50094, -0.80628, -0.85087, 0.52141,
  -0.75226, -0.85958, 0.60516, -0.80628, -0.85087, 0.52141, -0.75226, -0.85958,
  0.50094, -0.80628, -0.85087, 0.52141, -0.75226, -0.85958, 0.60516, -0.80628,
  -0.85087, 0.59691, -0.7225, -0.90388, 0.64709, -0.80628, -0.85087, 0.59691,
  -0.75226, -0.85958, 0.60516, -0.82287, -0.90388, 0.64709, -0.80628, -0.85087,
  0.59691, -0.7225, -0.90388, 0.64709, -0.82287, -0.90388, 0.64709, -0.80942,
  -0.83112, 0.57822, -0.80628, -0.85087, 0.59691, -0.80942, -0.83112, 0.57822,
  -0.82287, -0.90388, 0.64709, -0.90922, -0.83112, 0.57822, -0.88125, -0.85972,
  0.60053, -0.90922, -0.83112, 0.57822, -0.82287, -0.90388, 0.64709, -0.90922,
  -0.83112, 0.57822, -0.88125, -0.85972, 0.60053, -0.92226, -0.83112, 0.56782,
  -0.88125, -0.85972, 0.60053, -0.92226, -0.83112, 0.56677, -0.92226, -0.83112,
  0.56782, -0.88125, -0.85972, 0.60053, -0.92036, -0.83245, 0.56471, -0.92226,
  -0.83112, 0.56677, -0.92036, -0.83245, 0.56471, -0.88125, -0.85972, 0.60053,
  -0.88125, -0.85972, 0.50062, -0.8638, -0.93779, 0.61445, -0.88125, -0.85972,
  0.50062, -0.88125, -0.85972, 0.60053, -0.88125, -0.85972, 0.50062, -0.8638,
  -0.93779, 0.61445, -0.88098, -0.86091, 0.49782, -0.88098, -0.86091, 0.49782,
  -0.8638, -0.93779, 0.61445, -0.8638, -0.93779, 0.49782, -0.83178, -0.97333,
  0.63999, -0.8638, -0.93779, 0.49782, -0.8638, -0.93779, 0.61445, -0.8638,
  -0.93779, 0.49782, -0.83178, -0.97333, 0.63999, -0.86088, -0.94103, 0.49782,
  -0.86088, -0.94103, 0.49782, -0.83178, -0.97333, 0.63999, -0.80355, -1.00465,
  0.53448, -0.80355, -1.00465, 0.53448, -0.83178, -0.97333, 0.63999, -0.80834,
  -0.99934, 0.63733, -0.82287, -0.90388, 0.64709, -0.80834, -0.99934, 0.63733,
  -0.83178, -0.97333, 0.63999, -0.80834, -0.99934, 0.63733, -0.82287, -0.90388,
  0.64709, -0.7225, -0.90388, 0.64709, -0.80834, -0.99934, 0.63733, -0.7225,
  -0.90388, 0.64709, -0.69432, -0.99934, 0.63733, -0.69432, -0.99934, 0.63733,
  -0.7225, -0.90388, 0.64709, -0.69894, -0.93893, 0.6435, -0.7225, -0.90388,
  0.64709, -0.72024, -0.90724, 0.6372, -0.69894, -0.93893, 0.6435, -0.7225,
  -0.90388, 0.64709, -0.75076, -0.86182, 0.5942, -0.72024, -0.90724, 0.6372,
  -0.75226, -0.85958, 0.60516, -0.75076, -0.86182, 0.5942, -0.7225, -0.90388,
  0.64709, -0.82942, -0.86182, 0.5942, -0.72024, -0.90724, 0.6372, -0.75076,
  -0.86182, 0.5942, -0.72024, -0.90724, 0.6372, -0.82942, -0.86182, 0.5942,
  -0.82942, -0.90724, 0.6372, -0.82942, -0.86182, 0.5942, -0.82942, -0.88411,
  0.50732, -0.82942, -0.90724, 0.6372, -0.82942, -0.88411, 0.50732, -0.82942,
  -0.86182, 0.5942, -0.82942, -0.86182, 0.57227, -0.82942, -0.86182, 0.5942,
  -0.75076, -0.86182, 0.57227, -0.82942, -0.86182, 0.57227, -0.75076, -0.86182,
  0.57227, -0.82942, -0.86182, 0.5942, -0.75076, -0.86182, 0.5942, -0.73578,
  -0.88411, 0.50732, -0.82942, -0.86182, 0.57227, -0.75076, -0.86182, 0.57227,
  -0.82942, -0.86182, 0.57227, -0.73578, -0.88411, 0.50732, -0.82942, -0.88411,
  0.50732, -0.73578, -0.88411, 0.50732, -0.82942, -0.93849, 0.50732, -0.82942,
  -0.88411, 0.50732, -0.82942, -0.93849, 0.50732, -0.73578, -0.88411, 0.50732,
  -0.72295, -0.93849, 0.50732, -0.72295, -0.93849, 0.50732, -0.73578, -0.88411,
  0.50732, -0.72295, -0.90321, 0.50732, -0.72295, -0.93849, 0.50732, -0.82942,
  -0.96505, 0.52262, -0.82942, -0.93849, 0.50732, -0.82942, -0.96505, 0.52262,
  -0.72295, -0.93849, 0.50732, -0.74445, -1.03244, 0.56144, -0.74445, -1.03244,
  0.56144, -0.72295, -0.93849, 0.50732, -0.67687, -1.03244, 0.56144, -0.67687,
  -1.03244, 0.56144, -0.72295, -0.93849, 0.50732, -0.67687, -1.00214, 0.54398,
  -0.72295, -0.93849, 0.50732, -0.67687, -1.02116, 0.54398, -0.67687, -1.00214,
  0.54398, -0.67687, -1.02116, 0.54398, -0.67687, -1.03244, 0.56144, -0.67687,
  -1.00214, 0.54398, -0.67687, -1.03244, 0.56144, -0.67687, -1.02116, 0.54398,
  -0.67687, -1.04069, 0.55524, -0.67687, -1.03244, 0.56144, -0.67687, -1.04069,
  0.55524, -0.67687, -1.04101, 0.59622, -0.67687, -1.04101, 0.59622, -0.67687,
  -1.04069, 0.55524, -0.67687, -1.05178, 0.60023, -0.67687, -1.04069, 0.55524,
  -0.72687, -1.05178, 0.60023, -0.67687, -1.05178, 0.60023, -0.72687, -1.05178,
  0.60023, -0.67687, -1.04069, 0.55524, -0.74492, -1.04069, 0.55524, -0.74492,
  -1.04069, 0.55524, -0.80355, -1.00465, 0.63357, -0.72687, -1.05178, 0.60023,
  -0.80355, -1.00465, 0.63357, -0.74492, -1.04069, 0.55524, -0.80355, -1.00465,
  0.53448, -0.80355, -1.00465, 0.53448, -0.80834, -0.99934, 0.63733, -0.80355,
  -1.00465, 0.63357, -0.69432, -0.99934, 0.63733, -0.80355, -1.00465, 0.63357,
  -0.80834, -0.99934, 0.63733, -0.80355, -1.00465, 0.63357, -0.69432, -0.99934,
  0.63733, -0.72687, -1.05178, 0.60023, -0.72687, -1.05178, 0.60023, -0.69432,
  -0.99934, 0.63733, -0.67687, -1.05178, 0.60023, -0.67687, -1.05178, 0.60023,
  -0.69432, -0.99934, 0.63733, -0.67687, -1.03234, 0.61398, -0.69432, -0.99934,
  0.63733, -0.67687, -1.01589, 0.61398, -0.67687, -1.03234, 0.61398, -0.69432,
  -0.99934, 0.63733, -0.68745, -0.99589, 0.62813, -0.67687, -1.01589, 0.61398,
  -0.69432, -0.99934, 0.63733, -0.69089, -0.95091, 0.63273, -0.68745, -0.99589,
  0.62813, -0.69089, -0.95091, 0.63273, -0.69432, -0.99934, 0.63733, -0.69894,
  -0.93893, 0.6435, -0.69894, -0.93893, 0.6435, -0.72024, -0.90724, 0.6372,
  -0.69089, -0.95091, 0.63273, -0.72024, -0.90724, 0.6372, -0.68745, -0.99589,
  0.62813, -0.69089, -0.95091, 0.63273, -0.72024, -0.90724, 0.6372, -0.79054,
  -0.99589, 0.62813, -0.68745, -0.99589, 0.62813, -0.82942, -0.90724, 0.6372,
  -0.79054, -0.99589, 0.62813, -0.72024, -0.90724, 0.6372, -0.79054, -0.99589,
  0.62813, -0.82942, -0.90724, 0.6372, -0.82942, -0.96505, 0.63129, -0.82942,
  -0.90724, 0.6372, -0.82942, -0.93849, 0.50732, -0.82942, -0.96505, 0.63129,
  -0.82942, -0.90724, 0.6372, -0.82942, -0.88411, 0.50732, -0.82942, -0.93849,
  0.50732, -0.82942, -0.96505, 0.63129, -0.82942, -0.93849, 0.50732, -0.82942,
  -0.96505, 0.52262, -0.74445, -1.03244, 0.56144, -0.82942, -0.96505, 0.63129,
  -0.82942, -0.96505, 0.52262, -0.82942, -0.96505, 0.63129, -0.74445, -1.03244,
  0.56144, -0.73364, -1.04101, 0.59622, -0.74445, -1.03244, 0.56144, -0.67687,
  -1.04101, 0.59622, -0.73364, -1.04101, 0.59622, -0.67687, -1.04101, 0.59622,
  -0.74445, -1.03244, 0.56144, -0.67687, -1.03244, 0.56144, -0.68745, -0.99589,
  0.62813, -0.73364, -1.04101, 0.59622, -0.67687, -1.04101, 0.59622, -0.73364,
  -1.04101, 0.59622, -0.68745, -0.99589, 0.62813, -0.79054, -0.99589, 0.62813,
  -0.82942, -0.96505, 0.63129, -0.73364, -1.04101, 0.59622, -0.79054, -0.99589,
  0.62813, -0.68745, -0.99589, 0.62813, -0.67687, -1.04101, 0.59622, -0.67687,
  -1.01589, 0.61398, -0.67687, -1.04101, 0.59622, -0.67687, -1.03234, 0.61398,
  -0.67687, -1.01589, 0.61398, -0.67687, -1.03234, 0.61398, -0.67687, -1.04101,
  0.59622, -0.67687, -1.05178, 0.60023, -0.8638, -0.93779, 0.61445, -0.82287,
  -0.90388, 0.64709, -0.83178, -0.97333, 0.63999, -0.88125, -0.85972, 0.60053,
  -0.82287, -0.90388, 0.64709, -0.8638, -0.93779, 0.61445, -0.92226, -0.83112,
  0.56677, -0.80942, -0.83112, 0.57822, -0.92226, -0.83112, 0.56782, -0.80942,
  -0.83112, 0.57822, -0.92226, -0.83112, 0.56677, -0.80942, -0.83112, 0.56677,
  -0.80921, -0.83245, 0.56471, -0.80942, -0.83112, 0.57822, -0.80942, -0.83112,
  0.56677, -0.80942, -0.83112, 0.57822, -0.80921, -0.83245, 0.56471, -0.80628,
  -0.85087, 0.59691, -0.80628, -0.85087, 0.59691, -0.80921, -0.83245, 0.56471,
  -0.80628, -0.85087, 0.52141, -0.92226, -0.83112, 0.56782, -0.80942, -0.83112,
  0.57822, -0.90922, -0.83112, 0.57822, 1.83476, 0.83245, 0.56471, 2.08537,
  0.78247, 0.62205, 2.03, 0.79351, 0.6254, 2.11492, -0.74661, 0.62027, 2.08537,
  -0.78247, -0.09009, 2.08537, -0.78247, 0.62205, 2.14453, -0.71068, 0.58221,
  2.08537, -0.78247, -0.09009, 2.11492, -0.74661, 0.62027, 2.15205, -0.70156,
  0.29269, 2.08537, -0.78247, -0.09009, 2.14453, -0.71068, 0.58221, 2.08537,
  0.78247, 0.62205, 2.16561, 0.68511, 0.03559, 2.11492, 0.74661, 0.62027,
  2.16561, 0.68511, 0.03559, 2.08537, 0.78247, 0.62205, 2.08537, 0.78247,
  -0.09009, 2.11492, 0.74661, 0.62027, 2.16561, 0.68511, 0.03559, 2.15205,
  0.70156, 0.29269, 2.15205, 0.70156, 0.29269, 2.16561, 0.68511, 0.03559,
  2.16561, 0.68511, 0.27873, 2.16561, 0.68511, 0.03559, 2.21012, 0.41455,
  0.23287, 2.16561, 0.68511, 0.27873, 2.21012, 0.41455, 0.23287, 2.16561,
  0.68511, 0.03559, 2.21012, 0.41455, 0.10532, 2.21012, -0.41455, 0.10532,
  2.21012, 0.41455, 0.23287, 2.21012, 0.41455, 0.10532, 2.21012, 0.41455,
  0.23287, 2.21012, -0.41455, 0.10532, 2.21012, -0.41455, 0.23287, 2.16561,
  -0.68511, 0.03559, 2.21012, -0.41455, 0.23287, 2.21012, -0.41455, 0.10532,
  2.21012, -0.41455, 0.23287, 2.16561, -0.68511, 0.03559, 2.16561, -0.68511,
  0.27873, 2.11492, 0.74661, 0.62027, 2.15205, 0.70156, 0.29269, 2.14453,
  0.71068, 0.58221, 1.68689, 0.86194, -0.08895, 1.68689, 0.83283, -0.16943,
  1.83285, 0.83283, -0.16943, -0.76048, 0.86091, 0.49782, 1.83476, 0.83245,
  0.56471, -0.80628, 0.85352, 0.51518, -0.80628, 0.85352, 0.51518, 1.83476,
  0.83245, 0.56471, -0.80942, 0.83377, 0.56159, -0.80942, 0.83377, 0.56159,
  1.83476, 0.83245, 0.56471, -0.92226, 0.83377, 0.56159, -0.92226, 0.83377,
  0.56782, -0.80942, 0.83377, 0.56159, -0.92226, 0.83377, 0.56159, -0.80942,
  0.83377, 0.56159, -0.92226, 0.83377, 0.56782, -0.80942, 0.83377, 0.57822,
  -0.80942, 0.83377, 0.57822, -0.92226, 0.83377, 0.56782, -0.90922, 0.83377,
  0.57822, -0.88125, 0.86237, 0.60053, -0.90922, 0.83377, 0.57822, -0.92226,
  0.83377, 0.56782, -0.90922, 0.83377, 0.57822, -0.88125, 0.86237, 0.60053,
  -0.82287, 0.90653, 0.64709, -0.82287, 0.90653, 0.64709, -0.88125, 0.86237,
  0.60053, -0.8638, 0.94044, 0.61445, -0.88125, 0.86237, 0.49782, -0.8638,
  0.94044, 0.61445, -0.88125, 0.86237, 0.60053, -0.8638, 0.94044, 0.61445,
  -0.88125, 0.86237, 0.49782, -0.8638, 0.94044, 0.49782, -0.88125, 0.86237,
  0.49782, -0.76048, 0.86091, 0.49782, -0.8638, 0.94044, 0.49782, -0.76048,
  0.86091, 0.49782, -0.88125, 0.86237, 0.49782, -0.88334, 0.86091, 0.49782,
  -0.88125, 0.86237, 0.49782, -0.92226, 0.83377, 0.56159, -0.88334, 0.86091,
  0.49782, -0.92226, 0.83377, 0.56159, -0.88125, 0.86237, 0.49782, -0.88125,
  0.86237, 0.60053, -0.92226, 0.83377, 0.56159, -0.88125, 0.86237, 0.60053,
  -0.92226, 0.83377, 0.56782, -0.8638, 0.94044, 0.49782, -0.76048, 0.86091,
  0.49782, -0.86088, 0.94368, 0.49782, -0.86088, 0.94368, 0.49782, -0.76048,
  0.86091, 0.49782, -0.73488, 0.94368, 0.49782, -0.73488, 0.94368, 0.49782,
  -0.76048, 0.86091, 0.49782, -0.75226, 0.86224, 0.49782, -0.80628, 0.85352,
  0.51518, -0.75226, 0.86224, 0.49782, -0.76048, 0.86091, 0.49782, -0.75226,
  0.86224, 0.49782, -0.80628, 0.85352, 0.51518, -0.75226, 0.86224, 0.60516,
  -0.75226, 0.86224, 0.60516, -0.80628, 0.85352, 0.51518, -0.80628, 0.85352,
  0.59691, -0.80942, 0.83377, 0.56159, -0.80628, 0.85352, 0.59691, -0.80628,
  0.85352, 0.51518, -0.80628, 0.85352, 0.59691, -0.80942, 0.83377, 0.56159,
  -0.80942, 0.83377, 0.57822, -0.80942, 0.83377, 0.57822, -0.82287, 0.90653,
  0.64709, -0.80628, 0.85352, 0.59691, -0.82287, 0.90653, 0.64709, -0.80942,
  0.83377, 0.57822, -0.90922, 0.83377, 0.57822, -0.80628, 0.85352, 0.59691,
  -0.82287, 0.90653, 0.64709, -0.7225, 0.90653, 0.64709, -0.82287, 0.90653,
  0.64709, -0.80834, 1.00199, 0.63733, -0.7225, 0.90653, 0.64709, -0.80834,
  1.00199, 0.63733, -0.82287, 0.90653, 0.64709, -0.83178, 0.97598, 0.63999,
  -0.82287, 0.90653, 0.64709, -0.8638, 0.94044, 0.61445, -0.83178, 0.97598,
  0.63999, -0.86088, 0.94368, 0.49782, -0.83178, 0.97598, 0.63999, -0.8638,
  0.94044, 0.61445, -0.86088, 0.94368, 0.49782, -0.80834, 1.00199, 0.63733,
  -0.83178, 0.97598, 0.63999, -0.86088, 0.94368, 0.49782, -0.80355, 1.0073,
  0.63357, -0.80834, 1.00199, 0.63733, -0.80355, 1.0073, 0.63357, -0.86088,
  0.94368, 0.49782, -0.80355, 1.0073, 0.53448, -0.73488, 0.94368, 0.49782,
  -0.80355, 1.0073, 0.53448, -0.86088, 0.94368, 0.49782, -0.80355, 1.0073,
  0.53448, -0.73488, 0.94368, 0.49782, -0.74492, 1.04334, 0.55524, -0.74492,
  1.04334, 0.55524, -0.73488, 0.94368, 0.49782, -0.67687, 1.04334, 0.55524,
  -0.67687, 1.04334, 0.55524, -0.73488, 0.94368, 0.49782, -0.67687, 1.02381,
  0.54398, -0.73488, 0.94368, 0.49782, -0.72295, 0.94114, 0.50732, -0.67687,
  1.02381, 0.54398, -0.73488, 0.94368, 0.49782, -0.72295, 0.90586, 0.50732,
  -0.72295, 0.94114, 0.50732, -0.72295, 0.90586, 0.50732, -0.73488, 0.94368,
  0.49782, -0.73488, 0.88811, 0.49782, -0.73488, 0.94368, 0.49782, -0.75226,
  0.86224, 0.49782, -0.73488, 0.88811, 0.49782, -0.73578, 0.88677, 0.50732,
  -0.73488, 0.88811, 0.49782, -0.75226, 0.86224, 0.49782, -0.73488, 0.88811,
  0.49782, -0.73578, 0.88677, 0.50732, -0.72295, 0.90586, 0.50732, -0.73578,
  0.88677, 0.50732, -0.72295, 0.94114, 0.50732, -0.72295, 0.90586, 0.50732,
  -0.82942, 0.88677, 0.50732, -0.72295, 0.94114, 0.50732, -0.73578, 0.88677,
  0.50732, -0.72295, 0.94114, 0.50732, -0.82942, 0.88677, 0.50732, -0.82942,
  0.94114, 0.50732, -0.82942, 0.9099, 0.6372, -0.82942, 0.94114, 0.50732,
  -0.82942, 0.88677, 0.50732, -0.82942, 0.9677, 0.63129, -0.82942, 0.94114,
  0.50732, -0.82942, 0.9099, 0.6372, -0.82942, 0.94114, 0.50732, -0.82942,
  0.9677, 0.63129, -0.82942, 0.9677, 0.52262, -0.82942, 0.9677, 0.63129,
  -0.74445, 1.03509, 0.56144, -0.82942, 0.9677, 0.52262, -0.74445, 1.03509,
  0.56144, -0.82942, 0.9677, 0.63129, -0.73364, 1.04366, 0.59622, -0.73364,
  1.04366, 0.59622, -0.82942, 0.9677, 0.63129, -0.79054, 0.99854, 0.62813,
  -0.82942, 0.9677, 0.63129, -0.72024, 0.9099, 0.6372, -0.79054, 0.99854,
  0.62813, -0.72024, 0.9099, 0.6372, -0.82942, 0.9677, 0.63129, -0.82942,
  0.9099, 0.6372, -0.82942, 0.9099, 0.6372, -0.75076, 0.86447, 0.5942, -0.72024,
  0.9099, 0.6372, -0.75076, 0.86447, 0.5942, -0.82942, 0.9099, 0.6372, -0.82942,
  0.86447, 0.5942, -0.82942, 0.9099, 0.6372, -0.82942, 0.88677, 0.50732,
  -0.82942, 0.86447, 0.5942, -0.82942, 0.86447, 0.5942, -0.82942, 0.88677,
  0.50732, -0.82942, 0.86447, 0.57227, -0.82942, 0.88677, 0.50732, -0.75076,
  0.86447, 0.57227, -0.82942, 0.86447, 0.57227, -0.75076, 0.86447, 0.57227,
  -0.82942, 0.88677, 0.50732, -0.73578, 0.88677, 0.50732, -0.73578, 0.88677,
  0.50732, -0.75226, 0.86224, 0.49782, -0.75076, 0.86447, 0.57227, -0.75076,
  0.86447, 0.57227, -0.75226, 0.86224, 0.49782, -0.75076, 0.86447, 0.5942,
  -0.75226, 0.86224, 0.60516, -0.75076, 0.86447, 0.5942, -0.75226, 0.86224,
  0.49782, -0.7225, 0.90653, 0.64709, -0.75076, 0.86447, 0.5942, -0.75226,
  0.86224, 0.60516, -0.7225, 0.90653, 0.64709, -0.72024, 0.9099, 0.6372,
  -0.75076, 0.86447, 0.5942, -0.69894, 0.94158, 0.6435, -0.72024, 0.9099,
  0.6372, -0.7225, 0.90653, 0.64709, -0.72024, 0.9099, 0.6372, -0.69894,
  0.94158, 0.6435, -0.69089, 0.95357, 0.63273, -0.69894, 0.94158, 0.6435,
  -0.68745, 0.99854, 0.62813, -0.69089, 0.95357, 0.63273, -0.69432, 1.00199,
  0.63733, -0.68745, 0.99854, 0.62813, -0.69894, 0.94158, 0.6435, -0.69432,
  1.00199, 0.63733, -0.67687, 1.01854, 0.61398, -0.68745, 0.99854, 0.62813,
  -0.67687, 1.01854, 0.61398, -0.69432, 1.00199, 0.63733, -0.67687, 1.03499,
  0.61398, -0.69432, 1.00199, 0.63733, -0.67687, 1.05443, 0.60023, -0.67687,
  1.03499, 0.61398, -0.69432, 1.00199, 0.63733, -0.72687, 1.05443, 0.60023,
  -0.67687, 1.05443, 0.60023, -0.69432, 1.00199, 0.63733, -0.80355, 1.0073,
  0.63357, -0.72687, 1.05443, 0.60023, -0.80355, 1.0073, 0.63357, -0.69432,
  1.00199, 0.63733, -0.80834, 1.00199, 0.63733, -0.7225, 0.90653, 0.64709,
  -0.80834, 1.00199, 0.63733, -0.69432, 1.00199, 0.63733, -0.7225, 0.90653,
  0.64709, -0.69432, 1.00199, 0.63733, -0.69894, 0.94158, 0.6435, -0.80355,
  1.0073, 0.63357, -0.74492, 1.04334, 0.55524, -0.72687, 1.05443, 0.60023,
  -0.74492, 1.04334, 0.55524, -0.80355, 1.0073, 0.63357, -0.80355, 1.0073,
  0.53448, -0.74492, 1.04334, 0.55524, -0.67687, 1.05443, 0.60023, -0.72687,
  1.05443, 0.60023, -0.67687, 1.05443, 0.60023, -0.74492, 1.04334, 0.55524,
  -0.67687, 1.04334, 0.55524, -0.67687, 1.04334, 0.55524, -0.67687, 1.04366,
  0.59622, -0.67687, 1.05443, 0.60023, -0.67687, 1.04366, 0.59622, -0.67687,
  1.04334, 0.55524, -0.67687, 1.03509, 0.56144, -0.67687, 1.03509, 0.56144,
  -0.67687, 1.04334, 0.55524, -0.67687, 1.02381, 0.54398, -0.67687, 1.03509,
  0.56144, -0.67687, 1.02381, 0.54398, -0.67687, 1.00479, 0.54398, -0.67687,
  1.02381, 0.54398, -0.72295, 0.94114, 0.50732, -0.67687, 1.00479, 0.54398,
  -0.72295, 0.94114, 0.50732, -0.67687, 1.03509, 0.56144, -0.67687, 1.00479,
  0.54398, -0.72295, 0.94114, 0.50732, -0.74445, 1.03509, 0.56144, -0.67687,
  1.03509, 0.56144, -0.82942, 0.94114, 0.50732, -0.74445, 1.03509, 0.56144,
  -0.72295, 0.94114, 0.50732, -0.74445, 1.03509, 0.56144, -0.82942, 0.94114,
  0.50732, -0.82942, 0.9677, 0.52262, -0.73364, 1.04366, 0.59622, -0.67687,
  1.03509, 0.56144, -0.74445, 1.03509, 0.56144, -0.67687, 1.03509, 0.56144,
  -0.73364, 1.04366, 0.59622, -0.67687, 1.04366, 0.59622, -0.73364, 1.04366,
  0.59622, -0.68745, 0.99854, 0.62813, -0.67687, 1.04366, 0.59622, -0.68745,
  0.99854, 0.62813, -0.73364, 1.04366, 0.59622, -0.79054, 0.99854, 0.62813,
  -0.79054, 0.99854, 0.62813, -0.72024, 0.9099, 0.6372, -0.68745, 0.99854,
  0.62813, -0.68745, 0.99854, 0.62813, -0.72024, 0.9099, 0.6372, -0.69089,
  0.95357, 0.63273, -0.67687, 1.04366, 0.59622, -0.68745, 0.99854, 0.62813,
  -0.67687, 1.01854, 0.61398, -0.67687, 1.03499, 0.61398, -0.67687, 1.04366,
  0.59622, -0.67687, 1.01854, 0.61398, -0.67687, 1.04366, 0.59622, -0.67687,
  1.03499, 0.61398, -0.67687, 1.05443, 0.60023, -0.80628, 0.85352, 0.59691,
  -0.7225, 0.90653, 0.64709, -0.75226, 0.86224, 0.60516, -0.75076, 0.86447,
  0.5942, -0.82942, 0.86447, 0.57227, -0.75076, 0.86447, 0.57227, -0.82942,
  0.86447, 0.57227, -0.75076, 0.86447, 0.5942, -0.82942, 0.86447, 0.5942,
  -0.86088, 0.94368, 0.49782, -0.8638, 0.94044, 0.61445, -0.8638, 0.94044,
  0.49782, -0.75137, -0.86091, 0.49782, -0.73488, -0.94103, 0.49782, -0.73488,
  -0.88546, 0.49782, -2.11079, -0.52897, 0.18019, -2.21012, 0.12657, 0.07983,
  -2.11079, 0.52897, 0.18019, -2.21012, 0.12657, 0.07983, -2.11079, -0.52897,
  0.18019, -2.21012, -0.12657, 0.07983, 1.17165, 0.54045, 0.95101, -0.17251,
  0.216, 0.98, -0.23087, 0.54045, 0.95101, -0.17251, 0.216, 0.98, 1.17165,
  0.54045, 0.95101, 1.12081, 0.216, 0.98,
].map((item) => item * 20);

class RouteLayer {
  constructor({
    view,
    data = [],
    routeColor = "lightblue",
    speed = 40,
    isLock = false,
  }) {
    console.log(1);
    this.routeLayer = null;
    // paths_wgs84.push(data[0]);
    this.path = new Graphic({
      geometry: {
        type: "polyline",
        paths: data,
        spatialReference: view.spatialReference,
      },
      symbol: { type: "simple-line", color: "lightblue", width: "4px" },
      attributes: {
        OBJECTID: 1,
      },
    });
    this._addLineToMap(view, routeColor);
    this.gl = undefined;
    this.viewMatrix = new Array(16).fill(0); // 最后传进webgl偏移的矩阵
    this.rotateMatrix = new Array(16).fill(0); // 最后传进webgl偏移的矩阵
    this.cache = {
      projection,
      extent: {
        xmin: Infinity,
        xmax: -Infinity,
        ymin: Infinity,
        ymax: -Infinity,
      },
      transformation: new Array(16).fill(0), // xy方向投影变换矩阵
      position: [0, 0],
      angle: [1, 0],
      v: speed / 3.6,
      lastTime: 0,
      index: 0,
      length: 0,
      isLock,
    };
    this.paths_3857 = undefined;
    this.requestRender = externalRenderers.requestRender; // 请求渲染
  }

  setup(context) {
    const { cache } = this;

    const gl = (this.gl = context.gl);

    //#region 轮子 program
    const vs = gl.createShader(gl.VERTEX_SHADER);
    gl.shaderSource(vs, VS);
    gl.compileShader(vs);
    const fs = gl.createShader(gl.FRAGMENT_SHADER);
    gl.shaderSource(fs, FS);
    gl.compileShader(fs);
    const program = (this.program = gl.createProgram());
    gl.attachShader(program, vs);
    gl.attachShader(program, fs);
    gl.linkProgram(program);
    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      throw new Error(gl.getProgramInfoLog(program));
    }
    gl.deleteShader(vs);
    gl.deleteShader(fs);
    this.aPosition = gl.getAttribLocation(program, "a_Position");
    gl.bindAttribLocation(program, this.aPosition, "a_Position");
    this.uCamera = gl.getUniformLocation(program, "u_Camera");
    this.PositionBuffer_lunzi = gl.createBuffer();
    this.PositionBuffer_car = gl.createBuffer();
    this.bufferSize_lunzi = 0;
    this.bufferSize_car = 0;
    //#endregion

    const cacheRenderFunc = this.render;
    this.render = () => {};
    projection.load().then(() => {
      const { extent } = this.cache;
      cache.projection = projection;
      /** @type {Array<{x:number, y:number,length: number}>} */
      const paths = (this.paths_3857 = projection
        .project(this.path.geometry, { wkid: 3857 })
        .paths[0].map((e) => ({ x: e[0], y: e[1] })));
      for (var i = 0, ii = paths.length - 1; i < ii; i++) {
        var di = paths[i],
          ni = paths[i + 1];
        di.length = this._getLength(di.x - ni.x, di.y - ni.y);
        di.angle = [-(ni.y - di.y) / di.length, -(ni.x - di.x) / di.length];
        if (extent.xmin > di.x) extent.xmin = di.x;
        else if (extent.xmax < di.x) extent.xmax = di.x;
        if (extent.ymin > di.y) extent.ymin = di.y;
        else if (extent.ymax < di.y) extent.ymax = di.y;
      }

      gl.bindBuffer(gl.ARRAY_BUFFER, this.PositionBuffer_lunzi);
      gl.bufferData(
        gl.ARRAY_BUFFER,
        new Float32Array(lunzi_position),
        gl.STATIC_DRAW
      );
      this.bufferSize_lunzi = lunzi_position.length / 3;
      gl.bindBuffer(gl.ARRAY_BUFFER, this.PositionBuffer_car);
      gl.bufferData(
        gl.ARRAY_BUFFER,
        new Float32Array(car_position),
        gl.STATIC_DRAW
      );
      this.bufferSize_car = car_position.length / 3;

      car_position.splice(0, car_position.length);
      lunzi_position.splice(0, lunzi_position.length);
      const { xmin, ymin, xmax, ymax } = extent;
      const center = (extent.center = [
        (xmin + xmax) / 2,
        (ymin + ymax) / 2,
        0,
      ]);

      /**
       * offsetFunction 3
       * 生成偏移矩阵、xyz比例矩阵
       **/
      const resCoods = new Array(12);
      externalRenderers.toRenderCoordinates(
        view,
        [xmin, ymin, 0, xmin + 1, ymin, 0, xmin, ymin + 1, 0, xmin, ymin, 1],
        0,
        { wkid: 3857 },
        resCoods,
        0,
        4
      );
      // 偏移矩阵：cache.transformation
      externalRenderers.renderCoordinateTransformAt(
        view,
        center,
        { wkid: 3857 },
        cache.transformation
      );
      // xyz比例矩阵：最后一个参数
      this._multiply(cache.transformation, cache.transformation, [
        this._getLength(
          resCoods[3] - resCoods[0],
          resCoods[4] - resCoods[1],
          resCoods[5] - resCoods[2]
        ),
        0,
        0,
        0,
        0,
        this._getLength(
          resCoods[6] - resCoods[0],
          resCoods[7] - resCoods[1],
          resCoods[8] - resCoods[2]
        ),
        0,
        0,
        0,
        0,
        this._getLength(
          resCoods[9] - resCoods[0],
          resCoods[10] - resCoods[1],
          resCoods[11] - resCoods[2]
        ),
        0,
        0,
        0,
        0,
        1,
      ]);
      cache.lastTime = performance.now();

      this.render = cacheRenderFunc;

      //   range.oninput = (e) => {
      //     var v = e.currentTarget.value;
      //     rangeText.innerHTML = v + "km/h";
      //     cache.v = v / 3.6;
      //   };

      //   lock.onchange = (e) => {
      //     cache.isLock = e.currentTarget.checked;
      //   };
    });
  }
  render(context) {
    const { cache } = this;
    const { position, angle } = cache;
    /**
     * 此camera非sceneView的camera
     * 为externalRenderers.getRenderCamera()得到的RenderCamera
     **/
    const { camera } = context;
    const gl = context.gl;

    var t = performance.now();
    this._updatePos(cache, t - cache.lastTime, t);
    cache.lastTime = t;

    gl.enable(gl.DEPTH_TEST);
    gl.enable(gl.CULL_FACE);

    gl.cullFace(gl.FRONT);
    gl.useProgram(this.program);

    this._rotate(
      this.rotateMatrix,
      ...angle,
      [0, 0, 1],
      position[0],
      position[1],
      0
    );
    this._multiply(this.viewMatrix, cache.transformation, this.rotateMatrix);
    this._multiply(this.viewMatrix, camera.viewMatrix, this.viewMatrix);
    this._multiply(this.viewMatrix, camera.projectionMatrix, this.viewMatrix);
    gl.uniformMatrix4fv(this.uCamera, false, this.viewMatrix);

    gl.bindBuffer(gl.ARRAY_BUFFER, this.PositionBuffer_lunzi);
    gl.enableVertexAttribArray(this.aPosition);
    gl.vertexAttribPointer(this.aPosition, 3, gl.FLOAT, false, 0, 0);
    gl.drawArrays(gl.TRIANGLES, 0, this.bufferSize_lunzi);

    this.rotateMatrix[14] = 0.4;
    this._multiply(this.viewMatrix, cache.transformation, this.rotateMatrix);
    this._multiply(this.viewMatrix, camera.viewMatrix, this.viewMatrix);
    this._multiply(this.viewMatrix, camera.projectionMatrix, this.viewMatrix);
    gl.uniformMatrix4fv(this.uCamera, false, this.viewMatrix);

    gl.bindBuffer(gl.ARRAY_BUFFER, this.PositionBuffer_car);
    gl.vertexAttribPointer(this.aPosition, 3, gl.FLOAT, false, 0, 0);
    gl.drawArrays(gl.TRIANGLES, 0, this.bufferSize_car);

    gl.cullFace(gl.BACK);
    gl.disable(gl.CULL_FACE);
    gl.disable(gl.DEPTH_TEST);
    this.requestRender(view);
  }

  _addLineToMap(view, routeColor) {
    const layer = layerCreate({
      type: "feature",
      objectIdField: "OBJECTID",
      outFields: ["*"],
      fields: [{ name: "OBJECTID", alias: "OBJECTID", type: "oid" }],
      source: [this.path], // 使用自定义的数据源
      renderer: {
        type: "simple",
        symbol: {
          type: "simple-line",
          color: routeColor,
          width: 4,
        },
      },
      elevationInfo: {
        mode: "on-the-ground",
      },
    });
    view.map.add(layer);
    this.routeLayer = layer;
  }

  _updatePos(cache = this.cache, dt, t) {
    var length = dt * cache.v * 0.001;
    var indexPath = this.paths_3857[cache.index];
    cache.length += length;
    while (cache.length > indexPath.length) {
      cache.index += 1;
      if (cache.index > this.paths_3857.length - 2) cache.index = 0;
      cache.length -= indexPath.length;
      indexPath = this.paths_3857[cache.index];
    }
    var nextPath = this.paths_3857[cache.index + 1];
    var parsent = cache.length / indexPath.length;
    var nx = (nextPath.x - indexPath.x) * parsent + indexPath.x;
    var ny = (nextPath.y - indexPath.y) * parsent + indexPath.y;
    cache.position[0] = nx - cache.extent.center[0];
    cache.position[1] = ny - cache.extent.center[1];
    cache.angle[0] = indexPath.angle[0];
    cache.angle[1] = indexPath.angle[1];
    if (cache.isLock) {
      const camera = view.camera.clone();
      camera.tilt = 1;
      const position = camera.position;

      position.x = nx;
      position.y = ny;
      position.spatialReference = { wkid: 3857 };

      view.camera = camera;
    }
  }

  _getLength(x, y, z = 0) {
    return Math.sqrt(x * x + y * y + z * z);
  }

  _multiply(out, a, b) {
    let a00 = a[0],
      a01 = a[1],
      a02 = a[2],
      a03 = a[3];
    let a10 = a[4],
      a11 = a[5],
      a12 = a[6],
      a13 = a[7];
    let a20 = a[8],
      a21 = a[9],
      a22 = a[10],
      a23 = a[11];
    let a30 = a[12],
      a31 = a[13],
      a32 = a[14],
      a33 = a[15];
    // Cache only the current line of the second matrix
    let b0 = b[0],
      b1 = b[1],
      b2 = b[2],
      b3 = b[3];
    out[0] = b0 * a00 + b1 * a10 + b2 * a20 + b3 * a30;
    out[1] = b0 * a01 + b1 * a11 + b2 * a21 + b3 * a31;
    out[2] = b0 * a02 + b1 * a12 + b2 * a22 + b3 * a32;
    out[3] = b0 * a03 + b1 * a13 + b2 * a23 + b3 * a33;
    (b0 = b[4]), (b1 = b[5]), (b2 = b[6]), (b3 = b[7]);
    out[4] = b0 * a00 + b1 * a10 + b2 * a20 + b3 * a30;
    out[5] = b0 * a01 + b1 * a11 + b2 * a21 + b3 * a31;
    out[6] = b0 * a02 + b1 * a12 + b2 * a22 + b3 * a32;
    out[7] = b0 * a03 + b1 * a13 + b2 * a23 + b3 * a33;
    (b0 = b[8]), (b1 = b[9]), (b2 = b[10]), (b3 = b[11]);
    out[8] = b0 * a00 + b1 * a10 + b2 * a20 + b3 * a30;
    out[9] = b0 * a01 + b1 * a11 + b2 * a21 + b3 * a31;
    out[10] = b0 * a02 + b1 * a12 + b2 * a22 + b3 * a32;
    out[11] = b0 * a03 + b1 * a13 + b2 * a23 + b3 * a33;
    (b0 = b[12]), (b1 = b[13]), (b2 = b[14]), (b3 = b[15]);
    out[12] = b0 * a00 + b1 * a10 + b2 * a20 + b3 * a30;
    out[13] = b0 * a01 + b1 * a11 + b2 * a21 + b3 * a31;
    out[14] = b0 * a02 + b1 * a12 + b2 * a22 + b3 * a32;
    out[15] = b0 * a03 + b1 * a13 + b2 * a23 + b3 * a33;
    return out;
  }

  _rotate(out, sin, cos, axis, ox, oy, oz) {
    let x = axis[0],
      y = axis[1],
      z = axis[2];
    let len = Math.hypot(x, y, z);
    let s, c, t;
    let a00, a01, a02, a03;
    let a10, a11, a12, a13;
    let a20, a21, a22, a23;
    let b00, b01, b02;
    let b10, b11, b12;
    let b20, b21, b22;
    if (len < 0.000001) {
      return null;
    }
    len = 1 / len;
    x *= len;
    y *= len;
    z *= len;
    s = sin;
    c = cos;
    t = 1 - c;
    a00 = 1;
    a01 = 0;
    a02 = 0;
    a03 = 0;
    a10 = 0;
    a11 = 1;
    a12 = 0;
    a13 = 0;
    a20 = 0;
    a21 = 0;
    a22 = 1;
    a23 = 0;
    // Construct the elements of the rotation matrix
    b00 = x * x * t + c;
    b01 = y * x * t + z * s;
    b02 = z * x * t - y * s;
    b10 = x * y * t - z * s;
    b11 = y * y * t + c;
    b12 = z * y * t + x * s;
    b20 = x * z * t + y * s;
    b21 = y * z * t - x * s;
    b22 = z * z * t + c;
    // Perform rotation-specific matrix multiplication
    out[0] = a00 * b00 + a10 * b01 + a20 * b02;
    out[1] = a01 * b00 + a11 * b01 + a21 * b02;
    out[2] = a02 * b00 + a12 * b01 + a22 * b02;
    out[3] = a03 * b00 + a13 * b01 + a23 * b02;
    out[4] = a00 * b10 + a10 * b11 + a20 * b12;
    out[5] = a01 * b10 + a11 * b11 + a21 * b12;
    out[6] = a02 * b10 + a12 * b11 + a22 * b12;
    out[7] = a03 * b10 + a13 * b11 + a23 * b12;
    out[8] = a00 * b20 + a10 * b21 + a20 * b22;
    out[9] = a01 * b20 + a11 * b21 + a21 * b22;
    out[10] = a02 * b20 + a12 * b21 + a22 * b22;
    out[11] = a03 * b20 + a13 * b21 + a23 * b22;
    out[12] = ox;
    out[13] = oy;
    out[14] = oz;
    out[15] = 1;
    return out;
  }
}

export default RouteLayer;
