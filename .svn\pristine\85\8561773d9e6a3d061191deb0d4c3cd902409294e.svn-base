<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>坐标拾取示例</title>

    <link
      rel="stylesheet"
      href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <script src="./index.js" type="module"></script>
    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        left: 50%;
        width: 50%;
        height: 200px;
        display: flex;
      }

      .tools span {
        cursor: pointer;
        background-color: blue;
        width: 150px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        color: white;
      }

      .description {
        position: absolute;
        right: 10px;
        top: 10px;
        background-color: white;
        border-radius: 5px;
        padding: 20px;
      }

      #coordinate {
        position: absolute;
        right: 20px;
        bottom: 20px;
        width: 350px;
        height: 50px;
        background-color: white;
        padding: 10px;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv"></div>
    <div id="coordinate">坐标（x,y）:</div>
    <div class="description">
        使用方法：
        view 加载好了后，通过方法ArcGisUtils.mapClickEventHandle.addCoordinateListener((point)=>{})
        订阅坐标信息
    </div>
  </body>
  <script>
    const clock = setInterval(function () {
      if (window?.view) {
        clearInterval(clock);
        ArcGisUtils.mapClickEventHandle.addCoordinateListener((point) => {
          const { x, y } = point;
          const coordinateDom = document.getElementById("coordinate");
          coordinateDom.innerHTML = `坐标（x,y）:(${x},${y})}`;
        });
      }
    }, 1000);
  </script>
</html>
