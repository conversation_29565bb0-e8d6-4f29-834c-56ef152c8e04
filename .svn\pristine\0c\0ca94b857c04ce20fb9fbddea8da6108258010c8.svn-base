/*
 * @Descripttion: 默认加载水波纹
 * @Author: <EMAIL>
 * @Date: 2022-12-11 22:24:58
 */
/**
 * 默认添加水波纹效果
 * @param { color} color 波纹颜色 可选参数 默认值：'#039962'
 * @param {'calm' | 'rippled' | 'slight' | 'moderate'} waveStrength 水波纹强度(平静的、扩散的、轻微的、适度的)  可选参数  默认值：'moderate'（适度的）
 * @returns
 */

async function addWaterWaveEffect(
  color = "#039962",
  waveStrength = "moderate"
) {
  //   添加水波纹图层
  await ArcGisUtils.addWaterLayer();
  //    添加水波纹效果
  await ArcGisUtils.changeWaterLayer(color, waveStrength);
}

export default addWaterWaveEffect;
