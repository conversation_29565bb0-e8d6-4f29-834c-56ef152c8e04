/**
 * ccbar js api
 * date:2024-09-12
 * version:1.23
 */

/**
 * 自定义事件,提供addEvent,addEvents,emitEvent,removeEvent,removeEvents来实现简单的自定义事件监听和触发
 */
 var EventTarget = function() {
    this._listener = {};
};
EventTarget.prototype = {
    constructor: this,
    addEvent: function(type, fn) {
        if (typeof type === "string" && typeof fn === "function") {
            if (typeof this._listener[type] === "undefined") {
                this._listener[type] = [fn]
            } else {
                this._listener[type].push(fn)
            }
        }
        return this
    },
    addEvents: function(obj) {
        obj = typeof obj === "object" ? obj : {};
        var type;
        for (type in obj) {
            if (type && typeof obj[type] === "function") {
                this.addEvent(type, obj[type])
            }
        }
        return this
    },
    fireEvent: function(type) {
        if (type && this._listener[type]) {
            var events = {
                type: type,
                target: this
            };
            for (var length = this._listener[type].length, start = 0; start < length; start += 1) {
                var handlerArgs = Array.prototype.slice.call(arguments, 1);
                this._listener[type][start].call(this, events)
            }
        }
        return this
    },
    emitEvent: function(type) {
        var self = this;
        if (type && this._listener[type]) {
            var events = {
                type: type,
                target: this
            };
            for (var length = this._listener[type].length, start = 0; start < length; start += 1) {
                var handlerArgs = Array.prototype.slice.call(arguments, 1);
                try {
                    this._listener[type][start].apply(self, handlerArgs)
                } catch (e) {
                    console.error(e)
                }
            }
        }
        return this
    },
    fireEvents: function(array) {
        if (array instanceof Array) {
            for (var i = 0, length = array.length; i < length; i += 1) {
                this.fireEvent(array[i])
            }
        }
        return this
    },
    removeEvent: function(type, key) {
        var listeners = this._listener[type];
        if (listeners instanceof Array) {
            if (typeof key === "function") {
                for (var i = 0, length = listeners.length; i < length; i += 1) {
                    if (listeners[i] === key) {
                        listeners.splice(i, 1);
                        break
                    }
                }
            } else {
                if (key instanceof Array) {
                    for (var lis = 0, lenkey = key.length; lis < lenkey; lis += 1) {
                        this.removeEvent(type, key[lenkey])
                    }
                } else {
                    delete this._listener[type]
                }
            }
        }
        return this
    },
    removeEvents: function(params) {
        if (params instanceof Array) {
            for (var i = 0, length = params.length; i < length; i += 1) {
                this.removeEvent(params[i])
            }
        } else {
            if (typeof params === "object") {
                for (var type in params) {
                    this.removeEvent(type, params[type])
                }
            }
        }
        return this
    }
};
var ccbarEvent = new EventTarget();


/*重置ccbar状态*/
function ccbarResetState(data) {
    var funcMask = CallControl.getFunc();
    for (var i in funcMask) {
        funcMask[i] = false;
    }
    funcMask['logon'] = true;
    var resetStatus = {
        "workMode": "all",
        "stateDesc": "登出",
        "notifyContent": "",
        "state": "LOGOFF",
        "resultDesc": null,
        "funcMask": funcMask
    }
    var logoffNotisfy = $.extend({
        result: "Succ",
        resultCode: "000",
        resultDesc: "重置为签出状态",
        srcMessageId: "respLogout"
    },data)
    //关闭ws
    if (CallControl.ws.ws) CallControl.ws.ws.close()
    //关闭心跳
    var isLongPolling = false;
    clearLongPollingFlag = false;
    longPollAjax && longPollAjax.abort()
    longPollAjax = null;
    //主动触发事件
    ccbarEvent.emitEvent('logoff', logoffNotisfy)
    ccbarEvent.emitEvent('agentStateSync', resetStatus)

    CallControl.state = resetStatus.state;
    CallControl.workModeType = resetStatus.workMode;
    CallControl.funcMask = resetStatus.funcMask;
    CallControl.onRespAgentState(resetStatus);

    ccbarLongPollingProtect.stop();

    ccbarHeartbeat && CallControl.clearTimeout(ccbarHeartbeat);
}

/**
 * [ccbarRequestSend ccbar请求发送方法]
 * @param  {String} url             [请求路径]
 * @param  {Object} sendData        [发送的请求数据对象]
 * @param  {Function} successCallback [成功的回调函数]
 * @param  {Object} option          [额外的参数对象]
 */
function ccbarRequestSend(url, sendData, successCallback, option, noLongpolling) {
    var startTime = new Date().getTime();
    var reqHeader = {}
    if (CallControl.resultConfig.cuid) reqHeader.cuid = CallControl.resultConfig.cuid;
    var defaultOpts = {
        dataType: 'jsonp',
        jsonp: "callbackFunc",
        contentType: "application/x-www-form-urlencoded; charset=UTF-8",
        url: url,
        headers:reqHeader,
        data: sendData,
        timeout: 5000, //5秒超时
        success: function(result,r2,r3) {

            CallControl.lastRequestDelay = new Date().getTime() - startTime;
            ccbarEvent.emitEvent('ccbarHttpSucc', {
                time: new Date().getTime(),
                delay: CallControl.lastRequestDelay,
                onLine: true
            })

            try {
                if (successCallback && $.isFunction(successCallback)) {
                    successCallback(result,r2,r3)
                }


            } catch (e) {
                ccbarDebugger('ccbarRequestSend error', e)
            }
            if (!result.state || (result.data && result.data.code && (result.data.code == 'Fail' || (result.data.code != 'succ' && result.data.code != '000'&& result.data.code != '200')))) {
                ccbarDebugger('请求失败:' + result.data.content || result.msg);
                var msgTips = result.data.content || result.msg
                if (msgTips && msgTips != 'succ') tipsMsg(msgTips)
            }
            //重置
            if (result.state != 1 && result.data && result.data.code == '117') {
                ccbarResetState && ccbarResetState()
                var msg = result.data.content || '[117]坐席已签出'
                ccbar_popup && ccbar_popup.alert(msg)
            }

            if (result.data && (result.data.resultCode == '403' || result.data.code == '403')) {
                ccbarResetState && ccbarResetState()
                var msg = result.data.content || '[403]坐席已签出'
                ccbar_popup && ccbar_popup.alert(msg)
            }
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            CallControl.lastRequestDelay = -1;

            ccbarEvent.emitEvent('ccbarHttpFail', {
                time: new Date().getTime(),
                delay: new Date().getTime() - startTime,
                onLine: false,
                textStatus: textStatus,
                url: url
            })

            if (typeof(layer) != 'undefined') {
                layer.closeAll('loading');
            } else {
                $("#ccbar .ccbar-mask").fadeOut()
            }
            var errorText = '';
            if (textStatus === 'timeout') {
                errorText = '请求超时，请求检查网络是否可达！';
                if (sendData && sendData.cmdJson && JSON.parse(sendData.cmdJson).messageId) {
                    errorText = '[' + JSON.parse(sendData.cmdJson).messageId + ']请求超时，请求检查网络是否可达！'
                }
            } else if (XMLHttpRequest.status === 0) {
                errorText = '失败，请检查网络是否可达！'
                console.error(errorText);
            } else if (XMLHttpRequest.status == 404) {
                errorText = '链接不存在!';
            } else if (XMLHttpRequest.status == 500) {
                errorText = '功能异常[error:500],请与管理员联系!';
            } else if (textStatus === 'parsererror') {
                errorText = '请求异常,数据格式有误！';
            } else if (textStatus === 'abort') {
                errorText = url + '请求中止!';
            } else {
                errorText = XMLHttpRequest.responseText;
            }
            var errType = 'request ' + textStatus;
            try {
                if (textStatus != 'parsererror') tipsMsg('发送ccbar请求' + errorText);
            } catch (e) {
                ccbarDebugger("ccbarRequestSend", e);
            }
            var errorMsg = 'ccbarRequestSend ' + errType + ',textStatus=' + textStatus + ',status=' + XMLHttpRequest.status + ',readyState=' + XMLHttpRequest.readyState + ',url=' + url
            CallControl.log(errorMsg);


        },
        complete: function() {
            try {
                CallControl.setting.complete && CallControl.setting.complete();
                if (typeof(layer) != 'undefined') {
                    layer.closeAll('loading');
                } else {
                    $("#ccbar .ccbar-mask").fadeOut()
                }
            } catch (e) {

            }
        },
        beforeSend: function() {
            try {
                CallControl.setting.beforeSend && CallControl.setting.beforeSend();
            } catch (e) {}
        }
    };
    var finalOpts = $.extend({}, defaultOpts, $.isPlainObject(option) && option);
    $.ajax(finalOpts);
}

function jsonpCcbarCallback(result) {}
var _ccbarDebug = false;

// function ccbarDebugger() {
//     if (CallControl.getConfig().debug) {
//         ccbarDebugger('[ccbarLog]'+new Date().format('yyyy-MM-dd hh:mm:ss')+'   ==>',arguments)
//     }
// }
function ccbarDebugger() {
    if (CallControl.getConfig().debug) {
        // 使用 console.log 代替递归调用
        const timestamp = new Date().format('yyyy-MM-dd hh:mm:ss');
        const logPrefix = '[ccbarLog]' + timestamp + '   ==> ';
        // 处理 arguments，避免直接传递类数组对象
        const args = Array.from(arguments).map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg) : arg
        );
        
        console.log(logPrefix, ...args);
    }
}
var ccbar_config = {
    command: {
        'heartbeat': 'cmdHeartBeat', //心跳
        'logon': 'cmdLogin', //签入
        'logoff': 'cmdLogout', //签出
        'agentNotReady': 'cmdNotReady', //忙
        'agentReady': 'cmdReady', //闲
        'workNotReady': 'cmdWorkNotReady', //话后整理
        'workReady': 'cmdWorkReady', //完成话后整理
        'makeCall': 'cmdMakeCall', //外呼
        'answerCall': 'cmdAnswerCall', //应答
        'clearCall': 'cmdClearCall', //挂机
        'holdCall': 'cmdHoldCall', //保持
        'unholdCall': 'cmdRetrieveCall', //恢复
        'sstransfer': 'cmdTransferCall', //转移
        'consultation': 'cmdConsultCall', //咨询
        'conference': 'cmdConferenceCall', //三方通话
        'listenCall': 'cmdMonitorCall', //监听
        'startMonitor': 'cmdStartMonitorCall', //开始监听
        'stopMonitor': 'cmdStopMonitorCall', //结束监听
        'skillGroupInfo': 'cmdSkillGroupInfo', //技能组信息
        'startInvent': 'cmdInterventCall', //强插
        'stopInvent': 'cmdStopInterventCall', //结束强插
        'changeAgentStatus': 'cmdChangeAgentStatus', //改变坐席状态
        'consultIVR': 'cmdConsultIVR', //转IVR
        'getAgentInfo': 'cmdGetAgentInfo'
    },
    agentStatus: {
        'IDLE': {
            name: '空闲'
        },
        'ALERTING': {
            name: '振铃'
        },
        'TALK': {
            name: '通话中'
        },
        'BUSY': {
            name: '繁忙'
        },
        'LOGOFF': {
            name: '登出'
        },
        'OCCUPY': {
            name: '预占'
        },
        'WORKNOTREADY': {
            name: '话后处理中'
        },
        'HELD': {
            name: '保持'
        },
        'CONFERENCED': {
            name: '三方'
        },
        'CONSULTED': {
            name: '咨询'
        },
        'MONITORED': {
            name: '监听'
        },
        'SERVICEOFFLINE':{name:'网络异常'},
        'NETWORKOFFLINE':{name:'网络中断'}
    },
    message: {
        'sessionTimeout': '用户登录超时'
    },
    createCause: {
        '1': '客户来电(外线呼入)',
        '2': '客户来电(IVR转入)',
        '3': '席间转移呼入',
        '4': 'IVR咨询呼入',
        '5': '席间咨询呼入',
        '6': '呼出',
        '8': '预拨号呼出',
        '9': '呼叫前转呼入',
        '10': '转移呼入',
        '14': '席间呼入',
        '29': '席间呼出',
    },
    clearCause: {
        '0': '成功',
        '1': '无人应答',
        '2': '用户忙',
        '3': '用户挂机',
        '4': '网络忙',
        '5': '空号',
        '6': '用户拒绝',
        '7': '关机',
        '8': '暂停服务',
        '9': '不在服务器',
        '10': '传真机',
        '11': '用户欠费',
        '12': '重复号码',
        '13': '电话总机',
        '14': '久叫不应',
        '15': '话机异常',
        '50': '回铃音反馈,关机',
        '51': '空号',
        '52': '停机',
        '53': '用户拒接',
        '54': '用户忙',
        '55': '不在服务区',
        '56': '无应答',
        '98': '坐席挂断',
        '99': '系统错误',
        '100': '其他错误',
        '111': '三方会议失败',
        '112': '转移操作失败',
        '113': '话机状态异常',
        '114': '外呼操作失败',
        '115': '无效的企业',
        '116': '外呼时坐席已签出',
        '117': '坐席已签出',
        '120': '坐席忙',
        '999': '未定义错误'
    },
    error: {
        '000': '操作成功',
        '001': '包文格式错误',
        '002': '无效的操作请求',
        '100': '无效的技能组',
        '101': '无效的坐席工号',
        '102': '无效的坐席密码',
        '103': '无效的坐席状态',
        '104': '无效的呼叫状态',
        '105': '坐席工号已登陆',
        '106': '话机已被使用',
        '107': '外呼主显号码为空',
        '108': '无效的话机号码',
        '109': '未配置席间转移字冠',
        '110': '咨询操作失败',
        '111': '三方会议失败',
        '112': '转移操作失败',
        '113': '话机状态异常',
        '114': '外呼操作失败',
        '115': '无效的企业',
        '116': '外呼时坐席已签出',
        '117': '坐席已签出',
        '118': '坐席阻塞中',
        '119': '话机离线，请检查话机状态。',
        '125':"坐席振铃超时",

        '120': '超出平台最大签入坐席数限制',
        '122': '呼叫已存在其他坐席队列中',
        '999': '未定义错误'
    }

}

String.prototype.startWith = function(str) {
    var reg = new RegExp("^" + str);
    return reg.test(this);
}

String.prototype.endWith = function(str) {
    var reg = new RegExp(str + "$");
    return reg.test(this);
}

function cmdJson(data) {
    if (CallControl.resultConfig.cuid || CallControl.cuid) data.cuid = CallControl.resultConfig.cuid || CallControl.cuid
    //if (!data.agentId && CallControl.resultConfig.agentId) data.agentId = CallControl.resultConfig.agentId
    return {
        cmdJson: JSON.stringify(data)
    };
}

function objectAssign(originObj, extendObj) {
    return $.extend({}, originObj, extendObj)
}

var _longpollingTime = null;
var longPollingError = 0;
var isLongPolling = false;
var clearLongPollingFlag = false;
var longPollAjax = null;
var ccbarHeartbeat = null;
var timeoutLongpolling = null;
var ccbarCache = {}
var CallControl = (function() {
    var agentCache = {
        callEvent: {},
        agentState: {},
        notify: {},
        monitor: {},
        callerList: [],
        skillGroupList: []
    }
    var config = {
        debug: true, //是否输出日志
        url: '', //服务器地址
        entId: '', //企业id
        productId: '', //产品id
        loginKey: '', //秘钥
        agentId: '', //坐席工号
        password: '', //密码
        token: '', //md5加密的密码,优先级比password高
        dn: '', //话机
        readyMode: '', //签入后忙还是闲
        workMode: '', //工作模式
        autoAnswer: false, //自动应答
        event: {},
        layout: true,
        websocket: true,
        retryCount:10
    }
    var lastMakeCall = null;

    return {
        myIp: "",
        myPort: 0,
        myAgentId: "",
        myPhoneNum: "",
        sbcAddr: '',
        callers: [],
        phoneType: '',
        isSDK: false,
        isMultiMedia: false,
        funcMask: null,
        state: null,
        workModeType: null,
        isLogoned: false,
        agentLogin: null,
        ccbarMode: null,
        voiceAgent: 'on',
        multiMediaAgent: 'on',
        inbound: "on",
        outbound: "on",
        softMute:false, //话机静音
        agentState: {},
        setting: {
            beforeSend: null,
            complate: null
        },
        resultConfig:{},
        config: {
            agentPhones: [],
            websocket: true,
            ccbarMode: null,
            voiceAgent: null,
            multiMediaAgent: null,
            agentId: ''
        },
        updateConfig: function(options) {
            config = objectAssign(config, options)
        },
        getConfig: function() {
            return config;
        },
        getEventCache: function() {
            return agentCache;
        },
        getContextPath: function() {
            var path = config.url || '';
            return path;
        },
        ccbarInit: function(callback, options) {
            var defaultOpts = {
                serverIp: location.hostname || '',
                serverPort: location.port || '',
                entId: '',
                productId: '',
                loginKey: '',
                host: ''
            };
            var data = $.extend({}, defaultOpts, $.isPlainObject(options) && options);
            ccbarRequestSend(this.getContextPath() + '/yc-ccbar/AgentEvent?action=ccbarInit', cmdJson(data), function(result) {
                if (result.state == '1') {
                    CallControl.ccbarMode = result.data.ccbarMode;
                    CallControl.agentLogin = result.data.agentLogin;
                    CallControl.voiceAgent = result.data.voiceAgent;
                    CallControl.multiMediaAgent = result.data.multiMediaAgent;
                    CallControl.config = $.extend({}, CallControl.config, $.isPlainObject(result.data) && result.data)
                }
                if ($.isFunction(callback)) {
                    callback(result);
                }

            }, {
                jsonpCallback: "jsonpCallbackInit"
            }, true);
        },
        /*统一签入入口*/
        logonSSO: function(logonFunc, httpFirst) {
            /*签入前如果是ws需要先建立链接*/
            if (CallControl.isWebsoket()) {
                if (CallControl.ws.ws != null && CallControl.ws.ws.readyState === WebSocket.OPEN) {
                    ccbarDebugger('WebSocket opened,agent logon');
                    logonFunc && logonFunc();
                } else {
                    if (httpFirst) {
                        logonFunc && logonFunc(httpFirst);
                        return;
                    }
                    CallControl.ws.connect({
                        onopen: function() {
                            logonFunc && logonFunc();
                        }
                    });
                }
            } else {
                logonFunc && logonFunc();
            }
        },
        /**
         * [logon 签入]
         * 
         */
        logon: function(phoneNum, callback, option) {
            var data = {
                phoneNum: phoneNum,
                'multiMediaSwitch': 'off',
                'voiceSwitch': 'on',
                autoAnswer: config.autoAnswer,
                readyMode: config.readyMode
            };;
            data = $.extend({}, data, $.isPlainObject(option) && option);
            CallControl.logonSSO(function(httpFirst) {
                ccbarRequestSend(CallControl.getContextPath() + '/yc-ccbar/AgentEvent?action=login', cmdJson(data), function(result) {
                    if ($.isFunction(callback)) {
                        callback(result);
                    }

                    //建立ws
                    if (httpFirst && result.data.code == 'succ' || result.data.resultCode == '000' || result.data.resultCode == '105') {

                        /*ccbarEvent.emitEvent('logon', {
                            result: "Succ",
                            resultCode: "000",
                            resultDesc: "账号鉴权成功",
                            srcMessageId: "respLogin",
                            type:'loginFirst'
                        })*/
                        ccbarCache = result.data.result;
                        CallControl.cuid = result.data.result.cuid

                    }

                    if (result.data.code == 'succ') {
                        ccbarCache = result.data.result;

                        CallControl.agentId = result.data.result.agentId
                        CallControl.cuid = result.data.result.cuid

                        CallControl.resultConfig = result.data.result || {};
                        CallControl.sbcAddr = result.data.result.sbcAddr ? result.data.result.sbcAddr : '';
                        CallControl.phoneType = result.data.result.phoneType ? result.data.result.phoneType : 1;
                        CallControl.inbound = result.data.result.inbound;
                        CallControl.outbound = result.data.result.outbound;
                        CallControl.longPolling();

                        // 当是webrtc话机的时候,拉起webrtc
                        if (CallControl.phoneType == 5) {
                            // var data = {
                            //     url: result.data.result.webrtc_url,
                            //     sender: result.data.result.webrtc_sender || 'YUNQU_MOBILEAGENT',
                            //     deskey: result.data.result.webrtc_deskey,
                            //     desPassword: result.data.result.webrtc_desPassword,
                            //     account: result.data.result.webrtc_account,
                            //     password: result.data.result.webrtc_password
                            // }
                            var tempHost = CallControl.getContextPath();
                            var wshost = tempHost.replace('http', 'ws');
                            var webrtc_url = wshost + "/airwebrtc/webrtc.ws"
                            var phoneNum = result.data.result.phoneNum
                            ccbarDebugger("aaaa:" + webrtc_url)
                            var data = {
                                url: webrtc_url,
                                cuid: CallControl.cuid,
                                phoneNum: phoneNum
                            }
                            CallControl.webrtcConfig = data;
                            CallControl.webrtc.wakeupSIP(data, 'webrtc')
                        }
                    } else {
                        ccbarDebugger("签入失败:" + result.data.content)
                        if (CallControl.isWebsoket()) CallControl.ws.disconnect()
                    }
                }, {
                    jsonpCallback: "jsonpCallbackLogon"
                });
            }, true);

        },
        /**
         * [sdk用签入接口]
         * @param  {String}   entId     [企业ID]
         * @param  {String}   loginKey  [登陆密钥，32位长度]
         * @param  {String}   productId [企业订购的产品，由云呼平台提供]
         * @param  {String}   agentId [账号]
         * @param  {String}   token  [密码-MD5]
         * @param  {String}   phone     [话机号]
         * @param  {Function} callback  [回调函数]
         */
        agentLogon: function(entId, loginKey, productId, agentId, token, phone, callback, dataObj) { //坐席签入事件，这个在ccbar单独应用场景中使用
            //执行回调
            var data = {};
            var config = CallControl.getConfig();
            agentId = agentId || config.agentId;
            token = token || config.token;
            phone = phone || config.dn;
            var baseObj = {
                'agentId': agentId,
                'token': token,
                'phoneNum': phone || config.dn,
                'event': 'agentlogon',
                'multiMediaSwitch': 'off',
                'voiceSwitch': 'on',
                'autoAnswer': config.autoAnswer,
                'readyMode': config.readyMode,
                'workMode': config.workMode,
                'entId': config.entId,
                'loginKey': config.loginKey,
                'productId': config.productId,
                'skillId': config.skillId,

            };
            data = $.extend({}, data, baseObj, $.isPlainObject(dataObj) && dataObj);
            //强制签入方式
            if(config.reset == 1) data.reset = 1;
            var sendData = cmdJson(data)

            CallControl.beforeAgentLogon(agentId, token, phone, function(result) {
                if (result.state == 1 && result.data && result.data.code == 'succ') {
                    CallControl.config.websocket = result.data.result.websocket;
                    CallControl.logonSSO(function(httpFirst) {
                        ccbarRequestSend(CallControl.getContextPath() + '/yc-ccbar/AgentEvent?action=SDKLogin', sendData, function(result) {
                            if ($.isFunction(callback)) {
                                callback(result);
                            }

                            if (result.state == 1 && result.data && result.data.code == 'succ') {
                                CallControl.resultConfig = result.data.result || {};

                                if (!CallControl.isWebsoket()) {
                                    CallControl.cuid = result.data.result.cuid
                                    CallControl.longPolling('logon');
                                }


                                ccbarCache = result.data.result || {};
                                CallControl.sbcAddr = result.data.result.sbcAddr;
                                CallControl.phoneType = result.data.result.phoneType ? result.data.result.phoneType : 1;
                                CallControl.inbound = result.data.result.inbound;
                                CallControl.outbound = result.data.result.outbound;

                                // 当是webrtc话机的时候,拉起webrtc
                                if (CallControl.phoneType == 5) {
                                    // var data = {
                                    //     url: result.data.result.webrtc_url,
                                    //     sender: result.data.result.webrtc_sender || 'YUNQU_MOBILEAGENT',
                                    //     deskey: result.data.result.webrtc_deskey,
                                    //     desPassword: result.data.result.webrtc_desPassword,
                                    //     account: result.data.result.webrtc_account,
                                    //     password: result.data.result.webrtc_password
                                    // }
                                    var tempHost = CallControl.getContextPath();
                                    var wshost = tempHost.replace('http', 'ws');
                                    var webrtc_url = wshost + "/airwebrtc/webrtc.ws"
                                    var phoneNum = result.data.result.phoneNum
                                    var data = {
                                        url: webrtc_url,
                                        cuid: result.data.result.cuid,
                                        phoneNum: phoneNum
                                    }
                                    CallControl.webrtcConfig = data;
                                    try{
                                        CallControl.webrtc.wakeupSIP(data, true)
                                    }catch(e){
                                        console.error(e)
                                    }
                                }

                                //建立ws
                                if (httpFirst && result.data.code == 'succ' || result.data.resultCode == '000' || result.data.resultCode == '105') {
                                    ccbarEvent.emitEvent('logon', {
                                        result: "Succ",
                                        resultCode: "000",
                                        resultDesc: "账号鉴权成功",
                                        srcMessageId: "respLogin"
                                    })
                             
                                        CallControl.ws.connect({
                                            onopen: function () {

                                            }
                                        })
                                    

                                }
                            } else {
                                ccbarDebugger("签入失败:" + result.data.content)
                                if (CallControl.isWebsoket()) CallControl.ws.disconnect()

                                ccbarEvent.emitEvent('logon', {
                                    result: "fail",
                                    resultCode: result.msg == '[403]当前坐席工号已被签入' ? '403' : result.data.code,
                                    resultDesc: result.msg,
                                    srcMessageId: "respLogin"
                                })

                            }

                        }, {
                            jsonpCallback: 'jsoncallbackSDKlogon'
                        });
                    }, true);
                } else {
                    callback && callback(result);
                    ccbarEvent.emitEvent('logon', {
                        result: "fail",
                        resultCode: result.msg == '[403]当前坐席工号已被签入' ? '403' : result.data.code,
                        resultDesc: result.msg,
                        srcMessageId: "respLogin"
                    })
                }

            });


        },


        beforeAgentLogon: function(agentId, token, phone, callback, dataObj) {
            var config = CallControl.getConfig();
            var data = {
                "entId": config.entId,
                "loginKey": config.loginKey,
                "productId": config.productId,
                "agentId": agentId,
                "token": token,
                "phoneNum": phone,
                "event": "beforeAgentLogon"
            };
            var switchObj = {
                'multiMediaSwitch': 'off',
                'voiceSwitch': 'on'
            };
            data = $.extend({}, data, switchObj, $.isPlainObject(dataObj) && dataObj);
            if(config.reset == 1) data.reset = 1;
            $.ajax({
                dataType: 'jsonp',
                jsonp: "callbackFunc",
                jsonpCallback: "jsonpCallbackBeforeLogin",
                contentType: "application/x-www-form-urlencoded; charset=UTF-8",
                url: this.getContextPath() + "/yc-ccbar/AgentEvent?action=AgentLogin",
                data: cmdJson(data),
                timeout: 5000,
                success: function(result) {
                   
                    callback && callback(result);
                    if(result.data.resultCode != 'succ' && result.data.resultCode != '000' && result.data.resultCode != '200'){
                        ccbarEvent.emitEvent('notifyError', {
                            resultCode:result.data.resultCode,
                            resultText:result.data.content || result.msg,
                            srcMessageId:'agentLogin'
                        });

                        ccbarEvent.emitEvent('logon', result);
                    }
                }
            });
        },

        longPolling: function(reload) {
            /*websocket方式*/
            if (CallControl.isWebsoket() ||  CallControl.getConfig().webSocket) {
                if (CallControl.ws.ws == null || (CallControl.ws.ws.readyState != WebSocket.OPEN && CallControl.ws.ws.readyState != WebSocket.CONNECTING)) {
                    CallControl.ws.connect();
                    return;
                }
                /*http方式*/
            } else {
                reload = reload || false;
                var data = {
                    reload: reload,
                   cuid: CallControl.cuid,

                };

                if (isLongPolling || longPollAjax != null) {
                    return false
                } else {
                    isLongPolling = true;
                    clearLongPollingFlag = false;
                    var startTime = new Date().getTime()

                    var reqHeader = {}
                    if (CallControl.resultConfig.cuid) reqHeader.cuid = CallControl.resultConfig.cuid;
                    
                    longPollAjax = $.ajax({
                        dataType: 'jsonp',
                        jsonp: "callbackFunc",
                        // jsonpCallback:"jsonpCallbackLongPolling",
                        contentType: "application/x-www-form-urlencoded; charset=UTF-8",
                        url: this.getContextPath() + "/yc-ccbar/AgentEvent?action=LongPolling",
                        data: cmdJson(data),
                        headers:reqHeader,
                        timeout: 13000, //13秒超时再重连
                        error: function(XMLHttpRequest, textStatus, errorThrown) {
                            longPollAjax = null;

                            CallControl.handelLongPollingError(XMLHttpRequest, textStatus, errorThrown);
                            var logData = {
                               time: new Date().format('yyyy-MM-dd hh:mm:sss'),
                                delay: new Date().getTime() - startTime,
                                onLine: false,
                                readyState:XMLHttpRequest.readyState,
                                textStatus: textStatus
                            }
                            ccbarEvent.emitEvent('ccbarHttpLongpollingFail', logData)
                            logData.errorThrown = errorThrown
                            CallControl.logger && CallControl.logger.send('longPollingError',logData)
                        },
                        success: function(result) {
                        
                            longPollAjax = null;
                            var longpollingSuccFlag = CallControl.handelLongPollingSuccess(result)

                            ccbarEvent.emitEvent('ccbarHttpLongpollingSucc', {
                                time: new Date().format('yyyy-MM-dd hh:mm:sss'),
                                delay: new Date().getTime() - startTime,
                                onLine: true
                            })

                        },
                        complete: function(event) {

                            //超过1分钟没收到长连接,提示
                            var time = new Date();
                            if (_longpollingTime != null && (time - _longpollingTime) > (60 * 1000)) {
                                CallControl.longPollingTimeout && CallControl.longPollingTimeout();
                                return;
                            }
                            var readyState = event.readyState;
                            var eventMap = {
                                '0': 'UNSEND',
                                '1': 'OPENED',
                                '2': 'HEADERS_RECEIVED',
                                '3': 'LOADING',
                                '4': 'DONE'
                            }
                            var eventName = eventMap[readyState] || '未定义';
                            if (CallControl.isLogoned && readyState == '4') { //请求完成
                                if (!clearLongPollingFlag) CallControl.longPolling();
                            } else if (CallControl.isLogoned && readyState == '0') { //异常

                                CallControl.ccbarLongPollingRetry(true);
                            }
                        }
                    });
                }

            }
        },
        ccbarLongPollingRetry: function() {
            //计算重连时间
            var reSendTime = 500;
            if (longPollingError > 5 && longPollingError < 10) {
                reSendTime = 1500
            } else if (longPollingError > 10) {
                reSendTime = 3000
            }
            timeoutLongpolling && clearTimeout(timeoutLongpolling)
            timeoutLongpolling = CallControl.setTimeout(function() {
                debounce(CallControl.longPolling(true), reSendTime)
            }, reSendTime); // 递归调用

        },
        handelLongPollingError: function(XMLHttpRequest, textStatus, errorThrown) {
            if (XMLHttpRequest.status == 200) {
                return
            }
            var errType = 'request ' + textStatus;
            try {
                tipsMsg(ccbarI18nText('网络异常') + errType + ',error code:' + XMLHttpRequest.readyState + '--' + XMLHttpRequest.status + '--' + textStatus,8000);
            } catch (e) {
                ccbarDebugger(e);
            }
            isLongPolling = false;
            ++longPollingError;
            var errorMsg = 'longPolling ' + errType + ',textStatus=' + textStatus + ',status=' + XMLHttpRequest.status + ',readyState=' + XMLHttpRequest.readyState
            CallControl.log(errorMsg);

            if (textStatus == "timeout") { // 请求超时
                ccbarDebugger('timeout new longPolling,count:' + longPollingError);
            } else {
                ccbarDebugger('error new longPolling,count:' + longPollingError);
            }
            timeoutLongpolling && clearTimeout(timeoutLongpolling)

            timeoutLongpolling = setTimeout(function(){
                CallControl.ccbarLongPollingRetry(true);
            },2000)
            

        },
        handelLongPollingSuccess: function(result,isWs) {
            if (!result.state || (result.data && result.data.code == 'Fail')) {
                var msgTips = result.data && result.data.content || result.msg
                if (msgTips) tipsMsg(msgTips)
            }
            var time = new Date().getTime();
            _longpollingTime = time;

            if (!CallControl.isWebsoket() && !CallControl.getConfig().webSocket) { //http请求
                longPollingError = 0;
                clearLongPollingFlag = false;

                if(result.state !=1){
                    ccbar_popup && ccbar_popup.alert(result.msg);
                    ccbarResetState()
                    clearLongPollingFlag = true;
                    longPollAjax = null;
                    isLongPolling = false;
                    return;
                }

                try {
                    if (result.data.code == 'succ') {
                        try {
                            var results = result.data.events;
                            for (var i = 0; i < results.length; i++) {
                                if (results[i].agentId) CallControl.agentId = results[i].agentId;
                                try {
                                    handel(results[i])
                                } catch (e) {
                                    ccbarDebugger('ccbar handel error')
                                    ccbarDebugger(e)
                                }
                            }
                            if (clearLongPollingFlag) {
                                longPollAjax = null;
                                isLongPolling = false;
                                ccbarDebugger('取消longpolling');
                                return false;
                            }
                        } catch (e) {
                            CallControl.log('longpolling callback error in succ,' + String(e));
                            ccbarDebugger(e);
                        }
                    } else if (result.data.code == 'sessionTimeout' || result.data.code == '403') {
                        try {
                            ccbarDebugger('sessionTimeout');
                            longPollAjax = null;
                            isLongPolling = false;
                            ccbarResetState()
                            ccbar_popup && ccbar_popup.alert(result.msg)
                            try {
                                CallControl.onEventSessionTimeout(result.data)
                            } catch (e) {}
                            return false;
                        } catch (e) {
                            CallControl.log('longpolling callback error in sessionTimeout,' + String(e));
                            ccbarDebugger(e);
                        }
                    } else if (result.data.code == 'fail' || result.msg == '当前坐席工号已在其它电脑签入!' || result.errorCode == 403) {
                        longPollAjax = null;
                        isLongPolling = false;

                        try {
                            if (result.data.resultCode == '403' || result.msg == '当前坐席工号已在其它电脑签入!') {
                                clearLongPollingFlag = true;
                                longPollAjax = null;
                                isLongPolling = false;
                                ccbarDebugger('sessionTimeout');
                                try {
                                CallControl.onEventSessionTimeout(result.data)
                            } catch (e) {}
                                ccbarResetState()
                                return false;
                            }
                        } catch (e) {
                            CallControl.log('longpolling callback error in fail,' + String(e));
                            ccbarDebugger(e);
                        }
                    }
                    //ccbarDebugger('success new longPolling');
                    isLongPolling = false;
                    CallControl.longPolling(); //重新发起
                } catch (e) {
                    //ccbarDebugger('success new longPolling from catch');
                    ccbarDebugger(e);
                    isLongPolling = false;
                    CallControl.longPolling(); //重新发起
                }
            } else { //websocket
                try{
                    handel(result)
                }catch(e){
                    console.error(e)
                }
            }

            /*事件处理*/
            function handel(resultData) {
                var message = resultData;
                if (message == null) {
                    return;
                }
                if (message.agentId) CallControl.agentId = message.agentId
                //非监控信息发送确认消息
                if (message.messageId && message.messageId != 'monitor' && message.sequence) {
                    ccbarErrorSubmit.addConfirm(message.sequence)
                }
                switch (message.messageId) {
                    /*坐席状态同步*/
                    case 'agentStateSync':

                        //话机离线
                        if(message.cmddata.notifyContent == '119'){
                            ccbarEvent.emitEvent('notifyError', {
                                resultCode:"119",
                                resultText: '话机离线，请检查话机状态。',
                                srcMessageId:'agentStateSync'
                            });
                        }else if(message.cmddata.notifyContent == '125'){
                            ccbar_popup && ccbar_popup.alert('坐席振铃超时')
                            ccbarEvent.emitEvent('notifyError', {
                                resultCode:"125",
                                resultText:ccbarI18nText('坐席振铃超时'),
                                srcMessageId:'agentStateSync'
                            });
                        }

                        //发送状态变更事件
                        var state = message.cmddata.state; //坐席状态

                        if (state == 'WORKNOTREADY' && CallControl.state == state) {//同状态不推送
                            break
                        }
                        CallControl.state = state;
                        // 兼容中移 区分咨询中的转移和普通的转移
                        if(state=='CONSULTED'){
                            message.cmddata.funcMask.transfercall = true;
                            message.cmddata.funcMask.sstransfer = false;
                        }
                        if(state=='TALK'){
                            message.cmddata.funcMask.dtmf = true;
                        }else{
                            message.cmddata.funcMask.dtmf = false;
                        }

                        if(state == 'INTERVENTED' || state == 'SECRETLYTALK' || state == 'MONITORED' ){//强插状态或者私语状态
                            message.cmddata.funcMask.clearcall = true;
                        }

                        //设置功能黑名单,在对应状态下不可用
                        if(CallControl.getConfig().stateBan && CallControl.getConfig().stateBan[state]){
                            for (var fKey = 0; fKey < CallControl.getConfig().stateBan[state].length; fKey++) {
                                message.cmddata.funcMask[CallControl.getConfig().stateBan[state][fKey]] = false;
                            }
                        }

                        agentCache.agentState = message.cmddata;

                        CallControl.workModeType = message.cmddata.workMode;
                        CallControl.funcMask = message.cmddata.funcMask;
                        CallControl.onRespAgentState(message.cmddata);
                        ccbarEvent.emitEvent('agentStateSync', message.cmddata);
                        ccbarEvent.emitEvent('agentStateChange', message);

                        

                        //登出
                        if (state == "LOGOUT" || state == "LOGOFF") {
                            CallControl.isLogoned == false;
                            _longpollingTime = null

                        }
                        break;
                        /*呼叫状态*/
                    case 'callEventSync':
                       // ccbarDebugger("发送呼叫状态.");
                        CallControl.onRespCallEvent(message.cmddata);
                        ccbarEvent.emitEvent('callEventSync', message.cmddata);
                        ccbarEvent.emitEvent(message.cmddata.callEventId,message.cmddata);//触发呼叫事件监听
                        agentCache.callEventSync = message.cmddata;

                        // 呼叫结束时,如果收到resetPhone重新拉起,
                        if(message.cmddata && message.cmddata.callEventId == 'evtDisConnected' &&  message.cmddata.event && message.cmddata.event.resetPhone=='1'){
                            CallControl.wakeupSIP();
                            CallControl.softMute=false
                        }
                        break;
                        /*通知*/
                    case 'monitor':
                        var data = message.cmddata || message.cmdata || {};
                        ccbarEvent.emitEvent('monitor', data);
                        agentCache.monitor = message;
                        if (message.phoneStatus == 'offline') tipsMsg && tipsMsg('话机离线，请检查话机状态。', 8000)
                        //20秒内没发起过就主动发起心跳
                        if(CallControl.isWebsoket()) CallControl.ws.checkHeartbeat()

                        break;
                     case 'asrEvent':
                        var data = message || {};
                        ccbarEvent.emitEvent('asrEvent', data);
                        break;

                    case 'dcDataEvent':
                        var data = message || {};
                        ccbarEvent.emitEvent('dcDataEvent', data);
                        break;


                    case 'notify':
                        ccbarEvent.emitEvent('notify', message.cmddata);

                        CallControl.onRespNotify(message.cmddata);
                        var respFuncList = {
                            respLogin: 'logon',
                            respLogout: 'logoff',
                            respMakeCall: 'makecall',
                            respAnswerCall: 'answercall',
                            respClearCall: 'clearcall',
                            respReady: 'setready',
                            respNotReady: 'setnotready',
                            respWorkReady: 'workReady',
                            respWorkNotReady: 'workNotReady'
                        }

                        var respType = message.cmddata.srcMessageId;
                        ccbarEvent.emitEvent(respType, message.cmddata);

                        /*resultCode!=000时*/
                        if(message.cmddata.resultCode && message.cmddata.resultCode!='000' && message.cmddata.resultCode!='succ'){
                            var errMsg = message.cmddata.resultDesc|| ccbar_config.error[message.cmddata.resultCode] || '错误代码' + message.cmddata.resultCode

                            var errData = {
                                resultCode:message.cmddata.resultCode,
                                resultText:errMsg,
                                srcMessageId:message.cmddata.srcMessageId
                            }
                            ccbarEvent.emitEvent('notifyError', errData);

                        }

                        if (respType in respFuncList) {
                            if (!message.cmddata.agentId && message.agentId) {
                                message.cmddata.agentId && message.agentId
                            }
                            if (message.cmddata.resultCode == '104' && respFuncList[respType] == 'logoff') {} else {
                                ccbarEvent.emitEvent(respFuncList[respType], message.cmddata);
                            }
                        }

                        //签入失败,停止长连接
                        if (message.cmddata.srcMessageId == 'respLogin' && message.cmddata.result == 'Succ') {
                            CallControl.isLogoned = true;
                        }
                        if (message.cmddata.srcMessageId == 'respLogin' && message.cmddata.result == 'Fail') {
                            ccbarDebugger("签入失败");
                            clearLongPollingFlag = true;
                        }

                        if ((message.cmddata.srcMessageId == 'respLogout' && message.cmddata.resultCode == '000') || message.cmddata.resultCode == '101' || message.cmddata.resultCode == '117') {
                            clearLongPollingFlag = true;
                            CallControl.isLogoned = false;
                            _longpollingTime = null
                            var funcMask = CallControl.getFunc();
                            for (var i in funcMask) {
                                funcMask[i] = false;
                            }
                            funcMask['logon'] = true;
                            var resetStatus = {
                                "workMode": "all",
                                "stateDesc": "登出",
                                "notifyContent": "",
                                "state": "LOGOFF",
                                "resultDesc": null,
                                "funcMask": funcMask
                            }

                            if (message.cmddata.resultCode == '117') {
                                var msg = message.cmddata.content || '[117]坐席已签出'
                                clearLongPollingFlag = true;
                                ccbar_popup && ccbar_popup.alert(msg)
                            }

                            //CallControl.closeSIP()

                            CallControl.state = resetStatus.state;
                            CallControl.workModeType = resetStatus.workMode;
                            CallControl.funcMask = resetStatus.funcMask;
                            CallControl.onRespAgentState(resetStatus);
                            ccbarEvent.emitEvent('agentStateSync', resetStatus)
                        }
                        break;
                    case 'mediaEvent':
                        CallControl.onRespMediaEvent(message);
                        break;
                    case 'sessiontimeout':
                        break;
                    case 'heartbeat':
                        CallControl.ws.checkHeartbeat()
                        break;
                    default:
                        ccbarDebugger('未知状态');
                }
            }
        },
        /*websocket*/
        ws: {
            retryLock: false,
            retryClock: null,
            autoReconnect: true,
            ws: null,
            ip: '',
            port: '',
            lastMsg: '',
            host: '',
            retryCount: 0,
            noHeartbeatRespCount:0,
            noHeartbeatRespClock:null,
            init: function(ip, port, callback) {
                var tempHost = CallControl.getContextPath();
                if (tempHost == '') {
                        tempHost = location.origin;
                    }
                var wshost = tempHost.replace('http', 'ws');
                    var userName = ccbarCache.agentId || 'web';
                    ccbarDebugger('ccbar.js:ws.init:host='+wshost + '/yc-ccbar/ccbarws/'+userName);

                CallControl.ws.host = wshost + '/yc-ccbar/ccbarws/'+userName;
                if (ccbarCache.cuid) CallControl.ws.host = wshost + '/yc-ccbar/ccbarws/'+userName + '/' + ccbarCache.cuid;
            },
            connect: function(option) {
                if (CallControl.ws.ws == null || CallControl.ws.ws.readyState != WebSocket.OPEN) {
                    var tempHost = CallControl.getContextPath();
                    if (tempHost == '') {
                        tempHost = location.origin;
                    }
                    var wshost = tempHost.replace('http', 'ws');
                    var userName = ccbarCache.agentId || 'web';
                    var host = wshost + '/yc-ccbar/ccbarws/'+userName;
                    if (ccbarCache.cuid) host = wshost + '/yc-ccbar/ccbarws/'+userName + '/' + ccbarCache.cuid;
                    ccbarDebugger('ccbar.js:ws.connect:host='+host);
                    CallControl.ws.ws = new WebSocket(host);
                    CallControl.ws.ws.onmessage = CallControl.ws.handlerMessage;
                    CallControl.ws.ws.onopen = function(event) {
                        CallControl.ws.onopen(event);
                        option && option.onopen && option.onopen(event);
                        setTimeout(CallControl.queryAgentInfo(),1000);
                    }

                    CallControl.ws.ws.onclose = CallControl.ws.onclose;
                    CallControl.ws.ws.onerror = CallControl.ws.onerror;
                }
            },
            disconnect: function() {
                try {
                    ccbarHeartbeat && CallControl.clearTimeout(ccbarHeartbeat)
                    if (CallControl.ws && CallControl.ws.ws) CallControl.ws.ws.close();
                } catch (e) {
                    ccbarDebugger('ws关闭失败,', e);
                }
            },
            handlerMessage: function(message) {
                CallControl.ws.lastMsg = new Date().getTime();;
                var messageData = JSON.parse(message.data)
                //ccbarDebugger('收到消息',message,messageData)
                if (messageData.error) {

                    try {
                        if (messageData.error == 'session timeout！') {
                            return;
                        }
                        tipsMsg(messageData.error)
                    } catch (e) {}
                }
                CallControl.handelLongPollingSuccess && CallControl.handelLongPollingSuccess(messageData,true);
            },
            onopen: function(event) {
                ccbarDebugger('WebSocket opened',CallControl.ws.ws.readyState);
                CallControl.ws.lastMsg = new Date().getTime();
                ccbarEvent.emitEvent('ccbarWsOpen', event)
                tipsMsg && tipsMsg('话务系统websocket已连接', 3000);
                ccbarHeartbeat && CallControl.clearTimeout(ccbarHeartbeat);
                CallControl.ws.heartbeat(true)
                CallControl.ws.retryCount = 0;
                CallControl.ws.retryLock = false;
            },
            onclose: function(event) {
                ccbarDebugger(event)
                event.resultText = '话务websocket关闭';
                event.resultCode = event.code != 1000?event.code:'000'
                CallControl.ws.noHeartbeatRespCount = 0;

                ccbarHeartbeat && CallControl.clearTimeout(ccbarHeartbeat);
                CallControl.ws.retryLock = false;
                if (event.code != 1000) {
                    ccbarDebugger('ws非正常结束' + event.code)
                    CallControl.updateStatus && CallControl.updateStatus('SERVICEOFFLINE')
                    ccbarEvent.emitEvent('ccbarWsClose', event)

                    ccbarErrorSubmit.addError('wsClose', event.code) //反馈异常关闭

                    tipsMsg && tipsMsg('话务系统websocket已断开,请检查网络尝试重新登录', 3000);
                    CallControl.ws.retry();
                    return;
                } else {
                    ccbarDebugger('ws正常结束')
                    ccbarHeartbeat && CallControl.clearTimeout(ccbarHeartbeat);
                }
            },
            isWsConnect:function(){
                return CallControl.ws.ws != null && CallControl.ws.ws.readyState == WebSocket.OPEN
            },
            onmessage: function(message) {
                var data = JSON.parse(message.data);
                ccbarDebugger(data);
            },
            onerror: function(event) {
                ccbarDebugger('WebSocket error');
                ccbarEvent.emitEvent('ccbarWsError', event)
                ccbarDebugger( event)
            },
            heartbeat: function() {
                ccbarHeartbeat && CallControl.clearTimeout(ccbarHeartbeat)

                if(CallControl.ws.isWsConnect()){//ws在线 发心跳
                    if (CallControl.ws.lastMsg) {
                        var time = new Date().getTime();
                        if ((time - CallControl.ws.lastMsg) > (30 * 1000)) { //超过60秒没收到消息
                            ccbarDebugger('30秒没收到ws消息,请检查网络或尝试重新登录');
                            CallControl.ws.disconnect();
                            window.reStartCCBAR && CallControl.clearTimeout(window.reStartCCBAR)
                            window.reStartCCBAR = CallControl.setTimeout(CallControl.ws.connect, 1000);
                            return;
                        }
                    }
                    if (CallControl.ws.ws == null || CallControl.ws.ws.readyState != WebSocket.OPEN) {
                        tipsMsg && tipsMsg('话务系统websocket已断开,请检查网络或尝试重新登录', 3000);
                        ccbarDebugger('话务系统websocket已断开,请检查网络或尝试重新登录');
                        CallControl.clearTimeout(ccbarHeartbeat)
                        return;
                    }
                    ccbarHeartbeat = CallControl.setTimeout(CallControl.ws.heartbeat, 10000)
                    CallControl.ws.send('heartbeat', 'sendMessage', {
                        'cmdJson': JSON.stringify({
                            msgType: "heartbeat",
                            serialId:uuid(),
                            type: 'wsAuto'
                        })
                    });
                    CallControl.ws.lastWsTime = new Date().getTime();
                    //1.5秒后判断有没有收到心跳,没有就重新发起
                    ccbarHeartbeat && CallControl.clearTimeout(CallControl.ws.noHeartbeatRespClock)
                    
                    CallControl.ws.noHeartbeatRespClock = CallControl.setTimeout(CallControl.ws.checkGetHeartbeatResp,1500)
                }else{
                    ccbarDebugger('ws已断开,无法发起心跳',CallControl.ws.ws.readyState == WebSocket.OPEN,CallControl.ws.ws.readyState)
                }

            },
            checkGetHeartbeatResp:function(){
                CallControl.ws.noHeartbeatRespCount++
                if(CallControl.ws.noHeartbeatRespCount==3){//3次收不到就重连
                    CallControl.ws.noHeartbeatRespCount = 0;
                    ccbarDebugger('已经连续3次没有收到心跳响应,重连')
                    CallControl.updateStatus && CallControl.updateStatus('SERVICEOFFLINE')
                    
                    CallControl.ws.disconnect();
                    CallControl.ws.connect();
                }else{
                    CallControl.ws.heartbeat()
                }
            },
            checkHeartbeat:function(){
                ccbarHeartbeat && CallControl.clearTimeout(CallControl.ws.noHeartbeatRespClock)

                CallControl.ws.noHeartbeatRespCount = 0;

                var timeStamp = new Date().getTime();
                var delay = timeStamp - CallControl.ws.lastWsTime;
                if(CallControl.ws.lastWsTime){
                    CallControl.ws.delay = delay;
                    CallControl.ws.showNetworkCheck()
                }
                //20秒没发起心跳也重新检查一遍
                if(timeStamp - CallControl.ws.lastWsTime>20000){
                    CallControl.ws.heartbeat();
                }    
            },
            showNetworkCheck:function(){
                var timeStamp = new Date().getTime();
                var data = {
                    timestamp:timeStamp,
                    networkType:navigator.connection.networkType || navigator.connection.effectiveType,//网络类型
                    navigatorOnLine:navigator.onLine,//浏览器是否在线
                    heartbeat:CallControl.ws.delay,//ws心跳延迟
                    rtt:navigator.connection.rtt,//网络延迟,基于浏览器API判断,不一定准
                    httpDuration:CallControl.lastRequestDelay,//最后一次cti命令的请求延迟,不能实时反映,少于0时为请求异常
                    phoneStatus:agentCache.monitor.phoneStatus,//服务器侧话机状态
                    phoneType:CallControl.resultConfig.phoneType,//话机类型
                    ccbarSocketOnline:CallControl.ws.ws && CallControl.ws.ws.readyState == WebSocket.OPEN,//话务ws状态
                    webrtcState:WebRtcServer && WebRtcServer.isLive() && CallControl.webrtc.sockesOnline//webrtc话机状态
                }
                ccbarEvent.emitEvent('ccbarNetworkCheck',data)
            },
            send: function(event, action, data, callback) {
                var _data = {
                    event: event,
                    action: action
                }
                _data = $.extend({}, _data, data);

                _data = typeof(_data) == 'string' ? _data : JSON.stringify(_data)
                if (CallControl.ws.ws.readyState === WebSocket.OPEN) {
                    CallControl.ws.ws.send(_data);
                } else {
                    if (CallControl.isLogoned) {
                        CallControl.ws.connect();
                    }
                }
            },
            retry: function() {
                
                if ((CallControl.ws.readyState != WebSocket.OPEN) && CallControl.ws.autoReconnect && !CallControl.ws.retryLock) {
                    CallControl.ws.retryLock = true;
                    if (CallControl.ws.retryClock) CallControl.clearTimeout(CallControl.ws.retryClock)
                    //重连已经到了最大次数
                    if(CallControl.getConfig().retryCount && CallControl.ws.retryCount == CallControl.getConfig().retryCount){
                        ccbarResetState({
                            resultCode:'999',
                            resultDesc:'websocket重连'+CallControl.ws.retryCount+'次失败,请重新签入'
                        })
                        CallControl.ws.retryCount = 0;
                        return;
                    }
                     CallControl.ws.retryClock = CallControl.setTimeout(function() {
                        tipsMsg && tipsMsg('话务系统websocket重连中...');
                        CallControl.ws.connect();
                        ccbarErrorSubmit.addError('wsError', 'wsRetry-' + CallControl.ws.retryCount)
                        CallControl.ws.retryCount++
                        ccbarEvent.emitEvent('ccbarWsRetry',{ 
                            count:CallControl.ws.retryCount
                        })
                    }, 5000);

                }
            }
        },
        /*websocket end*/
        log: function(msg, succcallback) {
            return;
            msg = msg + ',timestamp=' + new Date().getTime()
            var data = {
                msg: msg
            }
            chatRequestSend(this.getContextPath() + "/yc-ccbar/ConsoleLog?action=log", cmdJson(data), function(result,r2,r3) {
                    ccbarDebugger('1===>',result,state,response)

                if (succcallback && $.isFunction(succcallback)) {
                    succcallback(result,r3.status);
                }
            }, {
                // methods:'post',
                jsonpCallback: "jsonpCallbackSendLog",
                error: function(XMLHttpRequest, textStatus, errorThrown) {
                    if (failcallback && $.isFunction(failcallback)) {
                        failcallback(XMLHttpRequest, textStatus, errorThrown);
                    }
                    if (textStatus == "timeout") { // 请求超时
                        ccbarDebugger('error log timeout!');
                    }
                }
            });
        },
        sendCTICommand: function(data, succcallback, failcallback) {
            if (!data.messageId) {
                ccbarDebugger("发起请求失败,没有messageId");
                return false;
            }
            data = $.extend({}, {
                // agentId: CallControl.agentId
            }, data)

            var startTime = new Date().getTime()

            var rid = uuid()
            data.rid = rid;
            var sendData = cmdJson(data)
            var logData = {
                messageId:data.messageId,
                rid:rid,
                startTime:new Date().format('hh:mm:ss'),

            }
            ccbarRequestSend(this.getContextPath() + "/yc-ccbar/AgentEvent?action=event", sendData, function(result,r2,response) {
                if(result.state!='1' && result.data && result.data.resultCode){
                    var notifyData = {
                        srcMessageId:data.messageId,
                        resultCode:result.data.resultCode,
                        resultText:result.msg

                    }
                    ccbarEvent.emitEvent('notifyError', notifyData)
                }
                logData.time = new Date().getTime() - startTime;
                logData.status = response.status;

                if(logData.time>2000){
                    CallControl.logger && CallControl.logger.send('delay',logData)
                    ccbarErrorSubmit.addError('delay',logData)
                }//请求超过2秒的上报

                if (succcallback && $.isFunction(succcallback)) {

                    succcallback(result);
                }
            }, {
                jsonpCallback: "jsonpCallbackSendCTICommand",
                error: function(XMLHttpRequest, textStatus, errorThrown) {
                    ccbarEvent.emitEvent('ccbarHttpFail', {
                        time: new Date().getTime(),
                        delay: new Date().getTime() - startTime,
                        onLine: false,
                        textStatus: textStatus,
                        messageId: data.messageId
                    })

                    if (typeof(layer) != 'undefined') {
                        layer.closeAll('loading');
                    } else {
                        $("#ccbar .ccbar-mask").fadeOut()
                    }

                    var messageId = sendData && sendData.cmdJson && JSON.parse(sendData.cmdJson).messageId;

                    logData.time = new Date().getTime() - startTime;
                    logData.status = textStatus;
                    CallControl.logger && CallControl.logger.send('cmdError',logData)

                    //发送命令失败时,反馈错误信息
                    ccbarErrorSubmit.addError('ctiError', data.messageId + ':' + XMLHttpRequest.status)
                    if (failcallback && $.isFunction(failcallback)) {
                        failcallback(XMLHttpRequest, textStatus, errorThrown);
                    }
                    var errorText = '';
                    if (textStatus === 'timeout') {
                        errorText = '请求失败，发送请求到服务端超时';
                        if (messageId) {
                            //errorText = '[' + JSON.parse(sendData.cmdJson).messageId + ']请求超时，请求检查网络是否可达！'
                            errorText = '请求失败，发送请求到服务端超时'
                        }
                    } else if (XMLHttpRequest.status === 0) {
                        errorText = '请求失败，发送请求到服务端超时'
                        console.error(errorText);
                    } else if (XMLHttpRequest.status == 500 || XMLHttpRequest.status == 404) {
                        errorText = '请求处理异常['+XMLHttpRequest.status+']'
                    } else if (textStatus === 'parsererror') {
                        errorText = '请求失败，数据格式有误';
                    } else if (textStatus === 'abort') {
                        errorText = '请求被中止';
                    } else {
                        errorText = XMLHttpRequest.responseText;
                    }
                    var errType = 'request ' + textStatus;
                    try {
                        if (textStatus != 'parsererror') tipsMsg('发送ccbar请求' + errorText);
                    } catch (e) {
                        ccbarDebugger("ccbarRequestSend", e);
                    }
                    var errorMsg = 'ccbarRequestSend ' + errType + ',textStatus=' + textStatus + ',status=' + XMLHttpRequest.status + ',readyState=' + XMLHttpRequest.readyState
                    CallControl.log(errorMsg);
                }
            });
        },
        /**
         * [downloadSIP 下载sip话机]
         */
        downloadSIP: function() {
            window.open(this.getContextPath() + "/yc-ccbar/ccbar/download.jsp")
        },
        /**
         * [wakeupSIP 拉起sip话机]
         */
        wakeupSIP: function(sbcAddr) {
            var time = new Date().getTime()
            if(CallControl.lastWakeSip && time-CallControl.lastWakeSip<200) return;
            if (!(CallControl.sbcAddr) && !CallControl.webrtcConfig) {
                return;
            }
            if ($('#_ccbar_hiddenIfm').length == 0) {
                var iframe = '<iframe id="_ccbar_hiddenIfm" name="_ccbar_hiddenIfm" border="0" frameborder="0" width="0" height="0" style="display: none"></iframe>';
                $('body').append(iframe);
            }
            if (CallControl.phoneType == 3) {
                _ccbar_hiddenIfm.location.href = this.sbcAddr;
            }
            CallControl.lastWakeSip = time
            // else if(CallControl.phoneType == 5) {
            //     CallControl.webrtc.wakeupSIP()
            // }
        },
        /**
         * [closeSIP 关闭sip话机]
         */
        closeSIP: function() {
            var time = new Date().getTime()
            if(CallControl.lastCloseWakeSip && time-CallControl.lastCloseWakeSip<200) return;
            if ($('#_ccbar_hiddenIfm').length == 0) {
                var iframe = '<iframe id="_ccbar_hiddenIfm" name="_ccbar_hiddenIfm" border="0" frameborder="0" width="0" height="0" style="display: none"></iframe>';
                $('body').append(iframe);
            }
            if (CallControl.phoneType == 3) _ccbar_hiddenIfm.location.href = 'yunqumicrosip://3A932DF0F2BAB4C8';
            else if (CallControl.phoneType == 5) CallControl.webrtc.closeSIP()

            CallControl.lastCloseWakeSip = time

        },
        /**
         * [getFunc 获取ccbar功能按钮的状态]
         */
        getFunc: function(type) {
            if (this.funcMask == null) {
                return false;
            }
            if (type != undefined && type != null) {
                if (typeof(type) == 'string' && type in this.funcMask) {
                    return this.funcMask[type];
                } else if (typeof(type) == 'array') {
                    var tempArr = []
                    for (var i = 0; i < type.length; i++) {
                        var tempFunc = type[i] in this.funcMask ? this.funcMask[type[i]] : false;
                        tempArr.push(tempFunc);
                    }
                    return tempArr;
                }
            } else {
                return this.funcMask;
            }
        },
        /**
         * [getState 获取坐席当前状态]
         * @return {String} [坐席状态]
         */
        getState: function() {
            return this.state;
        },
        // 启动坐席话务条, 只能启动一次
        /**
         * [start 启动话务条,旧ccbar遗留接口,已废弃]
         */
        start: function(myIp, myPort) {
            this.myIp = myIp;
            this.myPort = myPort;
        },
        phoneCheck:function(cmd){
            if(CallControl.resultConfig && CallControl.resultConfig.phoneType!=5 || CallControl.getConfig().dontWakeUpPhone){
                return true;
            }

            var type;
            var tips = {
                'protocolFail':'话机离线，需要在https环境使用',
                'browserFail':'话机离线，浏览器不支持webrtc',
                'phoneSDKFail':'话机离线，话机SDK加载失败',
                'phoneInitFail':'话机离线，话机初始化参数缺失',
                'networkOffline':'话机离线，话机到链接服务端的网络中断',
                'deviceFail':'话机离线，没有检测到麦克风和摄像头设备',
                'promiseFail':'呼叫失败，没有检测到麦克风和摄像头授权',
                'authFail':'话机离线，话机鉴权失败',
                'serverOffline':'话机离线，服务端检测不到话机状态',
            }

            //判断当前协议
            if(location.protocol != 'https:' && location.protocol != 'file:'){
                type = 'protocolFail';
            }

            //判断sdk
            if(typeof(yqWebrtcApp) == 'undefined'){
                type = 'phoneSDKFail';
            }
            //判断初始化参数
            if(!type && CallControl.webrtcConfig){
                for(var key in CallControl.webrtcConfig){
                    if(CallControl.webrtcConfig[key] == ''){
                        type = 'phoneInitFail' 
                    }
                }
            }else if(!type && !CallControl.webrtcConfig){
               type = 'phoneInitFail' 
            }

            //判断是否参数或者或者没有授权导致的鉴权失败
            if(!type && CallControl.webrtc.errorContent){
                var errorDetail = CallControl.webrtc.errorContent
                if(errorDetail == '鉴权失败'){
                    type = 'authFail' 
                }else if(errorDetail == 'NotAllowedError'){
                    type = 'promiseFail';
                }else if(errorDetail == 'DevicesNotFoundError'){
                    type = 'deviceFail'
                }
            }


            //判断网络
            if(!type && WebRtcServer){
                if(WebRtcServer.isLive()){
                    if(CallControl.webrtc.state == 'offline') type = 'networkOffline'
                }else{
                    type = 'networkOffline'
                }
            }

            if(!type && agentCache.monitor.phoneStatus == 'offline'){
                type = 'serverOffline';
            }

            if(type){
                ccbarEvent.emitEvent('notifyError',{
                    resultCode:'999',
                    resultText:tips[type]||'话机异常',
                    srcMessageId:cmd
                })
                tipsMsg(tips[type])
            }

            return !type;
        },
        /**
         * [makeCall 外呼接口]
         * @param  {String}   called   [CallType为1时，被外呼的坐席工号；CallType为2时，客户电话号码]
         * @param  {String}   caller   [主叫显示号码]
         * @param  {Function} callback [回调函数]
         * @param  {String}   callType [1: 席间呼叫 2:呼外线用户(默认)]
         */
        makeCall: function(called, caller, userData, callback, callType, skillId, isVideo) {
            if(!CallControl.phoneCheck()){
                return;
            }

            called = (called == null || typeof called == 'undefined') ? "" : called;
            caller = (caller == null || typeof caller == 'undefined') ? "" : caller;

            var data = {
                //agentId: CallControl.agentId,
                cuid: CallControl.cuid,
                called: called,
                caller: caller,
                userData: userData || '',
                callType: callType || 2,
                skillId: skillId || '',
                timestamp: new Date().getTime()
            };
            // 话后整理
            if(userData && typeof(userData.workReadyTimeout)!='undefined'){
                data.workReadyTimeout = userData.workReadyTimeout;
                delete userData.workReadyTimeout;
            }
            if(userData && typeof(userData.workReadyFlag)!='undefined'){
                data.workReadyFlag = userData.workReadyFlag;
                delete userData.workReadyFlag;
            }
            if (isVideo) data.callMode = '2'
            var startTime = new Date().getTime();
            var logData = {
                messageId:'cmdMakeCall',
                startTime:new Date().format('yyyy-MM-dd hh:mm:ss'),
            }
            

            ccbarRequestSend(this.getContextPath() + '/yc-ccbar/CallEvent?action=Makecall', cmdJson(data), function(result,state,response) {
                logData.time = new Date().getTime() - startTime;
                logData.status = response.status;

                if(logData.time>2000){
                        CallControl.logger && CallControl.logger.send('delay',logData)
                }//请求超过2秒的上报

                if(result.data && result.data.resultCode){
                    if(result.data.resultCode != '000' && result.data.resultCode != '200'){
                        ccbarEvent.emitEvent('notifyError', {
                            resultCode:result.data.resultCode,
                            resultText:result.data.content || result.msg,
                            srcMessageId:'cmdMakeCall'
                        });
                    }
                }
                if ($.isFunction(callback)) {
                    callback(result);
                }
                lastMakeCall = new Date().getTime();
            }, {
                jsonpCallback: "jsonpCallbackMakecall"
            });

        },
        /**
         * [cryptMakeCall des加密外呼接口]
         * @param  {String}   called   [CallType为1时，被外呼的坐席工号；CallType为2时，客户电话号码]
         * @param  {String}   caller   [主叫显示号码]
         * @param  {Function} callback [回调函数]
         * @param  {String}   callType [1: 席间呼叫 2:呼外线用户(默认)]
         */
        cryptMakeCall: function(called, caller, userData, callback, callType, skillId) {
            called = (called == null || typeof called == 'undefined') ? "" : called;
            caller = (caller == null || typeof caller == 'undefined') ? "" : caller;

            var data = {
                //agentId: CallControl.agentId,
                cuid: CallControl.cuid,
                called: called,
                caller: caller,
                userData: userData || '',
                callType: callType || 2,
                skillId: skillId || ''
            };
            ccbarRequestSend(this.getContextPath() + '/yc-ccbar/CallEvent?action=cryptMakecall', cmdJson(data), function(result) {
                if ($.isFunction(callback)) {
                    callback(result);
                }
            }, {
                jsonpCallback: "jsonpCallbackCryptMakeCall"
            });

        },
        /**
         * [logoff 发起登出的命令]
         * @param  {Function} callback [回调函数]
         */
        logoff: function(callback) {
            this.sendCTICommand({
                messageId: ccbar_config['command']['logoff']

            }, callback);
        },
        /**
         * [forceLogoff 强制签出坐席]
         * @param  {[type]}   agentId  [description]
         * @param  {Function} callback [description]
         * @return {[type]}            [description]
         */
        forceLogoff: function(agentId, callback) {
            CallControl.changeAgentStatus(agentId,'forcelogoff','',callback)
            return;
            var data = {
                messageId: 'cmdForceLogout'
            }
            if (agentId) {
                data.agentId = agentId
            }
            this.sendCTICommand(data, callback, {}, true);
        },
        /**
         * [workMode 变更呼叫模式]
         * @param  {String}   workMode [inbound呼入 outbound呼出 pdsbound智能外呼 all全功能]
         * @param  {Function} callback [回调函数]
         */
        workMode: function(workMode, callback) {
            var data = {
                workMode: workMode
            };
            var workModeName = {
                inbound: '呼入',
                outbound: '呼出'
            }
            if (workMode == 'all' && ('off' == CallControl['inbound'] || 'off' == CallControl['outbound'])) {
                callback && callback({
                    state: 0,
                    data: {
                        content: '切换权限受限'
                    }
                });
                return;
            }
            if ('off' == CallControl[workMode]) {
                callback && callback({
                    state: 0,
                    data: {
                        content: '权限受限'
                    }
                });
                return;
            }
            ccbarRequestSend(this.getContextPath() + '/yc-ccbar/AgentEvent?action=workMode', cmdJson(data), function(result) {
                if ($.isFunction(callback)) {
                    callback(result);
                }
            }, {
                jsonpCallback: "jsonpCallbackWorkMode"
            });
        },
        /*呼叫前判断,转移方法如果userData入参是空的时候才会执行判断,取这个方法返回的对象当userData的参数,如果方法返回false就取消发送转移命令*/
        beforeTransfer: function(called, displayNumber, callType, userData) {
            return ''
        },
        /**
         * [transfer 呼叫转移]
         * @param  {String}   called        [1:被转移坐席工号 2:被转移Ivr字冠 3:被转移客户电话 4:被转移技能组ID]
         * @param  {String}   displayNumber [被转移对象的主显号码]
         * @param  {String}  skillId [被转移坐席的技能组id,非必填]
         * @param  {String}   callType      [1:呼叫座席 2:呼叫IVR 3:呼叫外线 4:技能组]
         * @param  {Object}   userData      [业务私有数据]
         * @param  {Function} callback      [回调函数]
         */
        transfer: function(called, displayNumber, callType, userData, callback) {
            this.sstransfer(called, displayNumber, callType, userData, callback);
        },

        //单步转//呼叫转移
        sstransfer: function(called, displayNumber, skillId, callType, userData, callback, isVideo) {
            if (called == '' || called == null || typeof called == 'undefined') {
                tipsMsg('发起转移失败,转移目标不能为空');
                return;
            }
            userData = (userData == null || typeof userData == 'undefined') ? "" : userData;
            isVideo = isVideo || CallControl.webrtc.isVideo;
            if (userData == '' || $.isEmptyObject(userData) || userData == null) {
                //判断是否符合要求
                userData = CallControl.beforeTransfer(called, displayNumber, skillId, callType);
                if (userData === false) {
                    ccbarDebugger('未满足转移条件');
                    return;
                }
            }

            if(callType == 4 && String(called).indexOf('#')!=-1){
                called = String(called).split('#')[1]
            }

            var data = {
                messageId: ccbar_config['command']['sstransfer'],
                called: called,
                displayNumber: displayNumber,
                callType: callType,
                userData: userData,
                skillId: skillId
            }

            if (isVideo) data.callMode = '2'


            this.sendCTICommand(data, callback);
        },
        satisf:function(callback,userData){//主动转满意度
            this.sstransfer('satisf',null,null,2,userData,callback)
        },
        satisfy:function(callback,userData){//主动转满意度
            this.sstransfer('satisf',null,null,2,userData,callback)
        },
        /**
         * [consultation 咨询]
         * @param  {String}   called        [1:被转移坐席工号 2:被转移Ivr字冠 3:被转移客户电话 4:被转移技能组ID]
         * @param  {String}   displayNumber [被转移对象的主显号码]
         * @param  {String}   callType      [1:呼叫座席 2:呼叫IVR 3:呼叫外线 4:技能组]
         * @param  {Object}   userData      [业务私有数据]
         * @param  {Function} callback      [回调函数]
         */
        consultation: function(called, displayNumber, skillId, callType, userData, callback, isVideo) {
            if (called == '' || called == null || typeof called == 'undefined') {
                tipsMsg('发起咨询失败,转移目标不能为空');
                return;
            }
            isVideo = isVideo || CallControl.webrtc.isVideo;

            if(callType == 4 && String(called).indexOf('#')!=-1){
                called = String(called).split('#')[1]
            }
            
            userData = (userData == null || typeof userData == 'undefined') ? "" : userData;
            var data = {
                messageId: ccbar_config['command']['consultation'],
                called: called,
                displayNumber: displayNumber,
                callType: callType,
                skillId: skillId,
                userData: userData
            }
            if (isVideo) data.callMode = '2'
            this.sendCTICommand(data, callback);
        },
        /**
         * [conference 三方]
         * @param  {String}   called        [1:被转移坐席工号 2:被转移Ivr字冠 3:被转移客户电话 4:被转移技能组ID]
         * @param  {String}   displayNumber [被转移对象的主显号码]
         * @param  {String}   callType      [1:呼叫座席 2:呼叫IVR 3:呼叫外线 4:技能组]
         * @param  {Object}   userData      [业务私有数据]
         * @param  {Function} callback      [回调函数]
         */
        conference: function(userData, callback) {
            userData = (userData == null || typeof userData == 'undefined') ? "" : userData;
            this.sendCTICommand({
                messageId: ccbar_config['command']['conference'],
                // called:called,
                // displayNumber:displayNumber,
                // callType:callType,
                userData: userData
            }, callback);
        },
        /**
         * [clearCall 挂机]
         * @param  {Function} callback [回调函数]
         */
        clearCall: function(callback, userData, sender) {
            if(CallControl.getState() == 'INTERVENTED'){//强插状态
                CallControl.stopInvent()
                return;
            }

            if(CallControl.getState() == 'SECRETLYTALK'){//强插状态
                CallControl.stopSecretlyTalk()
                return;
            }

            if(CallControl.getState() == 'MONITORED'){//监听状态
                CallControl.stopMonitor()
                return;
            }

            if (!CallControl.getFunc('clearcall')) {
                return
            }
            if (lastMakeCall) {
                var timeNow = new Date().getTime();
                if (timeNow - lastMakeCall < 100) {
                    ccbarDebugger('在外呼后0.1秒内挂机,忽略')
                    return;
                }
            }
            var s = sender || 'JS';
            this.sendCTICommand({
                messageId: ccbar_config['command']['clearCall'],
                status: CallControl.getState(),
                timestamp: new Date().getTime(),
                sender: s
            }, callback);
        },
        /**
         * [answerCall 应答]
         * @param  {Function} callback [回调函数]
         */
        answerCall: function(callback) {
            this.sendCTICommand({
                messageId: ccbar_config['command']['answerCall']
            }, callback);
        },
        /**
         * [agentReady 设置为就绪]
         * @param  {Function} callback [回调函数]
         */
        agentReady: function(callback) {
            this.sendCTICommand({
                messageId: ccbar_config['command']['agentReady']
            }, callback);
        },

        /**
         * [agentNotReady 坐席示忙/设置小休]
         * @param  {String}   busyType [示忙类型 1:小休,2:会议,3:培训]
         * @param  {Function} callback [回调函数]
         */
        agentNotReady: function(busyType, callback) {
            var flag = true;
            if (typeof(CallControl.beforeSetNotReady) != 'undefined') {
                flag = CallControl.beforeSetNotReady();
            }
            if (!flag) return;
            this.sendCTICommand({
                messageId: ccbar_config['command']['agentNotReady'],
                busyType: busyType || 1
            }, callback);
        },
        /**
         * [holdCall 客户话路保持]
         * @param  {Function} callback [description]
         */
        holdCall: function(callback) {
            this.sendCTICommand({
                messageId: ccbar_config['command']['holdCall']
            }, callback);
        },
        /**
         * [unholdCall 客户话路恢复]
         * @param  {Function} callback [description]
         */
        unholdCall: function(callback) {
            this.sendCTICommand({
                messageId: ccbar_config['command']['unholdCall']
            }, callback);
        },
        /**
         * [listenCall 监听--旧ccbar接口兼容]
         * @param  {Function} callback        [description]
         */
        listenCall: function(listenedAgentId, userData, callback) {
            this.startMonitor(listenedAgentId, userData, callback);
        },
        /**
         * [startMonitor 开始监听-被监听坐席必须处于通话状态]
         * @param  {String}   listenedAgentId [被监听的座席工号]
         * @param  {Object}   userData [业务私有数据]
         * @param  {Function} callback   [回调方法]
         */
        startMonitor: function(agentId, userData, callback) {
            this.sendCTICommand({
                messageId: ccbar_config['command']['startMonitor'],
                called: agentId,
                userData: userData || ''
            }, callback);
        },
        /**
         * [stopMonitor 结束监听]
         * @param  {Function} callback [回调方法]
         */
        stopMonitor: function(callback) {
            this.sendCTICommand({
                messageId: ccbar_config['command']['stopMonitor']
            }, callback);
        },
        /**
         * [cmdSecretlyTalk 开始私语-被监听坐席必须处于通话状态]
         * @param  {String}   listenedAgentId [被私语的座席工号]
         * @param  {Object}   userData [业务私有数据]
         * @param  {Function} callback   [回调方法]
         */
        secretlyTalk: function(agentId, userData, callback) {
            this.sendCTICommand({
                messageId: 'cmdSecretlyTalk',
                called: agentId,
                userData: userData || ''
            }, callback);
        },
        /**
         * [stopMonitor 结束私语]
         * @param  {Function} callback [回调方法]
         */
        stopSecretlyTalk: function(callback) {
            this.sendCTICommand({
                messageId: 'cmdStopSecretlyTalk'
            }, callback);
        },
        /**
         * [startInvent 开始强插-被强插坐席必须处于通话状态]
         * @param  {String}   listenedAgentId [被强插的座席工号]
         * @param  {Function} callback        [回调方法]
         */
        startInvent: function(listenedAgentId, callback) {
            this.sendCTICommand({
                messageId: ccbar_config['command']['startInvent'],
                called: listenedAgentId
            }, callback);
        },
        /**
         * [stopInvent 结束强插]
         * @param  {Function} callback [回调方法]
         */
        stopInvent: function(callback) {
            this.sendCTICommand({
                messageId: ccbar_config['command']['stopInvent']
            }, callback);
        },
        /**
         * [changeAgentStatus 班长坐席发起，强制改变普通坐席当前的状态]
         * @param  {String}   agentId    [目标坐席ID]
         * @param  {String}   actionType [操作类型：forcelogoff 强制签出 forceready 强制示闲 forcenotready强制示忙]
         * @param  {String}   reason     [改变坐席状态的原因]
         * @param  {Function} callback   [回调函数]
         */
        changeAgentStatus: function(agentId, actionType, reason, callback) {
            this.sendCTICommand({
                messageId: ccbar_config['command']['changeAgentStatus'],
                called: agentId,
                actionType: actionType,
                reason: reason || ''
            }, callback);
        },
        /**
         * [heartbeat 心跳请求]
         */
        heartbeat: function(callback) {
            this.sendCTICommand({
                messageId: ccbar_config['command']['heartbeat'],
                callback: callback
            });
        },
        /**
         * [workReady 设置为完成话后整理]
         * @param  {Function} callback [回调函数]
         */
        workReady: function(callback) {
            this.sendCTICommand({
                messageId: ccbar_config['command']['workReady']
            }, callback);
        },
        /**
         * [workNotReady 设置为进入话后整理]
         * @param  {Function} callback [回调函数]
         */
        workNotReady: function(callback) {
            this.sendCTICommand({
                messageId: ccbar_config['command']['workNotReady']
            }, callback);
        },
        /**
         * [agentList 获取当前空闲坐席列表]
         * 技能组ID传空值时,返回所在坐席技能组的空闲坐席列表,和技能组列表
         * 传对应技能组ID时获取该技能组的空闲坐席列表
         * @param  {String}   skillGroupId [技能组ID]
         * @param  {Function} callback     [回调函数]
         */
        agentList: function(skillGroupId, callback, userData) {
            var data = {
                "event": "agentList"
            };
            if (skillGroupId != undefined && skillGroupId != null) {
                data.skillGroupId = skillGroupId;
            }
            ccbarRequestSend(this.getContextPath() + "/yc-ccbar/AgentEvent?action=AgentList", cmdJson(data), function(result) {
                if ($.isFunction(callback)) {
                    callback(result);
                }
            }, {
                jsonpCallback: 'JSONcallbackAgentList'
            });
        },
        /**
         * [callerList 获取当前坐席的外显号码列表]
         * @param  {Function} callback [回调函数]
         */
        callerList: function(callback) {
            var data = {
                "event": "callerList"
            };
            ccbarRequestSend(this.getContextPath() + "/yc-ccbar/AgentEvent?action=CallerList", cmdJson(data), function(result) {
                ccbarDebugger(result.msg);
                if (result.data.result.callers) {
                    CallControl.callers = result.data.result.callers
                }else if(result.data.result.callerList){
                    var temp = []
                    for (var i = 0; i < result.data.result.length; i++) {
                        temp.push(result.data.result[i].prefixNum)
                    }

                    result.data.result.callers = temp;
                }
                if ($.isFunction(callback)) {
                    callback(result);
                }
            }, {
                jsonpCallback: 'JSONcallbackCallerList'
            });
        },
        /**
         * 获取坐席信息
         */
        getAgentInfo: function(callback) {
            this.sendCTICommand({
                messageId: ccbar_config['command']['getAgentInfo']
            }, callback);
        },
        /**
         * [autoAnswer description]
         * @param  {[type]}   autoFlag [description]
         * @param  {Function} callback [description]
         * @return {[type]}            [description]
         */
        autoAnswer: function(autoFlag, callback) {
            var data = {
                "autoAnswer": autoFlag
            };

            ccbarRequestSend(this.getContextPath() + "/yc-ccbar/AgentEvent?action=AutoAnswer", cmdJson(data), function(result) {
                if ($.isFunction(callback)) {
                    callback(result);
                }
            }, {
                jsonpCallback: 'JSONcallbackAutoAnswer'
            });
        },
        /**
         * [consultIVR description]
         * @param  {[type]}   called   [流程号,电销任务直接填 '' ]
         * @param  {[type]}   source   [来源,电销任务填写 task ]
         * @param  {[type]}   userData [description]
         * @param  {Function} callback [description]
         * @return {[type]}            [description]
         */
        consultIVR: function(called, source, userData, callback) {
            this.sendCTICommand({
                messageId: ccbar_config['command']['consultIVR'],
                called: called,
                source: source || '',
                userData: userData || {}
            }, callback);
        },
        //阻塞
        block: function(options) {
            var defaultOpts = {
                messageId: 'cmdBlock',
                called: '',
                reason: '',
                userData: {}
            }
            var data = $.extend({}, defaultOpts, options);
            var callback = data.callback;
            delete data.callback;
            this.sendCTICommand(data, callback);
        },
        //解除阻塞
        unblock: function(options) {
            var defaultOpts = {
                messageId: 'cmdUnBlock',
                called: '',
                reason: '',
                userData: {}
            }
            var data = $.extend({}, defaultOpts, options);
            var callback = data.callback;
            delete data.callback;
            this.sendCTICommand(data, callback);
        },
        //拦截(班长)
        intercept: function(options) {
            var defaultOpts = {
                messageId: 'cmdIntercept',
                called: '',
                reason: '',
                userData: {}
            }
            var data = $.extend({}, defaultOpts, options);
            var callback = data.callback;
            delete data.callback;
            this.sendCTICommand(data, callback);
        },
        //拦截(坐席)
        interceptCall: function(options) {
            var defaultOpts = {
                messageId: 'cmdInterceptCall',
                called: '',
                reason: '',
                userData: {}
            }
            var data = $.extend({}, defaultOpts, options);
            var callback = data.callback;
            delete data.callback;
            this.sendCTICommand(data, callback);
        },
        //接入排队呼叫
        getCall: function(options) {
            var defaultOpts = {
                messageId: 'cmdGetCall',
                called: '',
                reason: '',
                userData: {}
            }
            var data = $.extend({}, defaultOpts, options);
            var callback = data.callback;
            delete data.callback;
            this.sendCTICommand(data, callback);
        },
        //强拆
        clearForceCall: function(options) {
            var defaultOpts = {
                messageId: 'cmdClearForceCall',
                called: '',
                reason: '',
                userData: {}
            }
            var data = $.extend({}, defaultOpts, options);
            var callback = data.callback;
            delete data.callback;
            this.sendCTICommand(data, callback);
        },
        //获取延迟
        ping: function() {},
        // 静音
        mute: function(flag) {
             //判断是否webrtc
             if(CallControl.phoneType == '3'){
                // 外置话机
                var defaultOpts = {
                        messageId: 'cmdRetrieveCall',
                    }
                CallControl.softMute=!CallControl.softMute
                if(CallControl.softMute){
                    defaultOpts = {
                        messageId: 'cmdMuteCall',
                    }
                }
                var data = $.extend({}, defaultOpts);
                var callback = data.callback;
                delete data.callback;
                this.sendCTICommand(data, callback);
            }
            if (window.yqWebrtcApp && yqWebrtcApp.Device) {
                if (!yqWebrtcApp.Device.microStatus()) {
                    CallControl.webrtc.microOn()
                } else {
                    CallControl.webrtc.microOff()
                }
                return;
            }
            //静音
            if(flag === false){
                CallControl.unholdCall()
                return;
            }
            if (!CallControl.getFunc('mutecall')) return;
           
        },
        // 视频外呼
        videoMakeCall: function(called, caller, userData, callback, callType, skillId) {
            this.makeCall(called, caller, userData, callback, callType, skillId, true)
        },
        // 视频转移
        videoTransfer: function(called, displayNumber, skillId, callType, userData, callback) {
            this.sstransfer(called, displayNumber, skillId, callType, userData, callback, true)
        },
        // 视频咨询
        videoConsultation: function(called, displayNumber, skillId, callType, userData, callback) {
            this.consultation(called, displayNumber, skillId, callType, userData, callback, true)
        },
        //主动获取坐席信息
        queryAgentInfo:function(callback){

            this.sendCTICommand({"messageId":"cmdQueryAgentInfo",sendAgentState:1}, callback);
        },
        isWebsoket:function(){
            return CallControl.getConfig().websocket ||  CallControl.config.websocket
        },
        updateStatus:function(state,func){
            var funcMask = CallControl.getFunc();
            for (var i in funcMask) {
                funcMask[i] = func && func[i]==true?true: false;
            }

            var resetStatus = {
                "workMode": "all",
                "stateDesc": ccbar_config.agentStatus[state] && ccbar_config.agentStatus[state].name?ccbar_config.agentStatus[state].name:'未知',
                "notifyContent": "",
                "state": state,
                "resultDesc": null,
                "funcMask": funcMask
            }
            
            ccbarEvent.emitEvent('agentStateSync', resetStatus)

            CallControl.state = resetStatus.state;
            CallControl.workModeType = resetStatus.workMode;
            CallControl.funcMask = resetStatus.funcMask;
            CallControl.onRespAgentState(resetStatus);

        },
        getBusyType:function(callback){
            CallControl.ccbarInit(function(res){
                if(res.state == 1){
                    callback && callback(res.data.busyTypes)
                }else{
                    callback && callback([]) 
                }
            })
        },
        //主动获取坐席信息
        dcData:function(data,callback){
            this.sendCTICommand({"messageId":"cmdDcData",data:data}, callback);
        },
    }
})();


/*监控*/
CallControl.monitor = {
    getSkillGroup: function(succcallback) {
        var data = {};
        ccbarRequestSend(CallControl.getContextPath() + "/yc-ccbar/AgentEvent?action=monitorGroup", cmdJson(data), function(result) {
            if (succcallback && $.isFunction(succcallback)) {
                succcalback(result);
            }
        }, {
            jsonpCallback: "jsonpCallbackMonitorGroup",
            error: function(XMLHttpRequest, textStatus, errorThrown) {
                if (failcallback && $.isFunction(failcallback)) {
                    failcallback(XMLHttpRequest, textStatus, errorThrown);
                }
                if (textStatus == "timeout") { // 请求超时
                    ccbarDebugger(data.messageId + ' timeout!');
                }
            }
        });
    },
    getAgents: function(skillGroupId, succcallback) {
        var data = {
            skillGroupId: skillGroupId || ''
        };
        ccbarRequestSend(CallControl.getContextPath() + "/yc-ccbar/AgentEvent?action=monitorAgent", cmdJson(data), function(result) {
            if (succcallback && $.isFunction(succcallback)) {
                succcallback(result);
            }
        }, {
            jsonpCallback: "jsonpCallbackMonitorAgent",
            error: function(XMLHttpRequest, textStatus, errorThrown) {
                if (failcallback && $.isFunction(failcallback)) {
                    failcallback(XMLHttpRequest, textStatus, errorThrown);
                }
                if (textStatus == "timeout") { // 请求超时
                    ccbarDebugger(data.messageId + ' timeout!');
                }
            }
        });
    }
};

/*busi业务层面接口*/
CallControl.busi = {

    alerting: function(callEvent) {},
    /*接通*/
    connected: function(callEvent) {
        if (callEvent == null) callEvent = {};
        ccbarDebugger("Event notify ->callEvent:" + JSON.stringify(callEvent));
        callEvent.event = "established";
        ccbarRequestSend(CallControl.getContextPath() + "/yc-ccbar/callEvent?action=event", callEvent, function(result) {
            if (result.state == 1) {
                //调用添加的回调方法
                $.isFunction(ccbar_busi.callbacks['established']) && ccbar_busi.callbacks['established'](result.data);
                return false;


                ccbarDebugger(JSON.stringify(result));
            } else {
                ccbar_popup.alert(result.msg, {
                    icon: 5
                });
            }
        }, {
            jsonpCallback: 'EVENTestablished'
        });
    },
    /*呼叫结束*/
    disconnect: function(callEvent) {
        this.call("callcleared", callEvent);
    },

    /**
     * [sdk用签入接口]
     * @param  {String}   entId     [企业ID]
     * @param  {String}   loginKey  [登陆密钥，32位长度]
     * @param  {String}   productId [企业订购的产品，由云呼平台提供]
     * @param  {String}   agentId [账号]
     * @param  {String}   token  [密码-MD5]
     * @param  {String}   phone     [话机号]
     * @param  {Function} callback  [回调函数]
     */
    agentLogon: function(entId, loginKey, productId, agentId, token, phone, callback, dataObj) { //坐席签入事件，这个在ccbar单独应用场景中使用
        CallControl.agentLogon(entId, loginKey, productId, agentId, token, phone, callback, dataObj)
    },
    /*获取坐席列表*/
    agentList: function(callback) {
        CallControl.agentList(callback);
    },
    /*获取外显号码列表*/
    callerList: function(callback) {
        CallControl.callerList(callback);
    },
    /*统一的话务处理接口*/
    call: function(eventId, callEvent) {
        if (callEvent == null) callEvent = {};
        ccbarDebugger("Event notify -> event:" + eventId + ",callEvent:" + JSON.stringify(callEvent));
        callEvent.event = eventId;
        ccbarRequestSend(CallControl.getContextPath() + "/yc-ccbar/callEvent?action=event", callEvent, function(result) {
            if (result.state == 1) {
                ccbarDebugger(JSON.stringify(result));

                //调用添加的回调方法
                $.isFunction(ccbar_busi.callbacks[eventId]) && ccbar_busi.callbacks[eventId](result.data);
                return false;

            } else {
                ccbar_popup && ccbar_popup.alert(result.msg, {
                    icon: 5
                });
            }
            return result;
        }, {
            jsonpCallback: 'EVENT' + eventId
        });
    }
};
/*busi业务层面接口 end*/

CallControl.sdk = ccbarsdk;


CallControl.wsChat = {
    sendMessage: function(sessionId, msgContent, msgType, callback) {
        var data = {
            sessionId: sessionId,
            msgContent: msgContent,
            msgType: msgType || 'text'
        };
        var _data = cmdJson(data);
        CallControl.ws.send('mediaEvent', 'sendMessage', _data);
        callback && callback();
    }

}


/**
 * [chatRequestSend ccbar请求发送方法]
 * @param  {String} url             [请求路径]
 * @param  {Object} sendData        [发送的请求数据对象]
 * @param  {Function} successCallback [成功的回调函数]
 * @param  {Object} option          [额外的参数对象]
 */
function chatRequestSend(url, sendData, successCallback, option) {
    var defaultOpts = {
        dataType: 'jsonp',
        jsonp: "callbackFunc",
        jsonpCallback: "jsonpChatCallback",
        contentType: "application/x-www-form-urlencoded; charset=UTF-8",
        url: url,
        data: sendData,
        timeout: 5000, //5秒超时

        // dataType: 'json',
        // type:'post',
        // jsonp: "callbackFunc",
        // jsonpCallback: "jsonpChatCallback",
        // contentType: "application/x-www-form-urlencoded; charset=UTF-8",
        // url: url,
        // data: sendData,
        // timeout: 5000, //5秒超时
        success: function(result,state,response) {
            ccbarDebugger('---->',result,state,response)
            if (successCallback) {
                result = result || {state:1}
                successCallback(result,state,response)
            }
            if (!result.state && result.data && result.data.code == 'Fail') {
                ccbarDebugger('请求失败:' + result.data.content);
            }
        },
        error:function(result,state,response){
            ccbarDebugger('err---->',result,state,response)
        }
    };
    var finalOpts = $.extend({}, defaultOpts, option);
    //ccbarDebugger(finalOpts, option)
    $.ajax(finalOpts).then(function(result) {
        option.then && option.then(result);
    });
}


/*sdk应用接口*/
var ccbarsdk = {
    firstLogon: true,
    setting: {
        host: '',
        ip: null,
        port: null,
        entId: null,
        productId: null,
        loginKey: null,
        listenerList: ['logon', 'logoff', 'established', 'clearcall']
    },
    init: function(host, entId, productId, loginKey, callback) {

        CallControl.updateConfig({
            url: host,
            entId: entId,
            productId: productId,
            loginKey: loginKey
        });

        if ($.isFunction(callback)) {
            callback();
        }

    },
    logon: function(agentId, token, phone, callback, switchObj) {
        if (!callback) {
            if (ccbarsdk.firstLogon) {
                ccbarEvent.addEvent("logon", function(rs) {
                    if (rs.resultCode == '000' || rs.resultCode == '105') {
                        if (rs.resultCode == '000' && rs.talkingFlag != '1') CallControl.wakeupSIP();
                    }
                });
                ccbarsdk.firstLogon = false;
            }

            callback = function(rs) {
                if (rs.data.code == 'succ') {

                } else {
                    tipsMsg(rs.data.content);
                }
            }
        }
        CallControl.agentLogon(ccbarsdk.setting.entId,
            ccbarsdk.setting.loginKey,
            ccbarsdk.setting.productId,
            agentId, token, phone,
            callback, switchObj);
    },
    makecall: function(called, caller, callback) {
        var userData = '';

        CallControl.makeCall(called, caller, '', function(result) {
            tipsMsg(ccbarI18nText('正在呼叫'));
            if (result.data.content != 'succ') {
                tipsMsg(result.data.content);
            }
            callback && callback(result);
        });
    },
    /**
     * [cryptMakeCall description]
     * @param  {[type]}   called   [DES加密处理过的被叫号码]
     * @param  {[type]}   caller   [主机号码]
     * @param  {Function} callback [description]
     * @return {[type]}            [description]
     */
    cryptMakeCall: function(called, caller, callback) {

        CallControl.cryptMakeCall(called, caller, '', function(result) {
            tipsMsg('正在呼叫');
            if (result.data.content != 'succ') {
                tipsMsg(result.data.content);
            }
            callback && callback(result);
        });
    },
    registMessageListener: function(listener, callback) {
        ccbarEvent.addEvent(listener, callback);
    }

}
/*sdk应用接口end*/
ccbarEvent.addEvent('evtConsultEnd', function(callEvent) {
    ccbarDebugger('evtConsultEnd');
    ccbarDebugger(callEvent);
    try {
        CallControl.onRespIVR && CallControl.onRespIVR(callEvent);
    } catch (e) {
        ccbarDebugger(e)
    }
});


/*外部调用的ccbar事件响应函数,可通过重写实现自定义的方法*/
CallControl.onEventSessionTimeout = function(data) {
    if (CallControl.isLogoned) {
        ccbar_popup && ccbar_popup.alert(data.content);
    }
}
CallControl.onRespAgentState = function(agentState) {
    ccbarDebugger(agentState.state);
    ccbarDebugger(agentState.stateDesc);
    ccbarDebugger(agentState.funcMask);
}
CallControl.onRespCallEvent = function(callEvent) {
    var callState = callEvent.eventName;
    if (callState == 'alerting') {
        ccbarDebugger("振铃")
    } else if (callState == 'connected') {
        ccbarDebugger("接通")
    } else if (callState == 'disconnect') {
        ccbarDebugger("挂断")
        CallControl.softMute=false
    }
}
CallControl.onRespNotify = function(notify) {
    var notifyAction = notify.srcMessageId;
    if (notify.result == 'Succ' && notify.resultDesc == '000') {
        ccbarDebugger(notify.srcMessageId + ' success')
    } else if (notify.result == 'Fail') {
        ccbarDebugger(notify.srcMessageId + ' Fail');
        ccbarDebugger(notify.srcMessageId + ' Error code:' + notify.resultDesc)
    } else {
        ccbarDebugger(notify.srcMessageId + ' Error code:' + notify.resultDesc)
    }
}
//媒体信息
CallControl.onRespMediaEvent = function(mediaEvent) {
    ccbarDebugger(mediaEvent);
}
//ivr回调
CallControl.onRespIVR = function(ivrEvent) {

}
/*间隔操作函数*/
/**
 *
 * @param fn {Function}   实际要执行的函数
 * @param delay {Number}  延迟时间，也就是阈值，单位是毫秒（ms）
 * @return {Function}     返回一个“去弹跳”了的函数
 */
function debounce(fn, delay) {
    // 定时器，用来 CallControl.setTimeout
    var timer;
    return function() {
        var context = this;
        var args = arguments;
        CallControl.clearTimeout(timer);
        timer = CallControl.setTimeout(function() {
            fn.apply(context, args)
        }, delay);
    }
}
/**
 *
 * @param fn {Function}   实际要执行的函数
 * @param delay {Number}  执行间隔，单位是毫秒（ms）
 * @return {Function}     返回一个“节流”函数
 */

function throttle(fn, threshhold) {
    // 记录上次执行的时间
    var last;
    // 定时器
    var timer;
    // 默认间隔为 250ms
    threshhold || (threshhold = 250)
    // 返回的函数，每过 threshhold 毫秒就执行一次 fn 函数
    return function() {
        // 保存函数调用时的上下文和参数，传递给 fn
        var context = this
        var args = arguments
        var now = +new Date()
        // 如果距离上次执行 fn 函数的时间小于 threshhold，那么就放弃
        // 执行 fn，并重新计时
        if (last && now < last + threshhold) {
            CallControl.clearTimeout(timer)
            // 保证在当前时间区间结束后，再执行一次 fn
            timer = CallControl.setTimeout(function() {
                last = now
                fn.apply(context, args)
            }, threshhold)
            // 在时间区间的最开始和到达指定间隔的时候执行一次 fn
        } else {
            last = now
            fn.apply(context, args)
        }
    }
}

CallControl.longPollingTimeout = function() {
    // ccbar_popup && ccbar_popup.alert('坐席话务状态已失效，请重新签入！')
}
//ccbar初始化方法v2.0
CallControl.init = function(options) {
    var defaultOpts = {
        debug: true, //是否输出日志
        url: '', //服务器地址
        entId: '', //企业id
        productId: '', //产品id
        loginKey: '', //秘钥
        agentId: '', //坐席工号
        password: '', //密码
        token: '', //md5加密的密码,优先级比password高
        dn: '', //话机
        readyMode: '', //签入后忙还是闲
        workMode: '', //工作模式
        autoAnswer: null, //自动应答
        autoLogin: false, //自动签入
        event: {},
        layout: true
    }
    var opts = $.extend({}, defaultOpts, options);
    //根据坐席类型判断是否需要关闭摄像头 agentType（坐席类型）：1、语音坐席 2、IM坐席（在线） 4、融合坐席 5、离线 6、5G坐席 7、5G融合坐席 8、app坐席
    if(typeof(opts.agentType)!= 'undefined'){
        var flag = opts.agentType == 6 || opts.agentType == 7;
        opts.webrtc = $.extend({},{cameraEnabled:flag},opts.webrtc)
    }
    CallControl.updateConfig(opts); //更新配置

    ccbarLayout.init(opts); //初始化界面
    if(CallControl.isSdkInit) return;

    /*注册绑定事件*/
    if(opts.event){
        for(var key in opts.event){
            try{
                ccbarEvent.addEvent(key,opts.event[key])
            }catch(e){
                ccbarDebugger(e)
            }
        }
    }
    CallControl.isSdkInit = true;
}

//默认的事件回调
var ccbarEventEmit = function(eventName, event) {
    //CallControl.getConfig()['event'][eventName] && CallControl.getConfig()['event'][eventName](event);
}

var ccbarEventCallBack = {
    //签入
    logon: function(event) {
        if (event.resultCode == '000' || event.resultCode == '105') {
            if (event.resultCode == '000' && event.talkingFlag != '1') CallControl.wakeupSIP();
            if (CallControl.getConfig().layout) {
                $('.ccbar-system-mod .ccbar-system-toggle,.ccbar-system-mod .ccbar-queue-tag').show()
            }
            if (CallControl.isWebsoket()) {
                CallControl.ws.lastMsg = null;
                ccbarDebugger('心跳初始化')
            } else {
                ccbarLongPollingProtect.start();
            }
        }
        ccbarEventEmit('logon', event);
    },
    //签出
    logoff: function(event) {
        if (event.resultCode == '000' || event.resultCode == '117' || event.resultCode == '403' || event.resultCode == '999') {
            CallControl.closeSIP();
            CallControl.agentId = ''
            if (CallControl.getConfig().layout) {
                $('.ccbar-system-mod .ccbar-system-toggle,.ccbar-system-mod .ccbar-queue-tag').hide()
            }
            if (CallControl.isWebsoket()) {
                CallControl.ws.disconnect();
            } else {
                ccbarLongPollingProtect.stop();
            }
            if (event.resultCode == '117') {
                tipsMsg && tipsMsg('[117]'+ccbarI18nText('坐席已签出'))
            }
        }
        ccbarEventEmit('logoff', event);
    },
    notify: function(event) {
        if (event.resultCode == '117' || event.resultCode == '101') {
            if (CallControl.isWebsoket()) {
                CallControl.ws.disconnect();
            } else {
                ccbarLongPollingProtect.stop();
            }
            var errMsg = ccbar_config.error[event.resultCode] || ccbarI18nText('错误代码') + event.resultCode
            tipsMsg && tipsMsg(errMsg);
            //
            if (event.resultCode == '117') {
                ccbar_popup && ccbar_popup.alert(errMsg)
            }
        }
    },
    //坐席状态变更
    agentStateSync: function(event) {
        ccbarEventEmit('agentStateSync', event);
    },
    //呼叫到达
    evtNetWorkReached: function(event) {
        ccbarEventEmit('evtNetWorkReached', event);
    },
    //振铃
    evtAltering: function(event) {
        ccbarEventEmit('evtAltering', event);
    },
    //接通
    evtConnected: function(event) {
        ccbarEventEmit('evtConnected', event);
        CallControl.softMute=false
        $('#yqNumberInputBox').hide()
        $('#yqNumberInputBoxTalk').show()
    },
    //挂断
    evtDisConnected: function(event) {
        ccbarEventEmit('evtDisConnected', event);
        $('#yqNumberInputBox').show()
        $('#yqNumberInputBoxTalk').hide()

        $('#ccbarVoiceView,#ccbarVoiceKeyboard').show();
        $('#ccbarVideoView,#ccbarVoiceTalking,#ccbarWebrtcRing').hide();
    },
    //咨询开始
    evtConsultedBegin: function(event) {
        ccbarEventEmit('evtConsultedBegin', event);
    },
    //咨询结束
    evtConsultedEnd: function(event) {
        ccbarEventEmit('evtConsultedEnd', event);
    },
    //报错
    error: function(event) {}
}

function uuid() {
    var s = [];
    var hexDigits = "0123456789abcdef";
    for (var i = 0; i < 32; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
    }
    s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
    // s[8] = s[13] = s[18] = s[23] = "-";

    var uuid = s.join("");
    uuid = uuid.replace('-', '');
    return uuid;
}

//事件初始化
function ccbarEventInit() {
    for (var key in ccbarEventCallBack) {
        if ($.isFunction(ccbarEventCallBack[key])) ccbarEvent.addEvent(key, ccbarEventCallBack[key])
    }
    window.addEventListener && window.addEventListener("online", function(e) {
        ccbarErrorSubmit.addError('network', 'onLine')
        if(CallControl.isLogoned){
            if(CallControl.isWebsoket() && !CallControl.ws.isWsConnect()) CallControl.updateStatus && CallControl.updateStatus('SERVICEOFFLINE')   
        }else{
            CallControl.updateStatus && CallControl.updateStatus('LOGOFF')   
            //ccbarResetState()
        }
        var msg = CallControl.isLogoned?ccbarI18nText('网络连接已恢复,自动示忙'):ccbarI18nText('网络连接已恢复')
        tipsMsg && tipsMsg(msg);
        e.resultCode = '000'
        e.resultText = msg
        ccbarEvent.emitEvent('networkOnline', e)
        //修改话务条状态
        CallControl.updateStatus && CallControl.updateStatus('LOGOFF')
       if(CallControl.isLogoned) CallControl.agentNotReady();//自动置忙
        CallControl.networkOffline = false;
        if(CallControl.isWebsoket() && CallControl.isLogoned) {
            CallControl.ws.retry()
        }else if(CallControl.isLogoned){
            CallControl.longPolling() 
        }
        CallControl.ws.showNetworkCheck();

    }, false);
    window.addEventListener && window.addEventListener("offline", function(e) {
        var msg = ccbarI18nText('网络连接中断')

        tipsMsg && tipsMsg(msg);
        CallControl.networkOffline = true;

        //如果是ws的直接断开ws,待网络恢复时重新发起链接
        if(CallControl.isWebsoket() && CallControl.isLogoned) {
            CallControl.ws.disconnect()
            CallControl.updateStatus && CallControl.updateStatus('SERVICEOFFLINE')   
        }
        
        e.resultCode = '999'
        e.resultText = msg

        ccbarEvent.emitEvent('networkOffline', e)

        ccbarErrorSubmit.addError('network', 'Offline')
        CallControl.ws.showNetworkCheck();

        //修改话务条状态
        CallControl.updateStatus && CallControl.updateStatus('NETWORKOFFLINE')
    }, false);

    var list = ['webrtcFail', 'webrtcError', 'webrtcVideoChatFail', 'webrtcVideoChatError',
        'webrtcDeviceFail','webrtcDeviceError',
     'notifyError','ccbarHttpFail','ccbarWsClose','ccbarWsOpen','ccbarWsRetry','networkOffline','networkOnline']
    for (var i = 0; i < list.length; i++) {
        (function(type){
            ccbarEvent.addEvent(type, function(e){
                e.resultCode = e.resultCode || e.msgCode;
                e.resultText = e.resultText || e.msgTip || e.message;
                e.srcMessageId = e.srcMessageId || e.type || e.msgType || type;
                if(type.startWith('webrtc')){
                    e.resultText = '[WEBRTC]'+e.resultText
                    CallControl.webrtc.logger.add(e)
                }else if(type.startWith('webrtc')){
                    e.resultText = '[CTI]'+e.resultText
                }
                e.resultText && tipsMsg(e.resultText)
                ccbarEvent.emitEvent('requestNotify',{
                    type:type,
                    data:e
                })
            })
        })(list[i])
    }
}

//长连接心跳保护
var ccbarLongPollingProtect = {
    flag: false,
    event: null,
    start: function() {
        ccbarLongPollingProtect.flag = true;
        ccbarLongPollingProtect.event = CallControl.setTimeout(function() {
            if (longPollAjax && longPollAjax.readyState == 1) {
                ccbarDebugger('===>longPolling is sending');
                return;
            }
            if (CallControl.isLogoned || CallControl.isConnect) {
                ccbarDebugger('ccbar longPolling protect[SEND]');
                CallControl.longPolling();
            }
            if (ccbarLongPollingProtect.flag) ccbarLongPollingProtect.start();

        }, 20 * 1000)
    },
    stop: function() {
        ccbarDebugger('ccbar longPolling protect[STOP]');
        CallControl.clearTimeout(ccbarLongPollingProtect.event);
        ccbarLongPollingProtect.event = null;
    }
}

$(function(){
    ccbarEventInit(); //初始化事件
})


//界面相关
var ccbarLayout = (function() {
    return {
        init: function(options) {
            ccbar_plugin.init('', '', options.layout);
            var rememberAgentId = window.localStorage && localStorage.getItem('ccbarAgentId');
            $('#ccbarLogin [data-ccbar-input="agentId"]').val(rememberAgentId);
            $('#ccbarLogin [data-ccbar-input="agentId"]').val(options.agentId);
            $('#ccbarLogin [data-ccbar-input="psw"]').val(options.password);
            $('#ccbarLogin [data-ccbar-input="dn"]').val(options.dn);
            if (options.layout && options.background) {
                $('#ccbar .ccbar-system-mod').css('background', options.background)
            }
            if (options.autoLogin) {
                CallControl.agentLogon()
            }
            //不再重新注册事件
            if(CallControl.isSdkInit) return;
            ccbarEvent.addEvent('evtAltering',ccbarLayout.evtAlteringRing)
            ccbarEvent.addEvent('evtConnected',ccbarLayout.evtConnected)
            ccbarEvent.addEvent('evtDisConnected',ccbarLayout.evtDisConnected)
        },
        evtAlteringRing:function(e){
            if(!audio_player.inited) audio_player.init()
            var ring = CallControl.getConfig().alteringRing;
            if(e.createCause!= 6 && e.createCause!= 29 && ring){
                var audioUrl = ring === true ? CallControl.getContextPath() +'/easitline-static/mp3/defaultAltering.mp3' : ring;
                var ringTimeout = CallControl.getConfig().evtAlteringRingTimeout || 60;
                audio_player.play(audioUrl,true,ringTimeout)
            }
        },
        evtConnected:function(e){
          if(CallControl.getConfig().alteringRing) audio_player.stop()
            $('.ccbar-webrtc-network-info').show()
        },
        evtDisConnected:function(e){
            $('.ccbar-webrtc-network-info').hide()

          if(CallControl.getConfig().alteringRing) audio_player.stop()
            var ring = CallControl.getConfig().disconnectRing;
            if(ring){
                audio_player.stop()
                var audioUrl = ring === true ? CallControl.getContextPath() +'/easitline-static/mp3/disconnect.mp3' : ring;
                audio_player.play(audioUrl, false, 60)
            }
        }
    }
})();

Date.prototype.format = function(format) {
    var o = {
        "M+": this.getMonth() + 1, // month
        "d+": this.getDate(), // day
        "h+": this.getHours(), // hour
        "m+": this.getMinutes(), // minute
        "s+": this.getSeconds(), // second
        "q+": Math.floor((this.getMonth() + 3) / 3), // quarter
        "S": this.getMilliseconds() // millisecond
    };
    if (/(y+)/.test(format))
        format = format.replace(RegExp.$1, (this.getFullYear() + "")
            .substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(format))
            format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
    return format;
};
CallControl.logAjax = function(url,data,callback){
    $.ajax({
        type:'post',
        url:url,
        data:data,
        header:{
            'Access-Control-Allow-Origin':'*'
        },
        crossDomain: true,
        success:function(result,state,response){
            callback && callback(result,state,response)
        },
        error:function(result,state,response){
            ccbarDebugger('result,state,response',result,state,response)
           if(response.status == 200) callback && callback(result,state,response)
        },
    })
}


/*消息回调处理--用做消息确认和错误反馈*/
var ccbarErrorSubmit = (function() {
    return {
        clock: null,
        errorList: [], //错误提示
        reciveList: [], //消息确认
        addError: function(msgType, msgContent) {
            var data = {
                type: msgType,
                content: msgContent,
                time: new Date().format('hhmmss')
            }
            ccbarErrorSubmit.errorList.push(data)
            if(ccbarErrorSubmit.errorList.length>10){
                ccbarErrorSubmit.errorList = ccbarErrorSubmit.errorList.slice(-10)
            }
            ccbarErrorSubmit.submit()
        },
        addConfirm: function(sequence) { //消息确认只保留10个
            ccbarErrorSubmit.reciveList.push(sequence)
            if(ccbarErrorSubmit.reciveList.length>10){
                ccbarErrorSubmit.reciveList = ccbarErrorSubmit.reciveList.slice(-10)
            }
            ccbarErrorSubmit.submit()
        },
        submit: function () {
            if (ccbarErrorSubmit.errorList.length == 0 && ccbarErrorSubmit.reciveList.length == 0) return;
            var data = {}
            if (ccbarErrorSubmit.errorList) data.errorList = ccbarErrorSubmit.errorList;
            //if (ccbarErrorSubmit.errorList) data.errorList = ccbarErrorSubmit.errorList.length;
            if (ccbarErrorSubmit.reciveList) data.reciveList = ccbarErrorSubmit.reciveList
            if (ccbarErrorSubmit.clock) CallControl.clearTimeout(ccbarErrorSubmit.clock)
            ccbarErrorSubmit.clock = CallControl.setTimeout(function() {
                var startTime = new Date().getTime()
                chatRequestSend(CallControl.getContextPath() + "/yc-ccbar/ConsoleLog?action=log", cmdJson(data), function (result) {
                    ccbarEvent.emitEvent('ccbarHttpSucc', {
                        time: new Date().getTime(),
                        delay: new Date().getTime() - startTime,
                        onLine: true
                    })

                    if (result.state == 1 || response.status == 200) {
                        ccbarErrorSubmit.errorList = []
                        ccbarErrorSubmit.confirmList = []
                    }
                }, {
                    // dataType:'json',
                    // methods:'post',
                    error: function (XMLHttpRequest, textStatus, errorThrown) {

                        if (!CallControl.networkOffline) ccbarErrorSubmit.submit() //尝试重新发起
                        if (textStatus == "timeout") { // 请求超时
                            ccbarDebugger('error log timeout!');
                        }
                    }
                });
            }, 5000)
        }
      
    }
})()

/*webrtc*/
var WebRtcServer, WebRtcServerHandler, WebRtcVideoHandler,WebRtcVolteHandler, WebRtcDevice,isWebrtcInit;
function ccbarWebrtcLog(e){

}
CallControl.webrtc = (function() {
    return {
        isVideo: false,
        isLibInit:false,
        logger:{
            add:function(data){},
            clear:function(data){},
            show:function(data){},
        },
        initLibs: function(callback) {
            if(location.protocol!='https:'){
                ccbar_popup && ccbar_popup.alert('当前环境不是https,无法正常使用webrtc话机')
                return;
            }

            if(CallControl.webrtc.isLibInit) return

            if (CallControl.getConfig().dontWakeUpPhone) return;
            CallControl.webrtc.isLibInit = true;
            var scriptUrls = ['/yc-ccbar/webrtc/js/tripledes.js', '/yc-ccbar/webrtc/js/mode-ecb.js', '/yc-ccbar/webrtc/dist/yqwebrtc-sdk-3.0.js']
            var html = '';
            for (var i = 0; i < scriptUrls.length; i++) {
                var script = document.createElement('script');
                script.src = CallControl.getContextPath() + scriptUrls[i];
                script.async = false;
                script.onload = CallControl.webrtc.checkLibs(callback);
                document.body.appendChild(script);
            }
        },
        // 检查
        checkLibs: function(callback) {
            return function() {
                if (typeof(yqWebrtcApp) != 'undefined' && typeof(CryptoJS) != 'undefined' && CryptoJS.mode.ECB) {
                    callback && callback();
                }
            }
        },
        loadHtml: function(id, url, callback) {
            $(id).load(url, callback)
        },
        sendDTMF: function(key) {
            if (CallControl.getFunc('clearcall')) WebRtcVideoHandler.sendDTMF(key);
        },
        microOff: function() {
            yqWebrtcApp.Device.turnOffMicro();
            $('#yqBtnMicro').addClass('yqBtnMicroOff')
            tipsMsg('关闭麦克风')
        },
        microOn: function() {
            yqWebrtcApp.Device.turnOnMicro();
            $('#yqBtnMicro').removeClass('yqBtnMicroOff')
            tipsMsg('打开麦克风')

        },
        SpeakerOff: function() {
            $('#yqBtnSpeaker').addClass('yqBtnSpeakerOff')
            tipsMsg('关闭麦克风')
            yqWebrtcApp.Device.turnOffMicro();

        },
        SpeakerOn: function() {
            $('#yqBtnSpeaker').removeClass('yqBtnSpeakerOff')
            tipsMsg('打开麦克风')
            yqWebrtcApp.Device.turnOnMicro();
        },
        wakeupSIP: function(data) {
            if (!CallControl.webrtcConfig || CallControl.getConfig().dontWakeUpPhone) return;
            //判断是话机号相同
            if(WebRtcServer && WebRtcServer.isLive()){
                if(CallControl.webrtcConfig.account == WebRtcServer.getUserInfo().rtcAccountID){
                    return;
                } else{
                    WebRtcServer.stop();
                }
            }else{
                WebRtcServer && WebRtcServer.stop();
            }
            if (!window.yqWebrtcApp) {
                CallControl.webrtc.initLibs(CallControl.webrtc.initWebrtc)
            } else {
                CallControl.webrtc.initWebrtc()
            }
        },
        initWebrtc: function(data) {
            CallControl.webrtc.initLayout()
            var baseConfig = $.extend({
                autoAccept: true,
                reconnectDuration: 2000,
                heartDuration:10000,//心跳间隔
                reconnectCount: 35,
                log: true
            }, {
                sender: 'YUNQU_MOBILEAGENT'
            }, CallControl.webrtcConfig,CallControl.getConfig().webrtc, data)

            //if(WebRtcServer && WebRtcServer.isLive()) WebRtcServer.stop()

            //执行check,打开对应权限
            //没有摄像头时
            CallControl.webrtc.errorContent = '';
            if(baseConfig.cameraEnabled === false){
                yqWebrtcApp.Device.setCameraEnabled(false)
                yqWebrtcApp.Device.check('audio');
            }else{
                yqWebrtcApp.Device.setCameraEnabled(true)
                yqWebrtcApp.Device.check();
            }
            
            
            //设置默认没有
            if(baseConfig.noVideoImg){
                yqWebrtcApp.Device.setDefaultNoVideoImg(baseConfig.noVideoImg)
            }
            WebRtcServer = new yqWebrtcApp.Server(baseConfig);
            // WebRtcServer = yqWebrtcApp.config(baseConfig);
            WebRtcDevice = yqWebrtcApp.Device;

            //volte
            WebRtcVideoHandler = WebRtcServer.createVolteCall({
                autoAccept: true
            })

            //绑定事件
            CallControl.webrtc.initEvent()

            WebRtcServer.start()

        },
        initEvent:function(){
            

            //视频通话事件
            var videoChatFunc = {
                onRing:'webrtcVideoChatRing',
                onHangup:'webrtcVideoChatHangup',
                onFail:'webrtcVideoChatFail',
                onRefuse:'webrtcVideoChatRefuse',
                onError:'webrtcVideoChatError',
                onTimeout:'webrtcVideoChatTimeout',
                onConnect:'webrtcVideoChatConnect',
                onTimer:'webrtcVideoChatTimer',
                onWaiting:'webrtcVideoChatWaiting',
                onMediaStatus:'webrtcVideoChatMediaStatus',
                onCallTypeChange:'webrtcCallTypeChange',
                onStats:'webrtcStats'
            }
            for(let key2 in videoChatFunc){
                WebRtcVideoHandler && WebRtcVideoHandler[key2]&& WebRtcVideoHandler[key2](function(e){
                    if(key2 == 'onConnect'){
                        if(e.msgData.callType == 'video'){
                             CallControl.webrtc.isVideo = true;
                            ccbarEvent.emitEvent('webrtcVideoChatConnect',e)
                        }else{
                            CallControl.webrtc.isVideo = false;
                            ccbarEvent.emitEvent('webrtcVoiceChatConnect',e)
                        }
                    }else{
                        ccbarEvent.emitEvent(videoChatFunc[key2],e)
                    }

                })
            }

            

            //sdk事件
            var sdkFunc = {
                onReady:'webrtcReady',
                onStop:'webrtcStop',
                onFail:'webrtcFail',
                onState:'webrtcState',
                onError:'webrtcError',
                onOnline:'webrtcOnline',
                onOffline:'webrtcOffline',
                onNetwork:'webrtcNetwork',
                onCallStatus:'webrtcCallStatus',
                onNetworkData:'webrtcNetworkData',
                onNetworkType:'webrtcNetworkType'
            }
            for(let key in sdkFunc){
               // if(WebRtcServer && WebRtcServer[key]) ccbarDebugger('注册'+key);
                WebRtcServer && WebRtcServer[key] && WebRtcServer[key](function(e){
                    ccbarEvent.emitEvent(sdkFunc[key],e)
                })
            }

            if(isWebrtcInit) return;

            //媒体上下文事件
            var deviceFunc = {
                ready:'webrtcDeviceReady',
                open:'webrtcDeviceOpen',
                close:'webrtcDeviceClose',
                error:'webrtcDeviceError',
                fail:'webrtcDeviceFail',
                change:'webrtcDeviceChange',
                share:'webrtcDeviceShare',
                cancelShare:'webrtcDeviceCancelShare',
                status:'webrtcDeviceStatus',
                insert:'webrtcDeviceInsert',
                pull:'webrtcDevicePull',
            }
            for(let key3 in deviceFunc){
                // ccbarDebugger('注册'+deviceFunc[key3]);

                yqWebrtcApp.Device.bind(key3,function(e){
                    if(deviceFunc[key3] == 'webrtcDeviceFail'){
                        e.msgTip = e.msgTip || e.message;
                        e.msgCode = e.msgCode || e.name;
                    }
                    ccbarEvent.emitEvent(deviceFunc[key3],e)

                })

            }

            //sdk
            ccbarEvent.addEvent('webrtcReady',function(e){
                CallControl.webrtc.state = 'online';
                $('#yqMark').addClass('ok');
                $('#yqBtnTelephoneSwitch').removeClass('offline')
                $('#yqCallerNum').text(CallControl.webrtcConfig.account)
                CallControl.ws.showNetworkCheck();

            })

            ccbarEvent.addEvent('webrtcError',function(e){
                tipsMsg('webrtch话机异常' + e.msgTip)
                $('#yqMark').removeClass('ok')
                if(e.msgCode == '10004'){//鉴权失败
                    CallControl.webrtc.errorContent = '鉴权失败'
                }
            })

            ccbarEvent.addEvent('webrtcDeviceFail',function(e){
                if(e.name == 'NotAllowedError' || e.name == "PermissionDeniedError"){//没有授权导致
                    CallControl.webrtc.errorContent = 'NotAllowedError'
                }else if(e.name == 'NotFoundError' || e.name == "DevicesNotFoundError"){//没有授权导致
                    CallControl.webrtc.errorContent = 'DevicesNotFoundError'

                }
            })

            ccbarEvent.addEvent('webrtcState',function(e){
                CallControl.webrtc.state = e.msgData.state ? 'online':'offline';
                CallControl.webrtc.sockesOnline = e.msgData.state;
            })

            ccbarEvent.addEvent('webrtcFail',function(e){
                tipsMsg(e.msgTip)
            })

            ccbarEvent.addEvent('webrtcStop',function(e){
                CallControl.webrtc.state = 'offline';
                CallControl.webrtc.clearVideoView()
                $('#yqBtnTelephoneSwitch').removeClass('talking')
                $('#yqBtnTelephoneSwitch').addClass('offline')
                CallControl.ws.showNetworkCheck();

            })

            ccbarEvent.addEvent('webrtcOffline',function(e){
               
                //ccbarEvent.emitEvent('webrtcOffline',e)

                $('#yqMark').removeClass('ok')
                $('#yqBtnTelephoneSwitch').addClass('offline')
            })

            ccbarEvent.addEvent('webrtcCallStatus',function(e){
                var state = e.msgData && e.msgData.status;
            
                switch(state){
                    case 'audio_change_video': //语音转视频
                    $('#ccbarVideoView').show();
                    $('#ccbarVoiceView').hide();
                    break;

                    case 'video_change_audio': //视频转音频
                    $('#ccbarVideoView,#ccbarVoiceKeyboard').hide();
                    $('#ccbarVoiceView,#ccbarVoiceKeyboard').show();
                    break;
                }
            })

            //device
            ccbarEvent.addEvent('webrtcCallTypeChange',function(e){
                ccbarDebugger(e);
                var state = e.msgData && e.msgData.status;
                if(e.type == 'video'){
                    state  = 'audio_change_video'
                    CallControl.webrtc.isVideo = true;
                }else if(e.type == 'audio'){
                    CallControl.webrtc.isVideo = false;

                    state  = 'video_change_audio'
                }
                
                switch(state){
                    case 'audio_change_video': //语音转视频
                    $('#ccbarVideoView').show();
                    $('#ccbarVoiceView').hide();
                    break;

                    case 'video_change_audio': //视频转音频
                    $('#ccbarVideoView,#ccbarVoiceKeyboard').hide();
                    $('#ccbarVoiceView,#ccbarVoiceTalking').show();
                    break;
                }
            })

            //音频
            ccbarEvent.addEvent('webrtcVoiceChatError',function(e){
                tipsMsg('webrtch话机通话异常')
            })

            ccbarEvent.addEvent('webrtcVoiceChatFail',function(e){
                tipsMsg(e.msgTip)
            })

            ccbarEvent.addEvent('webrtcVoiceConnect',function(e){
                //CallControl.webrtc.isVideo = false;
                ccbarEvent.emitEvent('webrtcVoiceSteam',e)
                //tipsMsg('接通')
                $('#yqBtnTelephoneSwitch').addClass('talking')
                $('#ccbarVideoView,#ccbarVoiceKeyboard').hide();
                $('#ccbarVoiceView,#ccbarVoiceTalking').show();

            })

            ccbarEvent.addEvent('webrtcVoiceHangup',function(e){
                CallControl.webrtc.clearVideoView()
                $('#yqBtnTelephoneSwitch').removeClass('talking')
            })

            //视频
            ccbarEvent.addEvent('webrtcVideoChatError',function(e){
                tipsMsg('webrtch话机通话异常')
            })

            ccbarEvent.addEvent('webrtcVideoChatFail',function(e){
                tipsMsg(e.msgTip)
            })
            
            ccbarEvent.addEvent('webrtcVideoChatConnect',function(e){
                //CallControl.webrtc.isVideo = true;
                CallControl.webrtc.updateVideo(e.msgData.list)

                ccbarEvent.emitEvent('webrtcVideoSteam',e)

                $('#yqBtnTelephoneSwitch').toggle(false);
                $('#yqWebrtcMainContainer').toggle(true)

                $('#yqBtnTelephoneSwitch').addClass('talking')
            })

            ccbarEvent.addEvent('webrtcVideoChatHangup',function(e){
                CallControl.webrtc.clearVideoView()
                $('#yqBtnTelephoneSwitch').removeClass('talking')
            })

            ccbarEvent.addEvent('webrtcStats',function(e){
                if(e.msgData.packetsLostRate>10){
                    $('#webrtcPLstate').css('color','red')
                }else if(e.msgData.packetsLostRate>5){
                    $('#webrtcPLstate').css('color','yellow')
                }else {
                    $('#webrtcPLstate').css('color','green')
                    
                }
                if(e.msgData.video){
                    $('#webrtcPL_freezeCount').text(e.msgData.video.in.freezeCount)
                    $('#webrtcPL_freezeTotalTimes').text(e.msgData.video.in.freezeDuration.toFixed(2))    
                }
                $('#webrtc_delay').text(e.msgData.delayed+'ms')//延迟
                $('#webrtcPL_rate').text(e.msgData.packetsLostRate.toFixed(2)+'%')//总丢包
                $('#webrtcPL_perrate').text(e.msgData.packetsLostPerRate.toFixed(2)+'%')//每秒丢包
                $('#webrtcPL_total').text(e.msgData.packetsLostTotal)
                


            })

            isWebrtcInit = true;
        },
        
        shareScreen: function() {
            var _self = this;
            if (yqWebrtcApp.Device.isShare()) {
                yqWebrtcApp.Device.cancelShare();
                tipsMsg('取消分享')
                _self.isScreening = false;
            } else {
                yqWebrtcApp.Device.shareScreen(WebRtcVideoHandler.getPipe()).then(function(stream) {
                    _self.isScreening = true;
                    tipsMsg('分享屏幕')

                }).catch(function() {

                });
            }
        },

        clearVideoView: function() {
            $('#ccbarVideoView').hide();
            $('#ccbarVoiceView').show();
            $('[data-ccbar-webrtc-video="local"],[data-ccbar-webrtc-video="remote"]').html('');
        },
        closeSIP: function() {
            ccbarDebugger('主动关闭话机')
            window.WebRtcServer && window.WebRtcServer.stop()
            $('#yqMark').removeClass('ok')
            $('#yqBtnTelephoneSwitch').addClass('offline')

        },
        Whiteboard: {
            create: function() {
                window.layer && layer.open({
                    content: '<canvas id="ccbarWBcanvas"></canvas>',
                    closeBtn: 2,
                    type: 1,
                    title: '白板',
                    area: ['640px', '480px'],
                    btn: ['分享/取消分享', '关闭'],
                    success: function() {
                        wbHandeler = WebRtcServer.createWhiteboard({
                            canvas: document.getElementById('ccbarWBcanvas'),
                            width: '640',
                            height: '380',
                            color: '#3cf',
                            background: '#fff',
                            lineWidth: 1
                        })
                    },
                    close: function(index) {
                        CallControl.webrtc.Whiteboard.destory()
                        layer.close(index)
                    },
                    btn1: function() {
                        CallControl.webrtc.Whiteboard.share()
                    },
                    btn2: function() {

                    },
                    btn3: function() {
                       // ccbarDebugger(3)
                    },
                })

            },
            destory: function() {
                wbHandeler.destory && wbHandeler.destory()
            },
            share: function() {
                wbHandeler.share && wbHandeler.share(WebRtcVideoHandler.getPipe(), true)
                tipsMsg('分享白板')
            },
            cancelShare: function() {
                tipsMsg('取消分享')
                wbHandeler.cancelShare && wbHandeler.cancelShare()
            },

        },
        screenshot: {
            share: function() {
                if (yqWebrtcApp.Device.isShare()) {
                    yqWebrtcApp.Device.cancelShare();
                    tipsMsg('取消分享')

                } else {
                    WebRtcVideoHandler && WebRtcVideoHandler.shareScreenshot().then(function(res) {
                        tipsMsg('分享截图')
                    })
                }

            },
            cancel: function() {
                yqWebrtcApp.Device.cancelShare();
            }
        },
        resource: {
            share: function() {
                if (yqWebrtcApp.Device.isShare()) {
                    yqWebrtcApp.Device.cancelShare();
                    tipsMsg('取消分享')

                } else {
                    WebRtcVideoHandler && WebRtcVideoHandler.shareResource().then(function(res) {
                        tipsMsg('分享媒体')
                    })
                }
            },
            cancel: function() {
                yqWebrtcApp.Device.cancelShare();
                tipsMsg('取消分享')

            }
        },
        camera: {
            switch: function() {
                if(WebRtcVideoHandler &&WebRtcVideoHandler.isVirtualPhoto()){
                     CallControl.webrtc.device.cancelVirtualPhoto()
                }else{
                    yqWebrtcApp.Device.switchCamera()
                }
                tipsMsg('切换摄像头')

            },
            turnOn: function() {
                yqWebrtcApp.Device.turnOnCamera()
                tipsMsg('打开摄像头')

            },
            turnOff: function() {
                yqWebrtcApp.Device.turnOffCamera()
                tipsMsg('关闭摄像头')
            }
        },
        setVirtualPhoto:function(url){
            if(!url){
                tipsMsg('设置虚拟头像失败')
            }
            WebRtcVideoHandler && WebRtcVideoHandler.setVirtualPhoto(url)
        },
        cancelVirtualPhoto:function(){
            WebRtcVideoHandler && WebRtcVideoHandler.cancelVirtualPhoto()
        },

        log: function(data) {

        },
        //获取webrtc服务对象
        getWebrtc:function(){
            return typeof(yqWebrtcApp)!='undefined'?yqWebrtcApp:null;
        },
        getHanler:function(){//获取webrtc事件处理器 
            return WebRtcVideoHandler
        },
        check:{
            isLive:function(){//检测服务是否正常连接
                return WebRtcServer && WebRtcServer.isLive();
            }
        },
        
        device:{//webrtc底层媒体上下文控制
            check:function(){
                yqWebrtcApp && yqWebrtcApp.Device.check();
            },
            cameraStatus:function(){//获取摄像头状态，是关还是开
                return yqWebrtcApp && yqWebrtcApp.Device.cameraStatus();
            },
            microStatus:function(){//获取麦克风状态，是关还是开
                return yqWebrtcApp && yqWebrtcApp.Device.microStatus();
            },
            isShare:function(){//判断是否正在桌面分享、资源插播分享、桌面截图分享等
                return yqWebrtcApp.Device.isShare();
            },
            cancelShare:function(){//取消分享
                yqWebrtcApp && yqWebrtcApp.Device.cancelShare();
            },
            switchCamera:function(flag){//摄像头切换,布尔值,为空是自动切换
                if(flag === true){
                    return yqWebrtcApp.Device.turnOnCamera()
                }else if(flag === false){
                    return yqWebrtcApp.Device.turnOffCamera()
                }else{
                    return yqWebrtcApp.Device.switchCamera()
                }
            },
            switchMicro:function(flag){//摄像头切换,布尔值,为空是自动切换
                if(flag === true){
                    return yqWebrtcApp.Device.turnOnMicro()
                }else if(flag === false){
                    return yqWebrtcApp.Device.turnOffMicro()
                }else{
                    return yqWebrtcApp.Device.switchMicro()
                }
            },
            mutedRemote:function(status){//语音通话时设置远端音频静音
                WebRtcServerHandler.mutedRemote();
            },
            setVirtualPhoto:function(url){//设置虚拟头像
                if(!url){
                    tipsMsg('设置虚拟头像失败')
                }
                WebRtcVideoHandler && WebRtcVideoHandler.setVirtualPhoto(url)
            },
            cancelVirtualPhoto:function(){//取消虚拟头像
                WebRtcVideoHandler && WebRtcVideoHandler.cancelVirtualPhoto()
            },
            shareScreen:function(show){//分享屏幕
                return WebRtcVideoHandler.shareScreen(show)
            },
            shareResource:function(resource, type, show, keep, loop){//分享资源
                return WebRtcVideoHandler.shareResource(resource, type, true,keep,loop)
            },
            shareScreenshot:function(show){//分享截屏
                return WebRtcVideoHandler.shareScreenshot(show)
            },
            takePhoto:function(stream){//拍照截屏
                return yqWebrtcApp.Device.takePhoto(stream)
            },
            convertToAudioCall:function(){//转音频
                WebRtcVideoHandler.convertToAudioCall()
            },
            convertToVideoCall:function(){//转视频
                WebRtcVideoHandler.convertToVideoCall()
            },
            convertCall:function(){//音视频互转
                if(WebRtcVideoHandler.getCallType()=='video'){
                    WebRtcVideoHandler.convertToAudioCall()
                }else{
                    WebRtcVideoHandler.convertToVideoCall()
                }
                tipsMsg('发送命令成功')
            },
            sendDTMF:function(key){
                WebRtcVideoHandler.sendDTMF(key);
            },
             pushMessage:function(resource, option, show){
                WebRtcVideoHandler && WebRtcVideoHandler.pushMessage(resource, option, show)
            },
            pushContent:function(resource, option, show){
                WebRtcVideoHandler && WebRtcVideoHandler.pushContent(resource, option, show)
            },
            cancelPush:function(resource, option, show){
                WebRtcVideoHandler && WebRtcVideoHandler.cancelPush(resource, option, show)
            },
            /*
            level: 级别，可取范围值：0-3，对应关系0==320*240|1==480*640|2==640*480|3==1280*720|4=1920*1080
            Rate: 帧率，可取范围值：1-200
             */
            setDefinition:function(level,Rate){
                return yqWebrtcApp.Device.setDefinition(level,Rate)
            },
            getDefinition:function(){
                return yqWebrtcApp.Device.getDefinition();
            }

        },
        updateVideo: function(list) {
            var videoView = $('#ccbarVideoView');

            list.forEach(function(item) {

                var tVideo = document.createElement('video');
                tVideo.srcObject = item.stream;
                tVideo.autoplay = true;
                tVideo.playsinline = 'playsinline';
                tVideo.muted = item.type == 'local' //||item.type=='remote'&&ctx.speakerMuted;
                tVideo.oncanplay = function() {
                    this.play();
                };
                ccbarDebugger('处理视频流', tVideo.muted, item.type);


                if (item.type == 'remote') {
                    CallControl.webrtc.remoteStream = item.stream;
                    $('[data-ccbar-webrtc-video="remote"]').html(tVideo);
                } else {
                    $('[data-ccbar-webrtc-video="local"]').html(tVideo);
                }

                $('#ccbarVideoView').show();
                $('#ccbarVoiceView').hide();

            })

        },
        initLayout: function() {
            //隐藏话机界面
            if(CallControl.getConfig().webrtcPhone === false){
                return
            }
            if ($('#yqWebrtcMainContainerBox').length == 0) {
                if (!window.useOld) {
                    $('body').append('<div id="yqWebrtcMainContainerBox" style="z-index: 3333;position: relative;"></div>')
                    var themeName = CallControl.webrtc.theme || 'nia'
                    CallControl.webrtc.loadHtml('#yqWebrtcMainContainerBox', CallControl.getContextPath() +'/yc-ccbar/webrtc/theme/' + themeName + '/tmpl.html',function(){
                        $('.yqWebrtcMinMax').on('click',function(e){
                            $('#yqWebrtcMainContainer').toggleClass('max')
                        })

                        //改变大小
                        var title = '.ccbar-webrtc-panel-header';
                        var oLeft = '';
                        var oTop = '';
                        // 获取要拖拽的div元素
                        var draggableDiv = document.getElementById('yqWebrtcMainContainer');
                        var draggableTitle = $('#yqWebrtcMainContainer .ccbar-webrtc-panel-header')[0]
                        // 定义变量来保存鼠标按下时的初始位置
                        var initialX, initialY;

                        // 监听鼠标按下事件
                        draggableTitle.addEventListener('mousedown', function(event) {
                          // 记录鼠标按下时的初始位置
                          initialX = event.clientX;
                          initialY = event.clientY;

                          // 监听鼠标移动事件
                          document.addEventListener('mousemove', dragElement);
                          
                          // 监听鼠标松开事件
                          document.addEventListener('mouseup', stopDragging);
                        });

                        // 定义拖拽函数
                        function dragElement(event) {
                          // 计算鼠标移动的距离
                          var deltaX = event.clientX - initialX;
                          var deltaY = event.clientY - initialY;

                          var divLeft = draggableDiv.offsetLeft + deltaX;
                          var divTop = draggableDiv.offsetTop + deltaY;

                          divTop = divTop<=0?0:divTop;
                          divLeft = divLeft<=0?0:divLeft;

                          // 更新div元素的位置
                          draggableDiv.style.left = divLeft + 'px';
                          draggableDiv.style.top = divTop + 'px';

                          // 更新初始位置
                          initialX = event.clientX;
                          initialY = event.clientY;
                        }

                        // 停止拖拽函数
                        function stopDragging() {
                          // 移除鼠标移动事件和鼠标松开事件的监听器
                          document.removeEventListener('mousemove', dragElement);
                          document.removeEventListener('mouseup', stopDragging);
                        }





                        // 获取要拖拽改变大小的div元素
                        var resizableDiv = document.getElementById('yqWebrtcMainContainer');

                        // 定义变量来保存鼠标按下时的初始位置和div的初始大小
                        var initialX, initialY, initialWidth, initialHeight;

                        // 监听鼠标按下事件
                        resizableDiv.addEventListener('mousedown', function(event) {
                          // 记录鼠标按下时的初始位置和div的初始大小
                          initialX = event.clientX;
                          initialY = event.clientY;
                          initialWidth = resizableDiv.offsetWidth;
                          initialHeight = resizableDiv.offsetHeight;

                          // 监听鼠标移动事件
                          document.addEventListener('mousemove', resizeElement);
                          
                          // 监听鼠标松开事件
                          document.addEventListener('mouseup', stopResizing);
                        });

                        // 定义改变大小的函数
                        function resizeElement(event) {
                          // 计算鼠标移动的距离
                          var deltaX = event.clientX - initialX;
                          var deltaY = event.clientY - initialY;

                          var divWidth = initialWidth + deltaX;
                          var divHeight = initialHeight + deltaY;

                            divWidth = divWidth<=240?240:divWidth;
                            divHeight = divHeight<=400?400:divHeight;


                          // 更新div的大小
                          resizableDiv.style.width = divWidth + 'px';
                          resizableDiv.style.height = divHeight + 'px';
                        }

                        // 停止改变大小的函数
                        function stopResizing() {
                          // 移除鼠标移动事件和鼠标松开事件的监听器
                          document.removeEventListener('mousemove', resizeElement);
                          document.removeEventListener('mouseup', stopResizing);
                        }


                        //图标拖拽
                        // 获取元素
                        var element = document.getElementById('yqBtnTelephoneSwitch');
                        var isDragging = false;
                        var startY = 0;
                        var currentY = 0;

                        // 鼠标按下事件
                        element.addEventListener('mousedown', function(e) {
                            isDragging = true;
                            startY = e.clientY;
                            currentY = element.offsetTop;
                        });

                        // 鼠标移动事件
                        document.addEventListener('mousemove', function(e) {
                            if (isDragging) {
                                var deltaY = e.clientY - startY;
                                element.style.top = currentY + deltaY + 'px';
                            }
                        });

                        // 鼠标释放事件
                        document.addEventListener('mouseup', function() {
                            isDragging = false;
                        });
                    })
                } else {
                    $('head').append('<link href="' + CallControl.getContextPath() + '/yc-ccbar/webrtc/css/base.css" rel="stylesheet" type="text/css"/>')

                    $('body').append(html)
                }
            } else {
                if (window.webrtcInit) return;
            }
            $('body').on('click', '#yqBtnTelephoneSwitch', function() {
                $('#yqBtnTelephoneSwitch,#yqWebrtcMainContainer').toggle()
            })

            $('body').on('click', '#yqWebrtcAppMin', function() {
                $('#yqBtnTelephoneSwitch,#yqWebrtcMainContainer').toggle()
            })

            $('body').on('click', '#yqBtnMicro', function() {
                if (yqWebrtcApp.Device.microStatus()) {
                    CallControl.webrtc.microOn()
                } else {
                    CallControl.webrtc.microOff()
                }
            })

            $('body').on('click', '[data-ccbar-webrtc-microphone="toggle"]', function() {
                if (!yqWebrtcApp.Device.microStatus()) {
                    CallControl.webrtc.SpeakerOn()
                } else {
                    CallControl.webrtc.SpeakerOff()

                }
            })

            $('body').on('click', '[data-ccbar-webrtc-microphone="switch"]', function() {
                tipsMsg('切换麦克风')
                yqWebrtcApp && yqWebrtcApp.Device && yqWebrtcApp.Device.switchMicro();
            })

            $('#yqBtnMicro').on('click', function() {
                if (!yqWebrtcApp.Device.microStatus()) {
                    CallControl.webrtc.microOn()
                } else {
                    CallControl.webrtc.microOff()

                }
            })

            //切换摄像头
            $('body').on('click', '[data-ccbar-webrtc-camera="switch"]', function() {
                CallControl.webrtc.camera.switch()
            })

            $('body').on('click', '[data-ccbar-webrtc-camera="toggle"]', function() {
                if (yqWebrtcApp.Device.cameraStatus()) {
                    CallControl.webrtc.camera.turnOff()
                } else {
                    CallControl.webrtc.camera.turnOn()
                }
            })

            //键盘
            $('body').on('click', '[data-ccbar-webrtc-keyboard]', function(e) {
                var key = $(this).attr('data-ccbar-webrtc-keyboard')
                $('#ccbarWebrtcInput').val($('#ccbarWebrtcInput').val() + '' + key)

                CallControl.webrtc.device.sendDTMF(key)
            })

            //外呼
            $('body').on('click', '[data-ccbar-webrtc="makecall"]', function() {
                var num = $.trim($('#ccbarWebrtcInput').val())
                ccbar_plugin.callControl.makeCall(num, null)
            })

            $('body').on('click', '[data-ccbar-webrtc="videomakecall"]', function() {
                var num = $.trim($('#ccbarWebrtcInput').val())
                ccbar_plugin.callControl.makeCall(num, null, true)
            })

            //分享
            $('body').on('click', '[data-ccbar-webrtc-share]', function() {
                var flag = $(this).data('ccbarWebrtcShare')
                switch (flag) {
                    case 'screen':
                        CallControl.webrtc.shareScreen()
                        break;

                    case 'screenshot':

                        CallControl.webrtc.device.takePhoto(CallControl.webrtc.remoteStream).then(function(base64){
                            var base64name = 'screenshot-'+new Date().format('yyyy-MM-dd')+'.png'
                            window.layer && layer.open({
                                title:'截图',
                                content:'<img src="'+base64+'" style="max-width:100%;max-height:100%"/>',
                                shade:0,
                                btn:['下载'],
                                yes:function(index){
                                    layer.close(index)
                                    downloadBase64File(base64,base64name)
                                },
                                
                            })
                        })

                        //CallControl.webrtc.screenshot.share()
                        break;

                    case 'resource':
                        CallControl.webrtc.resource.share()
                        break;
                }
                return false
            })

            // 切换客服和用户的视频

            //界面按钮操作
            ccbarEvent.addEvent('ccbarWebrtcAudioChange',function(event){//麦克风切换
                $('#yqWebrtcMainContainer [data-ccbar-webrtc-microphone="toggle"]').css({
                    opacity:event == true? 1:0.3
                })
            })

            ccbarEvent.addEvent('ccbarWebrtcVideoChange',function(event){//视频切换
                $('#yqWebrtcMainContainer [data-ccbar-webrtc-camera="toggle"]').css({
                    opacity:event == true? 1:0.3
                })
            })

            //切换客户和自己摄像头
            $('body').on('click','#ccbarVideoView [data-ccbar-webrtc-video]',function(e){
                $('#ccbarVideoView').toggleClass('videoToggle')
            })


            

            window.webrtcInit = true
        }
    }
})()
function downloadBase64File(base64String, fileName) {
  var link = document.createElement("a");
  link.setAttribute("href", base64String);
  link.setAttribute("download", fileName);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
/*设置通话实时转写*/
CallControl.voiceTransferConfig = function(flag){
    var url = CallControl.getContextPath() + '/zy-cti-gw/api/TransferConfig?action=getTransferConfig&isTransfer='+flag+'&agentId=' + CallControl.agentId;
    $.get(url,function(res){
        if(res.code == 200){//成功
            window.localStorage && localStorage.setItem('ccbarTransferConfig',flag)
        }else{
            tipsMsg(res.msg)
        }
    })
}


/*setTimeout setInterval hack*/

    if (!/MSIE 10/i.test (navigator.userAgent)) {
        try {
            var blob = new Blob (["\
            var fakeIdToId = {};\
            onmessage = function (event) {\
                var data = event.data,\
                    name = data.name,\
                    fakeId = data.fakeId,\
                    time;\
                if(data.hasOwnProperty('time')) {\
                    time = data.time;\
                }\
                switch (name) {\
                    case 'setInterval':\
                        fakeIdToId[fakeId] = setInterval(function () {\
                            postMessage({fakeId: fakeId});\
                        }, time);\
                        break;\
                    case 'clearInterval':\
                        if (fakeIdToId.hasOwnProperty (fakeId)) {\
                            clearInterval(fakeIdToId[fakeId]);\
                            delete fakeIdToId[fakeId];\
                        }\
                        break;\
                    case 'setTimeout':\
                        fakeIdToId[fakeId] = setTimeout(function () {\
                            postMessage({fakeId: fakeId});\
                            if (fakeIdToId.hasOwnProperty (fakeId)) {\
                                delete fakeIdToId[fakeId];\
                            }\
                        }, time);\
                        break;\
                    case 'clearTimeout':\
                        if (fakeIdToId.hasOwnProperty (fakeId)) {\
                            clearTimeout(fakeIdToId[fakeId]);\
                            delete fakeIdToId[fakeId];\
                        }\
                        break;\
                }\
            }\
            "]);
            // Obtain a blob URL reference to our worker 'file'.
            workerScript = window.URL.createObjectURL(blob);
        } catch (error) {
            /* Blob is not supported, use external script instead */
            CallControl.setInterval = function (func,time) {
                return setInterval(func,time)
            }
            CallControl.setTimeout = function (func,time) {
                return setTimeout(func,time)
            }

            CallControl.clearTimeout = function (func,time) {
                return clearTimeout(func,time)
            }

            CallControl.clearInterval = function (func,time) {
                return clearInterval(func,time)
            }
        }
    }
    var worker,
        fakeIdToCallback = {},
        lastFakeId = 0,
        maxFakeId = 0x7FFFFFFF, // 2 ^ 31 - 1, 31 bit, positive values of signed 32 bit integer
        logPrefix = '[ccbarLog]HackTimer: ';
    if (typeof (Worker) !== 'undefined') {
        function getFakeId () {
            do {
                if (lastFakeId == maxFakeId) {
                    lastFakeId = 0;
                } else {
                    lastFakeId ++;
                }
            } while (fakeIdToCallback.hasOwnProperty (lastFakeId));
            return lastFakeId;
        }
        try {
            worker = new Worker (workerScript);
            CallControl.setInterval = function (callback, time /* , parameters */) {
                var fakeId = getFakeId ();
                fakeIdToCallback[fakeId] = {
                    callback: callback,
                    parameters: Array.prototype.slice.call(arguments, 2)
                };
                worker.postMessage ({
                    name: 'setInterval',
                    fakeId: fakeId,
                    time: time
                });
                return fakeId;
            };
            CallControl.clearInterval = function (fakeId) {
                if (fakeIdToCallback.hasOwnProperty(fakeId)) {
                    delete fakeIdToCallback[fakeId];
                    worker.postMessage ({
                        name: 'clearInterval',
                        fakeId: fakeId
                    });
                }
            };
            CallControl.setTimeout = function (callback, time /* , parameters */) {
                var fakeId = getFakeId ();
                fakeIdToCallback[fakeId] = {
                    callback: callback,
                    parameters: Array.prototype.slice.call(arguments, 2),
                    isTimeout: true
                };
                worker.postMessage ({
                    name: 'setTimeout',
                    fakeId: fakeId,
                    time: time
                });
                return fakeId;
            };
            CallControl.clearTimeout = function (fakeId) {
                if (fakeIdToCallback.hasOwnProperty(fakeId)) {
                    delete fakeIdToCallback[fakeId];
                    worker.postMessage ({
                        name: 'clearTimeout',
                        fakeId: fakeId
                    });
                }
            };
            worker.onmessage = function (event) {
                var data = event.data,
                    fakeId = data.fakeId,
                    request,
                    parameters,
                    callback;
                if (fakeIdToCallback.hasOwnProperty(fakeId)) {
                    request = fakeIdToCallback[fakeId];
                    callback = request.callback;
                    parameters = request.parameters;
                    if (request.hasOwnProperty ('isTimeout') && request.isTimeout) {
                        delete fakeIdToCallback[fakeId];
                    }
                }
                if (typeof (callback) === 'string') {
                    try {
                        callback = new Function (callback);
                    } catch (error) {
                        ccbarDebugger(logPrefix + 'Error parsing callback code string: ', error);
                    }
                }
                if (typeof (callback) === 'function') {
                    callback.apply (window, parameters);
                }
            };
            worker.onerror = function (event) {
                ccbarDebugger(event);
            };
        } catch (error) {
            ccbarDebugger(logPrefix + 'Initialisation failed');
            console.error (error);
            CallControl.setInterval = function (func,time) {
                return setInterval(func,time)
            }
            CallControl.setTimeout = function (func,time) {
                return setTimeout(func,time)
            }

            CallControl.clearTimeout = function (func,time) {
                return clearTimeout(func,time)
            }

            CallControl.clearInterval = function (func,time) {
                return clearInterval(func,time)
            }
        }
    } else {
        ccbarDebugger(logPrefix + '当前浏览器不支持 Web Worker');
        CallControl.setInterval = function (func,time) {
            return setInterval(func,time)
        }
        CallControl.setTimeout = function (func,time) {
            return setTimeout(func,time)
        }

        CallControl.clearTimeout = function (func,time) {
            return clearTimeout(func,time)
        }

        CallControl.clearInterval = function (func,time) {
            return clearInterval(func,time)
        }
    }

//国际化
CallControl.i18n = (function(){
    return{
        current:'CN',
        data:{},
        init:function(lang){
           if(ccbarI18n[lang]) CallControl.i18n.data = ccbarI18n[lang]
            CallControl.i18n.current = lang;
        },
        get:function(key,type,def){
            if(type){
                return CallControl.i18n.data[type] && CallControl.i18n.data[type][key] || key;
            }
            return CallControl.i18n.data[key] || key;
        },
        tranResult(res){
            if(!CallControl.i18n.current || CallControl.i18n.current =='CN') return res;
            if(res.msg) res.msg = ccbarI18nText(res.msg)
            if(res.data && res.data.content) res.data.content = ccbarI18nText(res.data.content)
            if(res.data && res.data.resultCode) res.data.content = ccbarI18nText(res.data.resultCode,'resultCode')
            //if(res.msg) res.msg = ccbarI18nText(res.msg)
            return res;
        }
    }
})()
var ccbarI18nText = CallControl.i18n.get;

var ccbarI18n = {
    CN:{
        request:{
            403:'403',
            404:'404',
            500:'500',
            503:'503',
            0:'timeout',
        },
        btn:{
            //按钮-话务条
            "未签入":"未签入",
            "签入":"签入",
            "签出":"签出",
            "置忙":"置忙",
            "置闲":"置闲",
            "外呼":"外呼",
            "应答":"应答",
            "挂断":"挂断",
            "挂起":"挂起",
            "静音":"静音",
            "恢复":"恢复",
            "咨询":"咨询",
            "转移":"转移",
            "三方":"三方",
            "满意度":"满意度",
            "话后整理":"话后整理",
            "结束整理":"结束整理",
            "监听":"监听",
            "阻塞":"阻塞",
            "强拆":"强拆",
            "拦截":"拦截",
            "强插":"强插",
            "私语":"私语",
            "强制置忙":"强制置忙",
            "强制置闲":"强制置闲",
            "强制签出":"强制签出",
            //按钮-视频坐席
        },
        agentState:{
            //坐席状态 agentState
            "空闲":"空闲",
            "振铃":"振铃",
            "通话中":"通话中",
            "繁忙":"繁忙",
            "登出":"登出",
            "预占":"预占",
            "话后整理":"话后整理",
            "保持":"保持",
            "三方":"三方",
            "咨询":"咨询",
            "监听":"监听",
            "闭塞":"闭塞",
            "网络异常":"网络异常",
            "网络中断":"网络中断",
        },
        createCause:{
            //呼叫来源 createCause
            '1': '客户来电(外线呼入)',
            '2': '客户来电(IVR转入)',
            '3': '席间转移呼入',
            '4': 'IVR咨询呼入',
            '5': '席间咨询呼入',
            '6': '呼出',
            '8': '预拨号呼出',
            '9': '呼叫前转呼入',
            '10': '转移呼入',
            '14': '席间呼入',
            '29': '席间呼出',
        },
        clearCause:{
            //挂断结果 clearCause
            '0': '成功',
            '1': '无人应答',
            '2': '用户忙',
            '3': '用户挂机',
            '4': '网络忙',
            '5': '空号',
            '6': '用户拒绝',
            '7': '关机',
            '8': '暂停服务',
            '9': '不在服务器',
            '10': '传真机',
            '11': '用户欠费',
            '12': '重复号码',
            '13': '电话总机',
            '14': '久叫不应',
            '15': '话机异常',
            '50': '回铃音反馈,关机',
            '51': '空号',
            '52': '停机',
            '53': '用户拒接',
            '54': '用户忙',
            '55': '不在服务区',
            '56': '无应答',
            '98': '坐席挂断',
            '99': '系统错误',
            '100': '其他错误',
            '111': '三方会议失败',
            '112': '转移操作失败',
            '113': '话机状态异常',
            '114': '外呼操作失败',
            '115': '无效的企业',
            '116': '外呼时坐席已签出',
            '117': '坐席已签出',
            '120': '坐席忙',
            '999': '未定义错误',
        },
        resultCode:{
            //错误提示-对应resultCode
            'succ': '操作成功',
            '000': '操作成功',
            '001': '包文格式错误',
            '002': '无效的操作请求',
            '100': '无效的技能组',
            '101': '无效的坐席工号',
            '102': '无效的坐席密码',
            '103': '无效的坐席状态',
            '104': '无效的呼叫状态',
            '105': '坐席工号已登陆',
            '106': '话机已被使用',
            '107': '外呼主显号码为空',
            '108': '无效的话机号码',
            '109': '未配置席间转移字冠',
            '110': '咨询操作失败',
            '111': '三方会议失败',
            '112': '转移操作失败',
            '113': '话机状态异常',
            '114': '外呼操作失败',
            '115': '无效的企业',
            '116': '外呼时坐席已签出',
            '117': '坐席已签出',
            '118': '坐席阻塞中',
            '119': '话机离线，请检查话机状态。',
            '120': '超出平台最大签入坐席数限制',
            '122': '呼叫已存在其他坐席队列中',
            '125':"坐席振铃超时",
            '401': '坐席未配置技能组或话机号码无效',
            '402': '登录坐席数超过企业订购上限',
            '403': '会话已失效',
            '500': '系统错误',
            '501': '系统忙',
            '503': '非法请求',
            '999': '未定义错误',
        },

        //其他错误信息
    },
    EN:{
        //按钮-话务条
        btn:{
            "确定":'ok',
            "未签入":"NotLogon",
            "签入":"Logon",
            "签出":"Logout",
            "置忙":"Busy",
            "置闲":"Idle",
            "闲":"Idle",
            "外呼":"Makecall",
            "应答":"Answer",
            "挂断":"Hangup",
            "挂起":"Hold",
            "静音":"Mute",
            "恢复":"Retrieve",
            "咨询":"Consultation",
            "转移":"Transfer",
            "三方":"Conference",
            "满意度":"Satisfy",
            "话后整理":"workNotReady",
            "结束整理":"workReady",
            "监听":"Monitor",
            "阻塞":"Blocking",
            "强拆":"ForceCall",
            "拦截":"Intercept",
            "强插":"Invent",
            "私语":"SecretlyTalk",
            "强制置忙":"ForceBusy",
            "强制置闲":"ForceIdle",
            "强制签出":"ForceLogout",
            '呼入':'Inbound',
            '呼出':'outbound',
            '智能外呼':'pdsbound',
            '自动' :'All'
            //按钮-视频坐席
        },
        agentState:{
            //坐席状态 agentState
            "空闲":"Idle",
            "闲":"Idle",
            "振铃":"Ring",
            "通话中":"Talk",
            "通话":"Talk",
            "繁忙":"Busy",
            "忙":"Busy",
            "小休":"Resting",
            "会议":"Meeting",
            "培训":"Training",
            "登出":"Logout",
            "预占":"Occupy",
            "话后整理":"workNotReady",
            "保持":"Hold",
            "三方":"Conference",
            "咨询":"Consultation",
            "监听":"Monitor",
            "闭塞":"Blocking",
            "网络异常":"Network Exception",
            "网络中断":"Network interruption",
        },
        //Call source createCause
        createCause:{
            "1": "Customer calls (incoming calls from outside lines)",
            "2": "Customer call (IVR transfer in)",
            "3": "Transfer calls between seats",
            "4": "IVR consultation call in",
            "5": "Enquiry call in during dinner",
            "6": "Exhale",
            "8": "Pre dial outgoing call",
            "9": "Forward call to incoming call",
            "10": "Transfer incoming calls",
            "14": "Call in during the banquet",
            "29": "Breathing out during the banquet",
        },

        clearCause:{
            //Hangup result clearCause
            "0": "Success",
            "1": "Unattended",
            "2": "User busy",
            "3": "User hangs up",
            "4": "Network busy",
            "5": "Empty number",
            "6": "User rejected",
            "7": "Shutdown",
            "8": "Suspend service",
            "9": "Not on the server",
            "10": "Fax machine",
            "11": "User arrears",
            "12": "Duplicate number",
            "13": "Telephone switchboard",
            "14": "Shouting for a long time should not be",
            "15": "Telephone exception",
            "50": "Ring back tone feedback, shutdown",
            "51": "Empty number",
            "52": "Shutdown",
            "53": "User Reject",
            "54": "User busy",
            "55": "Not in the service area",
            "56": "No response",
            "98": "Seat hanging up",
            "99": "System error",
            "100": "Other errors",
            "111": "The tripartite meeting failed",
            "112": "Transfer operation failed",
            "113": "Telephone state is abnormal",
            "114": "Outbound call operation failed",
            "115": "Invalid enterprise",
            "116": "The agent has been checked out when calling out",
            "117": "The agent has been checked out",
            "120": "Busy seats",
            "999": "Undefined error",
        },

        resultCode:{
            //Error prompt - corresponding resultCode
            "succ": "Operation succeeded",
            "000": "Operation succeeded",
            "001": "Package format error",
            "002": "Invalid operation request",
            "100": "Invalid skill group",
            "101": "Invalid agent ID",
            "102": "Invalid seat password",
            "103": "Invalid seat status",
            "104": "Invalid call status",
            "105": "The agent ID has been logged in",
            "106": "The phone has been used",
            "107": "The outbound main display number is empty",
            "108": "Invalid phone number",
            "109": "No seat transfer prefix",
            "110": "Consultation operation failed",
            "111": "The tripartite meeting failed",
            "112": "Transfer operation failed",
            "113": "Telephone state is abnormal",
            "114": "Outbound call operation failed",
            "115": "Invalid enterprise",
            "116": "The agent has been checked out when calling out",
            "117": "The agent has been checked out",
            "118": "Seats are blocked",
            "119": "The phone is offline, please check the phone status.",
            "120": "The maximum number of check-in seats on the platform is exceeded",
            "122": "The call already exists in the queue of other agents",
            '401': 'The agent is not configured with a skill group or the phone number is invalid',
            '402': 'The number of registered seats exceeds the enterprise subscription limit',
            '403': 'Session has expired',
            '500': 'System error',
            '501': 'System busy',
            '503': 'Illegal request',
            "999": "Undefined error",

            '200':"Success",
            '4031':"[4031] Fail",
            '4032':"[4032] Fail",
            '4033':"[4033] Fail",
            '4034':"[4034] Fail",
            '4035':"[4035] Fail",
            '4036':"[4036] Fail",
            '4037':"[4037] Fail",
        },
        content:{
            "操作成功!":"Success!",
            "正在呼叫":"Calling",
            "当前不可外呼":"Unable makecall",
            "切换到呼入模式":"Switch workmode:inbound",
            "切换到呼出模式":"Switch workmode:outbound ",
            "切换到自动模式":"Switch workmode:auto",
            "请输入正确的电话号码":"Please enter the right phonenum",
            "已发起三方请求":'Thirt party talk command succ',
            "请选择目标坐席或呼叫对象":"Please select the target",
            "签入成功":"Login success",
            "已签出":"Logout success",
            '系统自动选择':'System Auto Select',
            '请选择目标坐席或呼叫对象':'Please Select Target Agent or Call Object',
            '客户':'Customer',
            '无外显号码,不允许呼叫':'No Display Number, Not Allowed to Call',
            '签入成功':'Sign In Successful',
            '已签出':'Signed Out',
            '坐席已签出':'Signed Out',
            '振铃中':'Ringing',
            '外呼失败':'Outbound Call Failed',
            '发起呼叫转移成功':'Successfully Initiated Call Transfer',
            '发起咨询成功':'Successfully Initiated Consultation',
            '发起三方通话成功':'Successfully Initiated Three-Way Call',
            '签入失败':'Sign In Failed',
            '排队':'In Queue',
            '技能组':'Skill Group',
            '在线':'Online',
            '闲':'Idle',
            '忙':'Busy',
            '通话':'In Call',
            '话后':'After Call Work',
            '排队均长':'Average Queue Length',
            '语音坐席监控':'Voice Agent Monitoring',
            '更新时间':'Update Time',
            '请选择技能组':'Please Select Skill Group',
            '转移功能当前不可':'Transfer Not Available',
        }
    },
    jp:{

    },
    gr:{

    }
}

CallControl.i18n.init('CN')

CallControl.logger = function(){

    if(typeof(window.indexedDB) == 'undefined'){
        return {
            send:function(type,data){
               
            },
            export:function(type,data){
               
            },
            clear:function(type,data){
               
            }
        }
    }
    //只保留当天数据
    
    let dbName = 'ccbarLogDb';

    var request = indexedDB.open(dbName, 3);
    var dataIndex = 'yyyyMMdd';
    var today = new Date().format('yyyyMMdd')
    
    var dbTarget = null;
    // 当数据库打开时
    request.onsuccess = function(event) {
      dbTarget = event.target.result;
      if(window.localStorage && localStorage.ccbarLogDay && localStorage.ccbarLogDay!=today){
        clearAll()
    }
    localStorage.setItem('ccbarLogDay',today)

      /*// 添加数据
      addData({ id: 1, name: "Alice", email: "<EMAIL>" });

      // 删除数据
      deleteData(1);

      // 修改数据
      getData(1, function(data) {
        data.name = "Bob";
        updateData(data);
      });

      // 查询数据
      getAllData(function(allData) {
        ccbarDebugger(allData);
      });*/
    };

    // 当数据库需要升级时
    request.onupgradeneeded = function(event) {
      var db = event.target.result;
      var objectStore = db.createObjectStore("ccbarLog", { keyPath: "id" ,autoIncrement: true });
      objectStore.createIndex("name", "名称", { unique: false });
      objectStore.createIndex("date", "日期", { unique: false });
    };

    // 添加数据
    function addData(data) {
      var transaction = dbTarget && dbTarget.transaction(["ccbarLog"], "readwrite");
      var objectStore = transaction.objectStore("ccbarLog");
      data.date = new Date().format('yyyyMMdd')
      data.time = data.time || new Date().format('hh:mm:ss')
      data.agentId = data.agentId ||  window.CallControl.agentId || 'unknow';
      var requestAdd = objectStore.add(data);
    }

    // 删除数据
    function deleteData(id) {
      var transaction = dbTarget && dbTarget.transaction(["ccbarLog"], "readwrite");
      var objectStore = transaction.objectStore("ccbarLog");
      var requestDelete = objectStore.delete(id);
    }

    // 修改数据
    function updateData(data) {
      var transaction = dbTarget && dbTarget.transaction(["ccbarLog"], "readwrite");
      var objectStore = transaction.objectStore("ccbarLog");
      var requestPut = objectStore.put(data);
    }

    // 查询数据
    function getAllData(callback) {
      var transaction = dbTarget && dbTarget.transaction(["ccbarLog"], "readonly");
      var objectStore = transaction.objectStore("ccbarLog");
      var requestGetAll = objectStore.getAll();
      requestGetAll.onsuccess = function(event) {
        var allData = event.target.result;
        callback(allData);
      };
    }

    // 根据ID查询数据
    function getData(id, callback) {
      var transaction = dbTarget && dbTarget.transaction(["ccbarLog"], "readonly");
      var objectStore = transaction.objectStore("ccbarLog");
      var requestGet = objectStore.get(id);
      requestGet.onsuccess = function(event) {
        var data = event.target.result;
        callback(data);
      };
    }

    function addRowData(rowData) {
      var transaction = dbTarget.transaction(["ccbarLog"], "readwrite");
      var objectStore = transaction.objectStore("ccbarLog");
      var requestAdd = objectStore.add(rowData);
    }

    // 导出数据到 CSV 文件
    function exportDataToCSV(type) {
      var transaction = dbTarget.transaction(["ccbarLog"], "readonly");
      var objectStore = transaction.objectStore("ccbarLog");
      var requestGetAll = objectStore.getAll();
      requestGetAll.onsuccess = function(event) {
        var allData = event.target.result;
        var csvContent = "";
        var csvContent = "data:text/csv;charset=utf-8,";

        // 将数据转换为 CSV 格式
        allData.forEach(function(row) {
          // var rowValues = Object.values(row).join(",");
          var time = row.time;
          var type = row.type;
          var agentId = row.agentId;
          var rowValues = JSON.stringify(row);
          var txt = time+'     ['+type+']    '+agentId+'   '+rowValues
          csvContent += txt + "\n";
        });

        // 创建一个链接或按钮，让用户点击下载文件
        var encodedUri = encodeURI(csvContent);
        var blobURL = encodeURIToBlobURL(encodedUri);
        var link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download",'ccbarLOG-'+ new Date().format('yyyyMMddhhmmsss')+".txt");
        document.body.appendChild(link); // Required for FF
        link.click();
        CallControl.logger.clear()
      };
    }

    function encodeURIToBlobURL(encodedString) {
      var blob = new Blob([encodedString], { type: "text/plain" });
      var url = URL.createObjectURL(blob);
      return url;
    }
    function clearObjectStore(db, objectStoreName) {
          var transaction = db.transaction([objectStoreName], "readwrite");
          transaction.objectStore(objectStoreName).clear();
    }

    function clearAll(){
        var objectStoreName = 'ccbarLog';
        clearObjectStore(dbTarget,objectStoreName)
    }

    return{
        send:function(type,data){
            data.type = type;
            addData(data);
            if(type!='click'){//

            }
        },
        export:exportDataToCSV,
        clear:clearAll
    }
}()