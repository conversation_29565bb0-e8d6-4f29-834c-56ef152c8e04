export default class Heatmap3D {
  constructor(view, options) {
    options = options || {};
    this.view = view;
    this.pixelValueArray = options.pixelValueArray || '';


    this.renderer = null; // three.js renderer
    this.camera = null;// three.js camera
    this.scene = null;// three.js scene
    this.ambient = null; // three.js ambient light source
    this.sun = null;// three.js sun light source

    this.xmin = options.extent.xmin;
    this.ymin = options.extent.ymin;
    this.xmax = options.extent.xmax;
    this.ymax = options.extent.ymax;

    this.externalRenderers = options.externalRenderers

    this.overstate = options.overstate;

  }
  setup(context) {

    // initialize the three.js renderer
    //////////////////////////////////////////////////////////////////////////////////////

    this.renderer = new THREE.WebGLRenderer({
      context: context.gl,
      premultipliedAlpha: false
    });
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.setSize(context.camera.fullWidth, context.camera.fullHeight);

    // prevent three.js from clearing the buffers provided by the ArcGIS JS API.
    this.renderer.autoClearDepth = false;
    this.renderer.autoClearStencil = false;
    this.renderer.autoClearColor = false;

    // The ArcGIS JS API renders to custom offscreen buffers, and not to the default framebuffers.
    // We have to inject this bit of code into the three.js runtime in order for it to bind those
    // buffers instead of the default ones.
    var originalSetRenderTarget = this.renderer.setRenderTarget.bind(this.renderer);
    this.renderer.setRenderTarget = function (target) {
      originalSetRenderTarget(target);
      if (target == null) {
        context.bindRenderTarget();
      }
    };

    // setup the three.js scene
    ///////////////////////////////////////////////////////////////////////////////////////
    this.scene = new THREE.Scene();
    // setup the camera
    var cam = context.camera;
    this.camera = new THREE.PerspectiveCamera(cam.fovY, cam.aspect, cam.near, cam.far);
    // setup scene lighting
    this.ambient = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(this.ambient);
    this.sun = new THREE.DirectionalLight(0xffffff, 0.5);
    this.sun.position.set(-600, 300, 60000);
    this.scene.add(this.sun);
    // var pixelValueArray = data.data;
    // 计算像素值的最大值最小值及归一化系数 start 20201026
    // 像素值最大值固定归一到255,方便设置颜色：红色 255,0,0  绿色 0,255,0 黄色 255,255,0
    // 计算像素值的最大值最小值及归一化系数 end 20201026
    var pixelValueObj = this.getPixelValueBoundary(this.pixelValueArray);
    var geometry = this.changeCoordinate(this.pixelValueArray, pixelValueObj);
    var canvas = this.produceCanvas(this.pixelValueArray, pixelValueObj);
    var ground = new THREE.Mesh(geometry, new THREE.MeshPhongMaterial({
      map: new THREE.CanvasTexture(canvas),
      // color: 0x800080,
      // 透明度设置 0921
      opacity: 0.7,
      wireframe: false, // 20201016 设置wireframe属性，显示网格顶点,
      transparent: true,
      side: THREE.DoubleSide
    }));
    this.scene.add(ground);

  }

  render(context) {
    // update camera parameters
    ///////////////////////////////////////////////////////////////////////////////////
    var cam = context.camera;
    //需要调整相机的视角
    this.camera.position.set(cam.eye[0], cam.eye[1], cam.eye[2]);
    this.camera.up.set(cam.up[0], cam.up[1], cam.up[2]);
    this.camera.lookAt(new THREE.Vector3(cam.center[0], cam.center[1], cam.center[2]));
    // Projection matrix can be copied directly
    this.camera.projectionMatrix.fromArray(cam.projectionMatrix);
    // update lighting
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    // view.environment.lighting.date = Date.now();
    var l = context.sunLight;
    this.sun.position.set(
      l.direction[0],
      l.direction[1],
      l.direction[2]
    );
    this.sun.intensity = l.diffuse.intensity;
    this.sun.color = new THREE.Color(l.diffuse.color[0], l.diffuse.color[1], l.diffuse.color[2]);
    this.ambient.intensity = l.ambient.intensity;
    this.ambient.color = new THREE.Color(l.ambient.color[0], l.ambient.color[1], l.ambient.color[2]);
    // draw the scene
    /////////////////////////////////////////////////////////////////////////////////////////////////////
    this.renderer.resetGLState();
    this.renderer.render(this.scene, this.camera);
    // as we want to smoothly animate the ISS movement, immediately request a re-render
    this.externalRenderers.requestRender(this.view);
    // cleanup
    context.resetWebGLState();
  }

  produceCanvas(data, pixelValueObj) {
    var colorArr = this.produceColor(data, pixelValueObj);
    var canvas = document.createElement("canvas");
    canvas.width = data[0].length;
    canvas.height = data.length;
    var context = canvas.getContext("2d");
    var imageData = context.getImageData(0, 0, canvas.width, canvas.height);
    var pixels = imageData.data;
    for (var i = 0; i < data.length; i++) {
      for (var j = 0; j < data[i].length; j++) {
        if (data[i][j] > 0) {
          pixels[(i * data[i].length + j) * 4] = colorArr[i][j][0];
          pixels[(i * data[i].length + j) * 4 + 1] = colorArr[i][j][1];
          pixels[(i * data[i].length + j) * 4 + 2] = colorArr[i][j][2];
          pixels[(i * data[i].length + j) * 4 + 3] = colorArr[i][j][3];
        }
      }
    }
    context.putImageData(imageData, 0, 0);
    return canvas;
  }


  produceColor(data, pixelValueObj) {
    var colorArr = [];
    // 渐变思路： 20221211
    // 透明 → 蓝 / 蓝 → 绿 / 绿 → 黄 / 黄 → 红 分界线百分比
    const parent = 10 / 100, scenedParent = 30 / 100, therParent = 40 / 100
    var minPixelValue = pixelValueObj.minPixelValue;
    var maxPixelValue = pixelValueObj.maxPixelValue;

    const p1Value = (maxPixelValue - minPixelValue) * parent
    const p1_p2Value = (maxPixelValue - minPixelValue) * (scenedParent - parent)
    const p2Value = (maxPixelValue - minPixelValue) * scenedParent
    const p2_p3Value = (maxPixelValue - minPixelValue) * (therParent - scenedParent)
    const p3Value = (maxPixelValue - minPixelValue) * therParent
    const p3_p4Value = (maxPixelValue - minPixelValue) * (1 - therParent)

    for (var i = 0; i < data.length; i++) {
      colorArr[i] = [];
      for (var j = 0; j < data[i].length; j++) {
        var dData = data[i][j], p
        if (dData <= p1Value) { // 如果像素值小于中间值,透明 → 蓝
          p = (dData - minPixelValue) / p1Value * 255
          colorArr[i][j] = [0, 0, 255, p]
        } else if (dData <= p2Value) {
          p = (dData - p1Value) / p1_p2Value * 255
          colorArr[i][j] = [0, p, 255 - p, 255]
        } else if (dData <= p3Value) {
          p = (dData - p2Value) / p2_p3Value * 255
          colorArr[i][j] = [p, 255, 0, 255]
        } else {
          p = (dData - p3Value) / p3_p4Value * 255
          colorArr[i][j] = [255, 255 - p, 0, 255]
        }
      }
    }
    return colorArr;
  }

  sortNumber(a, b) {
    return a - b;
  }

  getPixelValueBoundary(dataArray) {
    var obj = {};
    var temp = [];
    var length1 = dataArray.length;
    var length2;
    var dataArray2;
    var minData = Infinity, maxData = -minData
    for (let i = 0; i < length1; i += 1) {
      dataArray2 = dataArray[i];
      length2 = dataArray2.length;
      for (let j = 0; j < length2; j += 1) {
        if (dataArray2[j] !== 0) {
          var dData = dataArray2[j]
          temp.push(dData);
          if (dData > maxData) maxData = dData
          else if (dData < minData) minData = dData
        }
      }
    }
    console.log('像素值最小值：' + temp[0] + ',像素值最大值：' + temp[temp.length - 1]);
    obj.minPixelValue = minData
    obj.maxPixelValue = maxData
    return obj;
  }

  changeCoordinate(data, pixelValueObj) {
    var minPixelValue = pixelValueObj.minPixelValue;
    var maxPixelValue = pixelValueObj.maxPixelValue;
    var middlePixelValue = (maxPixelValue - minPixelValue) / 2;
    // z值的区间在[0,1300]之间 效果更好看 区间可以调整
    var pixelValueCoefficient = this.overstate / maxPixelValue; // 此处的1300可以调整
    var groundZ = 100; //统一加 200高程 高于地面,此处的200可以调整
    var width4raster = data[0].length;
    var height4raster = data.length;
    var allnum = width4raster * height4raster * 3; //每一个像素点都有3个坐标值： x,y,z
    var arr = new Array(allnum);
    // begin = new Array(allnum);
    var begin = [];
    var zArrayTemp = [];

    // 从上往下逐行计算,从左向右逐列计算
    var xTemp;
    var yTemp;
    var zTemp;
    var interval4x = (this.xmax - this.xmin) / width4raster; //每一个像素点的坐标在x方向的坐标值
    var interval4y = (this.ymax - this.ymin) / height4raster; //每一个像素点的坐标在y方向的坐标值

    for (let h = 0; h < height4raster; h += 1) { // 对于栅格的每一行像素来说,h=0 第一行;h=1 第二行
      yTemp = this.ymax - h * interval4y;
      for (let w = 0; w < width4raster; w += 1) { // 对于栅格的每一行像素中的各个像素来说, w=0 第h行第1列 ; w=1 第h行第2列
        xTemp = this.xmin + w * interval4x;
        begin.push(xTemp); // 设置x坐标
        begin.push(yTemp); // 设置y坐标 ,每一行的y坐标都是一样的
        zTemp = data[h][w] * pixelValueCoefficient + groundZ;
        begin.push(zTemp);
        zArrayTemp.push(zTemp);
      }
    }


    zArrayTemp.sort(this.sortNumber); // 按数字排序 20201023  z值最小值：200,z值最大值：1596.9633483886719
    console.log('z值最小值：' + zArrayTemp[0] + ',z值最大值：' + zArrayTemp[zArrayTemp.length - 1]);
    this.externalRenderers.toRenderCoordinates(this.view, begin, 0, this.view.spatialReference, arr, 0, width4raster *
      height4raster);


    var ground_geometry = new THREE.PlaneGeometry(100, 100, width4raster - 1, height4raster - 1);
    for (var i = 0; i < ground_geometry.vertices.length; i++) {
      var vertex = ground_geometry.vertices[i];
      vertex.x = arr[i * 3];
      vertex.y = arr[i * 3 + 1];
      vertex.z = arr[i * 3 + 2];
    }
    ground_geometry.computeFaceNormals();
    ground_geometry.computeVertexNormals();
    return ground_geometry;

  }


  dispose(content) { }
}
