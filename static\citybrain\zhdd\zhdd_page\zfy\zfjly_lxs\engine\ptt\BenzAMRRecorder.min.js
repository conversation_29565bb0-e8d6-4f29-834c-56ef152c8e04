!function(e,i){"object"==typeof exports&&"undefined"!=typeof module?module.exports=i():"function"==typeof define&&define.amd?define(i):(e=e||self).BenzAMRRecorder=i()}(this,function(){function e(e,i){if(!(e instanceof i))throw new TypeError("Cannot call a class as a function")}function i(e,i){for(var r=0;r<i.length;r++){var n=i[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function r(e,r,n){return r&&i(e.prototype,r),n&&i(e,n),e}"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;var n,t=(function(e,i){var r,n;e.exports=(r=function(){var e,i=0,r=[],n=[];function t(e,i){for(var r=new Float32Array(i),n=0,t=0;t<e.length;t++)r.set(e[t],n),n+=e[t].length;return r}function f(e,i,r){for(var n=0;n<r.length;n++)e.setUint8(i+n,r.charCodeAt(n))}self.onmessage=function(o){switch(o.data.command){case"init":b=o.data.config,e=b.sampleRate;break;case"record":v=o.data.buffer,r.push(v[0]),n.push(v[1]),i+=v[0].length;break;case"exportWAV":l=o.data.type,u=t(r,i),s=t(n,i),h=function(e,i){for(var r=e.length+i.length,n=new Float32Array(r),t=0,f=0;t<r;)n[t++]=e[f],n[t++]=i[f],f++;return n}(u,s),w=new ArrayBuffer(44+2*h.length),f(d=new DataView(w),0,"RIFF"),d.setUint32(4,36+2*h.length,!0),f(d,8,"WAVE"),f(d,12,"fmt "),d.setUint32(16,16,!0),d.setUint16(20,1,!0),d.setUint16(22,2,!0),d.setUint32(24,e,!0),d.setUint32(28,4*e,!0),d.setUint16(32,4,!0),d.setUint16(34,16,!0),f(d,36,"data"),d.setUint32(40,2*h.length,!0),function(e,i,r){for(var n=0;n<r.length;n++,i+=2){var t=Math.max(-1,Math.min(1,r[n]));e.setInt16(i,t<0?32768*t:32767*t,!0)}}(d,44,h),c=new Blob([d],{type:l}),self.postMessage({type:"blob",data:c});break;case"getBuffer":(a=[]).push(t(r,i)),a.push(t(n,i)),self.postMessage({type:"buffer",data:a});break;case"clear":i=0,r=[],n=[]}var a,l,u,s,c,h,w,d,v,b}}.toString().replace(/^\s*function.*?\(\)\s*{/,"").replace(/}\s*$/,""),(n=function(e,i){var n=i||{},t=n.bufferLen||4096;this.context=e.context,this.node=(this.context.createScriptProcessor||this.context.createJavaScriptNode).call(this.context,t,2,2);var f=new Worker((window.URL||window.webkitURL).createObjectURL(new Blob([r],{type:"text/javascript"})));f.onmessage=function(e){"blob"===e.data.type?a(e.data.data):o(e.data.data)},f.postMessage({command:"init",config:{sampleRate:this.context.sampleRate}});var o,a,l=!1;this.node.onaudioprocess=function(e){l&&f.postMessage({command:"record",buffer:[e.inputBuffer.getChannelData(0),e.inputBuffer.getChannelData(1)]})},this.configure=function(e){for(var i in e)e.hasOwnProperty(i)&&(n[i]=e[i])},this.record=function(){l=!0},this.stop=function(){l=!1},this.clear=function(){f.postMessage({command:"clear"})},this.getBuffer=function(e){o=e||n.callback,f.postMessage({command:"getBuffer"})},this.exportWAV=function(e,i){if(a=e||n.callback,i=i||n.type||"audio/wav",!a)throw new Error("Callback not set");f.postMessage({command:"exportWAV",type:i})},this.release=function(){this.stop(),this.clear(),this.configure=this.record=this.stop=this.clear=this.getBuffer=this.exportWAV=function(){},e.disconnect(this.node),this.node.onaudioprocess=null,this.node.disconnect(),f.terminate()},e.connect(this.node),this.node.connect(this.context.destination)}).forceDownload=function(e,i){var r=(window.URL||window.webkitURL).createObjectURL(e),n=window.document.createElement("a");n.href=r,n.download=i||"output.wav";var t=document.createEvent("Event");t.initEvent("click",!0,!0),n.dispatchEvent(t)},n)}(n={exports:{}},n.exports),n.exports),f=window.AudioContext||window.webkitAudioContext||window.mozAudioContext,o=null,a=!0;f?o=new f:(a=!1,console.warn("Web Audio API is Unsupported."));var l=function(){function i(){e(this,i),this.o=null,this.u=null,this.k=null,this.p=!1,this.g=null}return r(i,[{key:"playPcm",value:function(e,i,r,n){i=i||8e3,this.stopPcm();var t,f=n&&n>.001?e.slice(i*n):e;if(!f.length)return r();this.g=o.createBufferSource();try{t=o.createBuffer(1,f.length,i)}catch(a){i<11025?(t=o.createBuffer(1,f.length,4*i),this.g.playbackRate.value=.25):(t=o.createBuffer(1,f.length,2*i),this.g.playbackRate.value=.5)}t.copyToChannel?t.copyToChannel(f,0,0):t.getChannelData(0).set(f),this.g.buffer=t,this.g.loop=!1,this.g.connect(o.destination),this.g.onended=r,this.g.start()}},{key:"stopPcm",value:function(){this.g&&(this.g.stop(),this.g=null)}},{key:"stopPcmSilently",value:function(){this.g.onended=null,this.stopPcm()}},{key:"initRecorder",value:function(){var e=this;return new Promise(function(i,r){var n=function(r){e.o=r,e.u=o.createMediaStreamSource(r),e.k=new t(e.u),e.p=!1,i()},f=function(e){r(e)};e.k?i():window.navigator.mediaDevices&&window.navigator.mediaDevices.getUserMedia?window.navigator.mediaDevices.getUserMedia({audio:!0}).then(n).catch(f):window.navigator.getUserMedia?window.navigator.getUserMedia({audio:!0},n,f):f()})}},{key:"isRecording",value:function(){return this.k&&this.p}},{key:"startRecord",value:function(){this.k&&(this.k.clear(),this.k.record(),this.p=!0)}},{key:"stopRecord",value:function(){this.k&&(this.k.stop(),this.p=!1)}},{key:"generateRecordSamples",value:function(){var e=this;return new Promise(function(i){e.k&&e.k.getBuffer(function(e){i(e[0])})})}},{key:"releaseRecord",value:function(){this.o&&this.o.getTracks&&(this.o.getTracks().forEach(function(e){e.stop()}),this.o=null),this.k&&(this.k.release(),this.k=null)}}],[{key:"isPlaySupported",value:function(){return a}},{key:"isRecordSupported",value:function(){return!!(window.navigator.mediaDevices&&window.navigator.mediaDevices.getUserMedia||window.navigator.getUserMedia)}},{key:"getCtxSampleRate",value:function(){return o.sampleRate}},{key:"getCtxTime",value:function(){return o.currentTime}},{key:"decodeAudioArrayBufferByContext",value:function(e){return new Promise(function(i,r){o.decodeAudioData(e,function(e){var r=e.numberOfChannels,n=new Float32Array(e.length);switch(r){default:case 1:n=e.getChannelData(0);break;case 2:for(var t=e.getChannelData(0),f=e.getChannelData(1),o=0,a=n.length;o<a;o++)n[o]=.5*(t[o]+f[o]);break;case 4:for(var l=e.getChannelData(0),u=e.getChannelData(1),s=e.getChannelData(2),c=e.getChannelData(3),h=0,w=n.length;h<w;h++)n[h]=.25*(l[h]+u[h]+s[h]+c[h]);break;case 6:for(var d=e.getChannelData(0),v=e.getChannelData(1),b=e.getChannelData(2),k=e.getChannelData(4),m=e.getChannelData(5),y=0,p=n.length;y<p;y++)n[y]=.7071*(d[y]+v[y])+b[y]+.5*(k[y]+m[y])}i(n)},r)})}}]),i}(),u=function(){var e=function(){var e,i={toWAV:function(e){var i=this.A(e);if(!i)return null;var r=new Uint8Array(i.buffer,i.byteOffset,i.byteLength),n=new Uint8Array(r.length+this.WAV_HEADER_SIZE),t=0,f=function(e){var i=new Uint8Array(2);new Int16Array(i.buffer)[0]=e,n.set(i,t),t+=2},o=function(e){var i=new Uint8Array(4);new Int32Array(i.buffer)[0]=e,n.set(i,t),t+=4},a=function(e){var i=new TextEncoder("utf-8").encode(e);n.set(i,t),t+=i.length};return a("RIFF"),o(36+r.length),a("WAVEfmt "),o(16),f(1),f(1),o(8e3),o(16e3),f(2),f(16),a("data"),o(r.length),n.set(r,t),n},decode:function(e){var i=this.A(e);if(!i)return null;for(var r=new Float32Array(i.length),n=0;n<r.length;n++)r[n]=i[n]/32768;return r},A:function(i){if(String.fromCharCode.apply(null,i.subarray(0,this.AMR_HEADER.length))!==this.AMR_HEADER)return null;var r=this.Decoder_Interface_init();if(!r)return null;var n=new Int16Array(Math.floor(i.length/6*this.PCM_BUFFER_COUNT)),t=e.S(this.AMR_BUFFER_COUNT),f=new Uint8Array(e.HEAPU8.buffer,t,this.AMR_BUFFER_COUNT);t=e.S(2*this.PCM_BUFFER_COUNT);for(var o=new Int16Array(e.HEAPU8.buffer,t,this.PCM_BUFFER_COUNT),a=6,l=0;a+1<i.length&&l+1<n.length;){var u=this.SIZES[i[a]>>3&15];if(a+u+1>i.length)break;if(f.set(i.subarray(a,a+u+1)),this.Decoder_Interface_Decode(r,f.byteOffset,o.byteOffset,0),l+this.PCM_BUFFER_COUNT>n.length){var s=new Int16Array(2*n.length);s.set(n.subarray(0,l)),n=s}n.set(o,l),l+=this.PCM_BUFFER_COUNT,a+=u+1}return e._(f.byteOffset),e._(o.byteOffset),this.Decoder_Interface_exit(r),n.subarray(0,l)},encode:function(i,r,n){if(r<8e3)return console.error("pcmSampleRate should not be less than 8000."),null;void 0===n&&(n=this.Mode.MR795);var t=this.Encoder_Interface_init();if(!t)return null;var f=e.S(2*this.PCM_BUFFER_COUNT),o=new Int16Array(e.HEAPU8.buffer,f,this.PCM_BUFFER_COUNT);f=e.S(this.AMR_BUFFER_COUNT);for(var a=new Uint8Array(e.HEAPU8.buffer,f,this.AMR_BUFFER_COUNT),l=r/8e3,u=Math.floor(i.length/l),s=new Int16Array(u),c=0;c<u;c++)s[c]=32767*i[Math.floor(c*l)];var h=this.SIZES[n]+1,w=new Uint8Array(Math.ceil(u/this.PCM_BUFFER_COUNT*h)+this.AMR_HEADER.length);w.set(new TextEncoder("utf-8").encode(this.AMR_HEADER));for(var d=0,v=this.AMR_HEADER.length;d+this.PCM_BUFFER_COUNT<s.length&&v+h<w.length;){o.set(s.subarray(d,d+this.PCM_BUFFER_COUNT));var b=this.Encoder_Interface_Encode(t,n,o.byteOffset,a.byteOffset,0);if(b!=h){console.error([b,h]);break}w.set(a.subarray(0,b),v),d+=this.PCM_BUFFER_COUNT,v+=b}return e._(o.byteOffset),e._(a.byteOffset),this.Encoder_Interface_exit(t),w.subarray(0,v)},Decoder_Interface_init:function(){return console.warn("Decoder_Interface_init not initialized."),0},Decoder_Interface_exit:function(e){console.warn("Decoder_Interface_exit not initialized.")},Decoder_Interface_Decode:function(e,i,r,n){console.warn("Decoder_Interface_Decode not initialized.")},Encoder_Interface_init:function(e){return console.warn("Encoder_Interface_init not initialized."),0},Encoder_Interface_exit:function(e){console.warn("Encoder_Interface_exit not initialized.")},Encoder_Interface_Encode:function(e,i,r,n,t){console.warn("Encoder_Interface_Encode not initialized.")},Mode:{MR475:0,MR515:1,MR59:2,MR67:3,MR74:4,MR795:5,MR102:6,MR122:7,MRDTX:8},SIZES:[12,13,15,17,19,20,26,31,5,6,5,5,0,0,0,0],AMR_BUFFER_COUNT:32,PCM_BUFFER_COUNT:160,AMR_HEADER:"#!AMR\n",WAV_HEADER_SIZE:44};(e={canvas:{},print:function(e){console.log(e)},R:function(){return i.Decoder_Interface_init=e.M,i.Decoder_Interface_exit=e.D,i.Decoder_Interface_Decode=e.O,i.Encoder_Interface_init=e.N,i.Encoder_Interface_exit=e.T,i.Encoder_Interface_Encode=e.I,0}})||(e=(void 0!==e?e:null)||{});var r={};for(var n in e)e.hasOwnProperty(n)&&(r[n]=e[n]);var t="object"==typeof window,f="function"==typeof importScripts,o=!t&&!f;if(o)e.print||(e.print=print),"undefined"!=typeof printErr&&(e.printErr=printErr),"undefined"!=typeof read?e.read=read:e.read=function(){throw"no read() available (jsc?)"},e.readBinary=function(e){if("function"==typeof readbuffer)return new Uint8Array(readbuffer(e));var i=read(e,"binary");return d("object"==typeof i),i},"undefined"!=typeof scriptArgs?e.arguments=scriptArgs:void 0!==arguments&&(e.arguments=arguments);else{if(!t&&!f)throw"Unknown runtime environment. Where are we?";e.read=function(e){var i=new XMLHttpRequest;return i.open("GET",e,!1),i.send(null),i.responseText},void 0!==arguments&&(e.arguments=arguments),"undefined"!=typeof console?(e.print||(e.print=function(e){console.log(e)}),e.printErr||(e.printErr=function(e){console.log(e)})):e.print||(e.print=function(e){}),f&&(e.load=importScripts),void 0===e.setWindowTitle&&(e.setWindowTitle=function(e){document.title=e})}for(var n in!e.load&&e.read&&(e.load=function(i){var r;r=e.read(i),eval.call(null,r)}),e.print||(e.print=function(){}),e.printErr||(e.printErr=e.print),e.arguments||(e.arguments=[]),e.thisProgram||(e.thisProgram="./this.program"),e.print=e.print,e.printErr=e.printErr,e.preRun=[],e.postRun=[],r)r.hasOwnProperty(n)&&(e[n]=r[n]);var a={setTempRet0:function(e){s=e},getTempRet0:function(){return s},stackSave:function(){return j},stackRestore:function(e){j=e},getNativeTypeSize:function(e){switch(e){case"i1":case"i8":return 1;case"i16":return 2;case"i32":return 4;case"i64":return 8;case"float":return 4;case"double":return 8;default:if("*"===e[e.length-1])return a.QUANTUM_SIZE;if("i"===e[0]){var i=parseInt(e.substr(1));return d(i%8==0),i/8}return 0}},getNativeFieldSize:function(e){return Math.max(a.getNativeTypeSize(e),a.QUANTUM_SIZE)},STACK_ALIGN:16,prepVararg:function(e,i){return"double"===i||"i64"===i?7&e&&(d(4==(7&e)),e+=4):d(0==(3&e)),e},getAlignSize:function(e,i,r){return r||"i64"!=e&&"double"!=e?e?Math.min(i||(e?a.getNativeFieldSize(e):0),a.QUANTUM_SIZE):Math.min(i,8):8},dynCall:function(i,r,n){return n&&n.length?(n.splice||(n=Array.prototype.slice.call(n)),n.splice(0,0,r),e["dynCall_"+i].apply(null,n)):e["dynCall_"+i].call(null,r)},functionPointers:[],addFunction:function(e){for(var i=0;i<a.functionPointers.length;i++)if(!a.functionPointers[i])return a.functionPointers[i]=e,2*(1+i);throw"Finished up all reserved function pointers. Use a higher value for RESERVED_FUNCTION_POINTERS."},removeFunction:function(e){a.functionPointers[(e-2)/2]=null},warnOnce:function(i){a.warnOnce.shown||(a.warnOnce.shown={}),a.warnOnce.shown[i]||(a.warnOnce.shown[i]=1,e.printErr(i))},funcWrappers:{},getFuncWrapper:function(e,i){d(i),a.funcWrappers[i]||(a.funcWrappers[i]={});var r=a.funcWrappers[i];return r[e]||(r[e]=function(){return a.dynCall(i,e,arguments)}),r[e]},getCompilerSetting:function(e){throw"You must build with -s RETAIN_COMPILER_SETTINGS=1 for Runtime.getCompilerSetting or emscripten_get_compiler_setting to work"},stackAlloc:function(e){var i=j;return j=15+(j=j+e|0)&-16,i},staticAlloc:function(e){var i=x;return x=15+(x=x+e|0)&-16,i},dynamicAlloc:function(e){var i=H;return(H=15+(H=H+e|0)&-16)>=q&&!void ze("Cannot enlarge memory arrays. Either (1) compile with -s TOTAL_MEMORY=X with X higher than the current value "+q+", (2) compile with ALLOW_MEMORY_GROWTH which adjusts the size at runtime but prevents some optimizations, or (3) set Module.TOTAL_MEMORY before the program runs.")?(H=i,0):i},alignMemory:function(e,i){return e=Math.ceil(e/(i||16))*(i||16)},makeBigInt:function(e,i,r){return r?+(e>>>0)+4294967296*+(i>>>0):+(e>>>0)+4294967296*+(0|i)},GLOBAL_BASE:8,QUANTUM_SIZE:4,__dummy__:0};e.Runtime=a;var l,u,s,c,h,w=!1;function d(e,i){e||ze("Assertion failed: "+i)}function v(i){var r=e["_"+i];if(!r)try{r=[eval][0]("_"+i)}catch(n){}return d(r,"Cannot call unknown function "+i+" (perhaps LLVM optimizations or closure removed it?)"),r}function b(e,i,r,n){switch("*"===(r=r||"i8").charAt(r.length-1)&&(r="i32"),r){case"i1":case"i8":M[e>>0]=i;break;case"i16":O[e>>1]=i;break;case"i32":T[e>>2]=i;break;case"i64":u=[i>>>0,(l=i,+oe(l)>=1?l>0?(0|ue(+le(l/4294967296),4294967295))>>>0:~~+ae((l-+(~~l>>>0))/4294967296)>>>0:0)],T[e>>2]=u[0],T[e+4>>2]=u[1];break;case"float":L[e>>2]=i;break;case"double":F[e>>3]=i;break;default:ze("invalid type for setValue: "+r)}}function k(e,i,r){switch("*"===(i=i||"i8").charAt(i.length-1)&&(i="i32"),i){case"i1":case"i8":return M[e>>0];case"i16":return O[e>>1];case"i32":case"i64":return T[e>>2];case"float":return L[e>>2];case"double":return F[e>>3];default:ze("invalid type for setValue: "+i)}return null}!function(){var e={stackSave:function(){a.stackSave()},stackRestore:function(){a.stackRestore()},arrayToC:function(e){var i=a.stackAlloc(e.length);return te(e,i),i},stringToC:function(e){var i=0;return null!=e&&0!==e&&ne(e,i=a.stackAlloc(1+(e.length<<2))),i}},i={string:e.stringToC,array:e.arrayToC};h=function(e,r,n,t,f){var o=v(e),l=[],u=0;if(t)for(var s=0;s<t.length;s++){var c=i[n[s]];c?(0===u&&(u=a.stackSave()),l[s]=c(t[s])):l[s]=t[s]}var h=o.apply(null,l);if("string"===r&&(h=E(h)),0!==u){if(f&&f.async)return void EmterpreterAsync.asyncFinalizers.push(function(){a.stackRestore(u)});a.stackRestore(u)}return h};var r=/^function\s\(([^)]*)\)\s*{\s*([^*]*?)[\s;]*(?:return\s*(.*?)[;\s]*)?}$/;function n(e){var i=e.toString().match(r);return i?{arguments:(i=i.slice(1))[0],body:i[1],returnValue:i[2]}:{}}var t={};for(var f in e)e.hasOwnProperty(f)&&(t[f]=n(e[f]));c=function(e,i,r){r=r||[];var f=v(e),o=r.every(function(e){return"number"===e}),a="string"!==i;if(a&&o)return f;var l=r.map(function(e,i){return"$"+i}),u="(function("+l.join(",")+") {",s=r.length;if(!o){u+="var stack = "+t.stackSave.body+";";for(var c=0;c<s;c++){var h=l[c],w=r[c];if("number"!==w){var d=t[w+"ToC"];u+="var "+d.arguments+" = "+h+";",u+=d.body+";",u+=h+"="+d.returnValue+";"}}}return u+="var ret = "+n(function(){return f}).returnValue+"("+l.join(",")+");",a||(u+="ret = "+n(function(){return E}).returnValue+"(ret);"),o||(u+=t.stackRestore.body.replace("()","(stack)")+";"),u+="return ret})",[eval][0](u)}}(),e.ccall=h,e.cwrap=c,e.setValue=b,e.getValue=k;var m=2,y=4;function p(e,i,r,n){var t,f;"number"==typeof e?(t=!0,f=e):(t=!1,f=e.length);var o,l="string"==typeof i?i:null;if(o=r==y?n:[Fe,a.stackAlloc,a.staticAlloc,a.dynamicAlloc][void 0===r?m:r](Math.max(f,l?1:i.length)),t){var u;for(n=o,d(0==(3&o)),u=o+(-4&f);n<u;n+=4)T[n>>2]=0;for(u=o+f;n<u;)M[n++>>0]=0;return o}if("i8"===l)return e.subarray||e.slice?D.set(e,o):D.set(new Uint8Array(e),o),o;for(var s,c,h,w=0;w<f;){var v=e[w];"function"==typeof v&&(v=a.getFunctionIndex(v)),0!==(s=l||i[w])?("i64"==s&&(s="i32"),b(o+w,v,s),h!==s&&(c=a.getNativeTypeSize(s),h=s),w+=c):w++}return o}function E(i,r){if(0===r||!i)return"";for(var n,t=0,f=0;t|=n=D[i+f>>0],(0!=n||r)&&(f++,!r||f!=r););r||(r=f);var o="";if(t<128){for(var a;r>0;)a=String.fromCharCode.apply(String,D.subarray(i,i+Math.min(r,1024))),o=o?o+a:a,i+=1024,r-=1024;return o}return e.UTF8ToString(i)}function g(e,i){for(var r,n,t,f,o,a="";;){if(!(r=e[i++]))return a;if(128&r)if(n=63&e[i++],192!=(224&r))if(t=63&e[i++],224==(240&r)?r=(15&r)<<12|n<<6|t:(f=63&e[i++],240==(248&r)?r=(7&r)<<18|n<<12|t<<6|f:(o=63&e[i++],r=248==(252&r)?(3&r)<<24|n<<18|t<<12|f<<6|o:(1&r)<<30|n<<24|t<<18|f<<12|o<<6|63&e[i++])),r<65536)a+=String.fromCharCode(r);else{var l=r-65536;a+=String.fromCharCode(55296|l>>10,56320|1023&l)}else a+=String.fromCharCode((31&r)<<6|n);else a+=String.fromCharCode(r)}}function A(e,i,r,n){if(!(n>0))return 0;for(var t=r,f=r+n-1,o=0;o<e.length;++o){var a=e.charCodeAt(o);if(a>=55296&&a<=57343&&(a=65536+((1023&a)<<10)|1023&e.charCodeAt(++o)),a<=127){if(r>=f)break;i[r++]=a}else if(a<=2047){if(r+1>=f)break;i[r++]=192|a>>6,i[r++]=128|63&a}else if(a<=65535){if(r+2>=f)break;i[r++]=224|a>>12,i[r++]=128|a>>6&63,i[r++]=128|63&a}else if(a<=2097151){if(r+3>=f)break;i[r++]=240|a>>18,i[r++]=128|a>>12&63,i[r++]=128|a>>6&63,i[r++]=128|63&a}else if(a<=67108863){if(r+4>=f)break;i[r++]=248|a>>24,i[r++]=128|a>>18&63,i[r++]=128|a>>12&63,i[r++]=128|a>>6&63,i[r++]=128|63&a}else{if(r+5>=f)break;i[r++]=252|a>>30,i[r++]=128|a>>24&63,i[r++]=128|a>>18&63,i[r++]=128|a>>12&63,i[r++]=128|a>>6&63,i[r++]=128|63&a}}return i[r]=0,r-t}function S(e){for(var i=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n>=55296&&n<=57343&&(n=65536+((1023&n)<<10)|1023&e.charCodeAt(++r)),n<=127?++i:i+=n<=2047?2:n<=65535?3:n<=2097151?4:n<=67108863?5:6}return i}function _(i){var r=!!e.___cxa_demangle;if(r)try{var n=Fe(i.length);ne(i.substr(1),n);var t=Fe(4),f=e.___cxa_demangle(n,0,0,t);if(0===k(t,"i32")&&f)return E(f)}catch(h){}finally{n&&Te(n),t&&Te(t),f&&Te(f)}var o=3,l={v:"void",b:"bool",c:"char",s:"short",i:"int",l:"long",f:"float",d:"double",w:"wchar_t",a:"signed char",h:"unsigned char",t:"unsigned short",j:"unsigned int",m:"unsigned long",x:"long long",y:"unsigned long long",z:"..."},u=[],s=!0,c=i;try{if("Object._main"==i||"_main"==i)return"main()";if("number"==typeof i&&(i=E(i)),"_"!==i[0])return i;if("_"!==i[1])return i;if("Z"!==i[2])return i;switch(i[3]){case"n":return"operator new()";case"d":return"operator delete()"}c=function e(r,n,t){n=n||1/0;var f,a="",c=[];if("N"===i[o]){if(f=function(){"K"===i[++o]&&o++;for(var e=[];"E"!==i[o];)if("S"!==i[o])if("C"!==i[o]){var r=parseInt(i.substr(o)),n=r.toString().length;if(!r||!n){o--;break}var t=i.substr(o+n,r);e.push(t),u.push(t),o+=n+r}else e.push(e[e.length-1]),o+=2;else{o++;var f=i.indexOf("_",o),a=i.substring(o,f)||0;e.push(u[a]||"?"),o=f+1}return o++,e}().join("::"),0==--n)return r?[f]:f}else if(("K"===i[o]||s&&"L"===i[o])&&o++,v=parseInt(i.substr(o))){var h=v.toString().length;f=i.substr(o+h,v),o+=h+v}if(s=!1,"I"===i[o]){o++;var w=e(!0);a+=e(!0,1,!0)[0]+" "+f+"<"+w.join(", ")+">"}else a=f;e:for(;o<i.length&&n-- >0;){var d=i[o++];if(d in l)c.push(l[d]);else switch(d){case"P":c.push(e(!0,1,!0)[0]+"*");break;case"R":c.push(e(!0,1,!0)[0]+"&");break;case"L":o++;var v=i.indexOf("E",o)-o;c.push(i.substr(o,v)),o+=v+2;break;case"A":if(v=parseInt(i.substr(o)),o+=v.toString().length,"_"!==i[o])throw"?";o++,c.push(e(!0,1,!0)[0]+" ["+v+"]");break;case"E":break e;default:a+="?"+d;break e}}return t||1!==c.length||"void"!==c[0]||(c=[]),r?(a&&c.push(a+"?"),c):a+"("+c.join(", ")+")"}()}catch(h){c+="?"}return c.indexOf("?")>=0&&!r&&a.warnOnce("warning: a problem occurred in builtin C++ name demangling; build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling"),c}function R(){return function(){var e=new Error;if(!e.stack){try{throw new Error(0)}catch(i){e=i}if(!e.stack)return"(no stack trace available)"}return e.stack.toString()}().replace(/__Z[\w\d_]+/g,function(e){var i=_(e);return e===i?e:e+" ["+i+"]"})}e.ALLOC_NORMAL=0,e.ALLOC_STACK=1,e.ALLOC_STATIC=m,e.ALLOC_DYNAMIC=3,e.ALLOC_NONE=y,e.allocate=p,e.getMemory=function(e){return z?be.called&&$?Fe(e):a.dynamicAlloc(e):a.staticAlloc(e)},e.Pointer_stringify=E,e.AsciiToString=function(e){for(var i="";;){var r=M[e++>>0];if(!r)return i;i+=String.fromCharCode(r)}},e.stringToAscii=function(e,i){return fe(e,i,!1)},e.UTF8ArrayToString=g,e.UTF8ToString=function(e){return g(D,e)},e.stringToUTF8Array=A,e.stringToUTF8=function(e,i,r){return A(e,D,i,r)},e.lengthBytesUTF8=S,e.UTF16ToString=function(e){for(var i=0,r="";;){var n=O[e+2*i>>1];if(0==n)return r;++i,r+=String.fromCharCode(n)}},e.stringToUTF16=function(e,i,r){if(void 0===r&&(r=2147483647),r<2)return 0;for(var n=i,t=(r-=2)<2*e.length?r/2:e.length,f=0;f<t;++f){var o=e.charCodeAt(f);O[i>>1]=o,i+=2}return O[i>>1]=0,i-n},e.lengthBytesUTF16=function(e){return 2*e.length},e.UTF32ToString=function(e){for(var i=0,r="";;){var n=T[e+4*i>>2];if(0==n)return r;if(++i,n>=65536){var t=n-65536;r+=String.fromCharCode(55296|t>>10,56320|1023&t)}else r+=String.fromCharCode(n)}},e.stringToUTF32=function(e,i,r){if(void 0===r&&(r=2147483647),r<4)return 0;for(var n=i,t=n+r-4,f=0;f<e.length;++f){var o=e.charCodeAt(f);if(o>=55296&&o<=57343&&(o=65536+((1023&o)<<10)|1023&e.charCodeAt(++f)),T[i>>2]=o,(i+=4)+4>t)break}return T[i>>2]=0,i-n},e.lengthBytesUTF32=function(e){for(var i=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n>=55296&&n<=57343&&++r,i+=4}return i},e.stackTrace=R;for(var M,D,O,N,T,I,L,F,C,P,B,U=4096,x=0,z=!1,j=0,H=0,W=e.TOTAL_STACK||65536,q=e.TOTAL_MEMORY||524288,G=65536;G<q||G<2*W;)G<16777216?G*=2:G+=16777216;function X(e){for(;e.length>0;){var i=e.shift();if("function"!=typeof i){var r=i.func;"number"==typeof r?void 0===i.arg?a.dynCall("v",r):a.dynCall("vi",r,[i.arg]):r(void 0===i.arg?null:i.arg)}else i()}}G!==q&&(e.printErr("increasing TOTAL_MEMORY to "+G+" to be compliant with the asm.js spec (and given that TOTAL_STACK="+W+")"),q=G),d("undefined"!=typeof Int32Array&&"undefined"!=typeof Float64Array&&!!new Int32Array(1).subarray&&!!new Int32Array(1).set,"JS engine does not provide full typed array support"),B=new ArrayBuffer(q),M=new Int8Array(B),O=new Int16Array(B),T=new Int32Array(B),D=new Uint8Array(B),N=new Uint16Array(B),I=new Uint32Array(B),L=new Float32Array(B),F=new Float64Array(B),T[0]=255,d(255===D[0]&&0===D[3],"Typed arrays 2 must be run on a little-endian system"),e.HEAP=void 0,e.buffer=B,e.HEAP8=M,e.HEAP16=O,e.HEAP32=T,e.HEAPU8=D,e.HEAPU16=N,e.HEAPU32=I,e.HEAPF32=L,e.HEAPF64=F;var V=[],K=[],Y=[],Q=[],Z=[],$=!1;function J(){$||($=!0,X(K))}function ee(e){V.unshift(e)}function ie(e){Z.unshift(e)}function re(e,i,r){var n=r>0?r:S(e)+1,t=new Array(n),f=A(e,t,0,t.length);return i&&(t.length=f),t}function ne(e,i,r){for(var n=re(e,r),t=0;t<n.length;){var f=n[t];M[i+t>>0]=f,t+=1}}function te(e,i){for(var r=0;r<e.length;r++)M[i++>>0]=e[r]}function fe(e,i,r){for(var n=0;n<e.length;++n)M[i++>>0]=e.charCodeAt(n);r||(M[i>>0]=0)}e.addOnPreRun=ee,e.addOnInit=function(e){K.unshift(e)},e.addOnPreMain=function(e){Y.unshift(e)},e.addOnExit=function(e){Q.unshift(e)},e.addOnPostRun=ie,e.intArrayFromString=re,e.intArrayToString=function(e){for(var i=[],r=0;r<e.length;r++){var n=e[r];n>255&&(n&=255),i.push(String.fromCharCode(n))}return i.join("")},e.writeStringToMemory=ne,e.writeArrayToMemory=te,e.writeAsciiToMemory=fe,Math.imul&&-5===Math.imul(4294967295,5)||(Math.imul=function(e,i){var r=65535&e,n=65535&i;return r*n+((e>>>16)*n+r*(i>>>16)<<16)|0}),Math.imul=Math.imul,Math.clz32||(Math.clz32=function(e){e>>>=0;for(var i=0;i<32;i++)if(e&1<<31-i)return i;return 32}),Math.clz32=Math.clz32;var oe=Math.abs,ae=Math.ceil,le=Math.floor,ue=Math.min,se=0,ce=null,he=null;function we(i){se++,e.monitorRunDependencies&&e.monitorRunDependencies(se)}function de(i){if(se--,e.monitorRunDependencies&&e.monitorRunDependencies(se),0==se&&(null!==ce&&(clearInterval(ce),ce=null),he)){var r=he;he=null,r()}}e.addRunDependency=we,e.removeRunDependency=de,e.preloadedImages={},e.preloadedAudios={},x=31784,K.push(),p([154,14,0,0,188,14,0,0,226,14,0,0,8,15,0,0,46,15,0,0,84,15,0,0,130,15,0,0,208,15,0,0,66,16,0,0,108,16,0,0,42,17,0,0,248,17,0,0,228,18,0,0,240,19,0,0,24,21,0,0,86,22,0,0,238,23,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,12,0,13,0,15,0,17,0,19,0,20,0,26,0,31,0,5,0,6,0,5,0,5,0,0,0,0,0,0,0,0,0,1,252,146,252,36,253,182,253,72,254,218,254,108,255,0,0,0,0,32,78,32,78,32,78,32,78,32,78,80,70,0,64,0,32,0,0,0,0,255,127,112,125,112,125,112,125,112,125,112,125,153,89,255,127,112,125,112,125,102,102,102,38,153,25,153,25,154,89,185,62,232,43,188,30,132,21,16,15,139,10,97,7,42,5,157,3,0,96,0,72,0,54,128,40,96,30,200,22,22,17,209,12,157,9,54,7,102,70,184,38,75,21,182,11,113,6,139,3,243,1,18,1,151,0,83,0,154,89,185,62,232,43,188,30,132,21,16,15,139,10,97,7,42,5,157,3,44,3,128,0,30,2,140,0,57,11,111,4,218,8,74,13,19,8,51,2,133,49,135,2,36,16,6,7,225,21,165,20,9,30,118,1,151,14,185,1,160,42,78,10,31,46,190,9,10,80,29,3,98,20,163,2,68,26,162,32,162,20,160,6,208,5,172,1,250,22,196,1,212,20,232,15,255,13,244,4,165,9,133,3,22,62,237,3,134,58,199,12,91,40,250,18,51,14,229,7,36,10,67,3,72,48,28,19,174,47,168,6,120,52,68,6,158,35,37,9,128,15,2,6,103,21,208,38,211,14,161,1,79,5,158,1,56,14,33,6,59,31,213,13,141,44,133,2,104,33,123,2,216,15,97,5,224,64,236,23,156,44,188,2,215,7,95,2,127,48,42,6,111,43,46,18,112,53,172,6,214,46,205,4,60,31,129,28,175,51,83,22,124,9,135,4,25,8,149,7,74,24,233,23,218,13,12,7,221,34,10,7,231,33,44,6,111,54,248,13,1,52,93,24,254,23,106,4,106,23,198,6,61,55,54,18,7,44,249,12,194,47,15,6,107,54,199,11,217,19,224,40,228,36,50,26,153,6,171,2,156,5,26,5,44,28,93,15,242,15,153,10,113,30,192,2,222,58,34,3,155,24,92,20,241,16,237,20,20,26,29,2,174,23,114,2,83,53,116,14,234,44,104,9,28,63,204,2,145,47,239,2,129,31,225,44,170,24,208,8,114,17,240,1,125,28,11,2,229,39,249,14,202,32,221,11,211,32,198,3,148,55,88,7,255,33,33,21,11,64,255,18,252,28,187,7,201,23,206,4,155,36,46,17,222,56,35,13,247,52,57,11,107,51,185,5,158,21,142,6,82,51,179,57,170,28,88,2,38,5,36,2,156,16,211,13,60,39,60,9,91,41,110,2,32,51,157,2,46,55,198,13,175,19,56,38,234,59,107,2,43,12,78,2,58,64,197,11,182,60,72,16,177,60,75,6,45,60,204,4,151,62,83,36,110,29,112,19,198,7,189,4,183,44,133,4,224,48,143,21,3,37,84,10,36,30,242,7,224,51,191,8,139,62,229,19,130,31,105,26,99,39,133,5,138,19,43,9,235,48,87,23,22,59,83,11,88,71,241,8,211,61,223,9,137,63,14,40,59,57,55,44,5,7,81,1,43,12,141,1,182,13,112,11,240,17,110,10,95,29,116,2,151,44,144,2,58,23,131,9,144,25,199,28,46,32,61,3,160,15,95,3,48,39,188,9,185,62,223,13,28,71,30,4,215,23,174,5,252,22,220,30,64,73,140,13,72,7,32,2,238,35,171,2,103,45,64,16,242,17,108,6,86,12,133,4,81,62,0,10,61,48,149,14,12,68,140,20,218,23,212,7,101,11,206,6,83,64,137,20,147,65,144,6,53,67,223,6,165,18,159,12,218,28,147,23,6,56,28,39,195,15,186,1,98,16,202,1,254,35,194,8,3,29,121,16,60,50,33,3,178,43,57,3,104,49,36,8,156,50,154,25,33,37,228,3,229,25,217,3,41,41,198,9,185,59,142,19,58,49,7,8,124,60,117,6,66,63,9,27,151,55,158,22,66,10,60,3,239,21,150,6,95,53,146,22,84,14,18,6,49,44,73,10,42,38,179,5,179,54,125,18,25,62,147,24,134,24,78,7,230,30,237,8,82,66,219,17,192,64,9,15,144,59,7,9,151,62,172,12,123,56,144,69,71,46,203,10,189,7,127,5,120,5,108,3,239,16,219,13,39,17,114,16,29,21,168,2,53,68,13,3,101,25,254,19,155,31,253,29,187,28,26,3,141,32,158,4,193,58,88,12,80,58,223,11,197,79,112,3,209,56,84,3,49,48,116,57,248,26,128,7,129,16,165,3,26,32,63,4,163,41,244,15,98,39,181,17,175,10,72,3,177,80,57,4,71,65,78,23,1,62,226,17,119,42,14,10,189,14,142,4,183,56,204,15,219,80,67,10,115,59,174,10,170,59,138,8,113,24,154,12,69,51,24,76,28,28,162,3,158,9,82,6,163,17,20,12,28,54,181,16,220,40,65,3,187,67,42,3,251,65,241,8,186,60,25,32,35,53,148,6,125,12,42,7,76,62,4,11,196,61,207,20,110,66,134,9,148,65,46,5,55,61,220,31,206,45,108,33,178,14,5,8,91,37,37,5,249,52,134,26,195,47,144,7,244,31,222,13,231,51,242,6,171,63,199,25,163,63,78,30,73,33,247,9,57,28,85,10,93,71,65,29,245,65,200,8,218,69,68,11,113,67,0,13,201,36,194,78,34,43,128,32,6,5,108,2,151,5,71,2,105,23,241,8,138,15,42,14,24,20,240,2,97,52,62,3,177,21,44,11,244,45,20,23,241,41,48,2,70,21,52,2,9,52,192,11,170,46,99,14,175,77,30,3,97,38,216,2,95,53,44,34,223,28,237,11,211,9,10,3,162,23,65,3,69,25,210,19,113,32,159,9,253,23,73,7,204,59,238,4,72,56,195,17,95,53,163,17,65,12,167,11,175,9,235,4,240,58,39,18,22,60,47,10,156,56,88,9,174,48,233,9,115,29,133,11,109,50,28,47,92,21,172,2,69,12,210,2,217,19,250,4,188,49,104,16,198,59,169,2,139,30,80,2,134,25,229,7,94,64,33,34,52,52,114,3,21,21,131,3,64,57,130,8,149,57,131,16,190,55,18,5,105,54,237,7,117,60,58,29,199,61,220,17,217,9,221,7,198,19,12,7,39,20,182,25,218,27,13,14,168,42,75,6,209,45,172,6,7,66,127,13,140,63,240,25,90,36,239,3,153,36,58,8,238,74,173,19,153,48,173,16,47,62,52,5,253,59,184,13,122,46,61,55,229,62,198,26,218,7,225,2,195,14,93,3,190,44,64,11,236,13,212,13,97,35,217,4,103,48,128,3,98,33,21,18,41,45,144,22,193,31,77,2,26,32,76,2,40,73,171,14,173,50,77,12,113,61,246,2,250,64,242,2,118,59,130,43,255,61,160,8,65,18,98,2,234,39,166,2,153,59,50,16,97,22,255,12,185,32,134,6,150,77,17,9,90,60,135,21,230,54,105,21,96,22,72,11,156,29,66,5,48,56,205,20,108,63,110,15,14,59,160,14,202,59,155,5,5,57,230,15,13,48,80,61,193,29,163,6,122,8,116,3,107,17,215,17,174,70,234,12,198,49,47,3,78,58,139,3,168,58,185,16,158,60,176,32,74,70,63,4,54,9,97,3,153,63,203,14,63,61,244,17,228,63,254,5,200,64,162,8,193,65,225,37,57,62,161,17,205,12,61,4,171,37,139,8,197,46,180,23,239,35,110,17,251,34,93,6,49,40,246,11,97,64,35,20,106,60,154,27,110,53,239,9,153,20,229,8,106,65,69,24,15,65,80,13,80,79,35,13,0,73,193,7,92,55,67,50,50,59,87,61,121,17,252,3,145,6,118,3,215,16,205,16,248,34,73,14,5,23,123,4,127,45,172,5,14,62,179,8,230,17,244,25,17,27,181,4,76,24,31,3,127,48,81,13,96,62,37,15,147,77,61,8,217,37,93,8,150,57,126,34,144,56,39,10,25,7,214,4,91,30,45,3,135,74,58,17,178,21,16,8,103,14,28,11,27,68,208,8,57,65,134,17,71,63,12,21,92,31,203,10,77,13,71,8,18,68,101,21,130,53,226,10,167,77,160,10,138,35,40,15,252,70,225,18,184,67,175,47,252,19,228,3,71,19,220,3,160,38,9,12,126,23,251,20,9,62,131,6,213,32,159,4,239,58,62,9,65,77,90,27,187,46,26,6,111,28,104,4,219,65,252,5,146,61,5,21,116,57,17,8,137,78,107,8,6,67,53,32,247,69,174,24,91,21,224,5,4,16,14,10,13,68,154,26,41,22,72,11,252,64,54,13,15,35,39,7,191,78,129,18,94,76,126,28,2,26,221,10,208,44,249,12,197,75,190,19,190,73,114,18,55,64,69,9,206,79,34,17,89,44,158,103,73,45,252,11,50,11,30,6,244,19,46,4,142,37,51,19,75,19,208,13,117,29,110,3,237,80,83,3,26,27,43,17,159,65,53,30,153,39,251,3,117,38,196,3,134,60,115,15,99,60,102,13,175,73,214,3,152,78,195,3,236,65,87,50,254,55,104,16,199,25,196,4,6,36,46,3,46,66,14,20,29,22,34,19,112,21,6,7,34,79,122,15,109,66,34,24,9,70,41,23,149,36,92,13,50,29,179,7,81,76,57,20,59,74,190,11,70,64,204,14,198,62,63,9,216,33,183,10,229,36,246,102,104,42,7,5,227,13,241,3,230,21,38,14,253,75,136,21,165,48,29,3,154,80,143,3,67,60,250,11,141,66,35,40,195,73,73,10,73,15,244,4,63,76,43,13,132,70,110,20,91,75,142,6,52,76,100,12,152,70,2,42,241,64,189,26,62,12,250,8,117,42,133,9,220,60,1,27,53,49,53,13,108,43,225,12,122,65,120,9,165,73,59,26,19,67,159,38,199,49,45,10,233,34,68,12,89,74,84,30,171,71,40,15,251,79,98,14,146,76,52,13,244,50,173,75,30,41,84,90,1,0,3,0,0,0,1,0,2,0,4,0,82,120,26,113,81,106,240,99,241,93,78,88,2,83,7,78,89,73,242,68,51,115,174,103,80,93,251,83,149,75,6,68,56,61,25,55,150,49,161,44,205,76,21,46,166,27,151,16,244,9,249,5,149,3,38,2,74,1,198,0,249,79,26,80,59,80,92,80,125,80,164,80,197,80,236,80,13,81,52,81,85,81,124,81,157,81,196,81,236,81,19,82,58,82,97,82,137,82,176,82,215,82,255,82,38,83,84,83,123,83,169,83,208,83,254,83,38,84,84,84,129,84,175,84,221,84,11,85,57,85,103,85,149,85,201,85,247,85,43,86,89,86,142,86,194,86,247,86,43,87,95,87,148,87,200,87,3,88,56,88,115,88,174,88,233,88,36,89,95,89,154,89,219,89,22,90,88,90,153,90,212,90,28,91,94,91,159,91,231,91,48,92,113,92,192,92,8,93,80,93,159,93,237,93,60,94,138,94,224,94,46,95,131,95,217,95,52,96,138,96,229,96,72,97,163,97,6,98,104,98,209,98,51,99,156,99,11,100,123,100,234,100,96,101,214,101,76,102,201,102,76,103,207,103,82,104,220,104,108,105,252,105,147,106,48,107,205,107,113,108,27,109,204,109,125,110,59,111,249,111,197,112,150,113,111,114,84,115,64,116,50,117,50,118,63,119,88,120,225,122,255,127,255,127,255,127,255,127,255,127,255,127,255,127,225,122,88,120,63,119,50,118,50,117,64,116,84,115,111,114,150,113,197,112,249,111,59,111,125,110,204,109,27,109,113,108,205,107,48,107,147,106,252,105,108,105,220,104,82,104,207,103,76,103,201,102,76,102,214,101,96,101,234,100,123,100,11,100,156,99,51,99,209,98,104,98,6,98,163,97,72,97,229,96,138,96,52,96,217,95,131,95,46,95,224,94,138,94,60,94,237,93,159,93,80,93,8,93,192,92,113,92,48,92,231,91,159,91,94,91,28,91,212,90,153,90,88,90,22,90,219,89,154,89,95,89,36,89,233,88,174,88,115,88,56,88,3,88,200,87,148,87,95,87,43,87,247,86,194,86,142,86,89,86,43,86,247,85,201,85,149,85,103,85,57,85,11,85,221,84,175,84,129,84,84,84,38,84,254,83,208,83,169,83,123,83,84,83,38,83,255,82,215,82,176,82,137,82,97,82,58,82,19,82,236,81,196,81,157,81,124,81,85,81,52,81,13,81,236,80,197,80,164,80,125,80,92,80,59,80,26,80,249,79,210,79,177,79,145,79,112,79,13,0,14,0,16,0,18,0,20,0,21,0,27,0,32,0,6,0,7,0,6,0,6,0,0,0,0,0,0,0,1,0,13,0,14,0,16,0,18,0,19,0,21,0,26,0,31,0,6,0,6,0,6,0,6,0,0,0,0,0,0,0,1,0,79,115,156,110,74,97,126,77,72,54,9,31,195,10,153,251,125,242,48,239,127,240,173,244,231,249,176,254,22,2,202,3,255,3,55,3,4,2,220,0,0,0,125,255,62,255,41,255,0,0,216,127,107,127,182,126,187,125,123,124,248,122,53,121,53,119,250,116,137,114,128,46,128,67,0,120,0,101,128,94,64,113,64,95,192,28,64,76,192,57,84,0,1,0,254,255,2,0,5,0,10,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,5,0,10,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,10,0,19,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,94,0,0,0,253,255,3,0,3,0,6,0,5,0,9,0,18,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,17,0,19,0,19,0,19,0,19,0,23,0,39,0,57,0,5,0,8,0,8,0,7,0,8,0,7,0,2,0,8,0,4,0,7,0,2,0,4,0,7,0,2,0,8,0,4,0,7,0,2,0,8,0,8,0,7,0,8,0,7,0,2,0,6,0,4,0,7,0,2,0,6,0,4,0,7,0,2,0,6,0,4,0,7,0,2,0,6,0,8,0,9,0,9,0,8,0,9,0,2,0,6,0,4,0,9,0,2,0,6,0,8,0,9,0,2,0,6,0,4,0,9,0,2,0,6,0,8,0,9,0,9,0,8,0,11,0,3,0,7,0,4,0,11,0,3,0,7,0,8,0,11,0,3,0,7,0,4,0,11,0,3,0,7,0,8,0,9,0,9,0,8,0,13,0,4,0,7,0,5,0,13,0,4,0,7,0,8,0,13,0,4,0,7,0,5,0,13,0,4,0,7,0,9,0,9,0,9,0,8,0,13,0,4,0,4,0,5,0,6,0,13,0,4,0,4,0,5,0,8,0,13,0,4,0,4,0,5,0,6,0,13,0,4,0,4,0,5,0,8,0,9,0,9,0,8,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,5,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,8,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,5,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,7,0,8,0,9,0,8,0,6,0,9,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,6,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,9,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,6,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,3,0,8,0,9,0,9,0,6,0,95,0,103,0,118,0,134,0,148,0,159,0,204,0,244,0,39,0,43,0,38,0,37,0,0,0,0,0,0,0,0,0,0,0,1,0,2,0,3,0,4,0,5,0,6,0,7,0,8,0,9,0,10,0,11,0,12,0,13,0,14,0,15,0,23,0,24,0,25,0,26,0,27,0,28,0,48,0,49,0,61,0,62,0,82,0,83,0,47,0,46,0,45,0,44,0,81,0,80,0,79,0,78,0,17,0,18,0,20,0,22,0,77,0,76,0,75,0,74,0,29,0,30,0,43,0,42,0,41,0,40,0,38,0,39,0,16,0,19,0,21,0,50,0,51,0,59,0,60,0,63,0,64,0,72,0,73,0,84,0,85,0,93,0,94,0,32,0,33,0,35,0,36,0,53,0,54,0,56,0,57,0,66,0,67,0,69,0,70,0,87,0,88,0,90,0,91,0,34,0,55,0,68,0,89,0,37,0,58,0,71,0,92,0,31,0,52,0,65,0,86,0,7,0,6,0,5,0,4,0,3,0,2,0,1,0,0,0,15,0,14,0,13,0,12,0,11,0,10,0,9,0,8,0,23,0,24,0,25,0,26,0,27,0,46,0,65,0,84,0,45,0,44,0,43,0,64,0,63,0,62,0,83,0,82,0,81,0,102,0,101,0,100,0,42,0,61,0,80,0,99,0,28,0,47,0,66,0,85,0,18,0,41,0,60,0,79,0,98,0,29,0,48,0,67,0,17,0,20,0,22,0,40,0,59,0,78,0,97,0,21,0,30,0,49,0,68,0,86,0,19,0,16,0,87,0,39,0,38,0,58,0,57,0,77,0,35,0,54,0,73,0,92,0,76,0,96,0,95,0,36,0,55,0,74,0,93,0,32,0,51,0,33,0,52,0,70,0,71,0,89,0,90,0,31,0,50,0,69,0,88,0,37,0,56,0,75,0,94,0,34,0,53,0,72,0,91,0,0,0,1,0,4,0,5,0,3,0,6,0,7,0,2,0,13,0,15,0,8,0,9,0,11,0,12,0,14,0,10,0,16,0,28,0,74,0,29,0,75,0,27,0,73,0,26,0,72,0,30,0,76,0,51,0,97,0,50,0,71,0,96,0,117,0,31,0,77,0,52,0,98,0,49,0,70,0,95,0,116,0,53,0,99,0,32,0,78,0,33,0,79,0,48,0,69,0,94,0,115,0,47,0,68,0,93,0,114,0,46,0,67,0,92,0,113,0,19,0,21,0,23,0,22,0,18,0,17,0,20,0,24,0,111,0,43,0,89,0,110,0,64,0,65,0,44,0,90,0,25,0,45,0,66,0,91,0,112,0,54,0,100,0,40,0,61,0,86,0,107,0,39,0,60,0,85,0,106,0,36,0,57,0,82,0,103,0,35,0,56,0,81,0,102,0,34,0,55,0,80,0,101,0,42,0,63,0,88,0,109,0,41,0,62,0,87,0,108,0,38,0,59,0,84,0,105,0,37,0,58,0,83,0,104,0,0,0,1,0,4,0,3,0,5,0,6,0,13,0,7,0,2,0,8,0,9,0,11,0,15,0,12,0,14,0,10,0,28,0,82,0,29,0,83,0,27,0,81,0,26,0,80,0,30,0,84,0,16,0,55,0,109,0,56,0,110,0,31,0,85,0,57,0,111,0,48,0,73,0,102,0,127,0,32,0,86,0,51,0,76,0,105,0,130,0,52,0,77,0,106,0,131,0,58,0,112,0,33,0,87,0,19,0,23,0,53,0,78,0,107,0,132,0,21,0,22,0,18,0,17,0,20,0,24,0,25,0,50,0,75,0,104,0,129,0,47,0,72,0,101,0,126,0,54,0,79,0,108,0,133,0,46,0,71,0,100,0,125,0,128,0,103,0,74,0,49,0,45,0,70,0,99,0,124,0,42,0,67,0,96,0,121,0,39,0,64,0,93,0,118,0,38,0,63,0,92,0,117,0,35,0,60,0,89,0,114,0,34,0,59,0,88,0,113,0,44,0,69,0,98,0,123,0,43,0,68,0,97,0,122,0,41,0,66,0,95,0,120,0,40,0,65,0,94,0,119,0,37,0,62,0,91,0,116,0,36,0,61,0,90,0,115,0,0,0,1,0,2,0,3,0,4,0,5,0,6,0,7,0,8,0,9,0,10,0,11,0,12,0,13,0,14,0,15,0,16,0,26,0,87,0,27,0,88,0,28,0,89,0,29,0,90,0,30,0,91,0,51,0,80,0,112,0,141,0,52,0,81,0,113,0,142,0,54,0,83,0,115,0,144,0,55,0,84,0,116,0,145,0,58,0,119,0,59,0,120,0,21,0,22,0,23,0,17,0,18,0,19,0,31,0,60,0,92,0,121,0,56,0,85,0,117,0,146,0,20,0,24,0,25,0,50,0,79,0,111,0,140,0,57,0,86,0,118,0,147,0,49,0,78,0,110,0,139,0,48,0,77,0,53,0,82,0,114,0,143,0,109,0,138,0,47,0,76,0,108,0,137,0,32,0,33,0,61,0,62,0,93,0,94,0,122,0,123,0,41,0,42,0,43,0,44,0,45,0,46,0,70,0,71,0,72,0,73,0,74,0,75,0,102,0,103,0,104,0,105,0,106,0,107,0,131,0,132,0,133,0,134,0,135,0,136,0,34,0,63,0,95,0,124,0,35,0,64,0,96,0,125,0,36,0,65,0,97,0,126,0,37,0,66,0,98,0,127,0,38,0,67,0,99,0,128,0,39,0,68,0,100,0,129,0,40,0,69,0,101,0,130,0,8,0,7,0,6,0,5,0,4,0,3,0,2,0,14,0,16,0,9,0,10,0,12,0,13,0,15,0,11,0,17,0,20,0,22,0,24,0,23,0,19,0,18,0,21,0,56,0,88,0,122,0,154,0,57,0,89,0,123,0,155,0,58,0,90,0,124,0,156,0,52,0,84,0,118,0,150,0,53,0,85,0,119,0,151,0,27,0,93,0,28,0,94,0,29,0,95,0,30,0,96,0,31,0,97,0,61,0,127,0,62,0,128,0,63,0,129,0,59,0,91,0,125,0,157,0,32,0,98,0,64,0,130,0,1,0,0,0,25,0,26,0,33,0,99,0,34,0,100,0,65,0,131,0,66,0,132,0,54,0,86,0,120,0,152,0,60,0,92,0,126,0,158,0,55,0,87,0,121,0,153,0,117,0,116,0,115,0,46,0,78,0,112,0,144,0,43,0,75,0,109,0,141,0,40,0,72,0,106,0,138,0,36,0,68,0,102,0,134,0,114,0,149,0,148,0,147,0,146,0,83,0,82,0,81,0,80,0,51,0,50,0,49,0,48,0,47,0,45,0,44,0,42,0,39,0,35,0,79,0,77,0,76,0,74,0,71,0,67,0,113,0,111,0,110,0,108,0,105,0,101,0,145,0,143,0,142,0,140,0,137,0,133,0,41,0,73,0,107,0,139,0,37,0,69,0,103,0,135,0,38,0,70,0,104,0,136,0,7,0,6,0,5,0,4,0,3,0,2,0,1,0,0,0,16,0,15,0,14,0,13,0,12,0,11,0,10,0,9,0,8,0,26,0,27,0,28,0,29,0,30,0,31,0,115,0,116,0,117,0,118,0,119,0,120,0,72,0,73,0,161,0,162,0,65,0,68,0,69,0,108,0,111,0,112,0,154,0,157,0,158,0,197,0,200,0,201,0,32,0,33,0,121,0,122,0,74,0,75,0,163,0,164,0,66,0,109,0,155,0,198,0,19,0,23,0,21,0,22,0,18,0,17,0,20,0,24,0,25,0,37,0,36,0,35,0,34,0,80,0,79,0,78,0,77,0,126,0,125,0,124,0,123,0,169,0,168,0,167,0,166,0,70,0,67,0,71,0,113,0,110,0,114,0,159,0,156,0,160,0,202,0,199,0,203,0,76,0,165,0,81,0,82,0,92,0,91,0,93,0,83,0,95,0,85,0,84,0,94,0,101,0,102,0,96,0,104,0,86,0,103,0,87,0,97,0,127,0,128,0,138,0,137,0,139,0,129,0,141,0,131,0,130,0,140,0,147,0,148,0,142,0,150,0,132,0,149,0,133,0,143,0,170,0,171,0,181,0,180,0,182,0,172,0,184,0,174,0,173,0,183,0,190,0,191,0,185,0,193,0,175,0,192,0,176,0,186,0,38,0,39,0,49,0,48,0,50,0,40,0,52,0,42,0,41,0,51,0,58,0,59,0,53,0,61,0,43,0,60,0,44,0,54,0,194,0,179,0,189,0,196,0,177,0,195,0,178,0,187,0,188,0,151,0,136,0,146,0,153,0,134,0,152,0,135,0,144,0,145,0,105,0,90,0,100,0,107,0,88,0,106,0,89,0,98,0,99,0,62,0,47,0,57,0,64,0,45,0,63,0,46,0,55,0,56,0,0,0,1,0,2,0,3,0,4,0,5,0,6,0,7,0,8,0,9,0,10,0,11,0,12,0,13,0,14,0,23,0,15,0,16,0,17,0,18,0,19,0,20,0,21,0,22,0,24,0,25,0,26,0,27,0,28,0,38,0,141,0,39,0,142,0,40,0,143,0,41,0,144,0,42,0,145,0,43,0,146,0,44,0,147,0,45,0,148,0,46,0,149,0,47,0,97,0,150,0,200,0,48,0,98,0,151,0,201,0,49,0,99,0,152,0,202,0,86,0,136,0,189,0,239,0,87,0,137,0,190,0,240,0,88,0,138,0,191,0,241,0,91,0,194,0,92,0,195,0,93,0,196,0,94,0,197,0,95,0,198,0,29,0,30,0,31,0,32,0,33,0,34,0,35,0,50,0,100,0,153,0,203,0,89,0,139,0,192,0,242,0,51,0,101,0,154,0,204,0,55,0,105,0,158,0,208,0,90,0,140,0,193,0,243,0,59,0,109,0,162,0,212,0,63,0,113,0,166,0,216,0,67,0,117,0,170,0,220,0,36,0,37,0,54,0,53,0,52,0,58,0,57,0,56,0,62,0,61,0,60,0,66,0,65,0,64,0,70,0,69,0,68,0,104,0,103,0,102,0,108,0,107,0,106,0,112,0,111,0,110,0,116,0,115,0,114,0,120,0,119,0,118,0,157,0,156,0,155,0,161,0,160,0,159,0,165,0,164,0,163,0,169,0,168,0,167,0,173,0,172,0,171,0,207,0,206,0,205,0,211,0,210,0,209,0,215,0,214,0,213,0,219,0,218,0,217,0,223,0,222,0,221,0,73,0,72,0,71,0,76,0,75,0,74,0,79,0,78,0,77,0,82,0,81,0,80,0,85,0,84,0,83,0,123,0,122,0,121,0,126,0,125,0,124,0,129,0,128,0,127,0,132,0,131,0,130,0,135,0,134,0,133,0,176,0,175,0,174,0,179,0,178,0,177,0,182,0,181,0,180,0,185,0,184,0,183,0,188,0,187,0,186,0,226,0,225,0,224,0,229,0,228,0,227,0,232,0,231,0,230,0,235,0,234,0,233,0,238,0,237,0,236,0,96,0,199,0,0,0,2,0,0,0,3,0,0,0,2,0,0,0,3,0,1,0,3,0,2,0,4,0,1,0,4,0,1,0,4,0,0,0,205,12,156,25,0,32,102,38,205,44,0,48,51,51,102,54,154,57,205,60,0,64,51,67,102,70,154,73,205,76,159,0,64,241,53,167,206,0,190,242,52,176,12,1,67,244,88,185,93,1,201,245,133,194,163,1,215,246,223,200,226,1,166,247,189,205,42,2,116,248,147,210,125,2,66,249,109,215,221,2,18,250,77,220,74,3,222,250,30,225,201,3,174,251,0,230,90,4,124,252,216,234,1,5,74,253,179,239,193,5,25,254,141,244,158,6,231,254,104,249,156,7,181,255,67,254,193,8,133,0,33,3,17,10,83,1,252,7,147,11,33,2,213,12,80,13,240,2,178,17,79,15,190,3,140,22,155,17,141,4,104,27,63,20,91,5,67,32,72,23,41,6,29,37,199,26,248,6,249,41,203,30,199,7,212,46,105,35,149,8,175,51,185,40,100,9,138,56,222,48,113,10,224,62,135,63,244,11,253,71,150,82,120,13,27,81,93,107,252,14,57,90,93,107,252,14,57,90,0,0,1,0,3,0,2,0,6,0,4,0,5,0,7,0,0,0,1,0,3,0,2,0,5,0,6,0,4,0,7,0,248,127,211,127,76,127,108,126,51,125,163,123,188,121,127,119,239,116,12,114,217,110,89,107,141,103,121,99,31,95,130,90,166,85,141,80,60,75,182,69,0,64,28,58,15,52,223,45,141,39,32,33,156,26,6,20,97,13,178,6,0,0,78,249,159,242,250,235,100,229,224,222,115,216,33,210,241,203,228,197,0,192,74,186,196,180,115,175,90,170,126,165,225,160,135,156,115,152,167,148,39,145,244,141,17,139,129,136,68,134,93,132,205,130,148,129,180,128,45,128,8,128,255,127,46,124,174,120,118,117,125,114,186,111,41,109,194,106,131,104,102,102,105,100,137,98,194,96,19,95,122,93,245,91,130,90,33,89,207,87,139,86,85,85,44,84,15,83,252,81,244,80,246,79,1,79,20,78,48,77,83,76,126,75,175,74,231,73,37,73,104,72,178,71,0,71,84,70,173,69,10,69,107,68,209,67,59,67,168,66,25,66,142,65,6,65,130,64,0,64,0,0,175,5,50,11,140,16,192,21,207,26,188,31,136,36,53,41,196,45,55,50,143,54,206,58,245,62,4,67,252,70,223,74,174,78,105,82,17,86,167,89,44,93,159,96,3,100,87,103,155,106,209,109,250,112,20,116,33,119,34,122,23,125,255,127,255,127,217,127,98,127,157,126,138,125,42,124,125,122,133,120,66,118,182,115,227,112,202,109,110,106,208,102,242,98,215,94,130,90,246,85,52,81,64,76,29,71,206,65,87,60,186,54,252,48,31,43,40,37,26,31,249,24,200,18,140,12,72,6,0,0,184,249,116,243,56,237,7,231,230,224,216,218,225,212,4,207,70,201,169,195,50,190,227,184,192,179,204,174,10,170,126,165,41,161,14,157,48,153,146,149,54,146,29,143,74,140,190,137,123,135,131,133,214,131,118,130,99,129,158,128,39,128,0,128,249,150,148,221,53,235,27,241,93,244,116,246,223,247,237,248,184,249,86,250,214,250,61,251,148,251,221,251,26,252,78,252,123,252,163,252,197,252,227,252,252,252,18,253,38,253,55,253,69,253,81,253,91,253,100,253,106,253,111,253,114,253,116,253,116,253,114,253,111,253,106,253,100,253,91,253,81,253,69,253,55,253,38,253,18,253,252,252,227,252,197,252,163,252,123,252,78,252,26,252,221,251,148,251,61,251,214,250,86,250,184,249,237,248,223,247,116,246,93,244,27,241,53,235,148,221,249,150,48,117,144,101,8,82,152,58,64,31,0,0,192,224,104,197,248,173,112,154,153,104,33,3,201,9,85,253,154,250,70,2,92,2,6,251,183,13,250,232,182,17,13,254,108,248,195,11,62,236,238,21,58,248,219,251,77,250,90,17,68,253,41,235,1,18,196,1,179,253,232,242,137,11,243,4,68,251,226,245,195,6,86,14,133,238,49,252,39,17,23,246,181,3,173,250,45,252,102,22,66,118,247,14,60,240,156,11,232,251,22,252,173,9,29,244,255,10,73,247,217,6,181,249,178,6,17,249,7,6,16,252,173,1,87,255,216,1,16,251,128,8,110,245,219,9,171,249,88,1,58,3,7,250,188,6,135,249,165,6,241,247,84,10,12,244,81,11,70,248,45,2,12,3,167,250,74,3,143,2,98,57,254,44,244,4,55,245,217,233,90,29,221,255,9,245,32,244,215,18,136,11,24,223,201,14,175,5,131,8,67,222,115,31,201,247,82,250,9,3,84,4,175,246,206,8,149,254,94,253,201,247,158,23,207,233,48,4,51,12,62,236,192,20,231,246,112,241,12,27,207,240,163,2,17,249,29,0,161,39,66,118,247,14,60,240,156,11,232,251,22,252,173,9,29,244,255,10,73,247,217,6,181,249,178,6,17,249,7,6,16,252,173,1,87,255,216,1,16,251,128,8,110,245,219,9,171,249,88,1,58,3,7,250,188,6,135,249,165,6,241,247,84,10,12,244,81,11,70,248,45,2,12,3,167,250,74,3,143,2,0,64,103,65,213,66,76,68,203,69,82,71,226,72,122,74,28,76,199,77,123,79,56,81,255,82,209,84,172,86,146,88,130,90,126,92,132,94,150,96,180,98,221,100,18,103,84,105,162,107,254,109,102,112,221,114,96,117,242,119,147,122,66,125,255,127,3,115,186,110,119,98,225,79,109,57,245,33,71,12,184,250,206,238,23,233,38,233,191,237,33,245,96,253,187,4,232,9,58,12,175,11,211,8,146,4,0,0,23,252,140,249,180,248,126,249,133,251,48,254,218,0,244,2,36,4,75,4,136,3,38,2,135,0,11,255,254,253,134,253,166,253,61,254,25,255,0,0,191,0,52,1,84,1,40,1,198,0,78,0,220,255,136,255,93,255,91,255,124,255,177,255,237,255,34,0,73,0,91,0,89,0,70,0,38,0,0,0,254,254,194,254,73,254,134,253,112,253,251,252,57,253,10,254,244,254,63,255,254,255,125,0,122,0,217,255,247,255,105,0,129,0,27,1,116,1,63,2,235,254,188,254,59,255,25,254,67,254,150,254,220,254,229,255,177,0,31,2,86,1,5,2,4,2,130,0,27,0,152,255,136,255,116,255,182,255,200,255,204,253,81,252,16,250,59,252,210,252,242,253,190,254,254,255,159,0,145,2,200,254,228,254,126,254,171,253,19,254,242,253,94,254,27,255,105,0,193,1,211,253,154,252,205,251,105,252,74,252,16,253,59,253,196,254,62,0,230,1,198,254,65,255,53,255,182,254,96,255,153,255,205,255,131,0,82,1,3,2,10,6,224,8,194,14,112,21,60,27,190,32,63,39,221,43,222,49,146,53,84,37,17,42,27,49,236,51,45,56,131,45,92,41,39,38,145,33,84,25,6,0,82,0,125,255,154,0,200,255,33,253,183,0,191,255,247,254,9,0,46,255,151,254,113,0,206,2,25,7,242,3,190,4,37,6,89,3,53,5,228,8,59,3,32,6,141,7,205,2,197,7,158,8,70,3,148,4,31,7,209,2,232,3,106,8,30,1,220,1,229,5,9,255,237,253,230,0,147,0,174,255,57,2,26,0,79,255,80,252,229,255,239,254,180,2,92,255,248,254,73,255,224,0,22,3,15,4,131,3,178,3,89,2,229,1,3,3,126,4,12,2,165,2,135,3,116,255,119,1,10,3,154,1,164,2,173,1,45,1,18,2,241,3,207,2,134,2,38,0,226,0,111,1,40,0,145,0,211,255,7,254,34,1,121,0,135,255,46,1,127,0,166,0,132,255,129,254,68,252,154,254,57,254,47,252,203,2,110,3,126,3,210,3,155,3,211,0,221,1,16,1,64,0,188,0,178,255,17,0,113,255,191,255,38,0,131,2,74,2,109,2,122,255,86,254,117,253,91,1,33,2,4,11,164,4,166,10,138,9,142,0,176,255,199,6,27,1,130,0,205,1,250,254,113,254,135,251,101,254,155,0,174,1,73,1,119,1,11,3,53,0,30,255,117,255,127,255,20,255,146,6,29,1,232,2,47,5,226,2,185,2,128,6,56,1,153,1,10,1,69,1,208,2,135,0,1,0,221,0,197,1,8,0,203,0,145,0,43,1,128,2,248,2,29,0,212,1,126,2,103,0,173,1,123,1,164,1,186,3,164,3,46,5,186,4,234,4,192,2,244,3,128,4,90,255,68,254,246,254,196,254,126,255,136,254,191,0,127,4,112,7,16,255,225,253,20,251,144,255,12,1,183,4,70,0,38,4,47,6,22,1,80,5,38,6,254,254,240,254,0,253,19,0,51,2,192,8,253,255,247,254,135,0,217,254,177,253,124,254,140,0,98,1,50,255,252,254,8,254,229,252,79,254,50,253,217,250,109,0,75,1,194,3,83,254,169,255,140,2,216,254,170,1,251,3,17,255,7,3,83,3,233,1,54,5,49,4,178,254,180,254,25,0,31,2,182,4,15,7,70,1,61,0,215,2,66,2,81,3,125,5,48,255,235,254,73,1,104,255,64,0,157,2,78,254,90,253,41,253,58,254,185,255,251,0,93,2,224,1,254,0,30,254,11,0,228,3,223,254,139,1,230,1,210,2,25,4,160,5,226,255,196,254,238,252,150,255,141,255,149,253,93,3,194,5,132,5,31,4,86,5,160,4,44,3,213,4,157,3,42,0,5,255,192,253,86,1,141,0,58,254,88,255,176,255,79,5,170,254,112,253,29,249,100,0,53,3,213,2,222,3,235,2,32,3,76,1,184,1,56,2,151,2,123,1,84,3,112,0,165,0,143,254,85,2,142,3,26,1,248,255,66,3,1,5,160,254,60,2,183,2,206,1,198,8,14,7,89,1,190,0,94,5,160,1,147,3,118,8,168,0,174,255,24,1,252,253,66,254,72,3,47,0,21,2,44,0,150,254,57,253,137,251,22,0,193,0,192,5,171,255,233,0,21,7,194,255,67,2,224,5,38,2,176,3,213,6,211,2,138,2,124,4,204,3,116,3,115,5,87,254,131,2,0,0,232,3,184,3,74,4,249,0,166,5,160,2,178,254,169,255,124,8,214,253,90,7,112,10,140,0,34,7,61,7,152,3,213,6,30,10,52,4,141,7,246,7,119,255,69,254,237,249,245,4,150,4,212,1,19,254,134,255,241,5,61,254,9,4,190,4,226,1,159,6,94,4,47,3,137,2,128,1,66,254,76,253,107,0,193,254,163,253,138,255,49,255,7,254,13,2,44,254,244,255,176,10,75,0,142,7,25,5,112,3,54,9,219,8,5,5,39,6,212,7,208,255,208,254,94,251,77,254,51,254,5,255,146,254,108,254,221,253,223,254,163,253,171,253,230,253,214,252,91,255,136,255,3,0,100,1,127,2,217,4,222,5,96,0,177,0,238,2,77,254,183,253,106,251,156,254,109,0,177,255,27,254,32,1,213,7,9,0,92,4,219,2,112,3,86,8,178,3,247,254,49,6,41,4,133,4,186,4,75,3,14,254,100,253,175,1,118,1,65,1,27,255,160,5,53,8,101,5,193,1,205,1,131,4,151,255,39,0,128,254,249,254,111,1,182,0,141,254,108,253,5,3,68,255,127,4,203,3,53,5,96,6,155,5,6,3,243,4,197,4,30,254,192,252,47,250,19,255,46,255,92,3,122,3,79,6,40,4,216,1,38,4,168,4,185,0,53,4,221,3,200,253,32,252,88,249,63,254,122,252,5,248,114,255,135,254,54,254,46,255,214,253,251,251,245,255,109,4,217,8,183,254,93,253,131,252,6,255,145,2,163,4,7,2,230,5,243,6,8,2,27,2,123,5,15,2,141,5,22,5,205,253,153,252,32,251,109,255,49,254,111,3,180,255,30,9,24,11,51,2,13,10,81,9,120,2,134,7,104,11,207,2,231,7,48,7,223,253,45,253,84,4,129,0,131,255,116,3,137,5,96,6,157,3,162,255,30,6,215,6,171,254,253,5,15,6,79,2,139,1,238,254,180,255,213,3,15,11,153,0,169,11,52,7,8,4,5,10,189,10,228,5,16,11,87,7,23,3,175,4,26,2,66,255,59,254,209,5,234,254,220,253,134,4,11,255,149,7,252,7,0,4,24,6,114,6,0,2,253,0,210,1,194,255,189,254,127,4,39,254,136,254,251,1,79,254,100,5,114,8,131,3,151,7,165,5,134,0,192,2,184,1,204,1,13,2,228,255,62,254,23,1,58,5,0,0,203,3,252,0,67,254,141,253,33,252,164,254,166,253,112,250,142,1,200,2,120,6,149,255,58,1,78,255,93,0,178,8,190,8,6,2,81,3,144,2,50,254,57,253,65,254,174,0,222,255,167,4,137,255,42,0,237,3,140,254,18,1,246,2,12,4,48,9,46,7,163,2,188,6,218,5,174,1,6,5,85,8,127,255,73,254,0,0,139,254,32,3,96,8,6,0,51,6,174,9,222,1,84,2,80,8,84,254,32,253,225,5,129,1,178,0,212,3,139,0,193,1,201,4,242,253,182,252,42,252,145,0,18,6,218,4,111,2,168,5,144,2,93,1,248,3,202,5,31,0,232,254,159,1,196,254,212,2,105,6,104,1,34,4,44,2,76,254,154,254,177,4,157,254,99,4,147,7,145,1,48,6,200,8,241,253,12,252,99,1,233,0,238,0,185,8,218,253,127,252,129,253,147,254,11,254,165,7,133,1,68,7,85,6,162,0,108,4,240,4,19,255,150,4,110,5,128,253,101,254,116,0,28,255,158,6,250,8,103,6,138,8,219,8,50,2,249,4,98,10,67,1,82,1,238,6,66,2,83,4,84,3,22,0,82,2,166,3,113,255,206,2,190,1,50,0,71,0,247,255,174,254,70,253,129,250,102,0,118,255,204,252,202,254,43,254,133,251,158,1,67,0,245,254,36,4,46,3,161,5,12,6,80,5,248,4,218,6,103,7,125,6,227,7,85,8,28,7,16,7,14,9,53,7,132,2,163,255,198,1,90,3,73,1,120,255,233,1,254,254,128,255,58,255,23,253,215,255,204,255,247,254,39,252,90,1,137,0,223,1,51,249,20,253,84,253,117,251,67,249,145,254,129,252,135,251,240,252,24,254,78,252,56,252,171,255,122,254,43,253,215,0,172,254,85,255,252,3,148,3,177,7,52,2,179,0,234,2,150,2,209,3,198,6,119,3,110,2,146,3,171,3,88,3,141,4,53,1,176,2,35,3,149,3,161,0,58,2,118,0,236,255,229,254,208,252,214,255,204,0,52,251,187,254,50,254,61,252,54,255,113,255,36,252,28,254,151,254,66,253,46,252,35,254,210,254,234,252,92,251,156,255,238,252,192,251,226,251,77,252,108,249,54,255,181,252,242,252,241,251,158,250,123,252,144,253,146,255,171,255,100,1,213,0,246,255,19,254,108,1,6,3,169,1,54,3,223,1,173,255,45,2,8,2,32,252,232,249,196,253,165,253,27,253,230,255,10,254,130,253,121,252,209,0,50,1,147,0,196,254,175,253,172,253,171,255,45,255,31,255,106,252,239,253,117,0,233,0,73,254,30,253,77,4,239,2,121,2,177,5,180,6,231,5,229,6,177,5,142,3,98,4,132,4,81,3,74,5,100,3,214,1,153,252,130,251,252,248,153,252,163,252,32,252,138,255,155,0,212,0,229,251,175,252,162,253,163,251,199,248,66,245,5,252,109,250,179,248,114,1,72,255,98,254,191,3,237,1,104,0,190,3,15,4,31,2,154,0,141,2,201,0,225,4,251,1,150,0,151,2,247,1,230,0,111,2,9,3,163,2,147,2,88,0,146,255,75,3,244,0,224,0,126,1,29,2,46,1,212,2,177,1,154,2,142,4,222,2,85,1,118,255,20,0,115,254,97,251,88,254,210,255,191,254,160,254,132,255,53,5,253,3,56,4,6,1,110,1,211,2,154,3,27,1,217,253,31,0,132,253,157,253,79,253,71,253,97,254,72,252,245,252,55,255,207,250,170,253,153,254,71,252,251,250,166,0,237,1,49,1,221,0,78,3,191,2],"i8",y,a.GLOBAL_BASE),p([98,2,72,3,168,3,6,3,45,253,212,250,19,251,155,254,255,251,148,250,184,251,160,250,147,254,120,250,167,248,160,253,250,248,65,249,94,253,223,253,107,251,65,253,166,2,18,3,148,0,133,255,184,2,8,5,132,2,94,1,246,255,158,1,102,2,15,0,137,0,88,1,45,255,210,252,24,250,205,252,121,254,94,252,180,253,47,0,177,253,126,252,115,252,183,251,93,255,8,251,113,251,99,255,72,250,11,250,123,254,6,251,92,251,144,253,159,2,213,0,198,1,124,0,238,254,243,253,39,253,16,254,104,255,192,250,122,0,135,0,167,244,179,253,118,254,64,249,185,1,206,255,196,5,136,3,19,3,60,1,236,0,72,254,165,254,217,0,157,1,113,252,107,252,121,0,57,254,92,252,202,0,164,255,47,254,137,254,232,1,134,1,218,1,108,3,217,2,60,1,233,248,224,250,99,253,87,0,194,3,176,1,51,2,7,255,222,251,250,0,29,1,81,4,117,4,171,1,184,2,242,251,128,249,210,249,76,252,90,1,160,0,203,254,240,254,166,252,158,2,112,2,226,4,80,252,104,254,102,253,162,253,192,254,128,254,20,254,230,0,65,0,78,1,206,255,240,255,240,255,78,253,139,250,255,6,180,6,119,5,174,9,15,8,124,5,221,4,191,5,146,5,130,254,243,251,254,255,173,0,114,254,121,4,211,5,232,7,9,7,4,3,250,4,226,5,149,5,199,6,209,7,55,4,194,4,249,4,126,251,197,248,207,250,216,252,147,251,184,251,61,254,247,251,70,249,65,0,66,2,172,255,60,250,126,246,14,249,3,253,170,250,18,254,38,255,174,253,93,252,81,1,20,255,50,2,53,9,102,10,146,7,209,5,252,4,106,3,189,0,102,1,118,1,17,250,23,247,214,246,57,252,9,251,209,247,140,253,92,251,250,249,125,6,19,4,34,2,53,2,37,4,220,2,192,255,188,252,78,254,76,254,160,255,203,0,54,4,192,4,100,6,139,3,254,5,218,3,70,1,197,3,77,3,142,0,172,255,197,0,214,1,75,9,34,6,109,4,214,1,190,4,139,1,96,5,176,4,101,4,18,4,92,1,225,253,46,251,136,254,41,255,75,255,225,1,101,248,171,249,46,255,18,253,95,251,134,1,29,0,113,254,27,0,52,3,212,4,243,2,183,2,211,3,153,1,82,255,173,4,11,4,144,3,76,5,54,7,32,252,99,250,228,1,51,250,92,249,208,0,100,254,180,4,152,5,241,254,128,3,120,4,96,254,241,6,154,5,96,249,172,245,52,255,3,249,241,249,9,4,136,249,233,249,23,5,27,251,203,249,57,4,99,253,185,251,190,255,86,253,64,1,167,254,147,2,49,1,45,4,244,250,220,252,237,255,157,249,245,250,29,0,109,249,15,254,71,0,225,254,249,255,156,255,18,254,62,252,19,255,84,3,89,7,204,6,63,251,149,250,227,0,108,253,46,1,117,1,96,0,63,4,233,4,206,251,123,249,160,0,229,1,28,8,6,7,90,252,36,255,40,2,172,253,156,253,237,0,80,1,184,6,111,3,131,2,117,2,178,1,243,4,10,2,97,6,15,0,244,0,71,254,195,5,205,2,184,0,27,7,54,6,173,6,220,3,5,1,169,3,45,8,41,9,240,5,91,8,66,7,70,6,191,253,189,253,77,251,68,252,135,0,24,254,48,254,51,0,174,254,139,253,164,254,45,253,122,4,25,8,162,5,144,8,186,5,143,3,92,250,220,249,26,247,120,5,198,2,17,5,55,5,121,2,160,3,154,5,146,8,34,10,118,9,156,8,89,7,214,3,194,8,62,7,124,1,24,3,121,4,193,255,229,253,158,1,4,255,60,252,198,254,19,251,85,253,244,252,193,252,242,253,19,252,126,249,145,251,88,254,181,249,60,254,213,254,244,4,24,4,130,2,123,4,85,3,88,3,93,253,176,254,139,0,220,8,63,5,138,5,29,0,0,3,29,3,56,251,167,1,52,2,218,250,198,251,245,0,234,250,212,252,61,2,238,250,175,249,134,2,56,252,66,3,211,2,225,3,116,6,235,7,65,255,207,252,176,1,150,2,60,0,198,0,114,2,229,3,50,5,112,6,171,7,9,5,195,249,163,255,211,255,192,251,37,0,172,255,117,6,47,10,33,9,41,4,248,7,73,9,115,4,22,9,70,8,91,3,101,1,230,5,152,2,203,4,75,4,223,1,80,5,144,3,105,7,218,6,227,7,144,4,117,7,248,6,143,1,34,0,0,1,175,253,208,254,227,251,35,2,158,6,127,5,135,2,157,255,171,254,212,5,111,6,166,4,38,0,124,253,44,255,139,1,78,3,222,0,64,253,3,253,52,253,44,253,84,248,12,245,106,255,35,1,174,255,209,4,179,5,239,3,116,255,101,255,153,0,183,1,41,1,32,6,7,250,102,254,132,253,0,6,199,1,19,255,208,250,117,255,252,254,19,2,42,2,100,3,13,1,240,4,94,2,23,255,115,3,207,1,230,2,88,2,136,255,183,255,165,1,212,0,73,254,198,255,36,3,250,250,39,251,216,2,38,1,22,254,50,0,177,253,119,252,26,251,42,0,81,253,147,0,231,255,17,1,84,2,201,254,189,4,89,2,14,253,81,3,72,2,173,1,95,2,75,2,166,253,90,255,205,1,228,252,201,252,9,3,100,5,142,3,219,6,119,0,137,5,204,3,37,255,144,252,196,249,231,251,14,252,182,1,55,253,157,250,78,0,0,0,65,254,101,251,144,251,217,250,219,249,200,8,231,6,29,5,178,3,47,6,152,5,126,4,226,1,180,1,43,254,172,251,106,2,65,254,58,252,64,4,28,251,21,250,142,255,176,251,40,248,189,253,210,0,101,2,241,1,73,248,99,250,130,2,11,251,168,252,243,3,146,249,95,251,39,4,237,249,96,253,180,4,100,249,166,251,111,2,45,252,210,250,3,251,27,2,109,255,126,3,182,250,127,252,78,254,120,3,219,1,172,1,153,0,128,254,82,1,44,250,1,254,103,1,50,252,165,251,42,254,105,0,218,253,165,2,87,252,135,251,109,3,124,1,252,254,210,0,149,6,156,3,232,4,239,6,166,4,71,4,139,5,119,2,21,2,115,2,43,1,165,254,101,254,234,253,135,2,118,253,29,0,173,253,134,254,169,250,27,6,122,5,97,4,185,5,65,4,130,5,136,2,208,247,190,251,250,255,55,1,62,255,155,252,129,253,193,252,160,1,118,251,56,251,69,5,33,251,83,252,21,7,111,247,61,248,197,1,149,253,169,250,68,252,186,249,76,248,29,250,105,251,223,251,176,251,135,254,89,2,201,0,84,7,57,3,118,1,82,254,213,250,29,0,139,250,31,251,205,250,17,252,32,250,192,3,135,250,39,248,197,0,157,250,99,248,20,255,203,251,123,0,166,1,103,2,245,4,34,2,206,254,246,5,136,3,170,4,252,6,153,4,142,253,140,252,10,250,199,0,254,2,224,5,215,251,94,3,197,0,246,251,19,249,137,252,224,252,145,0,87,2,146,251,249,253,114,2,75,251,122,248,244,1,114,252,239,251,141,250,60,250,225,249,55,252,245,253,74,3,34,0,2,7,134,2,94,3,73,251,160,248,22,252,178,255,247,255,96,253,20,4,247,2,80,0,168,253,115,4,251,3,57,0,208,7,142,5,191,252,134,5,97,4,78,251,94,6,236,4,51,254,140,5,220,4,1,6,207,3,253,0,229,254,68,1,153,254,87,2,61,255,106,0,76,2,62,0,181,253,11,253,133,2,205,0,51,0,177,4,246,2,71,251,161,2,122,254,144,253,45,6,173,3,105,255,255,3,223,2,4,11,21,5,178,2,210,254,12,2,157,255,124,252,204,249,91,251,60,4,251,0,238,0,222,7,0,7,242,3,221,4,97,6,205,6,53,251,252,249,72,251,147,253,200,1,147,255,40,0,191,255,20,3,219,252,69,253,186,250,185,253,136,3,64,3,223,252,20,2,82,2,180,7,128,5,71,5,103,251,168,248,190,247,251,252,56,2,180,3,9,252,55,4,236,4,169,251,226,1,126,255,242,6,20,4,12,3,45,250,245,0,144,3,196,254,139,251,107,252,232,253,94,250,214,246,239,252,246,249,60,248,45,248,1,1,141,3,199,248,135,253,71,251,254,249,130,248,226,251,70,6,191,8,40,6,201,253,36,250,248,249,1,251,195,0,89,5,207,252,37,1,195,4,243,253,118,2,173,4,94,249,135,246,208,248,209,254,219,2,235,2,111,251,5,255,13,1,74,252,181,255,148,6,98,251,59,254,237,3,193,249,73,2,122,1,229,247,197,253,85,254,239,253,121,251,109,251,229,254,51,255,204,253,228,252,222,4,205,2,229,8,159,3,27,2,58,254,47,2,184,1,51,253,180,5,79,6,250,251,28,4,74,6,111,251,118,255,79,3,226,0,39,0,156,253,29,251,150,255,39,253,117,253,200,3,22,5,54,253,132,253,191,6,97,1,45,4,154,1,226,252,100,255,75,4,194,253,150,3,190,1,226,250,244,3,210,1,128,5,55,6,253,2,149,5,100,5,221,6,157,7,164,7,74,9,42,6,255,7,100,8,148,3,98,0,249,255,101,7,138,5,93,8,92,1,125,5,43,6,152,0,110,4,9,7,245,254,154,0,115,5,114,251,213,1,30,4,138,251,107,254,207,251,195,250,40,247,211,249,148,254,101,3,170,6,118,251,37,2,14,6,55,251,116,248,126,249,51,250,71,248,249,247,65,249,118,252,158,255,151,248,233,0,212,5,124,3,108,0,181,254,64,249,110,251,92,249,220,251,188,7,254,6,210,251,51,249,139,248,245,255,3,6,37,5,192,249,94,0,241,1,165,1,187,1,59,255,214,249,163,254,30,252,169,253,229,253,116,4,59,252,117,250,127,255,195,250,175,0,65,254,137,254,31,5,7,8,141,254,118,253,205,254,207,251,93,2,109,1,247,247,143,255,174,1,140,2,146,3,199,3,12,252,206,249,237,246,225,5,224,4,47,2,6,1,26,254,111,254,65,249,62,5,10,6,50,0,56,0,176,1,182,254,119,0,164,253,19,250,200,251,214,252,178,3,103,4,31,4,136,250,89,249,80,249,10,251,64,253,219,250,39,3,29,7,119,4,200,10,70,6,123,8,96,4,153,1,106,255,109,255,148,1,191,3,135,9,119,7,141,8,118,252,115,255,158,252,120,252,114,255,54,254,211,253,60,253,113,249,194,252,105,250,209,249,206,248,190,250,194,251,188,249,240,254,147,3,84,251,4,3,32,4,130,253,46,251,151,248,12,254,175,255,202,252,247,250,179,249,33,253,139,255,17,3,168,0,190,251,109,4,154,3,184,251,22,253,104,5,31,1,221,253,217,251,160,250,103,247,76,251,128,247,222,249,35,249,25,250,63,247,253,252,55,249,75,4,62,3,204,249,212,2,219,4,250,249,181,2,37,3,102,249,16,255,129,6,92,249,252,255,100,253,101,8,48,3,18,4,206,252,207,248,22,0,4,253,5,254,193,1,129,251,151,253,33,1,181,252,196,249,16,255,242,1,22,255,111,253,16,253,224,1,142,6,193,254,31,254,193,0,213,252,171,0,137,255,176,247,54,255,176,252,181,6,116,4,164,6,67,0,239,255,66,0,244,255,102,249,187,253,152,255,240,254,204,251,94,251,203,248,136,254,140,251,98,252,92,254,198,255,253,254,112,253,146,251,215,253,252,6,203,4,199,1,129,0,206,1,185,1,16,255,240,253,72,3,2,2,130,0,181,255,90,4,111,2,153,0,216,0,44,4,52,2,250,255,236,254,95,4,215,2,190,0,188,255,192,2,50,1,119,0,248,254,73,1,61,0,156,255,156,0,108,1,123,0,183,0,48,255,85,255,133,255,220,0,191,255,206,254,194,255,146,1,17,0,108,253,86,252,246,254,0,0,129,1,235,0,20,1,29,1,64,1,12,1,176,254,56,255,44,253,17,0,172,255,125,1,224,253,173,1,238,1,7,2,139,255,32,1,48,1,73,1,131,2,157,0,189,2,252,1,176,4,113,2,28,3,96,2,230,3,165,1,236,1,120,2,180,4,12,3,190,1,132,0,233,4,76,3,35,2,193,1,61,3,146,2,29,2,214,1,108,4,234,4,150,3,127,2,35,2,51,0,167,1,23,1,9,0,136,1,83,0,94,0,30,2,31,2,229,0,109,255,58,255,129,0,194,0,71,255,161,252,215,250,210,254,30,0,171,253,139,253,237,255,114,0,124,252,199,251,210,1,97,1,53,250,219,249,15,0,113,255,84,249,245,247,17,253,196,0,172,248,237,247,126,253,254,254,225,246,66,250,62,254,204,253,184,253,70,255,152,252,98,254,243,248,36,252,155,251,226,250,42,253,151,251,28,0,169,0,241,251,160,252,50,253,10,255,228,1,36,0,23,255,207,255,9,1,67,0,33,1,211,1,178,0,31,2,42,3,28,2,84,0,26,1,160,2,191,2,49,252,247,252,129,0,31,1,86,252,29,255,187,3,83,2,175,249,223,254,68,3,137,2,201,248,41,255,82,4,206,2,14,248,195,251,138,2,184,1,203,247,239,253,139,3,63,2,37,248,176,254,158,2,204,0,171,246,76,253,104,1,137,0,148,247,100,247,247,255,24,1,246,254,119,0,39,0,193,0,78,0,197,255,136,255,226,0,49,252,166,252,243,252,185,251,149,253,99,254,61,254,182,252,64,251,215,250,211,252,141,252,160,250,177,249,118,254,84,254,31,253,167,251,219,253,234,252,144,252,49,252,57,252,126,253,39,252,138,252,7,251,175,250,39,254,220,252,135,250,129,250,160,0,247,254,105,252,237,254,8,255,6,255,50,253,132,254,97,0,153,255,137,254,27,255,97,254,63,255,121,255,213,253,116,2,105,1,119,0,216,0,67,2,108,1,135,1,209,0,122,2,10,2,102,255,108,255,14,2,133,1,170,0,33,0,105,0,11,1,64,0,124,1,33,250,24,252,226,255,143,254,210,251,58,0,135,2,223,0,16,250,221,254,109,2,51,1,5,250,156,0,250,2,148,1,19,248,141,0,222,2,243,1,199,248,118,253,50,1,0,2,69,255,152,255,197,255,182,1,134,0,26,255,156,0,70,255,195,255,252,254,240,255,10,0,199,253,253,255,91,254,215,254,67,249,247,253,166,254,178,0,174,250,197,255,212,255,157,0,158,247,51,254,42,254,163,254,134,247,255,255,143,254,135,255,213,249,139,254,124,252,9,252,163,251,177,253,155,253,240,252,207,253,122,0,181,255,63,254,252,255,85,255,133,255,140,254,192,0,168,0,180,255,124,255,252,0,149,255,84,1,210,0,136,1,253,1,16,1,181,0,147,255,145,0,218,0,119,0,96,254,249,254,229,1,9,1,75,255,248,255,226,254,226,0,12,255,38,255,69,0,222,254,98,255,191,0,255,255,192,255,176,253,166,255,213,0,160,255,255,0,179,1,178,0,176,255,143,254,238,255,223,255,176,255,214,255,159,1,140,0,34,255,119,4,139,2,137,2,73,1,255,2,44,2,249,0,235,0,180,3,157,1,186,1,23,1,141,0,83,1,100,1,45,2,42,254,86,255,99,0,237,0,199,253,224,252,96,1,53,2,26,1,217,1,214,1,76,1,57,255,78,253,252,250,107,252,63,255,86,254,224,252,158,251,230,255,141,254,22,254,63,255,125,2,83,2,7,2,74,1,152,1,141,255,79,0,12,0,221,1,87,0,153,255,136,254,102,253,165,254,235,254,221,254,2,254,31,254,169,0,41,1,195,252,30,253,51,255,85,255,192,254,228,253,72,1,27,1,165,252,66,252,186,1,254,255,44,2,174,2,130,0,56,0,103,5,244,3,243,2,171,1,100,2,229,2,116,2,41,2,173,254,228,252,134,0,21,1,135,253,195,251,254,255,10,255,144,252,245,251,185,249,216,251,30,252,38,254,142,251,24,254,98,254,229,252,73,0,50,255,248,255,117,255,183,1,204,0,80,255,190,253,23,0,131,0,243,254,11,253,65,255,245,0,147,255,174,254,112,0,60,1,120,0,106,254,138,255,99,2,76,255,70,255,123,253,115,0,83,255,34,0,250,253,23,254,105,255,61,0,185,253,180,252,220,0,118,255,87,253,4,252,135,1,239,255,170,253,191,254,157,0,217,254,129,0,155,0,98,252,149,252,37,252,29,1,241,0,173,255,131,255,131,255,108,2,85,2,176,1,92,0,137,1,78,0,153,1,61,0,119,254,29,253,99,254,20,253,83,0,54,0,105,1,27,0,196,251,130,0,175,254,74,253,227,249,41,1,62,1,237,255,175,248,36,0,51,0,195,254,237,246,10,255,231,0,172,255,254,246,241,252,40,0,77,255,71,247,94,252,38,254,50,254,14,253,170,255,224,254,142,253,149,246,57,254,193,255,171,0,181,251,186,251,230,255,113,255,87,251,57,254,106,254,131,254,163,253,46,255,160,255,205,255,188,253,36,254,236,254,241,255,85,251,134,253,77,251,143,252,134,254,35,255,99,253,72,252,82,2,178,0,109,254,92,253,251,2,71,1,89,2,34,1,172,0,44,1,203,0,157,0,200,255,176,254,100,1,24,0,28,255,216,254,253,254,227,255,70,255,7,1,160,1,14,0,159,254,117,1,244,255,40,255,1,1,96,0,174,0,57,0,10,250,152,253,70,252,13,254,15,254,104,255,179,254,125,0,105,0,200,0,179,0,159,255,181,254,32,255,253,2,185,2,248,2,0,1,45,1,59,0,199,1,171,255,204,0,32,1,254,253,240,0,251,0,147,255,0,1,161,1,222,255,99,254,101,0,174,1,128,1,156,0,225,255,246,255,206,0,170,1,77,2,145,0,143,0,71,0,40,3,138,3,77,1,93,1,218,3,170,3,77,2,75,1,20,5,56,3,187,0,253,1,38,4,141,2,123,1,210,1,182,5,169,3,145,1,18,1,19,3,93,3,9,1,2,0,97,2,41,2,28,0,49,1,158,3,84,1,106,0,130,1,241,0,245,254,109,255,225,0,78,255,234,253,91,1,246,1,125,253,131,254,141,1,30,0,117,253,35,253,77,254,142,1,105,254,42,253,28,254,8,255,235,252,110,252,74,254,36,254,14,254,122,254,75,0,217,254,60,252,178,253,162,253,150,0,135,255,207,255,101,255,178,255,167,3,38,2,133,1,38,0,191,254,127,0,168,1,59,1,227,254,143,255,27,1,3,1,146,2,203,0,66,1,230,1,135,3,249,1,236,2,161,1,99,2,167,1,43,2,0,2,239,0,173,255,190,253,237,255,173,254,37,253,93,1,13,0,90,252,137,250,142,255,152,254,107,0,180,2,182,0,90,0,37,251,254,249,241,249,43,253,200,253,121,252,173,250,243,253,251,253,171,252,163,252,20,252,88,255,78,253,189,252,63,0,119,255,212,253,221,253,144,0,226,254,207,252,229,1,63,1,109,255,104,254,14,2,246,0,165,254,78,254,41,1,228,255,222,254,41,254,170,251,251,250,52,254,153,254,36,252,230,252,67,5,19,5,178,2,11,2,192,4,44,4,70,4,245,2,57,3,116,4,240,2,238,1,228,4,85,5,171,4,130,3,9,2,29,4,20,2,176,1,178,254,40,255,199,254,249,254,96,255,52,0,40,254,101,255,127,0,136,0,132,254,44,0,83,3,154,1,94,255,23,254,123,0,1,255,228,252,101,253,66,4,149,3,21,3,237,1,117,5,173,4,46,2,202,0,205,255,138,255,170,254,67,253,83,0,108,0,214,255,71,254,61,0,95,0,31,1,0,1,229,255,89,0,12,2,19,2,95,1,227,0,80,2,33,2,185,2,155,0,92,255,51,1,126,2,18,1,23,254,206,255,242,2,240,0,90,255,132,255,140,255,189,253,68,251,193,255,190,0,217,254,240,251,240,250,147,0,136,254,79,255,143,255,73,3,217,4,27,4,156,2,2,0,37,1,39,2,48,1,184,251,71,252,8,255,120,1,18,253,59,252,87,0,4,2,237,254,252,253,177,2,135,1,133,254,125,253,108,3,82,2,122,254,11,252,123,253,61,2,149,255,200,253,79,253,198,252,255,251,229,255,184,254,53,255,93,3,237,2,36,2,233,0,132,249,237,251,195,1,108,0,108,253,148,253,174,1,236,0,21,0,116,254,122,251,137,253,92,5,18,5,199,3,65,2,101,4,101,4,77,2,198,1,189,254,159,252,45,254,153,0,44,254,69,253,220,252,3,254,120,254,50,253,52,255,221,255,165,253,187,251,201,253,94,255,7,254,20,252,154,255,94,1,219,0,224,0,167,1,252,0,139,1,79,2,96,2,107,1,22,253,160,255,117,1,172,0,171,0,39,1,202,2,83,1,233,0,77,0,107,0,21,1,157,0,153,0,13,254,156,254,11,6,49,4,64,2,238,1,220,254,173,254,8,254,176,253,121,252,184,255,149,253,31,254,198,249,163,251,201,253,2,255,231,252,5,254,204,253,221,254,20,254,236,253,246,1,48,2,130,254,171,1,88,2,230,0,29,255,221,1,251,0,75,0,29,1,74,3,45,3,220,1,226,250,203,250,186,0,121,1,181,253,107,252,131,2,125,1,94,251,215,253,155,1,82,0,153,251,204,252,82,255,228,253,164,253,119,0,31,2,205,0,132,254,145,2,141,3,55,2,112,0,214,254,138,254,114,0,167,252,5,255,56,0,159,0,145,1,89,1,222,255,116,255,145,255,161,253,41,0,102,2,99,1,142,255,179,255,218,1,66,2,56,0,170,5,156,3,74,4,140,5,229,2,144,1,246,0,22,0,76,2,57,1,135,255,71,1,63,3,216,1,142,251,160,253,88,3,40,2,39,251,208,251,126,2,88,2,154,254,254,0,179,254,209,254,122,253,227,2,102,1,74,0,202,4,135,6,197,4,81,3,193,8,88,6,215,3,124,2,49,7,197,5,237,2,128,1,94,1,7,1,87,0,128,0,146,248,83,252,112,255,192,255,58,249,1,255,32,1,225,255,172,245,42,251,110,1,235,0,149,249,188,251,192,250,208,254,227,253,205,251,164,251,123,0,102,251,4,255,208,252,76,255,8,252,21,2,53,2,233,0,25,254,82,254,68,255,78,1,99,3,212,4,22,2,171,0,202,249,185,249,123,2,118,2,108,247,54,1,156,3,156,1,202,246,184,254,188,3,17,2,177,245,135,254,118,2,22,1,214,245,61,1,31,3,43,1,154,246,133,0,84,1,31,0,148,247,68,250,131,0,125,0,96,251,22,254,117,255,46,0,24,253,191,1,123,3,52,2,67,0,61,254,134,2,92,2,215,253,83,254,148,252,140,1,162,0,190,255,25,5,147,3,223,1,67,2,64,4,26,3,194,1,22,1,54,2,68,1,223,251,102,255,148,0,79,255,15,246,168,0,46,4,80,2,209,246,214,255,51,3,89,1,216,246,61,253,209,2,250,0,129,247,39,250,203,254,122,0,178,255,183,255,120,0,173,0,252,255,6,1,249,254,251,254,81,254,192,255,107,254,36,253,207,245,116,0,173,255,63,255,11,250,80,252,35,254,43,253,4,254,51,1,170,0,172,0,64,3,161,1,64,3,174,2,31,255,177,0,126,3,50,3,30,254,123,254,255,4,15,4,129,254,201,0,162,254,40,0,218,2,123,2,226,0,14,2,247,1,206,1,82,1,142,1,23,2,202,2,40,0,230,254,202,5,191,5,61,4,219,2,25,6,48,4,141,3,181,2,139,5,2,5,121,3,111,3,129,4,216,2,162,4,72,3,30,255,106,4,181,3,177,2,18,254,38,252,236,249,128,255,200,253,47,253,55,253,230,255,61,1,12,2,70,0,135,0,107,254,159,252,26,249,116,253,82,255,223,252,117,3,5,3,103,255,165,255,75,4,239,2,6,254,131,251,85,3,134,2,241,0,14,3,7,2,27,2,61,7,164,6,77,4,172,2,31,251,50,250,48,254,188,0,131,252,127,250,224,250,171,254,121,255,182,1,81,255,18,0,87,4,208,3,63,1,208,0,106,250,24,249,83,0,202,1,238,253,24,252,51,1,129,0,184,252,241,255,227,255,156,254,113,252,100,252,133,251,14,255,137,255,240,253,127,0,123,255,7,253,3,253,190,0,173,255,197,254,127,3,10,2,231,0,34,255,102,0,193,255,84,254,60,1,187,2,123,1,70,0,25,0,204,2,58,1,148,255,251,1,106,3,54,2,238,0,108,0,173,3,7,2,195,0,169,1,196,255,85,254,1,1,139,0,153,255,138,253,190,1,78,1,114,1,156,1,48,0,84,255,78,253,229,254,45,2,187,0,226,254,158,0,227,1,140,0,14,1,168,254,137,253,156,3,67,2,140,255,132,0,142,0,210,1,188,255,192,255,230,0,111,255,210,254,226,253,221,252,112,252,250,3,225,2,251,252,247,3,118,2,41,1,220,245,95,0,189,1,80,1,182,247,235,1,254,1,191,0,27,251,161,0,254,255,188,254,86,250,135,253,56,253,151,255,182,252,2,255,101,254,100,0,128,253,222,254,242,3,251,2,118,253,57,1,145,4,218,2,140,0,249,1,6,4,254,2,4,3,31,1,43,4,55,3,239,1,237,2,49,1,67,1,92,255,206,1,78,0,143,1,170,254,150,252,69,0,85,2,240,255,108,2,109,2,81,1,118,255,68,254,247,254,218,0,84,0,62,254,185,3,154,2,34,255,221,252,29,2,92,2,103,252,160,250,244,0,116,0,183,252,45,253,118,2,76,2,140,0,151,2,38,1,112,1,167,3,22,4,113,3,247,2,210,6,184,5,148,3,116,2,180,1,195,3,25,1,1,0,137,255,74,0,30,2,213,0,1,0,201,253,45,1,241,0,4,1,179,1,222,0,140,1,168,3,189,3,84,4,191,2,254,1,250,1,40,3,222,1,89,2,182,2,192,3,108,2,204,3,229,2,212,3,88,2,66,3,205,2,255,2,172,2,131,2,204,3,167,3,126,2,245,1,149,2,208,2,83,3,151,255,136,253,209,254,139,255,83,254,130,0,21,3,186,1,246,253,68,255,192,2,117,1,9,253,42,0,46,3,11,2,237,253,143,251,117,1,66,2,86,253,77,251,57,254,29,1,117,251,215,249,182,251,44,0,81,0,174,255,200,2,107,1,221,1,246,0,186,3,110,2,68,6,86,6,253,4,123,3,129,5,91,3,156,3,124,3,6,3,17,4,179,3,118,4,40,0,222,253,181,255,32,1,152,253,150,255,71,253,230,255,87,255,96,255,133,252,29,253,233,254,128,254,251,251,162,254,245,6,28,5,22,4,48,3,44,6,253,5,192,5,154,4,225,5,52,4,192,4,131,3,122,3,136,3,52,2,142,2,152,3,180,2,253,3,88,3,19,254,132,0,177,0,249,1,71,0,195,0,228,255,97,0,200,1,95,1,92,255,88,0,183,1,22,1,216,255,94,1,115,5,181,3,234,0,161,255,219,252,40,254,38,0,93,255,111,1,158,255,233,1,11,2,1,4,154,4,188,4,138,3,63,1,34,5,46,3,205,1,133,255,225,253,220,252,191,1,20,253,188,254,127,252,153,251,31,253,11,254,235,252,55,253,203,2,9,3,215,4,154,3,157,7,147,7,88,5,97,3,218,2,112,3,246,2,132,1,153,252,198,1,17,0,5,255,131,254,214,252,209,249,239,0,247,253,58,252,232,252,3,1,134,252,178,250,254,252,183,255,166,0,93,1,44,255,67,1,184,252,211,254,217,1,179,1,89,253,48,254,216,2,95,1,100,255,57,255,155,2,176,1,29,0,4,255,159,1,224,1,37,253,133,254,145,0,47,2,240,253,137,253,122,251,97,255,189,1,17,1,123,0,127,2,117,1,130,255,32,3,56,2,84,0,94,255,208,2,200,2,194,252,232,253,71,255,222,0,152,1,196,1,245,1,3,3,127,252,181,250,189,255,186,1,232,252,130,250,54,2,90,2,167,0,186,254,253,1,74,1,161,255,142,253,38,253,168,254,132,6,193,4,11,3,199,1,36,5,60,3,72,2,207,2,148,1,225,255,245,3,21,3,89,0,107,0,123,3,37,2,103,3,45,6,149,3,159,2,98,3,199,5,9,5,86,3,135,1,44,4,98,4,44,3,78,0,206,253,89,1,51,2,173,1,153,255,161,1,19,3,134,255,75,254,155,1,20,3,111,252,95,254,90,2,242,2,30,255,240,255,151,0,248,2,68,253,118,0,152,255,242,255,152,251,48,0,28,1,137,1,122,254,93,254,129,253,140,255,114,252,50,1,60,1,243,255,183,4,216,3,53,3,157,2,85,251,75,253,140,0,43,255,140,252,96,254,57,255,210,253,152,253,245,0,108,254,104,253,6,1,56,0,151,253,44,253,171,255,21,254,192,254,112,253,198,253,193,252,127,255,240,253,30,250,193,255,145,254,127,254,154,254,191,254,4,0,51,0,146,254,42,255,63,1,255,1,146,0,159,2,239,255,221,254,146,255,208,1,117,255,16,254,54,255,220,0,200,254,137,253,108,253,183,255,113,253,204,252,106,253,115,253,248,250,167,252,82,254,71,252,65,252,248,254,207,255,44,254,184,255,131,254,162,254,205,253,63,255,105,254,55,0,104,254,221,252,11,0,203,254,137,2,188,0,58,255,0,254,205,1,177,255,54,254,218,250,249,254,122,255,245,253,135,249,77,254,17,254,3,253,57,0,165,254,98,254,178,1,139,251,14,255,104,253,167,252,34,0,188,255,61,253,174,254,163,1,163,0,226,255,250,254,57,254,235,252,106,250,47,253,238,3,152,2,13,1,25,0,107,2,4,1,183,0,96,0,56,252,178,250,124,254,135,0,75,253,67,3,200,1,154,0,81,4,191,2,57,2,107,1,89,6,46,5,217,3,236,2,36,255,219,0,76,0,48,255,81,250,130,249,49,0,149,0,60,252,84,255,16,253,176,254,113,2,209,0,6,255,190,255,7,252,186,252,254,255,61,1,136,247,51,250,118,255,123,0,172,248,205,247,247,253,85,0,57,252,146,254,73,253,143,252,103,252,13,252,5,253,75,252,132,255,0,255,160,254,108,253,178,0,207,1,98,1,48,1,48,249,177,253,230,254,79,0,55,247,175,0,99,3,243,1,118,255,76,255,75,255,235,255,13,247,39,251,52,254,248,253,253,252,195,1,246,255,204,254,15,1,191,255,4,0,214,0,233,254,77,254,213,255,164,254,98,253,35,0,191,255,45,255,38,3,23,2,85,0,41,1,57,0,239,0,210,2,237,1,225,0,149,2,72,3,35,2,228,253,136,254,14,0,93,1,213,1,209,2,75,1,162,0,224,253,16,253,194,255,246,255,142,1,168,255,212,2,189,2,237,255,235,253,162,255,89,2,136,0,185,255,87,253,21,253,90,255,168,254,5,1,206,255,161,0,204,255,229,1,81,1,117,249,50,0,190,0,163,255,22,247,25,255,62,255,174,255,161,255,173,253,102,255,128,0,126,3,245,1,76,2,201,1,167,254,206,0,122,0,110,0,137,253,29,255,199,253,3,0,152,1,239,0,141,1,226,0,59,255,254,255,128,0,235,1,1,5,136,3,36,1,215,0,26,2,50,1,3,1,253,1,91,253,233,251,13,0,65,1,89,253,180,253,154,254,44,255,210,253,243,0,134,2,223,1,230,1,86,1,122,2,20,2,107,0,34,3,75,1,136,0,144,255,114,254,249,251,226,254,186,254,63,253,32,1,16,1,19,5,120,4,154,4,92,3,89,254,121,0,127,254,108,255,217,254,210,254,190,252,205,252,16,0,232,255,55,255,36,254,43,2,91,0,11,255,38,1,218,255,133,254,62,252,59,251,89,251,18,250,239,254,117,254,122,254,11,252,123,253,61,2,205,248,250,251,249,1,212,1,232,2,179,3,97,2,237,1,79,253,108,251,140,253,121,255,254,251,195,0,155,1,196,0,46,6,123,4,63,2,81,1,41,251,247,252,120,253,114,255,83,2,57,3,199,3,223,2,74,251,54,252,175,255,170,254,23,253,13,0,184,255,119,1,198,1,19,0,127,5,153,3,145,249,84,255,93,3,50,2,160,3,1,6,39,4,228,2,88,246,72,252,8,1,82,0,10,254,59,252,202,250,123,0,99,3,212,4,22,2,171,0,240,246,52,254,12,3,107,1,90,251,151,253,252,0,195,255,82,255,34,0,243,3,20,3,227,246,247,0,167,1,153,0,240,255,157,254,6,1,193,1,216,249,207,251,224,253,141,254,153,253,207,254,27,4,37,3,175,2,16,2,6,0,74,255,167,3,107,3,234,3,41,3,199,0,1,1,126,0,76,0,184,253,142,251,87,2,44,2,175,251,145,250,201,249,249,253,47,252,211,250,108,0,91,1,46,253,49,252,109,1,101,0,111,255,169,2,249,0,103,255,0,0,178,254,198,253,159,0,156,1,29,1,176,254,151,253,71,252,58,252,119,3,177,2,29,251,84,0,71,255,114,254,176,253,177,1,20,4,141,2,85,0,73,1,216,255,105,1,79,254,63,253,210,1,62,2,102,255,142,2,80,2,34,1,89,255,72,0,93,1,175,0,162,2,41,1,209,3,208,2,211,4,180,4,245,2,232,1,112,254,243,254,26,2,116,1,186,250,149,250,86,251,165,255,238,4,108,3,7,3,188,2,169,253,218,255,82,254,46,253,184,7,94,6,223,3,96,2,111,0,20,1,30,255,160,255,77,252,124,254,245,255,249,255,209,254,237,253,185,252,82,1,198,6,174,6,125,5,245,3,252,253,169,252,123,253,210,0,80,253,96,254,1,2,230,0,202,252,131,253,134,251,192,254,72,252,110,253,74,253,183,0,142,255,145,253,50,3,162,2,65,255,52,255,219,2,123,2,51,0,197,4,115,3,64,2,70,252,81,254,58,3,86,2,170,254,13,253,124,252,105,254,154,251,158,254,50,255,0,254,221,253,214,252,155,254,148,253,66,0,3,2,183,255,102,254,152,252,79,252,92,250,53,251,191,0,239,255,224,253,25,255,252,249,224,253,123,252,138,252,134,252,242,249,19,246,205,252,54,252,175,0,198,252,46,251,6,253,169,253,234,255,122,2,213,252,37,252,122,252,189,254,203,0,26,0,129,254,21,255,243,252,113,254,238,4,138,3,92,252,137,250,156,250,144,253,93,0,87,0,98,254,229,253,77,253,37,0,121,2,254,1,125,254,36,254,206,250,143,1,66,0,7,1,105,254,207,255,177,254,95,254,17,4,73,7,245,252,191,251,96,250,22,253,166,252,64,3,187,253,9,253,141,254,95,253,6,254,40,8,208,253,134,253,101,251,15,1,241,0,14,0,74,254,12,255,115,254,207,1,178,4,23,4,162,253,227,252,98,250,205,255,189,254,225,1,32,255,184,253,241,253,238,1,113,3,170,2,79,254,206,254,22,252,42,2,147,2,222,0,171,0,96,255,159,254,169,2,6,7,29,6,172,252,99,251,97,249,176,254,102,253,114,0,187,253,12,253,24,253,61,255,119,1,241,1,47,254,220,252,182,251,154,0,26,1,125,255,206,255,65,255,49,253,67,1,220,2,6,6,46,253,205,252,132,250,105,0,6,255,185,0,78,255,10,254,26,253,65,1,254,1,87,4,189,254,201,253,58,252,127,0,228,1,82,1,96,255,52,0,174,254,220,2,87,5,18,6,142,253,222,252,96,249,226,254,182,253,164,2,73,253,169,254,142,254,22,254,39,1,101,7,138,253,194,253,10,252,176,255,133,2,187,255,250,255,194,254,148,254,14,3,170,5,14,4,199,254,35,253,141,250,120,0,60,0,221,1,248,254,183,253,133,255,199,2,221,4,121,2,165,255,157,254,8,252,3,3,246,2,5,1,253,0,81,0,38,254,162,3,167,8,184,6,216,252,181,251,123,248,208,253,242,252,169,0,220,252,206,251,68,255,142,253,201,255,125,5,74,253,52,253,86,251,108,253,98,1,73,1,254,253,201,255,225,253,110,1,9,4,158,4,110,253,65,252,179,250,201,255,72,255,93,0,163,253,226,254,106,253,148,1,193,1,59,3,226,254,162,254,17,251,116,2,50,1,227,0,240,255,147,0,145,253,186,0,155,3,98,8,94,253,134,252,186,249,69,254,28,255,83,1,143,254,234,252,103,254,231,0,86,0,189,5,64,254,187,253,219,251,82,2,194,1,79,255,132,255,86,255,65,254,159,2,135,4,124,5,36,254,101,253,25,250,179,255,118,255,204,2,79,255,140,254,131,254,195,1,166,3,147,3,6,255,80,254,202,252,16,1,60,3,190,1,26,0,19,0,225,255,186,2,156,6,120,8,122,253,47,252,124,248,77,255,39,254,12,1,133,254,23,253,77,253,11,0,127,0,9,4,24,254,107,252,199,252,61,0,67,1,135,0,147,0,111,255,82,253,173,2,18,3,146,6,6,254,176,252,239,250,35,0,90,0,222,0,233,255,166,254,98,253,199,1,79,2,7,5,53,255,175,253,194,251,140,2,96,1,181,1,39,0,63,0,55,254,73,3,241,4,57,8,248,253,142,252,208,249,184,254,57,253,141,5,172,253,170,254,186,255,209,0,173,0,136,7,89,254,170,253,103,252,165,1,93,2,218,255,254,255,11,255,129,255,128,3,177,7,111,4,133,254,250,253,213,249,173,0,118,0,241,2,201,255,131,254,204,255,217,3,253,3,241,2,254,255,221,254,133,252,241,2,224,3,167,1,8,1,131,0,60,255,127,3,226,8,239,9,133,253,192,251,61,246,239,253,42,252,14,2,4,253,194,252,220,253,76,254,60,1,87,2,93,253,84,252,22,253,199,255,236,0,245,255,55,255,175,255,226,252,16,0,77,3,22,6,31,253,39,252,68,251,44,254,17,0,34,1,233,254,184,253,68,253,183,0,54,3,193,2,247,254,20,254,93,251,165,1,152,0,212,1,122,254,166,0,244,254,39,0,14,6,76,7,133,253,58,252,221,249,59,254,20,254,142,3,228,254,253,251,181,255,75,255,123,255,60,7,67,254,144,253,106,251,164,1,111,1,207,255,123,254,44,255,87,255,195,2,49,4,184,4,229,253,58,253,87,250,83,0,93,255,228,1,20,255,225,253,157,254,82,1,151,4,46,3,10,255,203,254,66,252,94,2,248,2,60,0,166,0,248,255,93,255,206,254,57,7,3,10,21,253,255,251,9,249,93,254,66,254,209,0,50,253,202,253,234,253,6,254,181,2,89,3,49,254,71,253,198,251,69,1,175,1,50,255,241,255,248,255,5,253,33,2,151,3,238,5,157,253,241,252,223,250,0,1,201,255,208,0,91,255,164,254,106,253,65,1,168,2,162,3,186,254,83,254,73,252,228,1,190,1,58,2,59,255,72,0,183,255,141,3,175,5,205,6,205,253,31,253,74,248,132,255,96,254,206,2,34,254,108,254,198,254,240,255,190,1,100,6,217,253,231,253,18,253,198,255,126,2,214,0,55,0,71,255,241,254,124,4,21,5,188,4,29,254,97,253,16,251,117,0,29,1,31,2,52,255,121,254,145,255,1,2,2,6,86,3,142,255,66,255,46,252,109,3,83,2,208,1,4,1,4,1,201,254,236,2,235,8,168,8,251,253,79,252,133,247,186,254,60,253,122,1,212,252,77,253,24,255,208,253,175,2,129,5,36,253,78,253,188,252,153,254,133,2,130,1,247,254,62,0,90,253,145,0,108,6,184,4,213,253,36,252,47,251,178,255,14,0,114,0,185,254,154,254,23,254,136,1,165,2,185,2,55,255,20,255,140,251,181,2,193,1,178,0,13,255,0,1,79,254,99,2,105,5,152,9,156,253,123,252,72,250,205,254,239,255,243,1,197,254,101,253,2,255,0,1,172,1,183,5,26,254,90,254,224,251,143,2,114,1,18,0,154,255,71,255,236,254,243,2,42,6,55,5,24,254,165,253,118,250,182,0,163,255,102,3,183,255,54,254,164,254,67,3,94,3,189,3,230,254,179,254,22,253,35,2,71,3,172,1,17,1,167,255,13,0,172,3,172,6,16,10,94,254,196,251,34,249,212,255,154,254,3,1,15,254,125,253,208,253,99,0,45,2,193,3,91,254,2,253,107,252,39,1,70,1,184,0,175,0,15,0,142,253,20,2,110,3,189,7,69,254,0,253,5,251,221,0,156,0,12,1,39,0,149,254,7,254,183,2,4,3,116,4,94,255,53,254,112,252,197,2,188,1,146,2,25,0,47,1,200,254,244,4,130,5,179,6,215,254,2,253,212,248,249,254,148,255,46,4,106,254,243,255,127,255,57,0,182,1,174,10,138,254,25,254,189,252,48,1,184,2,164,0,104,0,21,255,5,0,75,6,108,7,119,5,27,255,186,253,211,250,149,1,192,0,49,3,169,255,74,254,111,0,4,4,175,4,225,3,68,0,81,255,90,252,9,4,93,4,195,1,222,1,200,0,8,255,79,8,136,10,250,7,189,252,213,250,173,247,225,252,76,253,210,1,212,252,248,251,43,254,146,253,32,1,152,3,67,253,183,252,210,251,101,254,0,2,8,0,122,254,165,255,24,253,226,255,19,4,137,4,202,252,132,251,124,251,218,254,210,255,110,0,101,254,138,254,90,253,214,0,19,2,156,2,106,254,92,254,86,251,231,1,232,0,47,1,194,254,91,0,40,254,123,0,208,4,141,9,46,253,72,252,41,250,30,253,93,253,52,5,225,253,162,253,45,255,161,255,158,255,228,5,219,253,254,253,87,251,217,1,211,0,73,0,224,254,144,255,123,254,25,2,52,5,234,4,201,253,13,253,247,249,71,0,229,254,120,2,86,255,31,254,19,254,169,2,234,3,49,3,156,254,181,254,147,252,163,1,194,2,90,1,241,0,222,255,186,254,121,1,158,7,91,7,41,253,205,251,167,249,23,255,225,253,116,0,244,253,218,252,183,253,183,255,222,1,217,2,224,254,99,252,137,251,173,0,191,1,204,255,68,0,27,255,162,253,193,1,17,2,5,7,177,253,149,252,173,250,183,0,112,255,68,1,153,255,60,254,102,253,111,2,232,1,152,4,18,255,1,254,20,252,70,1,40,2,202,1,136,0,108,0,193,254,114,2,63,5,91,7,22,254,122,253,62,249,70,255,63,254,216,3,30,253,180,255,86,255,218,253,243,2,0,10,16,254,2,254,77,252,210,0,182,2,204,255,84,0,190,254,57,255,66,4,89,6,200,4,136,254,165,253,140,250,87,1,74,0,120,2,81,255,10,254,224,255,204,3,52,5,222,2,52,0,217,254,167,251,41,4,150,3,160,0,137,1,107,0,115,254,190,4,89,10,205,6,136,253,79,251,157,248,49,253,235,254,97,1,117,253,144,252,134,255,45,255,209,0,58,5,206,253,54,253,221,251,48,255,132,1,159,0,192,254,195,255,217,253,37,1,68,4,163,5,120,253,159,252,27,251,207,255,113,255,49,1,111,254,29,255,183,253,49,2,20,2,159,3,139,255,69,254,92,251,251,1,180,1,36,1,177,255,233,0,54,254,159,2,1,4,92,9,135,253,182,252,11,250,204,254,226,254,128,2,139,254,147,253,105,254,162,1,253,0,25,5,197,254,187,253,143,251,60,2,173,2,231,254,61,0,188,255,141,254,223,3,77,4,218,5,19,254,85,253,174,250,209,255,164,0,192,2,0,255,198,254,244,254,119,2,181,3,28,4,138,255,164,254,191,252,68,0,156,4,56,2,152,0,117,0,34,0,89,4,110,7,191,8,167,253,65,252,86,249,113,255,23,254,224,1,180,254,113,253,194,253,54,0,97,1,168,4,50,254,116,253,228,252,150,0,37,2,112,0,195,0,145,255,253,253,167,2,84,4,111,6,210,253,19,253,63,251,247,255,16,1,85,1,203,255,247,254,233,253,233,1,75,3,18,5,136,255,30,254,248,251,120,2,31,2,152,1,179,0,50,1,242,253,100,4,184,5,196,8,95,254,238,252,230,249,32,255,128,254,84,5,135,254,53,254,231,255,129,1,233,1,126,8,180,254,117,253,195,252,32,2,41,2,61,0,22,0,143,255,167,255,104,4,189,6,244,5,40,255,139,254,139,249,161,0,60,1,140,3,91,255,34,255,189,255,82,5,151,4,21,3,73,0,4,255,1,253,226,2,164,3,104,2,106,1,246,0,130,255,19,3,94,10,211,11,77,253,174,251,114,247,203,253,180,253,12,2,178,253,45,252,22,254,249,254,141,1,214,3,191,253,187,252,79,252,234,255,179,1,207,255,66,255,138,255,139,253,168,255,216,4,233,5,132,253,229,251,5,252,221,254,189,0,3,1,255,254,42,254,139,253,145,0,177,3,126,3,186,254,148,254,186,251,31,2,4,1,118,2,54,255,189,0,47,255,101,1,99,5,43,8,199,253,205,251,87,250,54,253,17,255,151,3,92,254,63,253,172,255,147,255,142,255,103,9,99,254,239,253,103,251,226,1,112,1,131,0,70,255,184,255,125,255,93,3,231,4,196,4,157,253,110,253,195,250,227,0,135,255,119,2,80,255,23,254,38,255,233,2,151,4,189,3,191,254,108,255,88,252,159,2,198,3,216,0,84,1,253,255,113,255,213,1,56,7,133,9,39,253,63,252,109,249,43,255,2,255,65,1,1,254,74,254,247,253,130,255,213,2,135,3,172,254,83,253,248,251,60,1,224,1,20,0,23,0,167,255,217,253,97,1,27,4,253,6,224,253,11,253,172,250,42,1,231,255,180,1,156,255,120,254,249,253,211,1,242,2,54,4,46,255,114,254,202,251,108,2,146,2,118,2],"i8",y,a.GLOBAL_BASE+10240),p([33,0,147,0,78,255,153,3,151,6,129,7,187,254,240,253,70,248,2,0,227,254,142,3,141,254,22,254,26,255,0,0,85,2,218,7,16,254,117,254,190,252,37,0,177,3,245,0,181,0,96,255,112,255,201,5,93,5,77,5,157,254,167,253,10,251,42,1,66,1,160,2,63,255,176,254,77,0,65,4,253,5,154,3,177,0,217,255,155,251,228,3,13,3,24,2,200,1,110,1,80,254,135,5,136,9,231,8,46,254,10,253,235,246,209,254,3,254,131,1,41,253,211,253,66,0,111,255,131,2,224,4,224,253,92,253,108,252,31,255,94,3,76,2,104,255,40,0,235,253,167,1,143,5,22,6,196,253,181,252,135,251,128,255,85,0,205,1,18,255,255,254,184,253,93,2,236,2,93,3,24,0,54,255,127,250,29,3,231,1,47,1,75,255,108,1,74,255,104,2,98,5,126,11,18,254,172,252,95,250,220,254,61,0,44,3,172,255,45,253,74,255,43,2,20,2,226,5,147,254,19,254,223,251,54,3,76,2,11,0,242,255,238,255,26,255,233,3,121,5,171,5,38,254,199,253,244,250,46,1,62,0,38,4,186,255,136,254,34,255,214,3,206,3,125,4,60,255,22,255,229,252,223,1,74,4,243,1,106,1,58,0,70,0,123,4,21,8,41,11,25,254,146,252,224,248,73,0,224,254,92,1,154,254,12,254,4,254,199,0,209,2,218,4,178,255,71,253,229,252,105,1,24,2,196,0,118,1,110,0,33,253,79,3,27,4,104,7,146,254,55,253,98,251,59,1,64,1,173,1,72,0,41,255,62,254,247,2,118,3,83,5,226,255,84,254,190,252,93,3,115,2,28,3,118,0,212,1,233,254,75,5,91,7,101,7,68,255,126,253,180,249,63,0,81,255,174,4,94,254,45,255,51,0,158,1,75,2,41,10,22,255,211,253,166,252,168,1,121,3,222,0,136,0,155,255,83,0,133,5,230,8,103,5,172,255,67,254,147,250,158,1,57,1,21,4,29,0,169,254,65,0,16,6,111,6,212,3,183,0,165,255,195,252,249,4,133,5,104,1,41,2,16,1,149,255,51,6,77,12,43,10,104,5,29,8,92,13,244,19,86,26,186,31,135,38,84,43,170,49,133,53,61,254,215,251,239,253,231,250,62,254,12,253,15,254,161,252,128,254,149,253,99,254,99,253,195,254,230,253,181,254,212,253,98,254,4,254,88,254,134,254,238,254,188,254,78,254,154,253,30,255,12,254,24,255,254,253,249,254,135,254,214,254,102,254,105,255,58,253,82,255,206,252,107,255,100,254,100,255,83,254,224,254,50,254,70,255,53,255,86,255,210,254,65,255,191,254,125,255,109,255,215,254,117,254,28,255,42,255,11,255,64,255,189,255,196,254,185,255,185,254,152,255,51,255,162,255,73,255,113,255,218,255,63,255,161,255,16,0,180,255,132,255,8,255,23,0,19,255,24,0,12,255,18,0,120,255,44,0,145,255,223,255,232,255,231,255,0,0,149,0,19,0,23,0,113,255,158,0,87,255,174,0,75,255,133,0,201,255,165,0,230,255,111,0,84,0,98,0,75,0,87,0,183,0,141,255,245,255,248,255,130,0,11,0,170,0,254,0,77,0,205,0,17,0,183,0,112,0,6,1,194,0,202,0,31,1,95,0,189,0,214,255,151,255,234,0,179,0,39,0,186,0,163,0,89,1,76,1,199,0,43,1,161,0,202,255,29,1,178,255,25,1,123,255,141,0,74,255,111,0,249,0,85,1,15,1,108,1,93,0,147,1,75,0,135,1,92,0,254,1,118,255,220,0,71,255,227,255,222,255,105,1,141,255,64,1,3,0,42,2,99,0,30,1,218,0,79,2,11,255,150,1,244,254,197,1,0,0,68,2,25,0,94,2,19,1,20,2,148,0,194,1,183,255,227,2,227,254,6,2,224,254,94,0,53,255,162,2,116,255,182,255,205,0,202,2,142,255,43,1,176,0,155,3,182,0,45,2,240,0,193,2,240,255,1,2,229,1,81,2,37,1,128,1,195,1,105,2,218,255,50,0,51,2,17,2,47,1,209,0,203,1,107,1,177,1,196,1,194,1,198,1,111,1,94,2,221,1,229,2,176,1,97,1,112,1,11,1,105,1,204,2,17,1,71,2,197,1,166,0,254,1,172,0,201,0,117,2,18,1,191,0,56,2,127,2,46,1,42,1,122,2,131,1,131,2,94,1,75,2,48,2,100,2,53,2,88,2,20,3,231,1,160,2,0,2,247,3,65,1,77,1,101,1,86,3,131,255,157,1,218,1,200,2,17,0,105,255,52,2,29,1,14,1,15,255,203,3,121,3,233,1,220,0,254,1,128,3,37,2,156,3,71,1,57,3,34,1,143,3,28,2,84,4,158,0,37,3,199,0,189,3,255,1,218,2,100,0,106,3,13,0,23,3,179,1,120,2,164,2,204,3,249,0,132,3,211,1,194,4,13,3,50,4,73,2,17,3,233,255,157,2,11,1,19,4,107,2,60,4,103,2,121,4,110,2,137,3,148,3,25,4,80,0,75,1,72,2,51,4,89,0,127,2,220,3,193,3,2,3,208,2,30,3,187,2,236,1,191,1,131,3,115,2,15,1,164,4,213,2,53,5,87,0,91,2,64,3,67,6,104,2,103,4,122,3,225,5,232,3,132,4,98,3,241,3,227,3,59,3,125,4,90,3,49,3,170,5,5,3,40,5,244,1,109,5,56,1,129,4,236,255,60,4,64,0,3,5,2,0,148,4,143,1,77,7,2,2,170,6,246,1,100,6,118,3,242,5,160,1,88,2,107,4,70,5,251,4,110,5,121,3,3,7,146,3,230,6,227,0,159,4,226,4,34,7,249,1,62,7,151,3,49,9,57,255,175,1,152,0,199,6,43,255,228,255,136,1,54,5,103,255,204,255,210,3,127,4,189,254,112,254,45,3,167,6,120,255,84,0,169,5,223,7,181,254,113,255,119,255,168,4,0,255,22,2,99,255,7,4,205,254,73,254,30,2,219,2,183,254,92,254,159,255,104,2,150,254,88,255,190,254,110,1,9,255,146,255,45,255,89,0,60,255,203,254,20,0,59,0,148,254,49,254,226,254,89,0,176,254,175,0,80,254,141,0,133,254,66,255,78,254,60,255,177,255,150,0,234,254,29,255,232,254,166,0,213,253,90,254,101,255,29,2,146,254,54,0,227,255,173,255,211,254,250,252,186,0,116,2,115,254,248,254,242,0,37,1,59,255,183,253,124,0,154,1,53,0,123,255,10,0,84,1,198,253,215,251,65,0,66,254,68,0,19,254,127,1,169,3,155,254,57,253,153,254,6,255,91,253,212,251,36,1,230,255,107,1,6,0,95,2,33,5,129,255,246,255,233,5,94,7,201,2,204,3,189,5,133,8,163,5,224,7,161,249,192,249,252,248,14,247,253,251,22,249,180,251,23,248,3,251,148,250,169,250,2,250,77,252,75,250,52,252,12,250,25,252,58,251,4,252,108,251,209,252,37,252,32,252,165,250,64,251,18,252,247,250,186,251,24,253,12,251,13,253,243,250,162,252,101,252,119,252,40,252,90,253,229,251,83,253,230,251,193,251,39,252,218,251,89,253,35,252,127,253,153,251,48,252,6,253,114,253,134,252,218,252,191,252,189,251,62,253,139,253,147,253,218,252,128,253,212,252,249,252,134,253,245,252,225,253,28,252,203,253,205,251,188,253,222,253,157,253,196,253,149,253,8,253,222,254,145,252,242,253,201,252,50,254,229,252,3,255,215,253,97,254,179,253,73,254,235,253,172,254,76,253,89,252,7,254,252,252,66,253,149,251,249,254,206,254,53,252,29,254,67,254,182,255,213,253,220,253,154,253,127,255,75,253,22,255,116,254,10,255,37,254,6,255,247,254,108,254,136,254,254,253,95,254,2,254,212,254,199,254,178,254,104,253,49,254,210,252,126,254,64,253,175,254,153,253,22,255,55,255,23,255,17,255,89,255,201,253,53,255,149,253,109,255,97,254,141,255,160,254,90,255,18,253,85,255,7,253,242,254,145,252,248,254,121,252,145,254,24,253,43,0,37,254,14,0,115,253,43,0,98,253,11,0,64,254,197,255,247,253,130,255,137,255,101,255,155,253,214,255,161,252,229,255,93,252,136,0,29,254,183,0,44,254,55,0,214,254,55,0,208,254,57,1,159,253,57,1,48,253,66,1,89,255,100,0,227,253,253,255,137,255,145,255,69,255,233,0,20,255,4,1,22,255,26,0,91,255,134,0,211,255,216,255,219,253,104,1,53,255,122,1,124,254,194,1,129,254,19,1,20,0,182,0,153,255,246,0,145,255,175,1,37,0,206,1,110,255,231,1,99,255,228,254,197,255,247,1,72,255,24,0,53,0,253,255,54,0,122,0,3,1,77,1,66,0,228,1,104,0,180,1,68,0,195,0,116,0,190,0,206,0,13,1,247,255,226,1,96,1,126,1,29,1,143,1,21,1,196,1,0,1,69,0,186,0,13,0,41,1,243,255,3,1,161,255,30,0,56,0,138,1,196,0,169,1,205,0,200,1,25,1,65,2,15,0,191,0,119,1,34,1,151,1,64,2,200,255,227,0,32,2,149,1,0,0,37,2,164,255,16,2,27,255,95,1,11,255,82,1,150,254,179,1,167,0,15,2,181,255,46,1,91,0,56,3,129,0,87,2,240,1,167,2,186,0,237,2,153,0,225,2,231,254,88,2,164,254,103,2,20,255,1,3,41,0,113,3,38,0,122,3,36,255,73,3,155,254,115,3,119,254,135,3,134,253,218,1,68,254,82,3,81,255,166,2,19,254,242,0,249,253,17,3,54,253,70,2,227,253,110,1,225,253,178,1,171,253,244,1,3,253,222,0,66,253,149,3,25,253,194,3,155,252,245,1,125,252,36,2,133,254,200,0,77,254,157,0,205,252,214,0,163,252,157,0,154,253,40,0,136,253,94,0,141,252,202,255,27,253,4,2,11,254,42,1,154,253,85,255,154,252,95,255,159,252,233,255,206,252,93,0,9,252,245,254,106,253,153,254,219,253,2,0,70,254,135,255,135,254,0,0,29,255,33,0,98,254,130,255,127,255,212,0,90,252,34,0,198,251,230,254,161,251,244,254,58,253,199,252,92,254,65,255,204,251,96,252,107,252,163,255,140,253,154,254,97,0,7,0,50,255,119,254,155,255,24,0,53,255,38,0,88,255,83,0,169,253,89,254,233,254,170,1,68,253,118,0,181,255,206,0,43,252,95,253,88,253,161,1,145,254,37,0,233,254,218,1,127,255,194,254,63,1,40,1,142,253,217,255,87,1,90,2,72,253,217,255,209,254,172,3,104,0,233,0,132,254,137,0,220,255,13,1,181,255,42,255,120,0,43,0,239,253,35,254,203,1,164,0,54,255,27,255,207,255,89,255,97,2,24,3,98,0,36,255,147,3,148,0,37,1,27,1,101,3,91,0,63,2,138,1,70,1,178,255,205,2,67,0,109,1,189,254,104,2,220,255,219,2,27,0,107,2,238,0,120,2,17,1,192,1,99,0,33,3,220,1,101,3,17,1,173,2,64,0,21,3,72,0,253,3,217,0,25,3,203,1,222,2,104,1,134,2,224,1,104,1,66,1,173,1,208,1,126,2,174,1,244,2,107,1,232,3,148,1,171,2,16,2,90,2,103,2,143,2,157,1,178,3,175,2,169,3,90,2,136,3,92,2,43,2,225,2,18,3,150,2,211,1,142,2,106,1,77,2,161,3,198,2,242,1,222,1,159,1,164,1,181,2,115,3,45,3,171,2,13,3,157,3,145,3,171,3,214,2,220,2,235,1,85,3,19,2,180,3,222,2,195,3,59,1,40,3,249,2,243,2,120,4,248,2,143,2,52,4,58,3,33,4,67,4,70,3,235,3,40,3,23,4,109,4,147,2,77,4,224,3,26,4,50,4,51,4,203,3,182,2,202,4,30,4,59,2,73,3,116,3,124,5,99,5,72,4,56,4,93,3,207,4,223,2,4,5,248,2,248,4,223,3,87,5,29,4,233,4,188,2,26,4,22,2,220,3,197,1,240,4,87,2,116,4,167,2,85,6,47,3,104,5,9,2,37,5,137,1,28,6,37,3,168,5,174,2,44,4,136,2,107,3,51,1,59,4,105,1,23,4,61,1,137,5,196,3,163,2,59,2,128,4,79,0,90,4,209,255,250,5,55,1,185,6,58,1,142,4,177,2,2,2,162,255,93,1,26,1,132,5,72,1,1,4,231,1,191,255,57,0,37,3,202,3,36,0,62,0,1,3,249,254,23,3,166,254,125,2,187,2,119,255,108,2,22,2,29,2,33,253,194,0,199,2,44,1,244,254,161,252,158,3,1,3,60,253,84,254,250,1,174,0,132,252,138,253,179,1,35,2,101,250,254,254,109,2,215,1,6,252,168,250,119,254,9,2,104,252,82,253,231,255,20,0,42,252,124,251,84,1,9,0,234,249,145,251,160,254,48,0,213,249,110,254,137,252,6,0,124,251,136,252,220,253,160,254,149,249,112,251,97,255,98,2,24,248,61,252,31,255,193,0,136,249,88,248,11,255,19,254,60,252,112,249,88,252,133,253,237,250,48,249,148,250,164,253,252,249,189,252,139,250,121,255,204,249,222,254,122,249,56,253,37,248,160,249,129,249,229,255,46,247,213,252,123,251,184,0,15,251,189,0,169,250,74,2,37,248,201,0,234,252,200,2,70,251,3,0,247,251,40,3,29,251,62,3,145,255,123,2,156,249,191,1,49,254,75,252,67,254,96,252,8,254,118,251,11,254,69,251,144,0,161,254,140,254,228,251,229,254,221,251,233,254,157,251,193,253,98,250,181,253,178,249,89,252,40,252,229,0,178,2,103,252,49,253,109,254,82,5,83,253,47,254,106,3,141,1,3,254,210,255,61,1,54,5,27,254,200,1,45,3,183,1,101,254,83,1,130,3,43,4,87,254,46,0,161,5,241,1,115,252,224,252,185,5,22,4,2,255,191,254,150,5,141,4,68,0,94,1,10,4,154,2,114,1,11,0,31,5,22,3,143,0,232,0,17,4,26,6,142,255,151,2,80,6,54,4,198,1,67,2,251,4,16,4,180,255,141,3,240,2,43,4,153,0,0,2,92,1,190,4,102,2,129,1,51,7,40,3,13,1,10,4,203,0,62,4,140,2,249,3,247,6,106,4,173,1,47,5,131,1,104,5,207,255,159,4,184,255,191,4,96,254,233,3,32,2,213,6,160,254,199,4,10,254,175,4,179,253,57,2,29,255,94,6,114,255,42,6,26,255,179,6,54,253,8,5,186,252,118,5,107,4,77,5,48,255,208,4,181,1,197,3,95,252,50,3,43,3,130,5,91,3,227,5,164,0,188,4,107,5,1,7,228,1,82,7,200,1,15,8,228,3,146,4,46,5,122,5,36,5,80,5,111,4,238,4,210,4,82,6,81,5,232,6,141,5,203,4,48,6,67,5,86,3,160,2,149,6,30,6,115,4,246,4,224,7,33,7,237,6,45,6,252,5,180,5,207,5,178,3,123,6,253,3,208,6,188,4,112,5,209,3,236,6,137,4,34,7,140,4,182,6,149,5,181,7,55,6,161,4,96,3,84,8,37,4,7,7,46,3,46,7,245,2,56,8,35,5,6,8,234,4,65,8,147,3,27,9,162,3,187,5,123,4,30,10,159,5,197,8,208,6,42,8,84,6,54,9,174,5,106,10,226,5,84,7,45,7,22,8,183,7,203,6,41,6,170,2,9,5,48,6,253,7,174,5,50,8,194,9,212,7,151,10,18,8,214,2,52,6,196,10,32,9,228,0,79,3,152,9,123,6,36,0,45,1,150,7,165,7,66,254,160,255,106,8,116,5,253,5,77,4,14,0,96,2,101,252,36,253,103,5,190,7,65,5,184,3,88,253,65,1,1,5,244,4,198,249,109,1,173,3,178,3,55,249,202,252,70,9,227,10,29,7,228,10,236,248,29,247,169,248,23,246,152,249,200,248,97,249,44,248,60,251,136,248,59,251,198,247,233,249,204,249,219,249,236,249,85,251,177,249,56,251,65,249,177,250,129,251,176,249,100,248,6,251,145,250,231,250,133,250,185,249,101,251,116,249,225,250,93,250,58,250,169,250,126,252,24,251,221,251,205,250,146,251,42,252,147,251,131,251,32,250,200,251,228,250,4,252,97,251,44,252,50,250,57,252,41,250,36,252,102,252,233,251,203,251,186,252,101,251,166,252,58,251,149,251,239,251,216,251,1,253,152,252,123,251,67,253,144,252,62,253,118,252,250,252,8,252,190,253,200,251,223,252,58,250,177,253,169,251,176,253,134,251,55,253,148,250,128,253,160,250,171,253,221,251,96,254,121,252,82,253,192,252,107,253,60,253,68,254,156,252,22,254,103,252,138,254,248,252,149,253,110,251,183,253,219,253,255,252,229,252,77,254,109,253,238,253,27,253,14,254,187,252,155,254,171,253,233,254,153,252,13,255,137,252,230,254,103,253,232,254,101,253,91,255,208,253,118,254,121,252,150,254,102,254,64,254,185,253,103,254,194,253,199,254,155,254,131,253,220,253,198,253,76,254,128,252,8,254,130,254,11,253,198,255,31,254,91,255,150,253,65,255,138,254,22,255,130,254,34,255,85,253,231,255,32,254,94,254,153,254,38,253,159,254,188,254,99,255,80,254,190,254,118,254,209,254,228,254,152,255,167,253,223,254,212,253,60,255,180,253,106,255,109,253,160,253,39,254,232,255,188,255,64,254,38,254,248,255,6,254,211,255,20,253,72,255,180,252,4,255,123,252,165,255,184,253,159,255,116,253,138,0,4,253,125,255,90,253,244,255,98,253,165,0,253,254,253,255,184,252,149,255,115,252,37,0,32,252,44,0,170,252,97,254,185,252,13,0,23,252,241,254,254,251,203,254,226,252,34,254,192,252,24,254,81,252,168,0,168,251,125,254,95,251,155,255,97,251,216,255,83,252,196,254,250,251,254,252,236,251,143,253,199,251,230,253,56,251,213,254,224,250,76,254,83,251,105,253,113,251,95,255,64,251,78,253,43,251,193,252,104,250,48,253,133,250,19,254,126,252,28,253,102,252,223,252,178,251,110,254,213,249,60,252,219,251,130,253,11,251,98,250,37,250,90,252,34,250,129,252,194,249,204,253,69,249,51,253,162,253,171,253,114,251,195,251,167,250,44,254,102,248,43,250,210,248,71,252,116,248,93,252,37,250,68,255,157,249,91,254,79,250,174,254,88,250,234,255,106,248,90,254,42,248,7,255,16,254,142,255,138,248,13,253,247,250,174,0,85,250,147,255,30,254,255,254,59,251,4,254,175,249,151,0,98,249,208,0,114,253,107,0,141,249,29,0,139,251,23,1,65,251,50,1,52,251,6,254,38,253,81,255,44,251,155,255,55,252,39,2,154,252,22,1,201,252,59,1,205,253,120,1,229,251,228,0,5,254,24,1,169,253,25,1,10,253,253,0,207,254,123,1,13,253,122,255,157,253,148,2,200,252,24,2,207,252,134,2,99,254,49,0,171,254,177,0,59,254,14,2,30,254,77,2,185,255,83,1,111,253,8,1,12,255,39,1,19,255,59,1,125,254,57,2,6,254,247,255,135,254,14,0,96,255,149,2,40,255,40,0,204,254,210,255,95,0,214,0,14,255,167,0,170,255,192,0,200,255,27,0,180,255,31,0,36,0,53,1,150,255,74,255,143,255,74,0,71,254,234,255,23,0,139,0,81,0,245,255,44,0,15,0,169,255,119,255,138,255,49,255,98,255,198,255,16,1,164,255,100,255,71,254,8,0,120,255,128,0,35,255,101,0,38,255,40,0,59,255,180,255,56,254,9,0,67,254,33,0,89,254,226,0,60,0,73,0,34,255,156,0,113,254,24,1,194,254,245,0,171,254,166,0,13,254,83,1,66,255,71,1,37,255,69,1,119,255,167,255,172,253,100,0,141,253,144,0,91,253,231,1,28,0,252,0,121,254,214,0,215,255,26,1,228,255,99,0,226,254,75,1,49,0,203,1,124,254,53,2,143,254,180,1,28,0,80,1,247,255,141,1,89,255,106,2,34,0,84,2,239,255,49,2,116,255,43,1,79,0,10,2,125,0,203,0,2,0,244,0,32,1,255,0,211,0,175,0,82,0,84,2,187,0,5,2,108,0,125,1,255,0,109,1,41,1,241,1,96,1,71,1,174,255,25,0,210,0,115,1,245,0,5,1,3,0,33,2,193,1,140,0,38,1,44,0,39,1,212,0,91,1,244,0,238,1,75,1,16,2,201,0,51,1,93,1,155,1,101,2,28,1,102,2,157,1,208,1,66,1,112,2,141,1,97,0,200,0,96,255,128,1,149,0,106,1,239,1,13,2,13,1,73,2,33,0,235,1,135,255,177,1,171,1,99,2,242,1,4,2,171,0,187,1,241,1,154,2,184,1,19,1,54,2,63,2,146,0,127,2,155,0,158,2,223,255,173,0,212,0,184,2,90,255,89,2,65,255,183,2,23,254,247,1,175,0,230,2,214,0,220,1,116,1,59,4,66,2,18,2,74,2,9,3,169,1,106,3,59,1,73,3,118,1,80,3,91,255,53,2,35,0,223,3,217,255,38,4,73,1,200,2,18,3,72,3,133,2,27,3,149,2,164,2,59,2,150,3,120,2,55,4,161,2,49,3,62,1,132,1,106,3,244,3,52,2,80,3,112,3,108,2,45,2,223,1,159,2,197,1,180,2,212,1,72,3,130,2,76,3,133,2,250,1,172,1,129,3,55,2,69,3,131,1,194,3,243,1,179,2,49,2,171,3,158,3,15,3,40,1,22,3,12,1,4,4,18,2,106,3,73,1,36,2,143,0,163,2,35,1,247,1,66,0,17,4,103,1,18,3,97,0,37,3,33,0,69,3,214,1,255,1,49,0,68,4,71,1,150,4,67,1,3,0,242,0,104,3,218,1,177,2,173,1,49,5,166,2,18,4,108,2,85,4,152,2,65,1,193,0,121,3,182,3,129,4,106,3,125,3,123,2,109,3,94,3,180,3,145,3,13,5,153,2,40,5,127,2,229,3,25,3,122,5,6,4,152,4,244,3,86,4,191,3,130,5,157,3,123,5,147,3,31,2,94,3,92,4,198,4,67,3,166,4,67,3,166,4,191,3,124,4,123,4,96,5,20,5,169,4,135,5,207,4,55,5,61,5,234,2,68,4,175,6,3,5,109,5,49,4,54,5,30,6,129,4,195,5,109,6,113,4,33,7,196,4,32,4,102,5,241,5,194,6,96,6,9,6,84,6,6,6,87,3,60,6,97,3,131,6,181,2,117,3,180,6,239,5,143,4,16,5,161,8,224,6,160,7,213,5,228,7,202,5,254,5,74,7,158,6,216,7,30,6,236,2,225,6,57,3,38,1,112,5,60,4,10,8,109,2,35,5,109,1,7,5,198,0,4,4,232,1,128,5,249,0,147,1,246,3,25,6,68,1,107,1,109,6,20,4,193,0,111,1,242,7,67,7,5,255,67,2,238,2,226,3,13,255,30,0,45,5,111,3,228,255,87,255,112,2,149,3,59,254,159,0,186,0,90,5,154,253,6,0,25,2,136,1,162,255,221,254,13,3,229,0,128,255,214,254,245,0,235,1,67,253,120,253,204,3,21,3,11,254,128,253,178,0,255,0,147,254,122,254,1,255,61,1,66,252,218,254,65,255,228,0,249,252,65,254,157,0,19,255,111,253,48,253,105,254,92,0,139,255,157,253,78,1,26,255,89,253,196,251,112,255,195,254,123,252,163,252,30,253,152,254,171,255,41,253,166,255,237,252,100,0,234,255,121,254,249,254,200,255,183,255,175,254,14,253,5,0,67,255,62,253,144,253,89,0,168,254,121,255,167,251,159,254,19,255,84,253,145,251,237,254,178,251,243,254,77,251,152,0,145,0,46,253,48,251,49,0,80,0,32,251,248,252,8,255,135,1,36,253,221,253,213,1,218,0,1,255,160,252,69,0,110,1,90,255,27,254,80,253,191,0,68,251,84,251,86,255,87,255,228,250,161,249,65,1,214,1,117,250,37,251,192,255,16,1,175,250,8,255,236,1,53,2,47,253,159,253,195,0,229,1,195,253,123,255,171,1,202,0,85,255,138,255,199,0,63,2,2,0,225,255,182,2,243,2,170,250,217,255,40,2,45,2,23,254,15,1,168,2,25,2,13,0,59,254,87,3,186,3,123,255,204,255,175,255,226,2,111,251,125,2,31,4,35,4,161,255,164,2,235,4,57,4,233,1,49,1,63,254,186,3,234,253,228,3,55,252,98,3,222,251,35,4,242,250,106,2,120,250,105,2,54,254,86,5,97,255,29,7,250,252,240,253,242,255,86,4,78,251,123,252,252,252,177,1,24,251,25,251,13,252,210,254,166,253,183,253,9,253,174,249,8,253,243,249,184,252,127,248,208,252,229,253,23,249,69,247,29,255,220,255,14,248,217,248,197,247,154,251,89,246,232,248,66,250,252,0,115,245,97,254,197,253,45,254,229,5,18,6,132,8,183,7,22,9,228,7,191,248,111,249,191,248,37,249,248,247,130,251,170,247,138,249,173,249,181,251,88,249,149,251,191,250,184,249,177,250,154,249,198,250,243,250,211,250,15,251,128,249,143,249,49,250,173,252,190,250,216,248,123,250,116,247,254,250,87,253,7,249,143,249,58,252,198,251,97,251,116,249,226,251,207,251,138,251,122,251,73,251,24,253,6,251,27,252,90,252,153,250,97,252,120,250,14,252,231,250,241,252,69,252,231,251,124,252,31,252,207,252,31,253,201,252,52,252,91,251,30,253,186,251,30,253,126,251,240,252,223,252,214,252,238,252,132,252,248,253,24,252,206,252,124,253,59,252,191,253,142,252,227,253,74,253,97,253,107,252,173,253,126,253,122,253,153,253,68,252,147,253,99,252,253,253,41,253,29,254,209,252,27,254,184,252,190,253,72,254,55,253,190,253,187,254,111,253,98,253,126,254,198,253,71,254,102,253,254,253,237,252,120,254,239,253,246,253,59,254,25,254,89,254,152,253,183,253,151,253,99,255,106,253,244,254,88,253,164,254,190,254,189,254,136,253,68,254,208,254,82,254,180,254,54,254,235,254,44,254,109,253,231,252,193,254,132,253,29,255,214,253,139,254,165,254,178,254,46,255,56,254,64,255,238,253,14,255,40,255,58,255,146,254,142,254,174,254,95,255,103,254,20,253,149,255,132,254,218,254,125,253,33,255,103,253,22,255,27,253,115,255,16,254,126,255,2,254,117,255,185,254,84,255,207,254,206,254,188,253,92,255,249,254,250,254,84,255,189,255,110,254,31,0,146,254,246,255,76,254,170,255,241,253,71,0,135,254,234,255,159,253,244,255,90,253,189,255,193,254,63,0,65,255,35,0,75,255,217,255,14,255,126,0,89,255,116,255,224,253,155,0,215,254,174,0,215,254,38,0,248,255,117,0,132,254,197,0,60,254,240,0,246,253,223,0,153,255,110,0,69,255,87,0,101,255,169,0,209,255,157,0,26,0,173,255,156,255,128,0,80,0,209,0,194,255,6,0,7,0,22,0,5,0,62,1,236,255,248,0,211,255,56,255,193,255,156,0,187,255,250,0,73,255,113,1,130,255,143,255,180,255,114,255,134,255,192,255,2,255,225,255,35,0,79,255,185,255,249,255,171,0,93,0,27,0,108,0,212,0,182,254,47,255,133,255,186,255,233,254,95,0,160,255,20,0,68,255,195,255,198,254,87,0,212,254,178,255,158,254,122,255,11,0,122,0,116,255,122,0,237,254,152,0,219,254,140,0,174,255,138,0,191,254,145,255,32,254,100,255,153,254,76,0,2,255,216,255,133,253,160,255,246,253,79,0,5,254,8,0,244,254,47,1,229,253,68,0,66,254,61,0,246,253,50,1,111,0,189,0,77,254,122,0,133,254,166,0,197,253,114,254,136,253,182,255,21,253,161,255,57,254,194,0,72,252,83,0,226,252,192,0,13,253,192,0,243,252,94,255,149,253,234,0,105,253,215,254,24,254,147,255,60,252,124,255,186,252,188,255,181,252,58,0,168,251,170,255,219,252,213,254,80,252,3,255,246,252,206,255,59,252,219,253,160,254,158,255,32,252,169,254,163,251,197,254,163,251,205,254,125,251,138,254,131,253,26,255,114,251,213,255,237,250,156,255,99,252,119,254,6,251,168,253,79,253,126,255,57,250,200,254,215,250,2,255,72,250,70,254,244,250,155,253,19,251,9,254,35,250,144,254,214,250,26,0,104,250,190,255,49,249,95,255,148,249,45,254,32,249,220,253,143,250,200,253,236,249,153,252,41,250,246,251,149,250,197,253,131,248,240,253,9,249,133,255,151,248,25,255,250,247,189,254,252,247,118,252,72,248,201,253,131,248,148,253,1,248,35,252,203,251,142,254,17,248,64,253,205,246,19,253,76,245,191,251,139,248,159,0,36,248,248,0,142,253,133,255,221,246,62,252,99,253,104,254,157,250,106,251,60,254,148,254,236,251,33,253,124,255,183,0,172,249,16,253,221,253,205,254,247,252,19,251,158,255,41,0,144,252,189,251,255,254,97,0,190,249,215,248,31,0,230,255,124,253,207,253,76,255,222,253,127,254,185,251,102,254,222,252,98,254,197,252,55,254,54,252,22,254,171,251,41,255,108,252,112,255,87,252,19,254,11,251,251,253,29,250,181,0,101,0,180,254,135,252,188,252,87,252,209,253,83,254,139,253,221,253,73,255,175,254,223,253,174,255,6,255,226,254,5,0,124,255,164,254,4,255,219,254,40,254,98,255,100,0,227,255,197,0,20,255,88,254,163,252,43,255,116,255,249,255,85,254,69,254,187,0,159,255,84,253,32,253,219,254,2,1,144,254,104,255,106,255,136,1,159,253,175,0,114,255,43,1,118,255,152,0,137,255,73,1,26,254,204,255,37,1,198,0,73,255,117,0,175,0,75,1,198,255,238,254,231,0,44,1,224,254,74,1,207,254,116,1,145,255,153,1,247,255,167,1,83,0,0,1,67,0,111,1,237,255,248,0,91,0,113,0,221,255,150,1,65,255,154,0,238,0,40,1,5,0,197,0,141,0,221,0,57,1,198,0,211,0,165,1,244,0,78,1,88,0,170,1,13,255,198,1,202,0,40,2,251,255,147,1,35,1,185,0,219,0,45,1,251,0,138,0,128,0,69,0,197,0,32,1,116,255,195,255,188,0,105,1,197,0,86,2,186,1,17,1,34,1,143,0,216,1,226,1,157,0,114,1,159,1,65,1,116,1,129,1,146,1,40,2,155,0,24,0,38,2,7,1,245,255,21,0,104,1,227,0,147,0,2,255,168,1,97,0,110,1,243,255,119,1,141,0,193,1,232,0,140,1,251,1,218,1,16,1,189,2,68,1,106,1,209,255,75,2,148,0,31,2,69,0,144,1,205,255,49,2,59,0,220,0,246,255,96,1,147,0,206,0,211,0,141,2,185,0,51,2,41,1,53,2,28,1,82,2,121,0,254,2,192,0,142,1,118,0,130,2,178,1,233,0,8,1,225,1,211,1,129,0,91,255,187,2,239,0,90,0,26,0,86,1,218,1,201,255,27,0,132,1,94,0,84,255,0,0,213,2,123,1,196,255,81,1,114,1,209,1,95,0,63,1,38,3,83,2,78,0,4,1,241,1,83,3,210,0,48,2,202,1,62,2,48,254,202,0,241,1,113,2,54,255,152,0,48,0,200,2,236,255,54,2,100,0,203,2,199,1,212,1,155,1,93,2,63,1,134,2,195,0,103,2,145,1,26,2,168,2,227,2,201,0,155,2,178,1,186,3,198,1,169,1,134,2,235,1,94,2,169,2,160,1,252,1,241,1,54,3,170,1,47,3,148,2,135,2,116,2,204,2,185,2,210,1,106,2,201,1,173,2,204,1,109,1,53,1,209,2,55,2,68,3,89,2,97,2,44,1,57,3,203,1,175,3,175,2,169,2,21,2,147,3,86,2,79,2,243,0,108,3,195,1,106,3,164,1,18,3,61,1,220,2,220,0,154,3,61,1,84,4,111,1,19,2,210,1,4,4,137,2,29,4,103,2,10,4,41,2,61,3,90,2,253,3,31,3,159,3,35,3,110,3,251,2,31,3,240,1,93,5,5,3,73,2,2,3,35,3,162,3,75,4,25,3,198,4,94,3,185,4,127,3,1,4,215,2,4,3,77,3,148,4,91,4,99,3,253,3,62,3,245,3,73,3,142,3,250,1,191,2,215,4,53,4,108,2,51,3,172,4,59,4,131,4,57,4,118,4,139,3,11,6,97,4,29,5,136,2,63,5,100,2,204,5,220,3,199,5,169,3,217,3,48,5,187,3,61,5,173,1,142,3,73,3,58,5,52,2,155,4,156,1,132,4,147,5,40,5,154,5,50,5,128,2,248,2,190,6,130,5,190,0,43,2,49,4,237,3,170,1,1,1,71,3,212,3,235,0,231,0,240,5,143,4,109,0,37,1,246,3,33,6,49,1,142,0,124,4,27,2,221,254,148,255,189,4,204,3,22,0,40,255,155,2,60,3,30,254,182,1,197,1,151,5,187,253,90,254,21,3,131,1,154,254,58,254,174,0,12,3,220,255,140,254,134,1,122,255,139,253,160,0,206,254,239,2,22,251,181,254,177,0,10,2,8,255,62,2,5,255,127,2,237,253,151,1,172,253,138,1,93,254,21,3,151,253,33,3,38,252,143,1,167,252,215,2,249,255,6,2,65,253,54,1,137,251,232,255,22,252,31,1,64,252,107,1,237,250,56,1,2,250,245,0,235,249,49,1,28,0,153,0,165,252,81,255,223,255,76,1,138,250,102,255,212,0,154,1,175,253,59,255,188,251,64,253,120,252,191,255,26,1,111,1,106,252,82,253,89,1,93,0,254,254,155,254,184,2,132,2,75,253,228,255,192,1,237,1,239,254,193,0,15,2,34,2,13,255,255,253,128,1,120,255,17,1,159,254,0,2,114,255,25,2,58,255,173,3,238,2,83,0,248,0,66,2,93,3,200,255,80,2,74,3,44,0,124,3,24,0,33,0,122,3,240,255,214,3,63,3,118,5,255,5,106,7,180,6,96,5,156,7,185,5,22,252,95,252,184,251,77,251,127,253,93,252,164,253,63,252,245,252,95,253,189,252,236,252,96,254,104,253,54,254,2,253,116,253,247,253,106,253,17,254,1,252,3,254,1,252,84,254,68,254,216,253,144,254,63,254,33,254,45,255,226,251,121,252,196,254,7,255,199,253,177,253,199,253,237,254,227,253,65,255,52,253,68,255,182,252,248,254,179,254,8,255,194,254,28,255,237,254,1,0,201,253,28,255,141,255,35,255,18,255,138,254,59,255,5,254,34,255,189,253,254,254,80,254,195,255,12,255,167,254,2,0,174,254,39,0,41,255,87,255,198,255,0,0,200,255,250,255,53,255,125,255,1,0,70,255,251,255,45,255,6,0,132,254,11,0,94,254,140,255,131,0,122,255,113,0,89,0,252,255,71,0,254,255,237,255,64,255,6,1,24,0,189,0,151,0,123,255,147,255,186,0,103,255,166,0,37,255,37,0,139,0,193,0,171,0,81,1,124,0,158,0,195,255,141,0,226,0,243,255,190,0,231,0,34,0,98,1,109,0,60,1,201,0,244,0,164,0,74,1,171,255,134,1,172,255,254,0,71,1,1,1,79,1,235,1,147,0,220,1,105,0,54,0,77,0,181,1,114,1,165,1,58,1,193,1,86,1,73,1,126,0,161,2,36,1,59,2,132,1,243,0,193,0,141,2,64,1,109,2,24,1,194,0,124,1,5,2,69,2,45,0,67,1,111,0,166,1,233,1,139,1,222,2,22,2,110,2,34,2,230,1,246,1,62,1,60,2,189,0,38,2,129,1,166,1,99,255,153,0,131,255,126,1,59,255,130,1,249,254,78,1,228,0,185,2,68,255,1,0,51,0,41,1,5,254,213,0,136,254,141,1,232,255,255,0,221,253,89,0,10,254,162,255,131,1,179,0,148,253,68,0,84,253,112,0,126,253,162,254,252,254,172,0,74,254,188,254,8,1,136,2,60,252,252,255,159,251,7,0,122,255,134,0,147,251,206,254,143,0,96,0,92,254,15,254,59,251,162,254,9,250,83,253,95,255,72,0,105,3,179,2,220,2,27,1,153,3,97,1,78,1,219,1,71,4,53,3,96,3,12,2,75,3,241,1,202,2,199,2,20,3,238,2,52,4,202,2,180,4,241,2,65,2,150,2,124,245,170,192,38,3,44,7,95,251,33,228,37,12,28,4,40,248,202,208,85,16,107,5,192,249,99,218,69,9,145,5,232,249,78,219,176,12,193,7,210,251,214,230,35,7,16,9,184,252,64,236,173,3,242,12,199,254,163,248,47,9,161,11,41,254,234,244,32,14,116,9,247,252,183,237,123,13,24,12,98,254,70,246,139,11,205,16,72,0,178,1,56,7,148,17,139,0,68,3,44,15,40,21,157,1,180,9,163,4,42,28,67,3,166,19,11,12,40,35,139,4,90,27,216,28,115,3,37,247,177,202,74,23,226,5,58,250,60,221,35,20,86,8,61,252,88,233,8,31,217,7,228,251,65,231,107,25,202,8,139,252,49,235,246,29,192,10,180,253,47,242,64,23,200,11,60,254,92,245,34,19,180,14,131,255,17,253,77,27,4,14,60,255,103,251,238,31,138,15,213,255,252,254,176,23,52,17,107,0,133,2,29,30,223,19,64,1,136,7,147,21,133,23,57,2,98,13,89,30,214,27,50,3,62,19,172,23,2,31,209,3,253,22,218,21,223,44,243,5,212,35,85,41,76,5,159,249,153,217,89,35,61,6,145,250,68,223,66,38,243,7,247,251,180,231,242,34,111,9,244,252,164,237,56,40,24,10,87,253,253,239,191,36,174,10,171,253,245,241,252,33,146,12,156,254,160,247,29,38,67,13,235,254,123,249,193,39,52,15,181,255,58,254,210,35,176,17,148,0,123,3,168,39,140,19,40,1,245,6,154,35,103,22,241,1,177,11,4,41,122,24,116,2,198,14,126,39,207,29,151,3,158,21,140,34,23,34,93,4,72,26,252,34,208,48,112,6,193,38,124,50,208,3,185,247,47,206,171,44,219,6,28,251,141,226,106,47,24,9,189,252,96,236,124,44,64,9,214,252,248,236,204,41,248,11,83,254,236,245,44,48,45,11,238,253,136,243,202,45,255,12,205,254,200,248,6,44,116,14,106,255,120,252,109,42,61,17,110,0,151,2,50,47,181,17,150,0,134,3,19,44,85,20,98,1,84,8,184,46,161,24,125,2,253,14,159,43,110,29,132,3,44,21,96,47,137,32,25,4,168,24,217,42,25,42,149,5,156,33,60,40,224,67,87,8,53,50,75,54,145,6,220,250,15,225,36,49,253,7,254,251,221,231,209,51,135,9,2,253,254,237,209,54,173,11,47,254,14,245,140,52,26,12,99,254,78,246,108,48,74,14,89,255,18,252,198,52,196,14,137,255,55,253,80,50,176,16,62,0,118,1,221,52,253,18,253,0,243,5,123,49,81,21,168,1,248,9,30,54,218,23,78,2,223,13,231,50,83,25,166,2,244,15,245,52,41,30,169,3,7,22,157,50,95,36,189,4,136,28,146,53,31,45,252,5,5,36,47,49,102,59,146,7,147,45,9,59,4,6,91,250,4,222,224,58,29,9,192,252,113,236,191,56,207,9,45,253,0,239,100,57,127,12,147,254,107,247,22,60,232,13,49,255,33,251,53,55,120,15,206,255,212,254,254,58,140,16,50,0,42,1,252,55,216,18,242,0,174,5,254,57,75,21,166,1,238,9,202,59,195,23,72,2,190,13,249,55,232,26,0,3,15,18,212,58,9,30,162,3,226,21,70,56,210,36,207,4,245,28,27,60,13,38,0,5,26,30,232,57,191,55,52,7,94,43,32,53,107,97,109,10,195,62,12,64,177,7,198,251,139,230,177,65,16,11,223,253,45,243,97,61,27,11,229,253,80,243,232,62,8,13,209,254,223,248,0,64,123,15,207,255,218,254,44,66,227,17,165,0,224,3,95,61,247,17,171,0,6,4,94,63,72,21,165,1,233,9,192,65,238,24,143,2,105,15,129,61,229,27,53,3,80,19,198,63,45,29,120,3,223,20,227,64,176,33,76,4,222,25,132,66,178,40,99,5,111,32,33,62,41,46,29,6,207,36,238,65,98,57,95,7,96,44,131,64,134,81,102,9,147,56,222,70,35,8,25,252,131,232,201,75,106,12,137,254,47,247,100,68,98,13,248,254,203,249,86,78,187,15,231,255,105,255,149,70,153,16,54,0,70,1,8,74,202,19,58,1,98,7,47,69,26,21,153,1,157,9,123,77,48,24,98,2,92,14,30,70,102,27,27,3,176,18,70,83,197,30,198,3,184,22,246,69,73,36,186,4,115,28,200,74,74,36,186,4,116,28,37,80,117,44,230,5,129,35,155,70,149,56,74,7,226,43,31,78,218,69,129,8,52,51,154,73,252,127,0,12,62,72,61,42,81,112,63,11,181,67,0,80,225,10,198,253,153,242,153,73,194,25,191,2,139,16,81,24,245,28,108,3,156,20,51,67,204,40,103,5,133,32,122,84,245,4,61,249,74,215,143,82,71,17,113,0,171,2,40,44,20,6,106,250,95,222,61,74,20,50,150,6,164,39,215,67,194,9,37,253,210,238,194,69,225,18,244,0,192,5,10,39,194,9,37,253,210,238,122,68,184,30,196,3,170,22,174,55,92,7,133,251,5,229,20,62,81,12,125,254,233,246,61,26,10,7,67,251,121,227,10,71,225,78,53,9,109,55,102,70,215,11,67,254,138,245,71,65,225,22,16,2,109,12,143,34,174,15,226,255,76,255,20,62,10,35,134,4,60,27,102,70,112,5,198,249,129,218,71,65,0,16,0,0,0,0,0,32,143,2,108,245,79,192,133,59,102,54,16,7,132,42,174,55,40,12,106,254,116,246,10,55,61,18,193,0,141,4,30,21,143,10,154,253,143,241,122,52,153,25,182,2,84,16,163,48,133,3,67,247,100,203,163,48,102,10,131,253,7,241,184,14,143,2,108,245,79,192,153,57,215,91,22,10,183,60,225,74,153,9,13,253,62,238,184,78,215,19,62,1,121,7,225,26,0,16,0,0,0,0,0,80,112,33,65,4,156,25,204,76,225,2,26,246,105,196,61,74,163,16,58,0,91,1,184,30,40,8,29,252,151,232,204,44,0,48,87,6,43,38,20,62,194,5,26,250,126,220,112,61,20,18,180,0,62,4,215,35,153,5,240,249,131,219,184,62,92,27,25,3,164,18,235,57,225,2,26,246,105,196,225,58,204,8,140,252,55,235,215,19,204,4,12,249,38,214,215,51,174,67,83,8,27,50,163,64,30,9,193,252,118,236,225,58,184,22,6,2,46,12,92,15,102,14,100,255,86,252,174,55,153,33,72,4,198,25,235,65,10,3,106,246,74,198,225,58,225,14,149,255,122,253,174,23,102,2,12,245,17,190,122,36,40,36,180,4,83,28,215,51,225,6,33,251,172,226,215,51,194,13,33,255,193,250,153,9,174,7,196,251,127,230,204,44,153,21,187,1,108,10,245,40,225,2,26,246,105,196,112,45,122,12,145,254,92,247,194,5,10,3,106,246,74,198,0,64,248,65,226,67,190,69,142,71,82,73,12,75,188,76,98,78,0,80,150,81,35,83,170,84,42,86,163,87,22,89,130,90,234,91,76,93,168,94,0,96,83,97,161,98,236,99,49,101,115,102,177,103,235,104,34,106,85,107,132,108,177,109,218,110,0,112,35,113,67,114,97,115,123,116,147,117,169,118,188,119,204,120,218,121,230,122,239,123,247,124,252,125,255,126,255,127,255,127,61,10,63,10,69,10,78,10,91,10,108,10,129,10,153,10,181,10,212,10,248,10,31,11,74,11,120,11,170,11,224,11,25,12,86,12,151,12,219,12,35,13,110,13,189,13,15,14,101,14,190,14,27,15,123,15,223,15,70,16,176,16,30,17,143,17,3,18,123,18,245,18,115,19,244,19,120,20,0,21,138,21,23,22,168,22,59,23,209,23,106,24,6,25,165,25,70,26,234,26,145,27,59,28,231,28,149,29,70,30,250,30,176,31,104,32,35,33,224,33,159,34,97,35,36,36,234,36,178,37,124,38,71,39,21,40,228,40,181,41,136,42,93,43,51,44,11,45,228,45,191,46,155,47,121,48,88,49,56,50,26,51,252,51,224,52,196,53,170,54,145,55,120,56,96,57,73,58,51,59,29,60,8,61,243,61,223,62,203,63,184,64,165,65,146,66,127,67,108,68,90,69,71,70,52,71,33,72,14,73,251,73,231,74,211,75,191,76,170,77,149,78,126,79,104,80,80,81,56,82,31,83,5,84,234,84,207,85,178,86,148,87,116,88,84,89,50,90,15,91,235,91,197,92,157,93,117,94,74,95,30,96,240,96,192,97,143,98,91,99,38,100,239,100,181,101,122,102,60,103,253,103,187,104,119,105,48,106,232,106,156,107,79,108,255,108,172,109,87,110,255,110,165,111,71,112,231,112,133,113,31,114,183,114,75,115,221,115,108,116,248,116,129,117,6,118,137,118,8,119,133,119,254,119,116,120,230,120,86,121,194,121,42,122,144,122,242,122,80,123,171,123,3,124,87,124,167,124,244,124,62,125,132,125,198,125,5,126,64,126,120,126,172,126,220,126,9,127,49,127,87,127,120,127,150,127,176,127,199,127,217,127,232,127,243,127,251,127,255,127,255,127,229,127,153,127,25,127,103,126,129,125],"i8",y,a.GLOBAL_BASE+20480),p([106,124,33,123,167,121,252,119,34,118,24,116,223,113,122,111,231,108,41,106,65,103,47,100,245,96,149,93,15,90,101,86,153,82,171,78,158,74,116,70,45,66,204,61,82,57,193,52,27,48,98,43,151,38,189,33,213,28,226,23,230,18,226,13,216,8,203,3,61,10,64,10,73,10,88,10,108,10,135,10,167,10,205,10,249,10,43,11,99,11,160,11,227,11,44,12,122,12,207,12,40,13,136,13,237,13,87,14,199,14,60,15,183,15,55,16,189,16,71,17,215,17,108,18,6,19,165,19,73,20,242,20,159,21,82,22,9,23,196,23,133,24,73,25,18,26,224,26,177,27,135,28,97,29,62,30,32,31,5,32,238,32,219,33,203,34,191,35,182,36,176,37,174,38,174,39,177,40,184,41,193,42,204,43,218,44,235,45,254,46,19,48,42,49,67,50,94,51,123,52,154,53,186,54,219,55,254,56,34,58,71,59,109,60,148,61,188,62,228,63,13,65,54,66,96,67,138,68,180,69,221,70,7,72,48,73,89,74,130,75,169,76,208,77,246,78,27,80,63,81,98,82,132,83,164,84,194,85,223,86,250,87,19,89,43,90,64,91,83,92,99,93,113,94,125,95,134,96,140,97,143,98,144,99,141,100,135,101,126,102,114,103,98,104,79,105,56,106,30,107,255,107,221,108,183,109,140,110,94,111,43,112,244,112,185,113,121,114,53,115,236,115,158,116,76,117,245,117,153,118,55,119,209,119,102,120,246,120,129,121,6,122,134,122,1,123,118,123,230,123,81,124,182,124,21,125,111,125,195,125,17,126,90,126,157,126,219,126,18,127,68,127,112,127,150,127,183,127,209,127,230,127,244,127,253,127,255,127,255,127,244,127,208,127,149,127,66,127,215,126,85,126,188,125,12,125,69,124,104,123,117,122,108,121,78,120,28,119,213,117,122,116,13,115,140,113,250,111,87,110,162,108,222,106,11,105,40,103,57,101,60,99,51,97,30,95,255,92,215,90,165,88,108,86,44,84,229,81,154,79,74,77,247,74,161,72,74,70,243,67,156,65,71,63,244,60,164,58,88,56,18,54,209,51,152,49,103,47,62,45,31,43,11,41,2,39,5,37,21,35,51,33,95,31,155,29,231,27,67,26,177,24,49,23,195,21,105,20,34,19,239,17,209,16,201,15,214,14,249,13,50,13,130,12,232,11,102,11,252,10,169,10,109,10,73,10,61,10,61,10,63,10,67,10,74,10,84,10,96,10,111,10,129,10,150,10,174,10,200,10,229,10,5,11,39,11,77,11,117,11,159,11,205,11,253,11,48,12,101,12,157,12,216,12,22,13,86,13,153,13,222,13,38,14,113,14,190,14,13,15,96,15,181,15,12,16,102,16,194,16,33,17,130,17,230,17,76,18,180,18,31,19,140,19,252,19,110,20,226,20,88,21,209,21,76,22,201,22,72,23,202,23,77,24,211,24,91,25,229,25,113,26,254,26,142,27,32,28,180,28,74,29,225,29,123,30,22,31,179,31,82,32,242,32,149,33,57,34,222,34,133,35,46,36,216,36,132,37,50,38,224,38,145,39,66,40,245,40,169,41,95,42,22,43,206,43,135,44,66,45,253,45,186,46,120,47,54,48,246,48,183,49,120,50,59,51,254,51,194,52,135,53,77,54,19,55,218,55,161,56,106,57,50,58,252,58,197,59,144,60,90,61,37,62,240,62,188,63,136,64,84,65,32,66,236,66,185,67,133,68,82,69,30,70,235,70,183,71,132,72,80,73,28,74,231,74,179,75,126,76,73,77,19,78,221,78,166,79,111,80,56,81,0,82,199,82,142,83,84,84,25,85,221,85,161,86,100,87,38,88,231,88,167,89,103,90,37,91,226,91,158,92,89,93,19,94,204,94,131,95,57,96,238,96,162,97,84,98,5,99,181,99,99,100,15,101,186,101,100,102,12,103,178,103,87,104,250,104,155,105,59,106,217,106,117,107,16,108,168,108,63,109,211,109,102,110,247,110,134,111,19,112,158,112,39,113,174,113,50,114,181,114,53,115,179,115,47,116,169,116,33,117,150,117,9,118,122,118,232,118,84,119,190,119,37,120,138,120,236,120,76,121,170,121,5,122,94,122,180,122,7,123,88,123,167,123,242,123,60,124,130,124,198,124,8,125,71,125,131,125,188,125,243,125,39,126,89,126,136,126,180,126,221,126,4,127,40,127,73,127,103,127,131,127,156,127,178,127,197,127,214,127,228,127,239,127,247,127,253,127,255,127,255,127,97,125,160,117,15,105,48,88,181,67,116,44,98,19,68,101,99,111,100,101,114,0,101,110,99,111,100,101,114,0],"i8",y,a.GLOBAL_BASE+30720);var ve=a.alignMemory(p(12,"i8",m),8);function be(e){var i,r=be;r.called||((i=H)%4096>0&&(i+=4096-i%4096),H=i,r.called=!0,d(a.dynamicAlloc),r.alloc=a.dynamicAlloc,a.dynamicAlloc=function(){ze("cannot dynamically allocate, sbrk now has control")});var n=H;return 0==e||r.alloc(e)?n:-1>>>0}function ke(i){return e.___errno_location&&(T[e.___errno_location()>>2]=i),i}d(ve%8==0);var me={EPERM:1,ENOENT:2,ESRCH:3,EINTR:4,EIO:5,ENXIO:6,E2BIG:7,ENOEXEC:8,EBADF:9,ECHILD:10,EAGAIN:11,EWOULDBLOCK:11,ENOMEM:12,EACCES:13,EFAULT:14,ENOTBLK:15,EBUSY:16,EEXIST:17,EXDEV:18,ENODEV:19,ENOTDIR:20,EISDIR:21,EINVAL:22,ENFILE:23,EMFILE:24,ENOTTY:25,ETXTBSY:26,EFBIG:27,ENOSPC:28,ESPIPE:29,EROFS:30,EMLINK:31,EPIPE:32,EDOM:33,ERANGE:34,ENOMSG:42,EIDRM:43,ECHRNG:44,EL2NSYNC:45,EL3HLT:46,EL3RST:47,ELNRNG:48,EUNATCH:49,ENOCSI:50,EL2HLT:51,EDEADLK:35,ENOLCK:37,EBADE:52,EBADR:53,EXFULL:54,ENOANO:55,EBADRQC:56,EBADSLT:57,EDEADLOCK:35,EBFONT:59,ENOSTR:60,ENODATA:61,ETIME:62,ENOSR:63,ENONET:64,ENOPKG:65,EREMOTE:66,ENOLINK:67,EADV:68,ESRMNT:69,ECOMM:70,EPROTO:71,EMULTIHOP:72,EDOTDOT:73,EBADMSG:74,ENOTUNIQ:76,EBADFD:77,EREMCHG:78,ELIBACC:79,ELIBBAD:80,ELIBSCN:81,ELIBMAX:82,ELIBEXEC:83,ENOSYS:38,ENOTEMPTY:39,ENAMETOOLONG:36,ELOOP:40,EOPNOTSUPP:95,EPFNOSUPPORT:96,ECONNRESET:104,ENOBUFS:105,EAFNOSUPPORT:97,EPROTOTYPE:91,ENOTSOCK:88,ENOPROTOOPT:92,ESHUTDOWN:108,ECONNREFUSED:111,EADDRINUSE:98,ECONNABORTED:103,ENETUNREACH:101,ENETDOWN:100,ETIMEDOUT:110,EHOSTDOWN:112,EHOSTUNREACH:113,EINPROGRESS:115,EALREADY:114,EDESTADDRREQ:89,EMSGSIZE:90,EPROTONOSUPPORT:93,ESOCKTNOSUPPORT:94,EADDRNOTAVAIL:99,ENETRESET:102,EISCONN:106,ENOTCONN:107,ETOOMANYREFS:109,EUSERS:87,EDQUOT:122,ESTALE:116,ENOTSUP:95,ENOMEDIUM:123,EILSEQ:84,EOVERFLOW:75,ECANCELED:125,ENOTRECOVERABLE:131,EOWNERDEAD:130,ESTRPIPE:86};e.L=Ce,e.F=Ie,e.C=Le;var ye={0:"Success",1:"Not super-user",2:"No such file or directory",3:"No such process",4:"Interrupted system call",5:"I/O error",6:"No such device or address",7:"Arg list too long",8:"Exec format error",9:"Bad file number",10:"No children",11:"No more processes",12:"Not enough core",13:"Permission denied",14:"Bad address",15:"Block device required",16:"Mount device busy",17:"File exists",18:"Cross-device link",19:"No such device",20:"Not a directory",21:"Is a directory",22:"Invalid argument",23:"Too many open files in system",24:"Too many open files",25:"Not a typewriter",26:"Text file busy",27:"File too large",28:"No space left on device",29:"Illegal seek",30:"Read only file system",31:"Too many links",32:"Broken pipe",33:"Math arg out of domain of func",34:"Math result not representable",35:"File locking deadlock error",36:"File or path name too long",37:"No record locks available",38:"Function not implemented",39:"Directory not empty",40:"Too many symbolic links",42:"No message of desired type",43:"Identifier removed",44:"Channel number out of range",45:"Level 2 not synchronized",46:"Level 3 halted",47:"Level 3 reset",48:"Link number out of range",49:"Protocol driver not attached",50:"No CSI structure available",51:"Level 2 halted",52:"Invalid exchange",53:"Invalid request descriptor",54:"Exchange full",55:"No anode",56:"Invalid request code",57:"Invalid slot",59:"Bad font file fmt",60:"Device not a stream",61:"No data (for no delay io)",62:"Timer expired",63:"Out of streams resources",64:"Machine is not on the network",65:"Package not installed",66:"The object is remote",67:"The link has been severed",68:"Advertise error",69:"Srmount error",70:"Communication error on send",71:"Protocol error",72:"Multihop attempted",73:"Cross mount point (not really error)",74:"Trying to read unreadable message",75:"Value too large for defined data type",76:"Given log. name not unique",77:"f.d. invalid for this operation",78:"Remote address changed",79:"Can   access a needed shared lib",80:"Accessing a corrupted shared lib",81:".lib section in a.out corrupted",82:"Attempting to link in too many libs",83:"Attempting to exec a shared library",84:"Illegal byte sequence",86:"Streams pipe error",87:"Too many users",88:"Socket operation on non-socket",89:"Destination address required",90:"Message too long",91:"Protocol wrong type for socket",92:"Protocol not available",93:"Unknown protocol",94:"Socket type not supported",95:"Not supported",96:"Protocol family not supported",97:"Address family not supported by protocol family",98:"Address already in use",99:"Address not available",100:"Network interface is not configured",101:"Network is unreachable",102:"Connection reset by network",103:"Connection aborted",104:"Connection reset by peer",105:"No buffer space available",106:"Socket is already connected",107:"Socket is not connected",108:"Can't send after socket shutdown",109:"Too many references",110:"Connection timed out",111:"Connection refused",112:"Host is down",113:"Host is unreachable",114:"Socket already connected",115:"Connection already in progress",116:"Stale file handle",122:"Quota exceeded",123:"No medium (in tape drive)",125:"Operation canceled",130:"Previous owner died",131:"State not recoverable"},pe={ttys:[],init:function(){},shutdown:function(){},register:function(e,i){pe.ttys[e]={input:[],output:[],ops:i},Se.registerDevice(e,pe.stream_ops)},stream_ops:{open:function(e){var i=pe.ttys[e.node.rdev];if(!i)throw new Se.ErrnoError(me.ENODEV);e.tty=i,e.seekable=!1},close:function(e){e.tty.ops.flush(e.tty)},flush:function(e){e.tty.ops.flush(e.tty)},read:function(e,i,r,n,t){if(!e.tty||!e.tty.ops.get_char)throw new Se.ErrnoError(me.ENXIO);for(var f=0,o=0;o<n;o++){var a;try{a=e.tty.ops.get_char(e.tty)}catch(l){throw new Se.ErrnoError(me.EIO)}if(void 0===a&&0===f)throw new Se.ErrnoError(me.EAGAIN);if(null==a)break;f++,i[r+o]=a}return f&&(e.node.timestamp=Date.now()),f},write:function(e,i,r,n,t){if(!e.tty||!e.tty.ops.put_char)throw new Se.ErrnoError(me.ENXIO);for(var f=0;f<n;f++)try{e.tty.ops.put_char(e.tty,i[r+f])}catch(o){throw new Se.ErrnoError(me.EIO)}return n&&(e.node.timestamp=Date.now()),f}},default_tty_ops:{get_char:function(e){if(!e.input.length){var i=null;if("undefined"!=typeof window&&"function"==typeof window.prompt?null!==(i=window.prompt("Input: "))&&(i+="\n"):"function"==typeof readline&&null!==(i=readline())&&(i+="\n"),!i)return null;e.input=re(i,!0)}return e.input.shift()},put_char:function(i,r){null===r||10===r?(e.print(g(i.output,0)),i.output=[]):0!=r&&i.output.push(r)},flush:function(i){i.output&&i.output.length>0&&(e.print(g(i.output,0)),i.output=[])}},default_tty1_ops:{put_char:function(i,r){null===r||10===r?(e.printErr(g(i.output,0)),i.output=[]):0!=r&&i.output.push(r)},flush:function(i){i.output&&i.output.length>0&&(e.printErr(g(i.output,0)),i.output=[])}}},Ee={ops_table:null,mount:function(e){return Ee.createNode(null,"/",16895,0)},createNode:function(e,i,r,n){if(Se.isBlkdev(r)||Se.isFIFO(r))throw new Se.ErrnoError(me.EPERM);Ee.ops_table||(Ee.ops_table={dir:{node:{getattr:Ee.node_ops.getattr,setattr:Ee.node_ops.setattr,lookup:Ee.node_ops.lookup,mknod:Ee.node_ops.mknod,rename:Ee.node_ops.rename,unlink:Ee.node_ops.unlink,rmdir:Ee.node_ops.rmdir,readdir:Ee.node_ops.readdir,symlink:Ee.node_ops.symlink},stream:{llseek:Ee.stream_ops.llseek}},file:{node:{getattr:Ee.node_ops.getattr,setattr:Ee.node_ops.setattr},stream:{llseek:Ee.stream_ops.llseek,read:Ee.stream_ops.read,write:Ee.stream_ops.write,allocate:Ee.stream_ops.allocate,mmap:Ee.stream_ops.mmap,msync:Ee.stream_ops.msync}},link:{node:{getattr:Ee.node_ops.getattr,setattr:Ee.node_ops.setattr,readlink:Ee.node_ops.readlink},stream:{}},chrdev:{node:{getattr:Ee.node_ops.getattr,setattr:Ee.node_ops.setattr},stream:Se.chrdev_stream_ops}});var t=Se.createNode(e,i,r,n);return Se.isDir(t.mode)?(t.node_ops=Ee.ops_table.dir.node,t.stream_ops=Ee.ops_table.dir.stream,t.contents={}):Se.isFile(t.mode)?(t.node_ops=Ee.ops_table.file.node,t.stream_ops=Ee.ops_table.file.stream,t.usedBytes=0,t.contents=null):Se.isLink(t.mode)?(t.node_ops=Ee.ops_table.link.node,t.stream_ops=Ee.ops_table.link.stream):Se.isChrdev(t.mode)&&(t.node_ops=Ee.ops_table.chrdev.node,t.stream_ops=Ee.ops_table.chrdev.stream),t.timestamp=Date.now(),e&&(e.contents[i]=t),t},getFileDataAsRegularArray:function(e){if(e.contents&&e.contents.subarray){for(var i=[],r=0;r<e.usedBytes;++r)i.push(e.contents[r]);return i}return e.contents},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array},expandFileStorage:function(e,i){if(e.contents&&e.contents.subarray&&i>e.contents.length&&(e.contents=Ee.getFileDataAsRegularArray(e),e.usedBytes=e.contents.length),!e.contents||e.contents.subarray){var r=e.contents?e.contents.buffer.byteLength:0;if(r>=i)return;i=Math.max(i,r*(r<1048576?2:1.125)|0),0!=r&&(i=Math.max(i,256));var n=e.contents;return e.contents=new Uint8Array(i),void(e.usedBytes>0&&e.contents.set(n.subarray(0,e.usedBytes),0))}for(!e.contents&&i>0&&(e.contents=[]);e.contents.length<i;)e.contents.push(0)},resizeFileStorage:function(e,i){if(e.usedBytes!=i){if(0==i)return e.contents=null,void(e.usedBytes=0);if(!e.contents||e.contents.subarray){var r=e.contents;return e.contents=new Uint8Array(new ArrayBuffer(i)),r&&e.contents.set(r.subarray(0,Math.min(i,e.usedBytes))),void(e.usedBytes=i)}if(e.contents||(e.contents=[]),e.contents.length>i)e.contents.length=i;else for(;e.contents.length<i;)e.contents.push(0);e.usedBytes=i}},node_ops:{getattr:function(e){var i={};return i.dev=Se.isChrdev(e.mode)?e.id:1,i.ino=e.id,i.mode=e.mode,i.nlink=1,i.uid=0,i.gid=0,i.rdev=e.rdev,Se.isDir(e.mode)?i.size=4096:Se.isFile(e.mode)?i.size=e.usedBytes:Se.isLink(e.mode)?i.size=e.link.length:i.size=0,i.atime=new Date(e.timestamp),i.mtime=new Date(e.timestamp),i.ctime=new Date(e.timestamp),i.blksize=4096,i.blocks=Math.ceil(i.size/i.blksize),i},setattr:function(e,i){void 0!==i.mode&&(e.mode=i.mode),void 0!==i.timestamp&&(e.timestamp=i.timestamp),void 0!==i.size&&Ee.resizeFileStorage(e,i.size)},lookup:function(e,i){throw Se.genericErrors[me.ENOENT]},mknod:function(e,i,r,n){return Ee.createNode(e,i,r,n)},rename:function(e,i,r){if(Se.isDir(e.mode)){var n;try{n=Se.lookupNode(i,r)}catch(f){}if(n)for(var t in n.contents)throw new Se.ErrnoError(me.ENOTEMPTY)}delete e.parent.contents[e.name],e.name=r,i.contents[r]=e,e.parent=i},unlink:function(e,i){delete e.contents[i]},rmdir:function(e,i){var r=Se.lookupNode(e,i);for(var n in r.contents)throw new Se.ErrnoError(me.ENOTEMPTY);delete e.contents[i]},readdir:function(e){var i=[".",".."];for(var r in e.contents)e.contents.hasOwnProperty(r)&&i.push(r);return i},symlink:function(e,i,r){var n=Ee.createNode(e,i,41471,0);return n.link=r,n},readlink:function(e){if(!Se.isLink(e.mode))throw new Se.ErrnoError(me.EINVAL);return e.link}},stream_ops:{read:function(e,i,r,n,t){var f=e.node.contents;if(t>=e.node.usedBytes)return 0;var o=Math.min(e.node.usedBytes-t,n);if(d(o>=0),o>8&&f.subarray)i.set(f.subarray(t,t+o),r);else for(var a=0;a<o;a++)i[r+a]=f[t+a];return o},write:function(e,i,r,n,t,f){if(!n)return 0;var o=e.node;if(o.timestamp=Date.now(),i.subarray&&(!o.contents||o.contents.subarray)){if(f)return o.contents=i.subarray(r,r+n),o.usedBytes=n,n;if(0===o.usedBytes&&0===t)return o.contents=new Uint8Array(i.subarray(r,r+n)),o.usedBytes=n,n;if(t+n<=o.usedBytes)return o.contents.set(i.subarray(r,r+n),t),n}if(Ee.expandFileStorage(o,t+n),o.contents.subarray&&i.subarray)o.contents.set(i.subarray(r,r+n),t);else for(var a=0;a<n;a++)o.contents[t+a]=i[r+a];return o.usedBytes=Math.max(o.usedBytes,t+n),n},llseek:function(e,i,r){var n=i;if(1===r?n+=e.position:2===r&&Se.isFile(e.node.mode)&&(n+=e.node.usedBytes),n<0)throw new Se.ErrnoError(me.EINVAL);return n},allocate:function(e,i,r){Ee.expandFileStorage(e.node,i+r),e.node.usedBytes=Math.max(e.node.usedBytes,i+r)},mmap:function(e,i,r,n,t,f,o){if(!Se.isFile(e.node.mode))throw new Se.ErrnoError(me.ENODEV);var a,l,u=e.node.contents;if(2&o||u.buffer!==i&&u.buffer!==i.buffer){if((t>0||t+n<e.node.usedBytes)&&(u=u.subarray?u.subarray(t,t+n):Array.prototype.slice.call(u,t,t+n)),l=!0,!(a=Fe(n)))throw new Se.ErrnoError(me.ENOMEM);i.set(u,a)}else l=!1,a=u.byteOffset;return{ptr:a,allocated:l}},msync:function(e,i,r,n,t){if(!Se.isFile(e.node.mode))throw new Se.ErrnoError(me.ENODEV);return 2&t?0:(Ee.stream_ops.write(e,i,0,n,r,!1),0)}}},ge={dbs:{},indexedDB:function(){if("undefined"!=typeof indexedDB)return indexedDB;var e=null;return"object"==typeof window&&(e=window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB),d(e,"IDBFS used, but indexedDB not supported"),e},DB_VERSION:21,DB_STORE_NAME:"FILE_DATA",mount:function(e){return Ee.mount.apply(null,arguments)},syncfs:function(e,i,r){ge.getLocalSet(e,function(n,t){if(n)return r(n);ge.getRemoteSet(e,function(e,n){if(e)return r(e);var f=i?n:t,o=i?t:n;ge.reconcile(f,o,r)})})},getDB:function(e,i){var r,n=ge.dbs[e];if(n)return i(null,n);try{r=ge.indexedDB().open(e,ge.DB_VERSION)}catch(t){return i(t)}r.onupgradeneeded=function(e){var i,r=e.target.result,n=e.target.transaction;(i=r.objectStoreNames.contains(ge.DB_STORE_NAME)?n.objectStore(ge.DB_STORE_NAME):r.createObjectStore(ge.DB_STORE_NAME)).indexNames.contains("timestamp")||i.createIndex("timestamp","timestamp",{unique:!1})},r.onsuccess=function(){n=r.result,ge.dbs[e]=n,i(null,n)},r.onerror=function(e){i(this.error),e.preventDefault()}},getLocalSet:function(e,i){var r={};function n(e){return"."!==e&&".."!==e}function t(e){return function(i){return _e.join2(e,i)}}for(var f=Se.readdir(e.mountpoint).filter(n).map(t(e.mountpoint));f.length;){var o,a=f.pop();try{o=Se.stat(a)}catch(l){return i(l)}Se.isDir(o.mode)&&f.push.apply(f,Se.readdir(a).filter(n).map(t(a))),r[a]={timestamp:o.mtime}}return i(null,{type:"local",entries:r})},getRemoteSet:function(e,i){var r={};ge.getDB(e.mountpoint,function(e,n){if(e)return i(e);var t=n.transaction([ge.DB_STORE_NAME],"readonly");t.onerror=function(e){i(this.error),e.preventDefault()},t.objectStore(ge.DB_STORE_NAME).index("timestamp").openKeyCursor().onsuccess=function(e){var t=e.target.result;if(!t)return i(null,{type:"remote",db:n,entries:r});r[t.primaryKey]={timestamp:t.key},t.continue()}})},loadLocalEntry:function(e,i){var r,n;try{n=Se.lookupPath(e).node,r=Se.stat(e)}catch(t){return i(t)}return Se.isDir(r.mode)?i(null,{timestamp:r.mtime,mode:r.mode}):Se.isFile(r.mode)?(n.contents=Ee.getFileDataAsTypedArray(n),i(null,{timestamp:r.mtime,mode:r.mode,contents:n.contents})):i(new Error("node type not supported"))},storeLocalEntry:function(e,i,r){try{if(Se.isDir(i.mode))Se.mkdir(e,i.mode);else{if(!Se.isFile(i.mode))return r(new Error("node type not supported"));Se.writeFile(e,i.contents,{encoding:"binary",canOwn:!0})}Se.chmod(e,i.mode),Se.utime(e,i.timestamp,i.timestamp)}catch(n){return r(n)}r(null)},removeLocalEntry:function(e,i){try{Se.lookupPath(e);var r=Se.stat(e);Se.isDir(r.mode)?Se.rmdir(e):Se.isFile(r.mode)&&Se.unlink(e)}catch(n){return i(n)}i(null)},loadRemoteEntry:function(e,i,r){var n=e.get(i);n.onsuccess=function(e){r(null,e.target.result)},n.onerror=function(e){r(this.error),e.preventDefault()}},storeRemoteEntry:function(e,i,r,n){var t=e.put(r,i);t.onsuccess=function(){n(null)},t.onerror=function(e){n(this.error),e.preventDefault()}},removeRemoteEntry:function(e,i,r){var n=e.delete(i);n.onsuccess=function(){r(null)},n.onerror=function(e){r(this.error),e.preventDefault()}},reconcile:function(e,i,r){var n=0,t=[];Object.keys(e.entries).forEach(function(r){var f=e.entries[r],o=i.entries[r];(!o||f.timestamp>o.timestamp)&&(t.push(r),n++)});var f=[];if(Object.keys(i.entries).forEach(function(r){i.entries[r],e.entries[r]||(f.push(r),n++)}),!n)return r(null);var o=0,a=("remote"===e.type?e.db:i.db).transaction([ge.DB_STORE_NAME],"readwrite"),l=a.objectStore(ge.DB_STORE_NAME);function u(e){return e?u.errored?void 0:(u.errored=!0,r(e)):++o>=n?r(null):void 0}a.onerror=function(e){u(this.error),e.preventDefault()},t.sort().forEach(function(e){"local"===i.type?ge.loadRemoteEntry(l,e,function(i,r){if(i)return u(i);ge.storeLocalEntry(e,r,u)}):ge.loadLocalEntry(e,function(i,r){if(i)return u(i);ge.storeRemoteEntry(l,e,r,u)})}),f.sort().reverse().forEach(function(e){"local"===i.type?ge.removeLocalEntry(e,u):ge.removeRemoteEntry(l,e,u)})}},Ae={DIR_MODE:16895,FILE_MODE:33279,reader:null,mount:function(e){d(f),Ae.reader||(Ae.reader=new FileReaderSync);var i=Ae.createNode(null,"/",Ae.DIR_MODE,0),r={};function n(e){for(var n=e.split("/"),t=i,f=0;f<n.length-1;f++){var o=n.slice(0,f+1).join("/");r[o]||(r[o]=Ae.createNode(t,o,Ae.DIR_MODE,0)),t=r[o]}return t}function t(e){var i=e.split("/");return i[i.length-1]}return Array.prototype.forEach.call(e.opts.files||[],function(e){Ae.createNode(n(e.name),t(e.name),Ae.FILE_MODE,0,e,e.lastModifiedDate)}),(e.opts.blobs||[]).forEach(function(e){Ae.createNode(n(e.name),t(e.name),Ae.FILE_MODE,0,e.data)}),(e.opts.packages||[]).forEach(function(e){e.metadata.files.forEach(function(i){var r=i.filename.substr(1);Ae.createNode(n(r),t(r),Ae.FILE_MODE,0,e.blob.slice(i.start,i.end))})}),i},createNode:function(e,i,r,n,t,f){var o=Se.createNode(e,i,r);return o.mode=r,o.node_ops=Ae.node_ops,o.stream_ops=Ae.stream_ops,o.timestamp=(f||new Date).getTime(),d(Ae.FILE_MODE!==Ae.DIR_MODE),r===Ae.FILE_MODE?(o.size=t.size,o.contents=t):(o.size=4096,o.contents={}),e&&(e.contents[i]=o),o},node_ops:{getattr:function(e){return{dev:1,ino:void 0,mode:e.mode,nlink:1,uid:0,gid:0,rdev:void 0,size:e.size,atime:new Date(e.timestamp),mtime:new Date(e.timestamp),ctime:new Date(e.timestamp),blksize:4096,blocks:Math.ceil(e.size/4096)}},setattr:function(e,i){void 0!==i.mode&&(e.mode=i.mode),void 0!==i.timestamp&&(e.timestamp=i.timestamp)},lookup:function(e,i){throw new Se.ErrnoError(me.ENOENT)},mknod:function(e,i,r,n){throw new Se.ErrnoError(me.EPERM)},rename:function(e,i,r){throw new Se.ErrnoError(me.EPERM)},unlink:function(e,i){throw new Se.ErrnoError(me.EPERM)},rmdir:function(e,i){throw new Se.ErrnoError(me.EPERM)},readdir:function(e){throw new Se.ErrnoError(me.EPERM)},symlink:function(e,i,r){throw new Se.ErrnoError(me.EPERM)},readlink:function(e){throw new Se.ErrnoError(me.EPERM)}},stream_ops:{read:function(e,i,r,n,t){if(t>=e.node.size)return 0;var f=e.node.contents.slice(t,t+n),o=Ae.reader.readAsArrayBuffer(f);return i.set(new Uint8Array(o),r),f.size},write:function(e,i,r,n,t){throw new Se.ErrnoError(me.EIO)},llseek:function(e,i,r){var n=i;if(1===r?n+=e.position:2===r&&Se.isFile(e.node.mode)&&(n+=e.node.size),n<0)throw new Se.ErrnoError(me.EINVAL);return n}}},Se=(p(1,"i32*",m),p(1,"i32*",m),p(1,"i32*",m),{root:null,mounts:[],devices:[null],streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,trackingDelegate:{},tracking:{openFlags:{READ:1,WRITE:2}},ErrnoError:null,genericErrors:{},filesystems:null,handleFSError:function(e){if(!(e instanceof Se.ErrnoError))throw e+" : "+R();return ke(e.errno)},lookupPath:function(e,i){if(i=i||{},!(e=_e.resolve(Se.cwd(),e)))return{path:"",node:null};var r={follow_mount:!0,recurse_count:0};for(var n in r)void 0===i[n]&&(i[n]=r[n]);if(i.recurse_count>8)throw new Se.ErrnoError(me.ELOOP);for(var t=_e.normalizeArray(e.split("/").filter(function(e){return!!e}),!1),f=Se.root,o="/",a=0;a<t.length;a++){var l=a===t.length-1;if(l&&i.parent)break;if(f=Se.lookupNode(f,t[a]),o=_e.join2(o,t[a]),Se.isMountpoint(f)&&(!l||l&&i.follow_mount)&&(f=f.mounted.root),!l||i.follow)for(var u=0;Se.isLink(f.mode);){var s=Se.readlink(o);if(o=_e.resolve(_e.dirname(o),s),f=Se.lookupPath(o,{recurse_count:i.recurse_count}).node,u++>40)throw new Se.ErrnoError(me.ELOOP)}}return{path:o,node:f}},getPath:function(e){for(var i;;){if(Se.isRoot(e)){var r=e.mount.mountpoint;return i?"/"!==r[r.length-1]?r+"/"+i:r+i:r}i=i?e.name+"/"+i:e.name,e=e.parent}},hashName:function(e,i){for(var r=0,n=0;n<i.length;n++)r=(r<<5)-r+i.charCodeAt(n)|0;return(e+r>>>0)%Se.nameTable.length},hashAddNode:function(e){var i=Se.hashName(e.parent.id,e.name);e.name_next=Se.nameTable[i],Se.nameTable[i]=e},hashRemoveNode:function(e){var i=Se.hashName(e.parent.id,e.name);if(Se.nameTable[i]===e)Se.nameTable[i]=e.name_next;else for(var r=Se.nameTable[i];r;){if(r.name_next===e){r.name_next=e.name_next;break}r=r.name_next}},lookupNode:function(e,i){var r=Se.mayLookup(e);if(r)throw new Se.ErrnoError(r,e);for(var n=Se.hashName(e.id,i),t=Se.nameTable[n];t;t=t.name_next){var f=t.name;if(t.parent.id===e.id&&f===i)return t}return Se.lookup(e,i)},createNode:function(e,i,r,n){Se.FSNode||(Se.FSNode=function(e,i,r,n){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=Se.nextInode++,this.name=i,this.mode=r,this.node_ops={},this.stream_ops={},this.rdev=n},Se.FSNode.prototype={},Object.defineProperties(Se.FSNode.prototype,{read:{get:function(){return 365==(365&this.mode)},set:function(e){e?this.mode|=365:this.mode&=-366}},write:{get:function(){return 146==(146&this.mode)},set:function(e){e?this.mode|=146:this.mode&=-147}},isFolder:{get:function(){return Se.isDir(this.mode)}},isDevice:{get:function(){return Se.isChrdev(this.mode)}}}));var t=new Se.FSNode(e,i,r,n);return Se.hashAddNode(t),t},destroyNode:function(e){Se.hashRemoveNode(e)},isRoot:function(e){return e===e.parent},isMountpoint:function(e){return!!e.mounted},isFile:function(e){return 32768==(61440&e)},isDir:function(e){return 16384==(61440&e)},isLink:function(e){return 40960==(61440&e)},isChrdev:function(e){return 8192==(61440&e)},isBlkdev:function(e){return 24576==(61440&e)},isFIFO:function(e){return 4096==(61440&e)},isSocket:function(e){return 49152==(49152&e)},flagModes:{r:0,rs:1052672,"r+":2,w:577,wx:705,xw:705,"w+":578,"wx+":706,"xw+":706,a:1089,ax:1217,xa:1217,"a+":1090,"ax+":1218,"xa+":1218},modeStringToFlags:function(e){var i=Se.flagModes[e];if(void 0===i)throw new Error("Unknown file open mode: "+e);return i},flagsToPermissionString:function(e){var i=["r","w","rw"][3&e];return 512&e&&(i+="w"),i},nodePermissions:function(e,i){return Se.ignorePermissions?0:(-1===i.indexOf("r")||292&e.mode)&&(-1===i.indexOf("w")||146&e.mode)&&(-1===i.indexOf("x")||73&e.mode)?0:me.EACCES},mayLookup:function(e){var i=Se.nodePermissions(e,"x");return i||(e.node_ops.lookup?0:me.EACCES)},mayCreate:function(e,i){try{return Se.lookupNode(e,i),me.EEXIST}catch(r){}return Se.nodePermissions(e,"wx")},mayDelete:function(e,i,r){var n;try{n=Se.lookupNode(e,i)}catch(f){return f.errno}var t=Se.nodePermissions(e,"wx");if(t)return t;if(r){if(!Se.isDir(n.mode))return me.ENOTDIR;if(Se.isRoot(n)||Se.getPath(n)===Se.cwd())return me.EBUSY}else if(Se.isDir(n.mode))return me.EISDIR;return 0},mayOpen:function(e,i){return e?Se.isLink(e.mode)?me.ELOOP:Se.isDir(e.mode)&&(0!=(2097155&i)||512&i)?me.EISDIR:Se.nodePermissions(e,Se.flagsToPermissionString(i)):me.ENOENT},MAX_OPEN_FDS:4096,nextfd:function(e,i){e=e||0,i=i||Se.MAX_OPEN_FDS;for(var r=e;r<=i;r++)if(!Se.streams[r])return r;throw new Se.ErrnoError(me.EMFILE)},getStream:function(e){return Se.streams[e]},createStream:function(e,i,r){Se.FSStream||(Se.FSStream=function(){},Se.FSStream.prototype={},Object.defineProperties(Se.FSStream.prototype,{object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return 0!=(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}}}));var n=new Se.FSStream;for(var t in e)n[t]=e[t];e=n;var f=Se.nextfd(i,r);return e.fd=f,Se.streams[f]=e,e},closeStream:function(e){Se.streams[e]=null},chrdev_stream_ops:{open:function(e){var i=Se.getDevice(e.node.rdev);e.stream_ops=i.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:function(){throw new Se.ErrnoError(me.ESPIPE)}},major:function(e){return e>>8},minor:function(e){return 255&e},makedev:function(e,i){return e<<8|i},registerDevice:function(e,i){Se.devices[e]={stream_ops:i}},getDevice:function(e){return Se.devices[e]},getMounts:function(e){for(var i=[],r=[e];r.length;){var n=r.pop();i.push(n),r.push.apply(r,n.mounts)}return i},syncfs:function(e,i){"function"==typeof e&&(i=e,e=!1);var r=Se.getMounts(Se.root.mount),n=0;function t(e){if(e)return t.errored?void 0:(t.errored=!0,i(e));++n>=r.length&&i(null)}r.forEach(function(i){if(!i.type.syncfs)return t(null);i.type.syncfs(i,e,t)})},mount:function(e,i,r){var n,t="/"===r,f=!r;if(t&&Se.root)throw new Se.ErrnoError(me.EBUSY);if(!t&&!f){var o=Se.lookupPath(r,{follow_mount:!1});if(r=o.path,n=o.node,Se.isMountpoint(n))throw new Se.ErrnoError(me.EBUSY);if(!Se.isDir(n.mode))throw new Se.ErrnoError(me.ENOTDIR)}var a={type:e,opts:i,mountpoint:r,mounts:[]},l=e.mount(a);return l.mount=a,a.root=l,t?Se.root=l:n&&(n.mounted=a,n.mount&&n.mount.mounts.push(a)),l},unmount:function(e){var i=Se.lookupPath(e,{follow_mount:!1});if(!Se.isMountpoint(i.node))throw new Se.ErrnoError(me.EINVAL);var r=i.node,n=r.mounted,t=Se.getMounts(n);Object.keys(Se.nameTable).forEach(function(e){for(var i=Se.nameTable[e];i;){var r=i.name_next;-1!==t.indexOf(i.mount)&&Se.destroyNode(i),i=r}}),r.mounted=null;var f=r.mount.mounts.indexOf(n);d(-1!==f),r.mount.mounts.splice(f,1)},lookup:function(e,i){return e.node_ops.lookup(e,i)},mknod:function(e,i,r){var n=Se.lookupPath(e,{parent:!0}).node,t=_e.basename(e);if(!t||"."===t||".."===t)throw new Se.ErrnoError(me.EINVAL);var f=Se.mayCreate(n,t);if(f)throw new Se.ErrnoError(f);if(!n.node_ops.mknod)throw new Se.ErrnoError(me.EPERM);return n.node_ops.mknod(n,t,i,r)},create:function(e,i){return i=void 0!==i?i:438,i&=4095,i|=32768,Se.mknod(e,i,0)},mkdir:function(e,i){return i=void 0!==i?i:511,i&=1023,i|=16384,Se.mknod(e,i,0)},mkdev:function(e,i,r){return void 0===r&&(r=i,i=438),i|=8192,Se.mknod(e,i,r)},symlink:function(e,i){if(!_e.resolve(e))throw new Se.ErrnoError(me.ENOENT);var r=Se.lookupPath(i,{parent:!0}).node;if(!r)throw new Se.ErrnoError(me.ENOENT);var n=_e.basename(i),t=Se.mayCreate(r,n);if(t)throw new Se.ErrnoError(t);if(!r.node_ops.symlink)throw new Se.ErrnoError(me.EPERM);return r.node_ops.symlink(r,n,e)},rename:function(e,i){var r,n,t=_e.dirname(e),f=_e.dirname(i),o=_e.basename(e),a=_e.basename(i);try{r=Se.lookupPath(e,{parent:!0}).node,n=Se.lookupPath(i,{parent:!0}).node}catch(w){throw new Se.ErrnoError(me.EBUSY)}if(!r||!n)throw new Se.ErrnoError(me.ENOENT);if(r.mount!==n.mount)throw new Se.ErrnoError(me.EXDEV);var l,u=Se.lookupNode(r,o),s=_e.relative(e,f);if("."!==s.charAt(0))throw new Se.ErrnoError(me.EINVAL);if("."!==(s=_e.relative(i,t)).charAt(0))throw new Se.ErrnoError(me.ENOTEMPTY);try{l=Se.lookupNode(n,a)}catch(w){}if(u!==l){var c=Se.isDir(u.mode),h=Se.mayDelete(r,o,c);if(h)throw new Se.ErrnoError(h);if(h=l?Se.mayDelete(n,a,c):Se.mayCreate(n,a))throw new Se.ErrnoError(h);if(!r.node_ops.rename)throw new Se.ErrnoError(me.EPERM);if(Se.isMountpoint(u)||l&&Se.isMountpoint(l))throw new Se.ErrnoError(me.EBUSY);if(n!==r&&(h=Se.nodePermissions(r,"w")))throw new Se.ErrnoError(h);Se.hashRemoveNode(u);try{r.node_ops.rename(u,n,a)}catch(w){throw w}finally{Se.hashAddNode(u)}}},rmdir:function(e){var i=Se.lookupPath(e,{parent:!0}).node,r=_e.basename(e),n=Se.lookupNode(i,r),t=Se.mayDelete(i,r,!0);if(t)throw new Se.ErrnoError(t);if(!i.node_ops.rmdir)throw new Se.ErrnoError(me.EPERM);if(Se.isMountpoint(n))throw new Se.ErrnoError(me.EBUSY);i.node_ops.rmdir(i,r),Se.destroyNode(n)},readdir:function(e){var i=Se.lookupPath(e,{follow:!0}).node;if(!i.node_ops.readdir)throw new Se.ErrnoError(me.ENOTDIR);return i.node_ops.readdir(i)},unlink:function(e){var i=Se.lookupPath(e,{parent:!0}).node,r=_e.basename(e),n=Se.lookupNode(i,r),t=Se.mayDelete(i,r,!1);if(t)throw t===me.EISDIR&&(t=me.EPERM),new Se.ErrnoError(t);if(!i.node_ops.unlink)throw new Se.ErrnoError(me.EPERM);if(Se.isMountpoint(n))throw new Se.ErrnoError(me.EBUSY);i.node_ops.unlink(i,r),Se.destroyNode(n)},readlink:function(e){var i=Se.lookupPath(e).node;if(!i)throw new Se.ErrnoError(me.ENOENT);if(!i.node_ops.readlink)throw new Se.ErrnoError(me.EINVAL);return _e.resolve(Se.getPath(i.parent),i.node_ops.readlink(i))},stat:function(e,i){var r=Se.lookupPath(e,{follow:!i}).node;if(!r)throw new Se.ErrnoError(me.ENOENT);if(!r.node_ops.getattr)throw new Se.ErrnoError(me.EPERM);return r.node_ops.getattr(r)},lstat:function(e){return Se.stat(e,!0)},chmod:function(e,i,r){var n;if(!(n="string"==typeof e?Se.lookupPath(e,{follow:!r}).node:e).node_ops.setattr)throw new Se.ErrnoError(me.EPERM);n.node_ops.setattr(n,{mode:4095&i|-4096&n.mode,timestamp:Date.now()})},lchmod:function(e,i){Se.chmod(e,i,!0)},fchmod:function(e,i){var r=Se.getStream(e);if(!r)throw new Se.ErrnoError(me.EBADF);Se.chmod(r.node,i)},chown:function(e,i,r,n){var t;if(!(t="string"==typeof e?Se.lookupPath(e,{follow:!n}).node:e).node_ops.setattr)throw new Se.ErrnoError(me.EPERM);t.node_ops.setattr(t,{timestamp:Date.now()})},lchown:function(e,i,r){Se.chown(e,i,r,!0)},fchown:function(e,i,r){var n=Se.getStream(e);if(!n)throw new Se.ErrnoError(me.EBADF);Se.chown(n.node,i,r)},truncate:function(e,i){if(i<0)throw new Se.ErrnoError(me.EINVAL);var r;if(!(r="string"==typeof e?Se.lookupPath(e,{follow:!0}).node:e).node_ops.setattr)throw new Se.ErrnoError(me.EPERM);if(Se.isDir(r.mode))throw new Se.ErrnoError(me.EISDIR);if(!Se.isFile(r.mode))throw new Se.ErrnoError(me.EINVAL);var n=Se.nodePermissions(r,"w");if(n)throw new Se.ErrnoError(n);r.node_ops.setattr(r,{size:i,timestamp:Date.now()})},ftruncate:function(e,i){var r=Se.getStream(e);if(!r)throw new Se.ErrnoError(me.EBADF);if(0==(2097155&r.flags))throw new Se.ErrnoError(me.EINVAL);Se.truncate(r.node,i)},utime:function(e,i,r){var n=Se.lookupPath(e,{follow:!0}).node;n.node_ops.setattr(n,{timestamp:Math.max(i,r)})},open:function(i,r,n,t,f){if(""===i)throw new Se.ErrnoError(me.ENOENT);var o;if(n=void 0===n?438:n,n=64&(r="string"==typeof r?Se.modeStringToFlags(r):r)?4095&n|32768:0,"object"==typeof i)o=i;else{i=_e.normalize(i);try{o=Se.lookupPath(i,{follow:!(131072&r)}).node}catch(s){}}var a=!1;if(64&r)if(o){if(128&r)throw new Se.ErrnoError(me.EEXIST)}else o=Se.mknod(i,n,0),a=!0;if(!o)throw new Se.ErrnoError(me.ENOENT);if(Se.isChrdev(o.mode)&&(r&=-513),65536&r&&!Se.isDir(o.mode))throw new Se.ErrnoError(me.ENOTDIR);if(!a){var l=Se.mayOpen(o,r);if(l)throw new Se.ErrnoError(l)}512&r&&Se.truncate(o,0),r&=-641;var u=Se.createStream({node:o,path:Se.getPath(o),flags:r,seekable:!0,position:0,stream_ops:o.stream_ops,ungotten:[],error:!1},t,f);return u.stream_ops.open&&u.stream_ops.open(u),!e.logReadFiles||1&r||(Se.readFiles||(Se.readFiles={}),i in Se.readFiles||(Se.readFiles[i]=1,e.printErr("read file: "+i))),u},close:function(e){e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(i){throw i}finally{Se.closeStream(e.fd)}},llseek:function(e,i,r){if(!e.seekable||!e.stream_ops.llseek)throw new Se.ErrnoError(me.ESPIPE);return e.position=e.stream_ops.llseek(e,i,r),e.ungotten=[],e.position},read:function(e,i,r,n,t){if(n<0||t<0)throw new Se.ErrnoError(me.EINVAL);if(1==(2097155&e.flags))throw new Se.ErrnoError(me.EBADF);if(Se.isDir(e.node.mode))throw new Se.ErrnoError(me.EISDIR);if(!e.stream_ops.read)throw new Se.ErrnoError(me.EINVAL);var f=!0;if(void 0===t)t=e.position,f=!1;else if(!e.seekable)throw new Se.ErrnoError(me.ESPIPE);var o=e.stream_ops.read(e,i,r,n,t);return f||(e.position+=o),o},write:function(e,i,r,n,t,f){if(n<0||t<0)throw new Se.ErrnoError(me.EINVAL);if(0==(2097155&e.flags))throw new Se.ErrnoError(me.EBADF);if(Se.isDir(e.node.mode))throw new Se.ErrnoError(me.EISDIR);if(!e.stream_ops.write)throw new Se.ErrnoError(me.EINVAL);1024&e.flags&&Se.llseek(e,0,2);var o=!0;if(void 0===t)t=e.position,o=!1;else if(!e.seekable)throw new Se.ErrnoError(me.ESPIPE);var a=e.stream_ops.write(e,i,r,n,t,f);o||(e.position+=a);try{e.path&&Se.trackingDelegate.onWriteToFile&&Se.trackingDelegate.onWriteToFile(e.path)}catch(l){console.log("FS.trackingDelegate['onWriteToFile']('"+path+"') threw an exception: "+l.message)}return a},allocate:function(e,i,r){if(i<0||r<=0)throw new Se.ErrnoError(me.EINVAL);if(0==(2097155&e.flags))throw new Se.ErrnoError(me.EBADF);if(!Se.isFile(e.node.mode)&&!Se.isDir(node.mode))throw new Se.ErrnoError(me.ENODEV);if(!e.stream_ops.allocate)throw new Se.ErrnoError(me.EOPNOTSUPP);e.stream_ops.allocate(e,i,r)},mmap:function(e,i,r,n,t,f,o){if(1==(2097155&e.flags))throw new Se.ErrnoError(me.EACCES);if(!e.stream_ops.mmap)throw new Se.ErrnoError(me.ENODEV);return e.stream_ops.mmap(e,i,r,n,t,f,o)},msync:function(e,i,r,n,t){return e&&e.stream_ops.msync?e.stream_ops.msync(e,i,r,n,t):0},munmap:function(e){return 0},ioctl:function(e,i,r){if(!e.stream_ops.ioctl)throw new Se.ErrnoError(me.ENOTTY);return e.stream_ops.ioctl(e,i,r)},readFile:function(e,i){if((i=i||{}).flags=i.flags||"r",i.encoding=i.encoding||"binary","utf8"!==i.encoding&&"binary"!==i.encoding)throw new Error('Invalid encoding type "'+i.encoding+'"');var r,n=Se.open(e,i.flags),t=Se.stat(e).size,f=new Uint8Array(t);return Se.read(n,f,0,t,0),"utf8"===i.encoding?r=g(f,0):"binary"===i.encoding&&(r=f),Se.close(n),r},writeFile:function(e,i,r){if((r=r||{}).flags=r.flags||"w",r.encoding=r.encoding||"utf8","utf8"!==r.encoding&&"binary"!==r.encoding)throw new Error('Invalid encoding type "'+r.encoding+'"');var n=Se.open(e,r.flags,r.mode);if("utf8"===r.encoding){var t=new Uint8Array(S(i)+1),f=A(i,t,0,t.length);Se.write(n,t,0,f,0,r.canOwn)}else"binary"===r.encoding&&Se.write(n,i,0,i.length,0,r.canOwn);Se.close(n)},cwd:function(){return Se.currentPath},chdir:function(e){var i=Se.lookupPath(e,{follow:!0});if(!Se.isDir(i.node.mode))throw new Se.ErrnoError(me.ENOTDIR);var r=Se.nodePermissions(i.node,"x");if(r)throw new Se.ErrnoError(r);Se.currentPath=i.path},createDefaultDirectories:function(){Se.mkdir("/tmp"),Se.mkdir("/home"),Se.mkdir("/home/<USER>")},createDefaultDevices:function(){var e;if(Se.mkdir("/dev"),Se.registerDevice(Se.makedev(1,3),{read:function(){return 0},write:function(e,i,r,n,t){return n}}),Se.mkdev("/dev/null",Se.makedev(1,3)),pe.register(Se.makedev(5,0),pe.default_tty_ops),pe.register(Se.makedev(6,0),pe.default_tty1_ops),Se.mkdev("/dev/tty",Se.makedev(5,0)),Se.mkdev("/dev/tty1",Se.makedev(6,0)),"undefined"!=typeof crypto){var i=new Uint8Array(1);e=function(){return crypto.getRandomValues(i),i[0]}}else e=function(){return 256*Math.random()|0};Se.createDevice("/dev","random",e),Se.createDevice("/dev","urandom",e),Se.mkdir("/dev/shm"),Se.mkdir("/dev/shm/tmp")},createSpecialDirectories:function(){Se.mkdir("/proc"),Se.mkdir("/proc/self"),Se.mkdir("/proc/self/fd"),Se.mount({mount:function(){var e=Se.createNode("/proc/self","fd",16895,73);return e.node_ops={lookup:function(e,i){var r=+i,n=Se.getStream(r);if(!n)throw new Se.ErrnoError(me.EBADF);var t={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:function(){return n.path}}};return t.parent=t,t}},e}},{},"/proc/self/fd")},createStandardStreams:function(){e.stdin?Se.createDevice("/dev","stdin",e.stdin):Se.symlink("/dev/tty","/dev/stdin"),e.stdout?Se.createDevice("/dev","stdout",null,e.stdout):Se.symlink("/dev/tty","/dev/stdout"),e.stderr?Se.createDevice("/dev","stderr",null,e.stderr):Se.symlink("/dev/tty1","/dev/stderr");var i=Se.open("/dev/stdin","r");d(0===i.fd,"invalid handle for stdin ("+i.fd+")");var r=Se.open("/dev/stdout","w");d(1===r.fd,"invalid handle for stdout ("+r.fd+")");var n=Se.open("/dev/stderr","w");d(2===n.fd,"invalid handle for stderr ("+n.fd+")")},ensureErrnoError:function(){Se.ErrnoError||(Se.ErrnoError=function(e,i){this.node=i,this.setErrno=function(e){for(var i in this.errno=e,me)if(me[i]===e){this.code=i;break}},this.setErrno(e),this.message=ye[e]},Se.ErrnoError.prototype=new Error,Se.ErrnoError.prototype.constructor=Se.ErrnoError,[me.ENOENT].forEach(function(e){Se.genericErrors[e]=new Se.ErrnoError(e),Se.genericErrors[e].stack="<generic error, no stack>"}))},staticInit:function(){Se.ensureErrnoError(),Se.nameTable=new Array(4096),Se.mount(Ee,{},"/"),Se.createDefaultDirectories(),Se.createDefaultDevices(),Se.createSpecialDirectories(),Se.filesystems={MEMFS:Ee,IDBFS:ge,NODEFS:{},WORKERFS:Ae}},init:function(i,r,n){d(!Se.init.initialized,"FS.init was previously called. If you want to initialize later with custom parameters, remove any earlier calls (note that one is automatically added to the generated code)"),Se.init.initialized=!0,Se.ensureErrnoError(),e.stdin=i||e.stdin,e.stdout=r||e.stdout,e.stderr=n||e.stderr,Se.createStandardStreams()},quit:function(){Se.init.initialized=!1;var i=e.P;i&&i(0);for(var r=0;r<Se.streams.length;r++){var n=Se.streams[r];n&&Se.close(n)}},getMode:function(e,i){var r=0;return e&&(r|=365),i&&(r|=146),r},joinPath:function(e,i){var r=_e.join.apply(null,e);return i&&"/"==r[0]&&(r=r.substr(1)),r},absolutePath:function(e,i){return _e.resolve(i,e)},standardizePath:function(e){return _e.normalize(e)},findObject:function(e,i){var r=Se.analyzePath(e,i);return r.exists?r.object:(ke(r.error),null)},analyzePath:function(e,i){try{e=(n=Se.lookupPath(e,{follow:!i})).path}catch(t){}var r={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var n=Se.lookupPath(e,{parent:!0});r.parentExists=!0,r.parentPath=n.path,r.parentObject=n.node,r.name=_e.basename(e),n=Se.lookupPath(e,{follow:!i}),r.exists=!0,r.path=n.path,r.object=n.node,r.name=n.node.name,r.isRoot="/"===n.path}catch(t){r.error=t.errno}return r},createFolder:function(e,i,r,n){var t=_e.join2("string"==typeof e?e:Se.getPath(e),i),f=Se.getMode(r,n);return Se.mkdir(t,f)},createPath:function(e,i,r,n){e="string"==typeof e?e:Se.getPath(e);for(var t=i.split("/").reverse();t.length;){var f=t.pop();if(f){var o=_e.join2(e,f);try{Se.mkdir(o)}catch(a){}e=o}}return o},createFile:function(e,i,r,n,t){var f=_e.join2("string"==typeof e?e:Se.getPath(e),i),o=Se.getMode(n,t);return Se.create(f,o)},createDataFile:function(e,i,r,n,t,f){var o=i?_e.join2("string"==typeof e?e:Se.getPath(e),i):e,a=Se.getMode(n,t),l=Se.create(o,a);if(r){if("string"==typeof r){for(var u=new Array(r.length),s=0,c=r.length;s<c;++s)u[s]=r.charCodeAt(s);r=u}Se.chmod(l,146|a);var h=Se.open(l,"w");Se.write(h,r,0,r.length,0,f),Se.close(h),Se.chmod(l,a)}return l},createDevice:function(e,i,r,n){var t=_e.join2("string"==typeof e?e:Se.getPath(e),i),f=Se.getMode(!!r,!!n);Se.createDevice.major||(Se.createDevice.major=64);var o=Se.makedev(Se.createDevice.major++,0);return Se.registerDevice(o,{open:function(e){e.seekable=!1},close:function(e){n&&n.buffer&&n.buffer.length&&n(10)},read:function(e,i,n,t,f){for(var o=0,a=0;a<t;a++){var l;try{l=r()}catch(u){throw new Se.ErrnoError(me.EIO)}if(void 0===l&&0===o)throw new Se.ErrnoError(me.EAGAIN);if(null==l)break;o++,i[n+a]=l}return o&&(e.node.timestamp=Date.now()),o},write:function(e,i,r,t,f){for(var o=0;o<t;o++)try{n(i[r+o])}catch(a){throw new Se.ErrnoError(me.EIO)}return t&&(e.node.timestamp=Date.now()),o}}),Se.mkdev(t,f,o)},createLink:function(e,i,r,n,t){var f=_e.join2("string"==typeof e?e:Se.getPath(e),i);return Se.symlink(r,f)},forceLoadFile:function(i){if(i.isDevice||i.isFolder||i.link||i.contents)return!0;var r=!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!e.read)throw new Error("Cannot load without read() or XMLHttpRequest.");try{i.contents=re(e.read(i.url),!0),i.usedBytes=i.contents.length}catch(n){r=!1}return r||ke(me.EIO),r},createLazyFile:function(e,i,r,n,t){function o(){this.lengthKnown=!1,this.chunks=[]}if(o.prototype.get=function(e){if(!(e>this.length-1||e<0)){var i=e%this.chunkSize,r=e/this.chunkSize|0;return this.getter(r)[i]}},o.prototype.setDataGetter=function(e){this.getter=e},o.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",r,!1),e.send(null),!(e.status>=200&&e.status<300||304===e.status))throw new Error("Couldn't load "+r+". Status: "+e.status);var i,n=Number(e.getResponseHeader("Content-length")),t=(i=e.getResponseHeader("Accept-Ranges"))&&"bytes"===i,f=1048576;t||(f=n);var o=this;o.setDataGetter(function(e){var i=e*f,t=(e+1)*f-1;if(t=Math.min(t,n-1),void 0===o.chunks[e]&&(o.chunks[e]=function(e,i){if(e>i)throw new Error("invalid range ("+e+", "+i+") or no bytes requested!");if(i>n-1)throw new Error("only "+n+" bytes available! programmer error!");var t=new XMLHttpRequest;if(t.open("GET",r,!1),n!==f&&t.setRequestHeader("Range","bytes="+e+"-"+i),"undefined"!=typeof Uint8Array&&(t.responseType="arraybuffer"),t.overrideMimeType&&t.overrideMimeType("text/plain; charset=x-user-defined"),t.send(null),!(t.status>=200&&t.status<300||304===t.status))throw new Error("Couldn't load "+r+". Status: "+t.status);return void 0!==t.response?new Uint8Array(t.response||[]):re(t.responseText||"",!0)}(i,t)),void 0===o.chunks[e])throw new Error("doXHR failed!");return o.chunks[e]}),this.B=n,this.U=f,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!f)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var a=new o;Object.defineProperty(a,"length",{get:function(){return this.lengthKnown||this.cacheLength(),this.B}}),Object.defineProperty(a,"chunkSize",{get:function(){return this.lengthKnown||this.cacheLength(),this.U}});var l={isDevice:!1,contents:a}}else l={isDevice:!1,url:r};var u=Se.createFile(e,i,l,n,t);l.contents?u.contents=l.contents:l.url&&(u.contents=null,u.url=l.url),Object.defineProperty(u,"usedBytes",{get:function(){return this.contents.length}});var s={};return Object.keys(u.stream_ops).forEach(function(e){var i=u.stream_ops[e];s[e]=function(){if(!Se.forceLoadFile(u))throw new Se.ErrnoError(me.EIO);return i.apply(null,arguments)}}),s.read=function(e,i,r,n,t){if(!Se.forceLoadFile(u))throw new Se.ErrnoError(me.EIO);var f=e.node.contents;if(t>=f.length)return 0;var o=Math.min(f.length-t,n);if(d(o>=0),f.slice)for(var a=0;a<o;a++)i[r+a]=f[t+a];else for(a=0;a<o;a++)i[r+a]=f.get(t+a);return o},u.stream_ops=s,u},createPreloadedFile:function(i,r,n,t,f,o,a,l,u,s){De.init();var c=r?_e.resolve(_e.join2(i,r)):i;function h(n){function h(e){s&&s(),l||Se.createDataFile(i,r,e,t,f,u),o&&o(),de()}var w=!1;e.preloadPlugins.forEach(function(e){w||e.canHandle(c)&&(e.handle(n,c,h,function(){a&&a(),de()}),w=!0)}),w||h(n)}we(),"string"==typeof n?De.asyncLoad(n,function(e){h(e)},a):h(n)},indexedDB:function(){return window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB},DB_NAME:function(){return"EM_FS_"+window.location.pathname},DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:function(e,i,r){i=i||function(){},r=r||function(){};var n=Se.indexedDB();try{var t=n.open(Se.DB_NAME(),Se.DB_VERSION)}catch(f){return r(f)}t.onupgradeneeded=function(){console.log("creating db"),t.result.createObjectStore(Se.DB_STORE_NAME)},t.onsuccess=function(){var n=t.result.transaction([Se.DB_STORE_NAME],"readwrite"),f=n.objectStore(Se.DB_STORE_NAME),o=0,a=0,l=e.length;function u(){0==a?i():r()}e.forEach(function(e){var i=f.put(Se.analyzePath(e).object.contents,e);i.onsuccess=function(){++o+a==l&&u()},i.onerror=function(){o+ ++a==l&&u()}}),n.onerror=r},t.onerror=r},loadFilesFromDB:function(e,i,r){i=i||function(){},r=r||function(){};var n=Se.indexedDB();try{var t=n.open(Se.DB_NAME(),Se.DB_VERSION)}catch(f){return r(f)}t.onupgradeneeded=r,t.onsuccess=function(){var n=t.result;try{var o=n.transaction([Se.DB_STORE_NAME],"readonly")}catch(f){return void r(f)}var a=o.objectStore(Se.DB_STORE_NAME),l=0,u=0,s=e.length;function c(){0==u?i():r()}e.forEach(function(e){var i=a.get(e);i.onsuccess=function(){Se.analyzePath(e).exists&&Se.unlink(e),Se.createDataFile(_e.dirname(e),_e.basename(e),i.result,!0,!0,!0),++l+u==s&&c()},i.onerror=function(){l+ ++u==s&&c()}}),o.onerror=r},t.onerror=r}}),_e={splitPath:function(e){return/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1)},normalizeArray:function(e,i){for(var r=0,n=e.length-1;n>=0;n--){var t=e[n];"."===t?e.splice(n,1):".."===t?(e.splice(n,1),r++):r&&(e.splice(n,1),r--)}if(i)for(;r--;r)e.unshift("..");return e},normalize:function(e){var i="/"===e.charAt(0),r="/"===e.substr(-1);return(e=_e.normalizeArray(e.split("/").filter(function(e){return!!e}),!i).join("/"))||i||(e="."),e&&r&&(e+="/"),(i?"/":"")+e},dirname:function(e){var i=_e.splitPath(e),r=i[0],n=i[1];return r||n?(n&&(n=n.substr(0,n.length-1)),r+n):"."},basename:function(e){if("/"===e)return"/";var i=e.lastIndexOf("/");return-1===i?e:e.substr(i+1)},extname:function(e){return _e.splitPath(e)[3]},join:function(){var e=Array.prototype.slice.call(arguments,0);return _e.normalize(e.join("/"))},join2:function(e,i){return _e.normalize(e+"/"+i)},resolve:function(){for(var e="",i=!1,r=arguments.length-1;r>=-1&&!i;r--){var n=r>=0?arguments[r]:Se.cwd();if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");if(!n)return"";e=n+"/"+e,i="/"===n.charAt(0)}return(i?"/":"")+(e=_e.normalizeArray(e.split("/").filter(function(e){return!!e}),!i).join("/"))||"."},relative:function(e,i){function r(e){for(var i=0;i<e.length&&""===e[i];i++);for(var r=e.length-1;r>=0&&""===e[r];r--);return i>r?[]:e.slice(i,r-i+1)}e=_e.resolve(e).substr(1),i=_e.resolve(i).substr(1);for(var n=r(e.split("/")),t=r(i.split("/")),f=Math.min(n.length,t.length),o=f,a=0;a<f;a++)if(n[a]!==t[a]){o=a;break}var l=[];for(a=o;a<n.length;a++)l.push("..");return(l=l.concat(t.slice(o))).join("/")}};function Re(e,i){if(De.mainLoop.timingMode=e,De.mainLoop.timingValue=i,!De.mainLoop.func)return 1;if(0==e)De.mainLoop.scheduler=function(){setTimeout(De.mainLoop.runner,i)},De.mainLoop.method="timeout";else if(1==e)De.mainLoop.scheduler=function(){De.requestAnimationFrame(De.mainLoop.runner)},De.mainLoop.method="rAF";else if(2==e){if(!window.setImmediate){var r=[],n="__emcc";window.addEventListener("message",function(e){e.source===window&&e.data===n&&(e.stopPropagation(),r.shift()())},!0),window.setImmediate=function(e){r.push(e),window.postMessage(n,"*")}}De.mainLoop.scheduler=function(){window.setImmediate(De.mainLoop.runner)},De.mainLoop.method="immediate"}return 0}function Me(i,r,n,t,f){e.noExitRuntime=!0,d(!De.mainLoop.func,"emscripten_set_main_loop: there can only be one main loop function at once: call emscripten_cancel_main_loop to cancel the previous one before setting a new one with different parameters."),De.mainLoop.func=i,De.mainLoop.arg=t;var o=De.mainLoop.currentlyRunningMainloop;if(De.mainLoop.runner=function(){if(!w){if(De.mainLoop.queue.length>0){var r=Date.now(),n=De.mainLoop.queue.shift();if(n.func(n.arg),De.mainLoop.remainingBlockers){var f=De.mainLoop.remainingBlockers,l=f%1==0?f-1:Math.floor(f);n.counted?De.mainLoop.remainingBlockers=l:(l+=.5,De.mainLoop.remainingBlockers=(8*f+l)/9)}return console.log('main loop blocker "'+n.name+'" took '+(Date.now()-r)+" ms"),De.mainLoop.updateStatus(),void setTimeout(De.mainLoop.runner,0)}o<De.mainLoop.currentlyRunningMainloop||(De.mainLoop.currentFrameNumber=De.mainLoop.currentFrameNumber+1|0,1==De.mainLoop.timingMode&&De.mainLoop.timingValue>1&&De.mainLoop.currentFrameNumber%De.mainLoop.timingValue!=0?De.mainLoop.scheduler():("timeout"===De.mainLoop.method&&e.ctx&&(e.printErr("Looks like you are rendering without using requestAnimationFrame for the main loop. You should use 0 for the frame rate in emscripten_set_main_loop in order to use requestAnimationFrame, as that can greatly improve your frame rates!"),De.mainLoop.method=""),De.mainLoop.runIter(function(){void 0!==t?a.dynCall("vi",i,[t]):a.dynCall("v",i)}),o<De.mainLoop.currentlyRunningMainloop||("object"==typeof SDL&&SDL.audio&&SDL.audio.queueNewAudioData&&SDL.audio.queueNewAudioData(),De.mainLoop.scheduler())))}},f||(r&&r>0?Re(0,1e3/r):Re(1,1),De.mainLoop.scheduler()),n)throw"SimulateInfiniteLoop"}var De={mainLoop:{scheduler:null,method:"",currentlyRunningMainloop:0,func:null,arg:0,timingMode:0,timingValue:0,currentFrameNumber:0,queue:[],pause:function(){De.mainLoop.scheduler=null,De.mainLoop.currentlyRunningMainloop++},resume:function(){De.mainLoop.currentlyRunningMainloop++;var e=De.mainLoop.timingMode,i=De.mainLoop.timingValue,r=De.mainLoop.func;De.mainLoop.func=null,Me(r,0,!1,De.mainLoop.arg,!0),Re(e,i),De.mainLoop.scheduler()},updateStatus:function(){if(e.setStatus){var i=e.statusMessage||"Please wait...",r=De.mainLoop.remainingBlockers,n=De.mainLoop.expectedBlockers;r?r<n?e.setStatus(i+" ("+(n-r)+"/"+n+")"):e.setStatus(i):e.setStatus("")}},runIter:function(i){if(!w){if(e.preMainLoop&&!1===e.preMainLoop())return;try{i()}catch(r){if(r instanceof Pe)return;throw r&&"object"==typeof r&&r.stack&&e.printErr("exception thrown: "+[r,r.stack]),r}e.postMainLoop&&e.postMainLoop()}}},isFullScreen:!1,pointerLock:!1,moduleContextCreatedCallbacks:[],workers:[],init:function(){if(e.preloadPlugins||(e.preloadPlugins=[]),!De.initted){De.initted=!0;try{new Blob,De.hasBlobConstructor=!0}catch(f){De.hasBlobConstructor=!1,console.log("warning: no blob constructor, cannot create blobs with mimetypes")}De.BlobBuilder="undefined"!=typeof MozBlobBuilder?MozBlobBuilder:"undefined"!=typeof WebKitBlobBuilder?WebKitBlobBuilder:De.hasBlobConstructor?null:console.log("warning: no BlobBuilder"),De.URLObject="undefined"!=typeof window?window.URL?window.URL:window.webkitURL:void 0,e.noImageDecoding||void 0!==De.URLObject||(console.log("warning: Browser does not support creating object URLs. Built-in browser image decoding will not be available."),e.noImageDecoding=!0);var i={canHandle:function(i){return!e.noImageDecoding&&/\.(jpg|jpeg|png|bmp)$/i.test(i)},handle:function(i,r,n,t){var o=null;if(De.hasBlobConstructor)try{(o=new Blob([i],{type:De.getMimetype(r)})).size!==i.length&&(o=new Blob([new Uint8Array(i).buffer],{type:De.getMimetype(r)}))}catch(f){a.warnOnce("Blob constructor present but fails: "+f+"; falling back to blob builder")}if(!o){var l=new De.BlobBuilder;l.append(new Uint8Array(i).buffer),o=l.getBlob()}var u=De.URLObject.createObjectURL(o),s=new Image;s.onload=function(){d(s.complete,"Image "+r+" could not be decoded");var t=document.createElement("canvas");t.width=s.width,t.height=s.height,t.getContext("2d").drawImage(s,0,0),e.preloadedImages[r]=t,De.URLObject.revokeObjectURL(u),n&&n(i)},s.onerror=function(e){console.log("Image "+u+" could not be decoded"),t&&t()},s.src=u}};e.preloadPlugins.push(i);var r={canHandle:function(i){return!e.noAudioDecoding&&i.substr(-4)in{".ogg":1,".wav":1,".mp3":1}},handle:function(i,r,n,t){var o=!1;function a(t){o||(o=!0,e.preloadedAudios[r]=t,n&&n(i))}function l(){o||(o=!0,e.preloadedAudios[r]=new Audio,t&&t())}if(!De.hasBlobConstructor)return l();try{var u=new Blob([i],{type:De.getMimetype(r)})}catch(f){return l()}var s=De.URLObject.createObjectURL(u),c=new Audio;c.addEventListener("canplaythrough",function(){a(c)},!1),c.onerror=function(e){o||(console.log("warning: browser could not fully decode audio "+r+", trying slower base64 approach"),c.src="data:audio/x-"+r.substr(-3)+";base64,"+function(e){for(var i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r="",n=0,t=0,f=0;f<e.length;f++)for(n=n<<8|e[f],t+=8;t>=6;){var o=n>>t-6&63;t-=6,r+=i[o]}return 2==t?(r+=i[(3&n)<<4],r+="=="):4==t&&(r+=i[(15&n)<<2],r+="="),r}(i),a(c))},c.src=s,De.safeSetTimeout(function(){a(c)},1e4)}};e.preloadPlugins.push(r);var n=e.canvas;n&&(n.requestPointerLock=n.requestPointerLock||n.mozRequestPointerLock||n.webkitRequestPointerLock||n.msRequestPointerLock||function(){},n.exitPointerLock=document.exitPointerLock||document.mozExitPointerLock||document.webkitExitPointerLock||document.msExitPointerLock||function(){},n.exitPointerLock=n.exitPointerLock.bind(document),document.addEventListener("pointerlockchange",t,!1),document.addEventListener("mozpointerlockchange",t,!1),document.addEventListener("webkitpointerlockchange",t,!1),document.addEventListener("mspointerlockchange",t,!1),e.elementPointerLock&&n.addEventListener("click",function(e){!De.pointerLock&&n.requestPointerLock&&(n.requestPointerLock(),e.preventDefault())},!1))}function t(){De.pointerLock=document.pointerLockElement===n||document.mozPointerLockElement===n||document.webkitPointerLockElement===n||document.msPointerLockElement===n}},createContext:function(i,r,n,t){if(r&&e.ctx&&i==e.canvas)return e.ctx;var f,o;if(r){var a={antialias:!1,alpha:!1};if(t)for(var l in t)a[l]=t[l];(o=GL.createContext(i,a))&&(f=GL.getContext(o).GLctx),i.style.backgroundColor="black"}else f=i.getContext("2d");return f?(n&&(r||d("undefined"==typeof GLctx,"cannot set in module if GLctx is used, but we are a non-GL context that would replace it"),e.ctx=f,r&&GL.makeContextCurrent(o),e.useWebGL=r,De.moduleContextCreatedCallbacks.forEach(function(e){e()}),De.init()),f):null},destroyContext:function(e,i,r){},fullScreenHandlersInstalled:!1,lockPointer:void 0,resizeCanvas:void 0,requestFullScreen:function(i,r,n){De.lockPointer=i,De.resizeCanvas=r,De.vrDevice=n,void 0===De.lockPointer&&(De.lockPointer=!0),void 0===De.resizeCanvas&&(De.resizeCanvas=!1),void 0===De.vrDevice&&(De.vrDevice=null);var t=e.canvas;function f(){De.isFullScreen=!1;var i=t.parentNode;(document.webkitFullScreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.mozFullscreenElement||document.fullScreenElement||document.fullscreenElement||document.msFullScreenElement||document.msFullscreenElement||document.webkitCurrentFullScreenElement)===i?(t.cancelFullScreen=document.cancelFullScreen||document.mozCancelFullScreen||document.webkitCancelFullScreen||document.msExitFullscreen||document.exitFullscreen||function(){},t.cancelFullScreen=t.cancelFullScreen.bind(document),De.lockPointer&&t.requestPointerLock(),De.isFullScreen=!0,De.resizeCanvas&&De.setFullScreenCanvasSize()):(i.parentNode.insertBefore(t,i),i.parentNode.removeChild(i),De.resizeCanvas&&De.setWindowedCanvasSize()),e.onFullScreen&&e.onFullScreen(De.isFullScreen),De.updateCanvasDimensions(t)}De.fullScreenHandlersInstalled||(De.fullScreenHandlersInstalled=!0,document.addEventListener("fullscreenchange",f,!1),document.addEventListener("mozfullscreenchange",f,!1),document.addEventListener("webkitfullscreenchange",f,!1),document.addEventListener("MSFullscreenChange",f,!1));var o=document.createElement("div");t.parentNode.insertBefore(o,t),o.appendChild(t),o.requestFullScreen=o.requestFullScreen||o.mozRequestFullScreen||o.msRequestFullscreen||(o.webkitRequestFullScreen?function(){o.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT)}:null),n?o.requestFullScreen({vrDisplay:n}):o.requestFullScreen()},nextRAF:0,fakeRequestAnimationFrame:function(e){var i=Date.now();if(0===De.nextRAF)De.nextRAF=i+1e3/60;else for(;i+2>=De.nextRAF;)De.nextRAF+=1e3/60;var r=Math.max(De.nextRAF-i,0);setTimeout(e,r)},requestAnimationFrame:function(e){"undefined"==typeof window?De.fakeRequestAnimationFrame(e):(window.requestAnimationFrame||(window.requestAnimationFrame=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame||window.oRequestAnimationFrame||De.fakeRequestAnimationFrame),window.requestAnimationFrame(e))},safeCallback:function(e){return function(){if(!w)return e.apply(null,arguments)}},allowAsyncCallbacks:!0,queuedAsyncCallbacks:[],pauseAsyncCallbacks:function(){De.allowAsyncCallbacks=!1},resumeAsyncCallbacks:function(){if(De.allowAsyncCallbacks=!0,De.queuedAsyncCallbacks.length>0){var e=De.queuedAsyncCallbacks;De.queuedAsyncCallbacks=[],e.forEach(function(e){e()})}},safeRequestAnimationFrame:function(e){return De.requestAnimationFrame(function(){w||(De.allowAsyncCallbacks?e():De.queuedAsyncCallbacks.push(e))})},safeSetTimeout:function(i,r){return e.noExitRuntime=!0,setTimeout(function(){w||(De.allowAsyncCallbacks?i():De.queuedAsyncCallbacks.push(i))},r)},safeSetInterval:function(i,r){return e.noExitRuntime=!0,setInterval(function(){w||De.allowAsyncCallbacks&&i()},r)},getMimetype:function(e){return{jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",bmp:"image/bmp",ogg:"audio/ogg",wav:"audio/wav",mp3:"audio/mpeg"}[e.substr(e.lastIndexOf(".")+1)]},getUserMedia:function(e){window.getUserMedia||(window.getUserMedia=navigator.getUserMedia||navigator.mozGetUserMedia),window.getUserMedia(e)},getMovementX:function(e){return e.movementX||e.mozMovementX||e.webkitMovementX||0},getMovementY:function(e){return e.movementY||e.mozMovementY||e.webkitMovementY||0},getMouseWheelDelta:function(e){var i=0;switch(e.type){case"DOMMouseScroll":i=e.detail;break;case"mousewheel":i=e.wheelDelta;break;case"wheel":i=e.deltaY;break;default:throw"unrecognized mouse wheel event: "+e.type}return i},mouseX:0,mouseY:0,mouseMovementX:0,mouseMovementY:0,touches:{},lastTouches:{},calculateMouseEvent:function(i){if(De.pointerLock)"mousemove"!=i.type&&"mozMovementX"in i?De.mouseMovementX=De.mouseMovementY=0:(De.mouseMovementX=De.getMovementX(i),De.mouseMovementY=De.getMovementY(i)),"undefined"!=typeof SDL?(De.mouseX=SDL.mouseX+De.mouseMovementX,De.mouseY=SDL.mouseY+De.mouseMovementY):(De.mouseX+=De.mouseMovementX,De.mouseY+=De.mouseMovementY);else{var r=e.canvas.getBoundingClientRect(),n=e.canvas.width,t=e.canvas.height,f=void 0!==window.scrollX?window.scrollX:window.pageXOffset,o=void 0!==window.scrollY?window.scrollY:window.pageYOffset;if("touchstart"===i.type||"touchend"===i.type||"touchmove"===i.type){var a=i.touch;if(void 0===a)return;var l=a.pageX-(f+r.left),u=a.pageY-(o+r.top),s={x:l*=n/r.width,y:u*=t/r.height};if("touchstart"===i.type)De.lastTouches[a.identifier]=s,De.touches[a.identifier]=s;else if("touchend"===i.type||"touchmove"===i.type){var c=De.touches[a.identifier];c||(c=s),De.lastTouches[a.identifier]=c,De.touches[a.identifier]=s}return}var h=i.pageX-(f+r.left),w=i.pageY-(o+r.top);h*=n/r.width,w*=t/r.height,De.mouseMovementX=h-De.mouseX,De.mouseMovementY=w-De.mouseY,De.mouseX=h,De.mouseY=w}},xhrLoad:function(e,i,r){var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=function(){200==n.status||0==n.status&&n.response?i(n.response):r()},n.onerror=r,n.send(null)},asyncLoad:function(e,i,r,n){De.xhrLoad(e,function(r){d(r,'Loading data file "'+e+'" failed (no arrayBuffer).'),i(new Uint8Array(r)),n||de()},function(i){if(!r)throw'Loading data file "'+e+'" failed.';r()}),n||we()},resizeListeners:[],updateResizeListeners:function(){var i=e.canvas;De.resizeListeners.forEach(function(e){e(i.width,i.height)})},setCanvasSize:function(i,r,n){var t=e.canvas;De.updateCanvasDimensions(t,i,r),n||De.updateResizeListeners()},windowedWidth:0,windowedHeight:0,setFullScreenCanvasSize:function(){if("undefined"!=typeof SDL){var e=I[SDL.screen+0*a.QUANTUM_SIZE>>2];e|=8388608,T[SDL.screen+0*a.QUANTUM_SIZE>>2]=e}De.updateResizeListeners()},setWindowedCanvasSize:function(){if("undefined"!=typeof SDL){var e=I[SDL.screen+0*a.QUANTUM_SIZE>>2];e&=-8388609,T[SDL.screen+0*a.QUANTUM_SIZE>>2]=e}De.updateResizeListeners()},updateCanvasDimensions:function(i,r,n){r&&n?(i.widthNative=r,i.heightNative=n):(r=i.widthNative,n=i.heightNative);var t=r,f=n;if(e.forcedAspectRatio&&e.forcedAspectRatio>0&&(t/f<e.forcedAspectRatio?t=Math.round(f*e.forcedAspectRatio):f=Math.round(t/e.forcedAspectRatio)),(document.webkitFullScreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.mozFullscreenElement||document.fullScreenElement||document.fullscreenElement||document.msFullScreenElement||document.msFullscreenElement||document.webkitCurrentFullScreenElement)===i.parentNode&&"undefined"!=typeof screen){var o=Math.min(screen.width/t,screen.height/f);t=Math.round(t*o),f=Math.round(f*o)}De.resizeCanvas?(i.width!=t&&(i.width=t),i.height!=f&&(i.height=f),void 0!==i.style&&(i.style.removeProperty("width"),i.style.removeProperty("height"))):(i.width!=r&&(i.width=r),i.height!=n&&(i.height=n),void 0!==i.style&&(t!=r||f!=n?(i.style.setProperty("width",t+"px","important"),i.style.setProperty("height",f+"px","important")):(i.style.removeProperty("width"),i.style.removeProperty("height"))))},wgetRequests:{},nextWgetRequestHandle:0,getNextWgetRequestHandle:function(){var e=De.nextWgetRequestHandle;return De.nextWgetRequestHandle++,e}};e.requestFullScreen=function(e,i,r){De.requestFullScreen(e,i,r)},e.requestAnimationFrame=function(e){De.requestAnimationFrame(e)},e.setCanvasSize=function(e,i,r){De.setCanvasSize(e,i,r)},e.pauseMainLoop=function(){De.mainLoop.pause()},e.resumeMainLoop=function(){De.mainLoop.resume()},e.getUserMedia=function(){De.getUserMedia()},e.createContext=function(e,i,r,n){return De.createContext(e,i,r,n)},Se.staticInit(),K.unshift(function(){e.noFSInit||Se.init.initialized||Se.init()}),Y.push(function(){Se.ignorePermissions=!1}),Q.push(function(){Se.quit()}),e.FS_createFolder=Se.createFolder,e.FS_createPath=Se.createPath,e.FS_createDataFile=Se.createDataFile,e.FS_createPreloadedFile=Se.createPreloadedFile,e.FS_createLazyFile=Se.createLazyFile,e.FS_createLink=Se.createLink,e.FS_createDevice=Se.createDevice,e.FS_unlink=Se.unlink,K.unshift(function(){}),Q.push(function(){}),C=j=a.alignMemory(x),z=!0,P=C+W,d((H=a.alignMemory(P))<q,"TOTAL_MEMORY not big enough for stack"),e.asmGlobalArg={Math:Math,Int8Array:Int8Array,Int16Array:Int16Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Uint16Array:Uint16Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array,NaN:NaN,Infinity:1/0},e.asmLibraryArg={abort:ze,assert:d,H:function(e){switch(e){case 30:return U;case 85:return G/U;case 132:case 133:case 12:case 137:case 138:case 15:case 235:case 16:case 17:case 18:case 19:case 20:case 149:case 13:case 10:case 236:case 153:case 9:case 21:case 22:case 159:case 154:case 14:case 77:case 78:case 139:case 80:case 81:case 82:case 68:case 67:case 164:case 11:case 29:case 47:case 48:case 95:case 52:case 51:case 46:return 200809;case 79:return 0;case 27:case 246:case 127:case 128:case 23:case 24:case 160:case 161:case 181:case 182:case 242:case 183:case 184:case 243:case 244:case 245:case 165:case 178:case 179:case 49:case 50:case 168:case 169:case 175:case 170:case 171:case 172:case 97:case 76:case 32:case 173:case 35:return-1;case 176:case 177:case 7:case 155:case 8:case 157:case 125:case 126:case 92:case 93:case 129:case 130:case 131:case 94:case 91:return 1;case 74:case 60:case 69:case 70:case 4:return 1024;case 31:case 42:case 72:return 32;case 87:case 26:case 33:return 2147483647;case 34:case 1:return 47839;case 38:case 36:return 99;case 43:case 37:return 2048;case 0:return 2097152;case 3:return 65536;case 28:return 32768;case 44:return 32767;case 75:return 16384;case 39:return 1e3;case 89:return 700;case 71:return 256;case 40:return 255;case 2:return 100;case 180:return 64;case 25:return 20;case 5:return 16;case 6:return 6;case 73:return 4;case 84:return"object"==typeof navigator&&navigator.hardwareConcurrency||1}return ke(me.EINVAL),-1},W:function(){return 0},q:function(){e.abort()},___setErrNo:ke,G:be,X:function(e){var i=Date.now()/1e3|0;return e&&(T[e>>2]=i),i},V:Re,K:function(e,i,r){return D.set(D.subarray(i,i+r),e),e},Y:Me,STACKTOP:j,STACK_MAX:P,tempDoublePtr:ve,ABORT:w};var Oe,Ne=function(e,i,r){"use asm";var n=new e.Int8Array(r);var t=new e.Int16Array(r);var f=new e.Int32Array(r);var o=new e.Uint8Array(r);var a=new e.Uint16Array(r);var l=new e.Uint32Array(r);var u=new e.Float32Array(r);var s=new e.Float64Array(r);var c=i.STACKTOP|0;var h=i.STACK_MAX|0;var w=i.tempDoublePtr|0;var d=i.ABORT|0;var v=e.NaN,b=e.Infinity;var k=0;var m=e.Math.floor;var y=e.Math.abs;var p=e.Math.sqrt;var E=e.Math.pow;var g=e.Math.cos;var A=e.Math.sin;var S=e.Math.tan;var _=e.Math.acos;var R=e.Math.asin;var M=e.Math.atan;var D=e.Math.atan2;var O=e.Math.exp;var N=e.Math.log;var T=e.Math.ceil;var I=e.Math.imul;var L=e.Math.min;var F=e.Math.clz32;var C=i.abort;var P=i.assert;var B=i.H;var U=i.W;var x=i.q;var z=i.___setErrNo;var j=i.G;var H=i.X;var W=i.V;var q=i.K;var G=i.Y;function X(e){e=e|0;var i=0;i=c;c=c+e|0;c=c+15&-16;return i|0}function V(){return c|0}function K(e){e=e|0;c=e}function Y(e,i){e=e|0;i=i|0;c=e;h=i}function Q(e,i){e=e|0;i=i|0}function Z(e){e=e|0;k=e}function $(){return k|0}function J(){var e=0,i=0;i=c;c=c+16|0;e=i;f[e>>2]=0;ai(e,31756)|0;c=i;return f[e>>2]|0}function ee(e){e=e|0;var i=0,r=0;i=c;c=c+16|0;r=i;f[r>>2]=e;li(r);c=i;return}function ie(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;le(e,(n|0)==0?(o[i>>0]|0)>>>3&15:15,i+1|0,r,2)|0;return}function re(e){e=e|0;var i=0;i=dt(8)|0;ci(i,i+4|0,e)|0;return i|0}function ne(e){e=e|0;hi(e,e+4|0);vt(e);return}function te(e,i,r,t,a){e=e|0;i=i|0;r=r|0;t=t|0;a=a|0;var l=0;a=c;c=c+16|0;l=a;f[l>>2]=i;r=(wi(f[e>>2]|0,f[e+4>>2]|0,i,r,t,l,3)|0)<<16>>16;n[t>>0]=o[t>>0]|0|4;c=a;return r|0}function fe(e){e=e|0;if(!e)e=-1;else{t[e>>1]=4096;e=0}return e|0}function oe(e,i,r,n,o,a){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;var l=0,u=0,s=0,c=0,h=0,w=0,d=0,v=0,b=0;h=f[a>>2]|0;b=o<<16>>16>0;if(b){l=0;u=0;do{c=t[r+(l<<1)>>1]|0;c=I(c,c)|0;if((c|0)!=1073741824){s=(c<<1)+u|0;if((c^u|0)>0&(s^u|0)<0){f[a>>2]=1;u=(u>>>31)+2147483647|0}else u=s}else{f[a>>2]=1;u=2147483647}l=l+1|0}while((l&65535)<<16>>16!=o<<16>>16);if((u|0)==2147483647){f[a>>2]=h;c=0;s=0;do{u=t[r+(c<<1)>>1]>>2;u=I(u,u)|0;if((u|0)!=1073741824){l=(u<<1)+s|0;if((u^s|0)>0&(l^s|0)<0){f[a>>2]=1;s=(s>>>31)+2147483647|0}else s=l}else{f[a>>2]=1;s=2147483647}c=c+1|0}while((c&65535)<<16>>16!=o<<16>>16)}else v=8}else{u=0;v=8}if((v|0)==8)s=u>>4;if(!s){t[e>>1]=0;return}d=((Kn(s)|0)&65535)+65535|0;u=d<<16>>16;if((d&65535)<<16>>16>0){l=s<<u;if((l>>u|0)==(s|0))s=l;else s=s>>31^2147483647}else{u=0-u<<16;if((u|0)<2031616)s=s>>(u>>16);else s=0}w=at(s,a)|0;l=f[a>>2]|0;if(b){u=0;s=0;do{h=t[i+(u<<1)>>1]|0;h=I(h,h)|0;if((h|0)!=1073741824){c=(h<<1)+s|0;if((h^s|0)>0&(c^s|0)<0){f[a>>2]=1;s=(s>>>31)+2147483647|0}else s=c}else{f[a>>2]=1;s=2147483647}u=u+1|0}while((u&65535)<<16>>16!=o<<16>>16);if((s|0)==2147483647){f[a>>2]=l;h=0;s=0;do{c=t[i+(h<<1)>>1]>>2;c=I(c,c)|0;if((c|0)!=1073741824){u=(c<<1)+s|0;if((c^s|0)>0&(u^s|0)<0){f[a>>2]=1;s=(s>>>31)+2147483647|0}else s=u}else{f[a>>2]=1;s=2147483647}h=h+1|0}while((h&65535)<<16>>16!=o<<16>>16)}else v=29}else{s=0;v=29}if((v|0)==29)s=s>>4;if(!s)c=0;else{u=(Kn(s)|0)<<16>>16;l=d-u|0;c=l&65535;s=(Sn(w,at(s<<u,a)|0)|0)<<16>>16;u=s<<7;l=l<<16>>16;if(c<<16>>16>0)l=c<<16>>16<31?u>>l:0;else{v=0-l<<16>>16;l=u<<v;l=(l>>v|0)==(u|0)?l:s>>24^2147483647}c=(I(((Cn(l,a)|0)<<9)+32768>>16,32767-(n&65535)<<16>>16)|0)>>>15<<16>>16}l=t[e>>1]|0;if(b){s=n<<16>>16;u=0;while(1){n=((I(l<<16>>16,s)|0)>>>15&65535)+c|0;l=n&65535;t[r>>1]=(I(t[r>>1]|0,n<<16>>16)|0)>>>12;u=u+1<<16>>16;if(u<<16>>16>=o<<16>>16)break;else r=r+2|0}}t[e>>1]=l;return}function ae(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var o=0,a=0,l=0,u=0,s=0,c=0,h=0,w=0,d=0;l=f[n>>2]|0;o=r<<16>>16>0;if(o){u=0;a=0;do{c=t[i+(u<<1)>>1]|0;c=I(c,c)|0;if((c|0)!=1073741824){s=(c<<1)+a|0;if((c^a|0)>0&(s^a|0)<0){f[n>>2]=1;a=(a>>>31)+2147483647|0}else a=s}else{f[n>>2]=1;a=2147483647}u=u+1|0}while((u&65535)<<16>>16!=r<<16>>16);if((a|0)==2147483647){f[n>>2]=l;c=0;l=0;do{s=t[i+(c<<1)>>1]>>2;s=I(s,s)|0;if((s|0)!=1073741824){u=(s<<1)+l|0;if((s^l|0)>0&(u^l|0)<0){f[n>>2]=1;l=(l>>>31)+2147483647|0}else l=u}else{f[n>>2]=1;l=2147483647}c=c+1|0}while((c&65535)<<16>>16!=r<<16>>16)}else d=8}else{a=0;d=8}if((d|0)==8)l=a>>4;if(!l)return;w=((Kn(l)|0)&65535)+65535|0;s=w<<16>>16;if((w&65535)<<16>>16>0){u=l<<s;if((u>>s|0)==(l|0))l=u;else l=l>>31^2147483647}else{s=0-s<<16;if((s|0)<2031616)l=l>>(s>>16);else l=0}h=at(l,n)|0;l=f[n>>2]|0;if(o){u=0;a=0;do{c=t[e+(u<<1)>>1]|0;c=I(c,c)|0;if((c|0)!=1073741824){s=(c<<1)+a|0;if((c^a|0)>0&(s^a|0)<0){f[n>>2]=1;a=(a>>>31)+2147483647|0}else a=s}else{f[n>>2]=1;a=2147483647}u=u+1|0}while((u&65535)<<16>>16!=r<<16>>16);if((a|0)==2147483647){f[n>>2]=l;l=0;u=0;do{c=t[e+(l<<1)>>1]>>2;c=I(c,c)|0;if((c|0)!=1073741824){s=(c<<1)+u|0;if((c^u|0)>0&(s^u|0)<0){f[n>>2]=1;u=(u>>>31)+2147483647|0}else u=s}else{f[n>>2]=1;u=2147483647}l=l+1|0}while((l&65535)<<16>>16!=r<<16>>16)}else d=28}else{a=0;d=28}if((d|0)==28)u=a>>4;if(!u)o=0;else{c=Kn(u)|0;s=c<<16>>16;if(c<<16>>16>0){l=u<<s;if((l>>s|0)==(u|0))u=l;else u=u>>31^2147483647}else{s=0-s<<16;if((s|0)<2031616)u=u>>(s>>16);else u=0}l=w-(c&65535)|0;s=l&65535;a=(Sn(h,at(u,n)|0)|0)<<16>>16;o=a<<7;l=l<<16>>16;if(s<<16>>16>0)o=s<<16>>16<31?o>>l:0;else{w=0-l<<16>>16;e=o<<w;o=(e>>w|0)==(o|0)?e:a>>24^2147483647}o=Cn(o,n)|0;if((o|0)>4194303)o=2147483647;else o=(o|0)<-4194304?-2147483648:o<<9;o=at(o,n)|0}a=(r&65535)+65535&65535;if(a<<16>>16<=-1)return;c=o<<16>>16;s=r+-1<<16>>16<<16>>16;while(1){l=i+(s<<1)|0;o=I(t[l>>1]|0,c)|0;do{if((o|0)!=1073741824){u=o<<1;if((u|0)<=268435455)if((u|0)<-268435456){t[l>>1]=-32768;break}else{t[l>>1]=o>>>12;break}else d=52}else{f[n>>2]=1;d=52}}while(0);if((d|0)==52){d=0;t[l>>1]=32767}a=a+-1<<16>>16;if(a<<16>>16<=-1)break;else s=s+-1|0}return}function le(e,i,r,n,o){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;var a=0,l=0,u=0,s=0;s=c;c=c+496|0;u=s;l=(o|0)==2;do{if(!(l&1|(o|0)==4)){if(o){e=-1;c=s;return e|0}l=t[r>>1]|0;i=r+490|0;o=r+2|0;a=0;while(1){t[u+(a<<1)>>1]=t[o>>1]|0;a=a+1|0;if((a|0)==244)break;else o=o+2|0}a=l<<16>>16;if(l<<16>>16==7){o=492;i=f[e+1760>>2]|0;break}else{o=492;i=t[i>>1]|0;break}}else{a=e+1168|0;if(l){si(i,r,u,a);a=604}else{Ve(i,r,u,a);a=3436}o=t[a+(i<<1)>>1]|0;do{if(i>>>0>=8){if((i|0)==8){i=t[u+76>>1]<<2|(t[u+74>>1]<<1|t[u+72>>1]);a=(t[u+70>>1]|0)==0?4:5;break}if(i>>>0<15){e=-1;c=s;return e|0}else{i=f[e+1760>>2]|0;a=7;break}}else a=0}while(0);if(o<<16>>16==-1){e=-1;c=s;return e|0}}}while(0);ui(e,i,u,a,n);f[e+1760>>2]=i;e=o;c=s;return e|0}function ue(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,o=0,l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0;y=c;c=c+48|0;k=y+20|0;m=y;o=k;n=o+20|0;do{t[o>>1]=t[e>>1]|0;o=o+2|0;e=e+2|0}while((o|0)<(n|0));e=t[k+18>>1]|0;b=(e&65535)-((e&65535)>>>15&65535)|0;e:do{if(((b<<16>>31^b)&65535)<<16>>16<=4095){n=9;b=9;while(1){e=e<<16>>16;e=(e<<19>>19|0)==(e|0)?e<<3:e>>>15^32767;v=i+(n<<1)|0;t[v>>1]=e;e=e<<16>>16;e=I(e,e)|0;if((e|0)==1073741824){f[r>>2]=1;o=2147483647}else o=e<<1;e=2147483647-o|0;if((e&o|0)<0){f[r>>2]=1;e=2147483647}w=Kn(e)|0;d=15-(w&65535)&65535;l=w<<16>>16;if(w<<16>>16>0){o=e<<l;if((o>>l|0)!=(e|0))o=e>>31^2147483647}else{o=0-l<<16;if((o|0)<2031616)o=e>>(o>>16);else o=0}o=Sn(16384,at(o,r)|0)|0;do{if(b<<16>>16>0){w=n+-1|0;u=o<<16>>16;s=b<<16>>16;h=0;while(1){n=a[k+(h<<1)>>1]|0;e=n<<16;l=I(t[k+(w-h<<1)>>1]|0,t[v>>1]|0)|0;if((l|0)==1073741824){f[r>>2]=1;o=2147483647}else o=l<<1;l=e-o|0;if(((l^e)&(o^e)|0)<0){f[r>>2]=1;l=(n>>>15)+2147483647|0}l=I((at(l,r)|0)<<16>>16,u)|0;if((l|0)==1073741824){f[r>>2]=1;l=2147483647}else l=l<<1;l=xn(l,d,r)|0;o=l-(l>>>31)|0;if((o>>31^o|0)>32767){l=24;break}t[m+(h<<1)>>1]=l;h=h+1|0;if((s|0)<=(h|0)){l=26;break}}if((l|0)==24){l=0;o=i;n=o+20|0;do{t[o>>1]=0;o=o+2|0}while((o|0)<(n|0));e=10}else if((l|0)==26){l=0;if(b<<16>>16>0)e=b;else{l=28;break}}o=e+-1<<16>>16;yt(k|0,m|0,((o&65535)<<1)+2|0)|0;n=o<<16>>16}else l=28}while(0);if((l|0)==28){e=b+-1<<16>>16;if(e<<16>>16>-1){n=e<<16>>16;o=32767}else break}e=t[k+(n<<1)>>1]|0;b=(e&65535)-((e&65535)>>>15&65535)|0;if(((b<<16>>31^b)&65535)<<16>>16>4095)break e;else b=o}c=y;return}}while(0);o=i;n=o+20|0;do{t[o>>1]=0;o=o+2|0}while((o|0)<(n|0));c=y;return}function se(e,i){e=e|0;i=i|0;var r=0,n=0,t=0,o=0,a=0;if(i<<16>>16<=0){e=0;return e|0}n=f[e>>2]|0;t=0;r=0;do{a=n&1;r=a|r<<1&131070;o=n>>1;n=(a|0)==(n>>>28&1|0)?o:o|1073741824;t=t+1<<16>>16}while(t<<16>>16<i<<16>>16);f[e>>2]=n;a=r&65535;return a|0}function ce(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,o=0,a=0,l=0,u=0,s=0;o=i;n=o+80|0;do{t[o>>1]=0;o=o+2|0}while((o|0)<(n|0));n=0;o=f[e>>2]|0;do{s=o&1;u=o>>1;u=(s|0)==(o>>>28&1|0)?u:u|1073741824;a=u&1;l=u>>1;f[e>>2]=(a|0)==(u>>>28&1|0)?l:l|1073741824;a=gn((I(s<<1|a,1310720)|0)>>>17&65535,n,r)|0;s=f[e>>2]|0;l=s&1;u=s>>1;o=(l|0)==(s>>>28&1|0)?u:u|1073741824;f[e>>2]=o;t[i+(a<<16>>16<<1)>>1]=((l&65535)<<13&65535)+-4096<<16>>16;n=n+1<<16>>16}while(n<<16>>16<10);return}function he(e,i,r,n,o,l){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;l=l|0;var u=0,s=0;u=t[e>>1]|0;if((u*31821|0)==1073741824){f[l>>2]=1;s=1073741823}else s=u*63642>>1;u=s+13849|0;if((s|0)>-1&(u^s|0)<0){f[l>>2]=1;u=(s>>>31)+2147483647|0}t[e>>1]=u;if(i<<16>>16<=0)return;s=0;u=o+((u&127)<<1)|0;while(1){t[n+(s<<1)>>1]=(-65536<<t[r+(s<<1)>>1]>>>16^65535)&a[u>>1];s=s+1|0;if((s&65535)<<16>>16==i<<16>>16)break;else u=u+2|0}return}function we(e){e=e|0;var i=0;if(!e){i=-1;return i|0}i=e+122|0;do{t[e>>1]=0;e=e+2|0}while((e|0)<(i|0));i=0;return i|0}function de(e,i,r,n,o){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;var l=0,u=0,s=0,c=0,h=0,w=0,d=0;s=159;u=0;while(1){h=t[r+(s<<1)>>1]|0;h=I(h,h)|0;h=(h|0)==1073741824?2147483647:h<<1;l=h+u|0;if((h^u|0)>-1&(l^u|0)<0){f[o>>2]=1;u=(u>>>31)+2147483647|0}else u=l;if((s|0)>0)s=s+-1|0;else{s=u;break}}o=s>>>14&65535;u=32767;l=59;while(1){h=t[e+(l<<1)>>1]|0;u=h<<16>>16<u<<16>>16?h:u;if((l|0)>0)l=l+-1|0;else break}h=(s|0)>536870911?32767:o;o=u<<16>>16;l=o<<20>>16;s=u<<16>>16>0?32767:-32768;r=55;u=t[e>>1]|0;while(1){c=t[e+(r<<1)>>1]|0;u=u<<16>>16<c<<16>>16?c:u;if((r|0)>1)r=r+-1|0;else break}r=t[e+80>>1]|0;c=t[e+82>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=t[e+84>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=t[e+86>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=t[e+88>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=t[e+90>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=t[e+92>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=t[e+94>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=t[e+96>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=t[e+98>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=t[e+100>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=t[e+102>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=t[e+104>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=t[e+106>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=t[e+108>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=t[e+110>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=t[e+112>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=t[e+114>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=t[e+116>>1]|0;r=r<<16>>16<c<<16>>16?c:r;c=e+118|0;d=t[c>>1]|0;do{if((h+-21&65535)<17557&u<<16>>16>20?(h<<16>>16|0)<(((o<<4|0)==(l|0)?l:s)|0)?1:(r<<16>>16<d<<16>>16?d:r)<<16>>16<1953:0){u=e+120|0;l=t[u>>1]|0;if(l<<16>>16>29){t[u>>1]=30;r=u;s=1;break}else{s=(l&65535)+1&65535;t[u>>1]=s;r=u;s=s<<16>>16>1&1;break}}else w=14}while(0);if((w|0)==14){r=e+120|0;t[r>>1]=0;s=0}u=0;do{d=u;u=u+1|0;t[e+(d<<1)>>1]=t[e+(u<<1)>>1]|0}while((u|0)!=59);t[c>>1]=h;u=t[r>>1]|0;u=u<<16>>16>15?16383:u<<16>>16>8?15565:13926;l=Nn(i+8|0,5)|0;if((t[r>>1]|0)>20){if(((Nn(i,9)|0)<<16>>16|0)>(u|0))w=20}else if((l<<16>>16|0)>(u|0))w=20;if((w|0)==20){t[n>>1]=0;return s|0}l=(a[n>>1]|0)+1&65535;if(l<<16>>16>10){t[n>>1]=10;return s|0}else{t[n>>1]=l;return s|0}return 0}function ve(e){e=e|0;var i=0;if(!e){i=-1;return i|0}i=e+18|0;do{t[e>>1]=0;e=e+2|0}while((e|0)<(i|0));i=0;return i|0}function be(e,i,r,n,o,l,u,s,c,h,w,d){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;l=l|0;u=u|0;s=s|0;c=c|0;h=h|0;w=w|0;d=d|0;var v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0;S=e+2|0;t[e>>1]=t[S>>1]|0;_=e+4|0;t[S>>1]=t[_>>1]|0;R=e+6|0;t[_>>1]=t[R>>1]|0;M=e+8|0;t[R>>1]=t[M>>1]|0;D=e+10|0;t[M>>1]=t[D>>1]|0;O=e+12|0;t[D>>1]=t[O>>1]|0;t[O>>1]=r;y=0;A=0;do{v=o+(A<<1)|0;k=ct(t[v>>1]|0,t[n+(A<<1)>>1]|0,d)|0;k=(k&65535)-((k&65535)>>>15&65535)|0;k=k<<16>>31^k;g=((Yn(k&65535)|0)&65535)+65535|0;b=g<<16>>16;if((g&65535)<<16>>16<0){m=0-b<<16;if((m|0)<983040)p=k<<16>>16>>(m>>16)&65535;else p=0}else{m=k<<16>>16;k=m<<b;if((k<<16>>16>>b|0)==(m|0))p=k&65535;else p=(m>>>15^32767)&65535}E=Yn(t[v>>1]|0)|0;k=t[v>>1]|0;b=E<<16>>16;if(E<<16>>16<0){m=0-b<<16;if((m|0)<983040)m=k<<16>>16>>(m>>16)&65535;else m=0}else{m=k<<16>>16;k=m<<b;if((k<<16>>16>>b|0)==(m|0))m=k&65535;else m=(m>>>15^32767)&65535}b=Sn(p,m)|0;m=(g&65535)+2-(E&65535)|0;k=m&65535;do{if(m&32768){if(k<<16>>16!=-32768){g=0-m|0;m=g<<16>>16;if((g&65535)<<16>>16<0){m=0-m<<16;if((m|0)>=983040){m=0;break}m=b<<16>>16>>(m>>16)&65535;break}}else m=32767;k=b<<16>>16;b=k<<m;if((b<<16>>16>>m|0)==(k|0))m=b&65535;else m=(k>>>15^32767)&65535}else m=lt(b,k,d)|0}while(0);y=gn(y,m,d)|0;A=A+1|0}while((A|0)!=10);m=y&65535;k=y<<16>>16>5325;y=e+14|0;if(k){o=(a[y>>1]|0)+1&65535;t[y>>1]=o;if(o<<16>>16>10)t[e+16>>1]=0}else t[y>>1]=0;switch(i|0){case 0:case 1:case 2:case 3:case 6:break;default:{O=e+16|0;d=r;r=t[O>>1]|0;r=r&65535;r=r+1|0;r=r&65535;t[O>>1]=r;return d|0}}p=(u|l)<<16>>16==0;E=h<<16>>16==0;g=i>>>0<3;y=m+(g&((E|(p&(s<<16>>16==0|c<<16>>16==0)|w<<16>>16<2))^1)?61030:62259)&65535;y=y<<16>>16>0?y:0;if(y<<16>>16<=2048){y=y<<16>>16;if((y<<18>>18|0)==(y|0))c=y<<2;else c=y>>>15^32767}else c=8192;s=e+16|0;w=k|(t[s>>1]|0)<40;y=t[_>>1]|0;if((y*6554|0)==1073741824){f[d>>2]=1;k=2147483647}else k=y*13108|0;y=t[R>>1]|0;m=y*6554|0;if((m|0)!=1073741824){y=(y*13108|0)+k|0;if((m^k|0)>0&(y^k|0)<0){f[d>>2]=1;y=(k>>>31)+2147483647|0}}else{f[d>>2]=1;y=2147483647}m=t[M>>1]|0;k=m*6554|0;if((k|0)!=1073741824){m=(m*13108|0)+y|0;if((k^y|0)>0&(m^y|0)<0){f[d>>2]=1;m=(y>>>31)+2147483647|0}}else{f[d>>2]=1;m=2147483647}y=t[D>>1]|0;k=y*6554|0;if((k|0)!=1073741824){y=(y*13108|0)+m|0;if((k^m|0)>0&(y^m|0)<0){f[d>>2]=1;k=(m>>>31)+2147483647|0}else k=y}else{f[d>>2]=1;k=2147483647}y=t[O>>1]|0;m=y*6554|0;if((m|0)!=1073741824){y=(y*13108|0)+k|0;if((m^k|0)>0&(y^k|0)<0){f[d>>2]=1;y=(k>>>31)+2147483647|0}}else{f[d>>2]=1;y=2147483647}k=at(y,d)|0;if(g&((p|E)^1)){y=t[e>>1]|0;if((y*4681|0)==1073741824){f[d>>2]=1;k=2147483647}else k=y*9362|0;y=t[S>>1]|0;m=y*4681|0;if((m|0)!=1073741824){y=(y*9362|0)+k|0;if((m^k|0)>0&(y^k|0)<0){f[d>>2]=1;k=(k>>>31)+2147483647|0}else k=y}else{f[d>>2]=1;k=2147483647}y=t[_>>1]|0;m=y*4681|0;if((m|0)!=1073741824){y=(y*9362|0)+k|0;if((m^k|0)>0&(y^k|0)<0){f[d>>2]=1;k=(k>>>31)+2147483647|0}else k=y}else{f[d>>2]=1;k=2147483647}y=t[R>>1]|0;m=y*4681|0;if((m|0)!=1073741824){y=(y*9362|0)+k|0;if((m^k|0)>0&(y^k|0)<0){f[d>>2]=1;y=(k>>>31)+2147483647|0}}else{f[d>>2]=1;y=2147483647}m=t[M>>1]|0;k=m*4681|0;if((k|0)!=1073741824){m=(m*9362|0)+y|0;if((k^y|0)>0&(m^y|0)<0){f[d>>2]=1;y=(y>>>31)+2147483647|0}else y=m}else{f[d>>2]=1;y=2147483647}m=t[D>>1]|0;k=m*4681|0;if((k|0)!=1073741824){m=(m*9362|0)+y|0;if((k^y|0)>0&(m^y|0)<0){f[d>>2]=1;m=(y>>>31)+2147483647|0}}else{f[d>>2]=1;m=2147483647}k=t[O>>1]|0;v=k*4681|0;if((v|0)!=1073741824){b=(k*9362|0)+m|0;if((v^m|0)>0&(b^m|0)<0){f[d>>2]=1;b=(m>>>31)+2147483647|0}}else{f[d>>2]=1;b=2147483647}k=at(b,d)|0}y=w?8192:c<<16>>16;v=I(y,r<<16>>16)|0;if((v|0)==1073741824){f[d>>2]=1;m=2147483647}else m=v<<1;k=k<<16>>16;b=k<<13;if((b|0)!=1073741824){v=m+(k<<14)|0;if((m^b|0)>0&(v^m|0)<0){f[d>>2]=1;m=(m>>>31)+2147483647|0}else m=v}else{f[d>>2]=1;m=2147483647}v=I(k,y)|0;if((v|0)==1073741824){f[d>>2]=1;b=2147483647}else b=v<<1;v=m-b|0;if(((v^m)&(b^m)|0)<0){f[d>>2]=1;v=(m>>>31)+2147483647|0}O=v<<2;r=s;d=at((O>>2|0)==(v|0)?O:v>>31^2147483647,d)|0;O=t[r>>1]|0;O=O&65535;O=O+1|0;O=O&65535;t[r>>1]=O;return d|0}function ke(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,f=0,o=0,l=0;n=i;f=n+80|0;do{t[n>>1]=0;n=n+2|0}while((n|0)<(f|0));n=0;do{l=t[e+(n<<1)>>1]|0;f=((l&8)<<10&65535^8192)+-4096<<16>>16;o=n<<16;l=((t[r+((l&7)<<1)>>1]|0)*327680|0)+o>>16;t[i+(l<<1)>>1]=f;o=((t[r+((a[e+(n+5<<1)>>1]&7)<<1)>>1]|0)*327680|0)+o>>16;if((o|0)<(l|0))f=0-(f&65535)&65535;l=i+(o<<1)|0;t[l>>1]=(a[l>>1]|0)+(f&65535);n=n+1|0}while((n|0)!=5);return}function me(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,f=0,o=0;f=i<<16>>16;n=(f<<1&2|1)+((f>>>1&7)*5|0)|0;i=f>>>4&3;i=((f>>>6&7)*5|0)+((i|0)==3?4:i)|0;f=r;o=f+80|0;do{t[f>>1]=0;f=f+2|0}while((f|0)<(o|0));e=e<<16>>16;t[r+(n<<1)>>1]=(0-(e&1)&16383)+57344;t[r+(i<<1)>>1]=(0-(e>>>1&1)&16383)+57344;return}function ye(e,i,r,n,f,o){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;o=o|0;var l=0,u=0;o=r<<16>>16;u=o>>>3;e=e<<16>>16;e=((e<<17>>17|0)==(e|0)?e<<1:e>>>15^32767)+(u&8)<<16;u=(a[n+(e+65536>>16<<1)>>1]|0)+((u&7)*5|0)|0;r=i<<16>>16;l=(0-(r&1)&16383)+57344&65535;e=f+((a[n+(e>>16<<1)>>1]|0)+((o&7)*5|0)<<16>>16<<1)|0;i=f;o=i+80|0;do{t[i>>1]=0;i=i+2|0}while((i|0)<(o|0));t[e>>1]=l;t[f+(u<<16>>16<<1)>>1]=(0-(r>>>1&1)&16383)+57344;return}function pe(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,f=0,o=0,a=0;i=i<<16>>16;n=(i&7)*5|0;f=(i>>>2&2|1)+((i>>>4&7)*5|0)|0;i=(i>>>6&2)+2+((i>>>8&7)*5|0)|0;o=r;a=o+80|0;do{t[o>>1]=0;o=o+2|0}while((o|0)<(a|0));e=e<<16>>16;t[r+(n<<1)>>1]=(0-(e&1)&16383)+57344;t[r+(f<<1)>>1]=(0-(e>>>1&1)&16383)+57344;t[r+(i<<1)>>1]=(0-(e>>>2&1)&16383)+57344;return}function Ee(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var f=0,o=0,a=0,l=0;i=i<<16>>16;a=t[r+((i&7)<<1)>>1]|0;l=t[r+((i>>>3&7)<<1)>>1]|0;o=t[r+((i>>>6&7)<<1)>>1]|0;r=(i>>>9&1)+3+((t[r+((i>>>10&7)<<1)>>1]|0)*5|0)|0;i=n;f=i+80|0;do{t[i>>1]=0;i=i+2|0}while((i|0)<(f|0));e=e<<16>>16;t[n+(a*327680>>16<<1)>>1]=(0-(e&1)&16383)+57344;t[n+((l*327680|0)+65536>>16<<1)>>1]=(0-(e>>>1&1)&16383)+57344;t[n+((o*327680|0)+131072>>16<<1)>>1]=(0-(e>>>2&1)&16383)+57344;t[n+(r<<16>>16<<1)>>1]=(0-(e>>>3&1)&16383)+57344;return}function ge(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,o=0,l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0;b=c;c=c+32|0;v=b+16|0;d=b;l=i;o=l+80|0;do{t[l>>1]=0;l=l+2|0}while((l|0)<(o|0));o=t[e>>1]|0;t[v>>1]=o;t[v+2>>1]=t[e+2>>1]|0;t[v+4>>1]=t[e+4>>1]|0;t[v+6>>1]=t[e+6>>1]|0;h=t[e+8>>1]|0;Ae(h>>>3&65535,h&7,0,4,1,d,r);h=t[e+10>>1]|0;Ae(h>>>3&65535,h&7,2,6,5,d,r);h=t[e+12>>1]|0;n=h>>2;do{if((n*25|0)!=1073741824){l=(I(n,1638400)|0)+786432>>21;n=l*6554>>15;if((n|0)>32767){f[r>>2]=1;u=1;s=1;e=163835;w=6;break}e=(n<<16>>16)*5|0;u=n&1;if((e|0)==1073741824){f[r>>2]=1;s=0;e=65535}else{s=0;w=6}}else{f[r>>2]=1;u=0;n=0;s=0;l=0;e=0;w=6}}while(0);if((w|0)==6)e=e&65535;w=l-e|0;u=u<<16>>16==0?w:4-w|0;w=u<<16>>16;t[d+6>>1]=gn(((u<<17>>17|0)==(w|0)?u<<1:w>>>15^32767)&65535,h&1,r)|0;if(s){f[r>>2]=1;n=32767}w=n<<16>>16;t[d+14>>1]=((n<<17>>17|0)==(w|0)?n<<1:w>>>15^32767)+(h>>>1&1);n=0;while(1){o=o<<16>>16==0?8191:-8191;w=(t[d+(n<<1)>>1]<<2)+n<<16;l=w>>16;if((w|0)<2621440)t[i+(l<<1)>>1]=o;u=(t[d+(n+4<<1)>>1]<<2)+n<<16;e=u>>16;if((e|0)<(l|0))o=0-(o&65535)&65535;if((u|0)<2621440){w=i+(e<<1)|0;t[w>>1]=(a[w>>1]|0)+(o&65535)}n=n+1|0;if((n|0)==4)break;o=t[v+(n<<1)>>1]|0}c=b;return}function Ae(e,i,r,n,o,a,l){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;l=l|0;var u=0,s=0,c=0,h=0,w=0,d=0,v=0;s=e<<16>>16>124?124:e;e=(s<<16>>16)*1311>>15;v=(e|0)>32767;if(!v){u=e<<16>>16;if((u*25|0)==1073741824){f[l>>2]=1;u=1073741823}else d=4}else{f[l>>2]=1;u=32767;d=4}if((d|0)==4)u=(u*50|0)>>>1;h=(s&65535)-u|0;u=(h<<16>>16)*6554>>15;w=(u|0)>32767;if(!w){s=u<<16>>16;if((s*5|0)==1073741824){f[l>>2]=1;c=1073741823}else d=9}else{f[l>>2]=1;s=32767;d=9}if((d|0)==9)c=(s*10|0)>>>1;h=h-c|0;d=h<<16>>16;s=i<<16>>16;c=s>>2;s=s-(c<<2)|0;t[a+(r<<16>>16<<1)>>1]=((h<<17>>17|0)==(d|0)?h<<1:d>>>15^32767)+(s&1);if(w){f[l>>2]=1;u=32767}r=u<<16>>16;t[a+(n<<16>>16<<1)>>1]=((u<<17>>17|0)==(r|0)?u<<1:r>>>15^32767)+(s<<16>>17);if(v){f[l>>2]=1;e=32767}n=e<<16>>16;t[a+(o<<16>>16<<1)>>1]=gn(c&65535,((e<<17>>17|0)==(n|0)?e<<1:n>>>15^32767)&65535,l)|0;return}function Se(e){e=e|0;var i=0,r=0,n=0,o=0;if(!e){o=-1;return o|0}On(e+1168|0);t[e+460>>1]=40;f[e+1164>>2]=0;i=e+646|0;r=e+1216|0;n=e+462|0;o=n+22|0;do{t[n>>1]=0;n=n+2|0}while((n|0)<(o|0));Ce(i,f[r>>2]|0)|0;qe(e+686|0)|0;ze(e+700|0)|0;ve(e+608|0)|0;Ye(e+626|0,f[r>>2]|0)|0;we(e+484|0)|0;Ze(e+730|0)|0;Pe(e+748|0)|0;_n(e+714|0)|0;_e(e,0)|0;o=0;return o|0}function _e(e,i){e=e|0;i=i|0;var r=0,n=0;if(!e){e=-1;return e|0}f[e+388>>2]=e+308;Et(e|0,0,308)|0;i=(i|0)!=8;if(i){r=e+412|0;n=r+20|0;do{t[r>>1]=0;r=r+2|0}while((r|0)<(n|0));t[e+392>>1]=3e4;t[e+394>>1]=26e3;t[e+396>>1]=21e3;t[e+398>>1]=15e3;t[e+400>>1]=8e3;t[e+402>>1]=0;t[e+404>>1]=-8e3;t[e+406>>1]=-15e3;t[e+408>>1]=-21e3;t[e+410>>1]=-26e3}t[e+432>>1]=0;t[e+434>>1]=40;f[e+1164>>2]=0;t[e+436>>1]=0;t[e+438>>1]=0;t[e+440>>1]=0;t[e+460>>1]=40;t[e+462>>1]=0;t[e+464>>1]=0;if(i){r=e+442|0;n=r+18|0;do{t[r>>1]=0;r=r+2|0}while((r|0)<(n|0));r=e+466|0;n=r+18|0;do{t[r>>1]=0;r=r+2|0}while((r|0)<(n|0));ve(e+608|0)|0;n=e+1216|0;Ye(e+626|0,f[n>>2]|0)|0;Ce(e+646|0,f[n>>2]|0)|0;qe(e+686|0)|0;ze(e+700|0)|0;_n(e+714|0)|0}else{r=e+466|0;n=r+18|0;do{t[r>>1]=0;r=r+2|0}while((r|0)<(n|0));ve(e+608|0)|0;Ce(e+646|0,f[e+1216>>2]|0)|0;qe(e+686|0)|0;ze(e+700|0)|0}we(e+484|0)|0;t[e+606>>1]=21845;Ze(e+730|0)|0;if(!i){e=0;return e|0}Pe(e+748|0)|0;e=0;return e|0}function Re(e,i,r,o,l,u){e=e|0;i=i|0;r=r|0;o=o|0;l=l|0;u=u|0;var s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,L=0,F=0,C=0,P=0,B=0,U=0,x=0,z=0,j=0,H=0,W=0,q=0,G=0,X=0,V=0,K=0,Y=0,Q=0,Z=0,$=0,J=0,ee=0,ie=0,re=0,ne=0,te=0,fe=0,oe=0,le=0,ue=0,se=0,ce=0,we=0,ve=0,Ae=0,Se=0,Re=0,Le=0,Ce=0,Pe=0,ze=0,qe=0,Ve=0,Ye=0,Ze=0,ii=0,ri=0,ni=0,ti=0,fi=0,oi=0,ai=0,li=0,ui=0,si=0,ci=0,hi=0,wi=0,di=0,vi=0,bi=0,ki=0,mi=0,yi=0,pi=0,Ei=0,gi=0,Ai=0,Si=0,_i=0,Ri=0,Mi=0;Mi=c;c=c+336|0;k=Mi+236|0;b=Mi+216|0;_i=Mi+112|0;Si=Mi+12|0;yi=Mi+256|0;Ei=Mi+136|0;pi=Mi+32|0;ki=Mi+8|0;mi=Mi+6|0;Ai=Mi+4|0;gi=Mi+2|0;Ri=Mi;hi=e+1164|0;wi=e+748|0;di=xe(wi,o,hi)|0;if(di){_e(e,8)|0;Be(wi,e+412|0,e+646|0,e+714|0,e+608|0,di,i,r,e+1168|0,l,u,hi);Ri=e+666|0;Gn(Ri,e+392|0,10,hi);Qe(e+626|0,Ri,hi);Ri=e+1156|0;f[Ri>>2]=di;c=Mi;return}switch(o|0){case 1:{s=1;A=6;break}case 2:case 7:{he(e+606|0,t[(f[e+1256>>2]|0)+(i<<1)>>1]|0,f[(f[e+1260>>2]|0)+(i<<2)>>2]|0,r,f[e+1276>>2]|0,hi);A=9;break}case 3:{A=9;break}default:{s=0;A=6}}do{if((A|0)==6){o=e+440|0;if((t[o>>1]|0)==6){t[o>>1]=5;si=0;ci=0;break}else{t[o>>1]=0;si=0;ci=0;break}}else if((A|0)==9){o=e+440|0;si=(a[o>>1]|0)+1&65535;t[o>>1]=si<<16>>16>6?6:si;si=1;ci=1;s=0}}while(0);oi=e+1156|0;switch(f[oi>>2]|0){case 1:{t[o>>1]=5;t[e+436>>1]=0;break}case 2:{t[o>>1]=5;t[e+436>>1]=1;break}default:}w=e+646|0;ai=e+666|0;h=_i;d=ai;v=h+20|0;do{n[h>>0]=n[d>>0]|0;h=h+1|0;d=d+1|0}while((h|0)<(v|0));li=(i|0)!=7;ui=e+1168|0;if(li){Ie(w,i,ci,r,ui,k,hi);h=e+392|0;Ln(h,k,u,hi);r=r+6|0}else{Fe(w,ci,r,ui,b,k,hi);h=e+392|0;Tn(h,b,k,u,hi);r=r+10|0}d=k;v=h+20|0;do{t[h>>1]=t[d>>1]|0;h=h+2|0;d=d+2|0}while((h|0)<(v|0));fi=i>>>0>1;M=i>>>0<4&1;ti=(i|0)==5;ni=ti?10:5;ti=ti?19:9;N=e+434|0;T=143-ti&65535;L=e+460|0;F=e+462|0;C=e+464|0;D=i>>>0>2;P=e+388|0;B=(i|0)==0;U=i>>>0<2;x=e+1244|0;z=e+432|0;j=i>>>0<6;H=e+1168|0;W=(i|0)==6;q=ci<<16>>16==0;G=e+714|0;X=e+686|0;V=e+436|0;K=e+700|0;Y=(i|0)==7;Q=e+482|0;Z=i>>>0<3;$=e+608|0;J=e+626|0;ee=e+438|0;ie=i>>>0<7;re=e+730|0;O=si^1;ne=s<<16>>16!=0;ri=ne?ci^1:0;te=e+442|0;fe=e+458|0;oe=e+412|0;le=e+80|0;ue=e+1236|0;se=e+1240|0;ce=e+468|0;we=e+466|0;ve=e+470|0;Ae=e+472|0;Se=e+474|0;Re=e+476|0;Le=e+478|0;Ce=e+480|0;Pe=e+444|0;ze=e+446|0;qe=e+448|0;Ve=e+450|0;Ye=e+452|0;Ze=e+454|0;ii=e+456|0;S=0;_=0;m=0;y=0;R=-1;while(1){R=(R<<16>>16)+1|0;v=R&65535;_=1-(_<<16>>16)|0;E=_&65535;b=fi&m<<16>>16==80?0:m;p=r+2|0;k=t[r>>1]|0;e:do{if(li){g=t[N>>1]|0;h=(g&65535)-ni&65535;h=h<<16>>16<20?20:h;d=(h&65535)+ti&65535;w=d<<16>>16>143;De(k,w?T:h,w?143:d,b,g,ki,mi,M,hi);b=t[ki>>1]|0;t[L>>1]=b;if(si){k=t[N>>1]|0;if(k<<16>>16<143){k=(k&65535)+1&65535;t[N>>1]=k}t[ki>>1]=k;t[mi>>1]=0;if((t[F>>1]|0)!=0?!(D|(t[C>>1]|0)<5):0){t[ki>>1]=b;k=b;b=0}else b=0}else{k=b;b=t[mi>>1]|0}Zn(f[P>>2]|0,k,b,40,1,hi);if(U){b=r+6|0;ye(v,t[r+4>>1]|0,t[p>>1]|0,f[x>>2]|0,yi,hi);r=t[z>>1]|0;g=r<<16>>16;k=g<<1;if((k|0)==(g<<17>>16|0)){d=B;break}d=B;k=r<<16>>16>0?32767:-32768;break}switch(i|0){case 2:{b=r+6|0;me(t[r+4>>1]|0,t[p>>1]|0,yi);r=t[z>>1]|0;g=r<<16>>16;k=g<<1;if((k|0)==(g<<17>>16|0)){d=B;break e}d=B;k=r<<16>>16>0?32767:-32768;break e}case 3:{b=r+6|0;pe(t[r+4>>1]|0,t[p>>1]|0,yi);r=t[z>>1]|0;g=r<<16>>16;k=g<<1;if((k|0)==(g<<17>>16|0)){d=B;break e}d=B;k=r<<16>>16>0?32767:-32768;break e}default:{if(j){b=r+6|0;Ee(t[r+4>>1]|0,t[p>>1]|0,f[H>>2]|0,yi);r=t[z>>1]|0;g=r<<16>>16;k=g<<1;if((k|0)==(g<<17>>16|0)){d=B;break e}d=B;k=r<<16>>16>0?32767:-32768;break e}if(!W){d=B;A=44;break e}ge(p,yi,hi);k=r+16|0;r=t[z>>1]|0;g=r<<16>>16;v=g<<1;if((v|0)==(g<<17>>16|0)){b=k;d=B;k=v;break e}b=k;d=B;k=r<<16>>16>0?32767:-32768;break e}}}else{Oe(k,18,143,b,ki,mi,hi);if(q?b<<16>>16==0|k<<16>>16<61:0){k=t[ki>>1]|0;b=t[mi>>1]|0}else{t[L>>1]=t[ki>>1]|0;k=t[N>>1]|0;t[ki>>1]=k;t[mi>>1]=0;b=0}Zn(f[P>>2]|0,k,b,40,0,hi);d=0;A=44}}while(0);if((A|0)==44){A=0;if(si)We(X,t[o>>1]|0,Ai,hi);else t[Ai>>1]=Te(i,t[p>>1]|0,f[se>>2]|0)|0;Ge(X,ci,t[V>>1]|0,Ai,hi);ke(r+4|0,yi,f[H>>2]|0);k=r+24|0;r=t[Ai>>1]|0;g=r<<16>>16;v=g<<1;if((v|0)==(g<<17>>16|0)){b=k;k=v}else{b=k;k=r<<16>>16>0?32767:-32768}}r=t[ki>>1]|0;e:do{if(r<<16>>16<40){h=k<<16>>16;w=r;k=r<<16>>16;while(1){v=yi+(k<<1)|0;r=(I(t[yi+(k-(w<<16>>16)<<1)>>1]|0,h)|0)>>15;if((r|0)>32767){f[hi>>2]=1;r=32767}g=r&65535;t[Ri>>1]=g;t[v>>1]=gn(t[v>>1]|0,g,hi)|0;k=k+1|0;if((k&65535)<<16>>16==40)break e;w=t[ki>>1]|0}}}while(0);e:do{if(d){d=(_&65535|0)==0;if(d){r=b;v=y}else{r=b+2|0;v=t[b>>1]|0}if(q)Me(G,i,v,yi,E,Ai,gi,ui,hi);else{We(X,t[o>>1]|0,Ai,hi);je(K,G,t[o>>1]|0,gi,hi)}Ge(X,ci,t[V>>1]|0,Ai,hi);He(K,ci,t[V>>1]|0,gi,hi);b=t[Ai>>1]|0;k=b<<16>>16>13017?13017:b;if(d)A=80;else g=v}else{r=b+2|0;k=t[b>>1]|0;switch(i|0){case 1:case 2:case 3:case 4:case 6:{if(q)Me(G,i,k,yi,E,Ai,gi,ui,hi);else{We(X,t[o>>1]|0,Ai,hi);je(K,G,t[o>>1]|0,gi,hi)}Ge(X,ci,t[V>>1]|0,Ai,hi);He(K,ci,t[V>>1]|0,gi,hi);b=t[Ai>>1]|0;k=b<<16>>16>13017?13017:b;if(!W){v=y;A=80;break e}if((t[N>>1]|0)<=45){v=y;A=80;break e}v=y;k=k<<16>>16>>>2&65535;A=80;break e}case 5:{if(si)We(X,t[o>>1]|0,Ai,hi);else t[Ai>>1]=Te(5,k,f[se>>2]|0)|0;Ge(X,ci,t[V>>1]|0,Ai,hi);if(q)Ne(G,5,t[r>>1]|0,yi,f[ue>>2]|0,gi,hi);else je(K,G,t[o>>1]|0,gi,hi);He(K,ci,t[V>>1]|0,gi,hi);k=t[Ai>>1]|0;r=b+4|0;b=k;v=y;k=k<<16>>16>13017?13017:k;A=80;break e}default:{if(q)Ne(G,i,k,yi,f[ue>>2]|0,gi,hi);else je(K,G,t[o>>1]|0,gi,hi);He(K,ci,t[V>>1]|0,gi,hi);k=t[Ai>>1]|0;b=k;v=y;A=80;break e}}}}while(0);if((A|0)==80){A=0;t[z>>1]=b<<16>>16>13017?13017:b;g=v}k=k<<16>>16;k=(k<<17>>17|0)==(k|0)?k<<1:k>>>15^32767;E=(k&65535)<<16>>16>16384;e:do{if(E){p=k<<16>>16;if(Y)b=0;else{b=0;while(1){k=(I(t[(f[P>>2]|0)+(b<<1)>>1]|0,p)|0)>>15;if((k|0)>32767){f[hi>>2]=1;k=32767}t[Ri>>1]=k;k=I(t[Ai>>1]|0,k<<16>>16)|0;if((k|0)==1073741824){f[hi>>2]=1;k=2147483647}else k=k<<1;t[Ei+(b<<1)>>1]=at(k,hi)|0;b=b+1|0;if((b|0)==40)break e}}do{k=(I(t[(f[P>>2]|0)+(b<<1)>>1]|0,p)|0)>>15;if((k|0)>32767){f[hi>>2]=1;k=32767}t[Ri>>1]=k;k=I(t[Ai>>1]|0,k<<16>>16)|0;if((k|0)!=1073741824){k=k<<1;if((k|0)<0)k=~((k^-2)>>1);else A=88}else{f[hi>>2]=1;k=2147483647;A=88}if((A|0)==88){A=0;k=k>>1}t[Ei+(b<<1)>>1]=at(k,hi)|0;b=b+1|0}while((b|0)!=40)}}while(0);if(q){t[we>>1]=t[ce>>1]|0;t[ce>>1]=t[ve>>1]|0;t[ve>>1]=t[Ae>>1]|0;t[Ae>>1]=t[Se>>1]|0;t[Se>>1]=t[Re>>1]|0;t[Re>>1]=t[Le>>1]|0;t[Le>>1]=t[Ce>>1]|0;t[Ce>>1]=t[Q>>1]|0;t[Q>>1]=t[Ai>>1]|0}if((si|(t[V>>1]|0)!=0?Z&(t[F>>1]|0)!=0:0)?(vi=t[Ai>>1]|0,vi<<16>>16>12288):0){A=(((vi<<16>>16)+118784|0)>>>1)+12288&65535;t[Ai>>1]=A<<16>>16>14745?14745:A}Ke(_i,ai,m,Si,hi);k=be($,i,t[gi>>1]|0,Si,J,ci,t[V>>1]|0,s,t[ee>>1]|0,t[F>>1]|0,t[C>>1]|0,hi)|0;switch(i|0){case 0:case 1:case 2:case 3:case 6:{v=t[Ai>>1]|0;p=1;break}default:{k=t[gi>>1]|0;v=t[Ai>>1]|0;if(ie)p=1;else{b=v<<16>>16;if(v<<16>>16<0)b=~((b^-2)>>1);else b=b>>>1;v=b&65535;p=2}}}h=v<<16>>16;m=p&65535;b=f[P>>2]|0;y=0;do{b=b+(y<<1)|0;t[pi+(y<<1)>>1]=t[b>>1]|0;b=I(t[b>>1]|0,h)|0;if((b|0)==1073741824){f[hi>>2]=1;w=2147483647}else w=b<<1;d=I(t[gi>>1]|0,t[yi+(y<<1)>>1]|0)|0;if((d|0)!=1073741824){b=(d<<1)+w|0;if((d^w|0)>0&(b^w|0)<0){f[hi>>2]=1;b=(w>>>31)+2147483647|0}}else{f[hi>>2]=1;b=2147483647}A=b<<m;A=at((A>>m|0)==(b|0)?A:b>>31^2147483647,hi)|0;b=f[P>>2]|0;t[b+(y<<1)>>1]=A;y=y+1|0}while((y|0)!=40);Je(re);if((Z?(t[C>>1]|0)>3:0)?!((t[F>>1]|0)==0|O):0)$e(re);ei(re,i,pi,k,t[Ai>>1]|0,yi,v,p,ui,hi);k=0;d=0;do{b=t[pi+(d<<1)>>1]|0;b=I(b,b)|0;if((b|0)!=1073741824){v=(b<<1)+k|0;if((b^k|0)>0&(v^k|0)<0){f[hi>>2]=1;k=(k>>>31)+2147483647|0}else k=v}else{f[hi>>2]=1;k=2147483647}d=d+1|0}while((d|0)!=40);if((k|0)<0)k=~((k^-2)>>1);else k=k>>1;k=st(k,Ri,hi)|0;v=((t[Ri>>1]|0)>>>1)+15|0;b=v&65535;v=v<<16>>16;if(b<<16>>16>0)if(b<<16>>16<31){k=k>>v;A=135}else{k=0;A=137}else{p=0-v<<16>>16;A=k<<p;k=(A>>p|0)==(k|0)?A:k>>31^2147483647;A=135}if((A|0)==135){A=0;if((k|0)<0)k=~((k^-4)>>2);else A=137}if((A|0)==137){A=0;k=k>>>2}k=k&65535;do{if(Z?(bi=t[C>>1]|0,bi<<16>>16>5):0)if(t[F>>1]|0)if((t[o>>1]|0)<4){if(ne){if(!(si|(t[ee>>1]|0)!=0))A=145}else if(!si)A=145;if((A|0)==145?(t[V>>1]|0)==0:0){A=147;break}Xe(pi,k,te,bi,t[V>>1]|0,ri,hi)|0;A=147}else A=147;else A=151;else A=147}while(0);do{if((A|0)==147){A=0;if(t[F>>1]|0){if(!si?(t[V>>1]|0)==0:0){A=151;break}if((t[o>>1]|0)>=4)A=151}else A=151}}while(0);if((A|0)==151){A=0;t[te>>1]=t[Pe>>1]|0;t[Pe>>1]=t[ze>>1]|0;t[ze>>1]=t[qe>>1]|0;t[qe>>1]=t[Ve>>1]|0;t[Ve>>1]=t[Ye>>1]|0;t[Ye>>1]=t[Ze>>1]|0;t[Ze>>1]=t[ii>>1]|0;t[ii>>1]=t[fe>>1]|0;t[fe>>1]=k}if(E){k=0;do{E=Ei+(k<<1)|0;t[E>>1]=gn(t[E>>1]|0,t[pi+(k<<1)>>1]|0,hi)|0;k=k+1|0}while((k|0)!=40);ae(pi,Ei,40,hi);f[hi>>2]=0;ht(u,Ei,l+(S<<1)|0,40,oe,0)}else{f[hi>>2]=0;ht(u,pi,l+(S<<1)|0,40,oe,0)}if(!(f[hi>>2]|0))pt(oe|0,l+(S+30<<1)|0,20)|0;else{v=193;while(1){b=e+(v<<1)|0;E=t[b>>1]|0;k=E<<16>>16;if(E<<16>>16<0)k=~((k^-4)>>2);else k=k>>>2;t[b>>1]=k;if((v|0)>0)v=v+-1|0;else{v=39;break}}while(1){b=pi+(v<<1)|0;E=t[b>>1]|0;k=E<<16>>16;if(E<<16>>16<0)k=~((k^-4)>>2);else k=k>>>2;t[b>>1]=k;if((v|0)>0)v=v+-1|0;else break}ht(u,pi,l+(S<<1)|0,40,oe,1)}pt(e|0,le|0,308)|0;t[N>>1]=t[ki>>1]|0;k=S+40|0;m=k&65535;if(m<<16>>16>=160)break;else{S=k<<16>>16;u=u+22|0;y=g}}t[F>>1]=de(e+484|0,e+466|0,l,C,hi)|0;Ue(wi,ai,l,hi);t[V>>1]=ci;t[ee>>1]=s;Qe(e+626|0,ai,hi);Ri=oi;f[Ri>>2]=di;c=Mi;return}function Me(e,i,r,n,o,l,u,s,h){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;l=l|0;u=u|0;s=s|0;h=h|0;var w=0,d=0,v=0,b=0,k=0;k=c;c=c+16|0;v=k+2|0;b=k;r=r<<16>>16;r=(r<<18>>18|0)==(r|0)?r<<2:r>>>15^32767;switch(i|0){case 3:case 4:case 6:{d=r<<16>>16;r=f[s+84>>2]|0;t[l>>1]=t[r+(d<<1)>>1]|0;s=t[r+(d+1<<1)>>1]|0;w=t[r+(d+3<<1)>>1]|0;l=t[r+(d+2<<1)>>1]|0;break}case 0:{s=(r&65535)+(o<<16>>16<<1^2)|0;s=(s&65535)<<16>>16>1022?1022:s<<16>>16;t[l>>1]=t[782+(s<<1)>>1]|0;l=t[782+(s+1<<1)>>1]|0;Pn(l<<16>>16,b,v,h);t[b>>1]=(a[b>>1]|0)+65524;s=ut(t[v>>1]|0,5,h)|0;d=t[b>>1]|0;d=gn(s,((d<<26>>26|0)==(d|0)?d<<10:d>>>15^32767)&65535,h)|0;s=t[v>>1]|0;r=t[b>>1]|0;if((r*24660|0)==1073741824){f[h>>2]=1;o=2147483647}else o=r*49320|0;w=(s<<16>>16)*24660>>15;r=o+(w<<1)|0;if((o^w|0)>0&(r^o|0)<0){f[h>>2]=1;r=(o>>>31)+2147483647|0}w=r<<13;s=l;w=at((w>>13|0)==(r|0)?w:r>>31^2147483647,h)|0;l=d;break}default:{d=r<<16>>16;r=f[s+80>>2]|0;t[l>>1]=t[r+(d<<1)>>1]|0;s=t[r+(d+1<<1)>>1]|0;w=t[r+(d+3<<1)>>1]|0;l=t[r+(d+2<<1)>>1]|0}}Rn(e,i,n,b,v,0,0,h);o=I((Qn(14,t[v>>1]|0,h)|0)<<16>>16,s<<16>>16)|0;if((o|0)==1073741824){f[h>>2]=1;r=2147483647}else r=o<<1;s=10-(a[b>>1]|0)|0;o=s&65535;s=s<<16>>16;if(o<<16>>16>0){b=o<<16>>16<31?r>>s:0;b=b>>>16;b=b&65535;t[u>>1]=b;Mn(e,l,w);c=k;return}else{h=0-s<<16>>16;b=r<<h;b=(b>>h|0)==(r|0)?b:r>>31^2147483647;b=b>>>16;b=b&65535;t[u>>1]=b;Mn(e,l,w);c=k;return}}function De(e,i,r,n,o,a,l,u,s){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;l=l|0;u=u|0;s=s|0;if(!(n<<16>>16)){u=e<<16>>16;if(e<<16>>16>=197){t[a>>1]=u+65424;t[l>>1]=0;return}o=((u<<16)+131072>>16)*10923>>15;if((o|0)>32767){f[s>>2]=1;o=32767}e=(o&65535)+19|0;t[a>>1]=e;t[l>>1]=u+58-((e*196608|0)>>>16);return}if(!(u<<16>>16)){s=e<<16>>16<<16;e=((s+131072>>16)*21846|0)+-65536>>16;t[a>>1]=e+(i&65535);t[l>>1]=((s+-131072|0)>>>16)-((e*196608|0)>>>16);return}if((ct(o,i,s)|0)<<16>>16>5)o=(i&65535)+5&65535;u=r<<16>>16;u=(u-(o&65535)&65535)<<16>>16>4?u+65532&65535:o;o=e<<16>>16;if(e<<16>>16<4){t[a>>1]=((((u&65535)<<16)+-327680|0)>>>16)+o;t[l>>1]=0;return}o=o<<16;if(e<<16>>16<12){s=(((o+-327680>>16)*10923|0)>>>15<<16)+-65536|0;e=s>>16;t[a>>1]=(u&65535)+e;t[l>>1]=((o+-589824|0)>>>16)-(s>>>15)-e;return}else{t[a>>1]=((o+-786432+((u&65535)<<16)|0)>>>16)+1;t[l>>1]=0;return}}function Oe(e,i,r,n,f,o,l){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;o=o|0;l=l|0;if(n<<16>>16){l=(a[f>>1]|0)+65531|0;l=(l<<16>>16|0)<(i<<16>>16|0)?i:l&65535;r=r<<16>>16;i=e<<16>>16<<16;e=((i+327680>>16)*10924|0)+-65536>>16;t[f>>1]=(((((l&65535)<<16)+589824>>16|0)>(r|0)?r+65527&65535:l)&65535)+e;t[o>>1]=((i+-196608|0)>>>16)-((e*393216|0)>>>16);return}n=e<<16>>16;if(e<<16>>16<463){e=((((n<<16)+327680>>16)*10924|0)>>>16)+17|0;t[f>>1]=e;t[o>>1]=n+105-((e*393216|0)>>>16);return}else{t[f>>1]=n+65168;t[o>>1]=0;return}}function Ne(e,i,r,n,o,a,l){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;l=l|0;var u=0,s=0,h=0,w=0;w=c;c=c+16|0;s=w+6|0;u=w+4|0;Rn(e,i,n,s,u,w+2|0,w,l);h=(r&31)*3|0;n=o+(h<<1)|0;if(!((ct(i&65535,7,l)|0)<<16>>16)){s=Qn(t[s>>1]|0,t[u>>1]|0,l)|0;u=s<<16>>16;u=(I(((s<<20>>20|0)==(u|0)?s<<4:u>>>15^32767)<<16>>16,t[n>>1]|0)|0)>>15;if((u|0)>32767){f[l>>2]=1;u=32767}n=u<<16;r=n>>16;if((u<<17>>17|0)==(r|0))u=n>>15;else u=r>>>15^32767}else{r=Qn(14,t[u>>1]|0,l)|0;r=I(r<<16>>16,t[n>>1]|0)|0;if((r|0)==1073741824){f[l>>2]=1;n=2147483647}else n=r<<1;r=ct(9,t[s>>1]|0,l)|0;u=r<<16>>16;if(r<<16>>16>0)u=r<<16>>16<31?n>>u:0;else{l=0-u<<16>>16;u=n<<l;u=(u>>l|0)==(n|0)?u:n>>31^2147483647}u=u>>>16}t[a>>1]=u;Mn(e,t[o+(h+1<<1)>>1]|0,t[o+(h+2<<1)>>1]|0);c=w;return}function Te(e,i,r){e=e|0;i=i|0;r=r|0;i=t[r+(i<<16>>16<<1)>>1]|0;if((e|0)!=7){e=i;return e|0}e=i&65532;return e|0}function Ie(e,i,r,o,a,l,u){e=e|0;i=i|0;r=r|0;o=o|0;a=a|0;l=l|0;u=u|0;var s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0;E=c;c=c+48|0;k=E+20|0;p=E;y=f[a+44>>2]|0;m=f[a+64>>2]|0;s=f[a+4>>2]|0;b=f[a+12>>2]|0;w=f[a+20>>2]|0;h=f[a+56>>2]|0;if(!(r<<16>>16)){d=i>>>0<2;if(d){r=765;v=508;w=f[a+52>>2]|0}else{a=(i|0)==5;r=a?1533:765;v=2044;s=a?h:s}h=t[o>>1]|0;r=((h*196608>>16|0)>(r&65535|0)?r:h*3&65535)<<16>>16;h=t[s+(r<<1)>>1]|0;t[k>>1]=h;t[k+2>>1]=t[s+(r+1<<1)>>1]|0;t[k+4>>1]=t[s+(r+2<<1)>>1]|0;r=t[o+2>>1]|0;if(d)r=r<<16>>16<<1&65535;d=(r<<16>>16)*196608|0;d=(d|0)>100466688?1533:d>>16;t[k+6>>1]=t[b+(d<<1)>>1]|0;t[k+8>>1]=t[b+(d+1<<1)>>1]|0;t[k+10>>1]=t[b+(d+2<<1)>>1]|0;o=t[o+4>>1]|0;o=((o<<18>>16|0)>(v&65535|0)?v:o<<2&65535)<<16>>16;t[k+12>>1]=t[w+(o<<1)>>1]|0;t[k+14>>1]=t[w+((o|1)<<1)>>1]|0;t[k+16>>1]=t[w+((o|2)<<1)>>1]|0;t[k+18>>1]=t[w+((o|3)<<1)>>1]|0;if((i|0)==8){r=0;while(1){m=e+(r<<1)|0;t[p+(r<<1)>>1]=gn(h,gn(t[y+(r<<1)>>1]|0,t[m>>1]|0,u)|0,u)|0;t[m>>1]=h;r=r+1|0;if((r|0)==10)break;h=t[k+(r<<1)>>1]|0}ft(p,205,10,u);s=e+20|0;h=p;r=s+20|0;do{n[s>>0]=n[h>>0]|0;s=s+1|0;h=h+1|0}while((s|0)<(r|0));Gn(p,l,10,u);c=E;return}else s=0;do{h=e+(s<<1)|0;r=(I(t[m+(s<<1)>>1]|0,t[h>>1]|0)|0)>>15;if((r|0)>32767){f[u>>2]=1;r=32767}o=gn(t[y+(s<<1)>>1]|0,r&65535,u)|0;i=t[k+(s<<1)>>1]|0;t[p+(s<<1)>>1]=gn(i,o,u)|0;t[h>>1]=i;s=s+1|0}while((s|0)!=10);ft(p,205,10,u);s=e+20|0;h=p;r=s+20|0;do{n[s>>0]=n[h>>0]|0;s=s+1|0;h=h+1|0}while((s|0)<(r|0));Gn(p,l,10,u);c=E;return}else{s=0;do{r=(t[e+20+(s<<1)>>1]|0)*29491>>15;if((r|0)>32767){f[u>>2]=1;r=32767}h=(t[y+(s<<1)>>1]|0)*3277>>15;if((h|0)>32767){f[u>>2]=1;h=32767}t[p+(s<<1)>>1]=gn(h&65535,r&65535,u)|0;s=s+1|0}while((s|0)!=10);if((i|0)==8){s=0;do{m=e+(s<<1)|0;k=gn(t[y+(s<<1)>>1]|0,t[m>>1]|0,u)|0;t[m>>1]=ct(t[p+(s<<1)>>1]|0,k,u)|0;s=s+1|0}while((s|0)!=10);ft(p,205,10,u);s=e+20|0;h=p;r=s+20|0;do{n[s>>0]=n[h>>0]|0;s=s+1|0;h=h+1|0}while((s|0)<(r|0));Gn(p,l,10,u);c=E;return}else s=0;do{h=e+(s<<1)|0;r=(I(t[m+(s<<1)>>1]|0,t[h>>1]|0)|0)>>15;if((r|0)>32767){f[u>>2]=1;r=32767}k=gn(t[y+(s<<1)>>1]|0,r&65535,u)|0;t[h>>1]=ct(t[p+(s<<1)>>1]|0,k,u)|0;s=s+1|0}while((s|0)!=10);ft(p,205,10,u);s=e+20|0;h=p;r=s+20|0;do{n[s>>0]=n[h>>0]|0;s=s+1|0;h=h+1|0}while((s|0)<(r|0));Gn(p,l,10,u);c=E;return}}function Le(e,i,r){e=e|0;i=i|0;r=r|0;pt(e|0,r+((i<<16>>16)*10<<1)|0,20)|0;return}function Fe(e,i,r,o,a,l,u){e=e|0;i=i|0;r=r|0;o=o|0;a=a|0;l=l|0;u=u|0;var s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0;E=c;c=c+80|0;b=E+60|0;k=E+40|0;y=E+20|0;p=E;m=f[o+48>>2]|0;w=f[o+24>>2]|0;d=f[o+28>>2]|0;v=f[o+32>>2]|0;if(i<<16>>16){s=0;do{b=m+(s<<1)|0;r=gn(((t[b>>1]|0)*1639|0)>>>15&65535,((t[e+20+(s<<1)>>1]|0)*31128|0)>>>15&65535,u)|0;t[y+(s<<1)>>1]=r;t[p+(s<<1)>>1]=r;k=e+(s<<1)|0;t[k>>1]=ct(r,gn(t[b>>1]|0,((t[k>>1]|0)*21299|0)>>>15&65535,u)|0,u)|0;s=s+1|0}while((s|0)!=10);ft(y,205,10,u);ft(p,205,10,u);s=e+20|0;o=p;i=s+20|0;do{n[s>>0]=n[o>>0]|0;s=s+1|0;o=o+1|0}while((s|0)<(i|0));Gn(y,a,10,u);Gn(p,l,10,u);c=E;return}i=f[o+16>>2]|0;o=f[o+8>>2]|0;h=t[r>>1]|0;h=((h<<18>>18|0)==(h|0)?h<<2:h>>>15^32767)<<16>>16;t[b>>1]=t[o+(h<<1)>>1]|0;t[b+2>>1]=t[o+(h+1<<1)>>1]|0;t[k>>1]=t[o+(h+2<<1)>>1]|0;t[k+2>>1]=t[o+(h+3<<1)>>1]|0;h=t[r+2>>1]|0;h=((h<<18>>18|0)==(h|0)?h<<2:h>>>15^32767)<<16>>16;t[b+4>>1]=t[i+(h<<1)>>1]|0;t[b+6>>1]=t[i+(h+1<<1)>>1]|0;t[k+4>>1]=t[i+(h+2<<1)>>1]|0;t[k+6>>1]=t[i+(h+3<<1)>>1]|0;h=t[r+4>>1]|0;o=h<<16>>16;if(h<<16>>16<0)i=~((o^-2)>>1);else i=o>>>1;h=i<<16>>16;h=((i<<18>>18|0)==(h|0)?i<<2:h>>>15^32767)<<16>>16;s=w+(h+1<<1)|0;i=t[w+(h<<1)>>1]|0;if(!(o&1)){t[b+8>>1]=i;t[b+10>>1]=t[s>>1]|0;t[k+8>>1]=t[w+(h+2<<1)>>1]|0;t[k+10>>1]=t[w+(h+3<<1)>>1]|0}else{if(i<<16>>16==-32768)i=32767;else i=0-(i&65535)&65535;t[b+8>>1]=i;i=t[s>>1]|0;if(i<<16>>16==-32768)i=32767;else i=0-(i&65535)&65535;t[b+10>>1]=i;i=t[w+(h+2<<1)>>1]|0;if(i<<16>>16==-32768)i=32767;else i=0-(i&65535)&65535;t[k+8>>1]=i;i=t[w+(h+3<<1)>>1]|0;if(i<<16>>16==-32768)i=32767;else i=0-(i&65535)&65535;t[k+10>>1]=i}s=t[r+6>>1]|0;s=((s<<18>>18|0)==(s|0)?s<<2:s>>>15^32767)<<16>>16;t[b+12>>1]=t[d+(s<<1)>>1]|0;t[b+14>>1]=t[d+(s+1<<1)>>1]|0;t[k+12>>1]=t[d+(s+2<<1)>>1]|0;t[k+14>>1]=t[d+(s+3<<1)>>1]|0;s=t[r+8>>1]|0;s=((s<<18>>18|0)==(s|0)?s<<2:s>>>15^32767)<<16>>16;t[b+16>>1]=t[v+(s<<1)>>1]|0;t[b+18>>1]=t[v+(s+1<<1)>>1]|0;t[k+16>>1]=t[v+(s+2<<1)>>1]|0;t[k+18>>1]=t[v+(s+3<<1)>>1]|0;s=0;do{o=e+(s<<1)|0;i=(t[o>>1]|0)*21299>>15;if((i|0)>32767){f[u>>2]=1;i=32767}v=gn(t[m+(s<<1)>>1]|0,i&65535,u)|0;t[y+(s<<1)>>1]=gn(t[b+(s<<1)>>1]|0,v,u)|0;r=t[k+(s<<1)>>1]|0;t[p+(s<<1)>>1]=gn(r,v,u)|0;t[o>>1]=r;s=s+1|0}while((s|0)!=10);ft(y,205,10,u);ft(p,205,10,u);s=e+20|0;o=p;i=s+20|0;do{n[s>>0]=n[o>>0]|0;s=s+1|0;o=o+1|0}while((s|0)<(i|0));Gn(y,a,10,u);Gn(p,l,10,u);c=E;return}function Ce(e,i){e=e|0;i=i|0;var r=0,n=0;if(!e){n=-1;return n|0}r=e;n=r+20|0;do{t[r>>1]=0;r=r+2|0}while((r|0)<(n|0));pt(e+20|0,i|0,20)|0;n=0;return n|0}function Pe(e){e=e|0;var i=0,r=0,o=0,a=0,l=0;if(!e){l=-1;return l|0}t[e>>1]=0;t[e+2>>1]=8192;i=e+4|0;t[i>>1]=3500;t[e+6>>1]=3500;f[e+8>>2]=1887529304;t[e+12>>1]=3e4;t[e+14>>1]=26e3;t[e+16>>1]=21e3;t[e+18>>1]=15e3;t[e+20>>1]=8e3;t[e+22>>1]=0;t[e+24>>1]=-8e3;t[e+26>>1]=-15e3;t[e+28>>1]=-21e3;t[e+30>>1]=-26e3;t[e+32>>1]=3e4;t[e+34>>1]=26e3;t[e+36>>1]=21e3;t[e+38>>1]=15e3;t[e+40>>1]=8e3;t[e+42>>1]=0;t[e+44>>1]=-8e3;t[e+46>>1]=-15e3;t[e+48>>1]=-21e3;t[e+50>>1]=-26e3;t[e+212>>1]=0;t[e+374>>1]=0;t[e+392>>1]=0;r=e+52|0;t[r>>1]=1384;t[e+54>>1]=2077;t[e+56>>1]=3420;t[e+58>>1]=5108;t[e+60>>1]=6742;t[e+62>>1]=8122;t[e+64>>1]=9863;t[e+66>>1]=11092;t[e+68>>1]=12714;t[e+70>>1]=13701;o=e+72|0;a=r;l=o+20|0;do{n[o>>0]=n[a>>0]|0;o=o+1|0;a=a+1|0}while((o|0)<(l|0));o=e+92|0;a=r;l=o+20|0;do{n[o>>0]=n[a>>0]|0;o=o+1|0;a=a+1|0}while((o|0)<(l|0));o=e+112|0;a=r;l=o+20|0;do{n[o>>0]=n[a>>0]|0;o=o+1|0;a=a+1|0}while((o|0)<(l|0));o=e+132|0;a=r;l=o+20|0;do{n[o>>0]=n[a>>0]|0;o=o+1|0;a=a+1|0}while((o|0)<(l|0));o=e+152|0;a=r;l=o+20|0;do{n[o>>0]=n[a>>0]|0;o=o+1|0;a=a+1|0}while((o|0)<(l|0));o=e+172|0;a=r;l=o+20|0;do{n[o>>0]=n[a>>0]|0;o=o+1|0;a=a+1|0}while((o|0)<(l|0));o=e+192|0;a=r;l=o+20|0;do{n[o>>0]=n[a>>0]|0;o=o+1|0;a=a+1|0}while((o|0)<(l|0));Et(e+214|0,0,160)|0;t[e+376>>1]=3500;t[e+378>>1]=3500;l=t[i>>1]|0;t[e+380>>1]=l;t[e+382>>1]=l;t[e+384>>1]=l;t[e+386>>1]=l;t[e+388>>1]=l;t[e+390>>1]=l;t[e+394>>1]=0;t[e+396>>1]=7;t[e+398>>1]=32767;t[e+400>>1]=0;t[e+402>>1]=0;t[e+404>>1]=0;f[e+408>>2]=1;t[e+412>>1]=0;l=0;return l|0}function Be(e,i,r,o,l,u,s,h,w,d,v,b){e=e|0;i=i|0;r=r|0;o=o|0;l=l|0;u=u|0;s=s|0;h=h|0;w=w|0;d=d|0;v=v|0;b=b|0;var k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,L=0,F=0,C=0,P=0,B=0,U=0,x=0,z=0,j=0,H=0,W=0,q=0,G=0,X=0,V=0,K=0,Y=0,Q=0,Z=0,$=0;$=c;c=c+304|0;W=$+192|0;z=$+168|0;G=$+148|0;Y=$+216|0;X=$+146|0;V=$+144|0;j=$+124|0;H=$+104|0;q=$+84|0;K=$+60|0;U=$+40|0;B=$;Z=e+404|0;Q=e+400|0;if((t[Z>>1]|0)!=0?(t[Q>>1]|0)!=0:0){P=e+394|0;t[P>>1]=t[636+(s<<1)>>1]|0;_=t[e+212>>1]|0;S=_+10|0;pt(e+52+(((S&65535|0)==80?0:S<<16>>16)<<1)|0,e+52+(_<<1)|0,20)|0;_=t[e+392>>1]|0;S=_+1|0;t[e+376+(((S&65535|0)==8?0:S<<16>>16)<<1)>>1]=t[e+376+(_<<1)>>1]|0;S=e+4|0;t[S>>1]=0;_=B+36|0;R=B+32|0;M=B+28|0;D=B+24|0;O=B+20|0;N=B+16|0;T=B+12|0;L=B+8|0;F=B+4|0;C=e+52|0;y=B;x=y+40|0;do{f[y>>2]=0;y=y+4|0}while((y|0)<(x|0));m=0;k=7;while(1){x=t[e+376+(k<<1)>>1]|0;A=x<<16>>16;if(x<<16>>16<0)A=~((A^-8)>>3);else A=A>>>3;m=gn(m,A&65535,b)|0;t[S>>1]=m;E=k*10|0;y=9;while(1){p=B+(y<<2)|0;g=f[p>>2]|0;x=t[e+52+(y+E<<1)>>1]|0;A=x+g|0;if((x^g|0)>-1&(A^g|0)<0){f[b>>2]=1;A=(g>>>31)+2147483647|0}f[p>>2]=A;if((y|0)>0)y=y+-1|0;else break}if((k|0)<=0)break;else k=k+-1|0}t[U+18>>1]=(f[_>>2]|0)>>>3;t[U+16>>1]=(f[R>>2]|0)>>>3;t[U+14>>1]=(f[M>>2]|0)>>>3;t[U+12>>1]=(f[D>>2]|0)>>>3;t[U+10>>1]=(f[O>>2]|0)>>>3;t[U+8>>1]=(f[N>>2]|0)>>>3;t[U+6>>1]=(f[T>>2]|0)>>>3;t[U+4>>1]=(f[L>>2]|0)>>>3;t[U+2>>1]=(f[F>>2]|0)>>>3;t[U>>1]=(f[B>>2]|0)>>>3;Gn(U,e+12|0,10,b);t[S>>1]=ct(t[S>>1]|0,t[P>>1]|0,b)|0;yt(e+214|0,C|0,160)|0;U=9;while(1){x=t[e+214+(U+70<<1)>>1]|0;p=x<<16>>16;B=t[e+214+(U+60<<1)>>1]|0;y=(B<<16>>16)+p|0;if((B^x)<<16>>16>-1&(y^p|0)<0){f[b>>2]=1;y=(p>>>31)+2147483647|0}x=t[e+214+(U+50<<1)>>1]|0;p=x+y|0;if((x^y|0)>-1&(p^y|0)<0){f[b>>2]=1;p=(y>>>31)+2147483647|0}x=t[e+214+(U+40<<1)>>1]|0;y=x+p|0;if((x^p|0)>-1&(y^p|0)<0){f[b>>2]=1;y=(p>>>31)+2147483647|0}x=t[e+214+(U+30<<1)>>1]|0;p=x+y|0;if((x^y|0)>-1&(p^y|0)<0){f[b>>2]=1;p=(y>>>31)+2147483647|0}x=t[e+214+(U+20<<1)>>1]|0;y=x+p|0;if((x^p|0)>-1&(y^p|0)<0){f[b>>2]=1;y=(p>>>31)+2147483647|0}x=t[e+214+(U+10<<1)>>1]|0;p=x+y|0;if((x^y|0)>-1&(p^y|0)<0){f[b>>2]=1;y=(y>>>31)+2147483647|0}else y=p;x=t[e+214+(U<<1)>>1]|0;p=x+y|0;if((x^y|0)>-1&(p^y|0)<0){f[b>>2]=1;p=(y>>>31)+2147483647|0}if((p|0)<0)p=~((p^-8)>>3);else p=p>>>3;A=p&65535;E=t[654+(U<<1)>>1]|0;g=7;while(1){k=e+214+((g*10|0)+U<<1)|0;p=ct(t[k>>1]|0,A,b)|0;t[k>>1]=p;p=(I(E,p<<16>>16)|0)>>15;if((p|0)>32767){f[b>>2]=1;p=32767}t[k>>1]=p;m=(p&65535)-(p>>>15&1)|0;m=m<<16>>31^m;y=m&65535;if(y<<16>>16>655)y=(((m<<16>>16)+261489|0)>>>2)+655&65535;y=y<<16>>16>1310?1310:y;if(!(p&32768))p=y;else p=0-(y&65535)&65535;t[k>>1]=p;if((g|0)>0)g=g+-1|0;else break}if((U|0)>0)U=U+-1|0;else break}}if(t[Q>>1]|0){A=e+32|0;g=e+12|0;y=A;E=g;x=y+20|0;do{n[y>>0]=n[E>>0]|0;y=y+1|0;E=E+1|0}while((y|0)<(x|0));E=e+4|0;m=t[E>>1]|0;k=e+6|0;t[k>>1]=m;do{if(t[e+402>>1]|0){y=t[e>>1]|0;t[e>>1]=0;y=y<<16>>16<32?y:32;x=y<<16>>16;p=x<<10;if((p|0)!=(x<<26>>16|0)){f[b>>2]=1;p=y<<16>>16>0?32767:-32768}if(y<<16>>16>1)p=Sn(1024,p&65535)|0;else p=16384;t[e+2>>1]=p;Le(r,t[h>>1]|0,f[w+60>>2]|0);Ie(r,8,0,h+2|0,w,g,b);y=r;x=y+20|0;do{n[y>>0]=0;y=y+1|0}while((y|0)<(x|0));m=t[h+8>>1]|0;m=m<<16>>16==0?-32768:((m+64&65535)>127?m<<16>>16>0?32767:32768:m<<16>>16<<9)+60416&65535;t[E>>1]=m;if((t[e+412>>1]|0)!=0?(f[e+408>>2]|0)!=0:0)break;y=A;E=g;x=y+20|0;do{n[y>>0]=n[E>>0]|0;y=y+1|0;E=E+1|0}while((y|0)<(x|0));t[k>>1]=m}}while(0);y=m<<16>>16;if(m<<16>>16<0)y=~((y^-2)>>1);else y=y>>>1;y=y+56536|0;p=y<<16;if((p|0)>0)y=0;else y=(p|0)<-946077696?-14436:y&65535;t[o>>1]=y;t[o+2>>1]=y;t[o+4>>1]=y;t[o+6>>1]=y;h=((y<<16>>16)*5443|0)>>>15&65535;t[o+8>>1]=h;t[o+10>>1]=h;t[o+12>>1]=h;t[o+14>>1]=h}y=((t[636+(s<<1)>>1]|0)*104864|0)>>>15<<16;if((y|0)<0)y=~((y>>16^-32)>>5);else y=y>>21;s=e+394|0;t[s>>1]=gn(((t[s>>1]|0)*29491|0)>>>15&65535,y&65535,b)|0;o=(a[e>>1]<<16)+65536|0;y=o>>16;w=e+2|0;y=(I(((o<<10>>26|0)==(y|0)?o>>>6:y>>>15^32767)<<16>>16,t[w>>1]|0)|0)>>15;if((y|0)>32767){f[b>>2]=1;y=32767}m=y&65535;if(m<<16>>16<=1024)if(m<<16>>16<-2048)g=-32768;else g=y<<4&65535;else g=16384;h=e+4|0;A=g<<16>>16;p=I(t[h>>1]|0,A)|0;if((p|0)==1073741824){f[b>>2]=1;U=2147483647}else U=p<<1;p=(I(t[e+30>>1]|0,A)|0)>>15;if((p|0)>32767){f[b>>2]=1;p=32767}S=p&65535;t[W+18>>1]=S;p=(I(t[e+28>>1]|0,A)|0)>>15;if((p|0)>32767){f[b>>2]=1;p=32767}t[W+16>>1]=p;p=(I(t[e+26>>1]|0,A)|0)>>15;if((p|0)>32767){f[b>>2]=1;p=32767}t[W+14>>1]=p;p=(I(t[e+24>>1]|0,A)|0)>>15;if((p|0)>32767){f[b>>2]=1;p=32767}t[W+12>>1]=p;p=(I(t[e+22>>1]|0,A)|0)>>15;if((p|0)>32767){f[b>>2]=1;p=32767}t[W+10>>1]=p;p=(I(t[e+20>>1]|0,A)|0)>>15;if((p|0)>32767){f[b>>2]=1;p=32767}t[W+8>>1]=p;p=(I(t[e+18>>1]|0,A)|0)>>15;if((p|0)>32767){f[b>>2]=1;p=32767}t[W+6>>1]=p;p=(I(t[e+16>>1]|0,A)|0)>>15;if((p|0)>32767){f[b>>2]=1;p=32767}t[W+4>>1]=p;p=(I(t[e+14>>1]|0,A)|0)>>15;if((p|0)>32767){f[b>>2]=1;p=32767}t[W+2>>1]=p;p=(I(t[e+12>>1]|0,A)|0)>>15;if((p|0)>32767){f[b>>2]=1;p=32767}t[W>>1]=p;o=e+6|0;A=16384-(g&65535)<<16>>16;p=I(t[o>>1]|0,A)|0;if((p|0)!=1073741824){y=(p<<1)+U|0;if((p^U|0)>0&(y^U|0)<0){f[b>>2]=1;B=(U>>>31)+2147483647|0}else B=y}else{f[b>>2]=1;B=2147483647}y=S;E=9;while(1){m=W+(E<<1)|0;p=(I(t[e+32+(E<<1)>>1]|0,A)|0)>>15;if((p|0)>32767){f[b>>2]=1;p=32767}y=gn(y,p&65535,b)|0;t[m>>1]=y;x=y<<16>>16;p=x<<1;if((p|0)!=(x<<17>>16|0)){f[b>>2]=1;p=y<<16>>16>0?32767:-32768}t[m>>1]=p;p=E+-1|0;if((E|0)<=0)break;y=t[W+(p<<1)>>1]|0;E=p}U=e+374|0;p=((a[U>>1]<<16)+-161021952>>16)*9830>>15;if((p|0)>32767){f[b>>2]=1;p=32767}p=4096-(p&65535)|0;y=p<<16;if((y|0)>268369920)A=32767;else A=(y|0)<0?0:p<<19>>16;P=e+8|0;p=se(P,3)|0;Xn(W,j,10,b);y=H;E=j;x=y+20|0;do{t[y>>1]=t[E>>1]|0;y=y+2|0;E=E+2|0}while((y|0)<(x|0));y=(p<<16>>16)*10|0;E=9;while(1){m=H+(E<<1)|0;k=t[m>>1]|0;p=(I(t[e+214+(E+y<<1)>>1]|0,A)|0)>>15;if((p|0)>32767){f[b>>2]=1;p=32767}t[m>>1]=gn(k,p&65535,b)|0;if((E|0)>0)E=E+-1|0;else break}ft(j,205,10,b);ft(H,205,10,b);y=r+20|0;E=j;x=y+20|0;do{n[y>>0]=n[E>>0]|0;y=y+1|0;E=E+1|0}while((y|0)<(x|0));Gn(j,W,10,b);Gn(H,q,10,b);zn(W,z,b);zn(q,K,b);y=v;E=z;x=y+22|0;do{n[y>>0]=n[E>>0]|0;y=y+1|0;E=E+1|0}while((y|0)<(x|0));y=v+22|0;E=z;x=y+22|0;do{n[y>>0]=n[E>>0]|0;y=y+1|0;E=E+1|0}while((y|0)<(x|0));y=v+44|0;E=z;x=y+22|0;do{n[y>>0]=n[E>>0]|0;y=y+1|0;E=E+1|0}while((y|0)<(x|0));y=v+66|0;E=z;x=y+22|0;do{n[y>>0]=n[E>>0]|0;y=y+1|0;E=E+1|0}while((y|0)<(x|0));ue(z+2|0,G,b);p=0;y=32767;do{m=t[G+(p<<1)>>1]|0;m=I(m,m)|0;if(m>>>0<1073741824)m=32767-(m>>>15)|0;else{f[b>>2]=1;m=0}y=(I(m<<16>>16,y<<16>>16)|0)>>15;if((y|0)>32767){f[b>>2]=1;y=32767}p=p+1|0}while((p|0)!=10);Pn(y<<16>>16,X,V,b);y=(a[X>>1]<<16)+-983040|0;m=y>>16;m=lt(ct(0,gn(((y<<12>>28|0)==(m|0)?y>>>4:m>>>15^32767)&65535,lt(t[V>>1]|0,3,b)|0,b)|0,b)|0,1,b)|0;y=(t[U>>1]|0)*29491>>15;if((y|0)>32767){f[b>>2]=1;y=32767}p=m<<16>>16;m=p*3277>>15;if((m|0)>32767){f[b>>2]=1;m=32767}t[U>>1]=gn(y&65535,m&65535,b)|0;m=B>>10;k=m+262144|0;if((m|0)>-1&(k^m|0)<0){f[b>>2]=1;k=(m>>>31)+2147483647|0}V=p<<4;m=k-V|0;if(((m^k)&(k^V)|0)<0){f[b>>2]=1;k=(k>>>31)+2147483647|0}else k=m;V=t[s>>1]<<5;m=V+k|0;if((V^k|0)>-1&(m^k|0)<0){f[b>>2]=1;m=(k>>>31)+2147483647|0}p=(Qn(m>>>16&65535,m>>>1&32767,b)|0)<<16>>16;ce(P,Y,b);k=39;while(1){y=Y+(k<<1)|0;m=(I(t[y>>1]|0,p)|0)>>15;if((m|0)>32767){f[b>>2]=1;m=32767}t[y>>1]=m;if((k|0)>0)k=k+-1|0;else break}ht(K,Y,d,40,i,1);ce(P,Y,b);k=39;while(1){y=Y+(k<<1)|0;m=(I(t[y>>1]|0,p)|0)>>15;if((m|0)>32767){f[b>>2]=1;m=32767}t[y>>1]=m;if((k|0)>0)k=k+-1|0;else break}ht(K,Y,d+80|0,40,i,1);ce(P,Y,b);k=39;while(1){y=Y+(k<<1)|0;m=(I(t[y>>1]|0,p)|0)>>15;if((m|0)>32767){f[b>>2]=1;m=32767}t[y>>1]=m;if((k|0)>0)k=k+-1|0;else break}ht(K,Y,d+160|0,40,i,1);ce(P,Y,b);y=39;while(1){k=Y+(y<<1)|0;m=(I(t[k>>1]|0,p)|0)>>15;if((m|0)>32767){f[b>>2]=1;m=32767}t[k>>1]=m;if((y|0)>0)y=y+-1|0;else break}ht(K,Y,d+240|0,40,i,1);t[l+14>>1]=20;t[l+16>>1]=0;if((u|0)==2){m=t[e>>1]|0;m=m<<16>>16>32?32:m<<16>>16<1?8:m;d=m<<16>>16;k=d<<10;if((k|0)!=(d<<26>>16|0)){f[b>>2]=1;k=m<<16>>16>0?32767:-32768}t[w>>1]=Sn(1024,k&65535)|0;t[e>>1]=0;y=e+32|0;E=e+12|0;x=y+20|0;do{n[y>>0]=n[E>>0]|0;y=y+1|0;E=E+1|0}while((y|0)<(x|0));b=t[h>>1]|0;t[o>>1]=b;t[h>>1]=(b&65535)+65280}if(!(t[Q>>1]|0)){c=$;return}do{if(!(t[e+402>>1]|0)){if(t[Z>>1]|0)break;c=$;return}}while(0);t[e>>1]=0;t[e+412>>1]=1;c=$;return}function Ue(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var o=0,l=0,u=0,s=0,h=0,w=0;h=c;c=c+16|0;u=h+2|0;s=h;t[s>>1]=0;l=e+212|0;o=(a[l>>1]|0)+10|0;o=(o&65535|0)==80?0:o&65535;t[l>>1]=o;pt(e+52+(o<<16>>16<<1)|0,i|0,20)|0;o=0;l=159;while(1){w=t[r+(l<<1)>>1]|0;w=I(w,w)|0;w=(w|0)==1073741824?2147483647:w<<1;i=w+o|0;if((w^o|0)>-1&(i^o|0)<0){f[n>>2]=1;o=(o>>>31)+2147483647|0}else o=i;if((l|0)>0)l=l+-1|0;else break}Pn(o,u,s,n);o=t[u>>1]|0;w=o<<16>>16;i=w<<10;if((i|0)!=(w<<26>>16|0)){f[n>>2]=1;i=o<<16>>16>0?32767:-32768}t[u>>1]=i;w=t[s>>1]|0;o=w<<16>>16;if(w<<16>>16<0)o=~((o^-32)>>5);else o=o>>>5;s=e+392|0;w=(a[s>>1]|0)+1|0;w=(w&65535|0)==8?0:w&65535;t[s>>1]=w;t[e+376+(w<<16>>16<<1)>>1]=o+57015+i;c=h;return}function xe(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,o=0,l=0,u=0,s=0,c=0,h=0,w=0;c=(i|0)==4;h=(i|0)==5;w=(i|0)==6;n=f[e+408>>2]|0;e:do{if((i+-4|0)>>>0<3)s=4;else{if((n+-1|0)>>>0<2)switch(i|0){case 2:case 3:case 7:{s=4;break e}default:}t[e>>1]=0;u=0}}while(0);if((s|0)==4){e:do{if((n|0)==2){switch(i|0){case 2:case 4:case 6:case 7:break;default:{o=1;break e}}o=2}else o=1}while(0);u=(a[e>>1]|0)+1&65535;t[e>>1]=u;u=(i|0)!=5&u<<16>>16>50?2:o}l=e+398|0;if(h&(t[e+412>>1]|0)==0){t[l>>1]=0;o=0}else o=t[l>>1]|0;o=gn(o,1,r)|0;t[l>>1]=o;r=e+404|0;t[r>>1]=0;e:do{switch(i|0){case 2:case 4:case 5:case 6:case 7:{if(!((i|0)==7&(u|0)==0)){if(o<<16>>16>30){t[r>>1]=1;t[l>>1]=0;t[e+396>>1]=0;break e}o=e+396|0;n=t[o>>1]|0;if(!(n<<16>>16)){t[l>>1]=0;break e}else{t[o>>1]=(n&65535)+65535;break e}}else s=14;break}default:s=14}}while(0);if((s|0)==14)t[e+396>>1]=7;if(!u)return u|0;o=e+400|0;t[o>>1]=0;n=e+402|0;t[n>>1]=0;if(c){t[o>>1]=1;return u|0}if(h){t[o>>1]=1;t[n>>1]=1;return u|0}if(!w)return u|0;t[o>>1]=1;t[r>>1]=0;return u|0}function ze(e){e=e|0;if(!e){e=-1;return e|0}t[e>>1]=1;t[e+2>>1]=1;t[e+4>>1]=1;t[e+6>>1]=1;t[e+8>>1]=1;t[e+10>>1]=0;t[e+12>>1]=1;e=0;return e|0}function je(e,i,r,n,o){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;var a=0,l=0,u=0,s=0;s=c;c=c+16|0;u=s+2|0;l=s;a=Nn(e,5)|0;e=e+10|0;if((ct(a,t[e>>1]|0,o)|0)<<16>>16>0)a=t[e>>1]|0;a=(I(t[674+(r<<16>>16<<1)>>1]|0,a<<16>>16)|0)>>15;if((a|0)>32767){f[o>>2]=1;a=32767}t[n>>1]=a;Dn(i,u,l,o);Mn(i,t[u>>1]|0,t[l>>1]|0);c=s;return}function He(e,i,r,n,f){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;if(!(i<<16>>16)){if(r<<16>>16){i=e+12|0;if((ct(t[n>>1]|0,t[i>>1]|0,f)|0)<<16>>16>0)t[n>>1]=t[i>>1]|0}else i=e+12|0;t[i>>1]=t[n>>1]|0}t[e+10>>1]=t[n>>1]|0;f=e+2|0;t[e>>1]=t[f>>1]|0;r=e+4|0;t[f>>1]=t[r>>1]|0;f=e+6|0;t[r>>1]=t[f>>1]|0;e=e+8|0;t[f>>1]=t[e>>1]|0;t[e>>1]=t[n>>1]|0;return}function We(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var o=0;o=Nn(e,5)|0;e=e+10|0;if((ct(o,t[e>>1]|0,n)|0)<<16>>16>0)o=t[e>>1]|0;o=(I(t[688+(i<<16>>16<<1)>>1]|0,o<<16>>16)|0)>>15;if((o|0)<=32767){n=o;n=n&65535;t[r>>1]=n;return}f[n>>2]=1;n=32767;n=n&65535;t[r>>1]=n;return}function qe(e){e=e|0;if(!e){e=-1;return e|0}t[e>>1]=1640;t[e+2>>1]=1640;t[e+4>>1]=1640;t[e+6>>1]=1640;t[e+8>>1]=1640;t[e+10>>1]=0;t[e+12>>1]=16384;e=0;return e|0}function Ge(e,i,r,n,f){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;if(!(i<<16>>16)){if(r<<16>>16){i=e+12|0;if((ct(t[n>>1]|0,t[i>>1]|0,f)|0)<<16>>16>0)t[n>>1]=t[i>>1]|0}else i=e+12|0;t[i>>1]=t[n>>1]|0}n=t[n>>1]|0;i=e+10|0;t[i>>1]=n;if((ct(n,16384,f)|0)<<16>>16>0){t[i>>1]=16384;i=16384}else i=t[i>>1]|0;f=e+2|0;t[e>>1]=t[f>>1]|0;n=e+4|0;t[f>>1]=t[n>>1]|0;f=e+6|0;t[n>>1]=t[f>>1]|0;e=e+8|0;t[f>>1]=t[e>>1]|0;t[e>>1]=i;return}function Xe(e,i,r,n,o,a,l){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;l=l|0;var u=0,s=0,c=0;s=Nn(r,9)|0;c=t[r+16>>1]|0;u=c<<16>>16;r=(u+(t[r+14>>1]|0)|0)>>>1;r=(u|0)<(r<<16>>16|0)?c:r&65535;if(!(i<<16>>16>5?s<<16>>16>i<<16>>16:0))return 0;u=r<<16>>16;u=((u<<18>>18|0)==(u|0)?u<<2:u>>>15^32767)&65535;if(!(n<<16>>16>6&o<<16>>16==0))u=ct(u,r,l)|0;s=s<<16>>16>u<<16>>16?u:s;c=Yn(i)|0;u=c<<16>>16;if(c<<16>>16<0){r=0-u<<16;if((r|0)<983040)u=i<<16>>16>>(r>>16)&65535;else u=0}else{r=i<<16>>16;o=r<<u;if((o<<16>>16>>u|0)==(r|0))u=o&65535;else u=(r>>>15^32767)&65535}n=I((Sn(16383,u)|0)<<16>>16,s<<16>>16)|0;if((n|0)==1073741824){f[l>>2]=1;o=2147483647}else o=n<<1;n=ct(20,c,l)|0;u=n<<16>>16;if(n<<16>>16>0)n=n<<16>>16<31?o>>u:0;else{i=0-u<<16>>16;n=o<<i;n=(n>>i|0)==(o|0)?n:o>>31^2147483647}n=(n|0)>32767?32767:n&65535;n=a<<16>>16!=0&n<<16>>16>3072?3072:n<<16>>16;r=0;do{o=e+(r<<1)|0;u=I(t[o>>1]|0,n)|0;if((u|0)==1073741824){f[l>>2]=1;u=2147483647}else u=u<<1;t[o>>1]=u>>>11;r=r+1|0}while((r|0)!=40);return 0}function Ve(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var a=0,l=0,u=0,s=0;a=f[n+104>>2]|0;l=f[n+96>>2]|0;if(e>>>0>=8){t[r>>1]=(o[i>>0]|0)>>>4&1;t[r+2>>1]=(o[i>>0]|0)>>>5&1;t[r+4>>1]=(o[i>>0]|0)>>>6&1;t[r+6>>1]=(o[i>>0]|0)>>>7&255;a=a+(e<<1)|0;if((t[a>>1]|0)>1){e=1;n=1;l=4}else return;while(1){u=i+e|0;e=l|1;t[r+(l<<16>>16<<1)>>1]=o[u>>0]&1;t[r+(e<<16>>16<<1)>>1]=(o[u>>0]|0)>>>1&1;s=l|3;t[r+(e+1<<16>>16<<16>>16<<1)>>1]=(o[u>>0]|0)>>>2&1;t[r+(s<<16>>16<<1)>>1]=(o[u>>0]|0)>>>3&1;t[r+(s+1<<16>>16<<16>>16<<1)>>1]=(o[u>>0]|0)>>>4&1;t[r+(s+2<<16>>16<<16>>16<<1)>>1]=(o[u>>0]|0)>>>5&1;t[r+(s+3<<16>>16<<16>>16<<1)>>1]=(o[u>>0]|0)>>>6&1;t[r+(s+4<<16>>16<<16>>16<<1)>>1]=(o[u>>0]|0)>>>7&255;n=n+1<<16>>16;if(n<<16>>16<(t[a>>1]|0)){e=n<<16>>16;l=l+8<<16>>16}else break}return}s=f[(f[n+100>>2]|0)+(e<<2)>>2]|0;t[r+(t[s>>1]<<1)>>1]=(o[i>>0]|0)>>>4&1;t[r+(t[s+2>>1]<<1)>>1]=(o[i>>0]|0)>>>5&1;t[r+(t[s+4>>1]<<1)>>1]=(o[i>>0]|0)>>>6&1;t[r+(t[s+6>>1]<<1)>>1]=(o[i>>0]|0)>>>7&255;u=a+(e<<1)|0;if((t[u>>1]|0)<=1)return;n=l+(e<<1)|0;a=1;e=1;l=4;while(1){a=i+a|0;l=l<<16>>16;if((l|0)<(t[n>>1]|0)){t[r+(t[s+(l<<1)>>1]<<1)>>1]=o[a>>0]&1;l=l+1|0;if((l|0)<(t[n>>1]|0)){t[r+(t[s+(l<<1)>>1]<<1)>>1]=(o[a>>0]|0)>>>1&1;l=l+1|0;if((l|0)<(t[n>>1]|0)){t[r+(t[s+(l<<1)>>1]<<1)>>1]=(o[a>>0]|0)>>>2&1;l=l+1|0;if((l|0)<(t[n>>1]|0)){t[r+(t[s+(l<<1)>>1]<<1)>>1]=(o[a>>0]|0)>>>3&1;l=l+1|0;if((l|0)<(t[n>>1]|0)){t[r+(t[s+(l<<1)>>1]<<1)>>1]=(o[a>>0]|0)>>>4&1;l=l+1|0;if((l|0)<(t[n>>1]|0)){t[r+(t[s+(l<<1)>>1]<<1)>>1]=(o[a>>0]|0)>>>5&1;l=l+1|0;if((l|0)<(t[n>>1]|0)){t[r+(t[s+(l<<1)>>1]<<1)>>1]=(o[a>>0]|0)>>>6&1;l=l+1|0;if((l|0)<(t[n>>1]|0)){t[r+(t[s+(l<<1)>>1]<<1)>>1]=(o[a>>0]|0)>>>7&1;l=l+1|0}}}}}}}}e=e+1<<16>>16;if(e<<16>>16<(t[u>>1]|0))a=e<<16>>16;else break}return}function Ke(e,i,r,n,f){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;var o=0,a=0,l=0,u=0;switch(r<<16>>16){case 0:{u=9;while(1){l=t[e+(u<<1)>>1]|0;r=l<<16>>16;if(l<<16>>16<0)r=~((r^-4)>>2);else r=r>>>2;a=t[i+(u<<1)>>1]|0;o=a<<16>>16;if(a<<16>>16<0)a=~((o^-4)>>2);else a=o>>>2;t[n+(u<<1)>>1]=gn((l&65535)-r&65535,a&65535,f)|0;if((u|0)>0)u=u+-1|0;else break}return}case 40:{a=9;while(1){f=t[e+(a<<1)>>1]|0;r=f<<16>>16;if(f<<16>>16<0)o=~((r^-2)>>1);else o=r>>>1;f=t[i+(a<<1)>>1]|0;r=f<<16>>16;if(f<<16>>16<0)r=~((r^-2)>>1);else r=r>>>1;t[n+(a<<1)>>1]=r+o;if((a|0)>0)a=a+-1|0;else break}return}case 80:{u=9;while(1){l=t[e+(u<<1)>>1]|0;r=l<<16>>16;if(l<<16>>16<0)l=~((r^-4)>>2);else l=r>>>2;r=t[i+(u<<1)>>1]|0;o=r<<16>>16;if(r<<16>>16<0)a=~((o^-4)>>2);else a=o>>>2;t[n+(u<<1)>>1]=gn(l&65535,(r&65535)-a&65535,f)|0;if((u|0)>0)u=u+-1|0;else break}return}case 120:{t[n+18>>1]=t[i+18>>1]|0;t[n+16>>1]=t[i+16>>1]|0;t[n+14>>1]=t[i+14>>1]|0;t[n+12>>1]=t[i+12>>1]|0;t[n+10>>1]=t[i+10>>1]|0;t[n+8>>1]=t[i+8>>1]|0;t[n+6>>1]=t[i+6>>1]|0;t[n+4>>1]=t[i+4>>1]|0;t[n+2>>1]=t[i+2>>1]|0;t[n>>1]=t[i>>1]|0;return}default:return}}function Ye(e,i){e=e|0;i=i|0;if(!e){e=-1;return e|0}pt(e|0,i|0,20)|0;e=0;return e|0}function Qe(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,o=0,a=0,l=0,u=0,s=0,c=0;c=0;do{s=e+(c<<1)|0;n=t[s>>1]|0;l=n&65535;u=l<<16;n=n<<16>>16;if((n*5243|0)==1073741824){f[r>>2]=1;a=2147483647}else a=n*10486|0;o=u-a|0;if(((o^u)&(a^u)|0)<0){f[r>>2]=1;a=(l>>>15)+2147483647|0}else a=o;n=t[i+(c<<1)>>1]|0;o=n*5243|0;if((o|0)!=1073741824){n=(n*10486|0)+a|0;if((o^a|0)>0&(n^a|0)<0){f[r>>2]=1;n=(a>>>31)+2147483647|0}}else{f[r>>2]=1;n=2147483647}t[s>>1]=at(n,r)|0;c=c+1|0}while((c|0)!=10);return}function Ze(e){e=e|0;var i=0;if(!e){i=-1;return i|0}i=e+18|0;do{t[e>>1]=0;e=e+2|0}while((e|0)<(i|0));i=0;return i|0}function $e(e){e=e|0;t[e+14>>1]=1;return}function Je(e){e=e|0;t[e+14>>1]=0;return}function ei(e,i,r,n,o,a,l,u,s,h){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;l=l|0;u=u|0;s=s|0;h=h|0;var w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0;D=c;c=c+160|0;_=D+80|0;R=D;E=f[s+120>>2]|0;g=f[s+124>>2]|0;A=f[s+128>>2]|0;p=f[s+132>>2]|0;d=e+6|0;y=e+8|0;t[y>>1]=t[d>>1]|0;k=e+4|0;t[d>>1]=t[k>>1]|0;m=e+2|0;t[k>>1]=t[m>>1]|0;t[m>>1]=t[e>>1]|0;t[e>>1]=o;s=o<<16>>16<14746?o<<16>>16>9830&1:2;w=e+12|0;o=t[w>>1]|0;v=o<<15;do{if((v|0)<=536870911)if((v|0)<-536870912){f[h>>2]=1;o=-2147483648;break}else{o=o<<17;break}else{f[h>>2]=1;o=2147483647}}while(0);S=n<<16>>16;b=e+16|0;if((at(o,h)|0)<<16>>16>=n<<16>>16){v=t[b>>1]|0;if(v<<16>>16>0){v=(v&65535)+65535&65535;t[b>>1]=v}if(!(v<<16>>16)){o=(t[e>>1]|0)<9830;o=(t[m>>1]|0)<9830?o?2:1:o&1;if((t[k>>1]|0)<9830)o=(o&65535)+1&65535;if((t[d>>1]|0)<9830)o=(o&65535)+1&65535;if((t[y>>1]|0)<9830)o=(o&65535)+1&65535;v=0;s=o<<16>>16>2?0:s}}else{t[b>>1]=2;v=2}m=s<<16>>16;y=e+10|0;m=(v<<16>>16==0?(m|0)>((t[y>>1]|0)+1|0):0)?m+65535&65535:s;e=(t[e+14>>1]|0)==1?0:n<<16>>16<10?2:m<<16>>16<2&v<<16>>16>0?(m&65535)+1&65535:m;t[y>>1]=e;t[w>>1]=n;switch(i|0){case 4:case 6:case 7:break;default:if(e<<16>>16<2){v=0;s=0;d=a;w=_;while(1){if(!(t[d>>1]|0))o=0;else{s=s<<16>>16;t[R+(s<<1)>>1]=v;o=t[d>>1]|0;s=s+1&65535}t[w>>1]=o;t[d>>1]=0;v=v+1<<16>>16;if(v<<16>>16>=40){y=s;break}else{d=d+2|0;w=w+2|0}}m=e<<16>>16==0;m=(i|0)==5?m?E:g:m?A:p;if(y<<16>>16>0){k=0;do{b=t[R+(k<<1)>>1]|0;s=b<<16>>16;e=t[_+(s<<1)>>1]|0;if(b<<16>>16<40){v=e<<16>>16;d=39-b&65535;w=b;s=a+(s<<1)|0;o=m;while(1){i=(I(t[o>>1]|0,v)|0)>>>15&65535;t[s>>1]=gn(t[s>>1]|0,i,h)|0;w=w+1<<16>>16;if(w<<16>>16>=40)break;else{s=s+2|0;o=o+2|0}}if(b<<16>>16>0){s=m+(d+1<<1)|0;M=36}}else{s=m;M=36}if((M|0)==36){M=0;o=e<<16>>16;v=0;d=a;while(1){i=(I(t[s>>1]|0,o)|0)>>>15&65535;t[d>>1]=gn(t[d>>1]|0,i,h)|0;v=v+1<<16>>16;if(v<<16>>16>=b<<16>>16)break;else{d=d+2|0;s=s+2|0}}}k=k+1|0}while((k&65535)<<16>>16!=y<<16>>16)}}}k=l<<16>>16;m=S<<1;o=u<<16>>16;w=0-o<<16;s=w>>16;if(u<<16>>16>0){v=0;d=r;while(1){e=I(t[r+(v<<1)>>1]|0,k)|0;if((e|0)==1073741824){f[h>>2]=1;w=2147483647}else w=e<<1;u=I(m,t[a>>1]|0)|0;e=u+w|0;if((u^w|0)>-1&(e^w|0)<0){f[h>>2]=1;e=(w>>>31)+2147483647|0}u=e<<o;t[d>>1]=at((u>>o|0)==(e|0)?u:e>>31^2147483647,h)|0;v=v+1|0;if((v|0)==40)break;else{a=a+2|0;d=d+2|0}}c=D;return}if((w|0)<2031616){v=0;d=r;while(1){e=I(t[r+(v<<1)>>1]|0,k)|0;if((e|0)==1073741824){f[h>>2]=1;w=2147483647}else w=e<<1;u=I(m,t[a>>1]|0)|0;e=u+w|0;if((u^w|0)>-1&(e^w|0)<0){f[h>>2]=1;e=(w>>>31)+2147483647|0}t[d>>1]=at(e>>s,h)|0;v=v+1|0;if((v|0)==40)break;else{a=a+2|0;d=d+2|0}}c=D;return}else{d=0;w=r;while(1){e=I(t[r+(d<<1)>>1]|0,k)|0;if((e|0)==1073741824){f[h>>2]=1;e=2147483647}else e=e<<1;u=I(m,t[a>>1]|0)|0;if((u^e|0)>-1&(u+e^e|0)<0)f[h>>2]=1;t[w>>1]=at(0,h)|0;d=d+1|0;if((d|0)==40)break;else{a=a+2|0;w=w+2|0}}c=D;return}}function ii(e){e=e|0;if(!e){e=-1;return e|0}t[e>>1]=0;t[e+2>>1]=0;t[e+4>>1]=0;t[e+6>>1]=0;t[e+8>>1]=0;t[e+10>>1]=0;e=0;return e|0}function ri(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var f=0,o=0,a=0,l=0,u=0,s=0,c=0,h=0,w=0,d=0,v=0,b=0,k=0;if(r<<16>>16<=0)return;f=e+10|0;u=e+8|0;c=e+4|0;h=e+6|0;w=e+2|0;o=t[c>>1]|0;a=t[h>>1]|0;l=t[e>>1]|0;s=t[w>>1]|0;d=0;while(1){v=t[f>>1]|0;b=t[u>>1]|0;t[f>>1]=b;k=t[i>>1]|0;t[u>>1]=k;v=((k<<16>>16)*7699|0)+((I(l<<16>>16,-7667)|0)+(((o<<16>>16)*15836|0)+((a<<16>>16)*15836>>15))+((I(s<<16>>16,-7667)|0)>>15))+(I(b<<16>>16,-15398)|0)+((v<<16>>16)*7699|0)|0;b=v<<3;v=(b>>3|0)==(v|0)?b:v>>31^2147483647;b=v<<1;t[i>>1]=at((b>>1|0)==(v|0)?b:v>>31^2147483647,n)|0;l=t[c>>1]|0;t[e>>1]=l;s=t[h>>1]|0;t[w>>1]=s;o=v>>>16&65535;t[c>>1]=o;a=(v>>>1)-(v>>16<<15)&65535;t[h>>1]=a;d=d+1<<16>>16;if(d<<16>>16>=r<<16>>16)break;else i=i+2|0}return}function ni(e){e=e|0;if(!e)e=-1;else{t[e>>1]=0;e=0}return e|0}function ti(e,i,r,n,o){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;var a=0,l=0,u=0,s=0;u=n<<16>>16;a=i+(u+-1<<1)|0;u=u+-2|0;s=t[a>>1]|0;if(n<<16>>16<2)n=r<<16>>16;else{n=r<<16>>16;l=0;i=i+(u<<1)|0;while(1){r=(I(t[i>>1]|0,n)|0)>>15;if((r|0)>32767){f[o>>2]=1;r=32767}t[a>>1]=ct(t[a>>1]|0,r&65535,o)|0;a=a+-2|0;l=l+1<<16>>16;if((l<<16>>16|0)>(u|0))break;else i=i+-2|0}}n=(I(t[e>>1]|0,n)|0)>>15;if((n|0)<=32767){u=n;u=u&65535;l=t[a>>1]|0;o=ct(l,u,o)|0;t[a>>1]=o;t[e>>1]=s;return}f[o>>2]=1;u=32767;u=u&65535;l=t[a>>1]|0;o=ct(l,u,o)|0;t[a>>1]=o;t[e>>1]=s;return}function fi(e){e=e|0;var i=0,r=0,n=0;if(!e){n=-1;return n|0}Et(e+104|0,0,340)|0;i=e+102|0;r=e;n=r+100|0;do{t[r>>1]=0;r=r+2|0}while((r|0)<(n|0));fe(i)|0;ni(e+100|0)|0;n=0;return n|0}function oi(e,i,r,o,a){e=e|0;i=i|0;r=r|0;o=o|0;a=a|0;var l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0;g=c;c=c+96|0;m=g+22|0;y=g;p=g+44|0;pt(e+124|0,r|0,320)|0;d=p+22|0;v=e+100|0;b=e+80|0;k=e+102|0;if((i&-2|0)==6){w=0;while(1){wt(o,702,m);wt(o,722,y);h=e+104+(w+10<<1)|0;ot(m,h,e,40);u=p;l=m;i=u+22|0;do{t[u>>1]=t[l>>1]|0;u=u+2|0;l=l+2|0}while((u|0)<(i|0));u=d;i=u+22|0;do{t[u>>1]=0;u=u+2|0}while((u|0)<(i|0));ht(y,p,p,22,d,0);i=0;u=21;do{l=t[p+(u<<16>>16<<1)>>1]|0;l=I(l,l)|0;if((l|0)==1073741824){E=7;break}s=l<<1;l=s+i|0;if((s^i|0)>-1&(l^i|0)<0){f[a>>2]=1;i=(i>>>31)+2147483647|0}else i=l;u=u+-1<<16>>16}while(u<<16>>16>-1);if((E|0)==7){E=0;f[a>>2]=1}s=i>>>16&65535;l=20;i=0;u=20;while(1){l=I(t[p+(l+1<<1)>>1]|0,t[p+(l<<1)>>1]|0)|0;if((l|0)==1073741824){E=13;break}A=l<<1;l=A+i|0;if((A^i|0)>-1&(l^i|0)<0){f[a>>2]=1;i=(i>>>31)+2147483647|0}else i=l;l=(u&65535)+-1<<16>>16;if(l<<16>>16>-1){l=l<<16>>16;u=u+-1|0}else break}if((E|0)==13){E=0;f[a>>2]=1}i=i>>16;if((i|0)<1)i=0;else i=Sn((i*26214|0)>>>15&65535,s)|0;ti(v,e,i,40,a);i=r+(w<<1)|0;ht(y,e,i,40,b,1);oe(k,h,i,29491,40,a);i=(w<<16)+2621440|0;if((i|0)<10485760){w=i>>16;o=o+22|0}else break}u=e+104|0;l=e+424|0;i=u+20|0;do{n[u>>0]=n[l>>0]|0;u=u+1|0;l=l+1|0}while((u|0)<(i|0));c=g;return}else{w=0;while(1){wt(o,742,m);wt(o,762,y);h=e+104+(w+10<<1)|0;ot(m,h,e,40);u=p;l=m;i=u+22|0;do{t[u>>1]=t[l>>1]|0;u=u+2|0;l=l+2|0}while((u|0)<(i|0));u=d;i=u+22|0;do{t[u>>1]=0;u=u+2|0}while((u|0)<(i|0));ht(y,p,p,22,d,0);i=0;u=21;do{l=t[p+(u<<16>>16<<1)>>1]|0;l=I(l,l)|0;if((l|0)==1073741824){E=22;break}A=l<<1;l=A+i|0;if((A^i|0)>-1&(l^i|0)<0){f[a>>2]=1;i=(i>>>31)+2147483647|0}else i=l;u=u+-1<<16>>16}while(u<<16>>16>-1);if((E|0)==22){E=0;f[a>>2]=1}s=i>>>16&65535;l=20;i=0;u=20;while(1){l=I(t[p+(l+1<<1)>>1]|0,t[p+(l<<1)>>1]|0)|0;if((l|0)==1073741824){E=28;break}A=l<<1;l=A+i|0;if((A^i|0)>-1&(l^i|0)<0){f[a>>2]=1;i=(i>>>31)+2147483647|0}else i=l;l=(u&65535)+-1<<16>>16;if(l<<16>>16>-1){l=l<<16>>16;u=u+-1|0}else break}if((E|0)==28){E=0;f[a>>2]=1}i=i>>16;if((i|0)<1)i=0;else i=Sn((i*26214|0)>>>15&65535,s)|0;ti(v,e,i,40,a);i=r+(w<<1)|0;ht(y,e,i,40,b,1);oe(k,h,i,29491,40,a);i=(w<<16)+2621440|0;if((i|0)<10485760){w=i>>16;o=o+22|0}else break}u=e+104|0;l=e+424|0;i=u+20|0;do{n[u>>0]=n[l>>0]|0;u=u+1|0;l=l+1|0}while((u|0)<(i|0));c=g;return}}function ai(e,i){e=e|0;i=i|0;var r=0,n=0;if(!e){e=-1;return e|0}f[e>>2]=0;r=dt(1764)|0;if(!r){e=-1;return e|0}if((Se(r)|0)<<16>>16==0?(n=r+1748|0,(ii(n)|0)<<16>>16==0):0){_e(r,0)|0;fi(r+1304|0)|0;ii(n)|0;f[r+1760>>2]=0;f[e>>2]=r;e=0;return e|0}i=f[r>>2]|0;if(!i){e=-1;return e|0}vt(i);f[r>>2]=0;e=-1;return e|0}function li(e){e=e|0;var i=0;if(!e)return;i=f[e>>2]|0;if(!i)return;vt(i);f[e>>2]=0;return}function ui(e,i,r,n,o){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;var l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0;E=c;c=c+208|0;p=E+88|0;y=E;m=e+1164|0;l=f[e+1256>>2]|0;if((n+-5|0)>>>0<2){k=l+16|0;if((t[k>>1]|0)>0){b=f[(f[e+1260>>2]|0)+32>>2]|0;v=0;l=0;while(1){d=b+(v<<1)|0;h=t[d>>1]|0;if(h<<16>>16>0){s=r;w=0;u=0;while(1){u=a[s>>1]|u<<1&131070;w=w+1<<16>>16;if(w<<16>>16>=h<<16>>16)break;else s=s+2|0}u=u&65535}else u=0;t[p+(v<<1)>>1]=u;l=l+1<<16>>16;if(l<<16>>16<(t[k>>1]|0)){r=r+(t[d>>1]<<1)|0;v=l<<16>>16}else break}}}else{b=l+(i<<1)|0;if((t[b>>1]|0)>0){k=f[(f[e+1260>>2]|0)+(i<<2)>>2]|0;d=0;l=0;while(1){v=k+(d<<1)|0;h=t[v>>1]|0;if(h<<16>>16>0){s=r;w=0;u=0;while(1){u=a[s>>1]|u<<1&131070;w=w+1<<16>>16;if(w<<16>>16>=h<<16>>16)break;else s=s+2|0}u=u&65535}else u=0;t[p+(d<<1)>>1]=u;l=l+1<<16>>16;if(l<<16>>16<(t[b>>1]|0)){r=r+(t[v>>1]<<1)|0;d=l<<16>>16}else break}}}Re(e,i,p,n,o,y);oi(e+1304|0,i,o,y,m);ri(e+1748|0,o,160,m);l=0;do{e=o+(l<<1)|0;t[e>>1]=a[e>>1]&65528;l=l+1|0}while((l|0)!=160);c=E;return}function si(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var l=0,u=0,s=0;u=f[n+100>>2]|0;s=(a[(f[n+96>>2]|0)+(e<<1)>>1]|0)+65535|0;n=s&65535;l=n<<16>>16>-1;if(e>>>0<8){if(!l)return;u=f[u+(e<<2)>>2]|0;l=s<<16>>16;while(1){t[r+(t[u+(l<<1)>>1]<<1)>>1]=(o[i+(l>>3)>>0]|0)>>>(l&7^7)&1;n=n+-1<<16>>16;if(n<<16>>16>-1)l=n<<16>>16;else break}return}else{if(!l)return;l=s<<16>>16;while(1){t[r+(l<<1)>>1]=(o[i+(l>>3)>>0]|0)>>>(l&7^7)&1;n=n+-1<<16>>16;if(n<<16>>16>-1)l=n<<16>>16;else break}return}}function ci(e,i,r){e=e|0;i=i|0;r=r|0;e=Jr(e,r,31764)|0;return((Qr(i)|0|e)<<16>>16!=0)<<31>>31|0}function hi(e,i){e=e|0;i=i|0;en(e);Zr(i);return}function wi(e,i,r,o,l,u,s){e=e|0;i=i|0;r=r|0;o=o|0;l=l|0;u=u|0;s=s|0;var h=0,w=0,d=0,v=0,b=0;b=c;c=c+512|0;h=b+8|0;w=b+4|0;d=b;f[d>>2]=0;v=s<<16>>16==3;if(!((s&65535)<2|v&1)){if(s<<16>>16!=2){l=-1;c=b;return l|0}rn(e,r,o,h+2|0,d);e=f[d>>2]|0;f[u>>2]=e;$r(i,e,w);i=f[w>>2]|0;t[h>>1]=i;t[h+490>>1]=(i|0)==3?-1:r&65535;n[l>>0]=i;i=1;do{h=h+1|0;n[l+i>>0]=n[h>>0]|0;i=i+1|0}while((i|0)!=492);h=492;c=b;return h|0}rn(e,r,o,h,d);$r(i,f[d>>2]|0,w);o=f[w>>2]|0;if((o|0)!=3){i=f[d>>2]|0;f[u>>2]=i;if((i|0)==8){switch(o|0){case 1:{t[h+70>>1]=0;break}case 2:{d=h+70|0;t[d>>1]=a[d>>1]|0|1;break}default:}t[h+72>>1]=r&1;t[h+74>>1]=r>>>1&1;t[h+76>>1]=r>>>2&1;i=8}}else{f[u>>2]=15;i=15}if(v){Zi(i,h,l,(f[e+4>>2]|0)+2392|0);l=t[3404+(f[u>>2]<<16>>16<<1)>>1]|0;c=b;return l|0}switch(s<<16>>16){case 0:{Qi(i,h,l,(f[e+4>>2]|0)+2392|0);l=t[3404+(f[u>>2]<<16>>16<<1)>>1]|0;c=b;return l|0}case 1:{Yi(i,h,l,(f[e+4>>2]|0)+2392|0);l=t[3436+(f[u>>2]<<16>>16<<1)>>1]|0;c=b;return l|0}default:{l=-1;c=b;return l|0}}return 0}function di(e,i,r,n,f,o){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;o=o|0;var a=0,l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0;S=c;c=c+480|0;A=S;o=240;s=f;u=e;l=A;a=0;while(1){g=((I(t[s>>1]|0,t[u>>1]|0)|0)+16384|0)>>>15;t[l>>1]=g;g=g<<16;a=(I(g>>15,g>>16)|0)+a|0;if((a|0)<0){h=4;break}o=o+-1|0;if(!((o&65535)<<16>>16)){o=0;break}else{s=s+2|0;u=u+2|0;l=l+2|0}}if((h|0)==4){a=o&65535;l=240-o|0;if(!(a<<16>>16))o=0;else{s=a;u=f+(l<<1)|0;o=e+(l<<1)|0;a=A+(l<<1)|0;while(1){t[a>>1]=((I(t[u>>1]|0,t[o>>1]|0)|0)+16384|0)>>>15;s=s+-1<<16>>16;if(!(s<<16>>16)){o=0;break}else{u=u+2|0;o=o+2|0;a=a+2|0}}}do{u=o&65535;o=120;l=A;a=0;while(1){g=(t[l>>1]|0)>>>2;p=l+2|0;t[l>>1]=g;g=g<<16>>16;g=I(g,g)|0;E=(t[p>>1]|0)>>>2;t[p>>1]=E;E=E<<16>>16;a=((I(E,E)|0)+g<<1)+a|0;o=o+-1<<16>>16;if(!(o<<16>>16))break;else l=l+4|0}o=u+4|0}while((a|0)<1)}g=a+1|0;E=(Kn(g)|0)<<16>>16;g=g<<E;t[r>>1]=g>>>16;t[n>>1]=(g>>>1)-(g>>16<<15);g=A+478|0;s=i<<16>>16;if(i<<16>>16<=0){A=E-o|0;A=A&65535;c=S;return A|0}k=A+476|0;m=E+1|0;y=239-s|0;p=A+(236-s<<1)|0;i=s;r=r+(s<<1)|0;n=n+(s<<1)|0;while(1){h=I((y>>>1)+65535&65535,-2)|0;u=A+(h+236<<1)|0;h=p+(h<<1)|0;f=240-i|0;b=f+-1|0;l=A+(b<<1)|0;e=b>>>1&65535;f=A+(f+-2<<1)|0;s=I(t[g>>1]|0,t[l>>1]|0)|0;if(!(e<<16>>16)){h=f;u=k}else{v=k;d=g;while(1){a=l+-4|0;w=d+-4|0;s=(I(t[v>>1]|0,t[f>>1]|0)|0)+s|0;e=e+-1<<16>>16;s=(I(t[w>>1]|0,t[a>>1]|0)|0)+s|0;if(!(e<<16>>16))break;else{f=l+-6|0;v=d+-6|0;l=a;d=w}}}if(b&1)s=(I(t[u>>1]|0,t[h>>1]|0)|0)+s|0;b=s<<m;t[r>>1]=b>>>16;t[n>>1]=(b>>>1)-(b>>16<<15);if((i&65535)+-1<<16>>16<<16>>16>0){y=y+1|0;p=p+2|0;i=i+-1|0;r=r+-2|0;n=n+-2|0}else break}A=E-o|0;A=A&65535;c=S;return A|0}function vi(e,i,r,n,f,o,l,u){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;o=o|0;l=l|0;u=u|0;var s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,L=0,F=0,C=0,P=0,B=0;N=c;c=c+3440|0;O=N+3420|0;_=N+3400|0;R=N+3224|0;D=N;A=N+3320|0;M=N+3240|0;S=N+24|0;xi(r,e,A,2,u);Yr(A,i,M,R,5,_,5,u);Bi(r,M,S,u);Vr(10,5,5,A,S,_,R,D,u);i=n;u=i+80|0;do{t[i>>1]=0;i=i+2|0}while((i|0)<(u|0));t[o>>1]=65535;t[o+2>>1]=65535;t[o+4>>1]=65535;t[o+6>>1]=65535;t[o+8>>1]=65535;v=0;b=D;k=O;do{e=t[b>>1]|0;b=b+2|0;s=(e*6554|0)>>>15;h=s<<16>>16;i=n+(e<<1)|0;u=t[i>>1]|0;if((t[M+(e<<1)>>1]|0)>0){t[i>>1]=u+4096;t[k>>1]=8192;w=s}else{t[i>>1]=u+61440;t[k>>1]=-8192;w=h+8|0}k=k+2|0;d=w&65535;i=e-(s<<2)-h<<16>>16;s=o+(i<<1)|0;u=t[s>>1]|0;e=u<<16>>16;do{if(u<<16>>16>=0){h=w<<16>>16;if(!((h^e)&8)){i=o+(i+5<<1)|0;if((e|0)>(h|0)){t[i>>1]=u;t[s>>1]=d;break}else{t[i>>1]=d;break}}else{i=o+(i+5<<1)|0;if((e&7)>>>0>(h&7)>>>0){t[i>>1]=d;break}else{t[i>>1]=u;t[s>>1]=d;break}}}else t[s>>1]=d}while(0);v=v+1<<16>>16}while(v<<16>>16<10);k=O+2|0;v=O+4|0;w=O+6|0;h=O+8|0;s=O+10|0;i=O+12|0;u=O+14|0;e=O+16|0;m=O+18|0;y=40;p=r+(0-(t[D>>1]|0)<<1)|0;E=r+(0-(t[D+2>>1]|0)<<1)|0;g=r+(0-(t[D+4>>1]|0)<<1)|0;A=r+(0-(t[D+6>>1]|0)<<1)|0;S=r+(0-(t[D+8>>1]|0)<<1)|0;_=r+(0-(t[D+10>>1]|0)<<1)|0;R=r+(0-(t[D+12>>1]|0)<<1)|0;M=r+(0-(t[D+14>>1]|0)<<1)|0;n=r+(0-(t[D+16>>1]|0)<<1)|0;b=r+(0-(t[D+18>>1]|0)<<1)|0;d=f;while(1){B=(I(t[O>>1]|0,t[p>>1]|0)|0)>>7;P=(I(t[k>>1]|0,t[E>>1]|0)|0)>>7;C=(I(t[v>>1]|0,t[g>>1]|0)|0)>>7;F=(I(t[w>>1]|0,t[A>>1]|0)|0)>>7;L=(I(t[h>>1]|0,t[S>>1]|0)|0)>>7;T=(I(t[s>>1]|0,t[_>>1]|0)|0)>>7;D=(I(t[i>>1]|0,t[R>>1]|0)|0)>>7;r=(I(t[u>>1]|0,t[M>>1]|0)|0)>>>7;f=(I(t[e>>1]|0,t[n>>1]|0)|0)>>>7;t[d>>1]=(B+128+P+C+F+L+T+D+r+f+((I(t[m>>1]|0,t[b>>1]|0)|0)>>>7)|0)>>>8;y=y+-1<<16>>16;if(!(y<<16>>16))break;else{p=p+2|0;E=E+2|0;g=g+2|0;A=A+2|0;S=S+2|0;_=_+2|0;R=R+2|0;M=M+2|0;n=n+2|0;b=b+2|0;d=d+2|0}}i=0;do{u=o+(i<<1)|0;e=t[u>>1]|0;if((i|0)<5)e=(a[l+((e&7)<<1)>>1]|e&8)&65535;else e=t[l+((e&7)<<1)>>1]|0;t[u>>1]=e;i=i+1|0}while((i|0)!=10);c=N;return}function bi(e,i,r,n,o,a,l,u){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;l=l|0;u=u|0;var s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,L=0,F=0,C=0,P=0,B=0,U=0,x=0,z=0;z=c;c=c+3456|0;C=z+3448|0;L=z+3360|0;N=z+3368|0;v=z+3280|0;F=z+3200|0;T=z;B=(n&65535)<<17;x=r<<16>>16;P=r<<16>>16<40;if(P){n=B>>16;r=x;do{h=(I(t[i+(r-x<<1)>>1]|0,n)|0)>>15;if((h|0)>32767){f[u>>2]=1;h=32767}O=i+(r<<1)|0;t[O>>1]=gn(t[O>>1]|0,h&65535,u)|0;r=r+1|0}while((r&65535)<<16>>16!=40)}xi(i,e,N,1,u);Kr(N,F,v,8);Bi(i,F,T,u);O=L+2|0;t[L>>1]=0;t[O>>1]=1;e=1;h=0;d=1;v=0;w=-1;do{M=t[2830+(v<<1)>>1]|0;D=M<<16>>16;R=0;do{S=t[2834+(R<<1)>>1]|0;_=S<<16>>16;A=e;E=D;p=d;g=M;y=w;while(1){s=t[N+(E<<1)>>1]|0;k=t[T+(E*80|0)+(E<<1)>>1]|0;r=_;d=1;m=S;e=S;w=-1;while(1){n=gn(s,t[N+(r<<1)>>1]|0,u)|0;n=n<<16>>16;n=(I(n,n)|0)>>>15;b=(t[T+(E*80|0)+(r<<1)>>1]<<15)+32768+((t[T+(r*80|0)+(r<<1)>>1]|0)+k<<14)|0;if(((I(n<<16>>16,d<<16>>16)|0)-(I(b>>16,w<<16>>16)|0)<<1|0)>0){d=b>>>16&65535;e=m;w=n&65535}b=r+5|0;m=b&65535;if(m<<16>>16>=40)break;else r=b<<16>>16}if(((I(w<<16>>16,p<<16>>16)|0)-(I(d<<16>>16,y<<16>>16)|0)<<1|0)>0){t[L>>1]=g;t[O>>1]=e;h=g}else{e=A;d=p;w=y}b=E+5|0;g=b&65535;if(g<<16>>16>=40)break;else{A=e;E=b<<16>>16;p=d;y=w}}R=R+1|0}while((R|0)!=4);v=v+1|0}while((v|0)!=2);k=e;m=h;n=o;r=n+80|0;do{t[n>>1]=0;n=n+2|0}while((n|0)<(r|0));d=m;r=0;b=0;n=0;while(1){h=d<<16>>16;s=t[F+(h<<1)>>1]|0;e=(h*6554|0)>>>15;d=e<<16;v=d>>15;w=h-(v+(e<<3)<<16>>17)|0;switch(w<<16>>16|0){case 0:{v=d>>10;e=1;break}case 1:{if(!((r&65535)<<16>>16))e=0;else{v=e<<22>>16|16;e=1}break}case 2:{v=e<<22>>16|32;e=1;break}case 3:{v=e<<17>>16|1;e=0;break}case 4:{v=e<<22>>16|48;e=1;break}default:{v=e;e=w&65535}}v=v&65535;w=o+(h<<1)|0;if(s<<16>>16>0){t[w>>1]=8191;t[C+(r<<1)>>1]=32767;h=e<<16>>16;if(e<<16>>16<0){h=0-h<<16;if((h|0)<983040)h=1>>>(h>>16)&65535;else h=0}else{T=1<<h;h=(T<<16>>16>>h|0)==1?T&65535:32767}n=gn(n,h,u)|0}else{t[w>>1]=-8192;t[C+(r<<1)>>1]=-32768}h=gn(b,v,u)|0;r=r+1|0;if((r|0)==2){b=h;break}d=t[L+(r<<1)>>1]|0;b=h}t[l>>1]=n;v=C+2|0;d=t[C>>1]|0;e=0;w=i+(0-(m<<16>>16)<<1)|0;h=i+(0-(k<<16>>16)<<1)|0;do{n=I(t[w>>1]|0,d)|0;w=w+2|0;if((n|0)!=1073741824?(U=n<<1,!((n|0)>0&(U|0)<0)):0)s=U;else{f[u>>2]=1;s=2147483647}r=I(t[v>>1]|0,t[h>>1]|0)|0;h=h+2|0;if((r|0)!=1073741824){n=(r<<1)+s|0;if((r^s|0)>0&(n^s|0)<0){f[u>>2]=1;n=(s>>>31)+2147483647|0}}else{f[u>>2]=1;n=2147483647}t[a+(e<<1)>>1]=at(n,u)|0;e=e+1|0}while((e|0)!=40);if(!P){c=z;return b|0}r=B>>16;n=x;do{s=(I(t[o+(n-x<<1)>>1]|0,r)|0)>>15;if((s|0)>32767){f[u>>2]=1;s=32767}a=o+(n<<1)|0;t[a>>1]=gn(t[a>>1]|0,s&65535,u)|0;n=n+1|0}while((n&65535)<<16>>16!=40);c=z;return b|0}function ki(e,i,r,n,o,a,l,u,s,h){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;l=l|0;u=u|0;s=s|0;h=h|0;var w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0;A=c;c=c+3456|0;k=A+3360|0;m=A+3368|0;y=A+3280|0;p=A+3200|0;E=A;g=o<<16>>16;v=g<<1;if((v|0)==(g<<17>>16|0))b=v;else{f[h>>2]=1;b=o<<16>>16>0?32767:-32768}g=n<<16>>16;w=n<<16>>16<40;if(w){o=b<<16>>16;d=g;do{n=r+(d<<1)|0;v=(I(t[r+(d-g<<1)>>1]|0,o)|0)>>15;if((v|0)>32767){f[h>>2]=1;v=32767}t[n>>1]=gn(t[n>>1]|0,v&65535,h)|0;d=d+1|0}while((d&65535)<<16>>16!=40)}xi(r,i,m,1,h);Kr(m,p,y,8);Bi(r,p,E,h);mi(e,m,E,s,k);v=yi(e,k,p,a,r,l,u,h)|0;if(!w){c=A;return v|0}d=b<<16>>16;o=g;do{n=a+(o<<1)|0;w=(I(t[a+(o-g<<1)>>1]|0,d)|0)>>15;if((w|0)>32767){f[h>>2]=1;w=32767}t[n>>1]=gn(t[n>>1]|0,w&65535,h)|0;o=o+1|0}while((o&65535)<<16>>16!=40);c=A;return v|0}function mi(e,i,r,n,f){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;var o=0,l=0,u=0,s=0,c=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0;A=f+2|0;t[f>>1]=0;t[A>>1]=1;E=e<<16>>16<<1;o=1;g=0;e=-1;do{p=(g<<3)+E<<16>>16;s=t[n+(p<<1)>>1]|0;p=t[n+((p|1)<<1)>>1]|0;l=s<<16>>16;e:do{if(s<<16>>16<40){y=p<<16>>16;if(p<<16>>16<40)m=o;else while(1){if((e<<16>>16|0)<(0-(o<<16>>16)|0)){t[f>>1]=s;t[A>>1]=p;u=1;e=-1}else u=o;o=l+5|0;s=o&65535;if(s<<16>>16>=40){o=u;break e}else{l=o<<16>>16;o=u}}while(1){b=t[r+(l*80|0)+(l<<1)>>1]|0;v=a[i+(l<<1)>>1]|0;d=y;o=1;k=p;u=p;c=-1;while(1){w=(a[i+(d<<1)>>1]|0)+v<<16>>16;w=(I(w,w)|0)>>>15;h=(t[r+(l*80|0)+(d<<1)>>1]<<15)+32768+((t[r+(d*80|0)+(d<<1)>>1]|0)+b<<14)|0;if(((I(w<<16>>16,o<<16>>16)|0)-(I(h>>16,c<<16>>16)|0)<<1|0)>0){o=h>>>16&65535;u=k;c=w&65535}h=d+5|0;k=h&65535;if(k<<16>>16>=40)break;else d=h<<16>>16}if(((I(c<<16>>16,m<<16>>16)|0)-(I(o<<16>>16,e<<16>>16)|0)<<1|0)>0){t[f>>1]=s;t[A>>1]=u;e=c}else o=m;l=l+5|0;s=l&65535;if(s<<16>>16>=40)break;else{l=l<<16>>16;m=o}}}}while(0);g=g+1|0}while((g|0)!=2);return}function yi(e,i,r,n,o,a,l,u){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;l=l|0;u=u|0;var s=0,c=0,h=0,w=0,d=0,v=0;s=n;c=s+80|0;do{t[s>>1]=0;s=s+2|0}while((s|0)<(c|0));s=t[i>>1]|0;d=(s*6554|0)>>>15;c=d<<16>>16;w=(748250>>>((s+(I(c,-5)|0)<<16>>16)+((e<<16>>16)*5|0)|0)&1|0)==0;h=(t[r+(s<<1)>>1]|0)>0;v=h?32767:-32768;t[n+(s<<1)>>1]=h?8191:-8192;s=i+2|0;e=t[s>>1]|0;n=n+(e<<1)|0;if((t[r+(e<<1)>>1]|0)>0){t[n>>1]=8191;r=32767;n=(h&1|2)&65535}else{t[n>>1]=-8192;r=-32768;n=h&1}d=((e*6554|0)>>>15<<3)+(w?d:c+64|0)&65535;t[l>>1]=n;w=0;h=o+(0-(t[i>>1]|0)<<1)|0;n=o+(0-(t[s>>1]|0)<<1)|0;do{s=I(v,t[h>>1]|0)|0;h=h+2|0;if((s|0)==1073741824){f[u>>2]=1;e=2147483647}else e=s<<1;c=I(r,t[n>>1]|0)|0;n=n+2|0;if((c|0)!=1073741824){s=(c<<1)+e|0;if((c^e|0)>0&(s^e|0)<0){f[u>>2]=1;s=(e>>>31)+2147483647|0}}else{f[u>>2]=1;s=2147483647}t[a+(w<<1)>>1]=at(s,u)|0;w=w+1|0}while((w|0)!=40);return d|0}function pi(e,i,r,n,o,l,u,s){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;l=l|0;u=u|0;s=s|0;var h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,L=0,F=0,C=0,P=0,B=0,U=0,x=0,z=0,j=0,H=0,W=0,q=0,G=0,X=0,V=0;V=c;c=c+3440|0;x=V+3360|0;z=V+3280|0;H=V+3200|0;j=V;q=(n&65535)<<17;X=r<<16>>16;W=r<<16>>16<40;if(W){r=q>>16;h=X;do{n=(I(t[i+(h-X<<1)>>1]|0,r)|0)>>15;if((n|0)>32767){f[s>>2]=1;n=32767}U=i+(h<<1)|0;t[U>>1]=gn(t[U>>1]|0,n&65535,s)|0;h=h+1|0}while((h&65535)<<16>>16!=40)}xi(i,e,x,1,s);Kr(x,H,z,6);Bi(i,H,j,s);U=1;w=2;d=1;n=0;h=1;e=-1;v=1;while(1){B=2;m=2;while(1){F=0;C=0;P=v;L=m;while(1){if(C<<16>>16<40){D=P<<16>>16;O=P<<16>>16<40;N=L<<16>>16;T=L<<16>>16<40;R=C<<16>>16;M=C;while(1){if((t[z+(R<<1)>>1]|0)>-1){A=t[j+(R*80|0)+(R<<1)>>1]|0;if(O){S=a[x+(R<<1)>>1]|0;g=D;k=1;_=P;r=P;m=0;b=-1;while(1){p=(a[x+(g<<1)>>1]|0)+S|0;E=p<<16>>16;E=(I(E,E)|0)>>>15;y=(t[j+(R*80|0)+(g<<1)>>1]<<15)+32768+((t[j+(g*80|0)+(g<<1)>>1]|0)+A<<14)|0;if(((I(E<<16>>16,k<<16>>16)|0)-(I(y>>16,b<<16>>16)|0)<<1|0)>0){k=y>>>16&65535;r=_;m=p&65535;b=E&65535}y=g+5|0;_=y&65535;if(_<<16>>16>=40)break;else g=y<<16>>16}}else{k=1;r=P;m=0}if(T){S=m&65535;_=r<<16>>16;g=(k<<16>>16<<14)+32768|0;E=N;m=1;A=L;b=L;k=-1;while(1){p=(a[x+(E<<1)>>1]|0)+S<<16>>16;p=(I(p,p)|0)>>>15;y=g+(t[j+(E*80|0)+(E<<1)>>1]<<12)+((t[j+(R*80|0)+(E<<1)>>1]|0)+(t[j+(_*80|0)+(E<<1)>>1]|0)<<13)|0;if(((I(p<<16>>16,m<<16>>16)|0)-(I(y>>16,k<<16>>16)|0)<<1|0)>0){m=y>>>16&65535;b=A;k=p&65535}y=E+5|0;A=y&65535;if(A<<16>>16>=40){g=m;E=k;break}else E=y<<16>>16}}else{g=1;b=L;E=-1}m=I(E<<16>>16,h<<16>>16)|0;if((m|0)==1073741824){f[s>>2]=1;y=2147483647}else y=m<<1;m=I(g<<16>>16,e<<16>>16)|0;if((m|0)==1073741824){f[s>>2]=1;k=2147483647}else k=m<<1;m=y-k|0;if(((m^y)&(k^y)|0)<0){f[s>>2]=1;m=(y>>>31)+2147483647|0}_=(m|0)>0;w=_?b:w;d=_?r:d;n=_?M:n;h=_?g:h;e=_?E:e}m=R+5|0;M=m&65535;if(M<<16>>16>=40)break;else R=m<<16>>16}}F=F+1<<16>>16;if(F<<16>>16>=3)break;else{T=L;L=P;P=C;C=T}}r=B+2|0;m=r&65535;if(m<<16>>16>=5)break;else B=r&65535}r=U+2|0;v=r&65535;if(v<<16>>16<4)U=r&65535;else{m=w;w=d;break}}r=o;h=r+80|0;do{t[r>>1]=0;r=r+2|0}while((r|0)<(h|0));E=n<<16>>16;e=t[H+(E<<1)>>1]|0;n=(E*6554|0)>>>15;r=n<<16;h=E-(((r>>16)*327680|0)>>>16)|0;switch(h<<16>>16|0){case 1:{n=r>>12;break}case 2:{n=r>>8;h=2;break}case 3:{n=n<<20>>16|8;h=1;break}case 4:{n=n<<24>>16|128;h=2;break}default:}r=o+(E<<1)|0;if(e<<16>>16>0){t[r>>1]=8191;_=32767;d=65536<<(h<<16>>16)>>>16&65535}else{t[r>>1]=-8192;_=-32768;d=0}y=w<<16>>16;w=t[H+(y<<1)>>1]|0;r=(y*6554|0)>>>15;h=r<<16;e=y-(((h>>16)*327680|0)>>>16)|0;switch(e<<16>>16|0){case 1:{r=h>>12;break}case 2:{r=h>>8;e=2;break}case 3:{r=r<<20>>16|8;e=1;break}case 4:{r=r<<24>>16|128;e=2;break}default:}h=o+(y<<1)|0;if(w<<16>>16>0){t[h>>1]=8191;p=32767;d=(65536<<(e<<16>>16)>>>16)+(d&65535)&65535}else{t[h>>1]=-8192;p=-32768}v=r+n|0;k=m<<16>>16;w=t[H+(k<<1)>>1]|0;n=(k*6554|0)>>>15;r=n<<16;h=k-(((r>>16)*327680|0)>>>16)|0;switch(h<<16>>16|0){case 1:{r=r>>12;break}case 2:{r=r>>8;h=2;break}case 3:{r=n<<20>>16|8;h=1;break}case 4:{r=n<<24>>16|128;h=2;break}default:r=n}n=o+(k<<1)|0;if(w<<16>>16>0){t[n>>1]=8191;m=32767;n=(65536<<(h<<16>>16)>>>16)+(d&65535)&65535}else{t[n>>1]=-8192;m=-32768;n=d}b=v+r|0;t[u>>1]=n;d=0;v=i+(0-E<<1)|0;e=i+(0-y<<1)|0;w=i+(0-k<<1)|0;do{n=I(t[v>>1]|0,_)|0;v=v+2|0;if((n|0)!=1073741824?(G=n<<1,!((n|0)>0&(G|0)<0)):0)h=G;else{f[s>>2]=1;h=2147483647}n=I(t[e>>1]|0,p)|0;e=e+2|0;if((n|0)!=1073741824){r=(n<<1)+h|0;if((n^h|0)>0&(r^h|0)<0){f[s>>2]=1;r=(h>>>31)+2147483647|0}}else{f[s>>2]=1;r=2147483647}h=I(t[w>>1]|0,m)|0;w=w+2|0;if((h|0)!=1073741824){n=(h<<1)+r|0;if((h^r|0)>0&(n^r|0)<0){f[s>>2]=1;n=(r>>>31)+2147483647|0}}else{f[s>>2]=1;n=2147483647}t[l+(d<<1)>>1]=at(n,s)|0;d=d+1|0}while((d|0)!=40);n=b&65535;if(!W){c=V;return n|0}h=q>>16;r=X;do{e=(I(t[o+(r-X<<1)>>1]|0,h)|0)>>15;if((e|0)>32767){f[s>>2]=1;e=32767}l=o+(r<<1)|0;t[l>>1]=gn(t[l>>1]|0,e&65535,s)|0;r=r+1|0}while((r&65535)<<16>>16!=40);c=V;return n|0}function Ei(e,i,r,n,o,l,u,s,h){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;l=l|0;u=u|0;s=s|0;h=h|0;var w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,L=0,F=0,C=0,P=0,B=0,U=0,x=0,z=0,j=0,H=0,W=0,q=0,G=0,X=0,V=0,K=0,Y=0,Q=0,Z=0,$=0,J=0,ee=0,ie=0,re=0,ne=0;ne=c;c=c+3456|0;$=ne+3448|0;Q=ne+3360|0;V=ne+3368|0;K=ne+3280|0;Z=ne+3200|0;Y=ne;ee=(n&65535)<<17;re=r<<16>>16;J=r<<16>>16<40;if(J){r=ee>>16;w=re;do{n=(I(t[i+(w-re<<1)>>1]|0,r)|0)>>15;if((n|0)>32767){f[h>>2]=1;n=32767}X=i+(w<<1)|0;t[X>>1]=gn(t[X>>1]|0,n&65535,h)|0;w=w+1|0}while((w&65535)<<16>>16!=40)}xi(i,e,V,1,h);Kr(V,Z,K,4);Bi(i,Z,Y,h);q=Q+2|0;t[Q>>1]=0;G=Q+4|0;t[q>>1]=1;X=Q+6|0;t[G>>1]=2;t[X>>1]=3;k=3;v=2;d=1;n=0;r=1;w=-1;b=3;do{x=0;z=0;j=b;H=1;W=2;while(1){if(z<<16>>16<40){L=H<<16>>16;F=H<<16>>16<40;C=W<<16>>16;P=W<<16>>16<40;B=j<<16>>16;U=j<<16>>16<40;T=z<<16>>16;N=v;D=d;M=r;O=z;while(1){if((t[K+(T<<1)>>1]|0)>-1){y=t[Y+(T*80|0)+(T<<1)>>1]|0;if(F){m=a[V+(T<<1)>>1]|0;p=L;_=1;v=H;d=H;A=0;S=-1;while(1){g=(a[V+(p<<1)>>1]|0)+m|0;E=g<<16>>16;E=(I(E,E)|0)>>>15;R=(t[Y+(T*80|0)+(p<<1)>>1]<<15)+32768+((t[Y+(p*80|0)+(p<<1)>>1]|0)+y<<14)|0;if(((I(E<<16>>16,_<<16>>16)|0)-(I(R>>16,S<<16>>16)|0)<<1|0)>0){_=R>>>16&65535;d=v;A=g&65535;S=E&65535}R=p+5|0;v=R&65535;if(v<<16>>16>=40)break;else p=R<<16>>16}}else{_=1;d=H;A=0}if(P){r=A&65535;e=d<<16>>16;y=(_<<16>>16<<14)+32768|0;p=C;R=1;m=W;v=W;S=0;A=-1;while(1){g=(a[V+(p<<1)>>1]|0)+r|0;E=g<<16>>16;E=(I(E,E)|0)>>>15;_=y+(t[Y+(p*80|0)+(p<<1)>>1]<<12)+((t[Y+(T*80|0)+(p<<1)>>1]|0)+(t[Y+(e*80|0)+(p<<1)>>1]|0)<<13)|0;if(((I(E<<16>>16,R<<16>>16)|0)-(I(_>>16,A<<16>>16)|0)<<1|0)>0){R=_>>>16&65535;v=m;S=g&65535;A=E&65535}_=p+5|0;m=_&65535;if(m<<16>>16>=40)break;else p=_<<16>>16}}else{R=1;v=W;S=0}if(U){y=S&65535;m=v<<16>>16;e=d<<16>>16;E=(R&65535)<<16|32768;g=B;r=1;p=j;_=j;R=-1;while(1){A=(a[V+(g<<1)>>1]|0)+y<<16>>16;A=(I(A,A)|0)>>>15;S=(t[Y+(g*80|0)+(g<<1)>>1]<<12)+E+((t[Y+(e*80|0)+(g<<1)>>1]|0)+(t[Y+(m*80|0)+(g<<1)>>1]|0)+(t[Y+(T*80|0)+(g<<1)>>1]|0)<<13)|0;if(((I(A<<16>>16,r<<16>>16)|0)-(I(S>>16,R<<16>>16)|0)<<1|0)>0){r=S>>>16&65535;_=p;R=A&65535}S=g+5|0;p=S&65535;if(p<<16>>16>=40)break;else g=S<<16>>16}}else{r=1;_=j;R=-1}if(((I(R<<16>>16,M<<16>>16)|0)-(I(r<<16>>16,w<<16>>16)|0)<<1|0)>0){t[Q>>1]=O;t[q>>1]=d;t[G>>1]=v;t[X>>1]=_;k=_;n=O;w=R}else{v=N;d=D;r=M}}else{v=N;d=D;r=M}g=T+5|0;O=g&65535;if(O<<16>>16>=40)break;else{T=g<<16>>16;N=v;D=d;M=r}}}x=x+1<<16>>16;if(x<<16>>16>=4)break;else{B=W;U=j;W=H;H=z;j=B;z=U}}b=b+1<<16>>16}while(b<<16>>16<5);R=k;_=v;S=d;A=n;n=o;r=n+80|0;do{t[n>>1]=0;n=n+2|0}while((n|0)<(r|0));e=A;r=0;w=0;n=0;while(1){v=e<<16>>16;b=t[Z+(v<<1)>>1]|0;e=v*13108>>16;d=v-((e*327680|0)>>>16)|0;e=t[s+(e<<1)>>1]|0;switch(d<<16>>16|0){case 1:{k=e<<16>>16<<3&65535;break}case 2:{k=e<<16>>16<<6&65535;break}case 3:{k=e<<16>>16<<10&65535;break}case 4:{k=((e&65535)<<10|512)&65535;d=3;break}default:k=e}e=o+(v<<1)|0;if(b<<16>>16>0){t[e>>1]=8191;e=32767;n=(65536<<(d<<16>>16)>>>16)+(n&65535)&65535}else{t[e>>1]=-8192;e=-32768}t[$+(r<<1)>>1]=e;w=(k&65535)+(w&65535)|0;r=r+1|0;if((r|0)==4){g=w;break}e=t[Q+(r<<1)>>1]|0}t[u>>1]=n;y=$+2|0;p=$+4|0;E=$+6|0;e=t[$>>1]|0;m=0;d=i+(0-(A<<16>>16)<<1)|0;v=i+(0-(S<<16>>16)<<1)|0;b=i+(0-(_<<16>>16)<<1)|0;k=i+(0-(R<<16>>16)<<1)|0;do{n=I(t[d>>1]|0,e)|0;d=d+2|0;if((n|0)!=1073741824?(ie=n<<1,!((n|0)>0&(ie|0)<0)):0)w=ie;else{f[h>>2]=1;w=2147483647}n=I(t[y>>1]|0,t[v>>1]|0)|0;v=v+2|0;if((n|0)!=1073741824){r=(n<<1)+w|0;if((n^w|0)>0&(r^w|0)<0){f[h>>2]=1;r=(w>>>31)+2147483647|0}}else{f[h>>2]=1;r=2147483647}n=I(t[p>>1]|0,t[b>>1]|0)|0;b=b+2|0;if((n|0)!=1073741824){w=(n<<1)+r|0;if((n^r|0)>0&(w^r|0)<0){f[h>>2]=1;w=(r>>>31)+2147483647|0}}else{f[h>>2]=1;w=2147483647}r=I(t[E>>1]|0,t[k>>1]|0)|0;k=k+2|0;if((r|0)!=1073741824){n=(r<<1)+w|0;if((r^w|0)>0&(n^w|0)<0){f[h>>2]=1;n=(w>>>31)+2147483647|0}}else{f[h>>2]=1;n=2147483647}t[l+(m<<1)>>1]=at(n,h)|0;m=m+1|0}while((m|0)!=40);n=g&65535;if(((re<<16)+-2621440|0)>-1|J^1){c=ne;return n|0}w=ee>>16;r=re;do{e=(I(t[o+(r-re<<1)>>1]|0,w)|0)>>15;if((e|0)>32767){f[h>>2]=1;e=32767}l=o+(r<<1)|0;t[l>>1]=gn(t[l>>1]|0,e&65535,h)|0;r=r+1|0}while((r&65535)<<16>>16!=40);c=ne;return n|0}function gi(e,i,r,n,o,l,u){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;l=l|0;u=u|0;var s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,L=0,F=0,C=0,P=0,B=0,U=0;U=c;c=c+3440|0;y=U+3424|0;L=U+3408|0;F=U+3240|0;p=U+3224|0;N=U+3328|0;m=U+3248|0;T=U+24|0;B=U+16|0;P=U;Ui(r,e,N,2,4,4,u);Yr(N,i,m,F,4,L,4,u);Bi(r,m,T,u);Vr(8,4,4,N,T,L,F,p,u);i=n;e=i+80|0;do{t[i>>1]=0;i=i+2|0}while((i|0)<(e|0));t[P>>1]=-1;t[B>>1]=-1;D=P+2|0;t[D>>1]=-1;O=B+2|0;t[O>>1]=-1;N=P+4|0;t[N>>1]=-1;T=B+4|0;t[T>>1]=-1;F=P+6|0;t[F>>1]=-1;L=B+6|0;t[L>>1]=-1;b=0;do{d=t[p+(b<<1)>>1]|0;i=d>>>2;h=i&65535;e=d&3;w=(t[m+(d<<1)>>1]|0)>0;d=n+(d<<1)|0;k=w&1^1;t[d>>1]=(a[d>>1]|0)+(w?8191:57345);t[y+(b<<1)>>1]=w?32767:-32768;w=P+(e<<1)|0;d=t[w>>1]|0;do{if(d<<16>>16>=0){v=B+(e<<1)|0;s=(d<<16>>16|0)<=(i<<16>>16|0);i=P+((e|4)<<1)|0;if((k&65535|0)==(a[v>>1]&1|0))if(s){t[i>>1]=h;break}else{t[i>>1]=d;t[w>>1]=h;t[v>>1]=k;break}else if(s){t[i>>1]=d;t[w>>1]=h;t[v>>1]=k;break}else{t[i>>1]=h;break}}else{t[w>>1]=h;t[B+(e<<1)>>1]=k}}while(0);b=b+1|0}while((b|0)!=8);E=y+2|0;g=y+4|0;A=y+6|0;S=y+8|0;_=y+10|0;R=y+12|0;M=y+14|0;y=t[y>>1]|0;b=0;v=r+(0-(t[p>>1]|0)<<1)|0;d=r+(0-(t[p+2>>1]|0)<<1)|0;w=r+(0-(t[p+4>>1]|0)<<1)|0;h=r+(0-(t[p+6>>1]|0)<<1)|0;i=r+(0-(t[p+8>>1]|0)<<1)|0;e=r+(0-(t[p+10>>1]|0)<<1)|0;s=r+(0-(t[p+12>>1]|0)<<1)|0;r=r+(0-(t[p+14>>1]|0)<<1)|0;do{k=I(t[v>>1]|0,y)|0;v=v+2|0;if((k|0)!=1073741824?(C=k<<1,!((k|0)>0&(C|0)<0)):0)k=C;else{f[u>>2]=1;k=2147483647}m=I(t[E>>1]|0,t[d>>1]|0)|0;d=d+2|0;if((m|0)!=1073741824){n=(m<<1)+k|0;if((m^k|0)>0&(n^k|0)<0){f[u>>2]=1;k=(k>>>31)+2147483647|0}else k=n}else{f[u>>2]=1;k=2147483647}m=I(t[g>>1]|0,t[w>>1]|0)|0;w=w+2|0;if((m|0)!=1073741824){n=(m<<1)+k|0;if((m^k|0)>0&(n^k|0)<0){f[u>>2]=1;n=(k>>>31)+2147483647|0}}else{f[u>>2]=1;n=2147483647}m=I(t[A>>1]|0,t[h>>1]|0)|0;h=h+2|0;if((m|0)!=1073741824){k=(m<<1)+n|0;if((m^n|0)>0&(k^n|0)<0){f[u>>2]=1;k=(n>>>31)+2147483647|0}}else{f[u>>2]=1;k=2147483647}m=I(t[S>>1]|0,t[i>>1]|0)|0;i=i+2|0;if((m|0)!=1073741824){n=(m<<1)+k|0;if((m^k|0)>0&(n^k|0)<0){f[u>>2]=1;n=(k>>>31)+2147483647|0}}else{f[u>>2]=1;n=2147483647}m=I(t[_>>1]|0,t[e>>1]|0)|0;e=e+2|0;if((m|0)!=1073741824){k=(m<<1)+n|0;if((m^n|0)>0&(k^n|0)<0){f[u>>2]=1;k=(n>>>31)+2147483647|0}}else{f[u>>2]=1;k=2147483647}m=I(t[R>>1]|0,t[s>>1]|0)|0;s=s+2|0;if((m|0)!=1073741824){n=(m<<1)+k|0;if((m^k|0)>0&(n^k|0)<0){f[u>>2]=1;n=(k>>>31)+2147483647|0}}else{f[u>>2]=1;n=2147483647}m=I(t[M>>1]|0,t[r>>1]|0)|0;r=r+2|0;if((m|0)!=1073741824){k=(m<<1)+n|0;if((m^n|0)>0&(k^n|0)<0){f[u>>2]=1;k=(n>>>31)+2147483647|0}}else{f[u>>2]=1;k=2147483647}t[o+(b<<1)>>1]=at(k,u)|0;b=b+1|0}while((b|0)!=40);t[l>>1]=t[B>>1]|0;t[l+2>>1]=t[O>>1]|0;t[l+4>>1]=t[T>>1]|0;t[l+6>>1]=t[L>>1]|0;e=t[P>>1]|0;i=t[P+8>>1]|0;s=t[D>>1]|0;t[l+8>>1]=i<<1&2|e&1|s<<2&4|(((i>>1)*327680|0)+(e>>>1<<16)+(I(s>>1,1638400)|0)|0)>>>13&65528;s=t[N>>1]|0;e=t[P+12>>1]|0;i=t[P+10>>1]|0;t[l+10>>1]=e<<1&2|s&1|i<<2&4|(((e>>1)*327680|0)+(s>>>1<<16)+(I(i>>1,1638400)|0)|0)>>>13&65528;i=t[P+14>>1]|0;s=t[F>>1]|0;e=s<<16>>16>>>1;if(!(i&2)){o=e;u=i<<16>>16;B=u>>1;B=B*327680|0;o=o<<16;B=o+B|0;B=B<<5;B=B>>16;B=B|12;B=B*2622|0;B=B>>>16;o=s&65535;o=o&1;u=u<<17;u=u&131072;B=B<<18;u=B|u;u=u>>>16;o=u|o;o=o&65535;l=l+12|0;t[l>>1]=o;c=U;return}o=4-(e<<16>>16)|0;u=i<<16>>16;B=u>>1;B=B*327680|0;o=o<<16;B=o+B|0;B=B<<5;B=B>>16;B=B|12;B=B*2622|0;B=B>>>16;o=s&65535;o=o&1;u=u<<17;u=u&131072;B=B<<18;u=B|u;u=u>>>16;o=u|o;o=o&65535;l=l+12|0;t[l>>1]=o;c=U;return}function Ai(e,i,r,n,o){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;var a=0,l=0,u=0,s=0,c=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0;k=r<<16>>16;a=0-k|0;r=o+(a<<2)|0;o=((k-(n<<16>>16)|0)>>>2)+1&65535;if(o<<16>>16<=0)return;k=i<<16>>16>>>1&65535;if(!(k<<16>>16)){while(1){f[r>>2]=0;f[r+4>>2]=0;f[r+8>>2]=0;f[r+12>>2]=0;if(o<<16>>16>1){r=r+16|0;o=o+-1<<16>>16}else break}return}b=e+(a<<1)|0;while(1){c=b+4|0;w=t[c>>1]|0;u=t[b>>1]|0;h=w;s=k;d=e;v=b;b=b+8|0;l=0;a=0;n=0;i=0;while(1){y=t[d>>1]|0;m=(I(u<<16>>16,y)|0)+l|0;l=t[v+2>>1]|0;a=(I(l,y)|0)+a|0;u=(I(h<<16>>16,y)|0)+n|0;n=t[v+6>>1]|0;h=(I(n,y)|0)+i|0;i=t[d+2>>1]|0;l=m+(I(i,l)|0)|0;a=a+(I(w<<16>>16,i)|0)|0;c=c+4|0;n=u+(I(i,n)|0)|0;u=t[c>>1]|0;i=h+(I(u<<16>>16,i)|0)|0;s=s+-1<<16>>16;if(!(s<<16>>16))break;y=w;h=u;w=t[v+8>>1]|0;d=d+4|0;v=v+4|0;u=y}f[r>>2]=l<<1;f[r+4>>2]=a<<1;f[r+8>>2]=n<<1;f[r+12>>2]=i<<1;if(o<<16>>16<=1)break;else{r=r+16|0;o=o+-1<<16>>16}}return}function Si(e,i,r,n,o,l,u,s,h){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;l=l|0;u=u|0;s=s|0;h=h|0;var w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0;S=c;c=c+16|0;g=S+2|0;A=S;do{if(o<<16>>16>0){m=n<<16>>16;p=0;v=0;n=0;d=0;y=0;while(1){w=t[e+(p<<1)>>1]|0;b=w<<16>>16;v=(I(b,b)|0)+v|0;b=t[i+(p<<1)>>1]|0;n=(I(b,b)|0)+n|0;d=(I(t[r+(p<<1)>>1]|0,b)|0)+d|0;b=I(b,m)|0;if((b|0)==1073741824){f[h>>2]=1;k=2147483647}else k=b<<1;b=k<<1;b=(ct(w,at((b>>1|0)==(k|0)?b:k>>31^2147483647,h)|0,h)|0)<<16>>16;b=I(b,b)|0;if((b|0)!=1073741824){w=(b<<1)+y|0;if((b^y|0)>0&(w^y|0)<0){f[h>>2]=1;w=(y>>>31)+2147483647|0}}else{f[h>>2]=1;w=2147483647}p=p+1|0;if((p&65535)<<16>>16==o<<16>>16){y=w;break}else y=w}v=v<<1;n=n<<1;d=d<<1;if((v|0)>=0){if((v|0)<400){w=y;E=14;break}}else{f[h>>2]=1;v=2147483647}k=Kn(v)|0;b=k<<16>>16;if(k<<16>>16>0){w=v<<b;if((w>>b|0)!=(v|0))w=v>>31^2147483647}else{w=0-b<<16;if((w|0)<2031616)w=v>>(w>>16);else w=0}t[l>>1]=w>>>16;v=n;m=d;w=y;n=15-(k&65535)&65535}else{n=0;d=0;w=0;E=14}}while(0);if((E|0)==14){t[l>>1]=0;v=n;m=d;n=-15}t[u>>1]=n;if((v|0)<0){f[h>>2]=1;v=2147483647}b=Kn(v)|0;d=b<<16>>16;if(b<<16>>16>0){n=v<<d;if((n>>d|0)!=(v|0))n=v>>31^2147483647}else{n=0-d<<16;if((n|0)<2031616)n=v>>(n>>16);else n=0}t[l+2>>1]=n>>>16;t[u+2>>1]=15-(b&65535);v=Kn(m)|0;d=v<<16>>16;if(v<<16>>16>0){n=m<<d;if((n>>d|0)!=(m|0))n=m>>31^2147483647}else{n=0-d<<16;if((n|0)<2031616)n=m>>(n>>16);else n=0}t[l+4>>1]=n>>>16;t[u+4>>1]=2-(v&65535);v=Kn(w)|0;n=v<<16>>16;if(v<<16>>16>0){d=w<<n;if((d>>n|0)!=(w|0))d=w>>31^2147483647}else{n=0-n<<16;if((n|0)<2031616)d=w>>(n>>16);else d=0}n=d>>>16&65535;w=15-(v&65535)&65535;t[l+6>>1]=n;t[u+6>>1]=w;if((d>>16|0)<=0){h=0;t[s>>1]=h;c=S;return}d=t[l>>1]|0;if(!(d<<16>>16)){h=0;t[s>>1]=h;c=S;return}n=Sn(lt(d,1,h)|0,n)|0;n=(n&65535)<<16;d=((ct(w,t[u>>1]|0,h)|0)&65535)+3|0;w=d&65535;d=d<<16>>16;if(w<<16>>16>0)w=w<<16>>16<31?n>>d:0;else{u=0-d<<16>>16;w=n<<u;w=(w>>u|0)==(n|0)?w:n>>31^2147483647}Pn(w,g,A,h);A=hr((a[g>>1]|0)+65509&65535,t[A>>1]|0,h)|0;g=A<<13;h=at((g>>13|0)==(A|0)?g:A>>31^2147483647,h)|0;t[s>>1]=h;c=S;return}function _i(e,i,r,n,o,l,u,s,h,w,d){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;l=l|0;u=u|0;s=s|0;h=h|0;w=w|0;d=d|0;var v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0;S=c;c=c+80|0;E=S;t[u>>1]=t[l>>1]|0;t[s>>1]=t[l+2>>1]|0;k=t[l+4>>1]|0;if(k<<16>>16==-32768)k=32767;else k=0-(k&65535)&65535;t[u+2>>1]=k;t[s+2>>1]=(a[l+6>>1]|0)+1;switch(e|0){case 0:case 5:{p=0;b=0;v=0;y=0;break}default:{p=0;b=1;v=1;y=1}}while(1){m=(t[o+(p<<1)>>1]|0)>>>3;t[E+(p<<1)>>1]=m;m=m<<16>>16;k=I(m,m)|0;if((k|0)!=1073741824){l=(k<<1)+b|0;if((k^b|0)>0&(l^b|0)<0){f[d>>2]=1;b=(b>>>31)+2147483647|0}else b=l}else{f[d>>2]=1;b=2147483647}k=I(t[i+(p<<1)>>1]|0,m)|0;if((k|0)!=1073741824){l=(k<<1)+v|0;if((k^v|0)>0&(l^v|0)<0){f[d>>2]=1;v=(v>>>31)+2147483647|0}else v=l}else{f[d>>2]=1;v=2147483647}k=I(t[n+(p<<1)>>1]|0,m)|0;if((k|0)!=1073741824){l=(k<<1)+y|0;if((k^y|0)>0&(l^y|0)<0){f[d>>2]=1;l=(y>>>31)+2147483647|0}}else{f[d>>2]=1;l=2147483647}p=p+1|0;if((p|0)==40){n=l;m=v;break}else y=l}v=Kn(b)|0;l=v<<16>>16;if(v<<16>>16>0){k=b<<l;if((k>>l|0)!=(b|0))k=b>>31^2147483647}else{k=0-l<<16;if((k|0)<2031616)k=b>>(k>>16);else k=0}o=u+4|0;t[o>>1]=k>>>16;i=s+4|0;t[i>>1]=-3-(v&65535);b=Kn(m)|0;l=b<<16>>16;if(b<<16>>16>0){k=m<<l;if((k>>l|0)!=(m|0))k=m>>31^2147483647}else{k=0-l<<16;if((k|0)<2031616)k=m>>(k>>16);else k=0}l=k>>>16;t[u+6>>1]=(l|0)==32768?32767:0-l&65535;t[s+6>>1]=7-(b&65535);b=Kn(n)|0;l=b<<16>>16;if(b<<16>>16>0){k=n<<l;if((k>>l|0)!=(n|0))k=n>>31^2147483647}else{k=0-l<<16;if((k|0)<2031616)k=n>>(k>>16);else k=0}t[u+8>>1]=k>>>16;t[s+8>>1]=7-(b&65535);switch(e|0){case 0:case 5:{k=0;v=0;break}default:{c=S;return}}do{v=(I(t[E+(k<<1)>>1]|0,t[r+(k<<1)>>1]|0)|0)+v|0;k=k+1|0}while((k|0)!=40);l=v<<1;k=Kn(l)|0;b=k<<16>>16;if(k<<16>>16>0){v=l<<b;if((v>>b|0)==(l|0)){g=v;A=40}else{g=l>>31^2147483647;A=40}}else{v=0-b<<16;if((v|0)<2031616){g=l>>(v>>16);A=40}}if((A|0)==40?(g>>16|0)>=1:0){d=lt(g>>>16&65535,1,d)|0;t[h>>1]=Sn(d,t[o>>1]|0)|0;t[w>>1]=65528-(k&65535)-(a[i>>1]|0);c=S;return}t[h>>1]=0;t[w>>1]=0;c=S;return}function Ri(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var o=0,a=0,l=0;a=0;o=0;do{l=t[e+(a<<1)>>1]|0;o=(I(l,l)|0)+o|0;a=a+1|0}while((a|0)!=40);if((o|0)<0){f[n>>2]=1;o=2147483647}n=Kn(o)|0;e=n<<16>>16;if(n<<16>>16>0){a=o<<e;if((a>>e|0)==(o|0))o=a;else o=o>>31^2147483647}else{e=0-e<<16;if((e|0)<2031616)o=o>>(e>>16);else o=0}t[r>>1]=o>>>16;t[i>>1]=16-(n&65535);return}function Mi(e,i,r,n,o,a,l,u,s,h,w,d,v){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;l=l|0;u=u|0;s=s|0;h=h|0;w=w|0;d=d|0;v=v|0;var b=0,k=0,m=0,y=0;k=c;c=c+16|0;b=k;if(h>>>0<2){l=ki(w,e,i,r,n,l,u,b,f[d+76>>2]|0,v)|0;v=f[s>>2]|0;t[v>>1]=l;l=t[b>>1]|0;f[s>>2]=v+4;t[v+2>>1]=l;c=k;return}switch(h|0){case 2:{l=bi(e,i,r,n,l,u,b,v)|0;v=f[s>>2]|0;t[v>>1]=l;l=t[b>>1]|0;f[s>>2]=v+4;t[v+2>>1]=l;c=k;return}case 3:{l=pi(e,i,r,n,l,u,b,v)|0;v=f[s>>2]|0;t[v>>1]=l;l=t[b>>1]|0;f[s>>2]=v+4;t[v+2>>1]=l;c=k;return}default:{if((h&-2|0)==4){l=Ei(e,i,r,n,l,u,b,f[d+36>>2]|0,v)|0;v=f[s>>2]|0;t[v>>1]=l;l=t[b>>1]|0;f[s>>2]=v+4;t[v+2>>1]=l;c=k;return}if((h|0)!=6){w=o<<16>>16;w=(w<<17>>17|0)==(w|0)?w<<1:w>>>15^32767;o=r<<16>>16<40;if(!o){vi(e,a,i,l,u,f[s>>2]|0,f[d+36>>2]|0,v);f[s>>2]=(f[s>>2]|0)+20;c=k;return}b=r<<16>>16;h=w<<16>>16;n=b;do{y=(I(t[i+(n-b<<1)>>1]|0,h)|0)>>>15&65535;m=i+(n<<1)|0;t[m>>1]=gn(t[m>>1]|0,y,v)|0;n=n+1|0}while((n&65535)<<16>>16!=40);vi(e,a,i,l,u,f[s>>2]|0,f[d+36>>2]|0,v);f[s>>2]=(f[s>>2]|0)+20;if(!o){c=k;return}o=r<<16>>16;h=w<<16>>16;b=o;do{n=(I(t[l+(b-o<<1)>>1]|0,h)|0)>>15;if((n|0)>32767){f[v>>2]=1;n=32767}y=l+(b<<1)|0;t[y>>1]=gn(t[y>>1]|0,n&65535,v)|0;b=b+1|0}while((b&65535)<<16>>16!=40);c=k;return}d=n<<16>>16;d=(d<<17>>17|0)==(d|0)?d<<1:d>>>15^32767;w=r<<16>>16<40;if(!w){gi(e,a,i,l,u,f[s>>2]|0,v);f[s>>2]=(f[s>>2]|0)+14;c=k;return}b=r<<16>>16;h=d<<16>>16;n=b;do{o=(I(t[i+(n-b<<1)>>1]|0,h)|0)>>15;if((o|0)>32767){f[v>>2]=1;o=32767}y=i+(n<<1)|0;t[y>>1]=gn(t[y>>1]|0,o&65535,v)|0;n=n+1|0}while((n&65535)<<16>>16!=40);gi(e,a,i,l,u,f[s>>2]|0,v);f[s>>2]=(f[s>>2]|0)+14;if(!w){c=k;return}o=r<<16>>16;h=d<<16>>16;b=o;do{n=(I(t[l+(b-o<<1)>>1]|0,h)|0)>>15;if((n|0)>32767){f[v>>2]=1;n=32767}y=l+(b<<1)|0;t[y>>1]=gn(t[y>>1]|0,n&65535,v)|0;b=b+1|0}while((b&65535)<<16>>16!=40);c=k;return}}}function Di(e){e=e|0;var i=0;if(!e){e=-1;return e|0}f[e>>2]=0;i=dt(4)|0;if(!i){e=-1;return e|0}if(!((Sr(i)|0)<<16>>16)){_r(f[i>>2]|0)|0;f[e>>2]=i;e=0;return e|0}else{Rr(i);vt(i);e=-1;return e|0}return 0}function Oi(e){e=e|0;var i=0;if(!e)return;i=f[e>>2]|0;if(!i)return;Rr(i);vt(f[e>>2]|0);f[e>>2]=0;return}function Ni(e){e=e|0;if(!e){e=-1;return e|0}_r(f[e>>2]|0)|0;e=0;return e|0}function Ti(e,i,r,n,o,l,u,s,h,w,d,v,b,k,m,y,p,E,g,A){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;l=l|0;u=u|0;s=s|0;h=h|0;w=w|0;d=d|0;v=v|0;b=b|0;k=k|0;m=m|0;y=y|0;p=p|0;E=E|0;g=g|0;A=A|0;var S=0,_=0,R=0,M=0;_=c;c=c+16|0;M=_+2|0;R=_;t[b>>1]=Mr(f[e>>2]|0,r,o,u,h,l,40,n,k,R,M,A)|0;e=t[M>>1]|0;n=f[p>>2]|0;f[p>>2]=n+2;t[n>>1]=e;Zn(u,t[b>>1]|0,t[k>>1]|0,40,t[R>>1]|0,A);Pi(u,l,v,40);t[m>>1]=ar(r,h,v,y,40,A)|0;t[E>>1]=32767;if(w<<16>>16!=0?(S=t[m>>1]|0,S<<16>>16>15565):0)S=un(i,S,A)|0;else S=0;if(r>>>0<2){M=t[m>>1]|0;t[m>>1]=M<<16>>16>13926?13926:M;if(S<<16>>16)t[E>>1]=15565}else{if(S<<16>>16){t[E>>1]=15565;t[m>>1]=15565}if((r|0)==7){R=Gr(7,t[E>>1]|0,m,0,0,g,A)|0;M=f[p>>2]|0;f[p>>2]=M+2;t[M>>1]=R}}b=t[m>>1]|0;S=0;while(1){R=I(t[v>>1]|0,b)|0;t[d>>1]=(a[h>>1]|0)-(R>>>14);R=(I(t[u>>1]|0,b)|0)>>>14;M=s+(S<<1)|0;t[M>>1]=(a[M>>1]|0)-R;S=S+1|0;if((S|0)==40)break;else{u=u+2|0;h=h+2|0;d=d+2|0;v=v+2|0}}c=_;return}function Ii(e,i){e=e|0;i=i|0;var r=0,n=0,t=0,o=0;o=c;c=c+16|0;t=o;if(!e){e=-1;c=o;return e|0}f[e>>2]=0;r=dt(2532)|0;f[t>>2]=r;if(!r){e=-1;c=o;return e|0}On(r+2392|0);f[r+2188>>2]=0;f[(f[t>>2]|0)+2192>>2]=0;f[(f[t>>2]|0)+2196>>2]=0;f[(f[t>>2]|0)+2200>>2]=0;f[(f[t>>2]|0)+2204>>2]=0;f[(f[t>>2]|0)+2208>>2]=0;f[(f[t>>2]|0)+2212>>2]=0;f[(f[t>>2]|0)+2220>>2]=0;n=f[t>>2]|0;f[n+2216>>2]=i;f[n+2528>>2]=0;r=n;if((((((((Di(n+2196|0)|0)<<16>>16==0?(jn(n+2192|0)|0)<<16>>16==0:0)?(rr(n+2200|0)|0)<<16>>16==0:0)?(Nr(n+2204|0)|0)<<16>>16==0:0)?(fn(n+2208|0)|0)<<16>>16==0:0)?(cn(n+2212|0)|0)<<16>>16==0:0)?(ji(n+2220|0,f[n+2432>>2]|0)|0)<<16>>16==0:0)?(yr(n+2188|0)|0)<<16>>16==0:0){Fi(n)|0;f[e>>2]=r;e=0;c=o;return e|0}Li(t);e=-1;c=o;return e|0}function Li(e){e=e|0;var i=0;if(!e)return;i=f[e>>2]|0;if(!i)return;pr(i+2188|0);Wn((f[e>>2]|0)+2192|0);nr((f[e>>2]|0)+2200|0);Oi((f[e>>2]|0)+2196|0);Ir((f[e>>2]|0)+2204|0);an((f[e>>2]|0)+2208|0);wn((f[e>>2]|0)+2212|0);Wi((f[e>>2]|0)+2220|0);vt(f[e>>2]|0);f[e>>2]=0;return}function Fi(e){e=e|0;var i=0,r=0,n=0,o=0;if(!e){o=-1;return o|0}f[e+652>>2]=e+320;f[e+640>>2]=e+240;f[e+644>>2]=e+160;f[e+648>>2]=e+80;f[e+1264>>2]=e+942;f[e+1912>>2]=e+1590;n=e+1938|0;f[e+2020>>2]=n;f[e+2384>>2]=e+2304;i=e+2028|0;f[e+2024>>2]=e+2108;f[e+2528>>2]=0;Et(e|0,0,640)|0;Et(e+1282|0,0,308)|0;Et(e+656|0,0,286)|0;r=e+2224|0;o=n+80|0;do{t[n>>1]=0;n=n+2|0}while((n|0)<(o|0));n=i;o=n+80|0;do{t[n>>1]=0;n=n+2|0}while((n|0)<(o|0));i=e+1268|0;n=r;o=n+80|0;do{t[n>>1]=0;n=n+2|0}while((n|0)<(o|0));t[i>>1]=40;t[e+1270>>1]=40;t[e+1272>>1]=40;t[e+1274>>1]=40;t[e+1276>>1]=40;Er(f[e+2188>>2]|0)|0;Hn(f[e+2192>>2]|0)|0;Ni(f[e+2196>>2]|0)|0;tr(f[e+2200>>2]|0)|0;Tr(f[e+2204>>2]|0)|0;on(f[e+2208>>2]|0)|0;hn(f[e+2212>>2]|0)|0;Hi(f[e+2220>>2]|0,f[e+2432>>2]|0)|0;t[e+2388>>1]=0;o=0;return o|0}function Ci(e,i,r,n,o,a){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;var l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,I=0,L=0,F=0,C=0,P=0,B=0,U=0,x=0,z=0,j=0,H=0,W=0,q=0,G=0,X=0,V=0,K=0,Y=0,Q=0,Z=0,$=0,J=0,ee=0,ie=0,re=0,ne=0,te=0,fe=0,oe=0,ae=0,le=0,ue=0,se=0,ce=0,he=0,we=0,de=0,ve=0,be=0;be=c;c=c+1184|0;G=be;w=be+1096|0;d=be+1008|0;s=be+904|0;se=be+928|0;ce=be+824|0;Y=be+744|0;we=be+664|0;de=be+584|0;Z=be+328|0;ae=be+504|0;le=be+424|0;he=be+344|0;ve=be+248|0;Q=be+168|0;ne=be+88|0;fe=be+68|0;oe=be+48|0;te=be+28|0;ue=be+24|0;ie=be+22|0;J=be+20|0;K=be+16|0;X=be+12|0;V=be+10|0;ee=be+8|0;$=be+6|0;re=be+4|0;f[G>>2]=n;q=e+2528|0;l=e+652|0;yt(f[l>>2]|0,r|0,320)|0;f[o>>2]=i;h=e+2216|0;if(!(f[h>>2]|0)){r=e+2220|0;n=0}else{n=mn(f[e+2212>>2]|0,f[l>>2]|0,q)|0;W=e+2220|0;r=W;n=Xi(f[W>>2]|0,n,o,q)|0}W=e+2392|0;gr(f[e+2188>>2]|0,i,f[e+644>>2]|0,f[e+648>>2]|0,w,W,q);u=e+2192|0;qn(f[u>>2]|0,i,f[o>>2]|0,w,d,s,G,q);Gi(f[r>>2]|0,s,f[l>>2]|0,q);if((f[o>>2]|0)==8){qi(f[r>>2]|0,n,f[(f[u>>2]|0)+40>>2]|0,(f[e+2200>>2]|0)+32|0,G,q);Et(e+1282|0,0,308)|0;l=e+2244|0;b=l+20|0;do{t[l>>1]=0;l=l+2|0}while((l|0)<(b|0));l=e+2284|0;b=l+20|0;do{t[l>>1]=0;l=l+2|0}while((l|0)<(b|0));l=f[e+2020>>2]|0;b=l+80|0;do{t[l>>1]=0;l=l+2|0}while((l|0)<(b|0));l=e+2028|0;b=l+80|0;do{t[l>>1]=0;l=l+2|0}while((l|0)<(b|0));Hn(f[u>>2]|0)|0;l=f[u>>2]|0;r=s;b=l+20|0;do{t[l>>1]=t[r>>1]|0;l=l+2|0;r=r+2|0}while((l|0)<(b|0));l=(f[u>>2]|0)+20|0;r=s;b=l+20|0;do{t[l>>1]=t[r>>1]|0;l=l+2|0;r=r+2|0}while((l|0)<(b|0));Ni(f[e+2196>>2]|0)|0;t[e+2388>>1]=0;H=0}else H=ln(f[e+2208>>2]|0,f[u>>2]|0,q)|0;x=e+640|0;u=e+2264|0;l=e+1264|0;r=e+2204|0;n=e+2212|0;z=e+1268|0;j=e+1278|0;Fr(i,2842,2862,2882,w,0,f[x>>2]|0,u,f[l>>2]|0,q);if(i>>>0>1){Ar(f[r>>2]|0,f[n>>2]|0,i,f[l>>2]|0,K,z,j,0,f[h>>2]|0,q);Fr(i,2842,2862,2882,w,80,f[x>>2]|0,u,f[l>>2]|0,q);Ar(f[r>>2]|0,f[n>>2]|0,i,(f[l>>2]|0)+160|0,K+2|0,z,j,1,f[h>>2]|0,q)}else{Fr(i,2842,2862,2882,w,80,f[x>>2]|0,u,f[l>>2]|0,q);Ar(f[r>>2]|0,f[n>>2]|0,i,f[l>>2]|0,K,z,j,1,f[h>>2]|0,q);t[K+2>>1]=t[K>>1]|0}if(f[h>>2]|0)kn(f[n>>2]|0,K,q);if((f[o>>2]|0)==8){de=e+656|0;ve=e+976|0;yt(de|0,ve|0,286)|0;ve=e+320|0;yt(e|0,ve|0,320)|0;c=be;return 0}_=e+2224|0;R=e+2244|0;M=e+2284|0;D=e+2388|0;O=e+2020|0;N=e+1916|0;T=e+1912|0;I=e+2024|0;L=e+2384|0;F=e+2196|0;C=e+2208|0;P=e+2464|0;B=e+2200|0;U=e+2224|0;g=e+2244|0;A=e+1270|0;S=e+1280|0;E=0;h=0;s=0;m=0;y=0;u=0;p=-1;while(1){v=p;p=p+1<<16>>16;m=1-(m<<16>>16)|0;n=m&65535;k=(m&65535|0)!=0;r=f[o>>2]|0;l=(r|0)==0;do{if(k)if(l){l=fe;r=_;b=l+20|0;do{t[l>>1]=t[r>>1]|0;l=l+2|0;r=r+2|0}while((l|0)<(b|0));l=oe;r=R;b=l+20|0;do{t[l>>1]=t[r>>1]|0;l=l+2|0;r=r+2|0}while((l|0)<(b|0));l=te;r=M;b=l+20|0;do{t[l>>1]=t[r>>1]|0;l=l+2|0;r=r+2|0}while((l|0)<(b|0));t[ue>>1]=t[D>>1]|0;i=(f[x>>2]|0)+(E<<1)|0;l=20;break}else{i=(f[x>>2]|0)+(E<<1)|0;l=19;break}else{i=(f[x>>2]|0)+(E<<1)|0;if(l)l=20;else l=19}}while(0);if((l|0)==19)nn(r,2842,2862,2882,w,d,i,M,g,f[O>>2]|0,N,(f[T>>2]|0)+(E<<1)|0,f[I>>2]|0,se,ae,f[L>>2]|0);else if((l|0)==20?(nn(0,2842,2862,2882,w,d,i,M,oe,f[O>>2]|0,N,(f[T>>2]|0)+(E<<1)|0,f[I>>2]|0,se,ae,f[L>>2]|0),k):0){l=ne;r=f[I>>2]|0;b=l+80|0;do{t[l>>1]=t[r>>1]|0;l=l+2|0;r=r+2|0}while((l|0)<(b|0))}l=le;r=ae;b=l+80|0;do{t[l>>1]=t[r>>1]|0;l=l+2|0;r=r+2|0}while((l|0)<(b|0));Ti(f[F>>2]|0,f[C>>2]|0,f[o>>2]|0,y,K,f[I>>2]|0,(f[T>>2]|0)+(E<<1)|0,le,se,H,ce,we,X,V,ee,Z,G,re,f[P>>2]|0,q);switch(v<<16>>16){case-1:{if((t[j>>1]|0)>0)t[A>>1]=t[X>>1]|0;break}case 2:{if((t[S>>1]|0)>0)t[z>>1]=t[X>>1]|0;break}default:}Mi(ce,f[I>>2]|0,t[X>>1]|0,t[D>>1]|0,t[ee>>1]|0,le,Y,de,G,f[o>>2]|0,p,W,q);fr(f[B>>2]|0,f[o>>2]|0,ae,(f[T>>2]|0)+(E<<1)|0,Y,se,ce,we,de,Z,n,t[re>>1]|0,ie,J,ee,$,G,W,q);sn(f[C>>2]|0,t[ee>>1]|0,q);i=f[o>>2]|0;do{if(!i)if(k){l=he;r=se;b=l+80|0;do{t[l>>1]=t[r>>1]|0;l=l+2|0;r=r+2|0}while((l|0)<(b|0));l=ve;r=de;b=l+80|0;do{t[l>>1]=t[r>>1]|0;l=l+2|0;r=r+2|0}while((l|0)<(b|0));l=Q;r=Y;b=l+80|0;do{t[l>>1]=t[r>>1]|0;l=l+2|0;r=r+2|0}while((l|0)<(b|0));s=t[X>>1]|0;h=t[V>>1]|0;tn(f[x>>2]|0,0,y,t[ee>>1]|0,t[$>>1]|0,d,a,se,Y,we,de,fe,M,oe,f[T>>2]|0,D,q);t[D>>1]=t[ue>>1]|0;u=y;break}else{l=M;r=te;b=l+20|0;do{t[l>>1]=t[r>>1]|0;l=l+2|0;r=r+2|0}while((l|0)<(b|0));k=u<<16>>16;Zn((f[T>>2]|0)+(k<<1)|0,s,h,40,1,q);Pi((f[T>>2]|0)+(k<<1)|0,ne,we,40);tn(f[x>>2]|0,f[o>>2]|0,u,t[ie>>1]|0,t[J>>1]|0,d+-22|0,a,he,Q,we,ve,U,M,g,f[T>>2]|0,ue,q);nn(f[o>>2]|0,2842,2862,2882,w,d,(f[x>>2]|0)+(E<<1)|0,M,g,f[O>>2]|0,N,(f[T>>2]|0)+(E<<1)|0,f[I>>2]|0,se,ae,f[L>>2]|0);Zn((f[T>>2]|0)+(E<<1)|0,t[X>>1]|0,t[V>>1]|0,40,1,q);Pi((f[T>>2]|0)+(E<<1)|0,f[I>>2]|0,we,40);tn(f[x>>2]|0,f[o>>2]|0,y,t[ee>>1]|0,t[$>>1]|0,d,a,se,Y,we,de,U,M,g,f[T>>2]|0,D,q);break}else tn(f[x>>2]|0,i,y,t[ee>>1]|0,t[$>>1]|0,d,a,se,Y,we,de,U,M,g,f[T>>2]|0,D,q)}while(0);i=E+40|0;y=i&65535;if(y<<16>>16>=160)break;else{E=i<<16>>16;w=w+22|0;d=d+22|0}}yt(e+1282|0,e+1602|0,308)|0;de=e+656|0;ve=e+976|0;yt(de|0,ve|0,286)|0;ve=e+320|0;yt(e|0,ve|0,320)|0;c=be;return 0}function Pi(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var f=0,o=0,a=0,l=0,u=0,s=0,c=0,h=0,w=0,d=0,v=0,b=0;d=n<<16>>16;if(n<<16>>16>1)w=1;else return;while(1){f=t[e>>1]|0;l=i+(w+-1<<1)|0;n=I(t[i+(w<<1)>>1]|0,f)|0;s=t[l>>1]|0;f=I(s<<16>>16,f)|0;a=(w+131071|0)>>>1;u=a&65535;o=t[e+2>>1]|0;if(!(u<<16>>16)){i=l;a=s}else{c=(a<<1)+131070&131070;h=w-c|0;a=e;do{b=(I(s<<16>>16,o)|0)+n|0;v=a;a=a+4|0;n=t[l+-2>>1]|0;o=(I(n,o)|0)+f|0;f=t[a>>1]|0;l=l+-4|0;n=b+(I(f,n)|0)|0;s=t[l>>1]|0;f=o+(I(s<<16>>16,f)|0)|0;u=u+-1<<16>>16;o=t[v+6>>1]|0}while(u<<16>>16!=0);a=i+(h+-3<<1)|0;e=e+(c+2<<1)|0;i=a;a=t[a>>1]|0}n=(I(a<<16>>16,o)|0)+n|0;t[r>>1]=f>>>12;t[r+2>>1]=n>>>12;n=(w<<16)+131072>>16;if((n|0)<(d|0)){r=r+4|0;e=e+(1-w<<1)|0;w=n}else break}return}function Bi(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var f=0,o=0,a=0,l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0;_=c;c=c+80|0;S=_;a=20;o=e;f=1;while(1){A=t[o>>1]|0;A=(I(A,A)|0)+f|0;f=t[o+2>>1]|0;f=A+(I(f,f)|0)|0;a=a+-1<<16>>16;if(!(a<<16>>16))break;else o=o+4|0}f=f<<1;if((f|0)<0){o=20;f=e;n=S;while(1){t[n>>1]=(t[f>>1]|0)>>>1;t[n+2>>1]=(t[f+2>>1]|0)>>>1;o=o+-1<<16>>16;if(!(o<<16>>16)){A=S;break}else{f=f+4|0;n=n+4|0}}}else{f=Cn(f>>1,n)|0;if((f|0)<16777215)f=((f>>9)*32440|0)>>>15<<16>>16;else f=32440;a=20;o=e;n=S;while(1){t[n>>1]=((I(t[o>>1]|0,f)|0)+32|0)>>>6;t[n+2>>1]=((I(t[o+2>>1]|0,f)|0)+32|0)>>>6;a=a+-1<<16>>16;if(!(a<<16>>16)){A=S;break}else{o=o+4|0;n=n+4|0}}}a=20;o=A;n=r+3198|0;f=0;while(1){g=t[o>>1]|0;g=(I(g,g)|0)+f|0;t[n>>1]=(g+16384|0)>>>15;E=t[o+2>>1]|0;f=(I(E,E)|0)+g|0;t[n+-82>>1]=(f+16384|0)>>>15;a=a+-1<<16>>16;if(!(a<<16>>16))break;else{o=o+4|0;n=n+-164|0}}g=i+78|0;E=1;while(1){f=39-E|0;e=r+3120+(f<<1)|0;n=r+(f*80|0)+78|0;f=i+(f<<1)|0;u=S+(E<<1)|0;o=65575-E|0;l=o&65535;a=t[A>>1]|0;if(!(l<<16>>16)){l=g;o=0}else{k=o+65535&65535;y=k*41|0;p=(I(E,-40)|0)-y|0;m=0-E|0;y=m-y|0;m=m-k|0;b=E+k|0;v=t[u>>1]|0;w=A;d=g;s=r+((38-E|0)*80|0)+78|0;o=0;h=0;while(1){u=u+2|0;o=(I(v<<16>>16,a)|0)+o|0;w=w+2|0;v=t[u>>1]|0;h=(I(v<<16>>16,a)|0)+h|0;M=f;f=f+-2|0;a=t[f>>1]|0;R=t[d>>1]<<1;M=(I((I(R,t[M>>1]|0)|0)>>16,(o<<1)+32768>>16)|0)>>>15&65535;t[n>>1]=M;t[e>>1]=M;a=(I((I(R,a)|0)>>16,(h<<1)+32768>>16)|0)>>>15&65535;t[e+-2>>1]=a;t[s>>1]=a;l=l+-1<<16>>16;a=t[w>>1]|0;if(!(l<<16>>16))break;else{d=d+-2|0;e=e+-82|0;n=n+-82|0;s=s+-82|0}}u=S+(b+1<<1)|0;l=i+(38-k<<1)|0;f=i+(m+38<<1)|0;e=r+3040+(y+38<<1)|0;n=r+3040+(p+38<<1)|0}M=(I(t[u>>1]|0,a)|0)+o|0;M=(I((M<<1)+32768>>16,(I(t[l>>1]<<1,t[f>>1]|0)|0)>>16)|0)>>>15&65535;t[e>>1]=M;t[n>>1]=M;n=(E<<16)+131072|0;if((n|0)<2621440)E=n>>16;else break}c=_;return}function Ui(e,i,r,n,o,a,l){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;l=l|0;var u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0;k=c;c=c+160|0;b=k;if(o<<16>>16>0){d=a&65535;v=0;u=5;do{if((v|0)<40){w=v;h=v&65535;a=0;while(1){if(h<<16>>16<40){h=h<<16>>16;s=0;do{s=(I(t[e+(h-w<<1)>>1]|0,t[i+(h<<1)>>1]|0)|0)+s|0;h=h+1|0}while((h&65535)<<16>>16!=40)}else s=0;s=s<<1;f[b+(w<<2)>>2]=s;s=sr(s)|0;a=(s|0)>(a|0)?s:a;s=w+d|0;h=s&65535;if(h<<16>>16>=40)break;else w=s<<16>>16}}else a=0;u=(a>>1)+u|0;v=v+1|0}while((v&65535)<<16>>16!=o<<16>>16)}else u=5;n=((Kn(u)|0)&65535)-(n&65535)|0;a=n<<16>>16;s=0-a<<16;u=(s|0)<2031616;s=s>>16;if((n&65535)<<16>>16>0)if(u){u=0;do{n=f[b+(u<<2)>>2]|0;i=n<<a;t[r+(u<<1)>>1]=at((i>>a|0)==(n|0)?i:n>>31^2147483647,l)|0;u=u+1|0}while((u|0)!=40);c=k;return}else{u=0;do{n=f[b+(u<<2)>>2]|0;i=n<<a;t[r+(u<<1)>>1]=at((i>>a|0)==(n|0)?i:n>>31^2147483647,l)|0;u=u+1|0}while((u|0)!=40);c=k;return}else if(u){u=0;do{t[r+(u<<1)>>1]=at(f[b+(u<<2)>>2]>>s,l)|0;u=u+1|0}while((u|0)!=40);c=k;return}else{u=0;do{t[r+(u<<1)>>1]=at(0,l)|0;u=u+1|0}while((u|0)!=40);c=k;return}}function xi(e,i,r,n,o){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;var a=0,l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0;_=c;c=c+160|0;S=_;E=e+2|0;g=t[e>>1]|0;A=0;o=5;do{p=A;u=0;while(1){w=i+(p<<1)|0;y=40-p|0;a=(y+131071|0)>>>1&65535;s=i+(p+1<<1)|0;l=I(t[w>>1]<<1,g)|0;if(!(a<<16>>16))a=E;else{m=131111-p+131070&131070;k=p+m|0;b=E;v=e;d=w;while(1){h=d+4|0;w=v+4|0;l=(I(t[s>>1]<<1,t[b>>1]|0)|0)+l|0;a=a+-1<<16>>16;l=(I(t[h>>1]<<1,t[w>>1]|0)|0)+l|0;if(!(a<<16>>16))break;else{s=d+6|0;b=v+6|0;v=w;d=h}}s=i+(k+3<<1)|0;a=e+(m+3<<1)|0}if(!(y&1))l=(I(t[s>>1]<<1,t[a>>1]|0)|0)+l|0;f[S+(p<<2)>>2]=l;l=(l|0)<0?0-l|0:l;u=(l|0)>(u|0)?l:u;l=p+5|0;if((l&65535)<<16>>16<40)p=l<<16>>16;else break}o=(u>>1)+o|0;A=A+1|0}while((A|0)!=5);n=((Kn(o)|0)&65535)-(n&65535)|0;l=n<<16>>16;o=0-l<<16;u=o>>16;if((n&65535)<<16>>16>0){a=20;o=S;while(1){S=f[o>>2]|0;n=S<<l;t[r>>1]=(((n>>l|0)==(S|0)?n:S>>31^2147483647)+32768|0)>>>16;S=f[o+4>>2]|0;n=S<<l;t[r+2>>1]=(((n>>l|0)==(S|0)?n:S>>31^2147483647)+32768|0)>>>16;a=a+-1<<16>>16;if(!(a<<16>>16))break;else{r=r+4|0;o=o+8|0}}c=_;return}if((o|0)<2031616){a=20;o=S;while(1){t[r>>1]=((f[o>>2]>>u)+32768|0)>>>16;t[r+2>>1]=((f[o+4>>2]>>u)+32768|0)>>>16;a=a+-1<<16>>16;if(!(a<<16>>16))break;else{r=r+4|0;o=o+8|0}}c=_;return}else{t[r>>1]=0;S=r+4|0;t[r+2>>1]=0;t[S>>1]=0;n=S+4|0;t[S+2>>1]=0;t[n>>1]=0;S=n+4|0;t[n+2>>1]=0;t[S>>1]=0;n=S+4|0;t[S+2>>1]=0;t[n>>1]=0;S=n+4|0;t[n+2>>1]=0;t[S>>1]=0;n=S+4|0;t[S+2>>1]=0;t[n>>1]=0;S=n+4|0;t[n+2>>1]=0;t[S>>1]=0;n=S+4|0;t[S+2>>1]=0;t[n>>1]=0;S=n+4|0;t[n+2>>1]=0;t[S>>1]=0;n=S+4|0;t[S+2>>1]=0;t[n>>1]=0;S=n+4|0;t[n+2>>1]=0;t[S>>1]=0;n=S+4|0;t[S+2>>1]=0;t[n>>1]=0;S=n+4|0;t[n+2>>1]=0;t[S>>1]=0;n=S+4|0;t[S+2>>1]=0;t[n>>1]=0;S=n+4|0;t[n+2>>1]=0;t[S>>1]=0;n=S+4|0;t[S+2>>1]=0;t[n>>1]=0;S=n+4|0;t[n+2>>1]=0;t[S>>1]=0;n=S+4|0;t[S+2>>1]=0;t[n>>1]=0;S=n+4|0;t[n+2>>1]=0;t[S>>1]=0;t[S+2>>1]=0;c=_;return}}function zi(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var t=0,o=0,a=0;a=(Sn(16383,i)|0)<<16>>16;i=I(a,i<<16>>16)|0;if((i|0)==1073741824){f[n>>2]=1;t=2147483647}else t=i<<1;o=(I(a,r<<16>>16)|0)>>15;i=t+(o<<1)|0;if((t^o|0)>0&(i^t|0)<0){f[n>>2]=1;i=(t>>>31)+2147483647|0}t=2147483647-i|0;r=t>>16;i=I(r,a)|0;if((i|0)==1073741824){f[n>>2]=1;o=2147483647}else o=i<<1;a=(I((t>>>1)-(r<<15)<<16>>16,a)|0)>>15;i=o+(a<<1)|0;if((o^a|0)>0&(i^o|0)<0){f[n>>2]=1;i=(o>>>31)+2147483647|0}o=i>>16;a=e>>16;r=I(o,a)|0;r=(r|0)==1073741824?2147483647:r<<1;t=(I((i>>>1)-(o<<15)<<16>>16,a)|0)>>15;n=(t<<1)+r|0;n=(t^r|0)>0&(n^r|0)<0?(r>>>31)+2147483647|0:n;a=(I(o,(e>>>1)-(a<<15)<<16>>16)|0)>>15;e=n+(a<<1)|0;e=(n^a|0)>0&(e^n|0)<0?(n>>>31)+2147483647|0:e;n=e<<2;return((n>>2|0)==(e|0)?n:e>>31^2147483647)|0}function ji(e,i){e=e|0;i=i|0;var r=0,n=0,o=0,a=0;if(!e){a=-1;return a|0}f[e>>2]=0;r=dt(192)|0;if(!r){a=-1;return a|0}n=r+176|0;t[n>>1]=0;t[n+2>>1]=0;t[n+4>>1]=0;t[n+6>>1]=0;t[n+8>>1]=0;t[n+10>>1]=0;n=r;o=i;a=n+20|0;do{t[n>>1]=t[o>>1]|0;n=n+2|0;o=o+2|0}while((n|0)<(a|0));n=r+20|0;o=i;a=n+20|0;do{t[n>>1]=t[o>>1]|0;n=n+2|0;o=o+2|0}while((n|0)<(a|0));n=r+40|0;o=i;a=n+20|0;do{t[n>>1]=t[o>>1]|0;n=n+2|0;o=o+2|0}while((n|0)<(a|0));n=r+60|0;o=i;a=n+20|0;do{t[n>>1]=t[o>>1]|0;n=n+2|0;o=o+2|0}while((n|0)<(a|0));n=r+80|0;o=i;a=n+20|0;do{t[n>>1]=t[o>>1]|0;n=n+2|0;o=o+2|0}while((n|0)<(a|0));n=r+100|0;o=i;a=n+20|0;do{t[n>>1]=t[o>>1]|0;n=n+2|0;o=o+2|0}while((n|0)<(a|0));n=r+120|0;o=i;a=n+20|0;do{t[n>>1]=t[o>>1]|0;n=n+2|0;o=o+2|0}while((n|0)<(a|0));n=r+140|0;o=i;a=n+20|0;do{t[n>>1]=t[o>>1]|0;n=n+2|0;o=o+2|0}while((n|0)<(a|0));n=r+160|0;a=n+20|0;do{t[n>>1]=0;n=n+2|0}while((n|0)<(a|0));t[r+188>>1]=7;t[r+190>>1]=32767;f[e>>2]=r;a=0;return a|0}function Hi(e,i){e=e|0;i=i|0;var r=0,n=0,f=0;if(!e){f=-1;return f|0}r=e+176|0;t[r>>1]=0;t[r+2>>1]=0;t[r+4>>1]=0;t[r+6>>1]=0;t[r+8>>1]=0;t[r+10>>1]=0;r=e;n=i;f=r+20|0;do{t[r>>1]=t[n>>1]|0;r=r+2|0;n=n+2|0}while((r|0)<(f|0));r=e+20|0;n=i;f=r+20|0;do{t[r>>1]=t[n>>1]|0;r=r+2|0;n=n+2|0}while((r|0)<(f|0));r=e+40|0;n=i;f=r+20|0;do{t[r>>1]=t[n>>1]|0;r=r+2|0;n=n+2|0}while((r|0)<(f|0));r=e+60|0;n=i;f=r+20|0;do{t[r>>1]=t[n>>1]|0;r=r+2|0;n=n+2|0}while((r|0)<(f|0));r=e+80|0;n=i;f=r+20|0;do{t[r>>1]=t[n>>1]|0;r=r+2|0;n=n+2|0}while((r|0)<(f|0));r=e+100|0;n=i;f=r+20|0;do{t[r>>1]=t[n>>1]|0;r=r+2|0;n=n+2|0}while((r|0)<(f|0));r=e+120|0;n=i;f=r+20|0;do{t[r>>1]=t[n>>1]|0;r=r+2|0;n=n+2|0}while((r|0)<(f|0));r=e+140|0;n=i;f=r+20|0;do{t[r>>1]=t[n>>1]|0;r=r+2|0;n=n+2|0}while((r|0)<(f|0));r=e+160|0;f=r+20|0;do{t[r>>1]=0;r=r+2|0}while((r|0)<(f|0));t[e+188>>1]=7;t[e+190>>1]=32767;f=1;return f|0}function Wi(e){e=e|0;var i=0;if(!e)return;i=f[e>>2]|0;if(!i)return;vt(i);f[e>>2]=0;return}function qi(e,i,r,n,o,a){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;var l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0;D=c;c=c+112|0;_=D+80|0;R=D+60|0;M=D+40|0;S=D;if(i<<16>>16==0?(l=e+178|0,(t[l>>1]|0)!=0):0){M=e+180|0;a=e+182|0;r=l;M=t[M>>1]|0;n=f[o>>2]|0;R=n+2|0;t[n>>1]=M;a=t[a>>1]|0;M=n+4|0;t[R>>1]=a;R=e+184|0;R=t[R>>1]|0;a=n+6|0;t[M>>1]=R;M=e+186|0;M=t[M>>1]|0;e=n+8|0;t[a>>1]=M;r=t[r>>1]|0;n=n+10|0;f[o>>2]=n;t[e>>1]=r;c=D;return}m=S+36|0;y=S+32|0;p=S+28|0;E=S+24|0;g=S+20|0;A=S+16|0;v=S+12|0;b=S+8|0;k=S+4|0;i=S;l=i+40|0;do{f[i>>2]=0;i=i+4|0}while((i|0)<(l|0));d=7;i=0;while(1){w=t[e+160+(d<<1)>>1]|0;l=w<<16>>16;if(w<<16>>16<0)l=~((l^-4)>>2);else l=l>>>2;i=gn(i,l&65535,a)|0;s=d*10|0;w=9;while(1){h=S+(w<<2)|0;u=f[h>>2]|0;O=t[e+(w+s<<1)>>1]|0;l=O+u|0;if((O^u|0)>-1&(l^u|0)<0){f[a>>2]=1;l=(u>>>31)+2147483647|0}f[h>>2]=l;if((w|0)>0)w=w+-1|0;else break}if((d|0)>0)d=d+-1|0;else break}l=i<<16>>16;if(i<<16>>16<0)l=~((l^-2)>>1);else l=l>>>1;t[R+18>>1]=(f[m>>2]|0)>>>3;t[R+16>>1]=(f[y>>2]|0)>>>3;t[R+14>>1]=(f[p>>2]|0)>>>3;t[R+12>>1]=(f[E>>2]|0)>>>3;t[R+10>>1]=(f[g>>2]|0)>>>3;t[R+8>>1]=(f[A>>2]|0)>>>3;t[R+6>>1]=(f[v>>2]|0)>>>3;t[R+4>>1]=(f[b>>2]|0)>>>3;t[R+2>>1]=(f[k>>2]|0)>>>3;t[R>>1]=(f[S>>2]|0)>>>3;i=e+178|0;l=(((l<<16)+167772160|0)>>>16)+128|0;t[i>>1]=l;l=l<<16;if((l|0)<0)l=~((l>>16^-256)>>8);else l=l>>24;t[i>>1]=l;if((l|0)<=63){if((l|0)<0){t[i>>1]=0;l=0}}else{t[i>>1]=63;l=63}O=ct(l<<8&65535,11560,a)|0;O=O<<16>>16>0?0:O<<16>>16<-14436?-14436:O;t[n>>1]=O;t[n+2>>1]=O;t[n+4>>1]=O;t[n+6>>1]=O;O=((O<<16>>16)*5443|0)>>>15&65535;t[n+8>>1]=O;t[n+10>>1]=O;t[n+12>>1]=O;t[n+14>>1]=O;Xn(R,_,10,a);ft(_,205,10,a);Gn(_,R,10,a);n=e+182|0;O=e+180|0;$n(r,8,R,M,n,O,a);a=n;n=i;O=t[O>>1]|0;r=f[o>>2]|0;M=r+2|0;t[r>>1]=O;a=t[a>>1]|0;O=r+4|0;t[M>>1]=a;M=e+184|0;M=t[M>>1]|0;a=r+6|0;t[O>>1]=M;e=e+186|0;e=t[e>>1]|0;O=r+8|0;t[a>>1]=e;e=t[n>>1]|0;r=r+10|0;f[o>>2]=r;t[O>>1]=e;c=D;return}function Gi(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var o=0,l=0,u=0,s=0,h=0,w=0;w=c;c=c+16|0;u=w+2|0;h=w;s=e+176|0;l=(a[s>>1]|0)+1|0;l=(l&65535|0)==8?0:l&65535;t[s>>1]=l;l=e+((l<<16>>16)*10<<1)|0;o=l+20|0;do{t[l>>1]=t[i>>1]|0;l=l+2|0;i=i+2|0}while((l|0)<(o|0));i=0;o=160;while(1){l=t[r>>1]|0;i=(I(l<<1,l)|0)+i|0;if((i|0)<0){i=2147483647;break}o=o+-1<<16>>16;if(!(o<<16>>16))break;else r=r+2|0}Pn(i,u,h,n);i=t[u>>1]|0;u=i<<16>>16;r=u<<10;if((r|0)!=(u<<26>>16|0)){f[n>>2]=1;r=i<<16>>16>0?32767:-32768}t[e+160+(t[s>>1]<<1)>>1]=(((t[h>>1]|0)>>>5)+r<<16)+-558432256>>17;c=w;return}function Xi(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var o=0,a=0,l=0;a=e+190|0;l=gn(t[a>>1]|0,1,n)|0;t[a>>1]=l;o=e+188|0;do{if(!(i<<16>>16)){e=t[o>>1]|0;if(!(e<<16>>16)){t[a>>1]=0;f[r>>2]=8;e=1;break}a=(e&65535)+65535&65535;t[o>>1]=a;if((gn(l,a,n)|0)<<16>>16<30){f[r>>2]=8;e=0}else e=0}else{t[o>>1]=7;e=0}}while(0);return e|0}function Vi(e,i,r,n,t,f,o,a){e=e|0;i=i|0;r=r|0;n=n|0;t=t|0;f=f|0;o=o|0;a=a|0;if(!(f<<16>>16)){f=e<<16>>16;if(((f<<16)+-5570560|0)<65536){i=(f*3|0)+-58+(i<<16>>16)|0;i=i&65535;return i|0}else{i=f+112|0;i=i&65535;return i|0}}if(!(o<<16>>16)){a=(e&65535)-(n&65535)<<16;i=(i<<16>>16)+2+(a>>15)+(a>>16)|0;i=i&65535;return i|0}n=n<<16>>16;n=(((r&65535)-n<<16)+-327680|0)>0?n+5&65535:r;t=t<<16>>16;r=e<<16>>16;n=(((t-(n&65535)<<16)+-262144|0)>0?t+65532&65535:n)<<16>>16;t=n*196608|0;e=t+-393216>>16;f=((i&65535)<<16)+(r*196608|0)>>16;if(!(e-f&32768)){i=r+5-n|0;i=i&65535;return i|0}if((t+196608>>16|0)>(f|0)){i=f+3-e|0;i=i&65535;return i|0}else{i=r+11-n|0;i=i&65535;return i|0}return 0}function Ki(e,i,r,n,t){e=e|0;i=i|0;r=r|0;n=n|0;t=t|0;t=e<<16>>16;do{if(!(n<<16>>16))if(e<<16>>16<95){t=((t*393216|0)+-6881280>>16)+(i<<16>>16)|0;break}else{t=t+368|0;break}else t=((((t-(r&65535)|0)*393216|0)+196608|0)>>>16)+(i&65535)|0}while(0);return t&65535|0}function Yi(e,i,r,o){e=e|0;i=i|0;r=r|0;o=o|0;var l=0,u=0,s=0,c=0,h=0,w=0;l=f[o+96>>2]|0;if(e>>>0<8){h=(f[o+100>>2]|0)+(e<<2)|0;c=f[h>>2]|0;n[r>>0]=t[i+(t[c>>1]<<1)>>1]<<4|e|t[i+(t[c+2>>1]<<1)>>1]<<5|t[i+(t[c+4>>1]<<1)>>1]<<6|t[i+(t[c+6>>1]<<1)>>1]<<7;c=l+(e<<1)|0;o=t[c>>1]|0;if((o+-7|0)>4){l=4;s=4;e=1;while(1){w=t[i+(t[(f[h>>2]|0)+(l<<1)>>1]<<1)>>1]|0;o=r+(e<<16>>16)|0;n[o>>0]=w;w=a[i+(t[(f[h>>2]|0)+((s|1)<<16>>16<<1)>>1]<<1)>>1]<<1|w&65535;n[o>>0]=w;w=a[i+(t[(f[h>>2]|0)+((s|2)<<16>>16<<1)>>1]<<1)>>1]<<2|w;n[o>>0]=w;w=a[i+(t[(f[h>>2]|0)+((s|3)<<16>>16<<1)>>1]<<1)>>1]<<3|w;n[o>>0]=w;w=a[i+(t[(f[h>>2]|0)+(s+4<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<4|w;n[o>>0]=w;w=a[i+(t[(f[h>>2]|0)+(s+5<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<5|w;n[o>>0]=w;w=a[i+(t[(f[h>>2]|0)+(s+6<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<6|w;n[o>>0]=w;u=s+8<<16>>16;e=e+1<<16>>16;n[o>>0]=a[i+(t[(f[h>>2]|0)+(s+7<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<7|w;l=u<<16>>16;o=t[c>>1]|0;if((l|0)>=(o+-7|0))break;else s=u}}else{u=4;e=1}c=o+4&7;if(!c)return;l=r+(e<<16>>16)|0;n[l>>0]=0;o=0;s=0;e=0;while(1){s=(a[i+(t[(f[h>>2]|0)+(u<<16>>16<<1)>>1]<<1)>>1]&255)<<o|s&255;n[l>>0]=s;e=e+1<<16>>16;o=e<<16>>16;if((o|0)>=(c|0))break;else u=u+1<<16>>16}return}if((e|0)==15){n[r>>0]=15;return}n[r>>0]=t[i>>1]<<4|e|t[i+2>>1]<<5|t[i+4>>1]<<6|t[i+6>>1]<<7;o=l+(e<<1)|0;e=t[o>>1]|0;l=((e&65535)<<16)+262144>>16;h=l&-8;s=(h+524281|0)>>>3&65535;if(s<<16>>16>0){l=((l&-8)+524281|0)>>>3;c=((l<<3)+524280&524280)+12|0;u=1;e=i+8|0;while(1){n[r+(u<<16>>16)>>0]=a[e+2>>1]<<1|a[e>>1]|a[e+4>>1]<<2|a[e+6>>1]<<3|a[e+8>>1]<<4|a[e+10>>1]<<5|a[e+12>>1]<<6|a[e+14>>1]<<7;if(s<<16>>16>1){s=s+-1<<16>>16;u=u+1<<16>>16;e=e+16|0}else break}e=t[o>>1]|0;u=(l<<16)+65536>>16}else{c=4;u=1}e=(0-h|4)+(e&65535)<<16;s=e>>16;if(!s)return;u=r+u|0;n[u>>0]=0;if((e|0)>0){e=0;l=0;o=0}else return;do{l=l&255|t[i+(c+e<<1)>>1]<<e;n[u>>0]=l;o=o+1<<16>>16;e=o<<16>>16}while((e|0)<(s|0));return}function Qi(e,i,r,o){e=e|0;i=i|0;r=r|0;o=o|0;var l=0,u=0,s=0,c=0,h=0,w=0,d=0,v=0;d=f[o+100>>2]|0;w=f[o+96>>2]|0;n[r>>0]=e&15;w=w+(e<<1)|0;l=t[w>>1]|0;if(e>>>0>=8){c=((l&65535)<<16)+-458752|0;if((c|0)>0){h=1;s=i;while(1){i=s+16|0;o=h+1<<16>>16;n[r+(h<<16>>16)>>0]=a[s+14>>1]|a[s+12>>1]<<1|((a[s+2>>1]<<6|a[s>>1]<<7|a[s+4>>1]<<5|a[s+6>>1]<<4)&240|a[s+8>>1]<<3|a[s+10>>1]<<2)&252;c=c+-524288&-65536;if((c|0)<=0)break;else{h=o;s=i}}l=t[w>>1]|0}else o=1;h=l&7;l=r+(o<<16>>16)|0;n[l>>0]=0;if(!h)return;else{u=0;s=0;c=0;o=i}while(1){s=s&255|t[o>>1]<<7-u;n[l>>0]=s;c=c+1<<16>>16;u=c<<16>>16;if((u|0)>=(h|0))break;else o=o+2|0}return}s=l<<16>>16;if(l<<16>>16>7){l=d+(e<<2)|0;o=0;h=0;u=1;while(1){v=a[i+(t[(f[l>>2]|0)+(o<<1)>>1]<<1)>>1]<<7;s=r+(u<<16>>16)|0;n[s>>0]=v;v=a[i+(t[(f[l>>2]|0)+((h|1)<<16>>16<<1)>>1]<<1)>>1]<<6|v;n[s>>0]=v;v=a[i+(t[(f[l>>2]|0)+((h|2)<<16>>16<<1)>>1]<<1)>>1]<<5|v;n[s>>0]=v;v=a[i+(t[(f[l>>2]|0)+((h|3)<<16>>16<<1)>>1]<<1)>>1]<<4|v;n[s>>0]=v;v=a[i+(t[(f[l>>2]|0)+((h|4)<<16>>16<<1)>>1]<<1)>>1]<<3|v&240;n[s>>0]=v;v=a[i+(t[(f[l>>2]|0)+((h|5)<<16>>16<<1)>>1]<<1)>>1]<<2|v;n[s>>0]=v;v=a[i+(t[(f[l>>2]|0)+((h|6)<<16>>16<<1)>>1]<<1)>>1]<<1|v;n[s>>0]=v;c=h+8<<16>>16;u=u+1<<16>>16;n[s>>0]=v&254|a[i+(t[(f[l>>2]|0)+((h|7)<<16>>16<<1)>>1]<<1)>>1];o=c<<16>>16;s=t[w>>1]|0;if((o|0)>=(s+-7|0))break;else h=c}}else{c=0;u=1}w=s&7;h=r+(u<<16>>16)|0;n[h>>0]=0;if(!w)return;u=d+(e<<2)|0;l=0;o=0;s=0;while(1){o=(a[i+(t[(f[u>>2]|0)+(c<<16>>16<<1)>>1]<<1)>>1]&255)<<7-l|o&255;n[h>>0]=o;s=s+1<<16>>16;l=s<<16>>16;if((l|0)>=(w|0))break;else c=c+1<<16>>16}return}function Zi(e,i,r,o){e=e|0;i=i|0;r=r|0;o=o|0;var l=0,u=0,s=0,c=0,h=0,w=0,d=0,v=0;d=f[o+100>>2]|0;w=f[o+96>>2]|0;n[r>>0]=e<<3;w=w+(e<<1)|0;l=t[w>>1]|0;if(e>>>0>=8){c=((l&65535)<<16)+-458752|0;if((c|0)>0){h=1;s=i;while(1){i=s+16|0;o=h+1<<16>>16;n[r+(h<<16>>16)>>0]=a[s+14>>1]|a[s+12>>1]<<1|((a[s+2>>1]<<6|a[s>>1]<<7|a[s+4>>1]<<5|a[s+6>>1]<<4)&240|a[s+8>>1]<<3|a[s+10>>1]<<2)&252;c=c+-524288&-65536;if((c|0)<=0)break;else{h=o;s=i}}l=t[w>>1]|0}else o=1;h=l&7;l=r+(o<<16>>16)|0;n[l>>0]=0;if(!h)return;else{u=0;s=0;c=0;o=i}while(1){s=s&255|t[o>>1]<<7-u;n[l>>0]=s;c=c+1<<16>>16;u=c<<16>>16;if((u|0)>=(h|0))break;else o=o+2|0}return}s=l<<16>>16;if(l<<16>>16>7){l=d+(e<<2)|0;o=0;h=0;u=1;while(1){v=a[i+(t[(f[l>>2]|0)+(o<<1)>>1]<<1)>>1]<<7;s=r+(u<<16>>16)|0;n[s>>0]=v;v=a[i+(t[(f[l>>2]|0)+((h|1)<<16>>16<<1)>>1]<<1)>>1]<<6|v;n[s>>0]=v;v=a[i+(t[(f[l>>2]|0)+((h|2)<<16>>16<<1)>>1]<<1)>>1]<<5|v;n[s>>0]=v;v=a[i+(t[(f[l>>2]|0)+((h|3)<<16>>16<<1)>>1]<<1)>>1]<<4|v;n[s>>0]=v;v=a[i+(t[(f[l>>2]|0)+((h|4)<<16>>16<<1)>>1]<<1)>>1]<<3|v&240;n[s>>0]=v;v=a[i+(t[(f[l>>2]|0)+((h|5)<<16>>16<<1)>>1]<<1)>>1]<<2|v;n[s>>0]=v;v=a[i+(t[(f[l>>2]|0)+((h|6)<<16>>16<<1)>>1]<<1)>>1]<<1|v;n[s>>0]=v;c=h+8<<16>>16;u=u+1<<16>>16;n[s>>0]=v&254|a[i+(t[(f[l>>2]|0)+((h|7)<<16>>16<<1)>>1]<<1)>>1];o=c<<16>>16;s=t[w>>1]|0;if((o|0)>=(s+-7|0))break;else h=c}}else{c=0;u=1}w=s&7;h=r+(u<<16>>16)|0;n[h>>0]=0;if(!w)return;u=d+(e<<2)|0;l=0;o=0;s=0;while(1){o=(a[i+(t[(f[u>>2]|0)+(c<<16>>16<<1)>>1]<<1)>>1]&255)<<7-l|o&255;n[h>>0]=o;s=s+1<<16>>16;l=s<<16>>16;if((l|0)>=(w|0))break;else c=c+1<<16>>16}return}function $i(e){e=e|0;var i=0;if(!e){e=-1;return e|0}f[e>>2]=0;i=dt(16)|0;if(!i){e=-1;return e|0}t[i>>1]=0;t[i+2>>1]=0;t[i+4>>1]=0;t[i+6>>1]=0;t[i+8>>1]=0;t[i+10>>1]=0;t[i+12>>1]=0;t[i+14>>1]=0;f[e>>2]=i;e=0;return e|0}function Ji(e){e=e|0;if(!e){e=-1;return e|0}t[e>>1]=0;t[e+2>>1]=0;t[e+4>>1]=0;t[e+6>>1]=0;t[e+8>>1]=0;t[e+10>>1]=0;t[e+12>>1]=0;t[e+14>>1]=0;e=0;return e|0}function er(e){e=e|0;var i=0;if(!e)return;i=f[e>>2]|0;if(!i)return;vt(i);f[e>>2]=0;return}function ir(e,i,r,n,o){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;var a=0,l=0,u=0,s=0,c=0;u=i<<16>>16<2722?0:i<<16>>16<5444?1:2;l=ut(r,1,o)|0;c=e+4|0;if(!(r<<16>>16>200?l<<16>>16>(t[c>>1]|0):0)){l=t[e>>1]|0;if(l<<16>>16){a=l+-1<<16>>16;t[e>>1]=a;a=a<<16>>16!=0;s=5}}else{t[e>>1]=8;a=1;s=5}if((s|0)==5)if((u&65535)<2&a)u=(u&65535)+1&65535;s=e+6|0;t[s>>1]=i;a=Nn(s,5)|0;if(!(u<<16>>16!=0|a<<16>>16>5443))if(a<<16>>16<0)a=16384;else{a=a<<16>>16;a=(((a<<18>>18|0)==(a|0)?a<<2:a>>>15^32767)<<16>>16)*24660>>15;if((a|0)>32767){f[o>>2]=1;a=32767}a=16384-a&65535}else a=0;l=e+2|0;if(!(t[l>>1]|0))a=lt(a,1,o)|0;t[n>>1]=a;t[l>>1]=a;t[c>>1]=r;n=e+12|0;t[e+14>>1]=t[n>>1]|0;r=e+10|0;t[n>>1]=t[r>>1]|0;e=e+8|0;t[r>>1]=t[e>>1]|0;t[e>>1]=t[s>>1]|0;return}function rr(e){e=e|0;var i=0,r=0,n=0,o=0,a=0,l=0;if(!e){e=-1;return e|0}f[e>>2]=0;i=dt(68)|0;n=i;if(!i){e=-1;return e|0}f[i+28>>2]=0;o=i+64|0;f[o>>2]=0;a=i+32|0;if(((_n(a)|0)<<16>>16==0?(l=i+48|0,(_n(l)|0)<<16>>16==0):0)?($i(o)|0)<<16>>16==0:0){r=i+32|0;do{t[i>>1]=0;i=i+2|0}while((i|0)<(r|0));_n(a)|0;_n(l)|0;Ji(f[o>>2]|0)|0;f[e>>2]=n;e=0;return e|0}er(o);vt(i);e=-1;return e|0}function nr(e){e=e|0;var i=0;if(!e)return;i=f[e>>2]|0;if(!i)return;er(i+64|0);vt(f[e>>2]|0);f[e>>2]=0;return}function tr(e){e=e|0;var i=0,r=0,n=0;if(!e){n=-1;return n|0}i=e+32|0;r=e;n=r+32|0;do{t[r>>1]=0;r=r+2|0}while((r|0)<(n|0));_n(i)|0;_n(e+48|0)|0;Ji(f[e+64>>2]|0)|0;n=0;return n|0}function fr(e,i,r,n,o,l,u,s,h,w,d,v,b,k,m,y,p,E,g){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;l=l|0;u=u|0;s=s|0;h=h|0;w=w|0;d=d|0;v=v|0;b=b|0;k=k|0;m=m|0;y=y|0;p=p|0;E=E|0;g=g|0;var A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,I=0,L=0;L=c;c=c+48|0;S=L+34|0;R=L+32|0;D=L+30|0;M=L+28|0;_=L+18|0;A=L+8|0;O=L+6|0;N=L+4|0;T=L+2|0;I=L;if(i){d=e+32|0;Rn(d,i,o,S,R,O,N,g);do{if((i|0)!=7){_i(i,l,u,s,h,w,_,A,I,T,g);if((i|0)==5){Wr(f[e+64>>2]|0,r,n,o,_,A,t[O>>1]|0,t[N>>1]|0,t[S>>1]|0,t[R>>1]|0,40,t[I>>1]|0,t[T>>1]|0,v,m,y,D,M,p,E,g);break}else{e=Xr(i,t[S>>1]|0,t[R>>1]|0,_,A,v,m,y,D,M,E,g)|0;l=f[p>>2]|0;f[p>>2]=l+2;t[l>>1]=e;break}}else{t[y>>1]=or(u,h,g)|0;e=qr(7,t[S>>1]|0,t[R>>1]|0,y,D,M,f[E+68>>2]|0,g)|0;l=f[p>>2]|0;f[p>>2]=l+2;t[l>>1]=e}}while(0);Mn(d,t[D>>1]|0,t[M>>1]|0);c=L;return}if(!(d<<16>>16)){Rn(e+48|0,0,o,S,R,O,N,g);_i(0,l,u,s,h,w,_,A,I,T,g);Ri(l,O,N,g);l=jr(e+32|0,t[e>>1]|0,t[e+2>>1]|0,e+8|0,e+18|0,t[e+4>>1]|0,t[e+6>>1]|0,o,t[S>>1]|0,t[R>>1]|0,A,_,t[O>>1]|0,t[N>>1]|0,v,b,k,m,y,g)|0;t[f[e+28>>2]>>1]=l;c=L;return}d=f[p>>2]|0;f[p>>2]=d+2;f[e+28>>2]=d;d=e+48|0;r=e+32|0;b=r;b=a[b>>1]|a[b+2>>1]<<16;r=r+4|0;r=a[r>>1]|a[r+2>>1]<<16;p=d;k=p;t[k>>1]=b;t[k+2>>1]=b>>>16;p=p+4|0;t[p>>1]=r;t[p+2>>1]=r>>>16;p=e+40|0;r=p;r=a[r>>1]|a[r+2>>1]<<16;p=p+4|0;p=a[p>>1]|a[p+2>>1]<<16;k=e+56|0;b=k;t[b>>1]=r;t[b+2>>1]=r>>>16;k=k+4|0;t[k>>1]=p;t[k+2>>1]=p>>>16;k=e+2|0;Rn(d,0,o,e,k,O,N,g);_i(0,l,u,s,h,w,e+18|0,e+8|0,I,T,g);s=(a[T>>1]|0)+1|0;p=t[I>>1]|0;b=s<<16>>16;if((s&65535)<<16>>16<0){E=0-b<<16;if((E|0)<983040)E=p<<16>>16>>(E>>16)&65535;else E=0}else{p=p<<16>>16;E=p<<b;if((E<<16>>16>>b|0)==(p|0))E=E&65535;else E=(p>>>15^32767)&65535}t[y>>1]=E;Ri(l,e+4|0,e+6|0,g);zr(d,t[e>>1]|0,t[k>>1]|0,t[T>>1]|0,t[I>>1]|0,g);c=L;return}function or(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,f=0,o=0;f=10;r=e;n=i;e=0;while(1){e=(I(t[n>>1]>>1,t[r>>1]|0)|0)+e|0;e=e+(I(t[n+2>>1]>>1,t[r+2>>1]|0)|0)|0;e=e+(I(t[n+4>>1]>>1,t[r+4>>1]|0)|0)|0;e=e+(I(t[n+6>>1]>>1,t[r+6>>1]|0)|0)|0;f=f+-1<<16>>16;if(!(f<<16>>16))break;else{r=r+8|0;n=n+8|0}}r=e<<1;f=Kn(r|1)|0;o=f<<16>>16;r=(f<<16>>16<17?r>>17-o:r<<o+-17)&65535;if(r<<16>>16<1){i=0;return i|0}else{f=20;n=i;e=0}while(1){i=t[n>>1]>>1;i=((I(i,i)|0)>>>2)+e|0;e=t[n+2>>1]>>1;e=i+((I(e,e)|0)>>>2)|0;f=f+-1<<16>>16;if(!(f<<16>>16))break;else n=n+4|0}e=e<<3;f=Kn(e)|0;i=f<<16>>16;r=Sn(r,(f<<16>>16<16?e>>16-i:e<<i+-16)&65535)|0;i=(o<<16)+327680-(i<<16)|0;e=i>>16;if((i|0)>65536)e=r<<16>>16>>e+-1;else e=r<<16>>16<<1-e;i=e&65535;return i|0}function ar(e,i,r,n,o,a){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;var l=0,u=0,s=0,c=0,h=0,w=0,d=0,v=0,b=0,k=0;f[a>>2]=0;h=o<<16>>16;s=h>>>2&65535;d=s<<16>>16==0;if(d)u=0;else{c=s;l=r;u=0;while(1){v=t[l>>1]|0;v=(I(v,v)|0)+u|0;u=t[l+2>>1]|0;u=v+(I(u,u)|0)|0;v=t[l+4>>1]|0;v=u+(I(v,v)|0)|0;u=t[l+6>>1]|0;u=v+(I(u,u)|0)|0;c=c+-1<<16>>16;if(!(c<<16>>16))break;else l=l+8|0}}if(!((u>>>31^1)&(u|0)<1073741824)){u=h>>>1&65535;if(!(u<<16>>16))u=1;else{l=u;c=r;u=0;while(1){v=t[c>>1]>>2;v=(I(v,v)|0)+u|0;u=t[c+2>>1]>>2;u=v+(I(u,u)|0)|0;l=l+-1<<16>>16;if(!(l<<16>>16))break;else c=c+4|0}u=u<<1|1}v=(Kn(u)|0)<<16>>16;w=v+65532&65535;v=at(u<<v,a)|0}else{h=u<<1|1;v=Kn(h)|0;w=v;v=at(h<<(v<<16>>16),a)|0}f[a>>2]=0;do{if(!(o<<16>>16)){u=1;b=14}else{h=o;c=i;u=r;o=0;while(1){k=I(t[u>>1]|0,t[c>>1]|0)|0;l=k+o|0;if((k^o|0)>0&(l^o|0)<0)break;h=h+-1<<16>>16;if(!(h<<16>>16)){b=13;break}else{c=c+2|0;u=u+2|0;o=l}}if((b|0)==13){u=l<<1|1;b=14;break}f[a>>2]=1;if(d)u=1;else{u=i;l=0;while(1){l=(I(t[r>>1]>>2,t[u>>1]|0)|0)+l|0;l=l+(I(t[r+2>>1]>>2,t[u+2>>1]|0)|0)|0;l=l+(I(t[r+4>>1]>>2,t[u+4>>1]|0)|0)|0;l=l+(I(t[r+6>>1]>>2,t[u+6>>1]|0)|0)|0;s=s+-1<<16>>16;if(!(s<<16>>16))break;else{u=u+8|0;r=r+8|0}}u=l<<1|1}r=(Kn(u)|0)<<16>>16;l=r+65532&65535;r=at(u<<r,a)|0}}while(0);if((b|0)==14){r=Kn(u)|0;l=r;r=at(u<<(r<<16>>16),a)|0}t[n>>1]=v;u=w<<16>>16;t[n+2>>1]=15-u;t[n+4>>1]=r;l=l<<16>>16;t[n+6>>1]=15-l;if(r<<16>>16<4){k=0;return k|0}l=lt(Sn(r<<16>>16>>>1&65535,v)|0,l-u&65535,a)|0;l=l<<16>>16>19661?19661:l;if((e|0)!=7){k=l;return k|0}k=l&65532;return k|0}function lr(e,i,r,n,o,a,l){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;l=l|0;var u=0,s=0,c=0,h=0,w=0,d=0;s=(n&65535)+65535&65535;if(s<<16>>16>o<<16>>16){w=n+-1<<16>>16<<16>>16;n=-2147483648;while(1){c=f[e+(0-w<<2)>>2]|0;u=c<<1;c=(u>>1|0)==(c|0)?u:c>>31^2147483647;u=f[e+(~w<<2)>>2]|0;h=c-u|0;if(((h^c)&(c^u)|0)<0){f[l>>2]=1;h=(c>>>31)+2147483647|0}c=f[e+(1-w<<2)>>2]|0;u=h-c|0;if(((u^h)&(c^h)|0)<0){f[l>>2]=1;u=(h>>>31)+2147483647|0}h=sr(u)|0;n=(h|0)<(n|0)?n:h;s=s+-1<<16>>16;if(s<<16>>16<=o<<16>>16){o=n;break}else w=w+-1|0}}else o=-2147483648;e=r<<16>>16>0;if(e){n=0;u=i;s=0;while(1){h=t[u>>1]|0;h=I(h,h)|0;if((h|0)!=1073741824){c=(h<<1)+s|0;if((h^s|0)>0&(c^s|0)<0){f[l>>2]=1;s=(s>>>31)+2147483647|0}else s=c}else{f[l>>2]=1;s=2147483647}n=n+1<<16>>16;if(n<<16>>16>=r<<16>>16)break;else u=u+2|0}if(e){e=0;w=i;n=i+-2|0;u=0;while(1){h=I(t[n>>1]|0,t[w>>1]|0)|0;if((h|0)!=1073741824){c=(h<<1)+u|0;if((h^u|0)>0&(c^u|0)<0){f[l>>2]=1;u=(u>>>31)+2147483647|0}else u=c}else{f[l>>2]=1;u=2147483647}e=e+1<<16>>16;if(e<<16>>16>=r<<16>>16)break;else{w=w+2|0;n=n+2|0}}}else u=0}else{s=0;u=0}n=s<<1;n=(n>>1|0)==(s|0)?n:s>>31^2147483647;r=u<<1;r=(r>>1|0)==(u|0)?r:u>>31^2147483647;s=n-r|0;if(((s^n)&(r^n)|0)<0){f[l>>2]=1;s=(n>>>31)+2147483647|0}e=sr(s)|0;w=((Kn(o)|0)&65535)+65535|0;s=w<<16>>16;if((w&65535)<<16>>16>0){n=o<<s;if((n>>s|0)!=(o|0))n=o>>31^2147483647}else{s=0-s<<16;if((s|0)<2031616)n=o>>(s>>16);else n=0}h=Kn(e)|0;u=h<<16>>16;if(h<<16>>16>0){s=e<<u;if((s>>u|0)==(e|0))d=33;else{s=e>>31^2147483647;d=33}}else{s=0-u<<16;if((s|0)<2031616){s=e>>(s>>16);d=33}else c=0}if((d|0)==33)if(s>>>0>65535)c=Sn(n>>>16&65535,s>>>16&65535)|0;else c=0;s=h&65535;d=(w&65535)-s|0;n=d&65535;if(!(d&32768)){l=lt(c,n,l)|0;t[a>>1]=l;return 0}if(n<<16>>16!=-32768){l=s-w|0;u=l<<16>>16;if((l&65535)<<16>>16<0){u=0-u<<16;if((u|0)>=983040){l=0;t[a>>1]=l;return 0}l=c<<16>>16>>(u>>16)&65535;t[a>>1]=l;return 0}}else u=32767;n=c<<16>>16;s=n<<u;if((s<<16>>16>>u|0)==(n|0)){l=s&65535;t[a>>1]=l;return 0}l=(n>>>15^32767)&65535;t[a>>1]=l;return 0}function ur(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;if(r<<16>>16)i=i<<16>>16<<1&65535;if(i<<16>>16<0){e=e+-2|0;i=(i&65535)+6&65535}r=i<<16>>16;n=6-r<<16>>16;i=(I(t[3468+(r<<1)>>1]|0,t[e>>1]|0)|0)+16384|0;i=i+(I(t[3468+(n<<1)>>1]|0,t[e+2>>1]|0)|0)|0;i=i+(I(t[3468+(r+6<<1)>>1]|0,t[e+-2>>1]|0)|0)|0;i=i+(I(t[3468+(n+6<<1)>>1]|0,t[e+4>>1]|0)|0)|0;i=(I(t[3468+(r+12<<1)>>1]|0,t[e+-4>>1]|0)|0)+i|0;i=i+(I(t[3468+(n+12<<1)>>1]|0,t[e+6>>1]|0)|0)|0;r=i+(I(t[3468+(r+18<<1)>>1]|0,t[e+-6>>1]|0)|0)|0;return(r+(I(t[3468+(n+18<<1)>>1]|0,t[e+8>>1]|0)|0)|0)>>>15&65535|0}function sr(e){e=e|0;e=e-(e>>>31)|0;return e>>31^e|0}function cr(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var f=0,o=0,a=0,l=0,u=0;if(!(e<<16>>16))return;else{f=3518;o=3538;n=r}while(1){n=n+2|0;i=i+2|0;u=t[i>>1]|0;l=t[f>>1]|0;r=I(l,u)|0;r=(r|0)==1073741824?2147483647:r<<1;u=(I(t[o>>1]|0,u)|0)>>15;a=(u<<1)+r|0;a=(r^u|0)>0&(a^r|0)<0?(r>>>31)+2147483647|0:a;l=(I(l,t[n>>1]|0)|0)>>15;r=a+(l<<1)|0;r=(a^l|0)>0&(r^a|0)<0?(a>>>31)+2147483647|0:r;t[i>>1]=r>>>16;t[n>>1]=(r>>>1)-(r>>16<<15);e=e+-1<<16>>16;if(!(e<<16>>16))break;else{f=f+2|0;o=o+2|0}}return}function hr(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,t=0;n=e&65535;t=n<<16;i=i<<16>>16;e=(i<<1)+t|0;if(!((i^t|0)>0&(e^t|0)<0)){t=e;return t|0}f[r>>2]=1;t=(n>>>15)+2147483647|0;return t|0}function wr(e){e=e|0;var i=0,r=0,n=0;if(!e){n=-1;return n|0}f[e>>2]=0;i=dt(22)|0;if(!i){n=-1;return n|0}t[i>>1]=4096;r=i+2|0;n=r+20|0;do{t[r>>1]=0;r=r+2|0}while((r|0)<(n|0));f[e>>2]=i;n=0;return n|0}function dr(e){e=e|0;var i=0;if(!e){i=-1;return i|0}t[e>>1]=4096;e=e+2|0;i=e+20|0;do{t[e>>1]=0;e=e+2|0}while((e|0)<(i|0));i=0;return i|0}function vr(e){e=e|0;var i=0;if(!e)return;i=f[e>>2]|0;if(!i)return;vt(i);f[e>>2]=0;return}function br(e,i,r,n,f,o){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;o=o|0;var l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,L=0,F=0,C=0,P=0,B=0,U=0,x=0;B=c;c=c+96|0;C=B+66|0;P=B+44|0;F=B+22|0;u=B;O=i+2|0;L=r+2|0;T=(t[L>>1]<<1)+(a[O>>1]<<16)|0;l=sr(T)|0;l=zi(l,t[i>>1]|0,t[r>>1]|0,o)|0;if((T|0)>0)l=mr(l)|0;M=l>>16;t[f>>1]=at(l,o)|0;E=l>>20;N=C+2|0;t[N>>1]=E;T=P+2|0;t[T>>1]=(l>>>5)-(E<<15);E=I(M,M)|0;E=(E|0)==1073741824?2147483647:E<<1;M=(I((l>>>1)-(M<<15)<<16>>16,M)|0)>>15;D=M<<1;R=D+E|0;R=(M^E|0)>0&(R^E|0)<0?(E>>>31)+2147483647|0:R;D=R+D|0;D=2147483647-(sr((R^M|0)>0&(D^R|0)<0?(R>>>31)+2147483647|0:D)|0)|0;R=D>>16;M=t[i>>1]|0;E=I(R,M)|0;E=(E|0)==1073741824?2147483647:E<<1;M=(I((D>>>1)-(R<<15)<<16>>16,M)|0)>>15;D=(M<<1)+E|0;D=(M^E|0)>0&(D^E|0)<0?(E>>>31)+2147483647|0:D;R=(I(t[r>>1]|0,R)|0)>>15;E=D+(R<<1)|0;E=(D^R|0)>0&(E^D|0)<0?(D>>>31)+2147483647|0:E;D=Kn(E)|0;E=E<<(D<<16>>16);R=F+2|0;M=u+2|0;s=E;E=(E>>>1)-(E>>16<<15)|0;g=u+4|0;A=F+4|0;S=2;_=2;while(1){p=s>>>16;l=p&65535;k=E&65535;m=_+-1|0;w=C+(m<<1)|0;y=P+(m<<1)|0;b=1;v=w;d=y;h=O;u=L;s=0;while(1){U=t[h>>1]|0;x=((I(t[d>>1]|0,U)|0)>>15)+s|0;s=t[v>>1]|0;s=x+(I(s,U)|0)+((I(s,t[u>>1]|0)|0)>>15)|0;b=b+1<<16>>16;if((b<<16>>16|0)>=(_|0))break;else{v=v+-2|0;d=d+-2|0;h=h+2|0;u=u+2|0}}x=(a[i+(_<<1)>>1]<<16)+(s<<5)+(t[r+(_<<1)>>1]<<1)|0;s=zi(sr(x)|0,l,k,o)|0;if((x|0)>0)s=mr(s)|0;u=D<<16>>16;if(D<<16>>16>0){l=s<<u;if((l>>u|0)!=(s|0))l=s>>31^2147483647}else{u=0-u<<16;if((u|0)<2031616)l=s>>(u>>16);else l=0}b=l>>16;if((_|0)<5)t[f+(m<<1)>>1]=(l+32768|0)>>>16;x=(l>>>16)-(l>>>31)|0;if(((x<<16>>31^x)&65535)<<16>>16>32750){l=16;break}d=(l>>>1)-(b<<15)<<16>>16;v=1;s=y;u=R;h=M;while(1){U=(I(t[s>>1]|0,b)|0)>>15;y=t[w>>1]|0;x=(I(y,d)|0)>>15;y=I(y,b)|0;x=y+U+(t[P+(v<<1)>>1]|0)+(t[C+(v<<1)>>1]<<15)+x|0;t[u>>1]=x>>>15;t[h>>1]=x&32767;v=v+1|0;if((v&65535)<<16>>16==S<<16>>16)break;else{w=w+-2|0;s=s+-2|0;u=u+2|0;h=h+2|0}}t[A>>1]=l>>20;t[g>>1]=(l>>>5)-(t[F+(_<<1)>>1]<<15);U=I(b,b)|0;U=(U|0)==1073741824?2147483647:U<<1;l=(I(d,b)|0)>>15;x=l<<1;u=x+U|0;u=(l^U|0)>0&(u^U|0)<0?(U>>>31)+2147483647|0:u;x=u+x|0;x=2147483647-(sr((u^l|0)>0&(x^u|0)<0?(u>>>31)+2147483647|0:x)|0)|0;u=x>>16;l=p<<16>>16;l=((I(u,E<<16>>16)|0)>>15)+(I(u,l)|0)+((I((x>>>1)-(u<<15)<<16>>16,l)|0)>>15)<<1;u=(Kn(l)|0)<<16>>16;l=l<<u;x=_<<1;yt(N|0,R|0,x|0)|0;yt(T|0,M|0,x|0)|0;_=_+1|0;if((_|0)>=11){l=20;break}else{D=u+(D&65535)&65535;s=l;E=(l>>1)-(l>>16<<15)|0;g=g+2|0;A=A+2|0;S=S+1<<16>>16}}if((l|0)==16){l=n+22|0;do{t[n>>1]=t[e>>1]|0;n=n+2|0;e=e+2|0}while((n|0)<(l|0));x=f;U=x;t[U>>1]=0;t[U+2>>1]=0>>>16;x=x+4|0;t[x>>1]=0;t[x+2>>1]=0>>>16;c=B;return 0}else if((l|0)==20){t[n>>1]=4096;x=((t[T>>1]|0)+8192+(t[N>>1]<<15)|0)>>>14&65535;t[n+2>>1]=x;t[e+2>>1]=x;x=((t[P+4>>1]|0)+8192+(t[C+4>>1]<<15)|0)>>>14&65535;t[n+4>>1]=x;t[e+4>>1]=x;x=((t[P+6>>1]|0)+8192+(t[C+6>>1]<<15)|0)>>>14&65535;t[n+6>>1]=x;t[e+6>>1]=x;x=((t[P+8>>1]|0)+8192+(t[C+8>>1]<<15)|0)>>>14&65535;t[n+8>>1]=x;t[e+8>>1]=x;x=((t[P+10>>1]|0)+8192+(t[C+10>>1]<<15)|0)>>>14&65535;t[n+10>>1]=x;t[e+10>>1]=x;x=((t[P+12>>1]|0)+8192+(t[C+12>>1]<<15)|0)>>>14&65535;t[n+12>>1]=x;t[e+12>>1]=x;x=((t[P+14>>1]|0)+8192+(t[C+14>>1]<<15)|0)>>>14&65535;t[n+14>>1]=x;t[e+14>>1]=x;x=((t[P+16>>1]|0)+8192+(t[C+16>>1]<<15)|0)>>>14&65535;t[n+16>>1]=x;t[e+16>>1]=x;x=((t[P+18>>1]|0)+8192+(t[C+18>>1]<<15)|0)>>>14&65535;t[n+18>>1]=x;t[e+18>>1]=x;x=((t[P+20>>1]|0)+8192+(t[C+20>>1]<<15)|0)>>>14&65535;t[n+20>>1]=x;t[e+20>>1]=x;c=B;return 0}return 0}function kr(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;n=e>>16;t[i>>1]=n;t[r>>1]=(e>>>1)-(n<<15);return}function mr(e){e=e|0;return((e|0)==-2147483648?2147483647:0-e|0)|0}function yr(e){e=e|0;var i=0;if(!e){e=-1;return e|0}f[e>>2]=0;i=dt(4)|0;if(!i){e=-1;return e|0}f[i>>2]=0;if(!((wr(i)|0)<<16>>16)){dr(f[i>>2]|0)|0;f[e>>2]=i;e=0;return e|0}else{vr(i);vt(i);e=-1;return e|0}return 0}function pr(e){e=e|0;var i=0;if(!e)return;i=f[e>>2]|0;if(!i)return;vr(i);vt(f[e>>2]|0);f[e>>2]=0;return}function Er(e){e=e|0;if(!e){e=-1;return e|0}dr(f[e>>2]|0)|0;e=0;return e|0}function gr(e,i,r,n,t,o,a){e=e|0;i=i|0;r=r|0;n=n|0;t=t|0;o=o|0;a=a|0;var l=0,u=0,s=0,h=0;h=c;c=c+64|0;s=h+48|0;u=h+22|0;l=h;if((i|0)==7){r=f[o+116>>2]|0;di(n,10,l,u,f[o+112>>2]|0,a)|0;cr(10,l,u,a);br(f[e>>2]|0,l,u,t+22|0,s,a)|0;di(n,10,l,u,r,a)|0;cr(10,l,u,a);br(f[e>>2]|0,l,u,t+66|0,s,a)|0;c=h;return}else{di(r,10,l,u,f[o+108>>2]|0,a)|0;cr(10,l,u,a);br(f[e>>2]|0,l,u,t+66|0,s,a)|0;c=h;return}}function Ar(e,i,r,n,f,o,a,l,u,s){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;o=o|0;a=a|0;l=l|0;u=u|0;s=s|0;if((r|0)==6){t[f>>1]=Lr(e,i,n,20,143,80,o,a,l,u,s)|0;return}t[a>>1]=0;t[a+2>>1]=0;if(r>>>0<2){t[f>>1]=Dr(i,r,n,20,143,160,l,u,s)|0;return}if(r>>>0<6){t[f>>1]=Dr(i,r,n,20,143,80,l,u,s)|0;return}else{t[f>>1]=Dr(i,r,n,18,143,80,l,u,s)|0;return}}function Sr(e){e=e|0;var i=0;if((e|0)!=0?(f[e>>2]=0,i=dt(2)|0,(i|0)!=0):0){t[i>>1]=0;f[e>>2]=i;i=0}else i=-1;return i|0}function _r(e){e=e|0;if(!e)e=-1;else{t[e>>1]=0;e=0}return e|0}function Rr(e){e=e|0;var i=0;if(!e)return;i=f[e>>2]|0;if(!i)return;vt(i);f[e>>2]=0;return}function Mr(e,i,r,n,f,o,l,u,s,h,w,d){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;o=o|0;l=l|0;u=u|0;s=s|0;h=h|0;w=w|0;d=d|0;var v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,L=0,F=0,C=0,P=0,B=0,U=0,x=0,z=0,j=0,H=0,W=0,q=0,G=0,X=0,V=0;V=c;c=c+240|0;p=V+160|0;E=V+80|0;j=V;z=t[3558+(i*18|0)>>1]|0;X=t[3558+(i*18|0)+2>>1]|0;v=t[3558+(i*18|0)+4>>1]|0;H=t[3558+(i*18|0)+6>>1]|0;m=t[3558+(i*18|0)+12>>1]|0;k=t[3558+(i*18|0)+14>>1]|0;b=t[3558+(i*18|0)+16>>1]|0;e:do{switch(u<<16>>16){case 0:case 80:if(i>>>0<2&u<<16>>16==80){W=(a[e>>1]|0)-(m&65535)|0;W=(W<<16>>16|0)<(b<<16>>16|0)?b:W&65535;x=k<<16>>16;q=(W&65535)+x&65535;G=q<<16>>16>143;W=G?143-x&65535:W;q=G?143:q;G=1;break e}else{W=(a[r+((u<<16>>16!=0&1)<<1)>>1]|0)-(a[3558+(i*18|0)+8>>1]|0)|0;W=(W<<16>>16|0)<(b<<16>>16|0)?b:W&65535;x=t[3558+(i*18|0)+10>>1]|0;q=(W&65535)+x&65535;G=q<<16>>16>143;W=G?143-x&65535:W;q=G?143:q;G=0;break e}default:{W=(a[e>>1]|0)-(m&65535)|0;W=(W<<16>>16|0)<(b<<16>>16|0)?b:W&65535;x=k<<16>>16;q=(W&65535)+x&65535;G=q<<16>>16>143;W=G?143-x&65535:W;q=G?143:q;G=1}}}while(0);U=W&65535;u=U+65532|0;y=u&65535;B=(q&65535)+4&65535;x=u<<16>>16;u=0-(u&65535)|0;m=u&65535;Pi(n+(u<<16>>16<<1)|0,o,p,l);u=l<<16>>16;M=u>>>1&65535;g=M<<16>>16==0;if(g)l=1;else{l=M;b=p;r=E;k=0;while(1){P=t[b>>1]|0;t[r>>1]=P>>>2;P=(I(P,P)|0)+k|0;k=t[b+2>>1]|0;t[r+2>>1]=k>>>2;k=P+(I(k,k)|0)|0;l=l+-1<<16>>16;if(!(l<<16>>16))break;else{b=b+4|0;r=r+4|0}}l=(k|0)<33554433}P=l?0:2;R=l?p:E;A=l?p:E;e:do{if(y<<16>>16<=B<<16>>16){S=u+-1|0;L=R+(S<<1)|0;F=o+(S<<1)|0;C=R+(u+-2<<1)|0;O=S>>>1;N=O&65535;_=N<<16>>16==0;T=l?12:14;O=(O<<1)+131070&131070;r=u+-3-O|0;D=R+(r<<1)|0;O=R+(u+-4-O<<1)|0;o=o+(r<<1)|0;if(!g){g=x;while(1){E=M;p=A;b=f;k=0;l=0;while(1){E=E+-1<<16>>16;u=t[p>>1]|0;k=(I(u,t[b>>1]|0)|0)+k|0;u=(I(u,u)|0)+l|0;l=t[p+2>>1]|0;k=k+(I(l,t[b+2>>1]|0)|0)|0;l=u+(I(l,l)|0)|0;if(!(E<<16>>16))break;else{p=p+4|0;b=b+4|0}}p=Cn(l<<1,d)|0;l=p>>16;b=k<<1>>16;E=I(l,b)|0;E=(E|0)==1073741824?2147483647:E<<1;b=(I((p>>>1)-(l<<15)<<16>>16,b)|0)>>15;p=(b<<1)+E|0;p=(b^E|0)>0&(p^E|0)<0?(E>>>31)+2147483647|0:p;l=(I(l,k&32767)|0)>>15;E=p+(l<<1)|0;t[j+(g-x<<1)>>1]=(p^l|0)>0&(E^p|0)<0?(p>>>31)+65535|0:E;if(y<<16>>16!=B<<16>>16){m=m+-1<<16>>16;E=t[n+(m<<16>>16<<1)>>1]|0;if(_){p=S;l=C;k=F;b=L}else{p=N;l=C;k=F;b=L;while(1){g=(I(t[k>>1]|0,E)|0)>>T;t[b>>1]=g+(a[l>>1]|0);g=(I(t[k+-2>>1]|0,E)|0)>>T;t[b+-2>>1]=g+(a[l+-2>>1]|0);p=p+-1<<16>>16;if(!(p<<16>>16)){p=r;l=O;k=o;b=D;break}else{l=l+-4|0;k=k+-4|0;b=b+-4|0}}}g=(I(t[k>>1]|0,E)|0)>>T;t[b>>1]=g+(a[l>>1]|0);t[R+(p+-1<<1)>>1]=E>>P}y=y+1<<16>>16;if(y<<16>>16>B<<16>>16)break e;else g=y<<16>>16}}if(_){l=R+(u+-2<<1)|0;k=x;while(1){Cn(0,d)|0;t[j+(k-x<<1)>>1]=0;if(y<<16>>16!=B<<16>>16){m=m+-1<<16>>16;f=t[n+(m<<16>>16<<1)>>1]|0;N=(I(t[F>>1]|0,f)|0)>>T;t[L>>1]=N+(a[C>>1]|0);t[l>>1]=f>>P}y=y+1<<16>>16;if(y<<16>>16>B<<16>>16)break e;else k=y<<16>>16}}p=R+(r+-1<<1)|0;l=x;while(1){Cn(0,d)|0;t[j+(l-x<<1)>>1]=0;if(y<<16>>16!=B<<16>>16){m=m+-1<<16>>16;l=t[n+(m<<16>>16<<1)>>1]|0;k=N;b=C;r=F;u=L;while(1){f=(I(t[r>>1]|0,l)|0)>>T;t[u>>1]=f+(a[b>>1]|0);f=(I(t[r+-2>>1]|0,l)|0)>>T;t[u+-2>>1]=f+(a[b+-2>>1]|0);k=k+-1<<16>>16;if(!(k<<16>>16))break;else{b=b+-4|0;r=r+-4|0;u=u+-4|0}}f=(I(t[o>>1]|0,l)|0)>>T;t[D>>1]=f+(a[O>>1]|0);t[p>>1]=l>>P}y=y+1<<16>>16;if(y<<16>>16>B<<16>>16)break;else l=y<<16>>16}}}while(0);y=W<<16>>16;r=U+1&65535;if(r<<16>>16>q<<16>>16)o=W;else{m=W;u=t[j+(y-x<<1)>>1]|0;while(1){k=t[j+((r<<16>>16)-x<<1)>>1]|0;b=k<<16>>16<u<<16>>16;m=b?m:r;r=r+1<<16>>16;if(r<<16>>16>q<<16>>16){o=m;break}else u=b?u:k}}e:do{if(!(G<<16>>16==0?o<<16>>16>z<<16>>16:0)){if(!(i>>>0<4&G<<16>>16!=0)){m=j+((o<<16>>16)-x<<1)|0;k=ur(m,v,X,d)|0;r=(v&65535)+1&65535;if(r<<16>>16<=H<<16>>16)while(1){b=ur(m,r,X,d)|0;u=b<<16>>16>k<<16>>16;v=u?r:v;r=r+1<<16>>16;if(r<<16>>16>H<<16>>16)break;else k=u?b:k}if((i+-7|0)>>>0<2){H=v<<16>>16==-3;r=(H<<31>>31)+o<<16>>16;v=H?3:v;break}switch(v<<16>>16){case-2:{r=o+-1<<16>>16;v=1;break e}case 2:{r=o+1<<16>>16;v=-1;break e}default:{r=o;break e}}}z=t[e>>1]|0;z=((z<<16>>16)-y|0)>5?y+5&65535:z;u=q<<16>>16;z=(u-(z<<16>>16)|0)>4?u+65532&65535:z;u=o<<16>>16;r=z<<16>>16;if((u|0)==(r+-1|0)?1:o<<16>>16==z<<16>>16){m=j+(u-x<<1)|0;u=ur(m,v,X,d)|0;r=(v&65535)+1&65535;if(r<<16>>16<=H<<16>>16)while(1){k=ur(m,r,X,d)|0;b=k<<16>>16>u<<16>>16;v=b?r:v;r=r+1<<16>>16;if(r<<16>>16>H<<16>>16)break;else u=b?k:u}if((i+-7|0)>>>0<2){H=v<<16>>16==-3;r=(H<<31>>31)+o<<16>>16;v=H?3:v;break}switch(v<<16>>16){case-2:{r=o+-1<<16>>16;v=1;break e}case 2:{r=o+1<<16>>16;v=-1;break e}default:{r=o;break e}}}if((u|0)==(r+-2|0)){r=j+(u-x<<1)|0;u=ur(r,0,X,d)|0;if((i|0)!=8){v=0;m=1;while(1){k=ur(r,m,X,d)|0;b=k<<16>>16>u<<16>>16;v=b?m:v;m=m+1<<16>>16;if(m<<16>>16>H<<16>>16)break;else u=b?k:u}if((i+-7|0)>>>0>=2)switch(v<<16>>16){case-2:{r=o+-1<<16>>16;v=1;break e}case 2:{r=o+1<<16>>16;v=-1;break e}default:{r=o;break e}}}else v=0;H=v<<16>>16==-3;r=(H<<31>>31)+o<<16>>16;v=H?3:v;break}if((u|0)==(r+1|0)){m=j+(u-x<<1)|0;r=ur(m,v,X,d)|0;u=(v&65535)+1&65535;if(u<<16>>16<=0)while(1){b=ur(m,u,X,d)|0;k=b<<16>>16>r<<16>>16;v=k?u:v;u=u+1<<16>>16;if(u<<16>>16>0)break;else r=k?b:r}if((i+-7|0)>>>0<2){H=v<<16>>16==-3;r=(H<<31>>31)+o<<16>>16;v=H?3:v;break}switch(v<<16>>16){case-2:{r=o+-1<<16>>16;v=1;break e}case 2:{r=o+1<<16>>16;v=-1;break e}default:{r=o;break e}}}else{r=o;v=0}}else{r=o;v=0}}while(0);if((i+-7|0)>>>0>1){H=e;e=Vi(r,v,t[e>>1]|0,W,q,G,i>>>0<4&1,d)|0;t[w>>1]=e;t[H>>1]=r;t[h>>1]=X;t[s>>1]=v;c=V;return r|0}else{d=Ki(r,v,W,G,d)|0;t[w>>1]=d;t[e>>1]=r;t[h>>1]=X;t[s>>1]=v;c=V;return r|0}return 0}function Dr(e,i,r,n,o,a,l,u,s){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;l=l|0;u=u|0;s=s|0;var h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0;O=c;c=c+1200|0;M=O+1188|0;R=O+580|0;D=O+578|0;_=O+576|0;E=O;A=O+582|0;S=(u|0)!=0;do{if(S)if(i>>>0<2){bn(e,1,s);break}else{bn(e,0,s);break}}while(0);g=o<<16>>16;d=0-g|0;w=r+(d<<1)|0;d=d&65535;m=a<<16>>16;do{if(d<<16>>16<a<<16>>16){k=d;b=w;d=0;while(1){y=t[b>>1]|0;d=(I(y<<1,y)|0)+d|0;if((d|0)<0)break;k=k+1<<16>>16;if(k<<16>>16>=a<<16>>16){p=14;break}else b=b+2|0}if((p|0)==14){if((d|0)<1048576){p=15;break}yt(A|0,w|0,m+g<<1|0)|0;y=0;break}h=m+g|0;v=h>>>1;k=v&65535;if(!(k<<16>>16))d=A;else{y=((v<<1)+131070&131070)+2|0;m=y-g|0;b=A;while(1){t[b>>1]=(t[w>>1]|0)>>>3;t[b+2>>1]=(t[w+2>>1]|0)>>>3;k=k+-1<<16>>16;if(!(k<<16>>16))break;else{w=w+4|0;b=b+4|0}}w=r+(m<<1)|0;d=A+(y<<1)|0}if(!(h&1))y=3;else{t[d>>1]=(t[w>>1]|0)>>>3;y=3}}else p=15}while(0);if((p|0)==15){y=m+g|0;d=y>>>1;v=d&65535;if(!(v<<16>>16))d=A;else{m=((d<<1)+131070&131070)+2|0;b=m-g|0;k=A;while(1){t[k>>1]=t[w>>1]<<3;t[k+2>>1]=t[w+2>>1]<<3;v=v+-1<<16>>16;if(!(v<<16>>16))break;else{w=w+4|0;k=k+4|0}}w=r+(b<<1)|0;d=A+(m<<1)|0}if(!(y&1))y=-3;else{t[d>>1]=t[w>>1]<<3;y=-3}}m=E+(g<<2)|0;b=A+(g<<1)|0;Ai(b,a,o,n,m);h=(i|0)==7&1;d=n<<16>>16;w=d<<2;if((w|0)!=(d<<18>>16|0)){f[s>>2]=1;w=n<<16>>16>0?32767:-32768}k=Or(e,m,b,y,h,a,o,w&65535,M,u,s)|0;d=d<<1;v=Or(e,m,b,y,h,a,w+65535&65535,d&65535,R,u,s)|0;d=Or(e,m,b,y,h,a,d+65535&65535,n,D,u,s)|0;if(l<<16>>16==1&S){lr(m,b,a,o,n,_,s)|0;dn(e,t[_>>1]|0)}w=t[M>>1]|0;h=t[R>>1]|0;if(((w<<16>>16)*55706>>16|0)>=(h<<16>>16|0)){R=w;M=k;R=R<<16>>16;R=R*55706|0;R=R>>16;D=t[D>>1]|0;D=D<<16>>16;D=(R|0)<(D|0);D=D?d:M;c=O;return D|0}t[M>>1]=h;R=h;M=v;R=R<<16>>16;R=R*55706|0;R=R>>16;D=t[D>>1]|0;D=D<<16>>16;D=(R|0)<(D|0);D=D?d:M;c=O;return D|0}function Or(e,i,r,n,o,a,l,u,s,c,h){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;l=l|0;u=u|0;s=s|0;c=c|0;h=h|0;var w=0,d=0,v=0,b=0,k=0;if(l<<16>>16<u<<16>>16){u=-2147483648;v=l}else{v=l;w=-2147483648;d=i+(0-(l<<16>>16)<<2)|0;i=l;while(1){l=f[d>>2]|0;k=(l|0)<(w|0);i=k?i:v;w=k?w:l;v=v+-1<<16>>16;if(v<<16>>16<u<<16>>16){u=w;v=i;break}else d=d+4|0}}i=a<<16>>16>>>2&65535;if(!(i<<16>>16))i=0;else{w=i;l=r+(0-(v<<16>>16)<<1)|0;i=0;while(1){k=t[l>>1]|0;k=(I(k,k)|0)+i|0;i=t[l+2>>1]|0;i=k+(I(i,i)|0)|0;k=t[l+4>>1]|0;k=i+(I(k,k)|0)|0;i=t[l+6>>1]|0;i=k+(I(i,i)|0)|0;w=w+-1<<16>>16;if(!(w<<16>>16))break;else l=l+8|0}i=i<<1}if(c)vn(e,u,i,h);i=Cn(i,h)|0;l=o<<16>>16!=0;if(l)i=(i|0)>1073741823?2147483647:i<<1;o=u>>16;e=i>>16;h=I(e,o)|0;h=(h|0)==1073741824?2147483647:h<<1;i=(I((i>>>1)-(e<<15)<<16>>16,o)|0)>>15;k=(i<<1)+h|0;k=(i^h|0)>0&(k^h|0)<0?(h>>>31)+2147483647|0:k;o=(I(e,(u>>>1)-(o<<15)<<16>>16)|0)>>15;i=k+(o<<1)|0;i=(k^o|0)>0&(i^k|0)<0?(k>>>31)+2147483647|0:i;if(!l){t[s>>1]=i;return v|0}l=n<<16>>16;if(n<<16>>16>0)if(n<<16>>16<31){l=i>>l;b=16}else l=0;else{b=0-l<<16>>16;l=i<<b;l=(l>>b|0)==(i|0)?l:i>>31^2147483647;b=16}if((b|0)==16){if((l|0)>65535){t[s>>1]=32767;return v|0}if((l|0)<-65536){t[s>>1]=-32768;return v|0}}t[s>>1]=l>>>1;return v|0}function Nr(e){e=e|0;var i=0;if(!e){e=-1;return e|0}f[e>>2]=0;i=dt(6)|0;if(!i){e=-1;return e|0}t[i>>1]=40;t[i+2>>1]=0;t[i+4>>1]=0;f[e>>2]=i;e=0;return e|0}function Tr(e){e=e|0;if(!e){e=-1;return e|0}t[e>>1]=40;t[e+2>>1]=0;t[e+4>>1]=0;e=0;return e|0}function Ir(e){e=e|0;var i=0;if(!e)return;i=f[e>>2]|0;if(!i)return;vt(i);f[e>>2]=0;return}function Lr(e,i,r,n,o,a,l,u,s,h,w){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;l=l|0;u=u|0;s=s|0;h=h|0;w=w|0;var d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0;T=c;c=c+1200|0;g=T+1186|0;A=T+1184|0;N=T+1182|0;E=T;_=T+576|0;S=o<<16>>16;O=_+(S<<1)|0;d=(0-S&65535)<<16>>16<a<<16>>16;if(d){m=0-o<<16>>16<<16>>16;v=0;do{k=t[r+(m<<1)>>1]|0;k=I(k,k)|0;if((k|0)!=1073741824){b=(k<<1)+v|0;if((k^v|0)>0&(b^v|0)<0){f[w>>2]=1;v=(v>>>31)+2147483647|0}else v=b}else{f[w>>2]=1;v=2147483647}m=m+1|0}while((m&65535)<<16>>16!=a<<16>>16)}else v=0;if((2147483646-v&v|0)>=0)if((v|0)==2147483647){if(d){v=0-o<<16>>16<<16>>16;do{t[_+(v+S<<1)>>1]=lt(t[r+(v<<1)>>1]|0,3,w)|0;v=v+1|0}while((v&65535)<<16>>16!=a<<16>>16)}}else y=14;else{f[w>>2]=1;y=14}do{if((y|0)==14){if((1048575-v&v|0)<0){f[w>>2]=1;v=(v>>>31)+2147483647|0}else v=v+-1048576|0;if((v|0)>=0){if(!d)break;D=0-o<<16>>16<<16>>16;yt(_+(S+D<<1)|0,r+(D<<1)|0,(((a+o<<16>>16)+-1&65535)<<1)+2|0)|0;break}if(d){v=0-o<<16>>16<<16>>16;do{D=t[r+(v<<1)>>1]|0;t[_+(v+S<<1)>>1]=(D<<19>>19|0)==(D|0)?D<<3:D>>>15^32767;v=v+1|0}while((v&65535)<<16>>16!=a<<16>>16)}}}while(0);M=E+(S<<2)|0;Ai(O,a,o,n,M);m=t[e>>1]|0;D=e+4|0;R=u+(s<<16>>16<<1)|0;e:do{if(o<<16>>16<n<<16>>16)p=o;else{if((t[D>>1]|0)<=0){r=o;u=-2147483648;k=o;y=3402;while(1){kr(f[E+(S-(r<<16>>16)<<2)>>2]|0,g,A,w);b=t[A>>1]|0;v=t[y>>1]|0;m=I(v,t[g>>1]|0)|0;if((m|0)==1073741824){f[w>>2]=1;d=2147483647}else d=m<<1;p=(I(v,b<<16>>16)|0)>>15;m=d+(p<<1)|0;if((d^p|0)>0&(m^d|0)<0){f[w>>2]=1;m=(d>>>31)+2147483647|0}b=(m|0)<(u|0);k=b?k:r;r=r+-1<<16>>16;if(r<<16>>16<n<<16>>16){p=k;break e}else{u=b?u:m;y=y+-2|0}}}u=o;d=-2147483648;k=o;p=2902+(S+123-(m<<16>>16)<<1)|0;r=3402;while(1){kr(f[E+(S-(u<<16>>16)<<2)>>2]|0,g,A,w);y=t[A>>1]|0;b=t[r>>1]|0;m=I(b,t[g>>1]|0)|0;if((m|0)==1073741824){f[w>>2]=1;v=2147483647}else v=m<<1;y=(I(b,y<<16>>16)|0)>>15;m=v+(y<<1)|0;if((v^y|0)>0&(m^v|0)<0){f[w>>2]=1;m=(v>>>31)+2147483647|0}kr(m,g,A,w);y=t[A>>1]|0;b=t[p>>1]|0;m=I(b,t[g>>1]|0)|0;if((m|0)==1073741824){f[w>>2]=1;v=2147483647}else v=m<<1;y=(I(b,y<<16>>16)|0)>>15;m=v+(y<<1)|0;if((v^y|0)>0&(m^v|0)<0){f[w>>2]=1;m=(v>>>31)+2147483647|0}b=(m|0)<(d|0);k=b?k:u;u=u+-1<<16>>16;if(u<<16>>16<n<<16>>16){p=k;break}else{d=b?d:m;p=p+-2|0;r=r+-2|0}}}}while(0);if(a<<16>>16>0){u=0;r=O;y=_+(S-(p<<16>>16)<<1)|0;k=0;v=0;while(1){m=t[y>>1]|0;b=I(m,t[r>>1]|0)|0;if((b|0)!=1073741824){d=(b<<1)+k|0;if((b^k|0)>0&(d^k|0)<0){f[w>>2]=1;k=(k>>>31)+2147483647|0}else k=d}else{f[w>>2]=1;k=2147483647}d=I(m,m)|0;if((d|0)!=1073741824){b=(d<<1)+v|0;if((d^v|0)>0&(b^v|0)<0){f[w>>2]=1;v=(v>>>31)+2147483647|0}else v=b}else{f[w>>2]=1;v=2147483647}u=u+1<<16>>16;if(u<<16>>16>=a<<16>>16)break;else{r=r+2|0;y=y+2|0}}}else{k=0;v=0}b=(h|0)==0;if(!b){bn(i,0,w);vn(i,k,v,w)}d=(at(v,w)|0)<<16>>16;if((d*13107|0)==1073741824){f[w>>2]=1;v=2147483647}else v=d*26214|0;d=k-v|0;if(((d^k)&(v^k)|0)<0){f[w>>2]=1;d=(k>>>31)+2147483647|0}h=at(d,w)|0;t[R>>1]=h;if(h<<16>>16>0){d=l+6|0;t[l+8>>1]=t[d>>1]|0;h=l+4|0;t[d>>1]=t[h>>1]|0;d=l+2|0;t[h>>1]=t[d>>1]|0;t[d>>1]=t[l>>1]|0;t[l>>1]=p;t[e>>1]=Nn(l,5)|0;t[e+2>>1]=32767;d=32767}else{t[e>>1]=p;e=e+2|0;d=((t[e>>1]|0)*29491|0)>>>15&65535;t[e>>1]=d}t[D>>1]=((ct(d,9830,w)|0)&65535)>>>15^1;if(b){c=T;return p|0}if((ct(s,1,w)|0)<<16>>16){c=T;return p|0}lr(M,O,a,o,n,N,w)|0;dn(i,t[N>>1]|0);c=T;return p|0}function Fr(e,i,r,n,t,f,o,a,l,u){e=e|0;i=i|0;r=r|0;n=n|0;t=t|0;f=f|0;o=o|0;a=a|0;l=l|0;u=u|0;var s=0,h=0;u=c;c=c+48|0;h=u+22|0;s=u;i=e>>>0<6?i:r;r=f<<16>>16>0?22:0;e=t+(r<<1)|0;wt(e,i,h);wt(e,n,s);e=f<<16>>16;f=l+(e<<1)|0;ot(h,o+(e<<1)|0,f,40);ht(s,f,f,40,a,1);r=t+(((r<<16)+720896|0)>>>16<<1)|0;wt(r,i,h);wt(r,n,s);e=(e<<16)+2621440>>16;l=l+(e<<1)|0;ot(h,o+(e<<1)|0,l,40);ht(s,l,l,40,a,1);c=u;return}function Cr(e){e=e|0;var i=0;if(!e){e=-1;return e|0}f[e>>2]=0;i=dt(12)|0;if(!i){e=-1;return e|0}t[i>>1]=0;t[i+2>>1]=0;t[i+4>>1]=0;t[i+6>>1]=0;t[i+8>>1]=0;t[i+10>>1]=0;f[e>>2]=i;e=0;return e|0}function Pr(e){e=e|0;if(!e){e=-1;return e|0}t[e>>1]=0;t[e+2>>1]=0;t[e+4>>1]=0;t[e+6>>1]=0;t[e+8>>1]=0;t[e+10>>1]=0;e=0;return e|0}function Br(e){e=e|0;var i=0;if(!e)return;i=f[e>>2]|0;if(!i)return;vt(i);f[e>>2]=0;return}function Ur(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,f=0,o=0,a=0,l=0,u=0,s=0,c=0,h=0,w=0,d=0;h=e+10|0;f=t[h>>1]|0;w=e+8|0;n=t[w>>1]|0;if(!(r<<16>>16)){e=n;c=f;t[h>>1]=c;t[w>>1]=e;return}l=e+4|0;u=e+6|0;s=e+2|0;a=t[u>>1]|0;c=t[l>>1]|0;o=r;r=f;while(1){d=(I(t[e>>1]|0,-3733)|0)+(((c<<16>>16)*7807|0)+((a<<16>>16)*7807>>15))|0;t[e>>1]=c;d=d+((I(t[s>>1]|0,-3733)|0)>>15)|0;t[s>>1]=a;d=((r<<16>>16)*1899|0)+d+(I(n<<16>>16,-3798)|0)|0;r=t[i>>1]|0;d=d+((r<<16>>16)*1899|0)|0;t[i>>1]=(d+2048|0)>>>12;f=d>>>12;c=f&65535;t[l>>1]=c;a=(d<<3)-(f<<15)&65535;t[u>>1]=a;o=o+-1<<16>>16;if(!(o<<16>>16))break;else{d=n;i=i+2|0;n=r;r=d}}t[h>>1]=n;t[w>>1]=r;return}function xr(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var o=0,a=0,l=0,u=0;o=t[(f[n+88>>2]|0)+(e<<1)>>1]|0;if(!(o<<16>>16))return;u=r;l=f[(f[n+92>>2]|0)+(e<<2)>>2]|0;while(1){r=t[l>>1]|0;if(!(r<<16>>16))r=0;else{e=t[i>>1]|0;a=r;n=u+((r<<16>>16)+-1<<1)|0;while(1){r=e<<16>>16;t[n>>1]=r&1;a=a+-1<<16>>16;if(!(a<<16>>16))break;else{e=r>>>1&65535;n=n+-2|0}}r=t[l>>1]|0}i=i+2|0;o=o+-1<<16>>16;if(!(o<<16>>16))break;else{u=u+(r<<16>>16<<1)|0;l=l+2|0}}return}function zr(e,i,r,n,o,l){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;l=l|0;var u=0,s=0,h=0,w=0,d=0;d=c;c=c+16|0;h=d+2|0;w=d;u=o<<16>>16;if(o<<16>>16<1){l=-5443;w=-32768;Mn(e,w,l);c=d;return}s=Qn(14,r,l)|0;if((u|0)<(s<<16>>16|0))r=n;else{r=(n&65535)+1&65535;o=u>>>1&65535}n=Sn(o,s&65535)|0;t[w>>1]=n;Pn(n<<16>>16,h,w,l);t[h>>1]=((((r&65535)-(i&65535)<<16)+-65536|0)>>>16)+(a[h>>1]|0);n=ut(t[w>>1]|0,5,l)|0;u=t[h>>1]|0;n=((u&65535)<<10)+(n&65535)&65535;if(n<<16>>16>18284){l=3037;w=18284;Mn(e,w,l);c=d;return}o=t[w>>1]|0;u=u<<16>>16;if((u*24660|0)==1073741824){f[l>>2]=1;r=2147483647}else r=u*49320|0;w=(o<<16>>16)*24660>>15;u=r+(w<<1)|0;if((r^w|0)>0&(u^r|0)<0){f[l>>2]=1;u=(r>>>31)+2147483647|0}w=u<<13;l=at((w>>13|0)==(u|0)?w:u>>31^2147483647,l)|0;w=n;Mn(e,w,l);c=d;return}function jr(e,i,r,n,o,l,u,s,h,w,d,v,b,k,m,y,p,E,g,A){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;l=l|0;u=u|0;s=s|0;h=h|0;w=w|0;d=d|0;v=v|0;b=b|0;k=k|0;m=m|0;y=y|0;p=p|0;E=E|0;g=g|0;A=A|0;var S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,L=0,F=0,C=0,P=0,B=0,U=0,x=0,z=0,j=0,H=0,W=0,q=0,G=0,X=0,V=0,K=0,Y=0,Q=0,Z=0,$=0,J=0,ee=0,ie=0,re=0,ne=0,te=0,fe=0,oe=0,ae=0;ae=c;c=c+80|0;ne=ae+66|0;te=ae+64|0;fe=ae+62|0;oe=ae+60|0;j=ae+40|0;H=ae+20|0;x=ae;t[ne>>1]=i;t[te>>1]=h;t[fe>>1]=w;U=Qn(14,r,A)|0;re=U&65535;t[oe>>1]=re;z=Qn(14,w,A)|0;B=(a[n>>1]|0)+65523|0;t[x>>1]=B;N=(a[n+2>>1]|0)+65522|0;T=x+2|0;t[T>>1]=N;L=((i&65535)<<16)+-720896|0;M=L>>16;L=(L>>>15)+15+(a[n+4>>1]|0)|0;F=x+4|0;t[F>>1]=L;C=(a[n+6>>1]|0)+M|0;P=x+6|0;t[P>>1]=C;M=M+1+(a[n+8>>1]|0)|0;D=x+8|0;t[D>>1]=M;S=(a[d>>1]|0)+65523&65535;t[x+10>>1]=S;O=(a[d+2>>1]|0)+65522&65535;t[x+12>>1]=O;_=((h&65535)<<16)+-720896|0;n=_>>16;_=(_>>>15)+15+(a[d+4>>1]|0)&65535;t[x+14>>1]=_;R=(a[d+6>>1]|0)+n&65535;t[x+16>>1]=R;n=n+1+(a[d+8>>1]|0)&65535;t[x+18>>1]=n;ee=(l&65535)-(b&65535)<<16;h=ee>>16;if((ee|0)>0){w=u;r=k<<16>>16>>h&65535}else{w=u<<16>>16>>0-h&65535;r=k}if((ut(r,1,A)|0)<<16>>16>w<<16>>16)r=1;else r=(((w<<16>>16)+3>>2|0)>(r<<16>>16|0))<<31>>31;d=B+r&65535;t[x>>1]=d;ee=N+r&65535;t[T>>1]=ee;J=L+r&65535;t[F>>1]=J;$=C+r&65535;t[P>>1]=$;Z=M+r&65535;t[D>>1]=Z;h=n<<16>>16>d<<16>>16?n:d;h=R<<16>>16>h<<16>>16?R:h;h=_<<16>>16>h<<16>>16?_:h;h=O<<16>>16>h<<16>>16?O:h;h=S<<16>>16>h<<16>>16?S:h;h=Z<<16>>16>h<<16>>16?Z:h;h=$<<16>>16>h<<16>>16?$:h;h=J<<16>>16>h<<16>>16?J:h;h=(ee<<16>>16>h<<16>>16?ee:h)+1&65535;n=0;while(1){r=h-(d&65535)|0;d=r&65535;w=a[o>>1]<<16;r=r<<16>>16;if(d<<16>>16>0)d=d<<16>>16<31?w>>r:0;else{ee=0-r<<16>>16;d=w<<ee;d=(d>>ee|0)==(w|0)?d:w>>31^2147483647}ee=d>>16;t[j+(n<<1)>>1]=ee;t[H+(n<<1)>>1]=(d>>>1)-(ee<<15);n=n+1|0;if((n|0)==5){r=5;w=v;break}d=t[x+(n<<1)>>1]|0;o=o+2|0}while(1){n=h-(S&65535)|0;S=n&65535;d=a[w>>1]<<16;n=n<<16>>16;if(S<<16>>16>0)d=S<<16>>16<31?d>>n:0;else{J=0-n<<16>>16;ee=d<<J;d=(ee>>J|0)==(d|0)?ee:d>>31^2147483647}ee=d>>16;t[j+(r<<1)>>1]=ee;t[H+(r<<1)>>1]=(d>>>1)-(ee<<15);d=r+1|0;if((d&65535)<<16>>16==10)break;S=t[x+(d<<1)>>1]|0;r=d;w=w+2|0}W=U<<16>>16;q=t[j>>1]|0;G=t[H>>1]|0;X=t[j+2>>1]|0;V=t[H+2>>1]|0;K=t[j+4>>1]|0;Y=t[H+4>>1]|0;Q=t[j+6>>1]|0;Z=t[H+6>>1]|0;$=t[j+8>>1]|0;J=t[H+8>>1]|0;ee=m&65535;b=z<<16>>16;l=t[j+10>>1]|0;R=t[H+10>>1]|0;_=t[j+12>>1]|0;o=t[H+12>>1]|0;r=t[j+14>>1]|0;w=t[H+14>>1]|0;n=t[j+16>>1]|0;S=t[H+16>>1]|0;M=t[j+18>>1]|0;H=t[H+18>>1]|0;h=2147483647;j=0;d=0;D=782;do{x=t[D>>1]|0;C=(I(W,t[D+2>>1]|0)|0)>>>15<<16;v=C>>16;L=x<<1;B=(I(L,x)|0)>>16;k=I(B,q)|0;if((k|0)==1073741824){f[A>>2]=1;P=2147483647}else P=k<<1;z=(I(G,B)|0)>>15;k=P+(z<<1)|0;if((P^z|0)>0&(k^P|0)<0){f[A>>2]=1;k=(P>>>31)+2147483647|0}B=I(X,x)|0;if((B|0)==1073741824){f[A>>2]=1;P=2147483647}else P=B<<1;z=(I(V,x)|0)>>15;B=P+(z<<1)|0;if((P^z|0)>0&(B^P|0)<0){f[A>>2]=1;B=(P>>>31)+2147483647|0}C=(I(C>>15,v)|0)>>16;P=I(K,C)|0;if((P|0)==1073741824){f[A>>2]=1;F=2147483647}else F=P<<1;z=(I(Y,C)|0)>>15;P=F+(z<<1)|0;if((F^z|0)>0&(P^F|0)<0){f[A>>2]=1;P=(F>>>31)+2147483647|0}C=I(Q,v)|0;if((C|0)==1073741824){f[A>>2]=1;F=2147483647}else F=C<<1;z=(I(Z,v)|0)>>15;C=F+(z<<1)|0;if((F^z|0)>0&(C^F|0)<0){f[A>>2]=1;z=(F>>>31)+2147483647|0}else z=C;F=(I(L,v)|0)>>16;C=I($,F)|0;if((C|0)==1073741824){f[A>>2]=1;L=2147483647}else L=C<<1;U=(I(J,F)|0)>>15;C=L+(U<<1)|0;if((L^U|0)>0&(C^L|0)<0){f[A>>2]=1;C=(L>>>31)+2147483647|0}F=t[D+4>>1]|0;L=t[D+6>>1]|0;D=D+8|0;if((x-ee&65535)<<16>>16<1?(ie=F<<16>>16,F<<16>>16<=m<<16>>16):0){N=(I(L<<16>>16,b)|0)>>>15<<16;x=N>>16;O=ie<<1;L=(I(O,ie)|0)>>16;F=I(l,L)|0;if((F|0)==1073741824){f[A>>2]=1;T=2147483647}else T=F<<1;U=(I(R,L)|0)>>15;F=T+(U<<1)|0;if((T^U|0)>0&(F^T|0)<0){f[A>>2]=1;F=(T>>>31)+2147483647|0}L=I(_,ie)|0;if((L|0)==1073741824){f[A>>2]=1;T=2147483647}else T=L<<1;U=(I(o,ie)|0)>>15;L=T+(U<<1)|0;if((T^U|0)>0&(L^T|0)<0){f[A>>2]=1;U=(T>>>31)+2147483647|0}else U=L;T=(I(N>>15,x)|0)>>16;L=I(r,T)|0;if((L|0)==1073741824){f[A>>2]=1;N=2147483647}else N=L<<1;v=(I(w,T)|0)>>15;L=N+(v<<1)|0;if((N^v|0)>0&(L^N|0)<0){f[A>>2]=1;v=(N>>>31)+2147483647|0}else v=L;L=I(n,x)|0;if((L|0)==1073741824){f[A>>2]=1;T=2147483647}else T=L<<1;N=(I(S,x)|0)>>15;L=T+(N<<1)|0;if((T^N|0)>0&(L^T|0)<0){f[A>>2]=1;u=(T>>>31)+2147483647|0}else u=L;T=(I(O,x)|0)>>16;L=I(M,T)|0;if((L|0)==1073741824){f[A>>2]=1;N=2147483647}else N=L<<1;x=(I(H,T)|0)>>15;L=N+(x<<1)|0;if((N^x|0)>0&(L^N|0)<0){f[A>>2]=1;L=(N>>>31)+2147483647|0}x=B+k+P+z+C+F+U+v+u+L|0;z=(x|0)<(h|0);h=z?x:h;d=z?j:d}j=j+1<<16>>16}while(j<<16>>16<256);m=(d&65535)<<18>>16;Hr(e,782+(m<<1)|0,re,i,y,p,A);Rn(e,0,s,te,fe,ne,oe,A);s=(Qn(14,t[fe>>1]|0,A)|0)&65535;Hr(e,782+((m|2)<<1)|0,s,t[te>>1]|0,E,g,A);c=ae;return d|0}function Hr(e,i,r,n,o,l,u){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;l=l|0;u=u|0;var s=0,h=0,w=0,d=0;d=c;c=c+16|0;h=d+2|0;w=d;t[o>>1]=t[i>>1]|0;s=t[i+2>>1]|0;r=I(r<<16>>16<<1,s)|0;o=10-(n&65535)|0;i=o&65535;o=o<<16>>16;if(i<<16>>16>0)i=i<<16>>16<31?r>>o:0;else{o=0-o<<16>>16;i=r<<o;i=(i>>o|0)==(r|0)?i:r>>31^2147483647}t[l>>1]=i>>>16;Pn(s,h,w,u);t[h>>1]=(a[h>>1]|0)+65524;o=ut(t[w>>1]|0,5,u)|0;n=t[h>>1]|0;o=((n&65535)<<10)+(o&65535)&65535;r=t[w>>1]|0;n=n<<16>>16;if((n*24660|0)==1073741824){f[u>>2]=1;i=2147483647}else i=n*49320|0;w=(r<<16>>16)*24660>>15;n=i+(w<<1)|0;if(!((i^w|0)>0&(n^i|0)<0)){u=n;u=u<<13;u=u+32768|0;u=u>>>16;u=u&65535;Mn(e,o,u);c=d;return}f[u>>2]=1;u=(i>>>31)+2147483647|0;u=u<<13;u=u+32768|0;u=u>>>16;u=u&65535;Mn(e,o,u);c=d;return}function Wr(e,i,r,n,o,l,u,s,h,w,d,v,b,k,m,y,p,E,g,A,S){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;l=l|0;u=u|0;s=s|0;h=h|0;w=w|0;d=d|0;v=v|0;b=b|0;k=k|0;m=m|0;y=y|0;p=p|0;E=E|0;g=g|0;A=A|0;S=S|0;var _=0,R=0,M=0,D=0,O=0,N=0,T=0,L=0,F=0,C=0,P=0,B=0,U=0,x=0,z=0,j=0,H=0,W=0,q=0,G=0,X=0,V=0,K=0,Y=0,Q=0,Z=0,$=0,J=0,ee=0,ie=0,re=0,ne=0,te=0,fe=0,oe=0,ae=0,le=0,ue=0,se=0,ce=0;ce=c;c=c+80|0;le=ce+72|0;ue=ce+70|0;se=ce+68|0;oe=ce+66|0;ae=ce+56|0;$=ce+24|0;Z=ce+12|0;Y=ce+48|0;Q=ce+40|0;q=ce+34|0;X=ce+22|0;H=ce+6|0;W=ce;Gr(5,k,m,H,W,f[A+72>>2]|0,S)|0;M=Qn(14,w,S)|0;G=A+68|0;j=f[G>>2]|0;K=h<<16>>16;V=K+65526|0;k=(a[l>>1]|0)+65523&65535;t[ae>>1]=k;A=(a[l+2>>1]|0)+65522&65535;t[ae+2>>1]=A;ne=V<<16>>16;te=((V<<17>>17|0)==(ne|0)?V<<1:ne>>>15^32767)+15+(a[l+4>>1]|0)&65535;t[ae+4>>1]=te;fe=(a[l+6>>1]|0)+ne&65535;t[ae+6>>1]=fe;l=ne+1+(a[l+8>>1]|0)&65535;t[ae+8>>1]=l;A=A<<16>>16>k<<16>>16?A:k;A=te<<16>>16>A<<16>>16?te:A;A=fe<<16>>16>A<<16>>16?fe:A;A=(gn(l<<16>>16>A<<16>>16?l:A,1,S)|0)&65535;l=k;k=0;while(1){w=A-(l&65535)|0;l=w&65535;R=a[o+(k<<1)>>1]<<16;w=w<<16>>16;if(l<<16>>16>0)w=l<<16>>16<31?R>>w:0;else{fe=0-w<<16>>16;w=R<<fe;w=(w>>fe|0)==(R|0)?w:R>>31^2147483647}kr(w,$+(k<<1)|0,Z+(k<<1)|0,S);w=k+1|0;if((w|0)==5)break;l=t[ae+(w<<1)>>1]|0;k=w}x=$+2|0;z=Z+2|0;fe=M<<16>>16;J=$+4|0;ee=Z+4|0;ie=$+6|0;re=Z+6|0;ne=$+8|0;te=Z+8|0;N=0;l=2147483647;o=0;w=0;while(1){U=t[H+(o<<1)>>1]|0;M=I(U,U)|0;if(M>>>0>1073741823){f[S>>2]=1;M=32767}else M=M>>>15;A=t[Z>>1]|0;R=M<<16>>16;M=I(R,t[$>>1]|0)|0;if((M|0)==1073741824){f[S>>2]=1;k=2147483647}else k=M<<1;B=(I(A<<16>>16,R)|0)>>15;M=k+(B<<1)|0;if((k^B|0)>0&(M^k|0)<0){f[S>>2]=1;M=(k>>>31)+2147483647|0}A=t[z>>1]|0;R=I(t[x>>1]|0,U)|0;if((R|0)!=1073741824){k=(R<<1)+M|0;if((R^M|0)>0&(k^M|0)<0){f[S>>2]=1;k=(M>>>31)+2147483647|0}}else{f[S>>2]=1;k=2147483647}M=(I(A<<16>>16,U)|0)>>15;if((M|0)>32767){f[S>>2]=1;M=32767}B=M<<16;M=(B>>15)+k|0;if((B>>16^k|0)>0&(M^k|0)<0){f[S>>2]=1;B=(k>>>31)+2147483647|0}else B=M;C=(B>>>31)+2147483647|0;P=o&65535;M=N;L=0;F=j;do{R=(I(t[F>>1]|0,fe)|0)>>15;F=F+6|0;if((R|0)>32767){f[S>>2]=1;R=32767}T=R<<16>>16;R=I(T,T)|0;if((R|0)==1073741824){f[S>>2]=1;O=2147483647}else O=R<<1;kr(O,le,ue,S);R=I(T,U)|0;if((R|0)==1073741824){f[S>>2]=1;O=2147483647}else O=R<<1;kr(O,se,oe,S);k=t[ee>>1]|0;D=t[ue>>1]|0;R=t[J>>1]|0;A=t[le>>1]|0;N=I(A,R)|0;if((N|0)!=1073741824){O=(N<<1)+B|0;if((N^B|0)>0&(O^B|0)<0){f[S>>2]=1;O=C}}else{f[S>>2]=1;O=2147483647}N=(I(D<<16>>16,R)|0)>>15;if((N|0)>32767){f[S>>2]=1;N=32767}D=N<<16;N=(D>>15)+O|0;if((D>>16^O|0)>0&(N^O|0)<0){f[S>>2]=1;N=(O>>>31)+2147483647|0}O=(I(A,k<<16>>16)|0)>>15;if((O|0)>32767){f[S>>2]=1;O=32767}D=O<<16;O=(D>>15)+N|0;if((D>>16^N|0)>0&(O^N|0)<0){f[S>>2]=1;O=(N>>>31)+2147483647|0}R=t[re>>1]|0;N=I(t[ie>>1]|0,T)|0;if((N|0)!=1073741824){D=(N<<1)+O|0;if((N^O|0)>0&(D^O|0)<0){f[S>>2]=1;D=(O>>>31)+2147483647|0}}else{f[S>>2]=1;D=2147483647}R=(I(R<<16>>16,T)|0)>>15;if((R|0)>32767){f[S>>2]=1;R=32767}T=R<<16;R=(T>>15)+D|0;if((T>>16^D|0)>0&(R^D|0)<0){f[S>>2]=1;R=(D>>>31)+2147483647|0}A=t[te>>1]|0;D=t[oe>>1]|0;k=t[ne>>1]|0;_=t[se>>1]|0;N=I(_,k)|0;do{if((N|0)==1073741824){f[S>>2]=1;N=2147483647}else{O=(N<<1)+R|0;if(!((N^R|0)>0&(O^R|0)<0)){N=O;break}f[S>>2]=1;N=(R>>>31)+2147483647|0}}while(0);O=(I(D<<16>>16,k)|0)>>15;if((O|0)>32767){f[S>>2]=1;O=32767}T=O<<16;O=(T>>15)+N|0;if((T>>16^N|0)>0&(O^N|0)<0){f[S>>2]=1;O=(N>>>31)+2147483647|0}R=(I(_,A<<16>>16)|0)>>15;if((R|0)>32767){f[S>>2]=1;R=32767}T=R<<16;R=(T>>15)+O|0;if((T>>16^O|0)>0&(R^O|0)<0){f[S>>2]=1;R=(O>>>31)+2147483647|0}T=(R|0)<(l|0);M=T?L:M;w=T?P:w;l=T?R:l;L=L+1<<16>>16}while(L<<16>>16<32);o=o+1|0;if((o|0)==3){R=M;o=w;break}else N=M}z=(R<<16>>16)*3|0;l=t[j+(z<<1)>>1]|0;t[p>>1]=t[j+(z+1<<1)>>1]|0;t[E>>1]=t[j+(z+2<<1)>>1]|0;l=I(l<<16>>16,fe)|0;if((l|0)==1073741824){f[S>>2]=1;M=2147483647}else M=l<<1;z=9-K|0;j=z&65535;z=z<<16>>16;x=j<<16>>16>0;if(x)M=j<<16>>16<31?M>>z:0;else{B=0-z<<16>>16;U=M<<B;M=(U>>B|0)==(M|0)?U:M>>31^2147483647}t[y>>1]=M>>>16;U=o<<16>>16;H=t[H+(U<<1)>>1]|0;t[m>>1]=H;W=t[W+(U<<1)>>1]|0;Si(i,r,n,H,d,Y,Q,q,S);ir(e,t[q>>1]|0,t[y>>1]|0,X,S);if(!((t[Y>>1]|0)!=0&(t[X>>1]|0)>0)){S=R;p=f[g>>2]|0;y=p+2|0;t[p>>1]=W;p=p+4|0;f[g>>2]=p;t[y>>1]=S;c=ce;return}T=Y+6|0;t[T>>1]=s;O=Q+6|0;t[O>>1]=u;h=((ct(b,h,S)|0)&65535)+10|0;A=h<<16>>16;if((h&65535)<<16>>16<0){w=0-A<<16;if((w|0)<983040)v=v<<16>>16>>(w>>16)&65535;else v=0}else{w=v<<16>>16;k=w<<A;if((k<<16>>16>>A|0)==(w|0))v=k&65535;else v=(w>>>15^32767)&65535}l=t[m>>1]|0;M=t[X>>1]|0;G=f[G>>2]|0;k=t[y>>1]|0;X=10-K|0;A=X<<16>>16;if((X&65535)<<16>>16<0){w=0-A<<16;if((w|0)<983040)s=k<<16>>16>>(w>>16)&65535;else s=0}else{w=k<<16>>16;k=w<<A;if((k<<16>>16>>A|0)==(w|0))s=k&65535;else s=(w>>>15^32767)&65535}o=l<<16>>16;w=I(o,o)|0;if(w>>>0>1073741823){f[S>>2]=1;l=32767}else l=w>>>15;R=gn(32767-(M&65535)&65535,1,S)|0;M=M<<16>>16;w=I(t[Y+2>>1]|0,M)|0;if((w|0)==1073741824){f[S>>2]=1;w=2147483647}else w=w<<1;X=w<<1;w=I(((X>>1|0)==(w|0)?X:w>>31^2147418112)>>16,l<<16>>16)|0;if((w|0)==1073741824){f[S>>2]=1;N=2147483647}else N=w<<1;D=(a[Q+2>>1]|0)+65521|0;A=D&65535;w=I(t[Y+4>>1]|0,M)|0;if((w|0)==1073741824){f[S>>2]=1;l=2147483647}else l=w<<1;w=l<<1;w=(I(((w>>1|0)==(l|0)?w:l>>31^2147418112)>>16,o)|0)>>15;if((w|0)>32767){f[S>>2]=1;w=32767}t[J>>1]=w;l=V&65535;t[le>>1]=l;l=gn(t[Q+4>>1]|0,l,S)|0;w=I(t[T>>1]|0,M)|0;if((w|0)==1073741824){f[S>>2]=1;w=2147483647}else w=w<<1;_=w<<1;t[ie>>1]=((_>>1|0)==(w|0)?_:w>>31^2147418112)>>>16;_=((K<<17>>17|0)==(K|0)?K<<1:K>>>15^32767)+65529&65535;t[le>>1]=_;_=gn(t[O>>1]|0,_,S)|0;w=(I(t[T>>1]|0,R<<16>>16)|0)>>15;if((w|0)>32767){f[S>>2]=1;w=32767}t[ne>>1]=w;R=gn(_,1,S)|0;k=I(t[Y>>1]|0,M)|0;if((k|0)==1073741824){f[S>>2]=1;w=2147483647}else w=k<<1;O=st(w,le,S)|0;o=(a[le>>1]|0)+47|0;t[le>>1]=o;o=(a[Q>>1]|0)-(o&65535)|0;M=o+31&65535;M=A<<16>>16>M<<16>>16?A:M;M=l<<16>>16>M<<16>>16?l:M;M=_<<16>>16>M<<16>>16?_:M;M=(R<<16>>16>M<<16>>16?R:M)<<16>>16;k=M-(D&65535)|0;w=k&65535;k=k<<16>>16;if(w<<16>>16>0)B=w<<16>>16<31?N>>k:0;else{Q=0-k<<16>>16;B=N<<Q;B=(B>>Q|0)==(N|0)?B:N>>31^2147483647}A=M-(l&65535)|0;w=A&65535;k=a[J>>1]<<16;A=A<<16>>16;if(w<<16>>16>0)k=w<<16>>16<31?k>>A:0;else{Y=0-A<<16>>16;Q=k<<Y;k=(Q>>Y|0)==(k|0)?Q:k>>31^2147483647}kr(k,J,ee,S);_=M-(_&65535)|0;k=_&65535;A=a[ie>>1]<<16;_=_<<16>>16;if(k<<16>>16>0)k=k<<16>>16<31?A>>_:0;else{Q=0-_<<16>>16;k=A<<Q;k=(k>>Q|0)==(A|0)?k:A>>31^2147483647}kr(k,ie,re,S);_=M-(R&65535)|0;k=_&65535;A=a[ne>>1]<<16;_=_<<16>>16;if(k<<16>>16>0)k=k<<16>>16<31?A>>_:0;else{Q=0-_<<16>>16;k=A<<Q;k=(k>>Q|0)==(A|0)?k:A>>31^2147483647}kr(k,ne,te,S);_=M+65505|0;t[le>>1]=_;_=_-(o&65535)|0;k=lt(_&65535,1,S)|0;A=k<<16>>16;if(k<<16>>16>0)A=k<<16>>16<31?O>>A:0;else{Q=0-A<<16>>16;A=O<<Q;A=(A>>Q|0)==(O|0)?A:O>>31^2147483647}do{if(!(_&1))N=A;else{kr(A,$,Z,S);k=t[Z>>1]|0;A=t[$>>1]|0;if((A*23170|0)==1073741824){f[S>>2]=1;_=2147483647}else _=A*46340|0;$=(k<<16>>16)*23170>>15;A=_+($<<1)|0;if(!((_^$|0)>0&(A^_|0)<0)){N=A;break}f[S>>2]=1;N=(_>>>31)+2147483647|0}}while(0);T=(B>>>31)+2147483647|0;O=2147483647;D=0;A=0;L=G;while(1){k=(I(t[L>>1]|0,fe)|0)>>15;L=L+6|0;if((k|0)>32767){f[S>>2]=1;k=32767}_=k&65535;if(_<<16>>16>=s<<16>>16)break;l=k<<16>>16;k=I(l,l)|0;if((k|0)==1073741824){f[S>>2]=1;w=2147483647}else w=k<<1;kr(w,ue,se,S);k=(ct(_,v,S)|0)<<16>>16;k=I(k,k)|0;if((k|0)==1073741824){f[S>>2]=1;k=2147483647}else k=k<<1;kr(k,oe,ae,S);_=t[ee>>1]|0;w=I(t[J>>1]|0,l)|0;do{if((w|0)==1073741824){f[S>>2]=1;w=2147483647}else{k=(w<<1)+B|0;if(!((w^B|0)>0&(k^B|0)<0)){w=k;break}f[S>>2]=1;w=T}}while(0);k=(I(_<<16>>16,l)|0)>>15;if((k|0)>32767){f[S>>2]=1;k=32767}$=k<<16;k=($>>15)+w|0;if(($>>16^w|0)>0&(k^w|0)<0){f[S>>2]=1;k=(w>>>31)+2147483647|0}o=t[re>>1]|0;R=t[se>>1]|0;l=t[ie>>1]|0;M=t[ue>>1]|0;w=I(M,l)|0;do{if((w|0)==1073741824){f[S>>2]=1;_=2147483647}else{_=(w<<1)+k|0;if(!((w^k|0)>0&(_^k|0)<0))break;f[S>>2]=1;_=(k>>>31)+2147483647|0}}while(0);w=(I(R<<16>>16,l)|0)>>15;if((w|0)>32767){f[S>>2]=1;w=32767}$=w<<16;w=($>>15)+_|0;if(($>>16^_|0)>0&(w^_|0)<0){f[S>>2]=1;w=(_>>>31)+2147483647|0}k=(I(M,o<<16>>16)|0)>>15;if((k|0)>32767){f[S>>2]=1;k=32767}$=k<<16;k=($>>15)+w|0;if(($>>16^w|0)>0&(k^w|0)<0){f[S>>2]=1;k=(w>>>31)+2147483647|0}k=st(k,le,S)|0;_=lt(t[le>>1]|0,1,S)|0;w=_<<16>>16;if(_<<16>>16>0)_=_<<16>>16<31?k>>w:0;else{$=0-w<<16>>16;_=k<<$;_=(_>>$|0)==(k|0)?_:k>>31^2147483647}k=_-N|0;if(((k^_)&(_^N)|0)<0){f[S>>2]=1;k=(_>>>31)+2147483647|0}k=(at(k,S)|0)<<16>>16;k=I(k,k)|0;if((k|0)==1073741824){f[S>>2]=1;_=2147483647}else _=k<<1;M=t[te>>1]|0;l=t[ae>>1]|0;R=t[ne>>1]|0;o=t[oe>>1]|0;w=I(o,R)|0;do{if((w|0)==1073741824){f[S>>2]=1;k=2147483647}else{k=(w<<1)+_|0;if(!((w^_|0)>0&(k^_|0)<0))break;f[S>>2]=1;k=(_>>>31)+2147483647|0}}while(0);w=(I(l<<16>>16,R)|0)>>15;if((w|0)>32767){f[S>>2]=1;w=32767}$=w<<16;w=($>>15)+k|0;if(($>>16^k|0)>0&(w^k|0)<0){f[S>>2]=1;w=(k>>>31)+2147483647|0}k=(I(o,M<<16>>16)|0)>>15;if((k|0)>32767){f[S>>2]=1;k=32767}$=k<<16;k=($>>15)+w|0;if(($>>16^w|0)>0&(k^w|0)<0){f[S>>2]=1;k=(w>>>31)+2147483647|0}w=(k|0)<(O|0);A=w?D:A;D=D+1<<16>>16;if(D<<16>>16>=32)break;else O=w?k:O}se=(A<<16>>16)*3|0;_=t[G+(se<<1)>>1]|0;t[p>>1]=t[G+(se+1<<1)>>1]|0;t[E>>1]=t[G+(se+2<<1)>>1]|0;_=I(_<<16>>16,fe)|0;if((_|0)==1073741824){f[S>>2]=1;_=2147483647}else _=_<<1;if(x)_=j<<16>>16<31?_>>z:0;else{p=0-z<<16>>16;S=_<<p;_=(S>>p|0)==(_|0)?S:_>>31^2147483647}t[y>>1]=_>>>16;S=A;p=f[g>>2]|0;y=p+2|0;t[p>>1]=W;p=p+4|0;f[g>>2]=p;t[y>>1]=S;c=ce;return}function qr(e,i,r,n,f,o,a,l){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;o=o|0;a=a|0;l=l|0;var u=0,s=0,c=0,h=0,w=0;w=(e|0)==7;u=t[n>>1]|0;if(w){u=u<<16>>16>>>1&65535;h=Qn(i,r,l)|0;i=h<<16;e=i>>16;if((h<<20>>20|0)==(e|0))e=i>>12;else e=e>>>15^32767}else{h=Qn(i,r,l)|0;i=h<<16;e=i>>16;if((h<<21>>21|0)==(e|0))e=i>>11;else e=e>>>15^32767}h=e<<16>>16;l=u<<16>>16;i=l-((I(h,t[a>>1]|0)|0)>>>15&65535)|0;i=((i&32768|0)!=0?0-i|0:i)&65535;s=1;e=0;c=a;while(1){c=c+6|0;u=l-((I(t[c>>1]|0,h)|0)>>>15&65535)|0;r=u<<16;u=(r|0)<0?0-(r>>16)|0:u;r=(u<<16>>16|0)<(i<<16>>16|0);e=r?s:e;s=s+1<<16>>16;if(s<<16>>16>=32)break;else i=r?u&65535:i}c=(e<<16>>16)*196608>>16;t[n>>1]=(I(t[a+(c<<1)>>1]|0,h)|0)>>>15<<(w&1);t[f>>1]=t[a+(c+1<<1)>>1]|0;t[o>>1]=t[a+(c+2<<1)>>1]|0;return e|0}function Gr(e,i,r,n,f,o,a){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;o=o|0;a=a|0;var l=0,u=0,s=0,c=0,h=0,w=0;l=ct(t[r>>1]|0,t[o>>1]|0,a)|0;l=(l&65535)-((l&65535)>>>15&65535)|0;l=(l<<16>>31^l)&65535;s=0;c=1;while(1){u=t[o+(c<<1)>>1]|0;if(u<<16>>16>i<<16>>16)u=l;else{u=ct(t[r>>1]|0,u,a)|0;u=(u&65535)-((u&65535)>>>15&65535)|0;u=(u<<16>>31^u)&65535;w=u<<16>>16<l<<16>>16;u=w?u:l;s=w?c&65535:s}c=c+1|0;if((c|0)==16)break;else l=u}if((e|0)!=5){l=t[o+(s<<16>>16<<1)>>1]|0;if((e|0)==7){t[r>>1]=l&65532;return s|0}else{t[r>>1]=l;return s|0}}u=s<<16>>16;switch(s<<16>>16){case 0:{l=0;break}case 15:{h=8;break}default:if((t[o+(u+1<<1)>>1]|0)>i<<16>>16)h=8;else l=u+65535&65535}if((h|0)==8)l=u+65534&65535;t[f>>1]=l;w=l<<16>>16;t[n>>1]=t[o+(w<<1)>>1]|0;w=w+1|0;t[f+2>>1]=w;w=w<<16>>16;t[n+2>>1]=t[o+(w<<1)>>1]|0;w=w+1|0;t[f+4>>1]=w;t[n+4>>1]=t[o+(w<<16>>16<<1)>>1]|0;t[r>>1]=t[o+(u<<1)>>1]|0;return s|0}function Xr(e,i,r,n,o,l,u,s,h,w,d,v){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;l=l|0;u=u|0;s=s|0;h=h|0;w=w|0;d=d|0;v=v|0;var b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,L=0,F=0,C=0,P=0,B=0;B=c;c=c+32|0;m=B+20|0;y=B+10|0;k=B;switch(e|0){case 3:case 4:case 6:{d=d+84|0;P=128;break}default:{d=d+80|0;P=64}}C=f[d>>2]|0;b=Qn(14,r,v)|0;F=i<<16>>16;L=F+65525|0;e=(a[o>>1]|0)+65523&65535;t[k>>1]=e;i=(a[o+2>>1]|0)+65522&65535;t[k+2>>1]=i;T=L<<16>>16;T=gn(t[o+4>>1]|0,((L<<17>>17|0)==(T|0)?L<<1:T>>>15^32767)+15&65535,v)|0;t[k+4>>1]=T;L=gn(t[o+6>>1]|0,L&65535,v)|0;t[k+6>>1]=L;o=gn(t[o+8>>1]|0,F+65526&65535,v)|0;t[k+8>>1]=o;i=i<<16>>16>e<<16>>16?i:e;i=T<<16>>16>i<<16>>16?T:i;i=L<<16>>16>i<<16>>16?L:i;i=(o<<16>>16>i<<16>>16?o:i)+1&65535;o=0;while(1){r=i-(e&65535)|0;d=r&65535;e=a[n+(o<<1)>>1]<<16;r=r<<16>>16;if(d<<16>>16>0)d=d<<16>>16<31?e>>r:0;else{L=0-r<<16>>16;d=e<<L;d=(d>>L|0)==(e|0)?d:e>>31^2147483647}kr(d,m+(o<<1)|0,y+(o<<1)|0,v);d=o+1|0;if((d|0)==5)break;e=t[k+(d<<1)>>1]|0;o=d}L=b<<16>>16;S=t[m>>1]|0;_=t[y>>1]|0;R=t[m+2>>1]|0;M=t[y+2>>1]|0;D=t[m+4>>1]|0;O=t[y+4>>1]|0;N=t[m+6>>1]|0;T=t[y+6>>1]|0;A=t[m+8>>1]|0;p=t[y+8>>1]|0;i=2147483647;E=0;d=0;g=C;while(1){o=t[g>>1]|0;if(o<<16>>16>l<<16>>16)b=i;else{b=(I(t[g+2>>1]|0,L)|0)>>15;if((b|0)>32767){f[v>>2]=1;b=32767}y=o<<16>>16;o=I(y,y)|0;if(o>>>0>1073741823){f[v>>2]=1;k=32767}else k=o>>>15;r=b<<16>>16;b=I(r,r)|0;if(b>>>0>1073741823){f[v>>2]=1;m=32767}else m=b>>>15;n=(I(r,y)|0)>>15;if((n|0)>32767){f[v>>2]=1;n=32767}b=k<<16>>16;k=I(S,b)|0;if((k|0)==1073741824){f[v>>2]=1;o=2147483647}else o=k<<1;b=(I(_,b)|0)>>15;k=o+(b<<1)|0;if((o^b|0)>0&(k^o|0)<0){f[v>>2]=1;k=(o>>>31)+2147483647|0}b=I(R,y)|0;if((b|0)==1073741824){f[v>>2]=1;o=2147483647}else o=b<<1;y=(I(M,y)|0)>>15;b=o+(y<<1)|0;if((o^y|0)>0&(b^o|0)<0){f[v>>2]=1;b=(o>>>31)+2147483647|0}o=b+k|0;if((b^k|0)>-1&(o^k|0)<0){f[v>>2]=1;o=(k>>>31)+2147483647|0}b=m<<16>>16;k=I(D,b)|0;if((k|0)==1073741824){f[v>>2]=1;e=2147483647}else e=k<<1;y=(I(O,b)|0)>>15;k=e+(y<<1)|0;if((e^y|0)>0&(k^e|0)<0){f[v>>2]=1;k=(e>>>31)+2147483647|0}b=k+o|0;if((k^o|0)>-1&(b^o|0)<0){f[v>>2]=1;e=(o>>>31)+2147483647|0}else e=b;b=I(N,r)|0;if((b|0)==1073741824){f[v>>2]=1;k=2147483647}else k=b<<1;y=(I(T,r)|0)>>15;b=k+(y<<1)|0;if((k^y|0)>0&(b^k|0)<0){f[v>>2]=1;b=(k>>>31)+2147483647|0}o=b+e|0;if((b^e|0)>-1&(o^e|0)<0){f[v>>2]=1;k=(e>>>31)+2147483647|0}else k=o;o=n<<16>>16;b=I(A,o)|0;if((b|0)==1073741824){f[v>>2]=1;e=2147483647}else e=b<<1;y=(I(p,o)|0)>>15;b=e+(y<<1)|0;if((e^y|0)>0&(b^e|0)<0){f[v>>2]=1;o=(e>>>31)+2147483647|0}else o=b;b=o+k|0;if((o^k|0)>-1&(b^k|0)<0){f[v>>2]=1;b=(k>>>31)+2147483647|0}y=(b|0)<(i|0);b=y?b:i;d=y?E:d}g=g+8|0;E=E+1<<16>>16;if((E<<16>>16|0)>=(P|0))break;else i=b}l=d<<16>>16;l=((l<<18>>18|0)==(l|0)?l<<2:l>>>15^32767)<<16>>16;t[u>>1]=t[C+(l<<1)>>1]|0;i=t[C+(l+1<<1)>>1]|0;t[h>>1]=t[C+(l+2<<1)>>1]|0;t[w>>1]=t[C+(l+3<<1)>>1]|0;i=I(i<<16>>16,L)|0;if((i|0)==1073741824){f[v>>2]=1;e=2147483647}else e=i<<1;r=10-F|0;i=r&65535;r=r<<16>>16;if(i<<16>>16>0){v=i<<16>>16<31?e>>r:0;v=v>>>16;v=v&65535;t[s>>1]=v;c=B;return d|0}else{h=0-r<<16>>16;v=e<<h;v=(v>>h|0)==(e|0)?v:e>>31^2147483647;v=v>>>16;v=v&65535;t[s>>1]=v;c=B;return d|0}return 0}function Vr(e,i,r,n,f,o,l,u,s){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;o=o|0;l=l|0;u=u|0;s=s|0;var h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,L=0,F=0,C=0,P=0,B=0,U=0,x=0,z=0,j=0,H=0,W=0,q=0,G=0,X=0,V=0,K=0,Y=0,Q=0,Z=0,$=0,J=0,ee=0,ie=0,re=0,ne=0,te=0,fe=0,oe=0,ae=0,le=0,ue=0,se=0,ce=0,he=0,we=0,de=0,ve=0,be=0,ke=0,me=0,ye=0,pe=0,Ee=0,ge=0;ge=c;c=c+160|0;Ee=ge;w=e<<16>>16;ye=e<<16>>16==10;pe=t[l+(t[o>>1]<<1)>>1]|0;if(e<<16>>16>0){s=0;h=u;while(1){t[h>>1]=s;s=s+1<<16>>16;if(s<<16>>16>=e<<16>>16)break;else h=h+2|0}}if(r<<16>>16<=1){c=ge;return}ke=o+2|0;me=pe<<16>>16;de=n+(me<<1)|0;ve=f+(me*80|0)+(me<<1)|0;be=o+6|0;Q=i&65535;Z=o+4|0;$=o+10|0;J=o+8|0;ee=o+14|0;ie=o+12|0;re=o+18|0;ne=o+16|0;te=u+2|0;fe=u+4|0;oe=u+6|0;ae=u+8|0;le=u+10|0;ue=u+12|0;se=u+14|0;ce=u+16|0;he=u+18|0;we=e<<16>>16>2;K=o+(w+-1<<1)|0;X=1;Y=1;z=0;j=0;V=-1;while(1){G=t[l+(t[ke>>1]<<1)>>1]|0;q=G<<16>>16;i=(a[n+(q<<1)>>1]|0)+(a[de>>1]|0)|0;h=(t[f+(me*80|0)+(q<<1)>>1]<<13)+32768+((t[f+(q*80|0)+(q<<1)>>1]|0)+(t[ve>>1]|0)<<12)|0;w=t[be>>1]|0;if(w<<16>>16<40){w=w<<16>>16;d=Ee;while(1){H=(t[f+(w*80|0)+(w<<1)>>1]|0)>>>1;x=t[f+(w*80|0)+(me<<1)>>1]|0;W=t[f+(w*80|0)+(q<<1)>>1]|0;t[d>>1]=i+(a[n+(w<<1)>>1]|0);t[d+2>>1]=(x+2+H+W|0)>>>2;w=w+Q|0;if((w&65535)<<16>>16<40){w=w<<16>>16;d=d+4|0}else break}M=t[be>>1]|0}else M=w;i=t[Z>>1]|0;R=h>>12;w=i<<16>>16;e:do{if(i<<16>>16<40){_=M<<16>>16;if(M<<16>>16<40){d=1;b=i;m=M;k=0;v=-1}else while(1){w=w+Q|0;if((w&65535)<<16>>16<40)w=w<<16>>16;else{d=1;W=i;H=M;w=0;break e}}while(1){S=((t[f+(w*80|0)+(w<<1)>>1]|0)+R>>1)+(t[f+(w*80|0)+(me<<1)>>1]|0)+(t[f+(w*80|0)+(q<<1)>>1]|0)|0;A=a[n+(w<<1)>>1]|0;E=_;g=M;p=Ee;y=k;while(1){h=(a[p>>1]|0)+A|0;s=h<<16>>16;s=(I(s,s)|0)>>>15;k=(S+(t[f+(w*80|0)+(E<<1)>>1]|0)>>2)+(t[p+2>>1]|0)>>1;if((I(s<<16>>16,d<<16>>16)|0)>(I(k,v<<16>>16)|0)){d=k&65535;b=i;m=g;k=h&65535;v=s&65535}else k=y;h=E+Q|0;g=h&65535;if(g<<16>>16>=40)break;else{E=h<<16>>16;p=p+4|0;y=k}}w=w+Q|0;i=w&65535;if(i<<16>>16<40)w=w<<16>>16;else{W=b;H=m;w=k;break}}}else{d=1;W=i;H=M;w=0}}while(0);b=d<<16>>16<<15;d=t[$>>1]|0;if(d<<16>>16<40){h=W<<16>>16;s=H<<16>>16;i=w&65535;d=d<<16>>16;w=Ee;while(1){P=t[f+(d*80|0)+(d<<1)>>1]>>1;C=t[f+(d*80|0)+(me<<1)>>1]|0;B=t[f+(d*80|0)+(q<<1)>>1]|0;U=t[f+(d*80|0)+(h<<1)>>1]|0;x=t[f+(d*80|0)+(s<<1)>>1]|0;t[w>>1]=(a[n+(d<<1)>>1]|0)+i;t[w+2>>1]=(C+2+P+B+U+x|0)>>>2;d=d+Q|0;if((d&65535)<<16>>16<40){d=d<<16>>16;w=w+4|0}else break}P=t[$>>1]|0}else P=d;v=t[J>>1]|0;d=v<<16>>16;e:do{if(v<<16>>16<40){D=W<<16>>16;O=H<<16>>16;N=P<<16>>16;M=b+32768|0;if(P<<16>>16<40){k=1;b=v;i=P;m=v;w=0;v=-1}else while(1){d=d+Q|0;if((d&65535)<<16>>16<40)d=d<<16>>16;else{d=1;x=v;U=P;w=0;break e}}while(1){s=a[n+(d<<1)>>1]|0;R=(t[f+(d*80|0)+(q<<1)>>1]|0)+(t[f+(d*80|0)+(me<<1)>>1]|0)+(t[f+(d*80|0)+(D<<1)>>1]|0)+(t[f+(d*80|0)+(O<<1)>>1]|0)|0;_=M+(t[f+(d*80|0)+(d<<1)>>1]<<11)|0;A=N;E=P;S=Ee;while(1){y=(a[S>>1]|0)+s|0;h=_+(t[S+2>>1]<<14)+(R+(t[f+(d*80|0)+(A<<1)>>1]|0)<<12)|0;p=y<<16>>16;p=(I(p,p)|0)>>>15;if((I(p<<16>>16,k<<16>>16)|0)>(I(h>>16,v<<16>>16)|0)){k=h>>>16&65535;g=m;i=E;w=y&65535;v=p&65535}else g=b;b=A+Q|0;E=b&65535;if(E<<16>>16>=40){b=g;break}else{A=b<<16>>16;b=g;S=S+4|0}}d=d+Q|0;m=d&65535;if(m<<16>>16<40)d=d<<16>>16;else{d=k;x=b;U=i;break}}}else{d=1;x=v;U=P;w=0}}while(0);k=d<<16>>16<<15;d=t[ee>>1]|0;if(d<<16>>16<40){h=W<<16>>16;s=H<<16>>16;v=x<<16>>16;b=U<<16>>16;i=w&65535;d=d<<16>>16;w=Ee;while(1){T=t[f+(d*80|0)+(d<<1)>>1]>>1;N=t[f+(me*80|0)+(d<<1)>>1]|0;L=t[f+(q*80|0)+(d<<1)>>1]|0;F=t[f+(h*80|0)+(d<<1)>>1]|0;C=t[f+(s*80|0)+(d<<1)>>1]|0;P=t[f+(v*80|0)+(d<<1)>>1]|0;B=t[f+(b*80|0)+(d<<1)>>1]|0;t[w>>1]=(a[n+(d<<1)>>1]|0)+i;t[w+2>>1]=(N+4+T+L+F+C+P+B|0)>>>3;d=d+Q|0;if((d&65535)<<16>>16<40){d=d<<16>>16;w=w+4|0}else break}i=t[ee>>1]|0}else i=d;m=t[ie>>1]|0;if(m<<16>>16<40){P=W<<16>>16;T=H<<16>>16;N=x<<16>>16;O=U<<16>>16;D=i<<16>>16;M=i<<16>>16<40;L=k+32768|0;C=m<<16>>16;s=1;g=m;E=i;F=m;b=0;d=-1;while(1){if(M){k=a[n+(C<<1)>>1]|0;w=(t[f+(C*80|0)+(q<<1)>>1]|0)+(t[f+(C*80|0)+(me<<1)>>1]|0)+(t[f+(C*80|0)+(P<<1)>>1]|0)+(t[f+(C*80|0)+(T<<1)>>1]|0)+(t[f+(C*80|0)+(N<<1)>>1]|0)+(t[f+(C*80|0)+(O<<1)>>1]|0)|0;v=L+(t[f+(C*80|0)+(C<<1)>>1]<<10)|0;p=D;m=i;_=E;R=Ee;while(1){S=(a[R>>1]|0)+k|0;E=v+(t[R+2>>1]<<14)+(w+(t[f+(C*80|0)+(p<<1)>>1]|0)<<11)|0;A=S<<16>>16;A=(I(A,A)|0)>>>15;if((I(A<<16>>16,s<<16>>16)|0)>(I(E>>16,d<<16>>16)|0)){s=E>>>16&65535;g=F;E=m;b=S&65535;d=A&65535}else E=_;y=p+Q|0;m=y&65535;if(m<<16>>16>=40)break;else{p=y<<16>>16;_=E;R=R+4|0}}}m=C+Q|0;F=m&65535;if(F<<16>>16>=40){B=E;break}else C=m<<16>>16}}else{s=1;g=m;B=i;b=0;d=-1}if(ye){p=s<<16>>16<<15;d=t[re>>1]|0;if(d<<16>>16<40){w=W<<16>>16;i=H<<16>>16;h=x<<16>>16;s=U<<16>>16;k=g<<16>>16;m=B<<16>>16;v=b&65535;d=d<<16>>16;b=Ee;while(1){N=t[f+(d*80|0)+(d<<1)>>1]>>1;O=t[f+(me*80|0)+(d<<1)>>1]|0;T=t[f+(q*80|0)+(d<<1)>>1]|0;L=t[f+(w*80|0)+(d<<1)>>1]|0;F=t[f+(i*80|0)+(d<<1)>>1]|0;C=t[f+(h*80|0)+(d<<1)>>1]|0;P=t[f+(s*80|0)+(d<<1)>>1]|0;z=t[f+(k*80|0)+(d<<1)>>1]|0;j=t[f+(m*80|0)+(d<<1)>>1]|0;t[b>>1]=(a[n+(d<<1)>>1]|0)+v;t[b+2>>1]=(O+4+N+T+L+F+C+P+z+j|0)>>>3;d=d+Q|0;if((d&65535)<<16>>16<40){d=d<<16>>16;b=b+4|0}else break}P=t[re>>1]|0}else P=d;k=t[ne>>1]|0;if(k<<16>>16<40){N=W<<16>>16;O=H<<16>>16;D=x<<16>>16;h=U<<16>>16;T=g<<16>>16;L=B<<16>>16;F=P<<16>>16;C=P<<16>>16<40;M=p+32768|0;w=k<<16>>16;s=1;m=k;b=P;i=k;d=-1;while(1){if(C){p=a[n+(w<<1)>>1]|0;v=(t[f+(q*80|0)+(w<<1)>>1]|0)+(t[f+(me*80|0)+(w<<1)>>1]|0)+(t[f+(N*80|0)+(w<<1)>>1]|0)+(t[f+(O*80|0)+(w<<1)>>1]|0)+(t[f+(D*80|0)+(w<<1)>>1]|0)+(t[f+(h*80|0)+(w<<1)>>1]|0)+(t[f+(T*80|0)+(w<<1)>>1]|0)+(t[f+(L*80|0)+(w<<1)>>1]|0)|0;k=M+(t[f+(w*80|0)+(w<<1)>>1]<<9)|0;R=F;A=P;_=Ee;while(1){S=(a[_>>1]|0)+p<<16>>16;S=(I(S,S)|0)>>>15;E=k+(t[_+2>>1]<<13)+(v+(t[f+(w*80|0)+(R<<1)>>1]|0)<<10)|0;if((I(S<<16>>16,s<<16>>16)|0)>(I(E>>16,d<<16>>16)|0)){s=E>>>16&65535;m=i;b=A;d=S&65535}y=R+Q|0;A=y&65535;if(A<<16>>16>=40)break;else{R=y<<16>>16;_=_+4|0}}}k=w+Q|0;i=k&65535;if(i<<16>>16>=40)break;else w=k<<16>>16}}else{s=1;m=k;b=P;d=-1}}else{m=z;b=j}if((I(d<<16>>16,X<<16>>16)|0)>(I(s<<16>>16,V<<16>>16)|0)){t[u>>1]=pe;t[te>>1]=G;t[fe>>1]=W;t[oe>>1]=H;t[ae>>1]=x;t[le>>1]=U;t[ue>>1]=g;t[se>>1]=B;if(ye){t[ce>>1]=m;t[he>>1]=b}}else{s=X;d=V}w=t[ke>>1]|0;if(we){i=1;h=2;while(1){t[o+(i<<1)>>1]=t[o+(h<<1)>>1]|0;h=h+1|0;if((h&65535)<<16>>16==e<<16>>16)break;else i=i+1|0}}t[K>>1]=w;Y=Y+1<<16>>16;if(Y<<16>>16>=r<<16>>16)break;else{X=s;z=m;j=b;V=d}}c=ge;return}function Kr(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var f=0,o=0,a=0,l=0,u=0,s=0;l=39;while(1){a=e+(l<<1)|0;o=t[a>>1]|0;f=i+(l<<1)|0;if(o<<16>>16>-1)t[f>>1]=32767;else{t[f>>1]=-32767;if(o<<16>>16==-32768)o=32767;else o=0-(o&65535)&65535;t[a>>1]=o}t[r+(l<<1)>>1]=o;if((l|0)>0)l=l+-1|0;else break}s=8-(n<<16>>16)|0;if((s|0)>0){u=0;f=0}else return;do{n=0;e=0;a=32767;while(1){i=t[r+(n<<1)>>1]|0;l=i<<16>>16>-1?i<<16>>16<a<<16>>16:0;f=l?e:f;o=n+5|0;e=o&65535;if(e<<16>>16>=40)break;else{n=o<<16>>16;a=l?i:a}}t[r+(f<<16>>16<<1)>>1]=-1;u=u+1<<16>>16}while((u<<16>>16|0)<(s|0));u=0;do{i=1;e=1;o=32767;while(1){n=t[r+(i<<1)>>1]|0;l=n<<16>>16>-1?n<<16>>16<o<<16>>16:0;f=l?e:f;a=i+5|0;e=a&65535;if(e<<16>>16>=40)break;else{i=a<<16>>16;o=l?n:o}}t[r+(f<<16>>16<<1)>>1]=-1;u=u+1<<16>>16}while((u<<16>>16|0)<(s|0));u=0;do{i=2;e=2;o=32767;while(1){n=t[r+(i<<1)>>1]|0;l=n<<16>>16>-1?n<<16>>16<o<<16>>16:0;f=l?e:f;a=i+5|0;e=a&65535;if(e<<16>>16>=40)break;else{i=a<<16>>16;o=l?n:o}}t[r+(f<<16>>16<<1)>>1]=-1;u=u+1<<16>>16}while((u<<16>>16|0)<(s|0));u=0;while(1){i=3;e=3;o=32767;while(1){n=t[r+(i<<1)>>1]|0;l=n<<16>>16>-1?n<<16>>16<o<<16>>16:0;f=l?e:f;a=i+5|0;e=a&65535;if(e<<16>>16>=40){o=f;break}else{i=a<<16>>16;o=l?n:o}}t[r+(o<<16>>16<<1)>>1]=-1;u=u+1<<16>>16;if((u<<16>>16|0)>=(s|0)){f=0;break}else f=o}do{i=4;e=4;u=32767;while(1){n=t[r+(i<<1)>>1]|0;l=n<<16>>16>-1?n<<16>>16<u<<16>>16:0;o=l?e:o;a=i+5|0;e=a&65535;if(e<<16>>16>=40)break;else{i=a<<16>>16;u=l?n:u}}t[r+(o<<16>>16<<1)>>1]=-1;f=f+1<<16>>16}while((f<<16>>16|0)<(s|0));return}function Yr(e,i,r,n,o,a,l,u){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;l=l|0;u=u|0;var s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0;S=c;c=c+80|0;A=S;v=40;b=i;k=e;h=256;w=256;while(1){s=t[b>>1]|0;b=b+2|0;s=I(s,s)|0;if((s|0)!=1073741824){d=(s<<1)+h|0;if((s^h|0)>0&(d^h|0)<0){f[u>>2]=1;h=(h>>>31)+2147483647|0}else h=d}else{f[u>>2]=1;h=2147483647}g=t[k>>1]|0;w=(I(g<<1,g)|0)+w|0;v=v+-1<<16>>16;if(!(v<<16>>16))break;else k=k+2|0}g=Cn(h,u)|0;p=g<<5;g=((p>>5|0)==(g|0)?p:g>>31^2147418112)>>16;p=(Cn(w,u)|0)<<5>>16;E=39;m=i+78|0;y=A+78|0;s=r+78|0;while(1){k=I(t[m>>1]|0,g)|0;m=m+-2|0;b=k<<1;i=e+(E<<1)|0;h=t[i>>1]|0;v=I(h<<16>>16,p)|0;if((v|0)!=1073741824){d=(v<<1)+b|0;if((v^b|0)>0&(d^b|0)<0){f[u>>2]=1;d=(k>>>30&1)+2147483647|0}}else{f[u>>2]=1;d=2147483647}w=d<<10;w=at((w>>10|0)==(d|0)?w:d>>31^2147483647,u)|0;if(w<<16>>16>-1)t[s>>1]=32767;else{t[s>>1]=-32767;if(w<<16>>16==-32768)w=32767;else w=0-(w&65535)&65535;if(h<<16>>16==-32768)d=32767;else d=0-(h&65535)&65535;t[i>>1]=d}s=s+-2|0;t[y>>1]=w;if((E|0)<=0)break;else{E=E+-1|0;y=y+-2|0}}i=o<<16>>16;if(o<<16>>16<=0){t[a+(i<<1)>>1]=t[a>>1]|0;c=S;return}k=l&65535;b=0;v=-1;s=0;while(1){if((b|0)<40){w=b;d=b&65535;h=-1;while(1){u=t[A+(w<<1)>>1]|0;l=u<<16>>16>h<<16>>16;h=l?u:h;s=l?d:s;w=w+k|0;d=w&65535;if(d<<16>>16>=40)break;else w=w<<16>>16}}else h=-1;t[n+(b<<1)>>1]=s;if(h<<16>>16>v<<16>>16)t[a>>1]=b;else h=v;b=b+1|0;if((b&65535)<<16>>16==o<<16>>16)break;else v=h}s=t[a>>1]|0;t[a+(i<<1)>>1]=s;if(o<<16>>16>1)h=1;else{c=S;return}do{n=s+1<<16>>16;s=n<<16>>16>=o<<16>>16?0:n;t[a+(h<<1)>>1]=s;t[a+(h+i<<1)>>1]=s;h=h+1|0}while((h&65535)<<16>>16!=o<<16>>16);c=S;return}function Qr(e){e=e|0;var i=0;if(!e){e=-1;return e|0}f[e>>2]=0;i=dt(12)|0;if(!i){e=-1;return e|0}t[i>>1]=8;f[e>>2]=i;t[i+2>>1]=3;t[i+4>>1]=0;f[i+8>>2]=0;e=0;return e|0}function Zr(e){e=e|0;var i=0;if(!e)return;i=f[e>>2]|0;if(!i)return;vt(i);f[e>>2]=0;return}function $r(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,o=0,a=0;do{if((i|0)==8){n=e+2|0;o=(t[n>>1]|0)+-1<<16>>16;t[n>>1]=o;i=e+8|0;if(!(f[i>>2]|0)){f[r>>2]=1;t[n>>1]=3;break}a=e+4|0;if(o<<16>>16>2&(t[a>>1]|0)>0){f[r>>2]=2;t[a>>1]=(t[a>>1]|0)+-1<<16>>16;break}if(!(o<<16>>16)){f[r>>2]=2;t[n>>1]=t[e>>1]|0;break}else{f[r>>2]=3;break}}else{t[e+2>>1]=t[e>>1]|0;f[r>>2]=0;i=e+8|0}}while(0);f[i>>2]=f[r>>2];return}function Jr(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,t=0,o=0;if(!e){e=-1;return e|0}f[e>>2]=0;r=dt(12)|0;n=r;if(!r){e=-1;return e|0}f[r>>2]=0;t=r+4|0;f[t>>2]=0;o=r+8|0;f[o>>2]=i;if((Cr(r)|0)<<16>>16==0?(Ii(t,f[o>>2]|0)|0)<<16>>16==0:0){Pr(f[r>>2]|0)|0;Fi(f[t>>2]|0)|0;f[e>>2]=n;e=0;return e|0}Br(r);Li(t);vt(r);e=-1;return e|0}function en(e){e=e|0;var i=0;if(!e)return;i=f[e>>2]|0;if(!i)return;Br(i);Li((f[e>>2]|0)+4|0);vt(f[e>>2]|0);f[e>>2]=0;return}function rn(e,i,r,n,o){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;var l=0,u=0,s=0,h=0,w=0;h=c;c=c+448|0;u=h+320|0;s=h;Et(n|0,0,488)|0;l=0;do{w=r+(l<<1)|0;t[w>>1]=(a[w>>1]|0)&65528;l=l+1|0}while((l|0)!=160);Ur(f[e>>2]|0,r,160);w=e+4|0;Ci(f[w>>2]|0,i,r,u,o,s)|0;xr(f[o>>2]|0,u,n,(f[w>>2]|0)+2392|0);c=h;return}function nn(e,i,r,n,f,o,a,l,u,s,h,w,d,v,b,k){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;o=o|0;a=a|0;l=l|0;u=u|0;s=s|0;h=h|0;w=w|0;d=d|0;v=v|0;b=b|0;k=k|0;var m=0,y=0,p=0;p=c;c=c+48|0;m=p+22|0;y=p;wt(f,(e&-2|0)==6?r:i,m);wt(f,n,y);r=h;i=m;f=r+22|0;do{t[r>>1]=t[i>>1]|0;r=r+2|0;i=i+2|0}while((r|0)<(f|0));ht(o,h,d,40,s,0);ht(y,d,d,40,s,0);ot(o,a,b,40);r=w;i=b;f=r+80|0;do{t[r>>1]=t[i>>1]|0;r=r+2|0;i=i+2|0}while((r|0)<(f|0));ht(o,w,k,40,l,0);ot(m,k,v,40);ht(y,v,v,40,u,0);c=p;return}function tn(e,i,r,n,f,o,l,u,s,c,h,w,d,v,b,k,m){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;o=o|0;l=l|0;u=u|0;s=s|0;c=c|0;h=h|0;w=w|0;d=d|0;v=v|0;b=b|0;k=k|0;m=m|0;var y=0,p=0,E=0,g=0,A=0;if((i|0)==7){E=11;i=n<<16>>16>>>1&65535;y=2}else{E=13;i=n;y=1}t[k>>1]=n<<16>>16<13017?n:13017;p=r<<16>>16;b=b+(p<<1)|0;k=i<<16>>16;f=f<<16>>16;r=20;i=s;m=b;while(1){s=m+2|0;A=I(t[m>>1]|0,k)|0;g=I(t[s>>1]|0,k)|0;A=(I(t[i>>1]|0,f)|0)+A<<1;g=(I(t[i+2>>1]|0,f)|0)+g<<1<<y;t[m>>1]=((A<<y)+32768|0)>>>16;t[s>>1]=(g+32768|0)>>>16;r=r+-1<<16>>16;if(!(r<<16>>16))break;else{i=i+4|0;m=m+4|0}}i=n<<16>>16;ht(o,b,l+(p<<1)|0,40,w,1);r=30;m=0;while(1){g=r+p|0;t[d+(m<<1)>>1]=(a[e+(g<<1)>>1]|0)-(a[l+(g<<1)>>1]|0);g=I(t[c+(r<<1)>>1]|0,i)|0;A=(I(t[h+(r<<1)>>1]|0,f)|0)>>E;t[v+(m<<1)>>1]=(a[u+(r<<1)>>1]|0)-(g>>>14)-A;m=m+1|0;if((m|0)==10)break;else r=r+1|0}return}function fn(e){e=e|0;var i=0;if(!e){e=-1;return e|0}f[e>>2]=0;i=dt(16)|0;if(!i){e=-1;return e|0}t[i>>1]=0;t[i+2>>1]=0;t[i+4>>1]=0;t[i+6>>1]=0;t[i+8>>1]=0;t[i+10>>1]=0;t[i+12>>1]=0;t[i+14>>1]=0;f[e>>2]=i;e=0;return e|0}function on(e){e=e|0;if(!e){e=-1;return e|0}t[e>>1]=0;t[e+2>>1]=0;t[e+4>>1]=0;t[e+6>>1]=0;t[e+8>>1]=0;t[e+10>>1]=0;t[e+12>>1]=0;t[e+14>>1]=0;e=0;return e|0}function an(e){e=e|0;var i=0;if(!e)return;i=f[e>>2]|0;if(!i)return;vt(i);f[e>>2]=0;return}function ln(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,f=0,o=0,l=0;n=a[i+6>>1]|0;r=a[i+8>>1]|0;f=n-r|0;f=(f&65535|0)!=32767?f&65535:32767;o=a[i+10>>1]|0;r=r-o|0;f=(r<<16>>16|0)<(f<<16>>16|0)?r&65535:f;r=a[i+12>>1]|0;o=o-r|0;f=(o<<16>>16|0)<(f<<16>>16|0)?o&65535:f;o=a[i+14>>1]|0;r=r-o|0;f=(r<<16>>16|0)<(f<<16>>16|0)?r&65535:f;o=o-(a[i+16>>1]|0)|0;r=t[i+2>>1]|0;l=a[i+4>>1]|0;i=(r&65535)-l|0;i=(i&65535|0)!=32767?i&65535:32767;n=l-n|0;if(((o<<16>>16|0)<(f<<16>>16|0)?o&65535:f)<<16>>16<1500?1:(((n<<16>>16|0)<(i<<16>>16|0)?n&65535:i)<<16>>16|0)<((r<<16>>16>32e3?600:r<<16>>16>30500?800:1100)|0)){o=(t[e>>1]|0)+1<<16>>16;l=o<<16>>16>11;t[e>>1]=l?12:o;return l&1|0}else{t[e>>1]=0;return 0}return 0}function un(e,i,r){e=e|0;i=i|0;r=r|0;i=lt(i,3,r)|0;i=gn(i,t[e+2>>1]|0,r)|0;i=gn(i,t[e+4>>1]|0,r)|0;i=gn(i,t[e+6>>1]|0,r)|0;i=gn(i,t[e+8>>1]|0,r)|0;i=gn(i,t[e+10>>1]|0,r)|0;i=gn(i,t[e+12>>1]|0,r)|0;return(gn(i,t[e+14>>1]|0,r)|0)<<16>>16>15565|0}function sn(e,i,r){e=e|0;i=i|0;r=r|0;var n=0;r=e+4|0;t[e+2>>1]=t[r>>1]|0;n=e+6|0;t[r>>1]=t[n>>1]|0;r=e+8|0;t[n>>1]=t[r>>1]|0;n=e+10|0;t[r>>1]=t[n>>1]|0;r=e+12|0;t[n>>1]=t[r>>1]|0;e=e+14|0;t[r>>1]=t[e>>1]|0;t[e>>1]=i<<16>>16>>>3;return}function cn(e){e=e|0;var i=0,r=0,n=0;if(!e){n=-1;return n|0}f[e>>2]=0;i=dt(128)|0;if(!i){n=-1;return n|0}r=i+72|0;n=r+46|0;do{t[r>>1]=0;r=r+2|0}while((r|0)<(n|0));t[i>>1]=150;t[i+36>>1]=150;t[i+18>>1]=150;t[i+54>>1]=0;t[i+2>>1]=150;t[i+38>>1]=150;t[i+20>>1]=150;t[i+56>>1]=0;t[i+4>>1]=150;t[i+40>>1]=150;t[i+22>>1]=150;t[i+58>>1]=0;t[i+6>>1]=150;t[i+42>>1]=150;t[i+24>>1]=150;t[i+60>>1]=0;t[i+8>>1]=150;t[i+44>>1]=150;t[i+26>>1]=150;t[i+62>>1]=0;t[i+10>>1]=150;t[i+46>>1]=150;t[i+28>>1]=150;t[i+64>>1]=0;t[i+12>>1]=150;t[i+48>>1]=150;t[i+30>>1]=150;t[i+66>>1]=0;t[i+14>>1]=150;t[i+50>>1]=150;t[i+32>>1]=150;t[i+68>>1]=0;t[i+16>>1]=150;t[i+52>>1]=150;t[i+34>>1]=150;t[i+70>>1]=0;t[i+118>>1]=13106;t[i+120>>1]=0;t[i+122>>1]=0;t[i+124>>1]=0;t[i+126>>1]=13106;f[e>>2]=i;n=0;return n|0}function hn(e){e=e|0;var i=0,r=0;if(!e){r=-1;return r|0}i=e+72|0;r=i+46|0;do{t[i>>1]=0;i=i+2|0}while((i|0)<(r|0));t[e>>1]=150;t[e+36>>1]=150;t[e+18>>1]=150;t[e+54>>1]=0;t[e+2>>1]=150;t[e+38>>1]=150;t[e+20>>1]=150;t[e+56>>1]=0;t[e+4>>1]=150;t[e+40>>1]=150;t[e+22>>1]=150;t[e+58>>1]=0;t[e+6>>1]=150;t[e+42>>1]=150;t[e+24>>1]=150;t[e+60>>1]=0;t[e+8>>1]=150;t[e+44>>1]=150;t[e+26>>1]=150;t[e+62>>1]=0;t[e+10>>1]=150;t[e+46>>1]=150;t[e+28>>1]=150;t[e+64>>1]=0;t[e+12>>1]=150;t[e+48>>1]=150;t[e+30>>1]=150;t[e+66>>1]=0;t[e+14>>1]=150;t[e+50>>1]=150;t[e+32>>1]=150;t[e+68>>1]=0;t[e+16>>1]=150;t[e+52>>1]=150;t[e+34>>1]=150;t[e+70>>1]=0;t[e+118>>1]=13106;t[e+120>>1]=0;t[e+122>>1]=0;t[e+124>>1]=0;t[e+126>>1]=13106;r=0;return r|0}function wn(e){e=e|0;var i=0;if(!e)return;i=f[e>>2]|0;if(!i)return;vt(i);f[e>>2]=0;return}function dn(e,i){e=e|0;i=i|0;t[e+118>>1]=i;return}function vn(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var o=0;r=at(r,n)|0;if(r<<16>>16<=0)return;r=r<<16>>16;if((r*21298|0)==1073741824){f[n>>2]=1;o=2147483647}else o=r*42596|0;r=i-o|0;if(((r^i)&(o^i)|0)<0){f[n>>2]=1;r=(i>>>31)+2147483647|0}if((r|0)<=0)return;e=e+104|0;t[e>>1]=a[e>>1]|0|16384;return}function bn(e,i,r){e=e|0;i=i|0;r=r|0;var n=0;e=e+104|0;n=lt(t[e>>1]|0,1,r)|0;t[e>>1]=n;if(!(i<<16>>16))return;t[e>>1]=(lt(n,1,r)|0)&65535|8192;return}function kn(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,f=0,o=0;f=e+112|0;n=ct(t[f>>1]|0,t[i>>1]|0,r)|0;n=(n&65535)-((n&65535)>>>15&65535)|0;n=((n<<16>>31^n)&65535)<<16>>16<4;o=t[i>>1]|0;t[f>>1]=o;i=i+2|0;o=ct(o,t[i>>1]|0,r)|0;o=(o&65535)-((o&65535)>>>15&65535)|0;n=((o<<16>>31^o)&65535)<<16>>16<4?n?2:1:n&1;t[f>>1]=t[i>>1]|0;f=e+102|0;t[f>>1]=lt(t[f>>1]|0,1,r)|0;i=e+110|0;if((gn(t[i>>1]|0,n,r)|0)<<16>>16<=3){t[i>>1]=n;return}t[f>>1]=a[f>>1]|0|16384;t[i>>1]=n;return}function mn(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,o=0,l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0;O=c;c=c+352|0;w=O+24|0;M=O;u=0;o=0;do{n=t[i+(u+-40<<1)>>1]|0;n=I(n,n)|0;if((n|0)!=1073741824){l=(n<<1)+o|0;if((n^o|0)>0&(l^o|0)<0){f[r>>2]=1;o=(o>>>31)+2147483647|0}else o=l}else{f[r>>2]=1;o=2147483647}u=u+1|0}while((u|0)!=160);d=o;if((343039-d&d|0)<0){f[r>>2]=1;o=(d>>>31)+2147483647|0}else o=d+-343040|0;if((o|0)<0){R=e+102|0;t[R>>1]=a[R>>1]&16383}h=d+-15e3|0;v=(14999-d&d|0)<0;if(v){f[r>>2]=1;l=(d>>>31)+2147483647|0}else l=h;if((l|0)<0){R=e+108|0;t[R>>1]=a[R>>1]&16383}n=e+72|0;s=e+74|0;l=t[n>>1]|0;u=t[s>>1]|0;o=0;do{R=o<<2;S=ct((t[i+(R<<1)>>1]|0)>>>2&65535,((l<<16>>16)*21955|0)>>>15&65535,r)|0;E=((S<<16>>16)*21955|0)>>>15&65535;p=gn(l,E,r)|0;A=R|1;_=ct((t[i+(A<<1)>>1]|0)>>>2&65535,((u<<16>>16)*6390|0)>>>15&65535,r)|0;g=((_<<16>>16)*6390|0)>>>15&65535;l=gn(u,g,r)|0;t[w+(R<<1)>>1]=gn(p,l,r)|0;t[w+(A<<1)>>1]=ct(p,l,r)|0;A=R|2;l=ct((t[i+(A<<1)>>1]|0)>>>2&65535,E,r)|0;S=gn(S,((l<<16>>16)*21955|0)>>>15&65535,r)|0;R=R|3;u=ct((t[i+(R<<1)>>1]|0)>>>2&65535,g,r)|0;_=gn(_,((u<<16>>16)*6390|0)>>>15&65535,r)|0;t[w+(A<<1)>>1]=gn(S,_,r)|0;t[w+(R<<1)>>1]=ct(S,_,r)|0;o=o+1|0}while((o|0)!=40);t[n>>1]=l;t[s>>1]=u;u=e+76|0;l=e+80|0;o=0;do{R=o<<2;yn(w+(R<<1)|0,w+((R|2)<<1)|0,u,r);yn(w+((R|1)<<1)|0,w+((R|3)<<1)|0,l,r);o=o+1|0}while((o|0)!=40);u=e+84|0;l=e+86|0;o=e+92|0;n=0;do{R=n<<3;pn(w+(R<<1)|0,w+((R|4)<<1)|0,u,r);pn(w+((R|2)<<1)|0,w+((R|6)<<1)|0,l,r);pn(w+((R|3)<<1)|0,w+((R|7)<<1)|0,o,r);n=n+1|0}while((n|0)!=20);u=e+88|0;l=e+90|0;o=0;do{R=o<<4;pn(w+(R<<1)|0,w+((R|8)<<1)|0,u,r);pn(w+((R|4)<<1)|0,w+((R|12)<<1)|0,l,r);o=o+1|0}while((o|0)!=10);y=En(w,e+70|0,32,40,4,1,15,r)|0;t[M+16>>1]=y;p=En(w,e+68|0,16,20,8,7,16,r)|0;t[M+14>>1]=p;E=En(w,e+66|0,16,20,8,3,16,r)|0;t[M+12>>1]=E;g=En(w,e+64|0,16,20,8,2,16,r)|0;t[M+10>>1]=g;A=En(w,e+62|0,16,20,8,6,16,r)|0;t[M+8>>1]=A;S=En(w,e+60|0,8,10,16,4,16,r)|0;t[M+6>>1]=S;_=En(w,e+58|0,8,10,16,12,16,r)|0;t[M+4>>1]=_;R=En(w,e+56|0,8,10,16,8,16,r)|0;t[M+2>>1]=R;m=En(w,e+54|0,8,10,16,0,16,r)|0;t[M>>1]=m;u=0;n=0;do{l=e+(n<<1)|0;i=Yn(t[l>>1]|0)|0;l=t[l>>1]|0;o=i<<16>>16;if(i<<16>>16<0){s=0-o<<16;if((s|0)<983040)s=l<<16>>16>>(s>>16)&65535;else s=0}else{s=l<<16>>16;l=s<<o;if((l<<16>>16>>o|0)==(s|0))s=l&65535;else s=(s>>>15^32767)&65535}l=Sn(lt(t[M+(n<<1)>>1]|0,1,r)|0,s)|0;k=ct(i,5,r)|0;o=k<<16>>16;if(k<<16>>16<0){s=0-o<<16;if((s|0)<983040)s=l<<16>>16>>(s>>16);else s=0}else{l=l<<16>>16;s=l<<o;if((s<<16>>16>>o|0)!=(l|0))s=l>>>15^32767}s=s<<16>>16;s=I(s,s)|0;if((s|0)!=1073741824){l=(s<<1)+u|0;if((s^u|0)>0&(l^u|0)<0){f[r>>2]=1;u=(u>>>31)+2147483647|0}else u=l}else{f[r>>2]=1;u=2147483647}n=n+1|0}while((n|0)!=9);k=u<<6;u=(((k>>6|0)==(u|0)?k:u>>31^2147418112)>>16)*3641>>15;if((u|0)>32767){f[r>>2]=1;u=32767}k=t[e>>1]|0;s=k<<16>>16;b=t[e+2>>1]|0;l=(b<<16>>16)+s|0;if((b^k)<<16>>16>-1&(l^s|0)<0){f[r>>2]=1;l=(s>>>31)+2147483647|0}k=t[e+4>>1]|0;s=k+l|0;if((k^l|0)>-1&(s^l|0)<0){f[r>>2]=1;s=(l>>>31)+2147483647|0}k=t[e+6>>1]|0;l=k+s|0;if((k^s|0)>-1&(l^s|0)<0){f[r>>2]=1;l=(s>>>31)+2147483647|0}k=t[e+8>>1]|0;s=k+l|0;if((k^l|0)>-1&(s^l|0)<0){f[r>>2]=1;s=(l>>>31)+2147483647|0}k=t[e+10>>1]|0;l=k+s|0;if((k^s|0)>-1&(l^s|0)<0){f[r>>2]=1;l=(s>>>31)+2147483647|0}k=t[e+12>>1]|0;s=k+l|0;if((k^l|0)>-1&(s^l|0)<0){f[r>>2]=1;s=(l>>>31)+2147483647|0}k=t[e+14>>1]|0;l=k+s|0;if((k^s|0)>-1&(l^s|0)<0){f[r>>2]=1;l=(s>>>31)+2147483647|0}k=t[e+16>>1]|0;s=k+l|0;if((k^l|0)>-1&(s^l|0)<0){f[r>>2]=1;s=(l>>>31)+2147483647|0}b=s<<13;b=((b>>13|0)==(s|0)?b:s>>31^2147418112)>>>16&65535;s=(I((ct(b,0,r)|0)<<16>>16,-2808)|0)>>15;if((s|0)>32767){f[r>>2]=1;s=32767}w=gn(s&65535,1260,r)|0;k=e+100|0;s=lt(t[k>>1]|0,1,r)|0;if((u<<16>>16|0)>((w<<16>>16<720?720:w<<16>>16)|0))s=(s&65535|16384)&65535;t[k>>1]=s;if(v){f[r>>2]=1;h=(d>>>31)+2147483647|0}o=t[e+118>>1]|0;v=e+126|0;s=t[v>>1]|0;n=s<<16>>16<19660;n=o<<16>>16<s<<16>>16?n?2621:6553:n?2621:655;i=s&65535;u=i<<16;s=I(n,s<<16>>16)|0;if((s|0)==1073741824){f[r>>2]=1;s=2147483647}else s=s<<1;l=u-s|0;if(((l^u)&(s^u)|0)<0){f[r>>2]=1;l=(i>>>15)+2147483647|0}u=I(n,o<<16>>16)|0;do{if((u|0)==1073741824){f[r>>2]=1;s=2147483647}else{s=l+(u<<1)|0;if(!((l^u|0)>0&(s^l|0)<0))break;f[r>>2]=1;s=(l>>>31)+2147483647|0}}while(0);i=at(s,r)|0;d=(h|0)>-1;t[v>>1]=d?i<<16>>16<13106?13106:i:13106;i=e+106|0;t[i>>1]=lt(t[i>>1]|0,1,r)|0;l=e+108|0;s=lt(t[l>>1]|0,1,r)|0;t[l>>1]=s;u=t[v>>1]|0;e:do{if(d){do{if(u<<16>>16>19660)t[i>>1]=a[i>>1]|16384;else{if(u<<16>>16>16383)break;u=e+116|0;s=0;break e}}while(0);t[l>>1]=s&65535|16384;D=62}else D=62}while(0);do{if((D|0)==62){s=e+116|0;if(u<<16>>16<=22936){u=s;s=0;break}u=s;s=gn(t[s>>1]|0,1,r)|0}}while(0);t[u>>1]=s;if((t[i>>1]&32640)!=32640){w=(t[l>>1]&32767)==32767;t[e+122>>1]=w&1;if(w)D=67}else{t[e+122>>1]=1;D=67}do{if((D|0)==67){u=e+98|0;if((t[u>>1]|0)>=5)break;t[u>>1]=5}}while(0);w=e+102|0;do{if((t[w>>1]&24576)==24576)D=71;else{if((t[e+104>>1]&31744)==31744){D=71;break}if(!(t[k>>1]&32640)){t[e+98>>1]=20;l=32767;break}else{l=m;u=0;s=0}while(1){n=t[e+18+(u<<1)>>1]|0;o=l<<16>>16>n<<16>>16;h=o?l:n;l=o?n:l;h=h<<16>>16<184?184:h;l=l<<16>>16<184?184:l;n=Yn(l)|0;o=n<<16>>16;do{if(n<<16>>16<0){i=0-o<<16;if((i|0)>=983040){i=0;break}i=l<<16>>16>>(i>>16)&65535}else{i=l<<16>>16;l=i<<o;if((l<<16>>16>>o|0)==(i|0)){i=l&65535;break}i=(i>>>15^32767)&65535}}while(0);h=Sn(lt(h,1,r)|0,i)|0;s=gn(s,lt(h,ct(8,n,r)|0,r)|0,r)|0;u=u+1|0;if((u|0)==9)break;l=t[M+(u<<1)>>1]|0}if(s<<16>>16>1e3){t[e+98>>1]=20;l=32767;break}l=t[k>>1]|0;u=e+98|0;s=t[u>>1]|0;do{if(!(l&16384))D=86;else{if(!(s<<16>>16)){s=l;break}s=ct(s,1,r)|0;t[u>>1]=s;D=86}}while(0);if((D|0)==86){if(s<<16>>16==20){l=32767;break}s=t[k>>1]|0}l=(s&16384)==0?16383:3276}}while(0);if((D|0)==71){t[e+98>>1]=20;l=32767}u=m;s=0;while(1){h=e+18+(s<<1)|0;i=Vn(l,ct(u,t[h>>1]|0,r)|0,r)|0;t[h>>1]=gn(t[h>>1]|0,i,r)|0;s=s+1|0;if((s|0)==9)break;u=t[M+(s<<1)>>1]|0}do{if(!(t[k>>1]&30720)){if(t[w>>1]&30720){D=95;break}if(!(t[e+114>>1]|0)){o=2097;n=1638;i=2}else D=95}else D=95}while(0);do{if((D|0)==95){if((t[e+98>>1]|0)==0?(t[e+114>>1]|0)==0:0){o=1867;n=491;i=2;break}o=1638;n=0;i=0}}while(0);l=0;do{u=e+(l<<1)|0;s=ct(t[e+36+(l<<1)>>1]|0,t[u>>1]|0,r)|0;if(s<<16>>16<0){s=Vn(o,s,r)|0;s=gn(-2,gn(t[u>>1]|0,s,r)|0,r)|0;s=s<<16>>16<40?40:s}else{s=Vn(n,s,r)|0;s=gn(i,gn(t[u>>1]|0,s,r)|0,r)|0;s=s<<16>>16>16e3?16e3:s}t[u>>1]=s;l=l+1|0}while((l|0)!=9);t[e+36>>1]=m;t[e+38>>1]=R;t[e+40>>1]=_;t[e+42>>1]=S;t[e+44>>1]=A;t[e+46>>1]=g;t[e+48>>1]=E;t[e+50>>1]=p;t[e+52>>1]=y;u=b<<16>>16>100;l=u?7:4;u=u?4:5;if(!d){t[e+94>>1]=0;t[e+96>>1]=0;t[e+114>>1]=0;t[e+116>>1]=0;r=0;e=e+120|0;t[e>>1]=r;c=O;return r|0}o=e+114|0;n=t[o>>1]|0;do{if((t[e+116>>1]|0)<=100){if(n<<16>>16)break;n=t[k>>1]|0;do{if(!(n&16368)){if((t[v>>1]|0)>21298)n=1;else break;e=e+120|0;t[e>>1]=n;c=O;return n|0}}while(0);o=e+94|0;if(!(n&16384)){t[o>>1]=0;n=e+96|0;o=t[n>>1]|0;if(o<<16>>16<=0){r=0;e=e+120|0;t[e>>1]=r;c=O;return r|0}t[n>>1]=ct(o,1,r)|0;r=1;e=e+120|0;t[e>>1]=r;c=O;return r|0}else{r=gn(t[o>>1]|0,1,r)|0;t[o>>1]=r;if((r<<16>>16|0)<(u|0)){r=1;e=e+120|0;t[e>>1]=r;c=O;return r|0}t[e+96>>1]=l;r=1;e=e+120|0;t[e>>1]=r;c=O;return r|0}}else{if(n<<16>>16>=250)break;t[o>>1]=250;n=250}}while(0);t[e+94>>1]=4;t[o>>1]=ct(n,1,r)|0;r=1;e=e+120|0;t[e>>1]=r;c=O;return r|0}function yn(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var o=0,a=0,l=0;o=(t[r>>1]|0)*21955>>15;if((o|0)>32767){f[n>>2]=1;o=32767}a=ct(t[e>>1]|0,o&65535,n)|0;o=(a<<16>>16)*21955>>15;if((o|0)>32767){f[n>>2]=1;o=32767}l=gn(t[r>>1]|0,o&65535,n)|0;t[r>>1]=a;r=r+2|0;o=(t[r>>1]|0)*6390>>15;if((o|0)>32767){f[n>>2]=1;o=32767}a=ct(t[i>>1]|0,o&65535,n)|0;o=(a<<16>>16)*6390>>15;if((o|0)>32767){f[n>>2]=1;o=32767}o=gn(t[r>>1]|0,o&65535,n)|0;t[r>>1]=a;t[e>>1]=lt(gn(l,o,n)|0,1,n)|0;t[i>>1]=lt(ct(l,o,n)|0,1,n)|0;return}function pn(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var o=0,a=0;o=(t[r>>1]|0)*13363>>15;if((o|0)>32767){f[n>>2]=1;o=32767}a=ct(t[i>>1]|0,o&65535,n)|0;o=(a<<16>>16)*13363>>15;if((o|0)>32767){f[n>>2]=1;o=32767}o=gn(t[r>>1]|0,o&65535,n)|0;t[r>>1]=a;t[i>>1]=lt(ct(t[e>>1]|0,o,n)|0,1,n)|0;t[e>>1]=lt(gn(t[e>>1]|0,o,n)|0,1,n)|0;return}function En(e,i,r,n,o,a,l,u){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;l=l|0;u=u|0;var s=0,c=0,h=0,w=0,d=0,v=0;if(r<<16>>16<n<<16>>16){w=o<<16>>16;s=a<<16>>16;d=r<<16>>16;c=0;do{v=t[e+((I(d,w)|0)+s<<1)>>1]|0;v=(v&65535)-((v&65535)>>>15&65535)|0;v=(v<<16>>31^v)<<16;h=(v>>15)+c|0;if((v>>16^c|0)>0&(h^c|0)<0){f[u>>2]=1;c=(c>>>31)+2147483647|0}else c=h;d=d+1|0}while((d&65535)<<16>>16!=n<<16>>16);d=c}else d=0;c=t[i>>1]|0;v=ct(16,l,u)|0;s=v<<16>>16;if(v<<16>>16>0){n=c<<s;if((n>>s|0)!=(c|0))n=c>>31^2147483647}else{s=0-s<<16;if((s|0)<2031616)n=c>>(s>>16);else n=0}s=n+d|0;if((n^d|0)>-1&(s^d|0)<0){f[u>>2]=1;s=(d>>>31)+2147483647|0}v=l<<16>>16;l=l<<16>>16>0;if(l){n=d<<v;if((n>>v|0)!=(d|0))n=d>>31^2147483647}else{n=0-v<<16;if((n|0)<2031616)n=d>>(n>>16);else n=0}t[i>>1]=n>>>16;if(r<<16>>16>0){w=o<<16>>16;c=a<<16>>16;h=0;do{a=t[e+((I(h,w)|0)+c<<1)>>1]|0;a=(a&65535)-((a&65535)>>>15&65535)|0;a=(a<<16>>31^a)<<16;n=(a>>15)+s|0;if((a>>16^s|0)>0&(n^s|0)<0){f[u>>2]=1;s=(s>>>31)+2147483647|0}else s=n;h=h+1|0}while((h&65535)<<16>>16!=r<<16>>16)}if(l){n=s<<v;if((n>>v|0)==(s|0)){u=n;u=u>>>16;u=u&65535;return u|0}u=s>>31^2147483647;u=u>>>16;u=u&65535;return u|0}else{n=0-v<<16;if((n|0)>=2031616){u=0;u=u>>>16;u=u&65535;return u|0}u=s>>(n>>16);u=u>>>16;u=u&65535;return u|0}return 0}function gn(e,i,r){e=e|0;i=i|0;r=r|0;e=(i<<16>>16)+(e<<16>>16)|0;if((e|0)<=32767){if((e|0)<-32768){f[r>>2]=1;e=-32768}}else{f[r>>2]=1;e=32767}return e&65535|0}function An(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var f=0,o=0,a=0,l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0;S=c;c=c+32|0;g=S+12|0;A=S;t[g>>1]=1024;t[A>>1]=1024;u=t[e+2>>1]|0;a=t[e+20>>1]|0;n=((a+u|0)>>>2)+64512|0;t[g+2>>1]=n;a=((u-a|0)>>>2)+1024|0;t[A+2>>1]=a;u=t[e+4>>1]|0;f=t[e+18>>1]|0;n=((f+u|0)>>>2)-n|0;t[g+4>>1]=n;a=((u-f|0)>>>2)+a|0;t[A+4>>1]=a;f=t[e+6>>1]|0;u=t[e+16>>1]|0;n=((u+f|0)>>>2)-n|0;t[g+6>>1]=n;a=((f-u|0)>>>2)+a|0;t[A+6>>1]=a;u=t[e+8>>1]|0;f=t[e+14>>1]|0;n=((f+u|0)>>>2)-n|0;t[g+8>>1]=n;a=((u-f|0)>>>2)+a|0;t[A+8>>1]=a;f=t[e+10>>1]|0;u=t[e+12>>1]|0;n=((u+f|0)>>>2)-n|0;t[g+10>>1]=n;t[A+10>>1]=((f-u|0)>>>2)+a;a=t[3454]|0;u=a<<16>>16;e=t[g+2>>1]|0;f=(e<<16>>16<<14)+(u<<10)|0;m=f&-65536;f=(f>>>1)-(f>>16<<15)<<16;E=(((I(f>>16,u)|0)>>15)+(I(m>>16,u)|0)<<2)+-16777216|0;E=(t[g+4>>1]<<14)+E|0;l=E>>16;E=(E>>>1)-(l<<15)<<16;m=(((I(E>>16,u)|0)>>15)+(I(l,u)|0)<<2)-((f>>15)+m)|0;m=(t[g+6>>1]<<14)+m|0;f=m>>16;m=(m>>>1)-(f<<15)<<16;l=(((I(m>>16,u)|0)>>15)+(I(f,u)|0)<<2)-((E>>15)+(l<<16))|0;l=(t[g+8>>1]<<14)+l|0;E=l>>16;f=(n<<16>>3)+((((I((l>>>1)-(E<<15)<<16>>16,u)|0)>>15)+(I(E,u)|0)<<1)-((m>>15)+(f<<16)))|0;m=g+4|0;u=g;E=0;l=0;n=0;k=g+10|0;f=(f+33554432|0)>>>0<67108863?f>>>10&65535:(f|0)>33554431?32767:-32768;e:while(1){y=e<<16>>16<<14;b=u+6|0;v=u+8|0;d=l<<16>>16;while(1){if((d|0)>=60)break e;u=(d&65535)+1<<16>>16;s=t[6908+(u<<16>>16<<1)>>1]|0;p=s<<16>>16;l=y+(p<<10)|0;o=l&-65536;l=(l>>>1)-(l>>16<<15)<<16;h=(((I(l>>16,p)|0)>>15)+(I(o>>16,p)|0)<<2)+-16777216|0;w=t[m>>1]|0;h=(w<<16>>16<<14)+h|0;M=h>>16;h=(h>>>1)-(M<<15)<<16;o=(((I(h>>16,p)|0)>>15)+(I(M,p)|0)<<2)-((l>>15)+o)|0;l=t[b>>1]|0;o=(l<<16>>16<<14)+o|0;e=o>>16;o=(o>>>1)-(e<<15)<<16;M=(((I(o>>16,p)|0)>>15)+(I(e,p)|0)<<2)-((h>>15)+(M<<16))|0;h=t[v>>1]|0;M=(h<<16>>16<<14)+M|0;R=M>>16;e=(((I((M>>>1)-(R<<15)<<16>>16,p)|0)>>15)+(I(R,p)|0)<<1)-((o>>15)+(e<<16))|0;o=t[k>>1]|0;e=(o<<16>>16<<13)+e|0;e=(e+33554432|0)>>>0<67108863?e>>>10&65535:(e|0)>33554431?32767:-32768;if((I(e<<16>>16,f<<16>>16)|0)<1){p=u;u=w;break}else{d=d+1|0;a=s;f=e}}m=o<<16>>16<<13;k=u<<16>>16<<14;w=l<<16>>16<<14;v=h<<16>>16<<14;o=s<<16>>16;d=4;while(1){R=(a<<16>>16>>>1)+(o>>>1)|0;o=R<<16;b=o>>16;o=y+(o>>6)|0;M=o&-65536;o=(o>>>1)-(o>>16<<15)<<16;h=k+((((I(o>>16,b)|0)>>15)+(I(M>>16,b)|0)<<2)+-16777216)|0;u=h>>16;h=(h>>>1)-(u<<15)<<16;M=w+((((I(h>>16,b)|0)>>15)+(I(u,b)|0)<<2)-((o>>15)+M))|0;o=M>>16;M=(M>>>1)-(o<<15)<<16;u=v+((((I(M>>16,b)|0)>>15)+(I(o,b)|0)<<2)-((h>>15)+(u<<16)))|0;h=u>>16;R=R&65535;o=m+((((I((u>>>1)-(h<<15)<<16>>16,b)|0)>>15)+(I(h,b)|0)<<1)-((M>>15)+(o<<16)))|0;o=(o+33554432|0)>>>0<67108863?o>>>10&65535:(o|0)>33554431?32767:-32768;M=(I(o<<16>>16,e<<16>>16)|0)<1;b=M?s:R;e=M?e:o;a=M?R:a;f=M?o:f;d=d+-1<<16>>16;o=b<<16>>16;if(!(d<<16>>16)){s=o;l=a;a=b;break}else s=b}u=n<<16>>16;o=e<<16>>16;e=(f&65535)-o|0;f=e<<16;if(f){M=(e&65535)-(e>>>15&1)|0;M=M<<16>>31^M;e=(Yn(M&65535)|0)<<16>>16;e=(I((Sn(16383,M<<16>>16<<e&65535)|0)<<16>>16,(l&65535)-s<<16>>16)|0)>>19-e;if((f|0)<0)e=0-(e<<16>>16)|0;a=s-((I(e<<16>>16,o)|0)>>>10)&65535}t[i+(u<<1)>>1]=a;f=E<<16>>16==0?A:g;R=a<<16>>16;e=t[f+2>>1]|0;o=(e<<16>>16<<14)+(R<<10)|0;M=o&-65536;o=(o>>>1)-(o>>16<<15)<<16;y=(((I(o>>16,R)|0)>>15)+(I(M>>16,R)|0)<<2)+-16777216|0;y=(t[f+4>>1]<<14)+y|0;m=y>>16;y=(y>>>1)-(m<<15)<<16;M=(((I(y>>16,R)|0)>>15)+(I(m,R)|0)<<2)-((o>>15)+M)|0;M=(t[f+6>>1]<<14)+M|0;o=M>>16;M=(M>>>1)-(o<<15)<<16;m=(((I(M>>16,R)|0)>>15)+(I(o,R)|0)<<2)-((y>>15)+(m<<16))|0;m=(t[f+8>>1]<<14)+m|0;y=m>>16;n=n+1<<16>>16;o=(((I((m>>>1)-(y<<15)<<16>>16,R)|0)>>15)+(I(y,R)|0)<<1)-((M>>15)+(o<<16))|0;o=(t[f+10>>1]<<13)+o|0;if(n<<16>>16<10){m=f+4|0;u=f;E=E^1;l=p;k=f+10|0;f=(o+33554432|0)>>>0<67108863?o>>>10&65535:(o|0)>33554431?32767:-32768}else{_=13;break}}if((_|0)==13){c=S;return}t[i>>1]=t[r>>1]|0;t[i+2>>1]=t[r+2>>1]|0;t[i+4>>1]=t[r+4>>1]|0;t[i+6>>1]=t[r+6>>1]|0;t[i+8>>1]=t[r+8>>1]|0;t[i+10>>1]=t[r+10>>1]|0;t[i+12>>1]=t[r+12>>1]|0;t[i+14>>1]=t[r+14>>1]|0;t[i+16>>1]=t[r+16>>1]|0;t[i+18>>1]=t[r+18>>1]|0;c=S;return}function Sn(e,i){e=e|0;i=i|0;var r=0,n=0,t=0,f=0,o=0,a=0;t=i<<16>>16;if(e<<16>>16<1?1:e<<16>>16>i<<16>>16){t=0;return t|0}if(e<<16>>16==i<<16>>16){t=32767;return t|0}n=t<<1;r=t<<2;f=e<<16>>16<<3;e=(f|0)<(r|0);f=f-(e?0:r)|0;e=e?0:4;o=(f|0)<(n|0);f=f-(o?0:n)|0;i=(f|0)<(t|0);e=(i&1|(o?e:e|2))<<3^8;i=f-(i?0:t)<<3;if((i|0)>=(r|0)){i=i-r|0;e=e&65528|4}f=(i|0)<(n|0);o=i-(f?0:n)|0;i=(o|0)<(t|0);e=(i&1^1|(f?e:e|2))<<16>>13;i=o-(i?0:t)<<3;if((i|0)>=(r|0)){i=i-r|0;e=e&65528|4}f=(i|0)<(n|0);o=i-(f?0:n)|0;i=(o|0)<(t|0);e=(i&1^1|(f?e:e|2))<<16>>13;i=o-(i?0:t)<<3;if((i|0)>=(r|0)){i=i-r|0;e=e&65528|4}a=(i|0)<(n|0);f=i-(a?0:n)|0;o=(f|0)<(t|0);i=(o&1^1|(a?e:e|2))<<16>>13;e=f-(o?0:t)<<3;if((e|0)>=(r|0)){e=e-r|0;i=i&65528|4}a=(e|0)<(n|0);a=((e-(a?0:n)|0)>=(t|0)|(a?i:i|2))&65535;return a|0}function _n(e){e=e|0;if(!e){e=-1;return e|0}t[e>>1]=-14336;t[e+8>>1]=-2381;t[e+2>>1]=-14336;t[e+10>>1]=-2381;t[e+4>>1]=-14336;t[e+12>>1]=-2381;t[e+6>>1]=-14336;t[e+14>>1]=-2381;e=0;return e|0}function Rn(e,i,r,n,o,l,u,s){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;l=l|0;u=u|0;s=s|0;var h=0,w=0,d=0,v=0,b=0,k=0;k=c;c=c+16|0;v=k+2|0;b=k;h=0;w=10;while(1){d=t[r>>1]|0;d=((I(d,d)|0)>>>3)+h|0;h=t[r+2>>1]|0;h=d+((I(h,h)|0)>>>3)|0;d=t[r+4>>1]|0;d=h+((I(d,d)|0)>>>3)|0;h=t[r+6>>1]|0;h=d+((I(h,h)|0)>>>3)|0;w=w+-1<<16>>16;if(!(w<<16>>16))break;else r=r+8|0}w=h<<4;w=(w|0)<0?2147483647:w;if((i|0)==7){Pn(((at(w,s)|0)<<16>>16)*52428|0,v,b,s);d=a[v>>1]<<16;w=t[b>>1]<<1;i=t[e+8>>1]|0;h=(i<<16>>16)*88|0;if(i<<16>>16>-1&(h|0)<-783741){f[s>>2]=1;r=2147483647}else r=h+783741|0;i=(t[e+10>>1]|0)*74|0;h=i+r|0;if((i^r|0)>-1&(h^r|0)<0){f[s>>2]=1;r=(r>>>31)+2147483647|0}else r=h;i=(t[e+12>>1]|0)*44|0;h=i+r|0;if((i^r|0)>-1&(h^r|0)<0){f[s>>2]=1;r=(r>>>31)+2147483647|0}else r=h;e=(t[e+14>>1]|0)*24|0;h=e+r|0;if((e^r|0)>-1&(h^r|0)<0){f[s>>2]=1;h=(r>>>31)+2147483647|0}e=d+-1966080+w|0;r=h-e|0;if(((r^h)&(h^e)|0)<0){f[s>>2]=1;r=(h>>>31)+2147483647|0}s=r>>17;t[n>>1]=s;s=(r>>2)-(s<<15)|0;s=s&65535;t[o>>1]=s;c=k;return}d=Kn(w)|0;h=d<<16>>16;if(d<<16>>16>0){r=w<<h;if((r>>h|0)==(w|0))w=r;else w=w>>31^2147483647}else{h=0-h<<16;if((h|0)<2031616)w=w>>(h>>16);else w=0}Bn(w,d,v,b);v=I(t[v>>1]|0,-49320)|0;h=(I(t[b>>1]|0,-24660)|0)>>15;h=(h&65536|0)==0?h:h|-65536;b=h<<1;r=b+v|0;if((b^v|0)>-1&(r^b|0)<0){f[s>>2]=1;r=(h>>>30&1)+2147483647|0}switch(i|0){case 6:{h=r+2134784|0;if((r|0)>-1&(h^r|0)<0){f[s>>2]=1;h=(r>>>31)+2147483647|0}break}case 5:{t[u>>1]=w>>>16;t[l>>1]=-11-(d&65535);h=r+2183936|0;if((r|0)>-1&(h^r|0)<0){f[s>>2]=1;h=(r>>>31)+2147483647|0}break}case 4:{h=r+2085632|0;if((r|0)>-1&(h^r|0)<0){f[s>>2]=1;h=(r>>>31)+2147483647|0}break}case 3:{h=r+2065152|0;if((r|0)>-1&(h^r|0)<0){f[s>>2]=1;h=(r>>>31)+2147483647|0}break}default:{h=r+2134784|0;if((r|0)>-1&(h^r|0)<0){f[s>>2]=1;h=(r>>>31)+2147483647|0}}}do{if((h|0)<=2097151)if((h|0)<-2097152){f[s>>2]=1;r=-2147483648;break}else{r=h<<10;break}else{f[s>>2]=1;r=2147483647}}while(0);u=(t[e>>1]|0)*11142|0;h=u+r|0;if((u^r|0)>-1&(h^r|0)<0){f[s>>2]=1;h=(r>>>31)+2147483647|0}u=(t[e+2>>1]|0)*9502|0;r=u+h|0;if((u^h|0)>-1&(r^h|0)<0){f[s>>2]=1;r=(h>>>31)+2147483647|0}u=(t[e+4>>1]|0)*5570|0;h=u+r|0;if((u^r|0)>-1&(h^r|0)<0){f[s>>2]=1;h=(r>>>31)+2147483647|0}e=(t[e+6>>1]|0)*3112|0;r=e+h|0;if((e^h|0)>-1&(r^h|0)<0){f[s>>2]=1;r=(h>>>31)+2147483647|0}r=I(r>>16,(i|0)==4?10878:10886)|0;if((r|0)<0)r=~((r^-256)>>8);else r=r>>8;t[n>>1]=r>>>16;if((r|0)<0)h=~((r^-2)>>1);else h=r>>1;n=r>>16<<15;r=h-n|0;if(((r^h)&(n^h)|0)>=0){s=r;s=s&65535;t[o>>1]=s;c=k;return}f[s>>2]=1;s=(h>>>31)+2147483647|0;s=s&65535;t[o>>1]=s;c=k;return}function Mn(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,f=0,o=0;f=e+4|0;t[e+6>>1]=t[f>>1]|0;o=e+12|0;t[e+14>>1]=t[o>>1]|0;n=e+2|0;t[f>>1]=t[n>>1]|0;f=e+10|0;t[o>>1]=t[f>>1]|0;t[n>>1]=t[e>>1]|0;n=e+8|0;t[f>>1]=t[n>>1]|0;t[n>>1]=i;t[e>>1]=r;return}function Dn(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var f=0,o=0;o=gn(0,t[e+8>>1]|0,n)|0;o=gn(o,t[e+10>>1]|0,n)|0;o=gn(o,t[e+12>>1]|0,n)|0;o=gn(o,t[e+14>>1]|0,n)|0;f=o<<16>>16>>2;f=(o<<16>>16<0?f|49152:f)&65535;t[i>>1]=f<<16>>16<-2381?-2381:f;i=gn(0,t[e>>1]|0,n)|0;i=gn(i,t[e+2>>1]|0,n)|0;i=gn(i,t[e+4>>1]|0,n)|0;n=gn(i,t[e+6>>1]|0,n)|0;e=n<<16>>16>>2;e=(n<<16>>16<0?e|49152:e)&65535;t[r>>1]=e<<16>>16<-14336?-14336:e;return}function On(e){e=e|0;f[e>>2]=6892;f[e+4>>2]=8180;f[e+8>>2]=21e3;f[e+12>>2]=9716;f[e+16>>2]=22024;f[e+20>>2]=12788;f[e+24>>2]=24072;f[e+28>>2]=26120;f[e+32>>2]=28168;f[e+36>>2]=6876;f[e+40>>2]=7452;f[e+44>>2]=8140;f[e+48>>2]=20980;f[e+52>>2]=16884;f[e+56>>2]=17908;f[e+60>>2]=7980;f[e+64>>2]=8160;f[e+68>>2]=6678;f[e+72>>2]=6646;f[e+76>>2]=6614;f[e+80>>2]=29704;f[e+84>>2]=28680;f[e+88>>2]=3720;f[e+92>>2]=8;f[e+96>>2]=4172;f[e+100>>2]=44;f[e+104>>2]=3436;f[e+108>>2]=30316;f[e+112>>2]=30796;f[e+116>>2]=31276;f[e+120>>2]=7472;f[e+124>>2]=7552;f[e+128>>2]=7632;f[e+132>>2]=7712;return}function Nn(e,i){e=e|0;i=i|0;var r=0,n=0,f=0,o=0,a=0,l=0,u=0,s=0,h=0,w=0;w=c;c=c+48|0;s=w+18|0;h=w;u=i<<16>>16;yt(h|0,e|0,u<<1|0)|0;if(i<<16>>16>0){r=0;n=0}else{h=u>>1;h=s+(h<<1)|0;h=t[h>>1]|0;h=h<<16>>16;h=e+(h<<1)|0;h=t[h>>1]|0;c=w;return h|0}do{l=0;a=-32767;while(1){f=t[h+(l<<1)>>1]|0;o=f<<16>>16<a<<16>>16;n=o?n:l&65535;l=l+1|0;if((l&65535)<<16>>16==i<<16>>16)break;else a=o?a:f}t[h+(n<<16>>16<<1)>>1]=-32768;t[s+(r<<1)>>1]=n;r=r+1|0}while((r&65535)<<16>>16!=i<<16>>16);h=u>>1;h=s+(h<<1)|0;h=t[h>>1]|0;h=h<<16>>16;h=e+(h<<1)|0;h=t[h>>1]|0;c=w;return h|0}function Tn(e,i,r,n,f){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;var o=0,a=0,l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0;o=c;c=c+32|0;a=o;R=i+2|0;_=a+2|0;t[a>>1]=((t[i>>1]|0)>>>1)+((t[e>>1]|0)>>>1);S=i+4|0;A=a+4|0;t[_>>1]=((t[R>>1]|0)>>>1)+((t[e+2>>1]|0)>>>1);g=i+6|0;E=a+6|0;t[A>>1]=((t[S>>1]|0)>>>1)+((t[e+4>>1]|0)>>>1);p=i+8|0;y=a+8|0;t[E>>1]=((t[g>>1]|0)>>>1)+((t[e+6>>1]|0)>>>1);m=i+10|0;k=a+10|0;t[y>>1]=((t[p>>1]|0)>>>1)+((t[e+8>>1]|0)>>>1);b=i+12|0;v=a+12|0;t[k>>1]=((t[m>>1]|0)>>>1)+((t[e+10>>1]|0)>>>1);d=i+14|0;w=a+14|0;t[v>>1]=((t[b>>1]|0)>>>1)+((t[e+12>>1]|0)>>>1);h=i+16|0;s=a+16|0;t[w>>1]=((t[d>>1]|0)>>>1)+((t[e+14>>1]|0)>>>1);u=i+18|0;l=a+18|0;t[s>>1]=((t[h>>1]|0)>>>1)+((t[e+16>>1]|0)>>>1);t[l>>1]=((t[u>>1]|0)>>>1)+((t[e+18>>1]|0)>>>1);zn(a,n,f);zn(i,n+22|0,f);t[a>>1]=((t[r>>1]|0)>>>1)+((t[i>>1]|0)>>>1);t[_>>1]=((t[r+2>>1]|0)>>>1)+((t[R>>1]|0)>>>1);t[A>>1]=((t[r+4>>1]|0)>>>1)+((t[S>>1]|0)>>>1);t[E>>1]=((t[r+6>>1]|0)>>>1)+((t[g>>1]|0)>>>1);t[y>>1]=((t[r+8>>1]|0)>>>1)+((t[p>>1]|0)>>>1);t[k>>1]=((t[r+10>>1]|0)>>>1)+((t[m>>1]|0)>>>1);t[v>>1]=((t[r+12>>1]|0)>>>1)+((t[b>>1]|0)>>>1);t[w>>1]=((t[r+14>>1]|0)>>>1)+((t[d>>1]|0)>>>1);t[s>>1]=((t[r+16>>1]|0)>>>1)+((t[h>>1]|0)>>>1);t[l>>1]=((t[r+18>>1]|0)>>>1)+((t[u>>1]|0)>>>1);zn(a,n+44|0,f);zn(r,n+66|0,f);c=o;return}function In(e,i,r,n,f){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;var o=0,a=0,l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0;o=c;c=c+32|0;a=o;R=i+2|0;_=a+2|0;t[a>>1]=((t[i>>1]|0)>>>1)+((t[e>>1]|0)>>>1);S=i+4|0;A=a+4|0;t[_>>1]=((t[R>>1]|0)>>>1)+((t[e+2>>1]|0)>>>1);g=i+6|0;E=a+6|0;t[A>>1]=((t[S>>1]|0)>>>1)+((t[e+4>>1]|0)>>>1);p=i+8|0;y=a+8|0;t[E>>1]=((t[g>>1]|0)>>>1)+((t[e+6>>1]|0)>>>1);m=i+10|0;k=a+10|0;t[y>>1]=((t[p>>1]|0)>>>1)+((t[e+8>>1]|0)>>>1);b=i+12|0;v=a+12|0;t[k>>1]=((t[m>>1]|0)>>>1)+((t[e+10>>1]|0)>>>1);d=i+14|0;w=a+14|0;t[v>>1]=((t[b>>1]|0)>>>1)+((t[e+12>>1]|0)>>>1);h=i+16|0;s=a+16|0;t[w>>1]=((t[d>>1]|0)>>>1)+((t[e+14>>1]|0)>>>1);u=i+18|0;l=a+18|0;t[s>>1]=((t[h>>1]|0)>>>1)+((t[e+16>>1]|0)>>>1);t[l>>1]=((t[u>>1]|0)>>>1)+((t[e+18>>1]|0)>>>1);zn(a,n,f);t[a>>1]=((t[r>>1]|0)>>>1)+((t[i>>1]|0)>>>1);t[_>>1]=((t[r+2>>1]|0)>>>1)+((t[R>>1]|0)>>>1);t[A>>1]=((t[r+4>>1]|0)>>>1)+((t[S>>1]|0)>>>1);t[E>>1]=((t[r+6>>1]|0)>>>1)+((t[g>>1]|0)>>>1);t[y>>1]=((t[r+8>>1]|0)>>>1)+((t[p>>1]|0)>>>1);t[k>>1]=((t[r+10>>1]|0)>>>1)+((t[m>>1]|0)>>>1);t[v>>1]=((t[r+12>>1]|0)>>>1)+((t[b>>1]|0)>>>1);t[w>>1]=((t[r+14>>1]|0)>>>1)+((t[d>>1]|0)>>>1);t[s>>1]=((t[r+16>>1]|0)>>>1)+((t[h>>1]|0)>>>1);t[l>>1]=((t[r+18>>1]|0)>>>1)+((t[u>>1]|0)>>>1);zn(a,n+44|0,f);c=o;return}function Ln(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var f=0,o=0,a=0,l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,I=0,L=0,F=0,C=0;f=c;c=c+32|0;o=f;L=t[e>>1]|0;t[o>>1]=L-(L>>>2)+((t[i>>1]|0)>>>2);L=e+2|0;N=t[L>>1]|0;F=i+2|0;I=o+2|0;t[I>>1]=N-(N>>>2)+((t[F>>1]|0)>>>2);N=e+4|0;M=t[N>>1]|0;T=i+4|0;O=o+4|0;t[O>>1]=M-(M>>>2)+((t[T>>1]|0)>>>2);M=e+6|0;S=t[M>>1]|0;D=i+6|0;R=o+6|0;t[R>>1]=S-(S>>>2)+((t[D>>1]|0)>>>2);S=e+8|0;E=t[S>>1]|0;_=i+8|0;A=o+8|0;t[A>>1]=E-(E>>>2)+((t[_>>1]|0)>>>2);E=e+10|0;m=t[E>>1]|0;g=i+10|0;p=o+10|0;t[p>>1]=m-(m>>>2)+((t[g>>1]|0)>>>2);m=e+12|0;v=t[m>>1]|0;y=i+12|0;k=o+12|0;t[k>>1]=v-(v>>>2)+((t[y>>1]|0)>>>2);v=e+14|0;h=t[v>>1]|0;b=i+14|0;d=o+14|0;t[d>>1]=h-(h>>>2)+((t[b>>1]|0)>>>2);h=e+16|0;l=t[h>>1]|0;w=i+16|0;s=o+16|0;t[s>>1]=l-(l>>>2)+((t[w>>1]|0)>>>2);l=e+18|0;C=t[l>>1]|0;u=i+18|0;a=o+18|0;t[a>>1]=C-(C>>>2)+((t[u>>1]|0)>>>2);zn(o,r,n);t[o>>1]=((t[e>>1]|0)>>>1)+((t[i>>1]|0)>>>1);t[I>>1]=((t[L>>1]|0)>>>1)+((t[F>>1]|0)>>>1);t[O>>1]=((t[N>>1]|0)>>>1)+((t[T>>1]|0)>>>1);t[R>>1]=((t[M>>1]|0)>>>1)+((t[D>>1]|0)>>>1);t[A>>1]=((t[S>>1]|0)>>>1)+((t[_>>1]|0)>>>1);t[p>>1]=((t[E>>1]|0)>>>1)+((t[g>>1]|0)>>>1);t[k>>1]=((t[m>>1]|0)>>>1)+((t[y>>1]|0)>>>1);t[d>>1]=((t[v>>1]|0)>>>1)+((t[b>>1]|0)>>>1);t[s>>1]=((t[h>>1]|0)>>>1)+((t[w>>1]|0)>>>1);t[a>>1]=((t[l>>1]|0)>>>1)+((t[u>>1]|0)>>>1);zn(o,r+22|0,n);C=t[i>>1]|0;t[o>>1]=C-(C>>>2)+((t[e>>1]|0)>>>2);e=t[F>>1]|0;t[I>>1]=e-(e>>>2)+((t[L>>1]|0)>>>2);e=t[T>>1]|0;t[O>>1]=e-(e>>>2)+((t[N>>1]|0)>>>2);e=t[D>>1]|0;t[R>>1]=e-(e>>>2)+((t[M>>1]|0)>>>2);e=t[_>>1]|0;t[A>>1]=e-(e>>>2)+((t[S>>1]|0)>>>2);e=t[g>>1]|0;t[p>>1]=e-(e>>>2)+((t[E>>1]|0)>>>2);e=t[y>>1]|0;t[k>>1]=e-(e>>>2)+((t[m>>1]|0)>>>2);e=t[b>>1]|0;t[d>>1]=e-(e>>>2)+((t[v>>1]|0)>>>2);e=t[w>>1]|0;t[s>>1]=e-(e>>>2)+((t[h>>1]|0)>>>2);e=t[u>>1]|0;t[a>>1]=e-(e>>>2)+((t[l>>1]|0)>>>2);zn(o,r+44|0,n);zn(i,r+66|0,n);c=f;return}function Fn(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var f=0,o=0,a=0,l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,I=0,L=0,F=0,C=0;f=c;c=c+32|0;o=f;L=t[e>>1]|0;t[o>>1]=L-(L>>>2)+((t[i>>1]|0)>>>2);L=e+2|0;N=t[L>>1]|0;F=i+2|0;I=o+2|0;t[I>>1]=N-(N>>>2)+((t[F>>1]|0)>>>2);N=e+4|0;M=t[N>>1]|0;T=i+4|0;O=o+4|0;t[O>>1]=M-(M>>>2)+((t[T>>1]|0)>>>2);M=e+6|0;S=t[M>>1]|0;D=i+6|0;R=o+6|0;t[R>>1]=S-(S>>>2)+((t[D>>1]|0)>>>2);S=e+8|0;E=t[S>>1]|0;_=i+8|0;A=o+8|0;t[A>>1]=E-(E>>>2)+((t[_>>1]|0)>>>2);E=e+10|0;m=t[E>>1]|0;g=i+10|0;p=o+10|0;t[p>>1]=m-(m>>>2)+((t[g>>1]|0)>>>2);m=e+12|0;v=t[m>>1]|0;y=i+12|0;k=o+12|0;t[k>>1]=v-(v>>>2)+((t[y>>1]|0)>>>2);v=e+14|0;h=t[v>>1]|0;b=i+14|0;d=o+14|0;t[d>>1]=h-(h>>>2)+((t[b>>1]|0)>>>2);h=e+16|0;l=t[h>>1]|0;w=i+16|0;s=o+16|0;t[s>>1]=l-(l>>>2)+((t[w>>1]|0)>>>2);l=e+18|0;C=t[l>>1]|0;u=i+18|0;a=o+18|0;t[a>>1]=C-(C>>>2)+((t[u>>1]|0)>>>2);zn(o,r,n);t[o>>1]=((t[e>>1]|0)>>>1)+((t[i>>1]|0)>>>1);t[I>>1]=((t[L>>1]|0)>>>1)+((t[F>>1]|0)>>>1);t[O>>1]=((t[N>>1]|0)>>>1)+((t[T>>1]|0)>>>1);t[R>>1]=((t[M>>1]|0)>>>1)+((t[D>>1]|0)>>>1);t[A>>1]=((t[S>>1]|0)>>>1)+((t[_>>1]|0)>>>1);t[p>>1]=((t[E>>1]|0)>>>1)+((t[g>>1]|0)>>>1);t[k>>1]=((t[m>>1]|0)>>>1)+((t[y>>1]|0)>>>1);t[d>>1]=((t[v>>1]|0)>>>1)+((t[b>>1]|0)>>>1);t[s>>1]=((t[h>>1]|0)>>>1)+((t[w>>1]|0)>>>1);t[a>>1]=((t[l>>1]|0)>>>1)+((t[u>>1]|0)>>>1);zn(o,r+22|0,n);i=t[i>>1]|0;t[o>>1]=i-(i>>>2)+((t[e>>1]|0)>>>2);e=t[F>>1]|0;t[I>>1]=e-(e>>>2)+((t[L>>1]|0)>>>2);e=t[T>>1]|0;t[O>>1]=e-(e>>>2)+((t[N>>1]|0)>>>2);e=t[D>>1]|0;t[R>>1]=e-(e>>>2)+((t[M>>1]|0)>>>2);e=t[_>>1]|0;t[A>>1]=e-(e>>>2)+((t[S>>1]|0)>>>2);e=t[g>>1]|0;t[p>>1]=e-(e>>>2)+((t[E>>1]|0)>>>2);e=t[y>>1]|0;t[k>>1]=e-(e>>>2)+((t[m>>1]|0)>>>2);e=t[b>>1]|0;t[d>>1]=e-(e>>>2)+((t[v>>1]|0)>>>2);e=t[w>>1]|0;t[s>>1]=e-(e>>>2)+((t[h>>1]|0)>>>2);e=t[u>>1]|0;t[a>>1]=e-(e>>>2)+((t[l>>1]|0)>>>2);zn(o,r+44|0,n);c=f;return}function Cn(e,i){e=e|0;i=i|0;var r=0,n=0;if((e|0)<1){i=1073741823;return i|0}r=(Kn(e)|0)<<16>>16;i=30-r|0;e=e<<r>>(i&1^1);r=(e>>25<<16)+-1048576>>16;n=t[7030+(r<<1)>>1]|0;i=(n<<16)-(I(n-(a[7030+(r+1<<1)>>1]|0)<<16>>15,e>>>10&32767)|0)>>(i<<16>>17)+1;return i|0}function Pn(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;n=Kn(e)|0;Bn(e<<(n<<16>>16),n,i,r);return}function Bn(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;if((e|0)<1){t[r>>1]=0;r=0;t[n>>1]=r;return}else{t[r>>1]=30-(i&65535);r=(e>>25<<16)+-2097152>>16;i=t[7128+(r<<1)>>1]|0;r=((i<<16)-(I(e>>>9&65534,i-(a[7128+(r+1<<1)>>1]|0)<<16>>16)|0)|0)>>>16&65535;t[n>>1]=r;return}}function Un(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,f=0;n=e+2|0;r=t[n>>1]|0;t[i>>1]=r;f=e+4|0;t[i+2>>1]=(a[f>>1]|0)-(a[e>>1]|0);t[i+4>>1]=(a[e+6>>1]|0)-(a[n>>1]|0);n=e+8|0;t[i+6>>1]=(a[n>>1]|0)-(a[f>>1]|0);t[i+8>>1]=(a[e+10>>1]|0)-(a[e+6>>1]|0);f=e+12|0;t[i+10>>1]=(a[f>>1]|0)-(a[n>>1]|0);t[i+12>>1]=(a[e+14>>1]|0)-(a[e+10>>1]|0);t[i+14>>1]=(a[e+16>>1]|0)-(a[f>>1]|0);t[i+16>>1]=(a[e+18>>1]|0)-(a[e+14>>1]|0);t[i+18>>1]=16384-(a[e+16>>1]|0);e=10;f=i;while(1){r=r<<16>>16;i=(r<<16)+-120782848|0;if((i|0)>0)i=1843-((i>>16)*12484>>16)|0;else i=3427-((r*56320|0)>>>16)|0;n=f+2|0;t[f>>1]=i<<3;e=e+-1<<16>>16;if(!(e<<16>>16))break;r=t[n>>1]|0;f=n}return}function xn(e,i,r){e=e|0;i=i|0;r=r|0;r=i<<16>>16;if(i<<16>>16>31){i=0;return i|0}if(i<<16>>16>0)return((1<<r+-1&e|0)!=0&1)+(i<<16>>16<31?e>>r:0)|0;r=0-r<<16>>16;i=e<<r;i=(i>>r|0)==(e|0)?i:e>>31^2147483647;return i|0}function zn(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,o=0,a=0,l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0;m=c;c=c+48|0;b=m+24|0;k=m;d=b+4|0;f[b>>2]=16777216;n=0-(t[e>>1]|0)|0;v=b+8|0;f[d>>2]=n<<10;o=t[e+4>>1]|0;s=n>>6;f[v>>2]=33554432-(((I((n<<9)-(s<<15)<<16>>16,o)|0)>>15)+(I(s,o)|0)<<2);s=b+4|0;o=(f[s>>2]|0)-(o<<10)|0;f[s>>2]=o;s=b+12|0;n=b+4|0;f[s>>2]=o;r=t[e+8>>1]|0;a=o;h=1;while(1){u=s+-4|0;l=f[u>>2]|0;w=l>>16;f[s>>2]=a+o-(((I((l>>>1)-(w<<15)<<16>>16,r)|0)>>15)+(I(w,r)|0)<<2);if((h|0)==2)break;a=f[s+-12>>2]|0;s=u;o=l;h=h+1|0}f[n>>2]=(f[n>>2]|0)-(r<<10);r=b+16|0;n=f[b+8>>2]|0;f[r>>2]=n;u=t[e+12>>1]|0;o=n;s=1;while(1){l=r+-4|0;a=f[l>>2]|0;w=a>>16;f[r>>2]=o+n-(((I((a>>>1)-(w<<15)<<16>>16,u)|0)>>15)+(I(w,u)|0)<<2);if((s|0)==3)break;o=f[r+-12>>2]|0;r=l;n=a;s=s+1|0}r=b+4|0;f[r>>2]=(f[r>>2]|0)-(u<<10);r=b+20|0;o=f[b+12>>2]|0;f[r>>2]=o;n=t[e+16>>1]|0;a=o;s=1;while(1){u=r+-4|0;l=f[u>>2]|0;w=l>>16;f[r>>2]=a+o-(((I((l>>>1)-(w<<15)<<16>>16,n)|0)>>15)+(I(w,n)|0)<<2);if((s|0)==4)break;a=f[r+-12>>2]|0;r=u;o=l;s=s+1|0}s=b+4|0;f[s>>2]=(f[s>>2]|0)-(n<<10);f[k>>2]=16777216;s=0-(t[e+2>>1]|0)|0;w=k+8|0;f[k+4>>2]=s<<10;n=t[e+6>>1]|0;h=s>>6;f[w>>2]=33554432-(((I((s<<9)-(h<<15)<<16>>16,n)|0)>>15)+(I(h,n)|0)<<2);h=k+4|0;n=(f[h>>2]|0)-(n<<10)|0;f[h>>2]=n;h=k+12|0;s=k+4|0;f[h>>2]=n;u=t[e+10>>1]|0;o=n;r=1;while(1){l=h+-4|0;a=f[l>>2]|0;y=a>>16;f[h>>2]=o+n-(((I((a>>>1)-(y<<15)<<16>>16,u)|0)>>15)+(I(y,u)|0)<<2);if((r|0)==2)break;o=f[h+-12>>2]|0;h=l;n=a;r=r+1|0}f[s>>2]=(f[s>>2]|0)-(u<<10);s=k+16|0;n=f[k+8>>2]|0;f[s>>2]=n;u=t[e+14>>1]|0;o=n;r=1;while(1){l=s+-4|0;a=f[l>>2]|0;y=a>>16;f[s>>2]=o+n-(((I((a>>>1)-(y<<15)<<16>>16,u)|0)>>15)+(I(y,u)|0)<<2);if((r|0)==3)break;o=f[s+-12>>2]|0;s=l;n=a;r=r+1|0}r=k+4|0;f[r>>2]=(f[r>>2]|0)-(u<<10);r=k+20|0;u=f[k+12>>2]|0;f[r>>2]=u;n=t[e+18>>1]|0;l=u;s=1;while(1){o=r+-4|0;a=f[o>>2]|0;y=a>>16;f[r>>2]=l+u-(((I((a>>>1)-(y<<15)<<16>>16,n)|0)>>15)+(I(y,n)|0)<<2);if((s|0)==4)break;l=f[r+-12>>2]|0;r=o;u=a;s=s+1|0}l=(f[k+4>>2]|0)-(n<<10)|0;h=b+20|0;u=k+20|0;s=f[b+16>>2]|0;e=(f[h>>2]|0)+s|0;f[h>>2]=e;h=f[k+16>>2]|0;y=(f[u>>2]|0)-h|0;f[u>>2]=y;u=f[b+12>>2]|0;s=s+u|0;f[b+16>>2]=s;a=f[k+12>>2]|0;h=h-a|0;f[k+16>>2]=h;n=f[v>>2]|0;u=u+n|0;f[b+12>>2]=u;o=f[w>>2]|0;v=a-o|0;f[k+12>>2]=v;a=f[d>>2]|0;w=n+a|0;f[b+8>>2]=w;d=o-l|0;f[k+8>>2]=d;b=a+(f[b>>2]|0)|0;k=l-(f[k>>2]|0)|0;t[i>>1]=4096;b=b+4096|0;t[i+2>>1]=(b+k|0)>>>13;t[i+20>>1]=(b-k|0)>>>13;k=w+4096|0;t[i+4>>1]=(k+d|0)>>>13;t[i+18>>1]=(k-d|0)>>>13;k=u+4096|0;t[i+6>>1]=(k+v|0)>>>13;t[i+16>>1]=(k-v|0)>>>13;k=s+4096|0;t[i+8>>1]=(k+h|0)>>>13;t[i+14>>1]=(k-h|0)>>>13;k=e+4096|0;t[i+10>>1]=(k+y|0)>>>13;t[i+12>>1]=(k-y|0)>>>13;c=m;return}function jn(e){e=e|0;var i=0,r=0,n=0,o=0,a=0;if(!e){a=-1;return a|0}f[e>>2]=0;i=dt(44)|0;if(!i){a=-1;return a|0}r=i+40|0;if((rt(r)|0)<<16>>16){a=-1;return a|0}n=i;o=7452;a=n+20|0;do{t[n>>1]=t[o>>1]|0;n=n+2|0;o=o+2|0}while((n|0)<(a|0));n=i+20|0;o=7452;a=n+20|0;do{t[n>>1]=t[o>>1]|0;n=n+2|0;o=o+2|0}while((n|0)<(a|0));nt(f[r>>2]|0)|0;f[e>>2]=i;a=0;return a|0}function Hn(e){e=e|0;var i=0,r=0,n=0;if(!e){n=-1;return n|0}i=e;r=7452;n=i+20|0;do{t[i>>1]=t[r>>1]|0;i=i+2|0;r=r+2|0}while((i|0)<(n|0));i=e+20|0;r=7452;n=i+20|0;do{t[i>>1]=t[r>>1]|0;i=i+2|0;r=r+2|0}while((i|0)<(n|0));nt(f[e+40>>2]|0)|0;n=0;return n|0}function Wn(e){e=e|0;var i=0;if(!e)return;i=f[e>>2]|0;if(!i)return;tt(i+40|0);vt(f[e>>2]|0);f[e>>2]=0;return}function qn(e,i,r,n,o,a,l,u){e=e|0;i=i|0;r=r|0;n=n|0;o=o|0;a=a|0;l=l|0;u=u|0;var s=0,h=0,w=0,d=0,v=0;v=c;c=c+64|0;d=v+44|0;s=v+24|0;h=v+4|0;w=v;if((i|0)==7){An(n+22|0,s,e,u);An(n+66|0,a,s,u);In(e,s,a,n,u);if((r|0)==8)n=6;else{et(f[e+40>>2]|0,s,a,h,d,f[l>>2]|0,u);Tn(e+20|0,h,d,o,u);o=(f[l>>2]|0)+10|0;n=7}}else{An(n+66|0,a,e,u);Fn(e,a,n,u);if((r|0)==8)n=6;else{$n(f[e+40>>2]|0,i,a,d,f[l>>2]|0,w,u);Ln(e+20|0,d,o,u);o=(f[l>>2]|0)+6|0;n=7}}if((n|0)==6){n=e;o=n+20|0;do{t[n>>1]=t[a>>1]|0;n=n+2|0;a=a+2|0}while((n|0)<(o|0));c=v;return}else if((n|0)==7){f[l>>2]=o;n=e;o=n+20|0;do{t[n>>1]=t[a>>1]|0;n=n+2|0;a=a+2|0}while((n|0)<(o|0));n=e+20|0;a=d;o=n+20|0;do{t[n>>1]=t[a>>1]|0;n=n+2|0;a=a+2|0}while((n|0)<(o|0));c=v;return}}function Gn(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var f=0,o=0,a=0;if(r<<16>>16>0)n=0;else return;do{o=t[e+(n<<1)>>1]|0;a=o>>8;f=t[7194+(a<<1)>>1]|0;t[i+(n<<1)>>1]=((I((t[7194+(a+1<<1)>>1]|0)-f|0,o&255)|0)>>>8)+f;n=n+1|0}while((n&65535)<<16>>16!=r<<16>>16);return}function Xn(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var f=0,o=0,a=0;n=(r<<16>>16)+-1|0;r=n&65535;if(r<<16>>16<=-1)return;f=63;a=i+(n<<1)|0;o=e+(n<<1)|0;while(1){e=t[o>>1]|0;i=f;while(1){n=i<<16>>16;f=t[7194+(n<<1)>>1]|0;if(e<<16>>16>f<<16>>16)i=i+-1<<16>>16;else break}t[a>>1]=(((I(t[7324+(n<<1)>>1]|0,(e<<16>>16)-(f<<16>>16)|0)|0)+2048|0)>>>12)+(n<<8);r=r+-1<<16>>16;if(r<<16>>16>-1){f=i;a=a+-2|0;o=o+-2|0}else break}return}function Vn(e,i,r){e=e|0;i=i|0;r=r|0;e=(I(i<<16>>16,e<<16>>16)|0)+16384>>15;e=e|0-(e&65536);if((e|0)<=32767){if((e|0)<-32768){f[r>>2]=1;e=-32768}}else{f[r>>2]=1;e=32767}return e&65535|0}function Kn(e){e=e|0;var i=0;e:do{if((e|0)!=0?(i=e-(e>>>31)|0,i=i>>31^i,(i&1073741824|0)==0):0){e=i;i=0;while(1){if(e&536870912){e=7;break}if(e&268435456){e=8;break}if(e&134217728){e=9;break}i=i+4<<16>>16;e=e<<4;if(e&1073741824)break e}if((e|0)==7){i=i|1;break}else if((e|0)==8){i=i|2;break}else if((e|0)==9){i=i|3;break}}else i=0}while(0);return i|0}function Yn(e){e=e|0;var i=0,r=0;if(!(e<<16>>16)){r=0;return r|0}i=(e&65535)-((e&65535)>>>15&65535)|0;i=(i<<16>>31^i)<<16;e=i>>16;if(!(e&16384)){r=i;i=0}else{r=0;return r|0}while(1){if(e&8192){e=i;r=7;break}if(e&4096){e=i;r=8;break}if(e&2048){e=i;r=9;break}i=i+4<<16>>16;r=r<<4;e=r>>16;if(e&16384){e=i;r=10;break}}if((r|0)==7){r=e|1;return r|0}else if((r|0)==8){r=e|2;return r|0}else if((r|0)==9){r=e|3;return r|0}else if((r|0)==10)return e|0;return 0}function Qn(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,o=0,l=0;i=i<<16>>16;if((i&134217727|0)==33554432){f[r>>2]=1;i=2147483647}else i=i<<6;n=i>>>16&31;l=t[7792+(n<<1)>>1]|0;o=l<<16;i=I(l-(a[7792+(n+1<<1)>>1]|0)<<16>>16,i>>>1&32767)|0;if((i|0)==1073741824){f[r>>2]=1;n=2147483647}else n=i<<1;i=o-n|0;if(((i^o)&(n^o)|0)>=0){l=i;e=e&65535;e=30-e|0;e=e&65535;r=xn(l,e,r)|0;return r|0}f[r>>2]=1;l=(l>>>15&1)+2147483647|0;e=e&65535;e=30-e|0;e=e&65535;r=xn(l,e,r)|0;return r|0}function Zn(e,i,r,n,f,o){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;o=o|0;var a=0,l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0;d=c;c=c+48|0;w=d;h=0-(r&65535)|0;h=f<<16>>16==0?h:h<<1&131070;r=h&65535;h=(r<<16>>16<0?h+6|0:h)<<16>>16;o=6-h|0;t[w>>1]=t[7858+(h<<1)>>1]|0;t[w+2>>1]=t[7858+(o<<1)>>1]|0;t[w+4>>1]=t[7858+(h+6<<1)>>1]|0;t[w+6>>1]=t[7858+(o+6<<1)>>1]|0;t[w+8>>1]=t[7858+(h+12<<1)>>1]|0;t[w+10>>1]=t[7858+(o+12<<1)>>1]|0;t[w+12>>1]=t[7858+(h+18<<1)>>1]|0;t[w+14>>1]=t[7858+(o+18<<1)>>1]|0;t[w+16>>1]=t[7858+(h+24<<1)>>1]|0;t[w+18>>1]=t[7858+(o+24<<1)>>1]|0;t[w+20>>1]=t[7858+(h+30<<1)>>1]|0;t[w+22>>1]=t[7858+(o+30<<1)>>1]|0;t[w+24>>1]=t[7858+(h+36<<1)>>1]|0;t[w+26>>1]=t[7858+(o+36<<1)>>1]|0;t[w+28>>1]=t[7858+(h+42<<1)>>1]|0;t[w+30>>1]=t[7858+(o+42<<1)>>1]|0;t[w+32>>1]=t[7858+(h+48<<1)>>1]|0;t[w+34>>1]=t[7858+(o+48<<1)>>1]|0;t[w+36>>1]=t[7858+(h+54<<1)>>1]|0;t[w+38>>1]=t[7858+(o+54<<1)>>1]|0;o=n<<16>>16>>>1&65535;if(!(o<<16>>16)){c=d;return}h=e+((r<<16>>16>>15<<16>>16)-(i<<16>>16)<<1)|0;while(1){s=h+2|0;a=t[s>>1]|0;i=a;n=h;l=5;u=w;f=16384;r=16384;while(1){b=t[u>>1]|0;k=(I(b,i<<16>>16)|0)+r|0;v=t[s+-2>>1]|0;r=(I(v,b)|0)+f|0;b=n;n=n+4|0;m=t[u+2>>1]|0;r=r+(I(m,a<<16>>16)|0)|0;f=t[n>>1]|0;m=k+(I(f,m)|0)|0;s=s+-4|0;k=t[u+4>>1]|0;v=m+(I(k,v)|0)|0;i=t[s>>1]|0;k=r+(I(i<<16>>16,k)|0)|0;r=t[u+6>>1]|0;f=k+(I(r,f)|0)|0;a=t[b+6>>1]|0;r=v+(I(a<<16>>16,r)|0)|0;if(l<<16>>16<=1)break;else{l=l+-1<<16>>16;u=u+8|0}}t[e>>1]=f>>>15;t[e+2>>1]=r>>>15;o=o+-1<<16>>16;if(!(o<<16>>16))break;else{h=h+4|0;e=e+4|0}}c=d;return}function $n(e,i,r,n,f,o,l){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;o=o|0;l=l|0;var u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0;D=c;c=c+144|0;y=D+120|0;S=D+100|0;R=D+80|0;M=D+60|0;_=D+40|0;b=D+20|0;k=D;Xn(r,y,10,l);Un(y,S,l);if((i|0)==8){t[o>>1]=0;s=2147483647;m=0;while(1){w=m*10|0;r=0;h=0;do{A=(a[7980+(h+w<<1)>>1]|0)+(a[8140+(h<<1)>>1]|0)|0;t[k+(h<<1)>>1]=A;A=(a[y+(h<<1)>>1]|0)-(A&65535)|0;t[b+(h<<1)>>1]=A;A=A<<16;r=(I(A>>15,A>>16)|0)+r|0;h=h+1|0}while((h|0)!=10);if((r|0)<(s|0)){p=M;v=b;d=p+20|0;do{t[p>>1]=t[v>>1]|0;p=p+2|0;v=v+2|0}while((p|0)<(d|0));p=R;v=k;d=p+20|0;do{t[p>>1]=t[v>>1]|0;p=p+2|0;v=v+2|0}while((p|0)<(d|0));p=e;v=7980+(w<<1)|0;d=p+20|0;do{t[p>>1]=t[v>>1]|0;p=p+2|0;v=v+2|0}while((p|0)<(d|0));t[o>>1]=m}else r=s;m=m+1|0;if((m|0)==8)break;else s=r}}else{r=0;do{A=I(t[8160+(r<<1)>>1]|0,t[e+(r<<1)>>1]|0)|0;A=(A>>>15)+(a[8140+(r<<1)>>1]|0)|0;t[R+(r<<1)>>1]=A;t[M+(r<<1)>>1]=(a[y+(r<<1)>>1]|0)-A;r=r+1|0}while((r|0)!=10)}do{if(i>>>0>=2){A=M+2|0;g=M+4|0;E=a[M>>1]|0;p=t[S>>1]<<1;y=a[A>>1]|0;b=t[S+2>>1]<<1;v=a[g>>1]|0;d=t[S+4>>1]<<1;if((i|0)==5){k=2147483647;o=0;r=0;m=17908;while(1){h=(I(E-(a[m>>1]|0)<<16>>16,p)|0)>>16;h=I(h,h)|0;w=(I(y-(a[m+2>>1]|0)<<16>>16,b)|0)>>16;h=(I(w,w)|0)+h|0;w=(I(v-(a[m+4>>1]|0)<<16>>16,d)|0)>>16;w=h+(I(w,w)|0)|0;h=(w|0)<(k|0);r=h?o:r;o=o+1<<16>>16;if(o<<16>>16>=512)break;else{k=h?w:k;m=m+6|0}}w=(r<<16>>16)*3|0;t[M>>1]=t[17908+(w<<1)>>1]|0;t[A>>1]=t[17908+(w+1<<1)>>1]|0;t[g>>1]=t[17908+(w+2<<1)>>1]|0;t[f>>1]=r;w=M+6|0;h=M+8|0;E=M+10|0;m=a[w>>1]|0;o=t[S+6>>1]<<1;k=a[h>>1]|0;b=t[S+8>>1]<<1;v=a[E>>1]|0;d=t[S+10>>1]<<1;u=2147483647;y=0;r=0;p=9716;while(1){s=(I(o,m-(a[p>>1]|0)<<16>>16)|0)>>16;s=I(s,s)|0;i=(I(b,k-(a[p+2>>1]|0)<<16>>16)|0)>>16;s=(I(i,i)|0)+s|0;i=(I(d,v-(a[p+4>>1]|0)<<16>>16)|0)>>16;i=s+(I(i,i)|0)|0;s=(i|0)<(u|0);r=s?y:r;y=y+1<<16>>16;if(y<<16>>16>=512)break;else{u=s?i:u;p=p+6|0}}u=(r<<16>>16)*3|0;t[w>>1]=t[9716+(u<<1)>>1]|0;t[h>>1]=t[9716+(u+1<<1)>>1]|0;t[E>>1]=t[9716+(u+2<<1)>>1]|0;t[f+2>>1]=r;u=M+12|0;t[f+4>>1]=Jn(u,12788,S+12|0,512)|0;y=A;m=g;r=E;s=M;break}else{k=2147483647;o=0;r=0;m=8180;while(1){h=(I(E-(a[m>>1]|0)<<16>>16,p)|0)>>16;h=I(h,h)|0;w=(I(y-(a[m+2>>1]|0)<<16>>16,b)|0)>>16;h=(I(w,w)|0)+h|0;w=(I(v-(a[m+4>>1]|0)<<16>>16,d)|0)>>16;w=h+(I(w,w)|0)|0;h=(w|0)<(k|0);r=h?o:r;o=o+1<<16>>16;if(o<<16>>16>=256)break;else{k=h?w:k;m=m+6|0}}w=(r<<16>>16)*3|0;t[M>>1]=t[8180+(w<<1)>>1]|0;t[A>>1]=t[8180+(w+1<<1)>>1]|0;t[g>>1]=t[8180+(w+2<<1)>>1]|0;t[f>>1]=r;w=M+6|0;h=M+8|0;E=M+10|0;m=a[w>>1]|0;o=t[S+6>>1]<<1;k=a[h>>1]|0;b=t[S+8>>1]<<1;v=a[E>>1]|0;d=t[S+10>>1]<<1;u=2147483647;y=0;r=0;p=9716;while(1){s=(I(o,m-(a[p>>1]|0)<<16>>16)|0)>>16;s=I(s,s)|0;i=(I(b,k-(a[p+2>>1]|0)<<16>>16)|0)>>16;s=(I(i,i)|0)+s|0;i=(I(d,v-(a[p+4>>1]|0)<<16>>16)|0)>>16;i=s+(I(i,i)|0)|0;s=(i|0)<(u|0);r=s?y:r;y=y+1<<16>>16;if(y<<16>>16>=512)break;else{u=s?i:u;p=p+6|0}}u=(r<<16>>16)*3|0;t[w>>1]=t[9716+(u<<1)>>1]|0;t[h>>1]=t[9716+(u+1<<1)>>1]|0;t[E>>1]=t[9716+(u+2<<1)>>1]|0;t[f+2>>1]=r;u=M+12|0;t[f+4>>1]=Jn(u,12788,S+12|0,512)|0;y=A;m=g;r=E;s=M;break}}else{g=M+2|0;A=M+4|0;w=a[M>>1]|0;h=t[S>>1]<<1;s=a[g>>1]|0;u=t[S+2>>1]<<1;i=a[A>>1]|0;d=t[S+4>>1]<<1;k=2147483647;o=0;r=0;m=8180;while(1){b=(I(h,w-(a[m>>1]|0)<<16>>16)|0)>>16;b=I(b,b)|0;v=(I(u,s-(a[m+2>>1]|0)<<16>>16)|0)>>16;b=(I(v,v)|0)+b|0;v=(I(d,i-(a[m+4>>1]|0)<<16>>16)|0)>>16;v=b+(I(v,v)|0)|0;b=(v|0)<(k|0);r=b?o:r;o=o+1<<16>>16;if(o<<16>>16>=256)break;else{k=b?v:k;m=m+6|0}}w=(r<<16>>16)*3|0;t[M>>1]=t[8180+(w<<1)>>1]|0;t[g>>1]=t[8180+(w+1<<1)>>1]|0;t[A>>1]=t[8180+(w+2<<1)>>1]|0;t[f>>1]=r;w=M+6|0;h=M+8|0;E=M+10|0;m=a[w>>1]|0;o=t[S+6>>1]<<1;k=a[h>>1]|0;b=t[S+8>>1]<<1;v=a[E>>1]|0;d=t[S+10>>1]<<1;u=2147483647;y=0;r=0;p=9716;while(1){s=(I(o,m-(a[p>>1]|0)<<16>>16)|0)>>16;s=I(s,s)|0;i=(I(b,k-(a[p+2>>1]|0)<<16>>16)|0)>>16;s=(I(i,i)|0)+s|0;i=(I(d,v-(a[p+4>>1]|0)<<16>>16)|0)>>16;i=s+(I(i,i)|0)|0;s=(i|0)<(u|0);r=s?y:r;y=y+1<<16>>16;if(y<<16>>16>=256)break;else{u=s?i:u;p=p+12|0}}u=(r<<16>>16)*6|0;t[w>>1]=t[9716+(u<<1)>>1]|0;t[h>>1]=t[9716+((u|1)<<1)>>1]|0;t[E>>1]=t[9716+(u+2<<1)>>1]|0;t[f+2>>1]=r;u=M+12|0;t[f+4>>1]=Jn(u,16884,S+12|0,128)|0;y=g;m=A;r=E;s=M}}while(0);p=e;v=M;d=p+20|0;do{t[p>>1]=t[v>>1]|0;p=p+2|0;v=v+2|0}while((p|0)<(d|0));t[_>>1]=(a[R>>1]|0)+(a[s>>1]|0);t[_+2>>1]=(a[R+2>>1]|0)+(a[y>>1]|0);t[_+4>>1]=(a[R+4>>1]|0)+(a[m>>1]|0);t[_+6>>1]=(a[R+6>>1]|0)+(a[w>>1]|0);t[_+8>>1]=(a[R+8>>1]|0)+(a[h>>1]|0);t[_+10>>1]=(a[R+10>>1]|0)+(a[r>>1]|0);t[_+12>>1]=(a[R+12>>1]|0)+(a[u>>1]|0);t[_+14>>1]=(a[R+14>>1]|0)+(a[M+14>>1]|0);t[_+16>>1]=(a[R+16>>1]|0)+(a[M+16>>1]|0);t[_+18>>1]=(a[R+18>>1]|0)+(a[M+18>>1]|0);ft(_,205,10,l);Gn(_,n,10,l);c=D;return}function Jn(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var f=0,o=0,l=0,u=0,s=0,c=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0;y=e+2|0;p=e+4|0;E=e+6|0;if(n<<16>>16>0){h=a[e>>1]|0;w=t[r>>1]<<1;d=a[y>>1]|0;v=t[r+2>>1]<<1;b=a[p>>1]|0;k=t[r+4>>1]<<1;m=a[E>>1]|0;f=t[r+6>>1]<<1;u=2147483647;s=0;r=0;c=i;while(1){o=(I(w,h-(a[c>>1]|0)<<16>>16)|0)>>16;o=I(o,o)|0;l=(I(v,d-(a[c+2>>1]|0)<<16>>16)|0)>>16;o=(I(l,l)|0)+o|0;l=(I(k,b-(a[c+4>>1]|0)<<16>>16)|0)>>16;l=o+(I(l,l)|0)|0;o=(I(f,m-(a[c+6>>1]|0)<<16>>16)|0)>>16;o=l+(I(o,o)|0)|0;l=(o|0)<(u|0);r=l?s:r;s=s+1<<16>>16;if(s<<16>>16>=n<<16>>16)break;else{u=l?o:u;c=c+8|0}}}else r=0;n=r<<16>>16<<2;m=n|1;t[e>>1]=t[i+(n<<1)>>1]|0;t[y>>1]=t[i+(m<<1)>>1]|0;t[p>>1]=t[i+(m+1<<1)>>1]|0;t[E>>1]=t[i+((n|3)<<1)>>1]|0;return r|0}function et(e,i,r,n,f,o,l){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;o=o|0;l=l|0;var u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,L=0,F=0,C=0,P=0,B=0;C=c;c=c+192|0;h=C+160|0;s=C+140|0;D=C+120|0;O=C+100|0;N=C+80|0;T=C+60|0;u=C+40|0;L=C+20|0;F=C;Xn(i,h,10,l);Xn(r,s,10,l);Un(h,D,l);Un(s,O,l);w=0;r=N;i=T;d=u;while(1){M=(((t[e+(w<<1)>>1]|0)*21299|0)>>>15)+(a[20980+(w<<1)>>1]|0)|0;t[r>>1]=M;t[i>>1]=(a[h>>1]|0)-M;t[d>>1]=(a[s>>1]|0)-M;w=w+1|0;if((w|0)==10)break;else{h=h+2|0;s=s+2|0;r=r+2|0;i=i+2|0;d=d+2|0}}t[o>>1]=it(T,u,21e3,t[D>>1]|0,t[D+2>>1]|0,t[O>>1]|0,t[O+2>>1]|0,128)|0;t[o+2>>1]=it(T+4|0,u+4|0,22024,t[D+4>>1]|0,t[D+6>>1]|0,t[O+4>>1]|0,t[O+6>>1]|0,256)|0;S=T+8|0;_=u+8|0;R=T+10|0;M=u+10|0;r=t[S>>1]|0;v=t[D+8>>1]<<1;b=t[R>>1]|0;k=t[D+10>>1]<<1;m=t[_>>1]|0;y=t[O+8>>1]<<1;p=t[M>>1]|0;E=t[O+10>>1]<<1;s=2147483647;g=0;d=0;A=24072;i=0;while(1){h=t[A>>1]|0;w=(I(r-h<<16>>16,v)|0)>>16;w=I(w,w)|0;h=(I(h+r<<16>>16,v)|0)>>16;h=I(h,h)|0;P=t[A+2>>1]|0;B=(I(b-P<<16>>16,k)|0)>>16;w=(I(B,B)|0)+w|0;P=(I(P+b<<16>>16,k)|0)>>16;h=(I(P,P)|0)+h|0;if((w|0)<(s|0)|(h|0)<(s|0)){B=t[A+4>>1]|0;P=(I(m-B<<16>>16,y)|0)>>16;P=(I(P,P)|0)+w|0;B=(I(B+m<<16>>16,y)|0)>>16;B=(I(B,B)|0)+h|0;h=t[A+6>>1]|0;w=(I(p-h<<16>>16,E)|0)>>16;w=P+(I(w,w)|0)|0;h=(I(h+p<<16>>16,E)|0)>>16;h=B+(I(h,h)|0)|0;B=(w|0)<(s|0);w=B?w:s;P=(h|0)<(w|0);w=P?h:w;d=B|P?g:d;i=P?1:B?0:i}else w=s;g=g+1<<16>>16;if(g<<16>>16>=256)break;else{s=w;A=A+8|0}}w=d<<16>>16;h=w<<2;d=h|1;s=24072+(d<<1)|0;r=t[24072+(h<<1)>>1]|0;if(!(i<<16>>16)){t[S>>1]=r;t[R>>1]=t[s>>1]|0;t[_>>1]=t[24072+(d+1<<1)>>1]|0;t[M>>1]=t[24072+((h|3)<<1)>>1]|0;i=w<<1}else{t[S>>1]=0-(r&65535);t[R>>1]=0-(a[s>>1]|0);t[_>>1]=0-(a[24072+(d+1<<1)>>1]|0);t[M>>1]=0-(a[24072+((h|3)<<1)>>1]|0);i=w<<1&65534|1}t[o+4>>1]=i;t[o+6>>1]=it(T+12|0,u+12|0,26120,t[D+12>>1]|0,t[D+14>>1]|0,t[O+12>>1]|0,t[O+14>>1]|0,256)|0;t[o+8>>1]=it(T+16|0,u+16|0,28168,t[D+16>>1]|0,t[D+18>>1]|0,t[O+16>>1]|0,t[O+18>>1]|0,64)|0;s=0;h=L;w=F;r=N;i=T;while(1){P=a[r>>1]|0;t[h>>1]=P+(a[i>>1]|0);B=t[u>>1]|0;t[w>>1]=P+(B&65535);t[e+(s<<1)>>1]=B;s=s+1|0;if((s|0)==10)break;else{h=h+2|0;w=w+2|0;r=r+2|0;i=i+2|0;u=u+2|0}}ft(L,205,10,l);ft(F,205,10,l);Gn(L,n,10,l);Gn(F,f,10,l);c=C;return}function it(e,i,r,n,f,o,a,l){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;o=o|0;a=a|0;l=l|0;var u=0,s=0,c=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0;d=t[e>>1]|0;p=e+2|0;b=t[p>>1]|0;m=t[i>>1]|0;E=i+2|0;y=t[E>>1]|0;if(l<<16>>16>0){w=n<<16>>16<<1;h=f<<16>>16<<1;c=o<<16>>16<<1;f=a<<16>>16<<1;o=2147483647;u=0;n=0;s=r;while(1){a=(I(w,d-(t[s>>1]|0)|0)|0)>>16;a=I(a,a)|0;if(((a|0)<(o|0)?(v=(I(h,b-(t[s+2>>1]|0)|0)|0)>>16,v=(I(v,v)|0)+a|0,(v|0)<(o|0)):0)?(k=(I(c,m-(t[s+4>>1]|0)|0)|0)>>16,k=(I(k,k)|0)+v|0,(k|0)<(o|0)):0){a=(I(f,y-(t[s+6>>1]|0)|0)|0)>>16;a=(I(a,a)|0)+k|0;g=(a|0)<(o|0);a=g?a:o;n=g?u:n}else a=o;u=u+1<<16>>16;if(u<<16>>16>=l<<16>>16)break;else{o=a;s=s+8|0}}}else n=0;g=n<<16>>16<<2;l=g|1;t[e>>1]=t[r+(g<<1)>>1]|0;t[p>>1]=t[r+(l<<1)>>1]|0;t[i>>1]=t[r+(l+1<<1)>>1]|0;t[E>>1]=t[r+((g|3)<<1)>>1]|0;return n|0}function rt(e){e=e|0;var i=0,r=0,n=0;if(!e){n=-1;return n|0}f[e>>2]=0;i=dt(20)|0;if(!i){n=-1;return n|0}r=i;n=r+20|0;do{t[r>>1]=0;r=r+2|0}while((r|0)<(n|0));f[e>>2]=i;n=0;return n|0}function nt(e){e=e|0;var i=0;if(!e){i=-1;return i|0}i=e+20|0;do{t[e>>1]=0;e=e+2|0}while((e|0)<(i|0));i=0;return i|0}function tt(e){e=e|0;var i=0;if(!e)return;i=f[e>>2]|0;if(!i)return;vt(i);f[e>>2]=0;return}function ft(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var f=0,o=0,a=0;if(r<<16>>16<=0)return;f=i<<16>>16;o=i&65535;a=0;while(1){n=t[e>>1]|0;if(n<<16>>16<i<<16>>16){t[e>>1]=i;n=(i<<16>>16)+f|0}else n=(n&65535)+o|0;a=a+1<<16>>16;if(a<<16>>16>=r<<16>>16)break;else{i=n&65535;e=e+2|0}}return}function ot(e,i,r,n){e=e|0;i=i|0;r=r|0;n=n|0;var f=0,o=0,a=0,l=0,u=0,s=0,c=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0;f=n<<16>>16;n=f>>>2&65535;if(!(n<<16>>16))return;w=f+-1|0;y=e+20|0;v=i+(f+-4<<1)|0;b=i+(f+-3<<1)|0;k=i+(f+-2<<1)|0;m=i+(w<<1)|0;d=i+(f+-11<<1)|0;w=r+(w<<1)|0;while(1){i=t[y>>1]|0;a=5;l=y;u=d;s=d+-2|0;c=d+-4|0;h=d+-6|0;o=2048;e=2048;f=2048;r=2048;while(1){o=(I(t[u>>1]|0,i)|0)+o|0;e=(I(t[s>>1]|0,i)|0)+e|0;f=(I(t[c>>1]|0,i)|0)+f|0;i=(I(t[h>>1]|0,i)|0)+r|0;r=t[l+-2>>1]|0;o=o+(I(t[u+2>>1]|0,r)|0)|0;e=e+(I(t[s+2>>1]|0,r)|0)|0;f=f+(I(t[c+2>>1]|0,r)|0)|0;l=l+-4|0;r=i+(I(t[h+2>>1]|0,r)|0)|0;a=a+-1<<16>>16;i=t[l>>1]|0;if(!(a<<16>>16))break;else{u=u+4|0;s=s+4|0;c=c+4|0;h=h+4|0}}u=(I(t[m>>1]|0,i)|0)+o|0;s=(I(t[k>>1]|0,i)|0)+e|0;c=(I(t[b>>1]|0,i)|0)+f|0;h=(I(t[v>>1]|0,i)|0)+r|0;t[w>>1]=u>>>12;t[w+-2>>1]=s>>>12;t[w+-4>>1]=c>>>12;t[w+-6>>1]=h>>>12;n=n+-1<<16>>16;if(!(n<<16>>16))break;else{v=v+-8|0;b=b+-8|0;k=k+-8|0;m=m+-8|0;d=d+-8|0;w=w+-8|0}}return}function at(e,i){e=e|0;i=i|0;var r=0;r=e+32768|0;if((e|0)>-1&(r^e|0)<0){f[i>>2]=1;r=(e>>>31)+2147483647|0}return r>>>16&65535|0}function lt(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,t=0;n=i<<16>>16;if(!(i<<16>>16))return e|0;if(i<<16>>16>0){e=e<<16>>16>>(i<<16>>16>15?15:n)&65535;return e|0}t=0-n|0;i=e<<16>>16;t=(t&65535)<<16>>16>15?15:t<<16>>16;n=i<<t;if((n<<16>>16>>t|0)==(i|0)){t=n&65535;return t|0}f[r>>2]=1;t=e<<16>>16>0?32767:-32768;return t|0}function ut(e,i,r){e=e|0;i=i|0;r=r|0;if(i<<16>>16>15){i=0;return i|0}r=lt(e,i,r)|0;if(i<<16>>16>0)return r+((1<<(i<<16>>16)+-1&e<<16>>16|0)!=0&1)<<16>>16|0;else{i=r;return i|0}return 0}function st(e,i,r){e=e|0;i=i|0;r=r|0;var n=0,o=0,l=0;if((e|0)<1){t[i>>1]=0;r=0;return r|0}o=(Kn(e)|0)&65534;l=o&65535;o=o<<16>>16;if(l<<16>>16>0){n=e<<o;if((n>>o|0)!=(e|0))n=e>>31^2147483647}else{o=0-o<<16;if((o|0)<2031616)n=e>>(o>>16);else n=0}t[i>>1]=l;i=n>>>25&63;i=i>>>0>15?i+-16|0:i;l=t[30216+(i<<1)>>1]|0;e=l<<16;n=I(l-(a[30216+(i+1<<1)>>1]|0)<<16>>16,n>>>10&32767)|0;if((n|0)==1073741824){f[r>>2]=1;o=2147483647}else o=n<<1;n=e-o|0;if(((n^e)&(o^e)|0)>=0){r=n;return r|0}f[r>>2]=1;r=(l>>>15&1)+2147483647|0;return r|0}function ct(e,i,r){e=e|0;i=i|0;r=r|0;e=(e<<16>>16)-(i<<16>>16)|0;if((e+32768|0)>>>0<=65535){r=e;r=r&65535;return r|0}f[r>>2]=1;r=(e|0)>32767?32767:-32768;r=r&65535;return r|0}function ht(e,i,r,n,f,o){e=e|0;i=i|0;r=r|0;n=n|0;f=f|0;o=o|0;var a=0,l=0,u=0,s=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0;R=c;c=c+48|0;d=R;u=d;a=f;l=u+20|0;do{t[u>>1]=t[a>>1]|0;u=u+2|0;a=a+2|0}while((u|0)<(l|0));w=d+18|0;m=e+2|0;y=e+4|0;v=i+20|0;p=e+6|0;E=e+8|0;g=e+10|0;A=e+12|0;S=e+14|0;_=e+16|0;b=e+18|0;k=e+20|0;l=t[w>>1]|0;a=5;s=i;h=r;u=d+20|0;while(1){O=t[e>>1]|0;D=(I(O,t[s>>1]|0)|0)+2048|0;O=(I(t[s+2>>1]|0,O)|0)+2048|0;d=l<<16>>16;D=D-(I(d,t[m>>1]|0)|0)|0;M=t[y>>1]|0;d=O-(I(d,M)|0)|0;O=t[w+-2>>1]|0;M=D-(I(O,M)|0)|0;D=t[p>>1]|0;O=d-(I(D,O)|0)|0;d=t[w+-4>>1]|0;D=M-(I(d,D)|0)|0;M=t[E>>1]|0;d=O-(I(M,d)|0)|0;O=t[w+-6>>1]|0;M=D-(I(O,M)|0)|0;D=t[g>>1]|0;O=d-(I(O,D)|0)|0;d=t[w+-8>>1]|0;D=M-(I(d,D)|0)|0;M=t[A>>1]|0;d=O-(I(M,d)|0)|0;O=t[w+-10>>1]|0;M=D-(I(O,M)|0)|0;D=t[S>>1]|0;O=d-(I(D,O)|0)|0;d=t[w+-12>>1]|0;D=M-(I(d,D)|0)|0;M=t[_>>1]|0;d=O-(I(d,M)|0)|0;O=t[w+-14>>1]|0;M=D-(I(O,M)|0)|0;D=t[b>>1]|0;O=d-(I(D,O)|0)|0;d=t[w+-16>>1]|0;D=M-(I(d,D)|0)|0;M=t[k>>1]|0;d=O-(I(M,d)|0)|0;M=D-(I(t[w+-18>>1]|0,M)|0)|0;M=(M+134217728|0)>>>0<268435455?M>>>12&65535:(M|0)>134217727?32767:-32768;d=d-(I(t[m>>1]|0,M<<16>>16)|0)|0;w=u+2|0;t[u>>1]=M;t[h>>1]=M;l=(d+134217728|0)>>>0<268435455?d>>>12&65535:(d|0)>134217727?32767:-32768;t[w>>1]=l;t[h+2>>1]=l;a=a+-1<<16>>16;if(!(a<<16>>16))break;else{s=s+4|0;h=h+4|0;u=u+4|0}}n=(n<<16>>16)+-10|0;u=n>>>1&65535;if(u<<16>>16){d=r+18|0;l=i+16|0;w=t[d>>1]|0;s=v;a=r+20|0;while(1){M=t[e>>1]|0;h=(I(M,t[s>>1]|0)|0)+2048|0;M=(I(t[l+6>>1]|0,M)|0)+2048|0;l=t[m>>1]|0;D=w<<16>>16;h=h-(I(D,l)|0)|0;O=t[y>>1]|0;D=M-(I(D,O)|0)|0;M=t[d+-2>>1]|0;O=h-(I(M,O)|0)|0;h=t[p>>1]|0;M=D-(I(h,M)|0)|0;D=t[d+-4>>1]|0;h=O-(I(D,h)|0)|0;O=t[E>>1]|0;D=M-(I(O,D)|0)|0;M=t[d+-6>>1]|0;O=h-(I(M,O)|0)|0;h=t[g>>1]|0;M=D-(I(M,h)|0)|0;D=t[d+-8>>1]|0;h=O-(I(D,h)|0)|0;O=t[A>>1]|0;D=M-(I(O,D)|0)|0;M=t[d+-10>>1]|0;O=h-(I(M,O)|0)|0;h=t[S>>1]|0;M=D-(I(h,M)|0)|0;D=t[d+-12>>1]|0;h=O-(I(D,h)|0)|0;O=t[_>>1]|0;D=M-(I(D,O)|0)|0;M=t[d+-14>>1]|0;O=h-(I(M,O)|0)|0;h=t[b>>1]|0;M=D-(I(h,M)|0)|0;D=t[d+-16>>1]|0;h=O-(I(D,h)|0)|0;O=t[k>>1]|0;D=M-(I(O,D)|0)|0;O=h-(I(t[d+-18>>1]|0,O)|0)|0;h=s+4|0;O=(O+134217728|0)>>>0<268435455?O>>>12&65535:(O|0)>134217727?32767:-32768;l=D-(I(l,O<<16>>16)|0)|0;d=a+2|0;t[a>>1]=O;do{if((l+134217728|0)>>>0>=268435455){a=a+4|0;if((l|0)>134217727){t[d>>1]=32767;l=32767;break}else{t[d>>1]=-32768;l=-32768;break}}else{l=l>>>12&65535;t[d>>1]=l;a=a+4|0}}while(0);u=u+-1<<16>>16;if(!(u<<16>>16))break;else{O=s;w=l;s=h;l=O}}}if(!(o<<16>>16)){c=R;return}u=f;a=r+(n<<1)|0;l=u+20|0;do{t[u>>1]=t[a>>1]|0;u=u+2|0;a=a+2|0}while((u|0)<(l|0));c=R;return}function wt(e,i,r){e=e|0;i=i|0;r=r|0;t[r>>1]=t[e>>1]|0;t[r+2>>1]=((I(t[i>>1]|0,t[e+2>>1]|0)|0)+16384|0)>>>15;t[r+4>>1]=((I(t[i+2>>1]|0,t[e+4>>1]|0)|0)+16384|0)>>>15;t[r+6>>1]=((I(t[i+4>>1]|0,t[e+6>>1]|0)|0)+16384|0)>>>15;t[r+8>>1]=((I(t[i+6>>1]|0,t[e+8>>1]|0)|0)+16384|0)>>>15;t[r+10>>1]=((I(t[i+8>>1]|0,t[e+10>>1]|0)|0)+16384|0)>>>15;t[r+12>>1]=((I(t[i+10>>1]|0,t[e+12>>1]|0)|0)+16384|0)>>>15;t[r+14>>1]=((I(t[i+12>>1]|0,t[e+14>>1]|0)|0)+16384|0)>>>15;t[r+16>>1]=((I(t[i+14>>1]|0,t[e+16>>1]|0)|0)+16384|0)>>>15;t[r+18>>1]=((I(t[i+16>>1]|0,t[e+18>>1]|0)|0)+16384|0)>>>15;t[r+20>>1]=((I(t[i+18>>1]|0,t[e+20>>1]|0)|0)+16384|0)>>>15;return}function dt(e){e=e|0;var i=0,r=0,n=0,t=0,o=0,a=0,l=0,u=0,s=0,c=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0,D=0,O=0,N=0,T=0,I=0,L=0,F=0,C=0,P=0,B=0,U=0,z=0,H=0,W=0,q=0,G=0,X=0,V=0,K=0,Y=0;do{if(e>>>0<245){m=e>>>0<11?16:e+11&-8;e=m>>>3;h=f[26]|0;u=h>>>e;if(u&3){n=(u&1^1)+e|0;i=n<<1;r=144+(i<<2)|0;i=144+(i+2<<2)|0;t=f[i>>2]|0;o=t+8|0;a=f[o>>2]|0;do{if((r|0)==(a|0))f[26]=h&~(1<<n);else{if(a>>>0>=(f[30]|0)>>>0?(c=a+12|0,(f[c>>2]|0)==(t|0)):0){f[c>>2]=r;f[i>>2]=a;break}x()}}while(0);K=n<<3;f[t+4>>2]=K|3;K=t+(K|4)|0;f[K>>2]=f[K>>2]|1;break}i=f[28]|0;if(m>>>0>i>>>0){if(u){t=2<<e;t=u<<e&(t|0-t);t=(t&0-t)+-1|0;o=t>>>12&16;t=t>>>o;n=t>>>5&8;t=t>>>n;r=t>>>2&4;t=t>>>r;a=t>>>1&2;t=t>>>a;l=t>>>1&1;l=(n|o|r|a|l)+(t>>>l)|0;t=l<<1;a=144+(t<<2)|0;t=144+(t+2<<2)|0;r=f[t>>2]|0;o=r+8|0;n=f[o>>2]|0;do{if((a|0)==(n|0)){f[26]=h&~(1<<l);w=i}else{if(n>>>0>=(f[30]|0)>>>0?(s=n+12|0,(f[s>>2]|0)==(r|0)):0){f[s>>2]=a;f[t>>2]=n;w=f[28]|0;break}x()}}while(0);K=l<<3;i=K-m|0;f[r+4>>2]=m|3;u=r+m|0;f[r+(m|4)>>2]=i|1;f[r+K>>2]=i;if(w){r=f[31]|0;n=w>>>3;a=n<<1;l=144+(a<<2)|0;t=f[26]|0;n=1<<n;if(t&n){t=144+(a+2<<2)|0;a=f[t>>2]|0;if(a>>>0<(f[30]|0)>>>0)x();else{v=t;b=a}}else{f[26]=t|n;v=144+(a+2<<2)|0;b=l}f[v>>2]=r;f[b+12>>2]=r;f[r+8>>2]=b;f[r+12>>2]=l}f[28]=i;f[31]=u;break}e=f[27]|0;if(e){t=(e&0-e)+-1|0;V=t>>>12&16;t=t>>>V;X=t>>>5&8;t=t>>>X;K=t>>>2&4;t=t>>>K;a=t>>>1&2;t=t>>>a;u=t>>>1&1;u=f[408+((X|V|K|a|u)+(t>>>u)<<2)>>2]|0;t=(f[u+4>>2]&-8)-m|0;a=u;while(1){l=f[a+16>>2]|0;if(!l){l=f[a+20>>2]|0;if(!l){i=t;break}}a=(f[l+4>>2]&-8)-m|0;K=a>>>0<t>>>0;t=K?a:t;a=l;u=K?l:u}e=f[30]|0;if(u>>>0>=e>>>0?(E=u+m|0,u>>>0<E>>>0):0){n=f[u+24>>2]|0;l=f[u+12>>2]|0;do{if((l|0)==(u|0)){a=u+20|0;l=f[a>>2]|0;if(!l){a=u+16|0;l=f[a>>2]|0;if(!l){y=0;break}}while(1){o=l+20|0;t=f[o>>2]|0;if(t){l=t;a=o;continue}o=l+16|0;t=f[o>>2]|0;if(!t)break;else{l=t;a=o}}if(a>>>0<e>>>0)x();else{f[a>>2]=0;y=l;break}}else{a=f[u+8>>2]|0;if((a>>>0>=e>>>0?(r=a+12|0,(f[r>>2]|0)==(u|0)):0)?(d=l+8|0,(f[d>>2]|0)==(u|0)):0){f[r>>2]=l;f[d>>2]=a;y=l;break}x()}}while(0);do{if(n){a=f[u+28>>2]|0;o=408+(a<<2)|0;if((u|0)==(f[o>>2]|0)){f[o>>2]=y;if(!y){f[27]=f[27]&~(1<<a);break}}else{if(n>>>0<(f[30]|0)>>>0)x();a=n+16|0;if((f[a>>2]|0)==(u|0))f[a>>2]=y;else f[n+20>>2]=y;if(!y)break}o=f[30]|0;if(y>>>0<o>>>0)x();f[y+24>>2]=n;a=f[u+16>>2]|0;do{if(a)if(a>>>0<o>>>0)x();else{f[y+16>>2]=a;f[a+24>>2]=y;break}}while(0);a=f[u+20>>2]|0;if(a)if(a>>>0<(f[30]|0)>>>0)x();else{f[y+20>>2]=a;f[a+24>>2]=y;break}}}while(0);if(i>>>0<16){K=i+m|0;f[u+4>>2]=K|3;K=u+(K+4)|0;f[K>>2]=f[K>>2]|1}else{f[u+4>>2]=m|3;f[u+(m|4)>>2]=i|1;f[u+(i+m)>>2]=i;n=f[28]|0;if(n){r=f[31]|0;t=n>>>3;a=t<<1;l=144+(a<<2)|0;o=f[26]|0;t=1<<t;if(o&t){a=144+(a+2<<2)|0;o=f[a>>2]|0;if(o>>>0<(f[30]|0)>>>0)x();else{p=a;g=o}}else{f[26]=o|t;p=144+(a+2<<2)|0;g=l}f[p>>2]=r;f[g+12>>2]=r;f[r+8>>2]=g;f[r+12>>2]=l}f[28]=i;f[31]=E}o=u+8|0;break}x()}else Y=154}else Y=154}else if(e>>>0<=4294967231){e=e+11|0;g=e&-8;h=f[27]|0;if(h){u=0-g|0;e=e>>>8;if(e)if(g>>>0>16777215)c=31;else{E=(e+1048320|0)>>>16&8;Y=e<<E;p=(Y+520192|0)>>>16&4;Y=Y<<p;c=(Y+245760|0)>>>16&2;c=14-(p|E|c)+(Y<<c>>>15)|0;c=g>>>(c+7|0)&1|c<<1}else c=0;e=f[408+(c<<2)>>2]|0;e:do{if(!e){l=0;e=0;Y=86}else{r=u;l=0;i=g<<((c|0)==31?0:25-(c>>>1)|0);s=e;e=0;while(1){n=f[s+4>>2]&-8;u=n-g|0;if(u>>>0<r>>>0)if((n|0)==(g|0)){n=s;e=s;Y=90;break e}else e=s;else u=r;Y=f[s+20>>2]|0;s=f[s+16+(i>>>31<<2)>>2]|0;l=(Y|0)==0|(Y|0)==(s|0)?l:Y;if(!s){Y=86;break}else{r=u;i=i<<1}}}}while(0);if((Y|0)==86){if((l|0)==0&(e|0)==0){e=2<<c;e=h&(e|0-e);if(!e){m=g;Y=154;break}e=(e&0-e)+-1|0;y=e>>>12&16;e=e>>>y;b=e>>>5&8;e=e>>>b;p=e>>>2&4;e=e>>>p;E=e>>>1&2;e=e>>>E;l=e>>>1&1;l=f[408+((b|y|p|E|l)+(e>>>l)<<2)>>2]|0;e=0}if(!l){b=u;v=e}else{n=l;Y=90}}if((Y|0)==90)while(1){Y=0;E=(f[n+4>>2]&-8)-g|0;l=E>>>0<u>>>0;u=l?E:u;e=l?n:e;l=f[n+16>>2]|0;if(l){n=l;Y=90;continue}n=f[n+20>>2]|0;if(!n){b=u;v=e;break}else Y=90}if((v|0)!=0?b>>>0<((f[28]|0)-g|0)>>>0:0){e=f[30]|0;if(v>>>0>=e>>>0?(L=v+g|0,v>>>0<L>>>0):0){u=f[v+24>>2]|0;l=f[v+12>>2]|0;do{if((l|0)==(v|0)){a=v+20|0;l=f[a>>2]|0;if(!l){a=v+16|0;l=f[a>>2]|0;if(!l){S=0;break}}while(1){o=l+20|0;t=f[o>>2]|0;if(t){l=t;a=o;continue}o=l+16|0;t=f[o>>2]|0;if(!t)break;else{l=t;a=o}}if(a>>>0<e>>>0)x();else{f[a>>2]=0;S=l;break}}else{a=f[v+8>>2]|0;if((a>>>0>=e>>>0?(k=a+12|0,(f[k>>2]|0)==(v|0)):0)?(m=l+8|0,(f[m>>2]|0)==(v|0)):0){f[k>>2]=l;f[m>>2]=a;S=l;break}x()}}while(0);do{if(u){l=f[v+28>>2]|0;a=408+(l<<2)|0;if((v|0)==(f[a>>2]|0)){f[a>>2]=S;if(!S){f[27]=f[27]&~(1<<l);break}}else{if(u>>>0<(f[30]|0)>>>0)x();a=u+16|0;if((f[a>>2]|0)==(v|0))f[a>>2]=S;else f[u+20>>2]=S;if(!S)break}l=f[30]|0;if(S>>>0<l>>>0)x();f[S+24>>2]=u;a=f[v+16>>2]|0;do{if(a)if(a>>>0<l>>>0)x();else{f[S+16>>2]=a;f[a+24>>2]=S;break}}while(0);a=f[v+20>>2]|0;if(a)if(a>>>0<(f[30]|0)>>>0)x();else{f[S+20>>2]=a;f[a+24>>2]=S;break}}}while(0);e:do{if(b>>>0>=16){f[v+4>>2]=g|3;f[v+(g|4)>>2]=b|1;f[v+(b+g)>>2]=b;l=b>>>3;if(b>>>0<256){o=l<<1;n=144+(o<<2)|0;t=f[26]|0;a=1<<l;if(t&a){a=144+(o+2<<2)|0;o=f[a>>2]|0;if(o>>>0<(f[30]|0)>>>0)x();else{_=a;R=o}}else{f[26]=t|a;_=144+(o+2<<2)|0;R=n}f[_>>2]=L;f[R+12>>2]=L;f[v+(g+8)>>2]=R;f[v+(g+12)>>2]=n;break}r=b>>>8;if(r)if(b>>>0>16777215)l=31;else{V=(r+1048320|0)>>>16&8;K=r<<V;X=(K+520192|0)>>>16&4;K=K<<X;l=(K+245760|0)>>>16&2;l=14-(X|V|l)+(K<<l>>>15)|0;l=b>>>(l+7|0)&1|l<<1}else l=0;a=408+(l<<2)|0;f[v+(g+28)>>2]=l;f[v+(g+20)>>2]=0;f[v+(g+16)>>2]=0;o=f[27]|0;t=1<<l;if(!(o&t)){f[27]=o|t;f[a>>2]=L;f[v+(g+24)>>2]=a;f[v+(g+12)>>2]=L;f[v+(g+8)>>2]=L;break}r=f[a>>2]|0;i:do{if((f[r+4>>2]&-8|0)!=(b|0)){l=b<<((l|0)==31?0:25-(l>>>1)|0);while(1){i=r+16+(l>>>31<<2)|0;a=f[i>>2]|0;if(!a)break;if((f[a+4>>2]&-8|0)==(b|0)){D=a;break i}else{l=l<<1;r=a}}if(i>>>0<(f[30]|0)>>>0)x();else{f[i>>2]=L;f[v+(g+24)>>2]=r;f[v+(g+12)>>2]=L;f[v+(g+8)>>2]=L;break e}}else D=r}while(0);r=D+8|0;i=f[r>>2]|0;K=f[30]|0;if(i>>>0>=K>>>0&D>>>0>=K>>>0){f[i+12>>2]=L;f[r>>2]=L;f[v+(g+8)>>2]=i;f[v+(g+12)>>2]=D;f[v+(g+24)>>2]=0;break}else x()}else{K=b+g|0;f[v+4>>2]=K|3;K=v+(K+4)|0;f[K>>2]=f[K>>2]|1}}while(0);o=v+8|0;break}x()}else{m=g;Y=154}}else{m=g;Y=154}}else{m=-1;Y=154}}while(0);e:do{if((Y|0)==154){e=f[28]|0;if(e>>>0>=m>>>0){i=e-m|0;r=f[31]|0;if(i>>>0>15){f[31]=r+m;f[28]=i;f[r+(m+4)>>2]=i|1;f[r+e>>2]=i;f[r+4>>2]=m|3}else{f[28]=0;f[31]=0;f[r+4>>2]=e|3;Y=r+(e+4)|0;f[Y>>2]=f[Y>>2]|1}o=r+8|0;break}e=f[29]|0;if(e>>>0>m>>>0){Y=e-m|0;f[29]=Y;o=f[32]|0;f[32]=o+m;f[o+(m+4)>>2]=Y|1;f[o+4>>2]=m|3;o=o+8|0;break}if(!(f[144]|0))kt();h=m+48|0;r=f[146]|0;c=m+47|0;n=r+c|0;r=0-r|0;s=n&r;if(s>>>0>m>>>0){e=f[136]|0;if((e|0)!=0?(D=f[134]|0,L=D+s|0,L>>>0<=D>>>0|L>>>0>e>>>0):0){o=0;break}i:do{if(!(f[137]&4)){e=f[32]|0;r:do{if(e){l=552;while(1){u=f[l>>2]|0;if(u>>>0<=e>>>0?(A=l+4|0,(u+(f[A>>2]|0)|0)>>>0>e>>>0):0){o=l;e=A;break}l=f[l+8>>2]|0;if(!l){Y=172;break r}}u=n-(f[29]|0)&r;if(u>>>0<2147483647){l=j(u|0)|0;L=(l|0)==((f[o>>2]|0)+(f[e>>2]|0)|0);e=L?u:0;if(L){if((l|0)!=(-1|0)){R=l;y=e;Y=192;break i}}else Y=182}else e=0}else Y=172}while(0);do{if((Y|0)==172){o=j(0)|0;if((o|0)!=(-1|0)){e=o;u=f[145]|0;l=u+-1|0;if(!(l&e))u=s;else u=s-e+(l+e&0-u)|0;e=f[134]|0;l=e+u|0;if(u>>>0>m>>>0&u>>>0<2147483647){L=f[136]|0;if((L|0)!=0?l>>>0<=e>>>0|l>>>0>L>>>0:0){e=0;break}l=j(u|0)|0;Y=(l|0)==(o|0);e=Y?u:0;if(Y){R=o;y=e;Y=192;break i}else Y=182}else e=0}else e=0}}while(0);r:do{if((Y|0)==182){o=0-u|0;do{if(h>>>0>u>>>0&(u>>>0<2147483647&(l|0)!=(-1|0))?(M=f[146]|0,M=c-u+M&0-M,M>>>0<2147483647):0)if((j(M|0)|0)==(-1|0)){j(o|0)|0;break r}else{u=M+u|0;break}}while(0);if((l|0)!=(-1|0)){R=l;y=u;Y=192;break i}}}while(0);f[137]=f[137]|4;Y=189}else{e=0;Y=189}}while(0);if((((Y|0)==189?s>>>0<2147483647:0)?(O=j(s|0)|0,N=j(0)|0,O>>>0<N>>>0&((O|0)!=(-1|0)&(N|0)!=(-1|0))):0)?(T=N-O|0,I=T>>>0>(m+40|0)>>>0,I):0){R=O;y=I?T:e;Y=192}if((Y|0)==192){u=(f[134]|0)+y|0;f[134]=u;if(u>>>0>(f[135]|0)>>>0)f[135]=u;b=f[32]|0;i:do{if(b){o=552;do{e=f[o>>2]|0;u=o+4|0;l=f[u>>2]|0;if((R|0)==(e+l|0)){F=e;C=u;P=l;B=o;Y=202;break}o=f[o+8>>2]|0}while((o|0)!=0);if(((Y|0)==202?(f[B+12>>2]&8|0)==0:0)?b>>>0<R>>>0&b>>>0>=F>>>0:0){f[C>>2]=P+y;Y=(f[29]|0)+y|0;K=b+8|0;K=(K&7|0)==0?0:0-K&7;V=Y-K|0;f[32]=b+K;f[29]=V;f[b+(K+4)>>2]=V|1;f[b+(Y+4)>>2]=40;f[33]=f[148];break}u=f[30]|0;if(R>>>0<u>>>0){f[30]=R;u=R}l=R+y|0;e=552;while(1){if((f[e>>2]|0)==(l|0)){o=e;l=e;Y=210;break}e=f[e+8>>2]|0;if(!e){l=552;break}}if((Y|0)==210)if(!(f[l+12>>2]&8)){f[o>>2]=R;d=l+4|0;f[d>>2]=(f[d>>2]|0)+y;d=R+8|0;d=(d&7|0)==0?0:0-d&7;c=R+(y+8)|0;c=(c&7|0)==0?0:0-c&7;l=R+(c+y)|0;v=d+m|0;w=R+v|0;e=l-(R+d)-m|0;f[R+(d+4)>>2]=m|3;r:do{if((l|0)!=(b|0)){if((l|0)==(f[31]|0)){Y=(f[28]|0)+e|0;f[28]=Y;f[31]=w;f[R+(v+4)>>2]=Y|1;f[R+(Y+v)>>2]=Y;break}i=y+4|0;a=f[R+(i+c)>>2]|0;if((a&3|0)==1){s=a&-8;n=a>>>3;n:do{if(a>>>0>=256){r=f[R+((c|24)+y)>>2]|0;o=f[R+(y+12+c)>>2]|0;t:do{if((o|0)==(l|0)){t=c|16;o=R+(i+t)|0;a=f[o>>2]|0;if(!a){o=R+(t+y)|0;a=f[o>>2]|0;if(!a){G=0;break}}while(1){t=a+20|0;n=f[t>>2]|0;if(n){a=n;o=t;continue}t=a+16|0;n=f[t>>2]|0;if(!n)break;else{a=n;o=t}}if(o>>>0<u>>>0)x();else{f[o>>2]=0;G=a;break}}else{t=f[R+((c|8)+y)>>2]|0;do{if(t>>>0>=u>>>0){u=t+12|0;if((f[u>>2]|0)!=(l|0))break;a=o+8|0;if((f[a>>2]|0)!=(l|0))break;f[u>>2]=o;f[a>>2]=t;G=o;break t}}while(0);x()}}while(0);if(!r)break;u=f[R+(y+28+c)>>2]|0;a=408+(u<<2)|0;do{if((l|0)!=(f[a>>2]|0)){if(r>>>0<(f[30]|0)>>>0)x();a=r+16|0;if((f[a>>2]|0)==(l|0))f[a>>2]=G;else f[r+20>>2]=G;if(!G)break n}else{f[a>>2]=G;if(G)break;f[27]=f[27]&~(1<<u);break n}}while(0);u=f[30]|0;if(G>>>0<u>>>0)x();f[G+24>>2]=r;l=c|16;a=f[R+(l+y)>>2]|0;do{if(a)if(a>>>0<u>>>0)x();else{f[G+16>>2]=a;f[a+24>>2]=G;break}}while(0);l=f[R+(i+l)>>2]|0;if(!l)break;if(l>>>0<(f[30]|0)>>>0)x();else{f[G+20>>2]=l;f[l+24>>2]=G;break}}else{a=f[R+((c|8)+y)>>2]|0;o=f[R+(y+12+c)>>2]|0;t=144+(n<<1<<2)|0;do{if((a|0)!=(t|0)){if(a>>>0>=u>>>0?(f[a+12>>2]|0)==(l|0):0)break;x()}}while(0);if((o|0)==(a|0)){f[26]=f[26]&~(1<<n);break}do{if((o|0)==(t|0))U=o+8|0;else{if(o>>>0>=u>>>0?(z=o+8|0,(f[z>>2]|0)==(l|0)):0){U=z;break}x()}}while(0);f[a+12>>2]=o;f[U>>2]=a}}while(0);l=R+((s|c)+y)|0;e=s+e|0}l=l+4|0;f[l>>2]=f[l>>2]&-2;f[R+(v+4)>>2]=e|1;f[R+(e+v)>>2]=e;l=e>>>3;if(e>>>0<256){o=l<<1;n=144+(o<<2)|0;t=f[26]|0;a=1<<l;do{if(!(t&a)){f[26]=t|a;X=144+(o+2<<2)|0;V=n}else{a=144+(o+2<<2)|0;o=f[a>>2]|0;if(o>>>0>=(f[30]|0)>>>0){X=a;V=o;break}x()}}while(0);f[X>>2]=w;f[V+12>>2]=w;f[R+(v+8)>>2]=V;f[R+(v+12)>>2]=n;break}r=e>>>8;do{if(!r)l=0;else{if(e>>>0>16777215){l=31;break}V=(r+1048320|0)>>>16&8;Y=r<<V;X=(Y+520192|0)>>>16&4;Y=Y<<X;l=(Y+245760|0)>>>16&2;l=14-(X|V|l)+(Y<<l>>>15)|0;l=e>>>(l+7|0)&1|l<<1}}while(0);a=408+(l<<2)|0;f[R+(v+28)>>2]=l;f[R+(v+20)>>2]=0;f[R+(v+16)>>2]=0;o=f[27]|0;t=1<<l;if(!(o&t)){f[27]=o|t;f[a>>2]=w;f[R+(v+24)>>2]=a;f[R+(v+12)>>2]=w;f[R+(v+8)>>2]=w;break}r=f[a>>2]|0;n:do{if((f[r+4>>2]&-8|0)!=(e|0)){l=e<<((l|0)==31?0:25-(l>>>1)|0);while(1){i=r+16+(l>>>31<<2)|0;a=f[i>>2]|0;if(!a)break;if((f[a+4>>2]&-8|0)==(e|0)){K=a;break n}else{l=l<<1;r=a}}if(i>>>0<(f[30]|0)>>>0)x();else{f[i>>2]=w;f[R+(v+24)>>2]=r;f[R+(v+12)>>2]=w;f[R+(v+8)>>2]=w;break r}}else K=r}while(0);r=K+8|0;i=f[r>>2]|0;Y=f[30]|0;if(i>>>0>=Y>>>0&K>>>0>=Y>>>0){f[i+12>>2]=w;f[r>>2]=w;f[R+(v+8)>>2]=i;f[R+(v+12)>>2]=K;f[R+(v+24)>>2]=0;break}else x()}else{Y=(f[29]|0)+e|0;f[29]=Y;f[32]=w;f[R+(v+4)>>2]=Y|1}}while(0);o=R+(d|8)|0;break e}else l=552;while(1){o=f[l>>2]|0;if(o>>>0<=b>>>0?(a=f[l+4>>2]|0,t=o+a|0,t>>>0>b>>>0):0)break;l=f[l+8>>2]|0}l=o+(a+-39)|0;l=o+(a+-47+((l&7|0)==0?0:0-l&7))|0;u=b+16|0;l=l>>>0<u>>>0?b:l;a=l+8|0;o=R+8|0;o=(o&7|0)==0?0:0-o&7;Y=y+-40-o|0;f[32]=R+o;f[29]=Y;f[R+(o+4)>>2]=Y|1;f[R+(y+-36)>>2]=40;f[33]=f[148];o=l+4|0;f[o>>2]=27;f[a>>2]=f[138];f[a+4>>2]=f[139];f[a+8>>2]=f[140];f[a+12>>2]=f[141];f[138]=R;f[139]=y;f[141]=0;f[140]=a;a=l+28|0;f[a>>2]=7;if((l+32|0)>>>0<t>>>0)do{Y=a;a=a+4|0;f[a>>2]=7}while((Y+8|0)>>>0<t>>>0);if((l|0)!=(b|0)){e=l-b|0;f[o>>2]=f[o>>2]&-2;f[b+4>>2]=e|1;f[l>>2]=e;t=e>>>3;if(e>>>0<256){a=t<<1;l=144+(a<<2)|0;o=f[26]|0;n=1<<t;if(o&n){r=144+(a+2<<2)|0;i=f[r>>2]|0;if(i>>>0<(f[30]|0)>>>0)x();else{H=r;W=i}}else{f[26]=o|n;H=144+(a+2<<2)|0;W=l}f[H>>2]=b;f[W+12>>2]=b;f[b+8>>2]=W;f[b+12>>2]=l;break}r=e>>>8;if(r)if(e>>>0>16777215)a=31;else{K=(r+1048320|0)>>>16&8;Y=r<<K;V=(Y+520192|0)>>>16&4;Y=Y<<V;a=(Y+245760|0)>>>16&2;a=14-(V|K|a)+(Y<<a>>>15)|0;a=e>>>(a+7|0)&1|a<<1}else a=0;n=408+(a<<2)|0;f[b+28>>2]=a;f[b+20>>2]=0;f[u>>2]=0;r=f[27]|0;i=1<<a;if(!(r&i)){f[27]=r|i;f[n>>2]=b;f[b+24>>2]=n;f[b+12>>2]=b;f[b+8>>2]=b;break}r=f[n>>2]|0;r:do{if((f[r+4>>2]&-8|0)!=(e|0)){a=e<<((a|0)==31?0:25-(a>>>1)|0);while(1){i=r+16+(a>>>31<<2)|0;n=f[i>>2]|0;if(!n)break;if((f[n+4>>2]&-8|0)==(e|0)){q=n;break r}else{a=a<<1;r=n}}if(i>>>0<(f[30]|0)>>>0)x();else{f[i>>2]=b;f[b+24>>2]=r;f[b+12>>2]=b;f[b+8>>2]=b;break i}}else q=r}while(0);r=q+8|0;i=f[r>>2]|0;Y=f[30]|0;if(i>>>0>=Y>>>0&q>>>0>=Y>>>0){f[i+12>>2]=b;f[r>>2]=b;f[b+8>>2]=i;f[b+12>>2]=q;f[b+24>>2]=0;break}else x()}}else{Y=f[30]|0;if((Y|0)==0|R>>>0<Y>>>0)f[30]=R;f[138]=R;f[139]=y;f[141]=0;f[35]=f[144];f[34]=-1;r=0;do{Y=r<<1;K=144+(Y<<2)|0;f[144+(Y+3<<2)>>2]=K;f[144+(Y+2<<2)>>2]=K;r=r+1|0}while((r|0)!=32);Y=R+8|0;Y=(Y&7|0)==0?0:0-Y&7;K=y+-40-Y|0;f[32]=R+Y;f[29]=K;f[R+(Y+4)>>2]=K|1;f[R+(y+-36)>>2]=40;f[33]=f[148]}}while(0);i=f[29]|0;if(i>>>0>m>>>0){Y=i-m|0;f[29]=Y;o=f[32]|0;f[32]=o+m;f[o+(m+4)>>2]=Y|1;f[o+4>>2]=m|3;o=o+8|0;break}}f[(bt()|0)>>2]=12;o=0}else o=0}}while(0);return o|0}function vt(e){e=e|0;var i=0,r=0,n=0,t=0,o=0,a=0,l=0,u=0,s=0,c=0,h=0,w=0,d=0,v=0,b=0,k=0,m=0,y=0,p=0,E=0,g=0,A=0,S=0,_=0,R=0,M=0;e:do{if(e){t=e+-8|0;s=f[30]|0;i:do{if(t>>>0>=s>>>0?(n=f[e+-4>>2]|0,r=n&3,(r|0)!=1):0){E=n&-8;g=e+(E+-8)|0;do{if(!(n&1)){t=f[t>>2]|0;if(!r)break e;c=-8-t|0;w=e+c|0;d=t+E|0;if(w>>>0<s>>>0)break i;if((w|0)==(f[31]|0)){o=e+(E+-4)|0;t=f[o>>2]|0;if((t&3|0)!=3){M=w;o=d;break}f[28]=d;f[o>>2]=t&-2;f[e+(c+4)>>2]=d|1;f[g>>2]=d;break e}r=t>>>3;if(t>>>0<256){n=f[e+(c+8)>>2]|0;o=f[e+(c+12)>>2]|0;t=144+(r<<1<<2)|0;do{if((n|0)!=(t|0)){if(n>>>0>=s>>>0?(f[n+12>>2]|0)==(w|0):0)break;x()}}while(0);if((o|0)==(n|0)){f[26]=f[26]&~(1<<r);M=w;o=d;break}do{if((o|0)==(t|0))i=o+8|0;else{if(o>>>0>=s>>>0?(a=o+8|0,(f[a>>2]|0)==(w|0)):0){i=a;break}x()}}while(0);f[n+12>>2]=o;f[i>>2]=n;M=w;o=d;break}a=f[e+(c+24)>>2]|0;t=f[e+(c+12)>>2]|0;do{if((t|0)==(w|0)){n=e+(c+20)|0;t=f[n>>2]|0;if(!t){n=e+(c+16)|0;t=f[n>>2]|0;if(!t){h=0;break}}while(1){r=t+20|0;i=f[r>>2]|0;if(i){t=i;n=r;continue}r=t+16|0;i=f[r>>2]|0;if(!i)break;else{t=i;n=r}}if(n>>>0<s>>>0)x();else{f[n>>2]=0;h=t;break}}else{n=f[e+(c+8)>>2]|0;if((n>>>0>=s>>>0?(l=n+12|0,(f[l>>2]|0)==(w|0)):0)?(u=t+8|0,(f[u>>2]|0)==(w|0)):0){f[l>>2]=t;f[u>>2]=n;h=t;break}x()}}while(0);if(a){t=f[e+(c+28)>>2]|0;n=408+(t<<2)|0;if((w|0)==(f[n>>2]|0)){f[n>>2]=h;if(!h){f[27]=f[27]&~(1<<t);M=w;o=d;break}}else{if(a>>>0<(f[30]|0)>>>0)x();t=a+16|0;if((f[t>>2]|0)==(w|0))f[t>>2]=h;else f[a+20>>2]=h;if(!h){M=w;o=d;break}}n=f[30]|0;if(h>>>0<n>>>0)x();f[h+24>>2]=a;t=f[e+(c+16)>>2]|0;do{if(t)if(t>>>0<n>>>0)x();else{f[h+16>>2]=t;f[t+24>>2]=h;break}}while(0);t=f[e+(c+20)>>2]|0;if(t)if(t>>>0<(f[30]|0)>>>0)x();else{f[h+20>>2]=t;f[t+24>>2]=h;M=w;o=d;break}else{M=w;o=d}}else{M=w;o=d}}else{M=t;o=E}}while(0);if(M>>>0<g>>>0?(v=e+(E+-4)|0,b=f[v>>2]|0,(b&1|0)!=0):0){if(!(b&2)){if((g|0)==(f[32]|0)){R=(f[29]|0)+o|0;f[29]=R;f[32]=M;f[M+4>>2]=R|1;if((M|0)!=(f[31]|0))break e;f[31]=0;f[28]=0;break e}if((g|0)==(f[31]|0)){R=(f[28]|0)+o|0;f[28]=R;f[31]=M;f[M+4>>2]=R|1;f[M+R>>2]=R;break e}u=(b&-8)+o|0;r=b>>>3;do{if(b>>>0>=256){i=f[e+(E+16)>>2]|0;o=f[e+(E|4)>>2]|0;do{if((o|0)==(g|0)){t=e+(E+12)|0;o=f[t>>2]|0;if(!o){t=e+(E+8)|0;o=f[t>>2]|0;if(!o){A=0;break}}while(1){n=o+20|0;r=f[n>>2]|0;if(r){o=r;t=n;continue}n=o+16|0;r=f[n>>2]|0;if(!r)break;else{o=r;t=n}}if(t>>>0<(f[30]|0)>>>0)x();else{f[t>>2]=0;A=o;break}}else{t=f[e+E>>2]|0;if((t>>>0>=(f[30]|0)>>>0?(y=t+12|0,(f[y>>2]|0)==(g|0)):0)?(p=o+8|0,(f[p>>2]|0)==(g|0)):0){f[y>>2]=o;f[p>>2]=t;A=o;break}x()}}while(0);if(i){o=f[e+(E+20)>>2]|0;t=408+(o<<2)|0;if((g|0)==(f[t>>2]|0)){f[t>>2]=A;if(!A){f[27]=f[27]&~(1<<o);break}}else{if(i>>>0<(f[30]|0)>>>0)x();o=i+16|0;if((f[o>>2]|0)==(g|0))f[o>>2]=A;else f[i+20>>2]=A;if(!A)break}o=f[30]|0;if(A>>>0<o>>>0)x();f[A+24>>2]=i;t=f[e+(E+8)>>2]|0;do{if(t)if(t>>>0<o>>>0)x();else{f[A+16>>2]=t;f[t+24>>2]=A;break}}while(0);r=f[e+(E+12)>>2]|0;if(r)if(r>>>0<(f[30]|0)>>>0)x();else{f[A+20>>2]=r;f[r+24>>2]=A;break}}}else{n=f[e+E>>2]|0;o=f[e+(E|4)>>2]|0;t=144+(r<<1<<2)|0;do{if((n|0)!=(t|0)){if(n>>>0>=(f[30]|0)>>>0?(f[n+12>>2]|0)==(g|0):0)break;x()}}while(0);if((o|0)==(n|0)){f[26]=f[26]&~(1<<r);break}do{if((o|0)==(t|0))k=o+8|0;else{if(o>>>0>=(f[30]|0)>>>0?(m=o+8|0,(f[m>>2]|0)==(g|0)):0){k=m;break}x()}}while(0);f[n+12>>2]=o;f[k>>2]=n}}while(0);f[M+4>>2]=u|1;f[M+u>>2]=u;if((M|0)==(f[31]|0)){f[28]=u;break e}else o=u}else{f[v>>2]=b&-2;f[M+4>>2]=o|1;f[M+o>>2]=o}t=o>>>3;if(o>>>0<256){n=t<<1;o=144+(n<<2)|0;i=f[26]|0;r=1<<t;if(i&r){r=144+(n+2<<2)|0;i=f[r>>2]|0;if(i>>>0<(f[30]|0)>>>0)x();else{S=r;_=i}}else{f[26]=i|r;S=144+(n+2<<2)|0;_=o}f[S>>2]=M;f[_+12>>2]=M;f[M+8>>2]=_;f[M+12>>2]=o;break e}i=o>>>8;if(i)if(o>>>0>16777215)t=31;else{S=(i+1048320|0)>>>16&8;_=i<<S;e=(_+520192|0)>>>16&4;_=_<<e;t=(_+245760|0)>>>16&2;t=14-(e|S|t)+(_<<t>>>15)|0;t=o>>>(t+7|0)&1|t<<1}else t=0;r=408+(t<<2)|0;f[M+28>>2]=t;f[M+20>>2]=0;f[M+16>>2]=0;i=f[27]|0;n=1<<t;r:do{if(i&n){r=f[r>>2]|0;n:do{if((f[r+4>>2]&-8|0)!=(o|0)){t=o<<((t|0)==31?0:25-(t>>>1)|0);while(1){i=r+16+(t>>>31<<2)|0;n=f[i>>2]|0;if(!n)break;if((f[n+4>>2]&-8|0)==(o|0)){R=n;break n}else{t=t<<1;r=n}}if(i>>>0<(f[30]|0)>>>0)x();else{f[i>>2]=M;f[M+24>>2]=r;f[M+12>>2]=M;f[M+8>>2]=M;break r}}else R=r}while(0);i=R+8|0;r=f[i>>2]|0;_=f[30]|0;if(r>>>0>=_>>>0&R>>>0>=_>>>0){f[r+12>>2]=M;f[i>>2]=M;f[M+8>>2]=r;f[M+12>>2]=R;f[M+24>>2]=0;break}else x()}else{f[27]=i|n;f[r>>2]=M;f[M+24>>2]=r;f[M+12>>2]=M;f[M+8>>2]=M}}while(0);M=(f[34]|0)+-1|0;f[34]=M;if(!M)i=560;else break e;while(1){i=f[i>>2]|0;if(!i)break;else i=i+8|0}f[34]=-1;break e}}}while(0);x()}}while(0);return}function bt(){var e=0;e=600;return e|0}function kt(){var e=0;do{if(!(f[144]|0)){e=B(30)|0;if(!(e+-1&e)){f[146]=e;f[145]=e;f[147]=-1;f[148]=-1;f[149]=0;f[137]=0;f[144]=(H(0)|0)&-16^1431655768;break}else x()}}while(0);return}function mt(){}function yt(e,i,r){e=e|0;i=i|0;r=r|0;var t=0;if((r|0)>=4096)return q(e|0,i|0,r|0)|0;t=e|0;if((e&3)==(i&3)){while(e&3){if(!r)return t|0;n[e>>0]=n[i>>0]|0;e=e+1|0;i=i+1|0;r=r-1|0}while((r|0)>=4){f[e>>2]=f[i>>2];e=e+4|0;i=i+4|0;r=r-4|0}}while((r|0)>0){n[e>>0]=n[i>>0]|0;e=e+1|0;i=i+1|0;r=r-1|0}return t|0}function pt(e,i,r){e=e|0;i=i|0;r=r|0;var t=0;if((i|0)<(e|0)&(e|0)<(i+r|0)){t=e;i=i+r|0;e=e+r|0;while((r|0)>0){e=e-1|0;i=i-1|0;r=r-1|0;n[e>>0]=n[i>>0]|0}e=t}else yt(e,i,r)|0;return e|0}function Et(e,i,r){e=e|0;i=i|0;r=r|0;var t=0,o=0,a=0,l=0;t=e+r|0;if((r|0)>=20){i=i&255;a=e&3;l=i|i<<8|i<<16|i<<24;o=t&~3;if(a){a=e+4-a|0;while((e|0)<(a|0)){n[e>>0]=i;e=e+1|0}}while((e|0)<(o|0)){f[e>>2]=l;e=e+4|0}}while((e|0)<(t|0)){n[e>>0]=i;e=e+1|0}return e-r|0}return{_:vt,___errno_location:bt,F:pt,O:ie,D:ee,N:re,C:Et,S:dt,L:yt,T:ne,M:J,I:te,runPostSets:mt,stackAlloc:X,stackSave:V,stackRestore:K,establishStackSpace:Y,setThrew:Q,setTempRet0:Z,getTempRet0:$}}(e.asmGlobalArg,e.asmLibraryArg,B),Te=(e.I=Ne.I,e._=Ne._),Ie=(e.runPostSets=Ne.runPostSets,e.F=Ne.F),Le=(e.D=Ne.D,e.N=Ne.N,e.C=Ne.C),Fe=e.S=Ne.S,Ce=e.L=Ne.L;function Pe(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function Be(i){function r(){e.calledRun||(e.calledRun=!0,w||(J(),X(Y),e.onRuntimeInitialized&&e.onRuntimeInitialized(),e.R&&je&&e.callMain(i),function(){if(e.postRun)for("function"==typeof e.postRun&&(e.postRun=[e.postRun]);e.postRun.length;)ie(e.postRun.shift());X(Z)}()))}i=i||e.arguments,se>0||(function(){if(e.preRun)for("function"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;)ee(e.preRun.shift());X(V)}(),se>0||e.calledRun||(e.setStatus?(e.setStatus("Running..."),setTimeout(function(){setTimeout(function(){e.setStatus("")},1),r()},1)):r()))}function Ue(i,r){if(!r||!e.noExitRuntime)throw e.noExitRuntime||(w=!0,j=Oe,X(Q),e.onExit&&e.onExit(i)),o&&"function"==typeof quit&&quit(i),new Pe(i)}e.O=Ne.O,e.M=Ne.M,e.T=Ne.T,e.___errno_location=Ne.___errno_location,a.stackAlloc=Ne.stackAlloc,a.stackSave=Ne.stackSave,a.stackRestore=Ne.stackRestore,a.establishStackSpace=Ne.establishStackSpace,a.setTempRet0=Ne.setTempRet0,a.getTempRet0=Ne.getTempRet0,Pe.prototype=new Error,Pe.prototype.constructor=Pe,he=function i(){e.calledRun||Be(),e.calledRun||(he=i)},e.callMain=e.callMain=function(i){d(0==se,"cannot call main when async dependencies remain! (listen on __ATMAIN__)"),d(0==V.length,"cannot call main when preRun functions remain to be called"),i=i||[],J();var r=i.length+1;function n(){for(var e=0;e<3;e++)t.push(0)}var t=[p(re(e.thisProgram),"i8",0)];n();for(var f=0;f<r-1;f+=1)t.push(p(re(i[f]),"i8",0)),n();t.push(0),t=p(t,"i32",0),Oe=a.stackSave();try{Ue(e.R(r,t,0),!0)}catch(o){if(o instanceof Pe)return;if("SimulateInfiniteLoop"==o)return e.noExitRuntime=!0,void a.stackRestore(Oe);throw o&&"object"==typeof o&&o.stack&&e.printErr("exception thrown: "+[o,o.stack]),o}},e.run=e.run=Be,e.exit=e.exit=Ue;var xe=[];function ze(i){void 0!==i?(e.print(i),e.printErr(i),i=JSON.stringify(i)):i="",w=!0;var r="abort("+i+") at "+R()+"\nIf this abort() is unexpected, build with -s ASSERTIONS=1 which can give more information.";throw xe&&xe.forEach(function(e){r=e(r,i)}),r}if(e.abort=e.abort=ze,e.preInit)for("function"==typeof e.preInit&&(e.preInit=[e.preInit]);e.preInit.length>0;)e.preInit.pop()();var je=!0;return e.noInitialRun&&(je=!1),e.noExitRuntime=!0,Be(),i}();self.onmessage=function(i){switch(i.data.command){case"encode":n=i.data.samples,t=(t=i.data.sampleRate)||8e3,self.postMessage({command:"encode",amr:e.encode(n,t,7)});break;case"decode":r=i.data.buffer,self.postMessage({command:"decode",amr:e.decode(r)})}var r,n,t}}.toString().replace(/^\s*function.*?\(\)\s*{/,"").replace(/}\s*$/,""),s=(window.URL||window.webkitURL).createObjectURL(new Blob([u],{type:"text/javascript"}));return function(){function i(){var r=this;e(this,i),this.Z=!1,this.$=!1,this.J=new l,this.ee=new Float32Array(0),this.ie=new Uint8Array(0),this.re=null,this.ne=null,this.te=null,this.fe=null,this.oe=null,this.ae=null,this.le=null,this.ue=null,this.se=null,this.ce=null,this.he=!1,this.we=!1,this.de=0,this.ve=0,this.be=function(){r.J.playPcm(new Float32Array(10),24e3)},this.ke=function(){r.he&&(r.he=!1,r.le&&r.le(),r.te&&r.te()),r.we||r.ne&&r.ne()},this.me=function(e,i){var r=new Worker(s);r.postMessage(e),r.onmessage=function(e){i(e.data.amr),r.terminate()}}}return r(i,[{key:"isInit",value:function(){return this.Z}},{key:"initWithArrayBuffer",value:function(e){var r=this;return(this.Z||this.$)&&i.throwAlreadyInitialized(),this.be(),new Promise(function(n,t){var f=new Uint8Array(e);r.decodeAMRAsync(f).then(function(o){r.ee=o,r.Z=!0,r.ee?(r.ie=f,n()):l.decodeAudioArrayBufferByContext(e).then(function(e){return r.Z=!0,r.encodeAMRAsync(e,l.getCtxSampleRate())}).then(function(e){return r.ie=e,r.re=i.rawAMRData2Blob(e),r.decodeAMRAsync(e)}).then(function(e){r.ee=e,n()}).catch(function(){t(new Error("Failed to decode."))})})})}},{key:"initWithBlob",value:function(e){var r=this;return(this.Z||this.$)&&i.throwAlreadyInitialized(),this.be(),this.re=e,new Promise(function(i){var r=new FileReader;r.onload=function(e){i(e.target.result)},r.readAsArrayBuffer(e)}).then(function(e){return r.initWithArrayBuffer(e)})}},{key:"initWithUrl",value:function(e){var r=this;return(this.Z||this.$)&&i.throwAlreadyInitialized(),this.be(),new Promise(function(i,r){var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=function(){i(this.response)},n.onerror=function(){r(new Error("Failed to fetch "+e))},n.send()}).then(function(e){return r.initWithArrayBuffer(e)})}},{key:"initWithRecord",value:function(){var e=this;return(this.Z||this.$)&&i.throwAlreadyInitialized(),this.be(),new Promise(function(i,r){e.J.initRecorder().then(function(){e.$=!0,i()}).catch(function(e){r(e)})})}},{key:"on",value:function(e,i){if("function"==typeof i)switch(e){case"play":this.fe=i;break;case"stop":this.le=i;break;case"pause":this.oe=i;break;case"resume":this.ae=i;break;case"ended":this.ne=i;break;case"autoEnded":this.te=i;break;case"startRecord":this.ue=i;break;case"cancelRecord":this.se=i;break;case"finishRecord":this.ce=i}}},{key:"onPlay",value:function(e){this.on("play",e)}},{key:"onStop",value:function(e){this.on("stop",e)}},{key:"onPause",value:function(e){this.on("pause",e)}},{key:"onResume",value:function(e){this.on("resume",e)}},{key:"onEnded",value:function(e){this.on("ended",e)}},{key:"onAutoEnded",value:function(e){this.on("autoEnded",e)}},{key:"onStartRecord",value:function(e){this.on("startRecord",e)}},{key:"onFinishRecord",value:function(e){this.on("finishRecord",e)}},{key:"onCancelRecord",value:function(e){this.on("cancelRecord",e)}},{key:"play",value:function(e){var i=e&&e<this.getDuration()?parseFloat(e):0;if(!this.Z)throw new Error("Please init AMR first.");this.fe&&this.fe(),this.he=!0,this.we=!1,this.de=l.getCtxTime()-i,this.J.playPcm(this.ee,this.$?l.getCtxSampleRate():8e3,this.ke.bind(this),i)}},{key:"stop",value:function(){this.J.stopPcm(),this.he=!1,this.we=!1,this.le&&this.le()}},{key:"pause",value:function(){this.he&&(this.he=!1,this.we=!0,this.ve=l.getCtxTime()-this.de,this.J.stopPcm(),this.oe&&this.oe())}},{key:"resume",value:function(){this.we&&(this.he=!0,this.we=!1,this.de=l.getCtxTime()-this.ve,this.J.playPcm(this.ee,this.$?l.getCtxSampleRate():8e3,this.ke.bind(this),this.ve),this.ae&&this.ae())}},{key:"playOrResume",value:function(){this.we?this.resume():this.play()}},{key:"pauseOrResume",value:function(){this.we?this.resume():this.pause()}},{key:"playOrPauseOrResume",value:function(){this.we?this.resume():this.he?this.pause():this.play()}},{key:"setPosition",value:function(e){var i=parseFloat(e);i>this.getDuration()?this.stop():this.we?this.ve=i:this.he?(this.J.stopPcmSilently(),this.de=l.getCtxTime()-i,this.J.playPcm(this.ee,this.$?l.getCtxSampleRate():8e3,this.ke.bind(this),i)):this.play(i)}},{key:"getCurrentPosition",value:function(){return this.we?this.ve:this.he?l.getCtxTime()-this.de:0}},{key:"isPlaying",value:function(){return this.he}},{key:"isPaused",value:function(){return this.we}},{key:"startRecord",value:function(){this.J.startRecord(),this.ue&&this.ue()}},{key:"finishRecord",value:function(){var e=this;return new Promise(function(r){e.J.stopRecord(),e.J.generateRecordSamples().then(function(i){return e.ee=i,e.encodeAMRAsync(i,l.getCtxSampleRate())}).then(function(n){e.ie=n,e.re=i.rawAMRData2Blob(e.ie),e.Z=!0,e.ce&&e.ce(),e.J.releaseRecord(),r()})})}},{key:"cancelRecord",value:function(){this.J.stopRecord(),this.J.releaseRecord(),this.se&&this.se()}},{key:"isRecording",value:function(){return this.J.isRecording()}},{key:"getDuration",value:function(){var e=this.$?l.getCtxSampleRate():8e3;return this.ee.length/e}},{key:"getBlob",value:function(){return this.re}},{key:"encodeAMRAsync",value:function(e,i){var r=this;return new Promise(function(n){r.me({command:"encode",samples:e,sampleRate:i},n)})}},{key:"decodeAMRAsync",value:function(e){var i=this;return new Promise(function(r){i.me({command:"decode",buffer:e},r)})}}],[{key:"rawAMRData2Blob",value:function(e){return new Blob([e.buffer],{type:"audio/amr"})}},{key:"throwAlreadyInitialized",value:function(){throw new Error("AMR has been initialized. For a new AMR, please generate a new BenzAMRRecorder().")}},{key:"isPlaySupported",value:function(){return l.isPlaySupported()}},{key:"isRecordSupported",value:function(){return l.isRecordSupported()}}]),i}()});
