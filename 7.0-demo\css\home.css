body {
    margin: 0;
    padding: 0;
}

.container {
    display: flex;
    flex-direction: column;
    width: 100vw;
    /* 宽度适应屏幕 */
    height: 100vh;
    /* 高度适应屏幕 */
}

header {
    width: 100%;
    height: 10%;
    background-color: black;
    position: relative;
}

.player {
    color: #fff;
    text-align: center;
}

.logout {
    position: absolute;
    top: 10%;
    right: 20px;
}

.content {
    display: flex;
    width: 100%;
    /* 撑满容器 */
    height: 100%;
    /* 撑满容器 */
}

aside {
    width: 30%;
    /* flex: 2; */
    /* 撑满左区域 */
    background-color: #fff;
    /* 左 */
}

main {
    width: 70%;
    /* flex: 5; */
    /* 撑满右区域,宽度是左区域的两倍 */
    background-color: yellow;
    /* 右 */
}

.toggle {
    cursor: pointer;
}

li.noIcon {
    list-style-type: none;
    width: 200px;
}

.icon {
    display: inline-block;
    width: 14px;
    height: 18px;
    margin-right: 6px;
    vertical-align: middle;
    background-repeat: no-repeat;
    background-size: 14px 14px;
    background-position: center;
}

.spanClass {
    display: inline-block;
    vertical-align: middle;
    width: 120px;
    overflow: hidden;
    /*内容会被修剪，并且其余内容是不可见的*/
    text-overflow: ellipsis;
    /*显示省略符号来代表被修剪的文本。*/
    white-space: nowrap;
    /*文本不换行*/
}

.unit {
    background-image: url(../img/i6.png);
}

.equip {
    background-image: url(../img/i5a.png);
}

.equip2 {
    background-image: url(../img/i5b.png);
}

.c2-online {
    background-image: url(../img/icon_C2_s.png);
}

.c2-offline {
    background-image: url(../img/icon_C2_n.png);
}

.jjy-online {
    background-image: url(../img/c2_online.png);
}

.jjy-offline {
    background-image: url(../img/c2_offline.png);
}

.offline {
    background-image: url(../img/offline.png);
}

.busy {
    background-image: url(../img/busy.png);
}

.online {
    background-image: url(../img/online.png);
}

.unit,
.equip,
.equip2,
.offline,
.online,
.busy {
    background-repeat: no-repeat;
    background-size: 14px 14px;
    background-position: center;
}

.video {
    background-image: url(../img/视频呼叫_d.png);
}

.videoEnable {
    background-image: url(../img/视频呼叫_n.png);
}

.videoEnable:focus {
    background-image: url(../img/视频呼叫_h.png);
}

.voice {
    background: url(../img/i4a.png);
}

.voiceEnable {
    background: url(../img/i4b.png);
}

.voiceEnable:focus {
    background: url(../img/i4c.png);
}

.treeIcon {
    /* display: inline-block; */
    width: 16px;
    height: 16px;
    margin: 0 5px;
    background-repeat: no-repeat;
    background-position: center center;
    cursor: pointer;
    float: right;
}

.two {
    background: url(../img/gb_s_close.png);
    width: 20px !important;
    height: 14px !important;
}

.twoEnable {
    background: url(../img/gb_s_show.png);
    width: 20px !important;
    height: 14px !important;
}

.twoEnable:focus {
    background: url(../img/gb_s_show1.png);
    width: 20px !important;
    height: 14px !important;
}


/* 播放器 */

.chrmoe_video {
    overflow: hidden;
}

.video_head {
    padding: 0.5% 0.5%;
    height: 5%;
    background-color: #1e2535;
}

.logo {
    float: left;
    width: 20%;
    height: 100%;
    padding-left: 2%;
    line-height: 100%;
    font-size: 18px;
    background-image: url("../img/pe.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen {
    width: 25%;
    height: 100%;
    /* padding-top: 0.2%; */
    float: right;
    margin-right: 50px
}

.screen ul {
    width: 100%;
    height: 100%;
    list-style: none;
}

.screen_li {
    width: 5%;
    padding-top: 5%;
    height: 0;
    float: right;
    margin-left: 5%;
    cursor: pointer;
}

.screen_li_xian {
    width: 2px;
    height: 50%;
    float: right;
    margin-left: 5%;
}

.screen1 {
    background-image: url("../img/分屏切换9.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screenOpen1 {
    background-image: url("../img/分屏切换10.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen1:hover {
    background-image: url("../img/分屏切换10.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen4 {
    background-image: url("../img/分屏切换1.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screenOpen4 {
    background-image: url("../img/分屏切换5.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen4:hover {
    background-image: url("../img/分屏切换5.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen6 {
    background-image: url("../img/分屏切换3.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screenOpen6 {
    background-image: url("../img/分屏切换7.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen6:hover {
    background-image: url("../img/分屏切换7.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen9 {
    background-image: url("../img/分屏切换2.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screenOpen9 {
    background-image: url("../img/分屏切换6.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen9:hover {
    background-image: url("../img/分屏切换6.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen16 {
    background-image: url("../img/分屏切换4.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screenOpen16 {
    background-image: url("../img/分屏切换8.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen16:hover {
    background-image: url("../img/分屏切换8.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen_xian {
    background-image: url("../img/直线1.png");
    background-size: 2px;
    background-repeat: no-repeat;
}

.screen_min {
    background-image: url("../img/最小化.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen_min:hover {
    background-image: url("../img/组223.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen_max {
    background-image: url("../img/最大化.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen_max:hover {
    background-image: url("../img/组224.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen_reduction {
    background-image: url("../img/还原.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen_reduction:hover {
    background-image: url("../img/组225.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen_close {
    background-image: url("../img/关闭.png");
    background-size: contain;
    background-repeat: no-repeat;
}

.screen_close:hover {
    background-color: #e81123;
}

.max {
    position: fixed;
}


/* 播放器主体 */

.main_player {
    width: 100%;
    height: 100%;
}

.type {
    width: 100%;
    height: 100%;
}

.chromeBox {
    width: calc(50% - 2px);
    height: calc(50% - 2px);
    float: left;
    background: url("../img/background.png") no-repeat center;
    background-color: #333;
}

.chromeBox1 {
    background: url("../img/background.png") no-repeat center;
    background-color: #333;
}

.chromeBox6 {
    float: left;
}

.chromeBoxLeft {
    width: calc(66.66% - 2px);
    height: calc(66.66% - 3px);
}

.chromeBoxRight {
    width: calc(33.33% - 2px);
    height: calc(33.33% - 2px);
}

.chromeBox6-1 {
    width: calc(33.33% - 2px);
    height: calc(33.33% - 2px);
}

.chromeBox9 {
    width: calc(33.33% - 2px);
    height: calc(33.33% - 2px);
    float: left;
    background: url("../img/background.png") no-repeat center;
    background-color: #333;
}

.chromeBox9_right {
    width: calc(33.33% - 2px);
    height: calc(33.33% - 2px);
    float: left;
    background: url("../img/background.png") no-repeat center;
    background-color: #333;
}

.chromeBox16 {
    width: calc(25% - 2px);
    height: calc(25% - 2px);
    float: left;
    background: url("../img/background.png") no-repeat center;
    background-color: #333;
}


/*  初始化播放器 */


/*video默认全屏按钮*/

video::-webkit-media-controls-fullscreen-button {
    display: none !important;
}


/*video默认aduio音量按钮*/

video::-webkit-media-controls-mute-button {
    display: none !important;
}


/*video默认setting按钮*/

video::-internal-media-controls-overflow-button {
    display: none !important;
}


/*腾讯云点播禁用firefox全屏、设置按钮*/

.trump-button[sub-component="fullscreen_btn"],
.trump-button[now="fullscreen"] {
    display: none !important;
}

.trump-button[sub-component="setting"] {
    display: none !important;
}


/*禁用video的controls（要慎重！不要轻易隐藏掉，会导致点击视频不能播放）*/

video::-webkit-media-controls {
    display: none !important;
}


/* 播放器 */

.chromeMain {
    width: 100%;
    height: 100%;
    position: relative;
}

.video_mian {
    position: relative;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.playerVideoBox {
    position: relative;
    width: 100%;
    height: 100%;
}

.playerAudioBox {
    position: relative;
    width: 100%;
    height: 100%;
}

.chrome {
    /* object-fit: fill; */
    width: 100%;
    height: 100%;
}

.myControls {
    display: none;
    /* display: block; */
    position: absolute;
    left: 0;
    bottom: 0;
    padding: 1.4% 0;
    /* padding: 14.219px 0; */
    width: 100%;
    height: 10%;
    /* height: 50px; */
    background-color: rgba(29, 35, 50, 0.6);
}

.control {
    float: right;
    margin-right: 3%;
    /* margin-right: 15.234px; */
    width: 5%;
    /* width: 23.3px; */
    height: 108%;
    cursor: pointer;
}

.voice_sos {
    background-image: url("../img/sos.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.voice_isSOS_open {
    background-image: url("../img/pullsos.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.voice_isTwo_close {
    background-image: url("../img/srzf_open.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.voice_isTwo_open {
    background-image: url("../img/srzf_close.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.voice_hui {
    background-image: url("../img/audio_hui.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.voice_intercom {
    background-image: url("../img/麦克风关闭2.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.voice_intercom:hover {
    background-image: url("../img/麦克风关闭1.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.voice_setUp {
    position: relative;
    background-image: url("../img/set_close.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.voice_setUp:hover {
    position: relative;
    background-image: url("../img/set_open.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.video_open {
    background-image: url("../img/麦克风1.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.photograph {
    background-image: url("../img/组件241.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.photograph:hover {
    background-image: url("../img/组件242.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.screenshot {
    background-image: url("../img/截图1.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.screenshot:hover {
    background-image: url("../img/截图2.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.preset_bit {
    background-image: url("../img/组件271.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.preset_bit:hover {
    background-image: url("../img/组件272.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.videotape {
    background-image: url("../img/视频2.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.videotape:hover {
    background-image: url("../img/视频1.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.voiceOpen {
    position: relative;
    background-image: url("../img/音量1.png");
    background-size: 100%;
    background-repeat: no-repeat;
}


/* .voiceOpen:hover {
    background-image: url("../img/音量 (1).png");
    background-size: 100%;
    background-repeat: no-repeat;
  } */

.voiceOpen:hover .range {
    display: block;
}

.voiceClose {
    background-image: url("../img/音量关闭2.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.voiceClose:hover {
    background-image: url("../img/音量关闭1.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.closeHui {
    background-image: url("../img/");
    background-size: 100%;
    background-repeat: no-repeat;
}

.end {
    background-image: url("../img/关闭分屏1.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.end:hover {
    background-image: url("../img/关闭分屏2.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.boxs {
    position: relative;
    height: 100%;
}

.box_audio_bg {
    background: url("..//img/audio_bg2.png") no-repeat center;
    background-color: #333;
}


/* audio */


/*video默认全屏按钮*/

audio::-webkit-media-controls-fullscreen-button {
    display: none !important;
}


/*video默认aduio音量按钮*/

audio::-webkit-media-controls-mute-button {
    display: none !important;
}


/*video默认setting按钮*/

audio::-internal-media-controls-overflow-button {
    display: none !important;
}


/*腾讯云点播禁用firefox全屏、设置按钮*/

.trump-button[sub-component="fullscreen_btn"],
.trump-button[now="fullscreen"] {
    display: none !important;
}

.trump-button[sub-component="setting"] {
    display: none !important;
}


/*禁用video的controls（要慎重！不要轻易隐藏掉，会导致点击视频不能播放）*/

audio::-webkit-media-controls {
    display: none !important;
}


/* 双人执法 */

.videoOne {
    width: 100%;
    height: 100%;
}

.videoTwo {
    width: 30%;
    height: 30%;
    position: absolute;
    right: 0px;
    top: 15px;
}

.videoTwo_close {
    width: 30%;
    height: 30%;
    position: absolute;
    right: 0px;
    top: 15px;
    z-index: -100;
}

.video_two {
    width: 100%;
    height: 100%;
}

.video_two_close {
    width: 100%;
    height: 100%;
    z-index: -100;
}