<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>行政执法局-右</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/elementui/js/index.js"></script>
    <script src="/echarts/echarts.min.js"></script>
    <script src="/static/js/jslib/moment.js"></script>
    <script src="/static/js/jslib/md5Type2.js"></script>
    <style>
      .el-date-editor .el-range-input {
        color: #eee;
        width: 122%;
        background: transparent;
        font-size: 24px;
        z-index: 555;
      }
      .el-input__inner {
        height: 48px !important;
      }
      .el-date-range-picker__time-header .el-input__inner {
        background-color: transparent !important;
        border: none !important;
      }
      .el-picker-panel {
        color: #fff;
        background-color: #132c4e;
      }
      .el-date-editor .el-range-separator {
        color: #fff;
        line-height: 38px;
        font-size: 20px;
      }
      .el-month-table td.in-range div,
      .el-month-table td.in-range div:hover {
        background-color: #39537a;
      }
      .el-date-range-picker__content .el-date-range-picker__header div {
        font-size: 23px;
      }
      .el-month-table {
        font-size: 22px;
        white-space: nowrap;
      }
      .el-date-editor .el-range__icon {
        font-size: 20px;
        line-height: 39px;
      }
      .el-date-editor .el-range__close-icon {
        font-size: 20px;
        line-height: 40px;
      }
      .el-date-table td.in-range div,
      .el-date-table td.in-range div:hover,
      .el-date-table.is-week-mode .el-date-table__row.current div,
      .el-date-table.is-week-mode .el-date-table__row:hover div {
        background-color: #445368;
      }
      .el-picker-panel__icon-btn {
        color: #fff;
      }

      .yyItem {
        width: 470px;
        height: 100px;
        background-size: cover;
        background: url("/static/images/home/<USER>");
        text-align: center;
        line-height: 100px;
        font-size: 34px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
        margin-bottom: 50px;
        cursor: pointer;
      }

      .box2 {
          width: 1200px;
          height: 900px;
          padding: 20px 40px;
          box-sizing: border-box;
          overflow-y: scroll;
          display: flex;
          flex-wrap: wrap;
          justify-content: flex-start;
          align-items: flex-start;
          align-content: flex-start;
      }

      .xsq {
          width: 940px;
          background: url("/static/images/home/<USER>");
      }
    </style>
  </head>

  <body>
    <div id="right">
      <div
        class="hearder_h1 cursor"
        @click="ssyjJump"
      >
        <span>三色预警 <i class="click-i"></i></span>
      </div>
      <div class="hearder_h2"><span>预警情况</span></div>
      <div class="yjqk_box box">
        <div class="tabs" v-show="city == '金华市'">
          <div
            class="tab_item"
            v-for="(item,index) in tabList"
            @click="clickTab(index)"
            :class="tabIndex==index?'tab_active':''"
          >
            {{item}}
          </div>
        </div>
        <div style="position: absolute; right: 40px" v-show="year != '2023'">
          <el-date-picker
            v-model="datas"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="queryData"
          >
          </el-date-picker>
        </div>
        <!-- <el-select v-model="value" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select> -->
      </div>
      <div
        style="
          width: 100%;
          height: 470px;
          text-align: center;
          font-size: 30px;
          color: #ccc;
        "
        v-show="tabIndex==0&&chartData1.length==0 || tabIndex==1&&chartData2.length==0"
      >
        暂无数据
      </div>
      <div
        v-show=" tabIndex==0&&chartData1.length!=0 || tabIndex==1&&chartData2.length!=0"
        class="box"
        id="chart01"
        style="width: 1030px; height: 470px"
      ></div>
      <!-- <div class="hearder_h2"><span>预警处置</span></div>
      <div class="yjcz_box box">
        <img src="/static/images/xzzfj/yjcz.png" alt="" />
        <div class="yjcz_czl">
          <div v-for="(item,index) in czlList">
            <span class="s-font-30 s-c-white">{{item.name}}</span>
            <br />
            <span
              :class="index==0?'s-yellow':'s-blue'"
              style="position: relative; top: 20px"
            >
              <span class="s-font-65 s-sty font-xt">{{item.value}}</span>
              <span class="s-font-25">{{item.unit}}</span>
            </span>
          </div>
        </div>
      </div> -->
      <div class="hearder_h2"><span>三书一函</span></div>
      <div class="ssyh_box box">
        <div class="ssyh_item" v-for="(item,index) in ssyhList">
          <img :src="item.icon" alt="" width="110" height="100" />
          <div style="margin: 10px 0 0 0px">
            <div class="item_name">{{item.name}}</div>
            <div class="item_bottom">
              <span
                class="item_value s-font-45 xt_font"
                :class="index%2== 1?'s-blue':'s-yellow'"
                >{{item.value}}</span
              >
              <span
                class="item_unit s-font-25"
                :class="index%2==1?'s-blue':'s-yellow'"
                >{{item.unit}}</span
              >
            </div>
          </div>
        </div>
      </div>
      <div class="hearder_h1 cursor" @click="changePage(title == '县级中心'?'xjzx':'yyjc')">
        <span>{{title}} <i class="click-i"></i></span>
      </div>
      <div class="yyjc_box box2">
<!--        <el-carousel-->
<!--          indicator-position="outside"-->
<!--          height="700px"-->
<!--          :autoplay="false"-->
<!--        >-->
<!--          <el-carousel-item v-for="(el,i) in newYyjcList" :key="i">-->
<!--            <div class="yyjc">-->
<!--              <div-->
<!--                class="yyjc_item"-->
<!--                v-for="(item,index) in newYyjcList[i]"-->
<!--                @click="openWebWin(item)"-->
<!--              >-->
<!--                <img-->
<!--                :src=`/static/images/${!['浦江县防违控违综合信息管理系统'].includes(item.name)?'yyjc/'+item.name+'.png':'xzzfj/tmp.gif'}`-->
<!--                alt="" width="385" height="220" />-->
<!--                <div class="yyjc_item_title">{{item.name}}</div>-->
<!--              </div>-->
<!--            </div>-->
<!--          </el-carousel-item>-->
<!--        </el-carousel>-->
        <div class="yyItem" v-for="(el,i) in yyjcList" :key="i" @click="xjzxItemClick(el)" :class="{xsq:city != '金华市'}">
          {{el.name}}
        </div>
      </div>
    </div>
    <script>
      window.parent.eventbus &&
        window.parent.eventbus.on("cityChange", (city) => {
          let filtName =
            city == "金义新区"
              ? "金东区"
              : city == "金华开发区"
              ? "开发区"
              : city;
          vm.city = filtName
          vm.SetTitle()
          vm.initApi(filtName,localStorage.getItem("year"))
        });

      window.parent.eventbus &&
      window.parent.eventbus.on("yearChange", (year) => {
        vm.year = year
        vm.initApi(localStorage.getItem("city"),year);
      });
      let vm = new Vue({
        el: "#right",
        data: {
          year:"",
          datas: [
            new Date().getFullYear() + "-01-01",
            moment(new Date()).format("YYYY-MM-DD"),
          ],
          tabIndex: 0,
          tabList: ["按区域", "按领域"],
          value: null,
          options: [
            {
              value: "1",
              label: "月度",
            },
            {
              value: "2",
              label: "年度",
            },
          ],
          chartData1: [],
          chartData2: [],
          czlList: [
            {
              name: "处置总量",
              value: 25,
              unit: "个",
            },
            {
              name: "处置率",
              value: 98,
              unit: "%",
            },
          ],
          yyjcList: [
            {
              title: "XXXX应用",
              url: "tmp.gif",
            },
            {
              title: "XXXX应用",
              url: "tmp.gif",
            },
            {
              title: "XXXX应用",
              url: "tmp.gif",
            },
            {
              title: "XXXX应用",
              url: "tmp.gif",
            },
            {
              title: "XXXX应用",
              url: "tmp.gif",
            },
            {
              title: "XXXX应用",
              url: "tmp.gif",
            },
            {
              title: "XXXX应用",
              url: "tmp.gif",
            },
            {
              title: "XXXX应用",
              url: "tmp.gif",
            },
          ],
          ssyhList: [
            {
              icon: "/static/images/xzzfj/ssyh.png",
              name: "执法报告书",
              value: 0,
              unit: "份",
            },
            {
              icon: "/static/images/xzzfj/ssyh.png",
              name: "执法建议书",
              value: 0,
              unit: "份",
            },
            {
              icon: "/static/images/xzzfj/ssyh.png",
              name: "执法督办书",
              value: 0,
              unit: "份",
            },
            {
              icon: "/static/images/xzzfj/ssyh.png",
              name: "风险提示函",
              value: 0,
              unit: "份",
            },
          ],
          city: localStorage.getItem("city"),
          title:"",
          token: null
        },
        computed: {
          newYyjcList() {
            let newArr = [];
            for (let i = 0; i < this.yyjcList.length; i += 4) {
              newArr.push(this.yyjcList.slice(i, i + 4));
            }
            return newArr;
          }
        },
        created() {
          this.ssyjToken()
        },
        mounted() {
          this.SetTitle();
          this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
        },
        methods: {
          xjzxItemClick(el) {
            if (el.key) {
              top.xzzfzx.cityFun(top.xzzfzx.cityList.find(item => item.name == el.city).name)
            } else if (el.isDddl) {
              //是否单点登录
              if (el.name == "磐安县行政执法指挥中心") {
                $.ajax({
                  type: "POST",
                  url: "/paxmd/sso/getToken",
                  data: JSON.stringify({
                    appId: "fc06313f1c824351ab855604366f27bc",
                    appSecret: "d83f902a2e344c41bdd3e3a7a8eea9f3",
                    timeStamp: Date.now(),
                    sign: md5("appId=fc06313f1c824351ab855604366f27bc&appSecret=d83f902a2e344c41bdd3e3a7a8eea9f3&timeStamp=" + Date.now() + "&key=message").toUpperCase(),
                  }),
                  headers: {
                    "Content-Type": "application/json"
                  },
                  dataType: "json",
                  success: function (res) {
                    console.log(res.data.token);
                    window.open(baseURL.url + "/paxmd/sso/login?token=" + res.data.token + "&userName=pazhzx&loginType=CABIN")
                  },
                  error: function (jqXHR, textStatus, errorThrown) {
                    console.error("Error:", textStatus, errorThrown);
                  }
                });
              }
            } else {
              this.openWebWin(el)
            }
          },
          SetTitle() {
            this.title = localStorage.getItem("city") == '金华市'?'县级中心':'应用集成'
          },
          getAreaCode(name) {
            let citylist = [
              {name:'金华市',value:'330700'},
              {name:'婺城区',value:'330702'},
              {name:'金东区',value:'330703'},
              {name:'武义县',value:'330723'},
              {name:'浦江县',value:'330726'},
              {name:'兰溪市',value:'330781'},
              {name:'义乌市',value:'330782'},
              {name:'东阳市',value:'330783'},
              {name:'磐安县',value:'330727'},
              {name:'开发区',value:'330700'},
              {name:'永康市',value:'330784'},
            ];
            return citylist.find(item => item.name == name).value
          },
          initApi(city,year) {
            this.initssyj(year)
            if (city == "金华市") {
              $get("xjzx.json").then((res) => {
                this.yyjcList = city == '金华市'?res:res.filter(item => item.city == city);
              });
            } else {
              $api("/xzzfj_yyji_list", { area: city }).then(res => {
                this.yyjcList = res.map(item => {return {
                  name: item.name,
                  url: item.url,
                  city: item.area,
                  img: item.picture_url
                }})
              })
            }
          },
          //初始化三色预警数据
          initssyj(year) {
            this.datas = year == "2023"?["2023-01-01","2023-12-31"]:[year + "-01-01",moment(new Date()).format("YYYY-MM-DD")]
            this.queryData()
            // this.getChart02("chart01", this.chartData2);
            // this.city == '金华市'?this.queryData():this.getChart02("chart01", this.chartData2);
          },
          ssyjToken() {
            $api2Get("/token/getTokenInfo1",{ type: 'dashboard', module: 'sy' }).then(res => {
              if (res.data.code == 200) {
                this.token = res.data.data.url.split("?")[1].split("&")[0].replace('token=', '')
                this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
              }
            })
          },
          //三色预警跳转
          ssyjJump() {
            const that = this
            // $api2Get("/token/getTokenInfo",{jmppage:"home"}).then(res => {
            $api2Get("/token/getTokenInfo1",{ type: 'dashboard', module: 'sy' }).then(res => {
              if (res.data.code == 200) {
                this.token = res.data.data.url.split("?")[1].split("&")[0].replace('token=', '')
                window.open(res.data.data.url);
              }
            })
          },
          queryData() {
            //  日期选择
            this.getYJQK();
            axios({
              method: "get",
              url: baseURL.url + "/statistics/letters/statistics-by-type",
              params: {
                startTime: this.datas[0],
                endTime: this.datas[1],
                adcode: this.city == '金华市'?"":this.getAreaCode(this.city)
              },
              headers: {
                Authorization: "Bearer " + this.token,
              }
            }).then((res) => {
              let result = res.data.data;
              this.ssyhList[0].value = result.filter(
                (a) => a.type == "ENFORCEMENT_REPORT"
              )[0].total;
              this.ssyhList[1].value = result.filter(
                (a) => a.type == "ENFORCEMENT_PROPOSAL"
              )[0].total;
              this.ssyhList[2].value = result.filter(
                (a) => a.type == "ENFORCEMENT_SUPERVISION"
              )[0].total;
              this.ssyhList[3].value = result.filter(
                (a) => a.type == "RISK_WARNING"
              )[0].total;
            });
          },
          getYJQK() {
            let a1 = axios({
              method: "get",
              url:
                // baseURL.url + "/statistics/warnings/statistics-by-institution",
                baseURL.url + "/statistics/warnings/statistics-by-district",
              params: {
                startTime: this.datas[0],
                endTime: this.datas[1],
                adcode: this.getAreaCode(this.city)
              },
              headers: {
                Authorization: "Bearer " + this.token,
              }
            }).then((res) => {
              let result = res.data.data;
              this.chartData1 = [];
              result.forEach((item) => {
                let obj = {
                  name: item.institution.name,
                  value1: item.statistics.find((el) => {
                    return el.level == "BLUE";
                  }).total,
                  value2: item.statistics.find((el) => {
                    return el.level == "ORANGE";
                  }).total,
                  value3: item.statistics.find((el) => {
                    return el.level == "RED";
                  }).total,
                };
                this.chartData1.push(obj);
              });
            });
            let a2 = axios({
              method: "get",
              url: baseURL.url + "/statistics/warnings/sz/statistics-by-domain",
              params: {
                startTime: this.datas[0],
                endTime: this.datas[1],
                adcode: this.getAreaCode(this.city)
              },
              headers: {
                Authorization: "Bearer " + this.token,
              }
            }).then((res) => {
              let result = res.data.data;
              this.chartData2 = [];
              result.forEach((item) => {
                let obj = {
                  name: item.domain.name,
                  value1: item.statistics.find((el) => {
                    return el.level == "BLUE";
                  }).total,
                  value2: item.statistics.find((el) => {
                    return el.level == "ORANGE";
                  }).total,
                  value3: item.statistics.find((el) => {
                    return el.level == "RED";
                  }).total,
                };
                this.chartData2.push(obj);
              });
            });
            axios.all([a1, a2]).then((res) => {
              this.city == '金华市'?this.clickTab(this.tabIndex):this.getChart02("chart01", this.chartData2);
            });
          },
          openWebWin(item) {
            let that = this;
            let name = item.name;
            let url = item.url;
            if (url == "") {
              this.$message.error("暂无该大屏信息");
              return false;
            }

            if (url == "currentUrlProject") {
              $api2Post("/xzzfj/mdUser/getUrl?area="+item.city).then(res => {
                top.window.location = res.data.data.url
              })
            } else if (name == "金华永康市永城数治系统") {
              // 金华永康市永城数治系统
              $.ajax({
                type: "get",
                url:
                  baseURL.url +
                  baseURL.admApi +
                  "/mis/system/linkdetails/tempAuthCode?appId=330700_tsapp_056&userId=" +
                  sessionStorage.getItem("role"),
                dataType: "json",
                headers: {
                  Authorization: sessionStorage
                    .getItem("Authorization")
                    .replaceAll('"', ""),
                },
                success: function (res) {
                  if (res.code === 200) {
                    that.openHtmlByMode(
                      url + "&csdnCode=" + res.tempAuthCode,
                      3840,
                      2160
                    );
                  } else {
                    this.$message.error("暂无权限");
                  }
                },
              });
            } else {
              let type = url.substring(0, 5).indexOf("s") != -1 ? true : false;
              // if (type) {
              //   window.parent.lay.openWinHtml("3840", "2160", url);
              // } else {
              that.openHtmlByMode(url, 3840, 2160);
              // }
            }
          },
          openHtmlByMode(url, width, higth) {
            let moveLeft = (7680 - width) / 2;
            let moveHigth = (2160 - higth) / 2;
            window.open(
              url,
              "项目接入系统",
              "directories=no, location=no, toolbar=no,scrollbars=yes, resizable=yes, height=" +
                higth +
                ", width=" +
                width +
                ", top=" +
                moveHigth +
                ", left=" +
                moveLeft +
                ""
            );
          },
          changePage(page) {
              window.parent.xzzfzx.header_title = page == "yyjc"?"应用集成":"县级中心";
              window.parent.fadeIfr.run("backBtnShow");
              window.parent.lay.closeIframeByNames([
                "left",
                "right",
                "home_bottom",
                "home_top",
              ]);
              window.parent.lay.openIframe({
                type: "openIframe",
                name: `${page}_index`,
                id: `${page}_index`,
                src:
                  baseURL.url + `/static/citybrain/yyjc/${page}_index.html`,
                width: "100%",
                height: "100%",
                left: "0px",
                top: "0px",
                zIndex: "666",
              });
          },
          clickTab(index) {
            this.tabIndex = index;
            if (index == 0) {
              this.getChart02("chart01", this.chartData1);
            } else {
              this.getChart02("chart01", this.chartData2);
            }
          },
          getChart02(id, chartData) {
            let myEc = echarts.init(document.getElementById(id));
            let xdata = [];
            let ydata = [[], [], []];
            chartData.forEach((item) => {
              xdata.push(item.name);
              ydata[0].push(item.value1);
              ydata[1].push(item.value2);
              ydata[2].push(item.value3);
            });
            let legend = ["蓝色预警", "黄色预警", "红色预警"];
            let color = ["76,152,251", "172,171,52", "245,102,121"];
            let option = {
              tooltip: {
                trigger: "axis",
                backgroundColor: "rgba(51, 51, 51, 0.7)",
                borderWidth: 0,
                axisPointer: {
                  type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                },
                textStyle: {
                  color: "white",
                  fontSize: "24",
                },
              },
              grid: {
                left: "5%",
                right: "5%",
                top: "15%",
                bottom: "0%",
                containLabel: true,
              },
              legend: {
                top: 10,
                left: "center",
                itemWidth: 16,
                itemHeight: 16,
                itemGap: 50,
                textStyle: {
                  fontSize: 24,
                  color: "#fff",
                  padding: [3, 0, 0, 0],
                },
                // data: legend,
              },
              xAxis: [
                {
                  type: "category",
                  data: xdata,
                  axisLine: {
                    lineStyle: {
                      color: "rgb(119,179,241,.4)", // 颜色
                      width: 1, // 粗细
                    },
                  },
                  axisTick: {
                    show: false,
                  },
                  axisLabel: {
                    interval: 0,
                    rotate: 30,
                    textStyle: {
                      color: "#D6E7F9",
                      fontSize: 20,
                    },
                  },
                },
              ],
              yAxis: [
                {
                  name: "",
                  type: "value",
                  nameTextStyle: {
                    fontSize: 24,
                    color: "#D6E7F9",
                    padding: 5,
                  },
                  splitLine: {
                    lineStyle: {
                      color: "rgb(119,179,241,.4)",
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 24,
                      color: "#D6E7F9",
                    },
                  },
                },
              ],
              series: [],
            };
            for (var i = 0; i < legend.length; i++) {
              option.series.push({
                name: legend[i],
                type: "bar",
                stack: "总量",
                barWidth: "40%",
                label: {
                  show: false,
                  position: "insideRight",
                },
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: "rgba(" + color[i] + ",0.99)",
                      },
                      {
                        offset: 1,
                        color: "rgba(" + color[i] + ",0)",
                      },
                    ]),
                    barBorderRadius: 4,
                  },
                },
                data: ydata[i],
              });
            }
            myEc.setOption(option);
            myEc.getZr().on("mousemove", (param) => {
              myEc.getZr().setCursorStyle("default");
            });
          },
        },
      });
    </script>
  </body>
</html>
<style>
  /* el警告信息 */
  .el-message__content {
    font-size: 30px !important;
  }
  .box {
      height: 900px;
      padding: 20px 40px;
      box-sizing: border-box;
      display: flex;
      /* flex-shrink: 1; */
      flex-direction: column;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: flex-start;
  }

  .yjqk_box {
    height: 59px;
    position: relative;
    display: flex;
    justify-content: space-between;
  }

  .tabs {
    width: 350px;
    display: flex;
    justify-content: space-around;
  }

  .tab_item {
    width: 243px;
    height: 59px;
    line-height: 59px;
    font-size: 36px;
    color: #abceef;
  }

  .tab_active {
    color: #fff;
    font-style: italic;
    font-weight: bold;
    background: url("/static/images/xzzfj/tab_bg.png");
    background-size: 100% 100%;
  }

  .yjcz_box {
    width: 100%;
    display: flex;
    justify-content: space-around;
    margin: 15px 0px;
  }

  .yjcz_czl {
    display: flex;
    justify-content: space-around;
    width: 749px;
    line-height: 60px;
    text-align: center;
    background: url("/static/images/xzzfj/czl_bg.png") no-repeat 0 0;
  }
  .font-xt {
    font-family: DINCondensed;
  }
  .s-yellow {
    color: #eed252;
  }

  .s-blue {
    color: #34dfe3;
  }
  .yyjc {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-evenly;
    flex-wrap: wrap;
  }
  .yyjc_item {
    width: 413px;
    height: 300px;
    background: url("/static/images/xzzfj/yyjc_bg.png") no-repeat 0 0;
  }
  .yyjc_item > img {
    margin: 11px 13px;
  }
  .yyjc_item_title {
    width: 396px;
    height: 57px;
    padding-top: 10px;
    line-height: 43px;
    color: #fff;
    font-size: 32px;
    text-align: center;
    background: url("/static/images/xzzfj/header_bg.png");
  }

  .ssyh_box {
    width: 100%;
    height: 270px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 20px 90px;
  }

  .ssyh_item {
    display: flex;
    width: 45%;
    margin-bottom: 10px;
  }

  .item_name {
    width: 200px;
    height: 35px;
    font-size: 30px;
    line-height: 35px;
    color: #d1d6df;
    font-style: italic;
  }
  .xt_font {
    font-family: DINCondensed;
    font-style: italic;
  }

  /*  */
  .el-scrollbar {
    overflow: hidden !important;
  }
  .el-scrollbar__wrap {
    overflow: hidden !important;
    margin-bottom: 0px !important;
    margin-right: 0px !important;
  }
  .el-input {
    width: 200px;
    font-size: 30px;
  }

  .el-input__inner {
    height: 60px;
    background-color: #132c4e;
    border: 1px solid #afdcfb;
    color: #fff;
  }

  .el-select-dropdown__item {
    font-size: 30px;
    height: 50px;
    color: #cfcfd6 !important;
    line-height: 50px;
  }

  .el-select-dropdown {
    background-color: #132c4e;
    border: 1px solid #afdcfb;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #27508f !important;
  }
  .el-carousel__button {
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }
  .el-carousel__arrow {
    width: 50px;
    height: 50px;
    font-size: 30px;
    background-color: rgba(31, 45, 61, 0.5);
  }
</style>
