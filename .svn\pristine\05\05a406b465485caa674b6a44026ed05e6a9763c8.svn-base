<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>舆情详情弹窗</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <link rel="stylesheet" href="/static/css/viewCss/commonObjzhdd.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/Vue/vue-count-to.min.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/static/js/jslib/Emiter.js"></script>
    <script src="/elementui/js/index.js"></script>
    <style>
        .el-message-box__title {
            font-size: 38px;
            background: linear-gradient(to bottom, #6ca9fc, #ffffff);
            -webkit-background-clip: text;
            color: transparent;
            font-weight: 700;
            font-style: italic;
        }
        .el-message-box__message p {
            line-height: 100px;
        }
        .el-message-box__content {
            color: #fff;
        }
        .el-message-box {
            width: 650px;
            box-shadow: inset 0px 0px 16px 0px rgba(0, 145, 255, 1);
            background-color: #021037;
            border-radius: 21px;
            border: 1px solid#155181;
        }
        .el-message-box__headerbtn {
            font-size: 30px;
        }
        .el-message-box__headerbtn .el-message-box__close {
            color: #fff;
        }
        .el-message-box__headerbtn {
            font-size: 38px !important;
        }

        .el-button--small {
            font-size: 28px;
            padding: 10px 33px;
            border-radius: 14px;
        }
      ul,
      ul li {
        list-style: none;
      }

      .rwgz-tc {
        width: 1500px;
        height: 1000px;
        background: url("/static/images/zhdd/bg_panel.png") no-repeat;
        background-size: 100% 100%;
      }

      .rw-title {
        padding: 46px 3% 0;
        width: 95%;
        height: 60px;
        line-height: 60px;
      }

      .close {
        background: url("/static/images/zhdd/close.png") no-repeat;
        width: 34px;
        height: 34px;
      }

      .content {
        width: 93%;
        height: 83%;
        box-sizing: border-box;
        margin: 1% auto;
      }

      .ql-align-center {
        text-align: center;
      }

      .ql-align-right {
        text-align: right;
      }

      .ql-indent-1 {
        padding-left: 3em;
      }

      .ql-indent-2 {
        padding-left: 6em;
      }

      .ql-indent-3 {
        padding-left: 9em;
      }

      .ql-indent-4 {
        padding-left: 12em;
      }

      .ql-indent-5 {
        padding-left: 15em;
      }

      .ql-indent-6 {
        padding-left: 18em;
      }

      .ql-indent-7 {
        padding-left: 21em;
      }

      .ql-indent-8 {
        padding-left: 24em;
      }

      .ql-indent-1 img {
        width: 100%;
      }

      img {
        width: 100%;
      }

      .content > div {
        margin-bottom: 1%;
      }

      .nr {
        height: 637px;
        display: inline-block;
        width: 91%;
      }
      .buttons {
        width: 236px;
        height: 80px;
        background: url("/static/images/zhdd/btn.png");
        background-size: 100% 100%;
        line-height: 76px;
        color: #9fc9e9;
        cursor: pointer;
        font-size: 28px;
        text-align: center;
        position: absolute;
        top: 90px;
        right: 90px;
      }
    </style>
  </head>

  <body>
    <div id="yqxq" class="rwgz-tc">
      <div class="rw-title flex-between">
        <div class="fs-44 text-mid-yellow" id="rwTitle">舆情详情</div>
        <div class="close cursor" @click="close"></div>
      </div>
      <div class="title text-center fs-40" id="title"></div>
      <div class="content" id="content">
        <div class="buttons" @click="openMessage" v-show="detailObj.state==1">
          舆情处置
        </div>
        <div class="flex-align-center">
          <div class="text-left text-mid-blue">标题：</div>
          <div class="text_right" style="width: 75%">{{detailObj.bt}}</div>
        </div>
        <div class="flex-align-center">
          <div class="flex-align-center" style="width: 50%">
            <div class="text-left text-mid-blue">时间：</div>
            <div class="" id="sj">{{detailObj.sj}}</div>
          </div>
          <div class="flex-align-center" style="width: 50%">
            <div class="text-left text-mid-blue">部门：</div>
            <div class="" id="bm">{{detailObj.bm}}</div>
          </div>
        </div>
        <div class="flex-align-center">
          <div class="flex-align-center" style="width: 50%">
            <div class="text-left text-mid-blue">风险等级：</div>
            <div class="" id="fxdj">{{detailObj.fxdj}}</div>
          </div>
          <div class="flex-align-center" style="width: 50%">
            <div class="texf-left text-mid-blue">标签：</div>
            <div class="" id="bq">{{detailObj.bq}}</div>
          </div>
        </div>
        <div class="flex-align-center">
          <div class="text-left text-mid-blue">内容：</div>
          <div class="scrollbar nr" id="nr">
            <p>
              <img :src="detailObj.nr" alt="" />
            </p>
          </div>
        </div>
      </div>
    </div>
    <script></script>
    <script>
      let vm = new Vue({
        el: "#yqxq",
        data: {
          gjType: null,
          detailObj: {
            bm: "",
            bq: "",
            bt: "",
            fxdj: "",
            nr: "",
            sj: "",
            state: null,
          },
          yqMsg: null,
        },
        computed: {},

        mounted() {
          let that = this;
          // that.getDetail("1668583194032738305");
          window.addEventListener("message", function (event) {
            //子获取父消息
            let newData;
            if (typeof event.data == "object") {
              newData = event.data;
            } else {
              newData = JSON.parse(event.data.argument);
            }
            that.yqMsg = newData;
            that.getDetail(newData.id);
          });
        },
        methods: {
          openMessage() {
            // window.parent.frames["zhdd_right"].postMessage(
            //   {
            //     舆情中心: {
            //       id: this.yqMsg.id,
            //       yqmsg: this.yqMsg.text,
            //       state: this.detailObj.state,
            //       dept_name:
            //         (this.yqMsg.dept_name.split(",")[0] &&
            //           this.yqMsg.dept_name.split(",")[0]) ||
            //         "-",
            //       type: "接收告警",
            //     },
            //   },
            //   "*"
            // );
            this.openDialog({
              id: this.yqMsg.id,
              yqmsg: this.yqMsg.text,
              state: this.detailObj.state,
              dept_name:
                (this.yqMsg.dept_name.split(",")[0] &&
                  this.yqMsg.dept_name.split(",")[0]) ||
                "-",
              type: "接收告警",
            })
          },
          openDialog(item) {
            if (item.state == "0") return;
            let that = this;
            // that.gjType = null;
            this.$alert(
              `<form action="" style="font-size: 30px;margin: 20px 0px;">
                  <input type="radio" name="sex" value="一键通知" onclick="vm.gjType='一键通知'" style="width: 20px;height: 20px;">
                  <span style="margin-left:5px">一键通知</span>
                  <input type="radio" name="sex" value="一键调度" onclick="vm.gjType='一键调度'" style="width: 20px;height: 20px;">
                  <span style="margin-left:5px">一键调度</span>
                  <input type="radio" name="sex" value="取消预警" onclick="vm.gjType='取消预警'" style="width: 20px;height: 20px;">
                  <span style="margin-left:5px">取消预警</span>
                </form>`,
              "请选择接受告警类型",
              {
                dangerouslyUseHTMLString: true,
                confirmButtonText: "确定",
                callback: (active) => {
                  if (active == "cancel") {
                    // that.gjType = null;
                  } else if (active == "confirm") {
                    if (that.gjType == "一键通知") {
                      that.yjtz(item);
                    } else if (that.gjType == "一键调度") {
                      that.yjdd(item);
                    } else if (that.gjType == "取消预警") {
                      if (item.id && item.yqmsg) {
                        that.getYq(item);
                      } else {
                        that.getSOS(item);
                      }
                    }
                  }
                },
              }
            );
          },
          yjtz(item) {
            window.parent.lay.openIframe({
              type: "openIframe",
              name: "zhddNotice",
              id: "zhddNotice",
              src:
                baseURL.url + "/static/citybrain/commonts/zhdd/zhddNotice.html",
              left: "1280px",
              top: "575px",
              width: "1300px",
              height: "800px",
              zIndex: "666",
              argument: {
                type: "openyjtz",
                city: this.city,
                data: item,
              },
            });
          },
          yjdd(item) {
            window.parent.lay.openIframe({
              type: "openIframe",
              name: "zhddDispatch",
              id: "zhddDispatch",
              src:
                baseURL.url +
                "/static/citybrain/commonts/zhdd/zhddDispatch1.html",
              left: "1020px",
              top: "380px",
              width: "1800px",
              height: "1000px",
              zIndex: "666",
              argument: {
                type: "openyjdd",
                city: this.city,
                data: item,
              },
            });
          },
          getYq(item) {
            let this_ = this;
            axios({
              method: "post",
              url:
                baseURL.url + "/jhyjzh-server/screen_api/xzzfyq_state_update",
              data: { id: item.id, status: 0 },
            }).then(function (res) {
              if (res.data.data == 1) {
                this_.$message({
                  message: "舆情上报成功",
                  type: "success",
                });
                window.parent.frames["yqxq"] &&
                window.parent.lay.closeIframeByNames(["yqxq"]);
                window.parent.frames["zhdd_bottom"] &&
                window.parent.frames["zhdd_bottom"].vm.initApi(
                  window.parent.frames["zhdd_bottom"].vm.city
                );
              }
            });
          },
          // sos接收告警
          getSOS(item) {
            let this_ = this;
            axios({
              method: "get",
              url:
                baseURL.url +
                "/jhyjzh-server/screen_api/zhdd/xzzf/updateAlarmStatus",
              params: { id: item.id, county: item.city },
            }).then(function (res) {
              if (res.data.data == 1) {
                this_.$message({
                  message: "接受告警成功",
                  type: "success",
                });
                this_.zfryPopClose();
                this_.init(this_.city);
              }
            });
          },
          //获取详情数据
          getDetail(id) {
            let that = this;
            axios({
              method: "get",
              url:
                baseURL.url +
                "/jhyjzh-server/screen_api/zhddzx/zhddzxLeft005-1",
              params: {
                id: id,
                // id: '1573501504130781186'
              },
            }).then(function (data) {
              let res = data.data.data;
              console.log(this.detailObj);
              let str = JSON.stringify(res.nr)
                .split('"')[2]
                .slice(0, -1)
                .replace(/&amp;/g, "&");
              that.detailObj.bm = res.bm;
              that.detailObj.bq = res.bq;
              that.detailObj.bt = res.bt;
              that.detailObj.fxdj = res.fxdj;
              that.detailObj.nr = str; //res.nr
              that.detailObj.sj = res.sj;
              that.detailObj.state = res.state;
              console.log(that.detailObj.nr);
            });
          },
          close() {
            window.parent.lay.closeIframeByNames(["yqxq"]);
          },
        },
      });
    </script>
  </body>
</html>
