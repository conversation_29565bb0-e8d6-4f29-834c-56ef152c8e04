<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>指挥调度弹窗</title>
    <link rel="shortcut icon" href="#" />
    <script src="/Vue/vue.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/elementui/js/index.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <!-- <script src="https://g.alicdn.com/gdt/jsapi/1.9.22/index.js"></script> -->
    <script src="/static/js/jslib/s.min.vue.js"></script>
    <!-- <link href="/static/js/layui/css/layui.css" rel="stylesheet" />
    <link href="/static/js/layui/css/formSelects-v4.css" rel="stylesheet" /> -->
    <link href="/static/css/viewCss/commonObjzhdd.css" rel="stylesheet" />

    <style type="text/css" scoped>
      [v-cloak] {
        display: none;
      }

      body {
        margin: 0;
        padding: 0;
      }

      #zhdddialog {
        width: 1300px;
        height: 800px;
        position: relative;
        background-image: url("/static/images/zhdd/bg_panel.png");
        background-size: 100% 100%;
      }

      .title {
        font-size: 36px;
        padding: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .close {
        width: 46px;
        height: 78px;
        font-size: 60px;
        color: #fff;
        font-weight: 600;
        cursor: pointer;
      }

      .content {
        width: 100%;
      }

      /*elementui-tree*/
      .el-tree {
        background: unset;
      }

      .el-tree-node__content:hover {
        background-color: #1c4d65;
      }

      .el-tree-node__label {
        font-size: 26px;
      }

      .el-tree-node.is-current > .el-tree-node__content {
        background-color: #1c4d65 !important;
      }

      .el-tree-node:focus > .el-tree-node__content {
        background-color: #4a9de7 !important;
        color: #fff !important;
      }

      /* 下面按钮的样式 */
      .btns-box {
        width: 100%;
        display: flex;
        justify-content: center;
      }

      .btns-box > div {
        display: inline-block;
        padding: 5px 50px;
        background: linear-gradient(0deg, #387af0, #003ca6);
        border: 1px solid;
        border-image: linear-gradient(-32deg, #359cf8, #afdcfb) 1 1;
        border-radius: 10px;
        margin: 0 20px;
      }

      /* el-form里面的样式 */
      .el-form {
        padding: 0 50px;
      }

      .el-form-item__label {
        font-size: 28px;
        color: #fff;
        text-align: left;
      }

      .el-select {
        width: 600px;
      }

      .el-input {
        font-size: 26px;
      }

      .el-input__inner {
        height: 55px;
        line-height: 55px;
        background-color: #052347;
        border-color: #314662 !important;
        border-radius: 5px;
        color: #fff;
      }

      .el-input--suffix .el-input__inner {
        padding-right: 50px;
      }

      .el-select .el-input .el-select__caret {
        color: #fff;
        font-size: 26px;
        margin-right: 10px;
      }

      .el-select-dropdown {
        background-color: #052347;
        border-color: #314662 !important;
      }

      .el-select-dropdown__item.hover,
      .el-select-dropdown__item:hover {
        border-color: #409eff;
        background-color: #052347;
        color: #409eff !important;
      }

      .el-select-dropdown__item.selected {
        color: #409eff;
        font-weight: normal;
      }

      .el-select-dropdown__item {
        font-size: 26px;
        color: #fff;
        height: 50px;
        line-height: 50px;
      }

      .popper__arrow {
        display: none !important;
      }

      .el-textarea__inner {
        border-color: #314662;
        background-color: #052347;
        font-size: 26px;
        color: #fff;
      }

      .el-textarea__inner:hover {
        border-color: #052347 !important;
      }

      .el-textarea__inner:focus {
        outline: none !important;
        border-color: #052347 !important;
      }

      .el-cascader .el-input .el-icon-arrow-down {
        font-size: 26px;
        color: #fff;
        line-height: 26px;
      }

      .el-radio__label {
          color: #ffffff;
      }

      .el-cascader {
        width: 600px;
        height: 50px;
        line-height: 50px;
      }

      .el-cascader .el-input__suffix {
        margin-right: 20px;
        margin-top: 2px;
      }

      .el-scrollbar__wrap {
        overflow-y: scroll;
      }

      .el-scrollbar__wrap::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 3px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
      }

      .el-scrollbar__wrap::-webkit-scrollbar-thumb {
        border-radius: 6px;
        background: #314662;
        height: 8px;
      }

      .el-cascader-menu {
        min-width: 200px;
        background-color: #052347;
        color: #fff;
        border: 1px solid #314662 !important;
        padding: 5px 10px;
      }

      .el-cascader-menus {
        width: 600px;
        display: flex;
        flex-wrap: wrap;
      }

      .el-cascader-menu__item {
        font-size: 26px;
      }

      .el-tag .el-icon-close {
        font-size: 26px;
        height: 25px;
        width: 26px;
        line-height: 26px;
      }

      .el-input__icon {
        height: 25px;
        top: 33%;
        right: 0px;
        position: absolute;
      }

      .el-tag--small {
        height: 35px;
        line-height: 35px;
      }

      .el-tag {
        font-size: 25px;
      }

      .team_input {
        height: 55px !important;
        line-height: 55px !important;
      }

      .team_input .el-input__inner {
        height: 55px !important;
        line-height: 55px !important;
      }

      .el-cascader__tags {
        width: 100%;
        max-height: 81px;
        overflow: hidden;
        overflow-y: scroll;
        top: 44%;
      }

      .el-cascader__tags::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 6px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
      }

      .el-cascader__tags::-webkit-scrollbar-thumb {
        border-radius: 6px;
        background: #314662;
        height: 8px;
      }

      .el-button {
        font-size: 35px;
        border: 1px solid #2450b6;
        color: #c6bdbd;
        background: transparent;
        line-height: 0;
        padding: 4px !important;
      }
      .el-cascader-node {
          font-size: 30px;
          margin-bottom: 10px;
          padding: 0 30px 0 0 !important;
      }
      .el-cascader-node:not(.is-disabled):focus,
      .el-cascader-node:not(.is-disabled):hover {
        background: #094194ab;
      }

      .el-cascader-panel .el-cascader-menu:nth-child(2) {
        /*min-width: 350px !important;*/
      }

      .el-cascader-panel .el-cascader-menu:nth-child(1) li .el-checkbox {
        display: none !important;
      }

      .el-cascader-panel
        .el-cascader-menu:nth-child(3)
        li
        .el-cascader-node__postfix {
        /*display: none !important;*/
      }

      .el-avatar,
      .el-cascader-panel,
      .el-radio,
      .el-radio--medium.is-bordered .el-radio__label,
      .el-radio__label {
        font-size: 23px !important;
      }

      .el-cascader__dropdown {
        /*top: 320 !important;*/
        background: transparent !important;
        border: 0;
      }

      .el-cascader-panel {
          display: flex;
          justify-content: center;
          flex-wrap: wrap;
          width: 1300px;
          height: fit-content;
          position: relative;
          /*right: 50px;*/
      }

      .el-cascader-menu__wrap {
          height: 270px;
      }

      .el-checkbox__inner {
        width: 20px;
        height: 20px;
      }

      .el-checkbox__inner::after {
        height: 13px;
        left: 7px;
      }

      .el-textarea__inner {
        font-family: "YouSheBiaoTiHei";
      }

      .el-cascader-node__label {
          width: 200px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
      }
    </style>
  </head>

  <body>
    <div id="zhdddialog" v-cloak>
      <div class="title">
        <span class="s-c-yellow-gradient1">一键通知</span>
        <div class="close" @click="close()">×</div>
      </div>

      <div class="content">
        <el-form ref="form" :model="form" label-width="250px">
          <el-form-item label="任务来源 :">
            <div>
              <el-input
                style="width: 80%"
                v-model="form.from"
                placeholder="请输入任务来源"
              ></el-input>
            </div>
          </el-form-item>
          <el-form-item label="响应级别 :">
            <el-select v-model="form.region" placeholder="请选择级别" style="width: 80%">
              <el-option
                label="三级响应(一般)"
                value="三级响应(一般)"
              ></el-option>
              <el-option
                label="二级响应(中级)"
                value="二级响应(中级)"
              ></el-option>
              <el-option
                label="一级响应(最高级)"
                value="一级响应(最高级)"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="'调度方式'">
            <el-radio-group v-model="labelName">
              <el-radio :label="'1'">扁平指挥</el-radio>
              <el-radio :label="'2'" v-show="adminCity == '金华市'">层级指挥</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-cascader
            v-if="labelName == '1'"
            style="width: 80%;margin-top: 15px"
            class="team_input"
            v-model="form.list"
            :props="props"
            :show-all-levels="false"
            @change="changeList"
          >
            <template slot-scope="{ node, data }">
              <span :title="data.label">{{ data.label }}</span>
            </template>
          </el-cascader>
            <el-select v-model="chooseDepart" placeholder="请选择" style="width: 80%;margin-top: 15px" v-else>
            <el-option
              v-for="item in departList"
              :key="item.deptName"
              :label="item.deptName"
              :value="item.deptName">
            </el-option>
          </el-select>
          </el-form-item>
          <!-- <el-form-item label="详细地址 :">
            <div>
              <el-input
                style="width: 70%"
                v-model="form.name"
                placeholder="请输入详情地址"
              ></el-input>
              <span style="width: 40%">
                <el-button circle icon="el-icon-search"></el-button>
                <el-button circle icon="el-icon-house"></el-button>
                <el-button circle icon="el-icon-location"></el-button>
              </span>
            </div>
          </el-form-item> -->
          <el-form-item label="指令内容 :">
            <el-input
              style="width: 80%"
              :rows="5"
              placeholder="请输入指令内容"
              type="textarea"
              v-model="form.desc"
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="btns-box s-m-t-50">
          <!-- <div class="fs-35" @click="close()">取消</div> -->
          <div
            class="fs-35"
            style="text-align: center; width: 300px"
            @click="submitForm()"
          >
            下发指令
          </div>
        </div>
      </div>
    </div>
  </body>
  <script>
    var vm = new Vue({
      el: " #zhdddialog",
      data() {
        let this_ = this;
        return {
          deptName:"",
          form: {
            from: "",
            name: "",
            region: "三级响应(一般)",
            desc: "",
            list: [],
            dataType: 1
          },
          staffList: [],
          selStaff: [], // 选择的人员列表的全部信息
          adminCity: localStorage.getItem('adminCity'),
          props: {
            multiple: true,
            lazy: true,
            lazyLoad(node, resolve) {
              this_.lazyLoads2(node, resolve);
            },
          },
          // props3: {
          //   label: "name",
          //   children: "children",
          //   isLeaf: "leaf",
          // },
          gjxx: [],
          labelName:"1",
          departList: [],
          chooseDepart:"",
          flag: false //判断科室列是否有值
        };
      },
      created() {},
      mounted() {
        let this_ = this;
        this_.getDepartList();
        this_.getDepartment()
        window.addEventListener("message", (e) => {
          if (e.data.type == "openyjtz") {
            this_.form.dataType = 3;
            this_.gjxx = e.data.data;
            if (e.data.data.id && e.data.data.yqmsg) {
              this_.form.from = "舆情中心";
              this_.form.desc = this_.gjxx.yqmsg;
            } else {
              this_.form.from =
                item.unitname.includes(item.city)? e.data.data.unitname + " SOS报警":e.data.data.city + "" + e.data.data.unitname + " SOS报警";
            }
          }
        });
      },
      methods: {
        getDepartList() {
          $api2Get("/xzzfj/workNotice/getXzzfList",{area: localStorage.getItem("city")}).then(res => {
            if (res.data.code == 200) {
              this.departList = res.data.data
            }
          })
        },
        getDepartment() {
          $api2Get("/system/dept/getUserDept").then(res => {
            if (res.data.code == 200) {
              this.deptName = res.data.data.deptName
            }
          })
        },
        lazyLoads(node, resolve) {
          const that = this;
          console.log("懒加载", node);
          if (node.level == 0) {
            $api("/zfzfry001",{area: localStorage.getItem("city") == '金华市'?"":localStorage.getItem("city")}).then((res) => {
              let data0 = [];
              res.map((a) => {
                data0.push({
                  label: [
                    "婺城区",
                    "金东区",
                    "兰溪市",
                    "东阳市",
                    "义乌市",
                    "永康市",
                    "浦江县",
                    "武义县",
                    "磐安县",
                    "开发区",
                  ].includes(a.dept_name.slice(0, 3))
                    ? a.dept_name.slice(0, 3) + "综合行政执法办"
                    : a.dept_name,
                  value: a.parent_id,
                  parent_id: a.parent_id,
                  dept_id: a.dept_id,
                });
              });
              resolve(data0);
            });
          } else if (node.level == 1) {
            $api("/zfzfry001", { deptId: node.data.dept_id }).then((res) => {
              let data1 = [];
              res.map((a) => {
                data1.push({
                  label: a.dept_name,
                  value: a.dept_id,
                  parent_id: a.parent_id,
                  dept_id: a.dept_id,
                });
              });
              resolve(data1);
            });
          } else if (node.level == 2) {
            $api("/zfzfry001", { deptId: node.data.dept_id }).then((res) => {
              if (res.length > 0) {
                that.flag = true
                let data2 = [];
                res.map((a) => {
                  data2.push({
                    label: a.dept_name,
                    value: a.dept_id,
                    parent_id: a.parent_id,
                    dept_id: a.dept_id,
                  });
                });
                resolve(data2);
              } else {
                that.flag = false
                $api("/zhzfry002", { deptId: node.data.dept_id }).then((res) => {
                  let data2 = [];
                  res.map((a) => {
                    const objs = {
                      label: a.nick_name,
                      value: a.employee_code,
                      dept_id: a.dept_id,
                      user_id: a.user_id,
                      user_name: a.user_name,
                      phone: a.phonenumber,
                      leaf: true,
                      hasChildren: false,
                    };
                    data2.push(objs);
                    this.staffList.push(objs);
                  });
                  // this.staffList.concat(data2);
                  console.log(data2);
                  resolve(data2);
                });
              }
            });
          } else if (node.level == 3 && that.flag) {
            $api("/zhzfry002", { deptId: node.data.dept_id }).then((res) => {
              let data2 = [];
              res.map((a) => {
                const objs = {
                  label: a.nick_name,
                  value: a.employee_code,
                  dept_id: a.dept_id,
                  user_id: a.user_id,
                  user_name: a.user_name,
                  phone: a.phonenumber,
                  leaf: true,
                  hasChildren: false,
                };
                data2.push(objs);
                this.staffList.push(objs);
              });
              // this.staffList.concat(data2);
              console.log(data2);
              resolve(data2);
            });
          } else {
            resolve([]);
          }
        },
        lazyLoads2(node, resolve) {
          if (node.level === 0) {
            let list = [];
            if (localStorage.getItem("adminCity") == '金华市') {
              list = [{
                label: "金华市", dept_id: 467
              }];
            } else {
              $api("zfzfry002",{ area: localStorage.getItem("adminCity"),
                pageSize: 500 }).then(res => {
                let data = [];
                let result = res;
                result.forEach((a) => {
                  let obj = {
                    label: a.deptName,
                    value: a.deptId,
                    parent_id: a.parentId,
                    dept_id: a.deptId,
                  };
                  data.push(obj);
                });
                list = [data.find(item => item.label == localStorage.getItem("adminCity"))]
                resolve(list);
              })
              // axios({
              //   type: "get",
              //   url: baseURL.admApi + "/system/dept/list",
              //   params: { parentId: 468 },
              //   headers: {
              //     "Content-Type": "application/json;charset=UTF-8",
              //     Authorization: sessionStorage.getItem("Authorization"),
              //     ptid: "PT0001",
              //   },
              // }).then((res) => {
              //   let data = [];
              //   let result = res.data.data;
              //   result.forEach((a) => {
              //     let obj = {
              //       label: a.deptName,
              //       value: a.deptId,
              //       parent_id: a.parentId,
              //       dept_id: a.deptId,
              //     };
              //     data.push(obj);
              //   });
              //   list = [data.find(item => item.label == localStorage.getItem("adminCity"))]
              //   resolve(list);
              // });
            }
            resolve(list);
          }
          if (node.level === 1) {
            $api("zfzfry002",{parentId:node.data.dept_id,
              pageSize: 500 }).then(res => {
              let data = [];
              let result = res.filter((item) => {
                return (
                  item.deptName.indexOf("数字金华技术运营有限公司") == -1
                );
              });
              result.forEach((a) => {
                let obj = {
                  label: a.deptName,
                  value: a.deptId,
                  parent_id: a.parentId,
                  dept_id: a.deptId,
                };
                data.push(obj);
              });
              console.log(data);
              data = localStorage.getItem('adminCity') == '金华市'?data:data.filter((item) => {return (item.label.indexOf("政府") != -1 || item.label.indexOf("街道") != -1)})
              resolve(data);
            })
            // axios({
            //   type: "get",
            //   url: baseURL.admApi + "/system/dept/list",
            //   params: { parentId: node.data.dept_id },
            //   headers: {
            //     "Content-Type": "application/json;charset=UTF-8",
            //     Authorization: sessionStorage.getItem("Authorization"),
            //     ptid: "PT0001",
            //   },
            // }).then((res) => {
            //   let data = [];
            //   let result = res.data.data.filter((item) => {
            //     return (
            //       item.deptName.indexOf("数字金华技术运营有限公司") == -1
            //     );
            //   });
            //   result.forEach((a) => {
            //     let obj = {
            //       label: a.deptName,
            //       value: a.deptId,
            //       parent_id: a.parentId,
            //       dept_id: a.deptId,
            //     };
            //     data.push(obj);
            //   });
            //   console.log(data);
            //   data = localStorage.getItem('adminCity') == '金华市'?data:data.filter((item) => {return (item.label.indexOf("政府") != -1 || item.label.indexOf("街道") != -1)})
            //   resolve(data);
            // });
          }
          if (node.level === 2) {
            $api("zfzfry002",{parentId:node.data.dept_id,
              pageSize: 500 }).then(res => {
              let data = [];
              let result = res;
              result.forEach((a) => {
                let obj = {
                  label: a.deptName,
                  value: a.deptId,
                  parent_id: a.parentId,
                  dept_id: a.deptId,
                };
                data.push(obj);
              });
              resolve(data);
            })
            // axios({
            //   type: "get",
            //   url: baseURL.admApi + "/system/dept/list",
            //   params: { parentId: node.data.dept_id },
            //   headers: {
            //     "Content-Type": "application/json;charset=UTF-8",
            //     Authorization: sessionStorage.getItem("Authorization"),
            //     ptid: "PT0001",
            //   },
            // }).then((res) => {
            //   let data = [];
            //   let result = res.data.data;
            //   result.forEach((a) => {
            //     let obj = {
            //       label: a.deptName,
            //       value: a.deptId,
            //       parent_id: a.parentId,
            //       dept_id: a.deptId,
            //     };
            //     data.push(obj);
            //   });
            //   resolve(data);
            // });
          }
          if (node.level === 3) {
            $api("zfzfry002",{parentId:node.data.dept_id,
              pageSize: 500 }).then(res => {
              let data = [];
              let county = [
                "婺城区",
                "金东区",
                "兰溪市",
                "东阳市",
                "义乌市",
                "永康市",
                "浦江县",
                "武义县",
                "磐安县",
              ];
              let result = null;
              if (county.indexOf(node.data.label) != -1) {
                result = res.filter((item) => {
                  return (
                    item.deptName.indexOf("政府") != -1  && item.deptId != 634 ||
                    item.deptName.indexOf("街道") != -1
                  );
                });
              } else {
                result = res;
              }
              result.forEach((a) => {
                let obj = {
                  label: a.deptName,
                  value: a.deptId,
                  parent_id: a.parentId,
                  dept_id: a.deptId,
                };
                data.push(obj);
              });
              resolve(data);
            })
            // axios({
            //   type: "get",
            //   url: baseURL.admApi + "/system/dept/list",
            //   params: { parentId: node.data.dept_id },
            //   headers: {
            //     "Content-Type": "application/json;charset=UTF-8",
            //     Authorization: sessionStorage.getItem("Authorization"),
            //     ptid: "PT0001",
            //   },
            // }).then((res) => {
            //   let data = [];
            //   let county = [
            //     "婺城区",
            //     "金东区",
            //     "兰溪市",
            //     "东阳市",
            //     "义乌市",
            //     "永康市",
            //     "浦江县",
            //     "武义县",
            //     "磐安县",
            //   ];
            //   let result = null;
            //   if (county.indexOf(node.data.label) != -1) {
            //     result = res.data.data.filter((item) => {
            //       return (
            //         item.deptName.indexOf("政府") != -1  && item.deptId != 634 ||
            //         item.deptName.indexOf("街道") != -1
            //       );
            //     });
            //   } else {
            //     result = res.data.data;
            //   }
            //   result.forEach((a) => {
            //     let obj = {
            //       label: a.deptName,
            //       value: a.deptId,
            //       parent_id: a.parentId,
            //       dept_id: a.deptId,
            //     };
            //     data.push(obj);
            //   });
            //   resolve(data);
            // });
          }
          if (node.level > 3) {
            $api("zfzfry002",{parentId:node.data.dept_id,
              pageSize: 500 }).then(res => {
              if (res && res[0]) {
                let data = [];
                let result = res;
                result.forEach((a) => {
                  let obj = {
                    label: a.deptName,
                    value: a.deptId,
                    parent_id: a.parentId,
                    dept_id: a.deptId,
                    pageSize: 500
                  };
                  data.push(obj);
                });
                resolve(data);
              } else  {
                axios({
                  type: "get",
                  url: baseURL.admApi + "/system/user/list",
                  params: { deptId: node.data.dept_id,
                    pageSize: 500 },
                  headers: {
                    "Content-Type": "application/json;charset=UTF-8",
                    Authorization: sessionStorage.getItem("Authorization"),
                    ptid: "PT0001",
                  },
                }).then((res) => {
                  if (res.data.rows && res.data.rows[0]) {
                    let data = [];
                    res.data.rows.map((a) => {
                      const objs = {
                        label: a.nickName,
                        value: a.employeeCode,
                        dept_id: a.deptId,
                        user_id: a.userId,
                        user_name: a.userName,
                        phone: a.phonenumber,
                        leaf: true,
                        hasChildren: false,
                      };
                      data.push(objs);
                      this.staffList.push(objs);
                    });
                    resolve(data);
                  } else {
                    resolve([]);
                  }
                });
              }
            })
            // axios({
            //   type: "get",
            //   url: baseURL.admApi + "/system/dept/list",
            //   params: { parentId: node.data.dept_id },
            //   headers: {
            //     "Content-Type": "application/json;charset=UTF-8",
            //     Authorization: sessionStorage.getItem("Authorization"),
            //     ptid: "PT0001",
            //   },
            // }).then((res) => {
            //   if (res.data.data && res.data.data[0]) {
            //     let data = [];
            //     let result = res.data.data;
            //     result.forEach((a) => {
            //       let obj = {
            //         label: a.deptName,
            //         value: a.deptId,
            //         parent_id: a.parentId,
            //         dept_id: a.deptId,
            //       };
            //       data.push(obj);
            //     });
            //     resolve(data);
            //   } else  {
            //     axios({
            //       type: "get",
            //       url: baseURL.admApi + "/system/user/list",
            //       params: { deptId: node.data.dept_id },
            //       headers: {
            //         "Content-Type": "application/json;charset=UTF-8",
            //         Authorization: sessionStorage.getItem("Authorization"),
            //         ptid: "PT0001",
            //       },
            //     }).then((res) => {
            //       if (res.data.rows && res.data.rows[0]) {
            //         let data = [];
            //         res.data.rows.map((a) => {
            //           const objs = {
            //             label: a.nickName,
            //             value: a.employeeCode,
            //             dept_id: a.deptId,
            //             user_id: a.userId,
            //             user_name: a.userName,
            //             phone: a.phonenumber,
            //             leaf: true,
            //             hasChildren: false,
            //           };
            //           data.push(objs);
            //           this.staffList.push(objs);
            //         });
            //         resolve(data);
            //       } else {
            //         resolve([]);
            //       }
            //     });
            //   }
            // });
          }
        },
        changeList(node) {
          console.log(this.staffList, "this.staffList");
          console.log(node, "选中");
          const arr = [];
          node.forEach((item) => {
            this.staffList.forEach((a) => {
              if (item[item.length - 1] === a.value) {
                arr.push(a);
              }
            });
          });
          console.log(this.staffList, arr, "arr-选中的人员信息");
          this.selStaff = arr;
        },
        submitForm() {
          let that = this;
          console.log(this.form);
          if (!this.form.region) {
            that.$message({
              message: "请选择级别",
              type: "warning",
            });
            return;
          }
          if (this.form.desc == "") {
            that.$message({
              message: "指令内容不能为空",
              type: "warning",
            });
            return;
          }
          if (this.chooseDepart == "" && this.labelName == "2") {
            that.$message({
              message: "请至少选择一个队伍",
              type: "warning",
            });
            return;
          }
          let employeeCodes = [];
          if (that.form.list.length == 0 && this.labelName == "1") {
            that.$message({
              message: "请至少选择一个人员",
              type: "warning",
            });
            return;
          }
          that.form.list.map((item) => {
            employeeCodes.push(item[2]);
          });
          // console.log(this.form, this.form.list, "this.form.list");
          // const p = {
          //   codeAndPhone: this.selStaff.value,
          //   name:
          // };
          console.log(this.selStaff, "this.selStaff");
          const parr = this.selStaff.map((item) => {
            return {
              codeAndPhone: item.value,
              name: item.label,
            };
          });
          let params = {
            source: "jinhuacsdn",
            list: parr,
            area: localStorage.getItem("adminCity") || "",
            level: that.form.region,
            type: 2,
            msg: that.form.desc,
            taskSource: that.form.from,
            dataType: that.form.dataType,
            detailsId: that.gjxx.id || null,
            url: "https://csdn.dsjj.jinhua.gov.cn:8300/xzzfzx/#/",
            deptName: that.deptName
          };
          let params2 = {
            source: "jinhuacsdn",
            targetDeptName: that.chooseDepart,
            area: localStorage.getItem("adminCity") || "",
            level: that.form.region,
            type: 2,
            msg: that.form.desc,
            taskSource: that.form.from,
            dataType: that.form.dataType,
            detailsId: that.gjxx.id || null,
            url: "https://csdn.dsjj.jinhua.gov.cn:8300/xzzfzx/#/",
            deptName: that.deptName
          };
          // let params = {
          //   source: "jinhuacsdn",
          //   employeeCodes: employeeCodes,
          //   msg: that.form.desc,
          // };
          if (this.labelName == "1") {
            axios({
              method: "post",
              url: baseURL.url + "/adm-api/pub/dingtalk/xzzfjWorkNotification",
              data: params,
            }).then(function (response) {
              if (response.data.code == 200) {
                that.$message({
                  message: "指令下达成功！",
                  type: "success",
                });
                // that.close();
                window.parent.frames["zhdd_right"] &&
                window.parent.frames["zhdd_right"].postMessage(
                  {
                    type: "refresh",
                    data: that.gjxx,
                  },
                  "*"
                );
              }
            });
          } else {
            $api2Post("/xzzfj/workNotice/xzzfjWorkNotificationArea",params2).then(response => {
              if (response.data.code == 200) {
                that.$message({
                  message: "指令下达成功！",
                  type: "success",
                });
                // that.close();
                window.parent.frames["zhdd_right"] &&
                window.parent.frames["zhdd_right"].postMessage(
                  {
                    type: "refresh",
                    data: that.gjxx,
                  },
                  "*"
                );
              }
            })
          }

        },

        close() {
          window.parent.frames["zhdd_middle"].vmMiddle.noticeStatus = false;
          window.parent.lay.closeIframeByNames(["zhddNotice"]);
        },
        getCheckedNodes(a, b) {
          console.log(a);
          console.log(b);
        },
      },
    });
  </script>
</html>
