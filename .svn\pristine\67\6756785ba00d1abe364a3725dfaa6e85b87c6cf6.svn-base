﻿
function myBrowser()
{
    var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
    var isOpera = userAgent.indexOf("Opera") > -1;
    if (isOpera)
    {
        return "Opera"
    }; //判断是否Opera浏览器
    if (userAgent.indexOf("Firefox") > -1)
    {
        return "FF";
    } //判断是否Firefox浏览器
    if (userAgent.indexOf("Chrome") > -1)
    {
        return "Chrome";
    }
    if (userAgent.indexOf("Safari") > -1)
    {
        return "Safari";
    } //判断是否Safari浏览器
    if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera)
    {
        return "IE";
    }; //判断是否IE浏览器

    if (!!window.ActiveXObject || "ActiveXObject" in window)
    {
        return "IE";
    }
    return "Unknown";
}

var id_video_my, id_video_peer, id_video_watch;

window.onload = function()
{
    //判断是哪个浏览器,设置使用的窗口
    var strB = myBrowser();

    if ("IE" == strB)
    {
        if (null != id_video_my_o.object)//OCX已注册
        {
            id_video_my             = id_video_my_o;
            id_video_peer           = id_video_peer_o;
            id_video_watch          = id_video_watch_o;
            document.getElementById("id_video_my_v").style.display="none";
            document.getElementById("id_video_peer_v").style.display="none";
            document.getElementById("id_video_watch_v").style.display="none";
            document.getElementById("id_video_my_down").style.display="none";
            document.getElementById("id_video_peer_down").style.display="none";
            document.getElementById("id_video_watch_down").style.display="none";
        }
        else//OCX未注册
        {
            id_video_my             = null;
            id_video_peer           = null;
            id_video_watch          = null;
            document.getElementById("id_video_my_v").style.display="none";
            document.getElementById("id_video_peer_v").style.display="none";
            document.getElementById("id_video_watch_v").style.display="none";
            document.getElementById("id_video_my_o").style.display="none";
            document.getElementById("id_video_peer_o").style.display="none";
            document.getElementById("id_video_watch_o").style.display="none";
        }
    }
    else
    {
        id_video_my             = id_video_my_v;
        id_video_peer           = id_video_peer_v;
        id_video_watch          = id_video_watch_v;
        document.getElementById("id_video_my_o").style.display="none";
        document.getElementById("id_video_peer_o").style.display="none";
        document.getElementById("id_video_watch_o").style.display="none";
        document.getElementById("id_video_my_down").style.display="none";
        document.getElementById("id_video_peer_down").style.display="none";
        document.getElementById("id_video_watch_down").style.display="none";
    }

    //判断URL地址,填写MC地址

    var strSrvAddr = document.getElementById("id_srv_addr");
    var strGpsSrvAddr = document.getElementById("id_gpssrv_addr");
    var strNsSrvAddr = document.getElementById("id_nssrv_addr");

    if (-1 != window.location.href.indexOf('file:///'))//本地文件
    {
        strSrvAddr.value = "ws://IPAddr:10004";
        strGpsSrvAddr.value = "ws://IPAddr:10005";
        strNsSrvAddr.value = "ws://IPAddr:10007";
    }
    else
    {
        if (-1 == window.location.href.indexOf('https'))
        {
            //用WS
            strSrvAddr.value = "ws://" + window.location.hostname + ":10004";
            strGpsSrvAddr.value = "ws://" + window.location.hostname + ":10005";
            strNsSrvAddr.value = "ws://" + window.location.hostname + ":10007";
        }
        else
        {
            //用WSS
            strSrvAddr.value = "wss://" + window.location.hostname + ":" + window.location.port + "/mc_wss";
            strGpsSrvAddr.value = "wss://" + window.location.hostname + ":" + window.location.port + "/gs_wss";
            strNsSrvAddr.value = "wss://" + window.location.hostname + ":" + window.location.port + "/ns_wss";
        }
    }

    return 0;
}


var g_ShowHB = false;

//是否显示心跳
function fn_ck_msgrx(){
    var cbk = document.getElementById('id_ck_msgrx');
    if (cbk.checked == false)
    {
        g_ShowHB = false;
    }
    else
    {
        g_ShowHB = true;
    }
}

//--------------------------------------------------------------------------------
//      收到消息的钩子函数,调试用
//  输入:
//      link:           链路名
//      msg:            收到的消息
//  返回:
//      0:              成功
//      -1:             失败
//--------------------------------------------------------------------------------
function onRecvMsgHook(link, msg)
{
    if (false == g_ShowHB && msg.MsgCode == IDT.MSG_HB)
        return;

	// var strOld = document.getElementById("id_rxtx_msg").value;
    var strMsg = JSON.stringify(msg);
    // \\->\
    var strMsg1 = strMsg.replace(new RegExp("\\\\\\\\", "g"), "\\");
    // document.getElementById("id_rxtx_msg").value = strOld + PUtility.PGetCurTime() + " " + link + ":<--Rx--" + strMsg1 + "\r\n";
    return 0;
}
//--------------------------------------------------------------------------------
//      发送消息的钩子函数,调试用
//  输入:
//      link:           链路名
//      msg:            收到的消息
//  返回:
//      0:              成功
//      -1:             失败
//--------------------------------------------------------------------------------
function onSendMsgHook(link, msg)
{
    if (false == g_ShowHB)
    {
        if (IDT.MSG_HB == msg.MsgCode)
            return;
        if (IDT.MSG_MM_STATUSSUBS == msg.MsgCode)
        {
            if (0 == msg.MsgBody.SN)
                return;
        }
    }

	// var strOld = document.getElementById("id_rxtx_msg").value;
    var strMsg = JSON.stringify(msg);
    // \\->\
    var strMsg1 = strMsg.replace(new RegExp("\\\\\\\\", "g"), "\\");
    // document.getElementById("id_rxtx_msg").value = strOld + PUtility.PGetCurTime() + " " + link + ":--Tx-->" + strMsg1 + "\r\n";
    return 0;
}
//--------------------------------------------------------------------------------
//      状态指示
//  输入:
//      status:         状态
//      usCause:        原因值
//  返回:
//      0:              成功
//      -1:             失败
//--------------------------------------------------------------------------------
function onStatusInd(status, usCause)
{
    if (0 == status)//离线
    {
		console.log(PUtility.PGetCurTime() + "  " + "离线 " + IDT.GetCauseStr(usCause));
    }
    else//在线
    {
        console.log(PUtility.PGetCurTime() + "  " + "在线 " + IDT.GetCauseStr(usCause));
        //加载组织下所有用户和组
        fn_UQueryAll();
        // fn_GQueryAll();
    }
    return 0;
}
//--------------------------------------------------------------------------------
//      组信息指示
//  输入:
//          gInfo:      组Json对象
//          Prio:       优先级,1~7,越小越高
//          Type:       GROUP_MEMBERTYPE_USER等,用户还是组
//          UTType:     终端类型,UT_TYPE_TAP等.如果是组,此字段无效
//          Attr:       终端属性,UT_ATTR_HS等,显示用.如果号码不是终端,无效
//          Num:        号码
//          Name:       名字
//          AGNum:      附加组号码,通常用做视频组
//          ChanNum:    摄像头通道个数,如果号码不是摄像头,无效
//          Status:     主状态,UT_STATUS_OFFLINE等
//          FGCount:    父组个数
//          FGNum:      父组号码
//  返回:
//      0:              成功
//      -1:             失败
//--------------------------------------------------------------------------------
function onGInfoInd(gInfo)
{
    PUtility.Log("IDTUser", PUtility.PGetCurTime(), "onGInfoInd", gInfo);

    var strGroup = '';
    var i;
    if (null != gInfo)
    {
        for (i = 0; i < gInfo.length; i++)
        {
            strGroup += gInfo[i].Num + '(' + gInfo[i].Name + ') ';
        }
    }
    // document.getElementById("id_my_group").textContent = strGroup;

    return 0;

    //加载组织下所有用户和组
    fn_UQueryAll();
    fn_GQueryAll();
    return 0;
}
//--------------------------------------------------------------------------------
//      收到即时消息指示
//  输入:
//      pucSn:          序号
//      dwType:         及时消息类型
//      pcFrom:         源号码
//      pcFromName:     源名字
//      pcTo:           目的号码,#+号码:表示是组号码
//      pcOriTo:        原始目的号码
//      pcTxt:          文本内容
//      pcFileName:     文件名
//      pcSourceFileName:源文件名
//      pcTime:         发送的时间
//  返回:
//      0:              成功
//      -1:             失败
//--------------------------------------------------------------------------------
function onIMRecv(pucSn, dwType, pcFrom, pcFromName, pcTo, pcOriTo, pcTxt, pcFileName, pcSourceFileName, pcTime)
{
    PUtility.Log("IDTUser", PUtility.PGetCurTime(), "onIMRecv", pucSn, dwType, pcFrom, pcFromName, pcTo, pcOriTo, pcTxt, pcFileName, pcSourceFileName, pcTime);
    return 0;
}
//--------------------------------------------------------------------------------
//      IM状态指示
//  输入:
//      dwSn:           消息事务号
//      pucSn:          系统的事务号
//      dwType:         及时消息类型
//      ucStatus:       状态,PTE_CODE_TXCFM
//  返回:
//      0:              成功
//      -1:             失败
//--------------------------------------------------------------------------------
function onIMStatusInd(dwSn, pucSn, dwType, ucStatus)
{
    //PTE_CODE_TXCFM=2
    PUtility.Log("IDTUser", PUtility.PGetCurTime(), "onIMStatusInd", dwSn, pucSn, dwType, ucStatus);
    return 0;
}

//--------------------------------------------------------------------------------
//      组/用户OAM操作指示
//  输入:
//      dwOptCode:      操作码
//                      OPT_USER_ADD        pucUNum,pucUName,ucUAttr有效
//                      OPT_USER_DEL        pucUNum有效
//                      OPT_USER_MODIFY     pucUNum,pucUName,ucUAttr有效
//                      OPT_G_ADD           pucGNum,pucGName有效
//                      OPT_G_DEL           pucGNum有效
//                      OPT_G_MODIFY        pucGNum,pucGName有效
//                      OPT_G_ADDUSER       pucGNum,pucUNum,pucUName,ucUAttr有效
//                      OPT_G_DELUSER       pucGNum,pucUNum有效
//                      OPT_G_MODIFYUSER    pucGNum,pucUNum,pucUName,ucUAttr有效
//      pucGNum:        组号码
//      pucGName:       组名字
//      pucUNum:        用户号码
//      pucUName:       用户名字
//      ucUAttr:        用户属性
//  返回:
//      0:              成功
//      -1:             失败
//--------------------------------------------------------------------------------
function onGUOamInd(dwOptCode, pucGNum, pucGName, pucUNum, pucUName, ucUAttr)
{
    return 0;
}
//--------------------------------------------------------------------------------
//      组/用户状态指示
//  输入:
//      GMemberStatus   组/用户状态Json对象
//[{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
//{
//  Type: 2,                GROUP_MEMBERTYPE_USER(1)    GROUP_MEMBERTYPE_GROUP(2)
//  Num: "1115",            字符串
//  Status: 1,              UT_STATUS_OFFLINE(0)        UT_STATUS_ONLINE(1)
//  GpsReport: 0,           暂时不使用.是否正在上报GPS,0未上报,1正在上报
//  CallNum: 0,             呼叫个数
//      []                  呼叫数组
//      CallType :%d,       SRV_TYPE_BASIC_CALL
//      CallStatus:%d,      GU_STATUSCALL_OALERT
//      CallId:%d,          CSAID，内部使用，可以不理会
//      PeerNum:%s,         对端号码
//      PeerName\":\"%s\"}  对端名字
//  返回:
//      0:              成功
//      -1:             失败
//--------------------------------------------------------------------------------
function onGUStatusInd(GMemberStatus)
{
    PUtility.Log("IDTUser", PUtility.PGetCurTime(), "onGUStatusInd", GMemberStatus);
    return 0;
}

//--------------------------------------------------------------------------------
//      GPS数据指示,获得其他用户的GPS记录
//  输入:
//      GpsRecStr:      GPS记录信息,Json格式
//  Num:    "986001",
//  Status: 1   //不使用
//  Count   1   //GpsInfo数组个数
//  GpsInfo:
//  [
//      {Longitude:"113.943718", Latitude:"22.543962", Speed:"0.000000", Direction:"0.000000", Time:"2017-11-11 16:59:59"}
//  ]
//  返回:
//      0:              成功
//      -1:             失败
//--------------------------------------------------------------------------------
function onGpsRecInd(GpsRecStr)
{
    if (localStorage.getItem("MsgBody")) {
        let MsgBody = JSON.parse(localStorage.getItem("MsgBody"))
        MsgBody.forEach((item,i) => {
            if (item.Num == GpsRecStr.Num) {
                item.GpsInfo = GpsRecStr.GpsInfo
            }
        })
        localStorage.setItem("MsgBody",JSON.stringify(MsgBody))
    }
    PUtility.Log("IDTUser", PUtility.PGetCurTime(), "onGpsRecInd", GpsRecStr);
    return 0;
}

//--------------------------------------------------------------------------------
//      GPS历史数据指示
//  输入:
//      UsrNum:         用户号码
//      sn:             操作序号
//      EndFlag:        结束标志,0未结束,1结束
//      GpsRecStr:      GPS记录信息,Json格式
//  Num:    "986001",
//  Status: 1   //不使用
//  Count   1   //GpsInfo数组个数
//  GpsInfo:
//  [
//      {Longitude:"113.943718", Latitude:"22.543962", Speed:"0.000000", Direction:"0.000000", Time:"2017-11-11 16:59:59"}
//  ]
//  返回:
//      0:              成功
//      -1:             失败
//--------------------------------------------------------------------------------
function onGpsHisQueryInd(UsrNum, sn, EndFlag, GpsRecStr)
{
//986001,0,2017-11-11 00:00:00,2017-11-11 23:59:59
    PUtility.Log("IDTUser", PUtility.PGetCurTime(), "onGpsHisQueryInd", UsrNum, sn, EndFlag, GpsRecStr);
    return 0;
}
//--------------------------------------------------------------------------------
//      NS查询数据指示
//  输入:
//      NsQueryExt:     查询条件
//      EndFlag:        结束标志,0未结束,1结束,-1出错
//      NsQueryRec:     NS记录信息,Json格式
//  Num:    "986001",
//  Status: 1   //不使用
//  Count   1   //GpsInfo数组个数
//  GpsInfo:
//  [
//      {Longitude:"113.943718", Latitude:"22.543962", Speed:"0.000000", Direction:"0.000000", Time:"2017-11-11 16:59:59"}
//  ]
//  返回:
//      0:              成功
//      -1:             失败
//--------------------------------------------------------------------------------
function onNsQueryInd(NsQueryExt, EndFlag, NsQueryRec)
{
//986001,0,2017-11-11 00:00:00,2017-11-11 23:59:59
    if (false == PUtility.isEmpty(NsQueryRec))
        PUtility.Log("IDTUser", PUtility.PGetCurTime(), "onNsQueryInd(", NsQueryRec.length, "):", NsQueryExt, EndFlag, NsQueryRec);
    else
        PUtility.Log("IDTUser", PUtility.PGetCurTime(), "onNsQueryInd(", 0, "):", NsQueryExt, EndFlag, NsQueryRec);
    return 0;
}

var m_CallId = -1, m_CallRef, m_ARx = 0, m_ATx = 0, m_VRx = 0, m_VTx = 0, m_CallIdWatch = -1;
//--------------------------------------------------------------------------------
//      呼叫信息指示
//  输入:
//      event:          事件
//          IDT.CALL_EVENT_Rel:             event, UsrCtx, ID(IDT的呼叫ID), ucClose(IDT.CLOSE_BYUSER), usCause(IDT.CAUSE_ZERO), strCause
//          IDT.CALL_EVENT_PeerAnswer:      event, UsrCtx, PeerNum, PeerName, SrvType(可能与发出时不同.例如想发起组呼(主控),但变成组呼接入), UserMark, UserCallRef
//          IDT.CALL_EVENT_In:              event, ID(此时是IDT的呼叫ID,不是用户上下文), pcPeerNum, pcPeerName, SrvType, bIsGCall, ARx, ATx, VRx, VTx
//          IDT.CALL_EVENT_MicInd:          event, UsrCtx, ind(0听话,1讲话)
//          IDT.CALL_EVENT_RecvInfo:        event, UsrCtx, Info, InfoStr
//          IDT.CALL_EVENT_TalkingIDInd:    event, UsrCtx, TalkingNum, TalkingName
//          IDT.CALL_EVENT_ConfCtrlInd:     event, UsrCtx, Info{Info(IDT.SRV_INFO_MICREL), InfoStr}, UsrNum, UsrName
//          IDT.CALL_EVENT_ConfStatusRsp:   event, UsrCtx(无效), MsgBody
//  返回:
//      0:              成功
//      -1:             失败
//--------------------------------------------------------------------------------
function onCallInd(event)
{
    var params = arguments.length;

    switch (event)
    {
    case IDT.CALL_EVENT_Rel://event, UsrCtx, ID(IDT的呼叫ID), ucClose(IDT.CLOSE_BYUSER), usCause(IDT.CAUSE_ZERO), strCause
        if (params < 6)
            return -1;
        PUtility.Log("IDTUser", PUtility.PGetCurTime(), "CALL_EVENT_Rel", event, arguments[1], arguments[2], arguments[3], IDT.GetCauseStr(arguments[4]));
        var calltime = document.getElementById("id_call_time");
    	calltime.textContent += "~";
        calltime.textContent += PUtility.PGetCurTime();
        calltime.textContent += "  ";
        calltime.textContent += IDT.GetCloseStr(arguments[3]);
        calltime.textContent += ":";
        calltime.textContent += IDT.GetCauseStr(arguments[4]);
        calltime.textContent += ":";
        calltime.textContent += arguments[5];

        var callinfo = document.getElementById("id_call_info");
    	callinfo.textContent = '讲话指示:';
        var callinfo = document.getElementById("id_call_talkinfo");
    	callinfo.textContent = '讲话方:';
        switch (arguments[1])
        {
        case 100:
            m_CallId = -1;
            break;
        case 200:
            m_CallIdWatch = -1;
            break;
        default:
            break;
        }
        break;

    case IDT.CALL_EVENT_PeerAnswer://event, UsrCtx, PeerNum, PeerName, SrvType(可能与发出时不同.例如想发起组呼(主控),但变成组呼接入), UserMark, UserCallRef
        if (params < 5)
            return -1;
        m_CallRef = arguments[2];
        PUtility.Log("IDTUser", PUtility.PGetCurTime(), "CALL_EVENT_PeerAnswer", event, arguments[1], arguments[2], arguments[3], arguments[4], arguments[5], arguments[6]);
        //m_IdtApi.CallConfCtrlReq(m_CallId, null, IDT.SRV_INFO_AUTOMICON, 1);//0是台上话权,1是台下话权.自由发言只针对台下话权
        break;

    case IDT.CALL_EVENT_In://event, ID(此时是IDT的呼叫ID,不是用户上下文), pcPeerNum, pcPeerName, SrvType, bIsGCall, ARx, ATx, VRx, VTx, UserMark, UserCallRef
        if (params < 10)
            return -1;
        //此时UsrCtx是IDT的callid,同CallMakeOut的返回值
        PUtility.Log("IDTUser", PUtility.PGetCurTime(), "CALL_EVENT_In", event, arguments[1], arguments[2], arguments[3], arguments[4],
            arguments[5], arguments[6], arguments[7], arguments[8], arguments[9], arguments[10], arguments[11]);
        var calltime = document.getElementById("id_call_time");
    	calltime.textContent = arguments[2] + '(' + arguments[3] + ')入呼叫-媒体属性(' + arguments[6] + arguments[7] + arguments[8] + arguments[9] + ')';
        m_CallId = arguments[1];
        m_ARx = arguments[6];
        m_ATx = arguments[7];
        m_VRx = arguments[8];
        m_VTx = arguments[9];

        if (IDT.SRV_TYPE_CONF == arguments[4] && true == arguments[5])//是组呼
        {
            fn_answer();
            var callinfo = document.getElementById("id_call_info");
        	callinfo.textContent = '讲话指示:0';
        }
        break;

    case IDT.CALL_EVENT_MicInd://event, UsrCtx, ind(0听话,1讲话)
        if (params < 3)
            return -1;
        var callinfo = document.getElementById("id_call_info");
    	callinfo.textContent = '讲话指示:' + arguments[2];
        break;

    case IDT.CALL_EVENT_RecvInfo://event, UsrCtx, Info, InfoStr
        if (params < 4)
            return -1;
        var callinfo = document.getElementById("id_call_info");
    	callinfo.textContent = '信息码:' + arguments[2] + ':' + arguments[3];
        break;

    case IDT.CALL_EVENT_TalkingIDInd://event, UsrCtx, TalkingNum, TalkingName
        if (params < 4)
            return -1;
        var callinfo = document.getElementById("id_call_talkinfo");
    	callinfo.textContent = '讲话方' + arguments[2] + ':' + arguments[3];
        break;

    case IDT.CALL_EVENT_ConfCtrlInd://event, UsrCtx, Info{Info(IDT.SRV_INFO_MICREL), InfoStr}
        if (params < 3)
            return -1;
        PUtility.Log("IDTUser", PUtility.PGetCurTime(), "CALL_EVENT_ConfCtrlInd", event, arguments[1], arguments[2]);
        break;

    case IDT.CALL_EVENT_ConfStatusRsp://event, UsrCtx(无效), MsgBody
        if (params < 3)
            return -1;
        PUtility.Log("IDTUser", PUtility.PGetCurTime(), "CALL_EVENT_ConfStatusRsp", event, arguments[1], arguments[2]);
        break;

    default:
        break;
    }
    return 0;
}


var m_IdtApi = null;
// 启动
function fn_Start()
{
	if (null == m_IdtApi)
	{
        m_IdtApi = new CIdtApi();
	}
    // 0=release,1=打印消息,2=打印消息和日志
    m_IdtApi.RUN_MODE = 0;

    var strSrvUrl = "wss://csdn.dsjj.jinhua.gov.cn:8300/mc_wss";
    var strGpsSrvUrl = "wss://csdn.dsjj.jinhua.gov.cn:8300/gs_wss";
    var strNsUrl = "wss://csdn.dsjj.jinhua.gov.cn:8300/ns_wss";
    // var strSrvUrl = "wss://localhost:8201/mc_wss";
    // var strGpsSrvUrl = "wss://localhost:8201/gs_wss";
    // var strNsUrl = "wss://localhost:8201/ns_wss";
    var strUserId = "1001";
    var strPwd = "Zfj@123456";
    var CallBack =
    {
        onRecvMsgHook : onRecvMsgHook,          //收到消息的钩子函数,只用来调试打印,如果修改消息内容,会出问题
        onSendMsgHook : onSendMsgHook,          //发送消息的钩子函数,只用来调试打印,如果修改消息内容,会出问题
        onStatusInd : onStatusInd,              //登录状态指示
        onGInfoInd  : onGInfoInd,               //组信息指示,指示用户在哪些组里面
        onIMRecv    : onIMRecv,                 //短信接收指示
        onIMStatusInd : onIMStatusInd,          //短信状态指示
        onGUOamInd  : onGUOamInd,               //用户/组OAM操作指示
        onGUStatusInd : onGUStatusInd,          //用户/组状态指示
        onGpsRecInd : onGpsRecInd,              //GPS数据指示
        onGpsHisQueryInd : onGpsHisQueryInd,    //GPS历史数据查询响应
        onCallInd   : onCallInd,                //呼叫指示
        onNsQueryInd : onNsQueryInd             //NS查询响应
    };

    var bIsIe = false;
    if ("IE" == myBrowser())
    {
        bIsIe = true;
    }
    m_IdtApi.Start(strSrvUrl, strGpsSrvUrl, strUserId, strPwd, 1024, 32, 1, 4096, CallBack, bIsIe, strNsUrl);
}

// 退出
function fn_Exit()
{
    m_IdtApi.Exit();
    m_IdtApi = null;
}

//bRes:     是否操作成功,false失败,true成功
//cause:    错误原因值,IDT.CAUSE_ZERO等
//strCause: 错误字符串
//MsgBody:  操作返回消息,Json对象
//UserSn:   用户启动时带的SN
function fn_OamCallBack(bRes, cause, strCause, MsgBody, UserSn)
{
    var xx = 0;

    switch (MsgBody.OpCode)
    {
    case IDT.OPT_USER_QUERY://用户查询
        PUtility.Log("IDTUser", PUtility.PGetCurTime(), "IDT.OPT_USER_QUERY", MsgBody);
        //document.getElementById("id_user_num").value = MsgBody;
        document.getElementById("id_user_name").value       = MsgBody.UsrName;
        document.getElementById("id_user_pwd").value        = MsgBody.Pwd;
        document.getElementById("id_user_type").value       = MsgBody.UserType;
        document.getElementById("id_user_attr").value       = MsgBody.UserAttr;
        document.getElementById("id_user_status").value     = MsgBody.UserStatus;
        document.getElementById("id_user_prio").value       = MsgBody.Prio;
        document.getElementById("id_user_concurrent").value = MsgBody.ConCurrent;
        document.getElementById("id_user_cfgip").value      = MsgBody.UserIPAddr;
//        document.getElementById("id_user_addr").value       = MsgBody.;
//        document.getElementById("id_user_contact").value    = MsgBody.;
//        document.getElementById("id_user_desc").value       = MsgBody.;
        document.getElementById("id_user_ctime").value      = MsgBody.CTime;
        document.getElementById("id_user_vtime").value      = MsgBody.VTime;
        document.getElementById("id_user_camtype").value    = MsgBody.CamInfo.Type;
        document.getElementById("id_user_camip").value      = MsgBody.CamInfo.IPAddr;
        document.getElementById("id_user_camport").value    = MsgBody.CamInfo.Port;
        document.getElementById("id_user_camname").value    = MsgBody.CamInfo.Name;
        document.getElementById("id_user_campwd").value     = MsgBody.CamInfo.Pwd;
        document.getElementById("id_user_camchannum").value = MsgBody.CamInfo.ChanNum;
//        document.getElementById("id_user_workinfo").value   = MsgBody.;
//        document.getElementById("id_user_proxyreg").value   = MsgBody.;
//        document.getElementById("id_user_datarole").value)  = MsgBody.;
//        document.getElementById("id_user_menurole").value)  = MsgBody.;
//        document.getElementById("id_user_deptnum").value    = MsgBody.;
//        document.getElementById("id_user_id").value         = MsgBody.;
//        document.getElementById("id_user_workid").value     = MsgBody.;
//        document.getElementById("id_user_workunit").value   = MsgBody.;
//        document.getElementById("id_user_worktitle").value  = MsgBody.;
//        document.getElementById("id_user_carid").value      = MsgBody.;
//        document.getElementById("id_user_tel").value        = MsgBody.;
//        document.getElementById("id_user_other").value      = MsgBody.;
//        document.getElementById("id_user_count").value      = MsgBody.;
        break;

//    case IDT.OPT_R_QUERY://路由查询
//        //结束了
//        if (1 == MsgBody.CommQuery.EndFlag)
//        {
//            break;
//        }
//        break;
    default:
        break;
    }

    return 0;
}

function fn_OamCallBack_UQueryAll(bRes, cause, strCause, MsgBody)
{
    if (true == PUtility.isEmpty(MsgBody))
    {
        return 0;
    }

    if (cause != IDT.CAUSE_ZERO)
    {
        PUtility.Log("IDTUser", PUtility.PGetCurTime(), "fn_OamCallBack_UQueryAll", IDT.GetCauseStr(cause));
        return 0;
    }

    var i;
    for (i = 0; i < MsgBody.GNumU; i++)
    {
        m_IdtApi.GpsSubs(MsgBody.GMember[i].Num, IDT.GU_STATUSSUBS_DETAIL1);
        //m_IdtApi.GpsSubs(MsgBody.GMember[i].Num, IDT.GU_STATUSSUBS_QUERY_ONETIME);
    }

    if (MsgBody.GNumU < 1024)//查询完成
        // return 0;

    if (cause == 0 && MsgBody.GMember) {
        localStorage.setItem("MsgBody",JSON.stringify(MsgBody.GMember))
    }

    var query = {
        GNum        : MsgBody.GNum,
			QueryExt    :
            {
                All     : 1,
                Group   : 0,
                User    : 1,
                Order   : 0,
                Page    : MsgBody.QueryExt.Page + 1,
                Count   : 1024,
                TotalCount : 0
            }
        };

    m_IdtApi.GQueryU(query, fn_OamCallBack_UQueryAll);

    return 0;
}

function fn_ReadGps()
{
    var strTo = document.getElementById("id_msmgr_num").value;
    var strFrom = document.getElementById("id_my_num").value;
    var strText = '{"fromDesc":"';
    strText += strFrom;
    strText += '",fromNumber":"';
    strText += strFrom;
    strText += '","messageId":"2018-03-13","subPara":"{\\\"type\\\":0}","toDesc":"';//0:查询 1:查询响应 2.设置 3设置响应
    strText += strTo;
    strText += '","toNumber":"';
    strText += strTo;
    strText += '","type":1}';//0:会议相关信息  1:表示终端Gps设置相关  2:视频参数设置  3.用户头像等属性改变 4.视频转发 6.表示sos消息

    m_IdtApi.IMSend(300, IDT.IM_TYPE_CONF, strTo, strText, null, null);
}

function fn_SetGps()
{
    var strTo = document.getElementById("id_msmgr_num").value;
    var strFrom = document.getElementById("id_my_num").value;
    var strText = '{"fromDesc":"';
    strText += strFrom;
    strText += '",fromNumber":"';
    strText += strFrom;
    strText += '","messageId":"2018-03-13","subPara":"{\\\"open\\\":true,\\\"type\\\":2}","toDesc":"';//0:查询 1:查询响应 2.设置 3设置响应
    strText += strTo;
    strText += '","toNumber":"';
    strText += strTo;
    strText += '","type":1}';//0:会议相关信息  1:表示终端Gps设置相关  2:视频参数设置  3.用户头像等属性改变 4.视频转发 6.表示sos消息

    m_IdtApi.IMSend(300, IDT.IM_TYPE_CONF, strTo, strText, null, null);
}

function fn_ReadVideoParam()
{
    var strTo = document.getElementById("id_msmgr_num").value;
    var strFrom = document.getElementById("id_my_num").value;
    var strText = '{"fromDesc":"';
    strText += strFrom;
    strText += '",fromNumber":"';
    strText += strFrom;
    strText += '","messageId":"2018-03-13","subPara":"{\\\"type\\\":0}","toDesc":"';//0:查询 1:查询响应 2.设置 3设置响应
    strText += strTo;
    strText += '","toNumber":"';
    strText += strTo;
    strText += '","type":2}';//0:会议相关信息  1:表示终端Gps设置相关  2:视频参数设置  3.用户头像等属性改变 4.视频转发 6.表示sos消息

    m_IdtApi.IMSend(300, IDT.IM_TYPE_CONF, strTo, strText, null, null);
}

function fn_SetVideoParam()
{
    var strTo = document.getElementById("id_msmgr_num").value;
    var strFrom = document.getElementById("id_my_num").value;
    var strText = '{"fromDesc":"';
    strText += strFrom;
    strText += '",fromNumber":"';
    strText += strFrom;
    strText += '","messageId":"2018-03-13","subPara":"{';
    strText += '\\\"type\\\":2,\\\"bitrate\\\":2200,\\\"framerate\\\":25,\\\"resolution\\\":\\\"1920*1080\\\",';//0:查询 1:查询响应 2.设置 3设置响应
    strText += '\\\"videoPara\\\":{\\\"maxBitrate\\\":3000,\\\"maxFramerate\\\":30,\\\"minBitrate\\\":300,\\\"minFramerate\\\":5,\\\"resolutionList\\\":[\\\"320*240\\\",\\\"640*480\\\",\\\"1280*720\\\",\\\"1920*1080\\\",\\\"480*720\\\"]}';
    strText += '}","toDesc":"';
    strText += strTo;
    strText += '","toNumber":"';
    strText += strTo;
    strText += '","type":2}';//0:会议相关信息  1:表示终端Gps设置相关  2:视频参数设置  3.用户头像等属性改变 4.视频转发 6.表示sos消息

    m_IdtApi.IMSend(300, IDT.IM_TYPE_CONF, strTo, strText, null, null);
}

function fn_TransVideo()
{
    var strTo = document.getElementById("id_msmgr_num").value;
    var strFrom = document.getElementById("id_my_num").value;
    var strWatchDown = document.getElementById("id_watchdown_num").value;

    //发送给strTo,让strTo查看strWatchDown的视频
    var strText = '{"fromDesc":"';
    strText += strFrom;
    strText += '","fromNumber":"';
    strText += strFrom;
    strText += '","messageId":"2018-03-13","subPara":"{';
    strText += '\\\"toNum\\\":\\\"';
    strText += strWatchDown;
    strText += '\\\",\\\"toNumDesc\\\":\\\"';
    strText += strWatchDown;
    strText += '\\\",\\\"type\\\":2,\\\"userList\\\":[]';//0:查询 1:查询响应 2.设置 3设置响应
    strText += '}","toDesc":"';
    strText += strTo;
    strText += '","toNumber":"';
    strText += strTo;
    strText += '","type":4}';//0:会议相关信息  1:表示终端Gps设置相关  2:视频参数设置  3.用户头像等属性改变 4.视频转发 6.表示sos消息

    m_IdtApi.IMSend(300, IDT.IM_TYPE_CONF, strTo, strText, null, null);
}

function fn_ScreenVideo()
{
    var strTo = document.getElementById("id_msmgr_num").value;
    var strFrom = document.getElementById("id_my_num").value;

    //发送给strTo,让strTo查看strWatchDown的视频
    var strText = '{"fromDesc":"';
    strText += strFrom;
    strText += '","fromNumber":"';
    strText += strFrom;
    strText += '","messageId":"2018-03-13","subPara":"{';
    strText += '\\\"messageId\\\":1533720661900,';
    strText += '\\\"type\\\":1}","toDesc":"';
    strText += strTo;
    strText += '","toNumber":"';
    strText += strTo;
    strText += '","type":7}';

    m_IdtApi.IMSend(300, IDT.IM_TYPE_CONF, strTo, strText, null, null);
}


function fn_MediaLinkOpenApp()
{
    var strApp = document.getElementById("id_medialink_app").value;

    m_IdtApi.MediaALinkCtrlSend("{\"APP\":\"DH_APP\"}");
}

//查询组织
function fn_OQuery()
{
    var strNum = document.getElementById("id_org_num").value;

    m_IdtApi.OQuery(strNum, fn_OamCallBack);
}

//查询所有用户
function fn_UQueryAll()
{
    var strGNum = '0';
    var iGroup  = 0;
    var iUser   = 1;
    var iPage   = 0;

    // var strOrgNum = document.getElementById("id_org_num").value;

//      pucGNum:        组号码
//      ucGroup:        是否查询下属组,0不查询,1查询
//      ucUser:         是否查询下属用户,0不查询,1查询
//      dwPage:         第几页,从0开始.默认每页1024个用户,如果不到1024个用户,说明查询结束

    var query = {
        GNum        : strGNum,
		QueryExt    :
        {
            All     : 1,
            Group   : iGroup,
            User    : iUser,
            Order   : 0,
            Page    : iPage,
            Count   : 1024,
            TotalCount : 0
        },
        OrgListMgr  :
		[
            {
                Num     : ""
            }
        ]
    };

    m_IdtApi.GQueryU(query, fn_OamCallBack_UQueryAll);
}

//查询区号下所有用户
function fn_UQueryAllZone()
{
    var strGNum = document.getElementById("id_zone_num_query_all").value;
    var iGroup  = 0;
    var iUser   = 1;
    var iPage   = 0;
    var strOrgNum = document.getElementById("id_org_num").value;

//      pucGNum:        组号码
//      ucGroup:        是否查询下属组,0不查询,1查询
//      ucUser:         是否查询下属用户,0不查询,1查询
//      dwPage:         第几页,从0开始.默认每页1024个用户,如果不到1024个用户,说明查询结束

    var query = {
        GNum        : strGNum,
		QueryExt    :
        {
            All     : 1,
            Group   : iGroup,
            User    : iUser,
            Order   : 0,
            Page    : iPage,
            Count   : 1024,
            TotalCount : 0
        },
        OrgListMgr  :
		[
            {
                Num     : strOrgNum
            }
        ]
    };

    m_IdtApi.GQueryU(query, fn_OamCallBack_UQueryAll);
}

//查询用户
function fn_UQuery()
{
    var strNum = document.getElementById("id_user_num").value;

    m_IdtApi.UQuery(strNum, fn_OamCallBack);
}

//查询所有组
function fn_GQueryAll()
{
    var strGNum = '0';
    var iGroup  = 1;
    var iUser   = 0;
    var iPage   = 0;
    var strOrgNum = document.getElementById("id_org_num").value;

//      pucGNum:        组号码
//      ucGroup:        是否查询下属组,0不查询,1查询
//      ucUser:         是否查询下属用户,0不查询,1查询
//      dwPage:         第几页,从0开始.默认每页1024个用户,如果不到1024个用户,说明查询结束

    var query = {
        GNum        : strGNum,
		QueryExt    :
        {
            All     : 1,
            Group   : iGroup,
            User    : iUser,
            Order   : 0,
            Page    : iPage,
            Count   : 1024,
            TotalCount : 0
        },
        OrgListMgr  :
		[
            {
                Num     : strOrgNum
            }
        ]
    };

    m_IdtApi.GQueryU(query, fn_OamCallBack);
}

//查询组
function fn_GQuery()
{
    var strGNum = document.getElementById("id_group_num").value;

    m_IdtApi.GQuery(strGNum, fn_OamCallBack);
}

//查询组中用户
function fn_GQueryU()
{
    var strGNum = document.getElementById("id_group_num").value;
    var iGroup = Number(document.getElementById("id_all_group").value);
    var iUser = Number(document.getElementById("id_all_user").value);
    var iPage = Number(document.getElementById("id_page_index").value);

    var strOrgNum = document.getElementById("id_org_num").value;
//      pucGNum:        组号码
//      ucGroup:        是否查询下属组,0不查询,1查询
//      ucUser:         是否查询下属用户,0不查询,1查询
//      dwPage:         第几页,从0开始.默认每页1024个用户,如果不到1024个用户,说明查询结束

    var query = {
        GNum        : strGNum,
		QueryExt    :
        {
            All     : 1,
            Group   : iGroup,
            User    : iUser,
            Order   : 0,
            Page    : iPage,
            Count   : 1024,
            TotalCount : 0
        },
        OrgListMgr  :
		[
            {
                Num     : strOrgNum
            }
        ]
    };

    m_IdtApi.GQueryU(query, fn_OamCallBack);
}

//查询用户所在组信息
function fn_UQueryG()
{
    var strUNum = document.getElementById("id_group_unum").value;
    m_IdtApi.UQueryG(strUNum, fn_OamCallBack);
}

function fn_BuildCatalogTree_Callback(bRes, cause, Tree)
{
    console.log("IDTUser", PUtility.PGetCurTime(), "fn_BuildCatalogTree_Callback", bRes, cause, Tree);

    if (null == Tree)
        return 0;

    var strTreeData = '总人数:' + Tree.UCount + "在线人数:" + Tree.UCountOnLine;
    document.getElementById("id_tree_data").textContent = strTreeData;
}

//--------------------------------------------------------------------------------
//      构建树
//  输入:
//      fnCallBack:     成功/失败的回调函数
//  返回:
//      0:              成功
//      -1:             失败
//--------------------------------------------------------------------------------
function fn_GBuildTree()
{
    var strGNum = document.getElementById("id_group_num").value;

    console.log("IDTUser", PUtility.PGetCurTime(), "fn_GBuildTree", strGNum);
    m_IdtApi.BuildCatalogTree(strGNum, fn_BuildCatalogTree_Callback);
}

function fn_gps_subs()
{
    var info = document.getElementById("id_gps_param_subs").value.split(",");
    m_IdtApi.GpsSubs(info[0], IDT.GU_STATUSSUBS_DETAIL1);
}

function fn_gps_report()
{
    //号码,经度,纬度,速度,方向
    //时间取当前时间
    var info = document.getElementById("id_gps_param_report").value.split(",");

    var date = new Date();
    var seperator1 = "-";
    var seperator2 = ":";
    var month = date.getMonth() + 1;
    var strDate = date.getDate();
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    var str = date.getFullYear() + seperator1 + month + seperator1 + strDate
            + " " + date.getHours() + seperator2 + date.getMinutes()
            + seperator2 + date.getSeconds();

    m_IdtApi.GpsReport(info[0], info[1], info[2], info[3], info[4], str);
}

function fn_gps_hisquery()
{
    //号码,SN,开始时间,结束时间
    var info = document.getElementById("id_gps_param_his").value.split(",");
    m_IdtApi.GpsHisQuery(info[0], 0, info[1], info[2]);
}

function fn_ns_query()
{
    //本端号码,对端号码,OT,SrvType,开始时间,结束时间
    var info = document.getElementById("id_ns_param").value.split(",");
    var QueryExt = {
            EndFlag : 0,
            Page    : 0,
            Count   : 50,
            StartTime : info[4],
            EndTime : info[5],
            All     : 1,
            MyNum   : info[0],
            PeerNum : info[1],
            OT      : Number(info[2]),
            SrvType : Number(info[3])
        };
    m_IdtApi.NsQuery(QueryExt);
}

//发送短信
function fn_IMSend()
{
    var strTo = document.getElementById("id_peer_num").value;
    var strTxt = document.getElementById("id_im_msg").value;
    var iType = Number(document.getElementById("id_im_type").value);

    var strFileName = null;
    var strSourceFileName = null;
    //m_IdtApi.IMSend(300, IDT.IM_TYPE_TXT, strTo, strTxt, strFileName, strSourceFileName);
    m_IdtApi.IMSend(300, iType, strTo, strTxt, strFileName, strSourceFileName);
}

var m_bAutoMic = false;

function fn_callout()
{
    var strTo = document.getElementById("id_peer_num").value;
    var strUserMark = document.getElementById("id_user_mark").value;
    var ARx = Number(document.getElementById("id_a_rx").value);
    var ATx = Number(document.getElementById("id_a_tx").value);
    var VRx = Number(document.getElementById("id_v_rx").value);
    var VTx = Number(document.getElementById("id_v_tx").value);
    m_CallId = m_IdtApi.CallMakeOut(100, id_video_my, id_video_peer, ARx, ATx, VRx, VTx, strTo, IDT.SRV_TYPE_BASIC_CALL,  "", 1, 0, strUserMark);
    var calltime = document.getElementById("id_call_time");
	calltime.textContent = PUtility.PGetCurTime();
    m_bAutoMic = false;
    var callinfo = document.getElementById("id_call_info");
	callinfo.textContent = '讲话指示:';
    var callinfo = document.getElementById("id_call_talkinfo");
	callinfo.textContent = '讲话方:';
}

function fn_rel()
{
    var usCause = Number(document.getElementById("id_rel_cause").value);
    if (-1 != m_CallId)
    {
        m_IdtApi.CallRel(m_CallId, 100, usCause);
        m_CallId = -1;
    }
    if (-1 != m_CallIdWatch)
    {
        m_IdtApi.CallRel(m_CallIdWatch, 200, usCause);
        m_CallIdWatch = -1;
    }

    var calltime = document.getElementById("id_call_time");
	calltime.textContent += "~";
    calltime.textContent += PUtility.PGetCurTime();
    calltime.textContent += "  ";
    calltime.textContent += IDT.GetCloseStr(IDT.CLOSE_BYUSER);
    calltime.textContent += ":";
    calltime.textContent += IDT.GetCauseStr(usCause);

    var callinfo = document.getElementById("id_call_info");
	callinfo.textContent = '讲话指示:';
    var callinfo = document.getElementById("id_call_talkinfo");
	callinfo.textContent = '讲话方:';

    m_bAutoMic = false;
}

function fn_sendnum()
{
    if (-1 == m_CallId)
        return;

    var strTo = document.getElementById("id_peer_num").value;

    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_NUM, strTo, null);
}

function fn_answer()
{
    m_IdtApi.CallAnswer(m_CallId, 100, id_video_my, id_video_peer, m_ARx, m_ATx, m_VRx, m_VTx);

    var calltime = document.getElementById("id_call_time");
	calltime.textContent = PUtility.PGetCurTime();
}

function fn_watch_down()
{
    var strTo = document.getElementById("id_peer_num").value;
    var strUserMark = document.getElementById("id_user_mark").value;
    var ARx = Number(document.getElementById("id_a_rx").value);
    var ATx = Number(document.getElementById("id_a_tx").value);
    var VRx = Number(document.getElementById("id_v_rx").value);

    m_CallIdWatch = m_IdtApi.CallMakeOut(200, null, id_video_watch, ARx, ATx, VRx, 0, strTo, IDT.SRV_TYPE_WATCH_DOWN,  "", 1, 0, strUserMark);
    var calltime = document.getElementById("id_call_time");
	calltime.textContent = PUtility.PGetCurTime();

    var callinfo = document.getElementById("id_call_info");
	callinfo.textContent = '讲话指示:';
    var callinfo = document.getElementById("id_call_talkinfo");
	callinfo.textContent = '讲话方:';

    //m_IdtApi.CallSetPeerVolume(m_CallId, 1.0);
}


function fn_watch_up()
{
    var strTo = document.getElementById("id_peer_num").value;
    var strUserMark = document.getElementById("id_user_mark").value;
    var ATx = Number(document.getElementById("id_a_tx").value);
    var VTx = Number(document.getElementById("id_v_tx").value);

    m_CallId = m_IdtApi.CallMakeOut(100, id_video_my, id_video_peer, 0, ATx, 0, VTx, strTo, IDT.SRV_TYPE_WATCH_UP,  "", 1, 0, strUserMark);
    var calltime = document.getElementById("id_call_time");
	calltime.textContent = PUtility.PGetCurTime();

    var callinfo = document.getElementById("id_call_info");
	callinfo.textContent = '讲话指示:';
    var callinfo = document.getElementById("id_call_talkinfo");
	callinfo.textContent = '讲话方:';
}

function fn_peer_volume()
{
    var volume = document.getElementById("peer_volume").value;

    m_IdtApi.CallSetPeerVolume(m_CallIdWatch, volume);
}

function fn_my_volume()
{
    var volume = document.getElementById("my_volume").value;

    m_IdtApi.CallSetMyVolume(m_CallId, volume);
}

function fn_capture()
{
    id_canvas.getContext('2d').drawImage(id_video_peer, 0, 0, id_canvas.width, id_canvas.height);
}

function fn_getStats()
{
    m_IdtApi.CallGetStats(m_CallId);
}

function fn_gcallout()
{
    var strTo = document.getElementById("id_conf_num").value;
    var strUserMark = document.getElementById("id_user_mark").value;
    m_CallId = m_IdtApi.CallMakeOut(100, id_video_my, id_video_peer, 0, 1, 0, 0, strTo, IDT.SRV_TYPE_CONF,  "", 1, 0, strUserMark);
    var calltime = document.getElementById("id_call_time");
	calltime.textContent = PUtility.PGetCurTime();
    m_bAutoMic = false;

    var callinfo = document.getElementById("id_call_info");
	callinfo.textContent = '讲话指示:';
    var callinfo = document.getElementById("id_call_talkinfo");
	callinfo.textContent = '讲话方:';
}

function fn_broadcast()
{
    var strTo = document.getElementById("id_conf_num").value;
    var strUserMark = document.getElementById("id_user_mark").value;
    m_CallId = m_IdtApi.CallMakeOut(100, id_video_my, id_video_peer, 0, 1, 0, 0, strTo, IDT.SRV_TYPE_CONF,  "", 1, 0, "", 0, strUserMark);
    var calltime = document.getElementById("id_call_time");
	calltime.textContent = PUtility.PGetCurTime();
    m_bAutoMic = false;

    var callinfo = document.getElementById("id_call_info");
	callinfo.textContent = '讲话指示:';
    var callinfo = document.getElementById("id_call_talkinfo");
	callinfo.textContent = '讲话方:';
}

function fn_micwant()
{
    m_IdtApi.CallMicCtrl(m_CallId, true);
}

function fn_micrel()
{
    m_IdtApi.CallMicCtrl(m_CallId, false);
}

function fn_ccallout()
{
    var strTo = document.getElementById("id_conf_num").value;
    var strUserMark = document.getElementById("id_user_mark").value;
    var iCallOutType = Number(document.getElementById("id_ccallout_type").value);;
    m_CallId = m_IdtApi.CallMakeOut(100, id_video_my, id_video_peer, 1, 1, 1, 1, strTo, IDT.SRV_TYPE_CONF,  "", iCallOutType, 0, strUserMark);
    var calltime = document.getElementById("id_call_time");
	calltime.textContent = PUtility.PGetCurTime();
    m_bAutoMic = false;

    var callinfo = document.getElementById("id_call_info");
	callinfo.textContent = '讲话指示:';
    var callinfo = document.getElementById("id_call_talkinfo");
	callinfo.textContent = '讲话方:';
}
function fn_ccall_url()
{
    var strTo = document.getElementById("id_conf_num").value;
    var strFrom = document.getElementById("id_my_num").value;
    var strText = '{"fromDesc":';
    strText += '"' + strFrom + '","fromNumber":';
    strText += '"' + strFrom + '","subPara":"{\\\"accept\\\":false,\\\"content\\\":\\\"会议开始通知, 点击加入\\\",\\\"desc\\\":\\\"调度员发起组会议:\\\",\\\"meetId\\\":\\\"';
    strText += strTo;
    strText += '\\\",\\\"number\\\":\\\"';
    strText += strTo;
    strText += '\\\",\\\"time\\\":\\\"1970-01-01 08:00:00\\\",\\\"title\\\":\\\"\\\",\\\"type\\\":2}","toDesc":"","toNumber":"';
    strText += strTo;
    strText += '","type":0}';
    m_IdtApi.IMSend(300, IDT.IM_TYPE_CONF, strTo, strText, null, null);
}

function fn_ccall_add()
{
    var strTo = document.getElementById("id_peer_num").value;
    m_IdtApi.CallUserCtrl(m_CallRef, strTo, 1, 1, 0, 1, 0);
}

function fn_ccall_del()
{
    var strTo = document.getElementById("id_peer_num").value;
    m_IdtApi.CallUserCtrl(m_CallRef, strTo, 0, 0, 0, 0, 0);
}
function fn_ccall_dial()
{
    var strTo = document.getElementById("id_conf_num").value;
    var strUserMark = document.getElementById("id_user_mark").value;
    m_CallId = m_IdtApi.CallMakeOut(100, id_video_my, id_video_peer, 1, 1, 1, 1, strTo, IDT.SRV_TYPE_CONF_JOIN,  "", 0, 0, strUserMark);
    var calltime = document.getElementById("id_call_time");
	calltime.textContent = PUtility.PGetCurTime();

    var callinfo = document.getElementById("id_call_info");
	callinfo.textContent = '讲话指示:';
    var callinfo = document.getElementById("id_call_talkinfo");
	callinfo.textContent = '讲话方:';
}
function fn_ccall_query()
{
    //查询会场状态
    var strTo = document.getElementById("id_conf_num").value;
    m_IdtApi.CallConfStatusReq(strTo, 1234);
}

function fn_ccall_talkflag()
{
    //自由发言
    if (m_bAutoMic)
    {
        m_IdtApi.CallConfCtrlReq(m_CallId, null, IDT.SRV_INFO_AUTOMICOFF, 1);//0是台上话权,1是台下话权.自由发言只针对台下话权
        m_bAutoMic = false;
    }
    else
    {
        m_IdtApi.CallConfCtrlReq(m_CallId, null, IDT.SRV_INFO_AUTOMICON, 1);//0是台上话权,1是台下话权.自由发言只针对台下话权
        m_bAutoMic = true;
    }
}
function fn_ccall_micgive0()
{
    //台上话权
    var strTo = document.getElementById("id_ccall_micnum").value;
    m_IdtApi.CallConfCtrlReq(m_CallId, strTo, IDT.SRV_INFO_MICGIVE, 0);
}
function fn_ccall_micgive1()
{
    //台下话权
    var strTo = document.getElementById("id_ccall_micnum").value;
    m_IdtApi.CallConfCtrlReq(m_CallId, strTo, IDT.SRV_INFO_MICGIVE, 1);
}
function fn_ccall_mictake()
{
    //收回话权
    var strTo = document.getElementById("id_ccall_micnum").value;
    m_IdtApi.CallConfCtrlReq(m_CallId, strTo, IDT.SRV_INFO_MICTAKE, 0xff);
}

function fn_ccallout_audio()
{
    var strTo = document.getElementById("id_conf_num").value;
    var strUserMark = document.getElementById("id_user_mark").value;
    m_CallId = m_IdtApi.CallMakeOut(100, id_video_my, id_video_peer, 1, 1, 0, 0, strTo, IDT.SRV_TYPE_CONF,  "", 1, 0, strUserMark, 1);
    var calltime = document.getElementById("id_call_time");
	calltime.textContent = PUtility.PGetCurTime();
    m_bAutoMic = true;
}

function fn_force_inj()
{
    //强插,强插只能强插音频
    var strTo = document.getElementById("id_force_num").value;
    var strUserMark = document.getElementById("id_user_mark").value;
    m_CallId = m_IdtApi.CallMakeOut(100, id_video_my, id_video_peer, 1, 1, 0, 0, strTo, IDT.SRV_TYPE_FORCE_INJ,  "", 0, 0, strUserMark);
    var calltime = document.getElementById("id_call_time");
	calltime.textContent = PUtility.PGetCurTime();

    var callinfo = document.getElementById("id_call_info");
	callinfo.textContent = '讲话指示:';
    var callinfo = document.getElementById("id_call_talkinfo");
	callinfo.textContent = '讲话方:';
}

function fn_force_rel()
{
    //强拆
    var strTo = document.getElementById("id_force_num").value;
    m_IdtApi.ForceRel(strTo);
}

function fn_fullscreen()
{
    var strB = myBrowser();
    if ("IE" == strB)
    {
        id_video_peer.RequestFullScreen();
    }
    else
    {
        PUtility.RequestFullScreen(id_video_peer);
    }
}

function fn_fullscreen1()
{
    var strB = myBrowser();
    if ("IE" == strB)
    {
        id_video_watch.RequestFullScreen();
    }
    else
    {
        PUtility.RequestFullScreen(id_video_watch);
    }
}

function fn_MicDetect_CallBack(status)
{
    console.log("fn_MicDetect_CallBack:", status);
}


var m_MicDetect = null;
function fn_MicDetect()
{
	if (null == m_MicDetect)
	{
        m_MicDetect = new MicDetect();
    }
    m_MicDetect.Run(-10.0, 2000, fn_MicDetect_CallBack);
}

var m_RecordRTC = null;

function stopRecordingCallback(audioURL)
{
    var blob = m_RecordRTC.getBlob();
    var file = new File([blob], '12345.mp4', {
        type: 'audio/mp4'
    });
    invokeSaveAsDialog(file, '12345.mp4');
    m_RecordRTC = null;
}

function fn_startsave()
{
    //var options = {
    //    recorderType: MediaStreamRecorder,
    //    mimeType: 'video/webm\;codecs=vp9'
    //};
    m_RecordRTC = RecordRTC(m_IdtApi.CallGetLocalStream(m_CallId), {type: 'audio'});
    m_RecordRTC.startRecording();
}

function fn_stopsave()
{
    m_RecordRTC.stopRecording(stopRecordingCallback);
}

function fn_cam_up()
{
    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_CAMCTRL, JSON.stringify({'CtrlCode' : IDT.SRV_CAMCTRL_UP,
        'CtrlValue' : Number(document.getElementById("id_cam_value").value)}), document.getElementById("id_peer_num").value);
}
function fn_cam_dowm()
{
    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_CAMCTRL, JSON.stringify({'CtrlCode' : IDT.SRV_CAMCTRL_DOWN,
        'CtrlValue' : Number(document.getElementById("id_cam_value").value)}), document.getElementById("id_peer_num").value);
}
function fn_cam_left()
{
    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_CAMCTRL, JSON.stringify({'CtrlCode' : IDT.SRV_CAMCTRL_LEFT,
        'CtrlValue' : Number(document.getElementById("id_cam_value").value)}), document.getElementById("id_peer_num").value);
}
function fn_cam_right()
{
    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_CAMCTRL, JSON.stringify({'CtrlCode' : IDT.SRV_CAMCTRL_RIGHT,
        'CtrlValue' : Number(document.getElementById("id_cam_value").value)}), document.getElementById("id_peer_num").value);
}
function fn_cam_zoomwide()
{
    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_CAMCTRL, JSON.stringify({'CtrlCode' : IDT.SRV_CAMCTRL_ZOOMWIDE,
        'CtrlValue' : Number(document.getElementById("id_cam_value").value)}), document.getElementById("id_peer_num").value);
}
function fn_cam_zoomtele()
{
    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_CAMCTRL, JSON.stringify({'CtrlCode' : IDT.SRV_CAMCTRL_ZOOMTELE,
        'CtrlValue' : Number(document.getElementById("id_cam_value").value)}), document.getElementById("id_peer_num").value);
}
function fn_cam_focusnear()
{
    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_CAMCTRL, JSON.stringify({'CtrlCode' : IDT.SRV_CAMCTRL_FOCUSNEAR,
        'CtrlValue' : Number(document.getElementById("id_cam_value").value)}), document.getElementById("id_peer_num").value);
}
function fn_cam_focusfar()
{
    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_CAMCTRL, JSON.stringify({'CtrlCode' : IDT.SRV_CAMCTRL_FOCUSFAR,
        'CtrlValue' : Number(document.getElementById("id_cam_value").value)}), document.getElementById("id_peer_num").value);
}
function fn_cam_irisopen()
{
    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_CAMCTRL, JSON.stringify({'CtrlCode' : IDT.SRV_CAMCTRL_IRISOPEN,
        'CtrlValue' : Number(document.getElementById("id_cam_value").value)}), document.getElementById("id_peer_num").value);
}
function fn_cam_irisclose()
{
    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_CAMCTRL, JSON.stringify({'CtrlCode' : IDT.SRV_CAMCTRL_IRISCLOSE,
        'CtrlValue' : Number(document.getElementById("id_cam_value").value)}), document.getElementById("id_peer_num").value);
}
function fn_cam_autoscan()
{
    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_CAMCTRL, JSON.stringify({'CtrlCode' : IDT.SRV_CAMCTRL_AUTOSCAN,
        'CtrlValue' : Number(document.getElementById("id_cam_value").value)}), document.getElementById("id_peer_num").value);
}
function fn_cam_criuse()
{
    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_CAMCTRL, JSON.stringify({'CtrlCode' : IDT.SRV_CAMCTRL_CRUISE,
        'CtrlValue' : Number(document.getElementById("id_cam_value").value)}), document.getElementById("id_peer_num").value);
}
function fn_cam_infrared()
{
    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_CAMCTRL, JSON.stringify({'CtrlCode' : IDT.SRV_CAMCTRL_INFRARED,
        'CtrlValue' : Number(document.getElementById("id_cam_value").value)}), document.getElementById("id_peer_num").value);
}
function fn_cam_rainstrip()
{
    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_CAMCTRL, JSON.stringify({'CtrlCode' : IDT.SRV_CAMCTRL_RAINSTRIP,
        'CtrlValue' : Number(document.getElementById("id_cam_value").value)}), document.getElementById("id_peer_num").value);
}
function fn_cam_preset()
{
    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_CAMCTRL, JSON.stringify({'CtrlCode' : IDT.SRV_CAMCTRL_PRESET,
        'CtrlValue' : Number(document.getElementById("id_cam_value").value)}), document.getElementById("id_peer_num").value);
}
function fn_cam_reboot()
{
    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_CAMCTRL, JSON.stringify({'CtrlCode' : IDT.SRV_CAMCTRL_REBOOT,
        'CtrlValue' : Number(document.getElementById("id_cam_value").value)}), document.getElementById("id_peer_num").value);
}
function fn_cam_stop()
{
    m_IdtApi.CallSendInfo(m_CallId, IDT.SRV_INFO_CAMCTRL, JSON.stringify({'CtrlCode' : IDT.SRV_CAMCTRL_STOP,
        'CtrlValue' : Number(document.getElementById("id_cam_value").value)}), document.getElementById("id_peer_num").value);
}



//添加组
function fn_GAdd()
{
    var strGNum = document.getElementById("id_group_num").value;
    var strGName = document.getElementById("id_group_name").value;
    var iType = Number(document.getElementById("id_group_type").value);
    var iPrio = Number(document.getElementById("id_group_prio").value);
    var strAGNum = document.getElementById("id_group_agnum").value;
    var strOrgNum = document.getElementById("id_org_num").value;

    var jGroup = {
        GNum        : strGNum,
        GName       : strGName,
        GType       : iType,
        Prio        : iPrio,
        AGNum       : strAGNum,
        OrgListMgr  :
		[
            {
                Num     : strOrgNum
            }
        ]
        };
    var jFather = [
        {"Num" : "20018888810", "Prio" : 7}
        ];
    var jMember = [
        {"Prio":7,"Type":1,"Num":"2001"},
        {"Prio":7,"Type":1,"Num":"2011"},
        {"Prio":7,"Type":1,"Num":"2012"},
        {"Prio":7,"Type":1,"Num":"2013"}
        ];
    //m_IdtApi.GAdd(jGroup, jFather, jMember, fn_OamCallBack);
    m_IdtApi.GAdd(jGroup, null, null, fn_OamCallBack);
}

//删除组
function fn_GDel()
{
    var strGNum = document.getElementById("id_group_num").value;

    m_IdtApi.GDel(strGNum, fn_OamCallBack);
}

//修改组
function fn_GModify()
{
    var strGNum = document.getElementById("id_group_num").value;
    var strGName = document.getElementById("id_group_name").value;
    var iType = Number(document.getElementById("id_group_type").value);
    var iPrio = Number(document.getElementById("id_group_prio").value);
    var strAGNum = document.getElementById("id_group_agnum").value;
    var strOrgNum = document.getElementById("id_org_num").value;

    var jGroup = {
        GNum        : strGNum,
        GName       : strGName,
        GType       : iType,
        Prio        : iPrio,
        AGNum       : strAGNum,
        OrgListMgr  :
		[
            {
                Num     : strOrgNum
            }
        ]
        };
    m_IdtApi.GModify(jGroup, fn_OamCallBack);
}


//组添加用户
function fn_GAddU()
{
    var strGNum = document.getElementById("id_group_num").value;
    var strUNum = document.getElementById("id_group_unum").value;
    var ucType = Number(document.getElementById("id_group_utype").value);
    var ucPrio = Number(document.getElementById("id_group_uprio").value);
    m_IdtApi.GAddU(strGNum, strUNum, ucType, ucPrio, fn_OamCallBack);
}

//组删除用户
function fn_GDelU()
{
    var strGNum = document.getElementById("id_group_num").value;
    var strUNum = document.getElementById("id_group_unum").value;
    m_IdtApi.GDelU(strGNum, strUNum, fn_OamCallBack);
}

//组修改用户
function fn_GModifyU()
{
    var strGNum = document.getElementById("id_group_num").value;
    var strUNum = document.getElementById("id_group_unum").value;
    var ucType = Number(document.getElementById("id_group_utype").value);
    var ucPrio = Number(document.getElementById("id_group_uprio").value);
    m_IdtApi.GModifyU(strGNum, strUNum, ucType, ucPrio, fn_OamCallBack);
}



