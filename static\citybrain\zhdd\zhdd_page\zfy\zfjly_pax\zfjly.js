var zfjly = {
    get: axios.create({baseURL: "/zfjly_pax", withCredentials: true,}).get,
    post: axios.create({baseURL: "/zfjly_pax", withCredentials: true,}).post,
    dataInfo_set: [],
    form: {
        hostbodyArr: null,
        hostbody: null,
    },
    dataInfo_hostbody: null,
    wsUrl: null,
    ws: null,
    timerId: null,
    isAudio: false,
    isC2: false,
    wsChannelId: null,
    sn: null,
    player: null,
    recorders: null,

    //登录执法记录仪平台
    loginZFJLY: function () {
        return new Promise((resolve) => {
            this.getLoginBaseInfo("").then((res) => {
                var send = {
                    username: "paadmin",
                    pwd: "Pa@258369!",
                    token: res.data,
                };
                this.login(send).then((el) => {
                    if (el.data.code === 200) {
                        this.isShow = true;
                        this.heartbeat().then(() => {
                            this.getUserInformation().then((e) => {
                                var _this = this;
                                try {
                                    zfjly.wsUrl = e.data.data.wsurl;
                                    this.createWebSocket(e.data.data.wsurl, e.data.data);
                                } catch (error) {
                                    console.log(error);
                                    resolve(false);
                                }
                                resolve(true);
                            });
                        });
                    } else {
                        resolve(false);
                    }
                });
            });
        });
    },

    // 创建WebSocket连接
    createWebSocket: function(wsUrl, userData) {
        var _this = this;
        try {
            this.ws = new WebSocket(wsUrl);
        } catch (error) {
            console.log("WebSocket创建失败:", error);
            return;
        }

        var data1 = {
            logincode: userData.logincode,
            username: userData.username,
            scode: userData.scode,
            cate: userData.auth_cate,
        };

        var psd = {
            command: "client_login",
            data: JSON.stringify(data1),
        };

        this.ws.onopen = function () {
            console.log("WebSocket已连接，发送登录信息");
            _this.ws.send(JSON.stringify(psd));
        };

        this.ws.onerror = function (e) {
            console.warn("WebSocket连接出错:", e);
            _this.ws.close();
            _this.ws = null;
        };

        this.ws.onclose = function () {
            console.log("WebSocket已断开连接");
        };

        this.ws.onmessage = function (event) {
            console.log("WebSocket收到消息:", event.data);
            var data = JSON.parse(event.data);

            // 处理site_data信息
            if (data.site_data) {
                var dataInfo = data.site_data;

                // 处理开始播放视频事件
                if (dataInfo.start_live) {
                    if (dataInfo.start_live.recorder_type == "2") {
                        _this.isC2 = true;
                    } else {
                        _this.isC2 = false;
                    }
                    var ws = "ws:/" + dataInfo.start_live.wsInfo.wsIp + ":" + dataInfo.start_live.wsInfo.wsPort;
                    var viewId = dataInfo.start_live.wsInfo.wsViewId;
                    window.pullFlow_vms2(ws, viewId);
                }

                // 处理开始音频事件
                if (dataInfo.start_audio) {
                    _this.isAudio = true;
                    var wss = `ws://${dataInfo.start_audio.wsInfo.wsIp}:${dataInfo.start_audio.wsInfo.wsPort}`;
                    _this.wsChannelId = dataInfo.start_audio.wsInfo.wsChannelId;
                    _this.sn = dataInfo.start_audio.wsInfo.sn;
                    window.voice_pull_vms2(wss, _this.wsChannelId);
                }

                // 处理预设点事件
                if (dataInfo.preset) {
                    window.preset2(dataInfo.preset.data);
                }
            }
        };
    },

    getLoginBaseInfo: function (data) {
        return this.get(`rest/index/login/get?key=${data}`)
    },

    login: function (param) {
        var base64 = {
            encode: function (str) {
                return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function (_, hex) {
                    return String.fromCharCode('0x' + hex);
                }));
            },
            decode: function (str) {
                return decodeURIComponent(atob(str).split('').map(function (c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));
            }
        };
        let {username, pwd, token, captcha_code} = param;
        let password = hex_md5(pwd);
        let login_if = base64.encode(JSON.stringify({ username, password }));
        let data = this.param_up({login_info: login_if, token, captcha_code, 'withCredentials': true});
        return this.post('/rest/index/login/login', data);
    },

    param_up: function (param_arr) {
        var keys = Object.keys(param_arr).sort();
        var string = "";
        for (var i = 0; i < keys.length; i++) {
            var k = keys[i];
            string += k + "=" + param_arr[k] + ";";
        }
        string += hex_md5("Pe2695jingyi");
        let str_encode = encodeURIComponent(string);
        //编码后MD5加密
        param_arr.pe_signals = hex_md5(str_encode);
        return JSON.stringify(param_arr);
    },

    getUserInformation: function () {
        return this.get('/rest/user/user/get_info');
    },

    //心跳
    heartbeat: function () {
        return new Promise((resolve) => {
            this.heart();
            this.timerId = setInterval(this.heart, 20000);
            resolve();
        });
    },

    heart: function () {
        zfjly.online().then((e) => {
            console.log("心跳检测:", e);
        }).catch(err => {
            console.error("心跳检测失败:", err);
        });
    },

    online: function () {
        return this.get('rest/other/user/online');
    },

    //获取设备信息
    getDeviceInfo: function () {
        return new Promise((resolve) => {
            this.unitEquipTree("1001", "bh", "dname", false).then((res) => {
                var lineon = [];
                let info = {
                    ids: []
                };
                res.data.data.forEach((item) => {
                    if (item.lineon == 1) {
                        this.dataInfo_set.push(item);
                        lineon.push(item.hostbody);
                        info.ids.push(item.hostbody)
                    }
                });
                this.getPosition(info).then((res) => {
                    let data = res.data.data;
                    if (data && data.length > 0) {
                        data.forEach((item) => {
                            let lat = item.lat;
                            let lng = item.lng;
                            let name = item.name;
                        });
                    }
                    this.pulldata = res.data;
                    this.dataInfo_hostbody = lineon.toString();
                    this.form.hostbodyArr = this.dataInfo_hostbody;
                    resolve(this.dataInfo_set);
                }).catch(err => {
                    console.error("获取位置信息失败:", err);
                    this.pulldata = res.data;
                    this.dataInfo_hostbody = lineon.toString();
                    this.form.hostbodyArr = this.dataInfo_hostbody;
                    resolve(this.dataInfo_set);
                });
            }).catch(err => {
                console.error("获取设备信息失败:", err);
                resolve([]);
            });
        });
    },

    unitEquipTree: function (id = '', bh = 'bh', text = 'dname', isNewapi = false) {
        let data = {
            "id": id,
            "bh": bh,
            "text": text
        };
        this.extendSignal(data);
        if (isNewapi) {
            return this.post('/rest/other/unitjson/gdlist_dv', data);
        } else {
            return this.post('/rest/other/unitjson/gdlist', data);
        }
    },

    extendSignal: function (target) {
        let keys = Object.keys(target),
          arr = [],
          solt = "Pe2695jingyi",
          str,
          pe_signals;
        keys.sort(); // 排序
        keys.forEach((key) => {
            const value = JSON.stringify(target[key]);
            arr.push(`${key}=${value}`);
        });
        str = arr.join(";") + hex_md5(solt);
        str = encodeURIComponent(str);
        pe_signals = hex_md5(str);
        target.pe_signals = pe_signals;
        return target;
    },

    //获取设备经纬度信息
    getPosition: function (data) {
        this.extendSignal(data)
        return this.post('/rest/gis/gismoni/get_point', data)
    },

    // 开始拉流指定设备的视频
    startLive: function(hostbody) {
        var send = {
            hostbody_arr: Array.isArray(hostbody) ? hostbody : [hostbody || "T0C0223"],
        };

        return new Promise((resolve, reject) => {
            this.startLiveVideo(send).then(res => {
                if (res.data.code == 200 && res.data.data[0].is_existed) {
                    // 使用WebRTC方式播放
                    const rtspUrl = res.data.data[0]?.play_info?.rtsp_url || res.data.data[0].rtsp_url;
                    // 使用固定的WebRTC服务地址
                    const webRtcUrl = res.data.data[0]?.play_info?.webrtc_url || res.data.data[0].webrtc_url;

                    // 保存sn值，用于停止视频流
                    if (res.data.data[0].sn) {
                        this.sn = res.data.data[0].sn;
                    }

                    console.log("准备使用WebRTC播放视频流:", rtspUrl, "SN:", res.data.data[0].sn || "1213");

                    // 返回完整的视频流信息
                    resolve({
                        data: {
                            code: 200,
                            data: [{
                                ...res.data.data[0],
                                webrtc_url: webRtcUrl,
                                rtsp: rtspUrl,
                                is_existed: true,
                                play_info: {
                                    webrtc_url: webRtcUrl
                                },
                                // 确保sn字段被传递
                                sn: res.data.data[0].sn || "1213" // 如果没有sn则使用默认值
                            }]
                        }
                    });
                } else {
                    // 设备不在线或其他错误
                    resolve(res);
                }
            }).catch(error => {
                console.error("开始视频流失败:", error);
                reject(error);
            });
        });
    },

    // 创建WebRTC播放器并播放视频流
    createWebRTCPlayer: function(videoElement, webRtcUrl, streamUrl) {
        if (!videoElement) {
            console.error("播放器元素不存在");
            return Promise.reject("播放器元素不存在");
        }

        return new Promise((resolve, reject) => {
            try {
                // 创建WebRTC播放器配置
                const webrtcConfig = {
                    element: videoElement,
                    debug: true,
                    zlmsdpUrl: webRtcUrl,
                    simulcast: false,
                    useCamera: false,
                    audioEnable: true,
                    videoEnable: true,
                    recvOnly: true,
                    resolution: { w: 1280, h: 720 },
                    usedatachannel: false
                };

                // 创建WebRTC播放器实例
                const webrtcPlayer = new Webrtc(webrtcConfig);

                // 开始播放
                webrtcPlayer.start_play().then(stream => {
                    console.log("WebRTC播放成功:", stream);
                    this.player = webrtcPlayer;
                    resolve({
                        player: webrtcPlayer,
                        stream: stream,
                        type: true
                    });
                }).catch(error => {
                    console.error("WebRTC播放失败:", error);
                    reject(error);
                });
            } catch (error) {
                console.error("创建WebRTC播放器失败:", error);
                reject(error);
            }
        });
    },

    //开始视频流
    startLiveVideo: function (data) {
        this.extendSignal(data);
        return this.post('/rest/live/chrome/startLive', data);
    },

    // 开始音频流
    startLiveAudio: function (data) {
        this.extendSignal(data);
        return this.post('/rest/live/chrome/startAudioInVideo', data);
    },

    // 发送命令
    send_cmd: function (data) {
        this.extendSignal(data)
        return this.post('/rest/gis/gismoni/send_cmd', data)
    },

    // 停止视频流
    stopLive: function(data) {
        this.extendSignal(data);
        return this.post('/rest/live/chrome/stopLive', data);
    },

    // 停止音频
    stopAudio: function(data) {
        this.extendSignal(data);
        return this.post('/rest/live/chrome/stopAudio', data);
    },

    // 音频对讲相关属性
    pcStartAudio: null,
    isStartAudio: 0,
    dynamicAudioDiv: null,
    isVolumeOn: false, // 声音开关状态

    // 拉流中的对讲 - 参考7.0-demo/js/http.js的实现
    startAudioInVideo: function(hostbody) {
        return new Promise((resolve, reject) => {
            let sent = {
                hostbody: hostbody,
            };

            // 使用startLiveAudio接口，适配当前系统
            this.startLiveAudio(sent).then((res) => {
                console.log("startAudioInVideo response:", res);
                if (res.data && res.data.code === 200 && res.data.data) {
                    // 适配返回格式，添加platform_webrtc_push_url
                    const audioData = res.data.data;
                    if (audioData.platform_webrtc_push_url) {
                        // 将webrtc_url作为platform_webrtc_push_url使用
                        resolve({
                            code: 200,
                            data: {
                                platform_webrtc_push_url: audioData.platform_webrtc_push_url
                            }
                        });
                    } else {
                        reject("未获取到音频推流地址");
                    }
                } else {
                    reject("设备不在线或不支持对讲");
                }
            }).catch((err) => {
                console.error("startAudioInVideo error:", err);
                reject(err);
            });
        });
    },

    // 停止音频对讲
    stopAudioInVideo: function(hostbodyArr) {
        return new Promise((resolve) => {
            console.log("停止音频对讲:", hostbodyArr);
            // 调用stopLiveAudio接口
            this.stopLiveAudio({
                hostbody_arr: hostbodyArr
            }).then(() => {
                resolve();
            }).catch(() => {
                resolve(); // 即使失败也继续执行
            });
        });
    },

    // 设备静音控制
    sendMute: function(imei, mute, hostbody = null) {
        return new Promise((resolve, reject) => {
            console.log("设置设备静音:", imei, mute);

            // 如果没有传入imei，尝试从当前设备获取
            const executeCommand = (deviceImei) => {
                if (this.send_cmd) {
                    let send = {
                        imei: deviceImei,
                        type: mute ? "startmute" : "stopmute", // true为开启静音，false为关闭静音
                    };

                    this.send_cmd(send).then((res) => {
                        console.log("设备静音控制成功:", res);
                        resolve(res);
                    }).catch((err) => {
                        console.error("设备静音控制失败:", err);
                        reject(err);
                    });
                } else {
                    // 如果没有send_cmd接口，直接resolve
                    console.warn("send_cmd 方法不可用，跳过设备静音控制");
                    resolve();
                }
            };

            if (imei) {
                // 如果已有imei，直接执行
                executeCommand(imei);
            } else if (hostbody || this.form.hostbody) {
                // 如果没有imei，尝试从设备获取
                this.getDeviceImei(hostbody || this.form.hostbody).then((deviceImei) => {
                    executeCommand(deviceImei);
                }).catch((err) => {
                    console.warn("获取设备imei失败:", err, "跳过静音控制");
                    resolve(); // 即使失败也继续执行
                });
            } else {
                console.warn("无法获取设备信息，跳过静音控制");
                resolve();
            }
        });
    },

    // 获取设备imei
    getDeviceImei: function(hostbody) {
        return new Promise((resolve, reject) => {
            this.unitEquipTree("1001", "bh", "dname", false).then((res) => {
                if (res.data && res.data.data) {
                    const device = res.data.data.find(item => item.hostbody === hostbody);
                    if (device && device.imei) {
                        resolve(device.imei);
                    } else {
                        reject("未找到设备或设备无imei信息");
                    }
                } else {
                    reject("获取设备列表失败");
                }
            }).catch(err => {
                reject("获取设备信息失败: " + err);
            });
        });
    },

    // 设置声音状态
    setVolume: function(videoElement, isOn, voiceButton) {
        return new Promise((resolve) => {
            console.log("设置声音状态:", isOn);

            this.isVolumeOn = isOn;

            if (isOn) {
                // 开启声音
                if (voiceButton) {
                    voiceButton.setAttribute("class", "btn volume-on");
                    voiceButton.textContent = "关闭声音";
                }
                if (videoElement) {
                    videoElement.muted = false;
                }
            } else {
                // 关闭声音
                if (voiceButton) {
                    voiceButton.setAttribute("class", "btn volume-off");
                    voiceButton.textContent = "开启声音";
                }
                if (videoElement) {
                    videoElement.muted = true;
                }
            }

            resolve({
                success: true,
                isVolumeOn: this.isVolumeOn,
                message: this.isVolumeOn ? "声音已开启" : "声音已关闭"
            });
        });
    },

    // 获取当前声音状态
    getVolumeStatus: function() {
        return {
            isVolumeOn: this.isVolumeOn,
            message: this.isVolumeOn ? "声音已开启" : "声音已关闭"
        };
    },

    // 启动音频对讲 - 参考7.0-demo/DynamicDom.js中startAudioInVideo的实现
    startAudioIntercom: function(hostbody) {
        return new Promise((resolve, reject) => {
            if (!hostbody) {
                reject("设备编码不能为空");
                return;
            }

            // 使用startAudioInVideo获取音频推流地址
            this.startAudioInVideo(hostbody).then((res) => {
                if (res.code == 200) {
                    console.log("获取音频推流地址成功:", res.data.platform_webrtc_push_url);

                    // res.data.platform_webrtc_push_url 平台推流地址
                    // 如果其他窗口推了麦克风的流，就不用走这里啦
                    if (!this.isStartAudio) {
                        var InitialData = {
                            element: "",
                            debug: true,
                            zlmsdpUrl: res.data.platform_webrtc_push_url,
                            simulcast: false,
                            useCamera: false,
                            audioEnable: true,
                            videoEnable: false,
                            recvOnly: false,
                            resolution: { w: 3840, h: 2160 },
                            usedatachannel: false,
                        };

                        // 确保Webrtc类可用
                        if (typeof Webrtc === 'undefined') {
                            reject("Webrtc类未加载");
                            return;
                        }

                        this.pcStartAudio = new Webrtc(InitialData);
                        this.pcStartAudio.start_play().then(() => {
                            this.isStartAudio++;
                            console.log("音频推流启动成功，isStartAudio:", this.isStartAudio);

                            // 监听连接失败事件
                            this.pcStartAudio.on("failed", () => {
                                console.log("音频推流网络断开");
                                this.stopAudioIntercom(hostbody);
                            });

                            resolve({
                                success: true,
                                message: "音频对讲启动成功"
                            });

                        }).catch((err) => {
                            console.error("音频推流启动失败:", err);
                            reject("音频推流启动失败: " + err);
                        });
                    } else {
                        // 如果已经有音频推流，直接标记为活跃
                        this.isStartAudio++;
                        console.log("复用现有音频推流，isStartAudio:", this.isStartAudio);
                        resolve({
                            success: true,
                            message: "音频对讲启动成功(复用现有推流)"
                        });
                    }
                } else {
                    reject("获取音频推流地址失败");
                }
            }).catch(err => {
                console.error("音频对讲初始化失败:", err);
                reject("音频对讲初始化失败: " + err);
            });
        });
    },

    // 停止音频对讲
    stopAudioIntercom: function(hostbody) {
        return new Promise((resolve) => {
            console.log("停止音频对讲:", hostbody);

            // 减少音频推流计数器
            this.isStartAudio--;

            // 如果计数器归零，关闭全局音频推流
            if (this.isStartAudio <= 0) {
                if (this.pcStartAudio) {
                    try {
                        this.pcStartAudio.close_play();
                        this.pcStartAudio = null;
                        console.log("全局音频推流已关闭");
                    } catch (err) {
                        console.error("关闭音频推流失败:", err);
                    }
                }
                this.isStartAudio = 0; // 确保不会变成负数
            }

            // 调用停止音频接口
            if (hostbody) {
                this.stopAudioInVideo([hostbody]).then(() => {
                    console.log("音频对讲接口已停止");
                    resolve({
                        success: true,
                        message: "音频对讲已停止"
                    });
                }).catch(err => {
                    console.error("停止音频对讲接口失败:", err);
                    resolve({
                        success: false,
                        message: "停止音频对讲接口失败: " + err
                    });
                });
            } else {
                resolve({
                    success: true,
                    message: "音频对讲已停止"
                });
            }

            console.log("音频对讲已停止，当前isStartAudio:", this.isStartAudio);
        });
    },

    // 关闭所有资源
    closeAll: function() {
        // 停止音频对讲
        if (this.isStartAudio > 0) {
            this.stopAudioIntercom(this.form.hostbody);
        }

        if (this.player) {
            this.player = null;
        }

        if (this.recorders) {
            this.recorders.closeWebsocket();
            this.recorders = null;
        }

        if (this.timerId) {
            clearInterval(this.timerId);
            this.timerId = null;
        }

        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }

        window.parent.lay.closeIframeByNames(["videoTest"]);
    }
}
