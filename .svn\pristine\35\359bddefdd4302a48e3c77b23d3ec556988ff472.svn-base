<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>无人机详情</title>
    <script src="/Vue/vue.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <style>
      [v-cloak] {
        display: none;
      }
      body {
        margin: 0;
        padding: 0;
      }
      #app {
        width: 1200px;
        height: 680px;
        position: relative;
        background-image: url("/static/images/zhdd/bg_panel.png");
        background-size: 100% 100%;
      }
      .title {
        font-size: 36px;
        padding: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .close {
        width: 46px;
        height: 78px;
        font-size: 60px;
        color: #fff;
        font-weight: 600;
        cursor: pointer;
        position: absolute;
        right: 40px;
        top: 20px;
      }
      .content {
        width: 100%;
        padding: 0px 60px;
        box-sizing: border-box;
      }
      /* 表格 */
      .table {
        width: 100%;
        /* height: 500px; */
        padding: 10px 30px;
        box-sizing: border-box;
      }
      .table .th {
        width: 100%;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        font-weight: 700;
        font-size: 28px;
        line-height: 60px;
        color: #ffffff;
      }
      .table .th_td {
        letter-spacing: 0px;
        text-align: center;
      }
      .table .tbody {
        width: 100%;
        height: calc(100% - 60px);
        /* overflow-y: auto; */
        overflow: hidden;
      }
      .table .tbody:hover {
        overflow-y: auto;
      }
      .table .tbody::-webkit-scrollbar {
        width: 4px;
        /*滚动条整体样式*/
        height: 4px;
        /*高宽分别对应横竖滚动条的尺寸*/
      }
      .table .tbody::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: #20aeff;
        height: 8px;
      }
      .table .tr {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        height: 70px;
        line-height: 70px;
        font-size: 28px;
        color: #ffffff;
        cursor: pointer;
        border-top: 1px solid #959aa1;
        border-image: linear-gradient(to right, #e9f5ff3b, #f5ffffd4, #e9f5ff3b)
          1;
        padding: 0 15px;
        box-sizing: border-box;
      }
      .table .tr:nth-child(2n) {
        background: rgba(50, 134, 248, 0.2);
      }

      .table .tr:nth-child(2n + 1) {
        background: rgba(50, 134, 248, 0.12);
      }
      .table .tr:hover {
        background-color: #0074da75;
      }
      .table .tr_td {
        letter-spacing: 0px;
        text-align: center;
        box-sizing: border-box;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="title">
        <span class="s-c-yellow-gradient s-w7 s-font-40"></span>
        <div class="close" @click="close()">×</div>
      </div>
      <div class="content">
        <div class="table" style="height: 571px">
          <div class="th">
            <div class="th_td" style="flex: 0.3">县市区</div>
            <div class="th_td" style="flex: 0.2">无人机数量</div>
          </div>
          <div class="tbody">
            <div class="tr" v-for="(item,index) in wrjData" :key="index">
              <div class="tr_td" style="flex: 0.3">{{item.city}}</div>
              <div class="tr_td" style="flex: 0.2">{{item.num+item.unit}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      var vm = new Vue({
        el: " #app",
        data() {
          return {
            wrjData: [
              { city: "婺城区", num: 1, unit: "架" },
              { city: "金东区", num: 5, unit: "架" },
              { city: "兰溪市", num: 9, unit: "架" },
              { city: "东阳市", num: 0, unit: "架" },
              { city: "义乌市", num: 10, unit: "架" },
              { city: "永康市", num: 0, unit: "架" },
              { city: "浦江县", num: 0, unit: "架" },
              { city: "武义县", num: 0, unit: "架" },
              { city: "磐安县", num: 0, unit: "架" },
              { city: "开发区", num: 0, unit: "架" },
            ],
          };
        },
        mounted() {},
        methods: {
          close() {
            window.parent.lay.closeIframeByNames(["wrj_diong"]);
          },
        },
      });
    </script>
  </body>
</html>
