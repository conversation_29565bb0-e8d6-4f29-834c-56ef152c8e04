<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>网格信息详情</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/Vue/vue.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
      }
      .sjzx-middle {
        position: relative;
        /* top: 500px;
        left: 50%; */
        width: 1040px;
        height: 720px;
        /* width: 2168px;
        height: 1617px; */
        /* width: max-content;
        height: max-content; */
        /* background-color: #031827;
        box-shadow: -3px 2px 35px 0px #000000;
        border-style: solid;
        border-width: 2px;
        border-image-source: linear-gradient(-32deg, #359cf8 0%, #afdcfb 100%);
        border-image-slice: 1; */
        background-image: url("/static/citybrain/tcgl/img/bg2.png");
        background-size: 100% 100%;
      }
      .head {
        width: 100%;
        height: 135px;
        /* background-image: linear-gradient(0deg, #073346 0%, #00aae2 100%), linear-gradient(#ffffff, #ffffff);
        background-blend-mode: normal, normal; */
        padding: 10px 50px;
        box-sizing: border-box;
        display: flex;
        align-items: end;
        justify-content: space-between;
        background: linear-gradient(
          180deg,
          #ffffff 0%,
          #ffeccb 50.244140625%,
          #ffc460 53.0029296875%,
          #ffeccb 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .head > span {
        font-size: 48px !important;
        font-weight: 500;
        color: #fff;
        display: inline-block;
        margin-right: 20px;
      }

      .img {
        cursor: pointer;
        display: inline-block;
        margin: 20px;
        float: right;
        width: 34px;
        height: 34px;
        background-image: url("/static/citybrain/tcgl/img/close-hover.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .sjzx-middle-con {
        /* width: 100%;*/
        height: calc(100% - 240px);
        width: 1040px;
        padding: 0px 50px;
        box-sizing: border-box;
        overflow-y: scroll;
      }
      .sjzx-middle-con::-webkit-scrollbar {
        width: 4px;
        /*滚动条整体样式*/
        height: 4px;
        /*高宽分别对应横竖滚动条的尺寸*/
      }
      .sjzx-middle-con::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: #20aeff;
        height: 8px;
      }
      /*  */
      .header {
        height: 80px;
        line-height: 80px;
        background: url("/static/citybrain/tcgl/img/wgy_bg.png") no-repeat;
        background-size: 100% 100%;
      }
      .header::before {
        content: "";
        display: inline-block;
        width: 35px;
        height: 35px;
        text-align: left;
        margin-right: 30px;
        background: url("/static/citybrain/tcgl/img/headerlef.png");
        animation: jumpBoxHandler1 2s infinite;
      }
      .topLef {
        height: 190px;
        width: 100%;
        display: flex;
        justify-content: space-evenly;
        align-items: end;
      }
      .header::after {
        content: "";
        display: inline-block;
        width: 35px;
        height: 35px;
        margin-left: 30px;
        background: url("/static/citybrain/tcgl/img/headerght.png") no-repeat;
        animation: jumpBoxHandler2 2s infinite;
      }

      @keyframes jumpBoxHandler1 {
        0% {
          transform: translate(0px, 0px); /*开始位置*/
        }
        50% {
          transform: translate(15px, 0px); /* 可配置跳动方向 */
        }
        100% {
          transform: translate(0px, 0px); /*结束位置*/
        }
      }
      @keyframes jumpBoxHandler2 {
        0% {
          transform: translate(0px, 0px); /*开始位置*/
        }
        50% {
          transform: translate(-15px, 0px); /* 可配置跳动方向 */
        }
        100% {
          transform: translate(0px, 0px); /*结束位置*/
        }
      }

      .list_box {
        cursor: pointer;
        width: 50%;
        height: 205px;
        padding: 10px 0;
        /* background: url('/static/citybrain/csdn/img/cstz2-middle/blue-click.png') no-repeat; */
        background-size: 100% 100%;
        border-bottom: 1px solid #fff;
      }
      table {
        position: relative;
        width: 100%;
        border-collapse: separate;
        border-spacing: 0px 15px;
      }
      .line_bot {
        display: inline-block !important;
        position: absolute;
        left: 0px;
        width: 100%;
        border: 1px solid;
        border-image: linear-gradient(
            to right,
            #03357400,
            #85adf824,
            #82abf863,
            #85adf824,
            #03357400
          )
          1;
      }
      .line_right {
        display: inline-block !important;
        position: absolute;
        top: 0;
        left: 50%;
        height: 100%;
        margin: -10px;
        border: 1px solid;
        border-image: linear-gradient(
            to bottom,
            #03357400,
            #85adf824,
            #82abf863,
            #85adf824,
            #03357400
          )
          1;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="sjzx-middle">
        <div class="head">
          <span>{{titleName}}</span>
          <div class="img" @click="closeMiddleIframe('wg_map_main')"></div>
        </div>
        <div
          style="
            display: flex;
            justify-content: flex-start;
            align-items: center;
          "
        >
          <div class="s-c-blue-gradient s-font-40" style="margin-left: 50px">
            网格事件数
          </div>
          <div class="s-font-40" style="margin-left: 20px; color: #ffffff">
            {{eventNumber}}件
          </div>

          <div class="s-c-blue-gradient s-font-40" style="margin-left: 50px">
            协同事件数
          </div>
          <div class="s-font-40" style="margin-left: 20px; color: #ffffff">
            {{xtNumber}}件
          </div>
        </div>
        <div class="sjzx-middle-con s-flex s-flex-wrap s-row-around">
          <div
            class="s-font-40 s-text-center s-c-white"
            v-show="nameList.length==0"
          >
            暂无数据
          </div>
          <table>
            <tr v-for="(item,index) in table">
              <th v-for="ele in item ">
                <p class="header s-font-40" style="margin-top: 30px">
                  <span class="s-c-blue-gradient">{{ele.zzsf}}</span>
                </p>
                <p class="s-c-white s-font-45">{{xzzfzx.handleName(ele.xm)}}</p>
                <p class="s-c-white s-font-45">
                  {{ele.sjhm}}
                  <img
                    @click="callPhone(ele.allsjhm)"
                    :style="{cursor: ele.allsjhm?'pointer':'no-drop'}"
                    src="/static/citybrain/tcgl/img/dianhua.png"
                    alt=""
                  />
                </p>
              </th>
              <th :class="{'line_bot':index!=0}"></th>
            </tr>
            <tr :class="{'line_right':1<nameList.length }"></tr>
          </table>
          <!-- <div v-for="item,i in nameList" class="s-text-center list_box" @click="doSpth(item.allsjhm)">
            <p class="header s-c-blue-gradient s-font-40" style="margin: 6px 0">{{item.zzsf}}</p>
            <p class="s-c-white s-font-45">{{item.xm}}</p>
            <p class="s-c-white s-font-45">{{item.sjhm}}</p>
          </div> -->
        </div>
      </div>
    </div>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script>
      window.addEventListener("message", function (e) {
        if (e.data && e.data.id == "qkwg_open") {
          vm.titleName = `${e.data.obj.szqx}-${e.data.obj.szz}-${
            e.data.obj.szsq
          }
          ${(e.data.obj.name && "-" + e.data.obj.name) || ""}`;
          let axiosCode = e.data.obj.oid_ ? e.data.obj.oid_ : e.data.obj.oid_1;
          $api("gis_wgll_13", { oid: axiosCode }).then((res) => {
            vm.nameList = res;
          });
          $api("csdn_yjyp_wgsjs_xz",{
            area_name: "'" + vm.titleName.split("-")[0].trim() + "'",
            xz: "'" + vm.titleName.split("-")[1].trim() + "'",
            cs:"'" + vm.titleName.split("-")[2].trim() + "'",
            wg:"'" + vm.titleName.split("-")[3].trim() + "'",
          }).then((res) => {
            if (res[0]) {
              vm.eventNumber = res[0].wgsjs;
              vm.xtNumber = res[0].xts;
            } else {
              vm.eventNumber = 0
              vm.xtNumber = 0
            }
          });
          console.log(sessionStorage.getItem("wgsjType"));
        }
      });
      let vm = new Vue({
        el: "#app",
        data() {
          return {
            titleName: "",
            nameList: [],
            base_url: "https://csdn.dsjj.jinhua.gov.cn:8101/adm-api/",
            eventNumber: 0, //网格事件数
            xtNumber: 0, //协同事件数
          };
        },
        mounted() {},
        computed: {
          xzzfzx() {
            return window.parent.xzzfzx
          },
          table() {
            let newNameList = [];
            for (let i = 0; i < Math.ceil(this.nameList.length / 2); i++) {
              newNameList[i] = [];
              for (let j = 0; j < 2; j++) {
                // 如果是最后一个板块
                if (i === Math.ceil(this.nameList.length / 2) - 1) {
                  if (Math.ceil(this.nameList.length % 2) !== 0) {
                    // 只有最后一个板块的数据在余数以内的才赋值
                    if (j < Math.ceil(this.nameList.length % 2)) {
                      newNameList[i][j] = this.nameList[i * 2 + j];
                    }
                  } else {
                    // 如果刚好整整一个板块，则全部附上值
                    newNameList[i][j] = this.nameList[i * 2 + j];
                  }
                } else {
                  newNameList[i][j] = this.nameList[i * 2 + j];
                }
              }
            }
            return newNameList;
          },
        },
        methods: {
          callPhone(phones) {
            // window.parent.postMessage(
            //   JSON.stringify(
            //     {
            //       type: "openIframe",
            //       name: "zbPhone",
            //       src: "/static/citybrain/djtl/commont/zbPhone.html",
            //       left: "calc(50% - 300px)",
            //       top: "469px",
            //       width: "1500px",
            //       height: "920px",
            //       zIndex: "1005",
            //       argument: { phone: phones },
            //     },
            //     "*"
            //   )
            // );


            if (phones != "") {
              // window.parent.lay.openIframe({
              //   type: "openIframe",
              //   name: "zbPhone",
              //   id: "zbPhone",
              //   src: "/static/citybrain/zhdd/zhdd_page/zhtx_callPhone/zbPhone.html",
              //   left: "1200px",
              //   top: "469px",
              //   width: "1500px",
              //   height: "920px",
              //   zIndex: "666",
              //   argument: { phone: phones },
              // });

              window.parent.lay.openIframe({
                type: "openIframe",
                name: "CallPhone",
                id: "CallPhone",
                src: baseURL.url + "/static/citybrain/commonts/CallPhone/CallPhone.html",
                left: "1200px",
                top: "575px",
                width: "1515px",
                height: "866px",
                zIndex: "666",
                argument: { phone: phones },
              });
            }
          },
          getMain(obj) {
            this.titleName = `${obj.szs}-${obj.szqx}-${obj.szz}-${obj.szsq}-${obj.name}`;
            $api("/gis_wgll_13", { oid: obj.oid }).then((res) => {
              console.log(res);
              this.nameList = res;
            });
          },
          // 关闭弹窗
          closeMiddleIframe(name) {
            // let data = JSON.stringify({
            //   type: 'closeIframe',
            //   name: name,
            // })
            window.parent.lay.closeIframeByNames([name]);
          },
          doSpth(sjhm) {
            let phone = [];
            phone.push(sjhm);
            let that = this;
            $post(this.base_url + "pub/dingtalk/accountId", {
              source: "jinhuacsdn",
              phones: phone,
            }).then((res) => {
              const accountIds = res.map((a) => a.accountId);
              if (!accountIds.length) {
                that.$message({
                  message: "视频会议人员的accountId均为空，发起视频会议失败",
                  type: "warning",
                });
              } else {
                window.top.postMessage(
                  {
                    accountIds,
                    type: "doVideoMeeting",
                  },
                  "*"
                );
              }
            });
          },
        },
      });
    </script>
  </body>
</html>
