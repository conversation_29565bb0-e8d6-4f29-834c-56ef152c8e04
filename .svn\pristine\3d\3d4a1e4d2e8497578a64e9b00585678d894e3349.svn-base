<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>效果</title>

    <link
      rel="stylesheet"
      href="https://dev.arcgisonline.cn/jsapi/4.24/esri/themes/light/main.css"
    />
    <!-- <script src="./libs/three-r79.min.js"></script> -->
    <script src="./index.js" type="module"></script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        left: 50%;
        width: 50%;
        height: 200px;
        display: flex;
      }

      .tools span {
        cursor: pointer;
        background-color: blue;
        width: 150px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        color: white;
      }

      #weather-box {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv">
      <div class="tools">
        <span class="dom-init">视域分析</span>
        <!-- <span
          id="'set-cam"
          onclick="ArcGisUtils.setXYPos(window.view,{x: 119.7048288269463,y: 27.99616488279615})"
          >定位</span
        > -->
      </div>
      <div
        class="input-pos"
        style="
          width: 230px;
          position: absolute;
          top: 0;
          left: 80px;
          z-index: 99;
          background: white;
        "
      >
        <h3>视域分析</h3>
        <div class="">
          <label for="cheese">分析半径(米): </label>
          <input type="text" class="c-bj" value="500" />
        </div>
        <div class="">
          <label for="cheese">分级密度: </label>
          <input type="text" class="c-md" value="1" />
        </div>

        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <button class="dom-start">开始</button>
          <button class="dom-analysis">分析</button>
          <button class="dom-clear">清除</button>
        </div>

        <div class="dom-analysis-result"></div>
      </div>
    </div>
  </body>
  <script>
    function handleDomAnalysisInitClick() {
      if (!window.domAnalysisRef) {
        window.domAnalysisRef = new ArcGisUtils.DomAnalysis(window.view);
        window.domAnalysisRef.watch((_data) => {
          console.log(_data);
          try {
            let ele = document.querySelector(".dom-analysis-result");
            ele.innerHTML = `分析结果
           分析总数: ${_data.count} 
           可见总数: ${_data.visible} 
           不可见总数: ${_data.invisible} 
           开敞度: ${((_data.visible * 100) / _data.count || 0).toFixed(3)}% 
           `;
          } catch (error) {}
        });

        // 参数更新
        document.querySelector(".c-bj").addEventListener("input", () => {
          try {
            window.domAnalysisRef.options.radiusLen = this.value;
            console.log(this.value);
          } catch (error) {}
        });
        document.querySelector(".c-md").addEventListener("input", () => {
          try {
            window.domAnalysisRef.setDensificationFactor(+this.value);
            console.log(this.value);
          } catch (error) {}
        });
      }
      // let fov = document.querySelector(".c-bj").value;
      // let tilt = document.querySelector(".c-md").value;
      // let heading = document.querySelector(".c-heading").value;

      // ArcGisUtils.setCam(window.view, {
      //   fov,
      //   tilt,
      //   heading,
      // });
      // ArcGisUtils.updateCam(window.view, {
      //   heading: ".c-heading",
      //   tilt: ".c-tilt",
      //   fov: ".c-fov",
      // });
    }

    function handleStartClick() {
      if (!window.domAnalysisRef) {
        return;
      }
      console.log("autoDrawSphere");
      window.domAnalysisRef.autoDrawSphere();
    }

    function handleAnalysisClick() {
      if (!window.domAnalysisRef) {
        return;
      }
      console.log("setDomAnalysisResult");
      window.domAnalysisRef.setDomAnalysisResult();
    }
    function handleClearClick() {
      if (!window.domAnalysisRef) {
        return;
      }
      console.log("removeAll");
      window.domAnalysisRef.removeAll();
    }

    // 初始化
    document
      .querySelector(".dom-init")
      .addEventListener("click", handleDomAnalysisInitClick);
    // 绘制范围
    document
      .querySelector(".dom-start")
      .addEventListener("click", handleStartClick);
    document
      .querySelector(".dom-analysis")
      .addEventListener("click", handleAnalysisClick);
    document
      .querySelector(".dom-clear")
      .addEventListener("click", handleClearClick);
  </script>
</html>
