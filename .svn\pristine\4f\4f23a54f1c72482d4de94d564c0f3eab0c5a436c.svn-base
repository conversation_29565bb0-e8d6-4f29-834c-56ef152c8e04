<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>执法态势-右</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/viewCss/index.css" />
    <link rel="stylesheet" href="/static/css/viewCss/zfts_right.css" />
    <script src="/Vue/vue.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/elementui/js/index.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/echarts/5.4.1/echarts.min.js"></script>
    <script src="/echarts/echarts-gl.min.js"></script>
    <script src="/static/js/jslib/moment.js"></script>
  </head>

  <body>
    <div id="right" v-cloak>
      <div class="hearder_h1"><span>执法办案</span></div>
      <div class="zfba">
        <li v-for="(item,index) in zfbaData" :key="index" @click="showAnjianDialog(item)">
          <img :src="item.icon" alt="" />
          <div class="text-con">
            <div class="text-bg"><span class="text">{{item.name}}</span></div>
            <div style="line-height: 50px">
              {{item.value}}<span class="unit">{{item.unit}}</span>
            </div>
          </div>
        </li>
      </div>
      <!-- 案件回访 -->
      <div class="ajhf">
        <div class="left-con" @click="changePage" style="cursor: pointer">
          <img src="/static/images/zfts/ajhf.png" alt="" />
          <div class="aj-t">案件回访</div>
        </div>
        <div class="right-con" style="position: relative">
          <img src="/static/images/zfts/myd-bg.png" alt="" />
          <div class="aj-t myd">
            <div>满意度</div>
            <div style="color: #eed252" class="xt-font">{{my}}</div>
          </div>
          <div class="aj-t hfsl">
            <div>回访数量</div>
            <div style="color: cyan" class="xt-font">{{hfsl}}</div>
          </div>
        </div>
      </div>
<!--      <div id="ajhf-chart"></div>-->
      <div class="hearder_h1"><span>执法协同</span></div>
      <div class="zfxt-con">
        <div style="position: absolute; top: -75px; left: 680px; z-index: 2" v-show="year != '2023'">
          <el-date-picker
            v-model="value1"
            type="monthrange"
            @change="timePickerFun"
            range-separator="-"
            start-placeholder="开始月份"
            value-format="yyyy-MM"
            end-placeholder="结束月份"
          >
          </el-date-picker>
        </div>
        <div
          v-show="!showZFXT"
          class="s-font-30 s-c-white s-text-center"
          style="line-height: 300px"
        >
          暂无数据
        </div>
<!--        <div v-show="showZFXT" class="zfxt-bg"></div>-->
<!--        <div v-show="showZFXT" id="zfxt"></div>-->
        <div class="zfxtContainer" v-show="showZFXT">
            <div class="zfxtIndexs">
              <div class="zfxt-item" v-for="(item,i) in zfxtData">
                <div class="zfxtNumberText1" :class="{zfxtNumberText2: i%2 == 0}">{{item.count}}</div>
                <div class="zfxt-text">{{item.ywwd1}}</div>
                <div class="yellowLine" :class="{blueLine: i%2 == 0}"></div>
              </div>
            </div>
            <div class="base"></div>
        </div>
      </div>
      <div class="hearder_h1"><span>基层治理协同</span></div>
      <div class="jczlXt-container">
        <div style="position: absolute; top: -75px; left: 680px; z-index: 2" v-show="year != '2023'">
          <el-date-picker
            v-model="value2"
            type="monthrange"
            @change="getZlxtData"
            range-separator="-"
            start-placeholder="开始月份"
            value-format="yyyy-MM"
            end-placeholder="结束月份"
          >
          </el-date-picker>
        </div>
        <div class="zlxt-item-center"></div>
        <div class="zlxt-items">
          <div class="zlxt-item" v-for="(item,i) in zlxtData" :key="i" :class="{zlxtItemLeft:i % 2 == 0,zlxtItemRight:i % 2 != 0}" @click="showZlxtDialog(item)">
            <div class="zlxt-item-name" :class="{ml:i % 2 != 0}">{{item.name}}:</div>
            <div class="zlxt-item-value" :class="{ml2:i % 2 == 0}">{{item.value}}</div>
          </div>
        </div>

      </div>
<!--      <div class="hearder_h1"><span>"1+8"信访投诉举报</span></div>-->
<!--      <div class="tab-con s-flex">-->
<!--        <span-->
<!--          v-for="(item,i) in btnList"-->
<!--          :class="btnActive==i?'tab-con-active':''"-->
<!--          @click="changeBtn(i)"-->
<!--          >{{item}}-->
<!--        </span>-->
<!--      </div>-->
<!--      <div id="tsjb"></div>-->
      <div class="hearder_h1"><span>案件回访</span></div>
      <div
        class="ajhfChart"
        id="chartAjhf"
        style="width: 1030px; height: 470px"
      ></div>
    </div>
    <script>
      window.parent.eventbus &&
      window.parent.eventbus.on("yearChange", (year) => {
        vm.year = year
        vm.initApi(localStorage.getItem("city"),year);
      });
      window.parent.eventbus && window.parent.eventbus.on("cityChange", (city) => {
        let filtName =
          city == "金义新区"
            ? "金东区"
            : city == "金华开发区"
              ? "开发区"
              : city;
        vm.initApi(filtName,localStorage.getItem("year"));
      });
      let vm = new Vue({
        el: "#right",
        data: {
          year:"",
          showZFXT: false,
          city: "",
          btnActive: 0,
          btnList: ["按时间", "按领域"],
          value1: [
            new Date().getFullYear() + "-01",
            new Date().getFullYear() +
              "-" +
            (new Date().getMonth() + 1).toString().padStart(2, "0"),
          ],
          value2: [
            new Date().getFullYear() + "-01",
            new Date().getFullYear() +
            "-" +
            (new Date().getMonth() + 1).toString().padStart(2, "0"),
          ],
          //执法办案
          zfbaData: [],
          //案件回访
          ajhfData: [
            // {
            //   label: "1月",
            //   num: 118,
            //   num1: 116,
            // },
            // {
            //   label: "2月",
            //   num: 89,
            //   num1: 88,
            // },
            {
              label: "3月",
              num: 261,
              num1: 260,
            },
            {
              label: "4月",
              num: 265,
              num1: 262,
            },
            {
              label: "5月",
              num: 278,
              num1: 277,
            },
            {
              label: "6月",
              num: 280,
              num1: 278,
            },
            {
              label: "7月",
              num: 263,
              num1: 262,
            },
            // {
            //   label: "8月",
            //   num: 0,
            //   num1: 0,
            // },
            // {
            //   label: "9月",
            //   num: 0,
            //   num1: 0,
            // },
            // {
            //   label: "10月",
            //   num: 0,
            //   num1: 0,
            // },
            // {
            //   label: "11月",
            //   num: 0,
            //   num1: 0,
            // },
            // {
            //   label: "12月",
            //   num: 0,
            //   num1: 0,
            // },
          ],
          //执法协同
          zfxtData: [
            {
              name: "抄告抄送",
              value: 189,
              zb: 50,
              itemStyle: {},
            },
            {
              name: "联合会商",
              value: 184,
              zb: 9.97,
              itemStyle: {},
            },
            {
              name: "基层治理协同",
              value: 144,
              zb: 40.03,
              itemStyle: {},
            },
            {
              name: "联合执法",
              value: 532,
              zb: 40.03,
              itemStyle: {},
            },
            {
              name: "紧急任务",
              value: 1,
              zb: 40.03,
              itemStyle: {},
            },
          ],
          //部门投诉举报-按时间
          bmjb_time: [],
          //部门投诉举报-按领域
          bmjb_area: [],
          //治理协同数据
          zlxtData:[
            {
              name:"网格上报数",
              value:"0"
            },
            {
              name:"超期办结",
              value:"0"
            },
            {
              name:"协同数",
              value:"0"
            },
            {
              name:"已协同事项数",
              value:"0"
            },
            {
              name:"办结数",
              value:"0"
            },
            {
              name:"参与处置部门数",
              value:"0"
            }
          ],
          hfsl:"",
          my:""
        },
        computed:{

        },
        mounted() {
          this.timePickerFun(this.value1);
          this.ajhfData.map((a) => {
            a.myd = ((a.num1 / a.num) * 100).toFixed(2);
          });
          // this.lineCharts1("ajhf-chart", this.ajhfData);
          // this.getChart03("tsjb");
          this.initApi(localStorage.getItem("city"),localStorage.getItem("year"));
        },
        methods: {
          getAjhfChartData() {
            if (localStorage.getItem("city") == "金华市") {
              $api2Get('/ajhf/zftsZfrw/caseTotalList',{region:localStorage.getItem("city")}).then(res => {
                this.getChartAjhf(localStorage.getItem("city"),'chartAjhf',res.data.data?res.data.data:[])
              })
            } else {
              $api2Get('/ajhf/zftsZfrw/hfStatic',{region:localStorage.getItem("city")}).then(res => {
                this.getChartAjhf(localStorage.getItem("city"),'chartAjhf',res.data.data?res.data.data:[])
              })
            }
          },
          getChartAjhf(city, id, chartData) {
            let myEc = echarts.init(document.getElementById(id));
            let xdata = [];
            let ydata = [[], [], [], []];
            if (city == "金华市") {
              chartData.forEach((item) => {
                xdata.push(item.region);
                ydata[0].push(item.hfTotal);
                ydata[1].push(item.hfz);
                ydata[2].push(item.wfh);
                ydata[3].push(item.wxhfs);
              });
            } else {
              chartData.forEach((item) => {
                xdata.push(item.date);
                ydata[0].push(item.yhfs);
                ydata[1].push(item.hfzs);
                ydata[2].push(item.whfs);
                ydata[3].push(item.wxhfs);
              });
            }
            let legend = ["已回访", "回访中", "未回访", "无需回访"];
            let color = ["76,152,251", "172,171,52", "245,102,121", "72,224,64"];
            let option = {
              tooltip: {
                trigger: "axis",
                backgroundColor: "rgba(51, 51, 51, 0.7)",
                borderWidth: 0,
                axisPointer: {
                  type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                },
                textStyle: {
                  color: "white",
                  fontSize: "24",
                },
              },
              grid: {
                left: "5%",
                right: "5%",
                top: "15%",
                bottom: "15%",
                containLabel: true,
              },
              legend: {
                top: 10,
                left: "center",
                itemWidth: 16,
                itemHeight: 16,
                itemGap: 50,
                textStyle: {
                  fontSize: 24,
                  color: "#fff",
                  padding: [3, 0, 0, 0],
                },
                // data: legend,
              },
              xAxis: [
                {
                  type: "category",
                  data: xdata,
                  axisLine: {
                    lineStyle: {
                      color: "rgb(119,179,241,.4)", // 颜色
                      width: 1, // 粗细
                    },
                  },
                  axisTick: {
                    show: false,
                  },
                  axisLabel: {
                    interval: 0,
                    // rotate: 30,
                    textStyle: {
                      color: "#D6E7F9",
                      fontSize: 20,
                    },
                  },
                },
              ],
              yAxis: [
                {
                  name: "",
                  type: "value",
                  nameTextStyle: {
                    fontSize: 24,
                    color: "#D6E7F9",
                    padding: 5,
                  },
                  splitLine: {
                    lineStyle: {
                      color: "rgb(119,179,241,.4)",
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 24,
                      color: "#D6E7F9",
                    },
                  },
                },
              ],
              series: [],
            };
            for (var i = 0; i < legend.length; i++) {
              option.series.push({
                name: legend[i],
                type: "bar",
                stack: "总量",
                barWidth: "30",
                label: {
                  show: false,
                  position: "insideRight",
                },
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: "rgba(" + color[i] + ",0.99)",
                      },
                      {
                        offset: 1,
                        color: "rgba(" + color[i] + ",0)",
                      },
                    ]),
                    barBorderRadius: 4,
                  },
                },
                data: ydata[i],
              });
            }
            myEc.setOption(option);
            myEc.getZr().on("mousemove", (param) => {
              myEc.getZr().setCursorStyle("default");
            });
          },
          //获取回访概况信息
          getHfgk() {
            // console.log(range, city);
            $api2Get("/ajhf/zftsZfrw/dpHfgk", {
              xsq: localStorage.getItem("city"),
              startTime: this.value1[0],
              endTime: this.value1[1],
            }).then(res => {
              if (res.data.code == 200) {
                this.my = res.data.data.myd + "%"
                this.hfsl = res.data.data.fhzs
              }
            })
          },
          Allhfsl() {
            // let result = 0
            // this.ajhfData.forEach((item,i) => {
            //   result += item.num
            // })

            // let result = localStorage.getItem("year") == "2024"?263:1894
            // return result

            this.hfsl = localStorage.getItem("year") == "2024"?263:1939
          },
          myd() {
            // return localStorage.getItem("year") == "2024"?"99.24%":"98.71%"
            this.my = localStorage.getItem("year") == "2024"?"99.24%":"98.71%"
          },
          /**切换页面 */
          changePage() {
            let pageTit = "案件回访";
            let page = "ajhf"
            window.parent.xzzfzx.page_menu = page;
            window.parent.xzzfzx.header_title = pageTit;
            window.parent.lay.closeIframeByKeepNames([]);
            window.parent.lay.openIframe({
              type: "openIframe",
              name: `${page}_left`,
              id: `${page}_left`,
              src: baseURL.url + `/static/citybrain/${page}/${page}_left.html`,
              width: "1030px",
              height: "1900px",
              left: "20px",
              top: "calc(50% - 870px)",
              zIndex: "666",
            });
            window.parent.lay.openIframe({
              type: "openIframe",
              name: `${page}_right`,
              id: `${page}_right`,
              src: baseURL.url + `/static/citybrain/${page}/${page}_right.html`,
              width: "1030px",
              height: "1900px",
              right: "20px",
              top: "calc(50% - 870px)",
              zIndex: "666",
            });
            window.parent.lay.openIframe({
              type: "openIframe",
              name: `${page}__bottom`,
              id: `${page}__bottom`,
              src: baseURL.url + `/static/citybrain/${page}/${page}_bottom.html`,
              width: "1760px",
              height: "525px",
              left: "calc(50% - 860px)",
              top: '73.3%',
              zIndex: "666",
            });
          },
          showAnjianDialog(item) {
              window.parent.lay.openIframe({
                type: "openIframe",
                name: "AnjianDialog",
                id: "AnjianDialog",
                src:
                  baseURL.url + "/static/citybrain/commonts/zfts/AnjianDialog.html",
                left: "1330px",
                top: "575px",
                width: "1015px",
                height: "965px",
                zIndex: "666",
                argument: {
                  type: item.name,
                },
              });
          },
          getZlxtData() {
            this.zlxtData[0].value = 0
            this.zlxtData[1].value = 0
            this.zlxtData[2].value = 0
            this.zlxtData[3].value = 0
            this.zlxtData[4].value = 0
            this.zlxtData[5].value = 0
            $api("csdn_yjyp_jczlxt",{area_name:localStorage.getItem("city"),sj1:this.value2[0],sj2:this.value2[1]}).then(res => {
              res.forEach((item,i) => {
                switch (item.zbmc) {
                  case "网格上报数":
                    this.zlxtData[0].value = item.tjz
                    break;
                  case "超期办结数":
                    this.zlxtData[1].value = item.tjz
                    break;
                  case "协同数":
                    this.zlxtData[2].value = item.tjz
                    break;
                  case "已协同":
                    this.zlxtData[3].value = item.tjz
                    break;
                  case "办结数":
                    this.zlxtData[4].value = item.tjz
                    break;
                  case "处置部门数":
                    this.zlxtData[5].value = item.tjz
                    break;
                }
              })
            })
            // //参与处置部门数
            // $api("csdn_yjyp_cybm1",{area_name:localStorage.getItem("city"),sj1:this.value2[0],sj2:this.value2[1]}).then(res => {
            //   this.zlxtData[5].value += res.length
            // })
            // //已协同事项数
            // $api("csdn_yjyp_gtsx1",{area_name:localStorage.getItem("city"),sj1:this.value2[0],sj2:this.value2[1]}).then(res => {
            //   this.zlxtData[3].value += res.length
            // })
            // //网格上报数
            // $api("csdn_yjyp_wgsbs1",{area_name:localStorage.getItem("city"),sj1:this.value2[0],sj2:this.value2[1]}).then(res => {
            //   this.zlxtData[0].value += res.length
            // })
            // //协同数
            // $api("csdn_yjyp_xts1",{area_name:localStorage.getItem("city"),sj1:this.value2[0],sj2:this.value2[1]}).then(res => {
            //   this.zlxtData[2].value += res.length
            // })
            // //办结数
            // $api("csdn_yjyp_bjs1",{area_name:localStorage.getItem("city"),sj1:this.value2[0],sj2:this.value2[1]}).then(res => {
            //   this.zlxtData[4].value += res.length
            // })
            // //超期办结
            // $api("csdn_yjyp_cqbjs1",{area_name:localStorage.getItem("city"),sj1:this.value2[0],sj2:this.value2[1]}).then(res => {
            //   this.zlxtData[1].value += res.length
            // })

          },
          showZlxtDialog(item) {
            const that = this
            if (item.name == "参与处置部门数" || item.name == "已协同事项数") {
              window.parent.lay.openIframe({
                type: "openIframe",
                name: "zlxtDialog",
                id: "zlxtDialog",
                src:
                  baseURL.url + "/static/citybrain/commonts/zfts/zlxtDialog.html",
                left: "1330px",
                top: "575px",
                width: "1387px",
                height: "726px",
                zIndex: "666",
                argument: {
                  type: item.name,
                  time: that.value2
                },
              });
            } else if (item.name == "网格上报数" || item.name == "协同数" || item.name == "办结数") {
              window.parent.lay.openIframe({
                type: "openIframe",
                name: "zlxtDialog2",
                id: "zlxtDialog2",
                src:
                  baseURL.url + "/static/citybrain/commonts/zfts/zlxtDialog2.html",
                left: "1330px",
                top: "575px",
                width: "1387px",
                height: "820px",
                zIndex: "666",
                argument: {
                  type: item.name,
                  time: that.value2
                },
              });
            } else {
              window.parent.lay.openIframe({
                type: "openIframe",
                name: "zlxtDialog3",
                id: "zlxtDialog3",
                src:
                  baseURL.url + "/static/citybrain/commonts/zfts/zlxtDialog3.html",
                left: "1330px",
                top: "575px",
                width: "1387px",
                height: "860px",
                zIndex: "666",
                argument: {
                  type: item.name,
                  time: that.value2
                },
              });
            }
          },
          initApi(city,year) {
            if (year) {
              if (year == "2023") {
                this.value1 = ["2023-01","2023-12"]
                this.value2 = ["2023-01","2023-12"]
              } else {
                this.value1 = ["2024-01",moment(new Date()).format("YYYY-MM")]
                this.value2 = ["2024-01",moment(new Date()).format("YYYY-MM")]
              }
            }
            this.getAjhfChartData()
            this.Allhfsl()
            this.myd()
            this.getZlxtData()
            this.timePickerFun()
            this.getHfgk()
            // 执法办案
            $api("/csdn_yjyp2", { area_code: city,sjwd2: year }).then((res) => {
              this.zfbaData = [];
              res.map((a, i) => {
                this.zfbaData.push({
                  icon: `/static/images/zfts/${
                    i == 0
                      ? "ajzs"
                      : i == 1
                      ? "jy"
                      : i == 2
                      ? "zdaj"
                      : i == 3
                      ? "cfje"
                      : i == 4
                      ? "sxfg"
                      : "jy"
                  }.png`,
                  name: a.label.includes("-") ? a.label.split("-")[1] : a.label,
                  value: a.num,
                  unit: a.unit,
                });
              });
            });
            // 执法协同
            //“1+8”部门投诉举报数（按时间）//投诉举报事项 转执法案件数 占比
            let a1 = $api("/csdn_yjyp18", { area_code: city,sjwd2: year }).then((res) => {
              console.log(res,"time");
              let arr = [];
              res.forEach((a) => {
                if (a.unit == "件") {
                  arr.push({
                    name: a.sjwd1.slice(0, 4) + "-" + a.sjwd1.slice(4),
                    value2: a.num,
                    zb: res.filter((e) => e.sjwd1 == a.sjwd1 && e.unit == "%")[0].num,
                  });
                }
              });
              return arr;
            });
            let a2 = $api("/csdn_yjyp5", { area_code: city,sjwd2: year }).then((res) => {
              let arr = [];
              res.map((a) => {
                arr.push({
                  name: a.sjwd1.slice(0, 4) + "-" + a.sjwd1.slice(4),
                  value1: a.num,
                });
              });
              return arr;
            });
            axios.all([a1, a2]).then((res) => {
              res[0].map((item2) => {
                res[1].map((item1) => {
                  if (item2.name == item1.name) {
                    let json = {
                      value1: item1.value1,
                    };
                    Object.assign(item2, json);
                    return item2;
                  }
                });
              });
              console.log(res,"time");
              this.bmjb_time = res[0];
              this.changeBtn(this.btnActive);
            });
            //“1+8”部门投诉举报数（按领域）
            // let s1 = $api("/csdn_yjyp19", { area_code: city }).then((res) => {
            //   let arr = [];
            //   res.map((a) => {
            //     if (a.unit == "件") {
            //       arr.push({
            //         name: a.ywwd1,
            //         value2: a.num,
            //       });
            //     } else {
            //       let index = arr.findIndex((s) => s.name == a.ywwd1);
            //       arr[index].zb = a.num;
            //     }
            //   });
            //   return arr;
            // });
            // let s2 = $api("/csdn_yjyp6", { area_code: city }).then((res) => {
            //   let arr = res.map((a) => {
            //     return {
            //       name: a.ywwd1,
            //       value1: a.num,
            //     };
            //   });
            //   return arr;
            // });
            // axios.all([s1, s2]).then((res) => {
            //   res[0].map((item2) => {
            //     res[1].map((item1) => {
            //       if (item2.name == item1.name) {
            //         let json = {
            //           value1: item1.value1,
            //         };
            //         Object.assign(item2, json);
            //         return item2;
            //       }
            //     });
            //   });
            //   this.bmjb_area = res[0];
            // });

            $api("/csdn_yjyp_xftsjb", { area_code: city,sjwd2: year }).then((res) => {
              this.bmjb_area = res.map(item => {return {
                name:item.ywwd1,
                value1:item.num2,
                value2:item.num1,
                zb:item.bfb
              }});
            });
            this.getChart03("tsjb", this.btnActive == 0?this.bmjb_time:this.bmjb_area);
          },
          timePickerFun(e) {
            $api("/yjyp_zfts_zfxt", {
              qxwd: localStorage.getItem("city") == "金华市" ? "" : localStorage.getItem("city"),
              sjwd1: this.value1[0],
              sjwd2: this.value1[1],
            }).then((res) => {
              if (res.length > 0) {
                this.showZFXT = true;
                this.zfxtData = res
                this.getChart02("zfxt", res);
              } else {
                this.showZFXT = false;
              }
            });
          },
          changeBtn(index) {
            this.btnActive = index;
            if (index == 0) {
              this.getChart03("tsjb", this.bmjb_time);
            } else {
              this.getChart03("tsjb", this.bmjb_area);
            }
          },
          // 案件回访
          lineCharts1(id, echartData) {
            let myChart = echarts.init(document.getElementById(id));
            let xdata = [];
            let addData = [];
            let tongData = [];
            let tagNum = "";
            let unit = "";
            echartData.map((ele) => {
              xdata.push(ele.label);
              addData.push(ele.num);
              tongData.push(ele.myd);
            });

            // 指定图表的配置项和数据
            let option = {
              color: ["#e86056", "#F5CC53", "#6AE4B2"],
              tooltip: {
                trigger: "axis",
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                axisPointer: {
                  type: "shadow",
                },
                textStyle: {
                  color: "white",
                  fontSize: "28",
                },
                formatter: function (params, ticket, callback) {
                  let res =
                    params[0].axisValueLabel +
                    "<br/>" +
                    "回访数量:" +
                    params[0].value +
                    "项" +
                    "<br/>" +
                    "满意度:" +
                    params[1].value +
                    "%";
                  return res;
                },
              },
              legend: {
                selectedMode: false,
                top: "0px",
                left: "center",
                textStyle: {
                  color: "#fff",
                  fontSize: 30,
                  fontFamily: "SourceHanSansCN-Medium",
                },
                itemWidth: 25,
                itemHeight: 16,
              },
              grid: {
                top: "15%",
                left: "3%",
                right: "5%",
                bottom: "0%",
                containLabel: true,
              },
              xAxis: [
                {
                  type: "category",
                  data: xdata,
                  splitLine: { show: false },
                  axisTick: {
                    show: false,
                  },
                  axisLine: {
                    lineStyle: {
                      color: "#0f2944", // 颜色
                      width: 1, // 粗细
                    },
                  },
                  axisLabel: {
                    interval: 0,
                    // rotate: 25,
                    textStyle: {
                      color: "#fff",
                      fontSize: 30,
                      fontFamily: "SourceHanSansCN-Medium",
                    },
                  },
                },
              ],
              yAxis: [
                {
                  type: "value",
                  name: "回访数量:件",
                  nameTextStyle: {
                    fontSize: 26,
                    color: "#fff",
                    padding: [0, 0, 10, 0],
                  },
                  splitLine: {
                    lineStyle: {
                      color: "rgba(0, 192, 255, 0.2)",
                    },
                  },
                  axisLabel: {
                    formatter: "{value}", //右侧Y轴文字显示
                    textStyle: {
                      color: "#fff",
                      fontSize: 30,
                      fontFamily: "SourceHanSansCN-Medium",
                    },
                  },
                },
                {
                  name: "满意度：%",
                  type: "value",
                  nameTextStyle: {
                    fontSize: 26,
                    color: "#fff",
                    padding: [0, 0, 10, 0],
                  },
                  splitLine: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.1,
                      width: 2,
                    },
                  },
                  axisTick: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.5,
                      width: 2,
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 30,
                      color: "#fff",
                    },
                  },
                },
              ],
              series: [
                {
                  cursor: "auto",
                  name: "回访数量",
                  type: "bar",
                  yAxisIndex: 0,
                  markLine: {
                    symbol: "none",
                    label: {
                      normal: {
                        color: "#fff",
                        formatter: "目标值\n" + tagNum + "亿",
                        fontSize: 25,
                        padding: 10,
                        backgroundColor: "rgba(145,193,80,0.8)",
                        show: true,
                        position: "end",
                        distance: 2,
                      },
                    },
                    lineStyle: {
                      type: "solid",
                      color: "#ccc",
                    },
                    data: [
                      {
                        yAxis: tagNum,
                      },
                    ],
                  },
                  data: addData,
                  barWidth: 20,
                  itemStyle: {
                    normal: {
                      color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                        {
                          offset: 0,
                          color: "#004f69",
                        },
                        {
                          offset: 1,
                          color: "#00c0ff",
                        },
                      ]),
                    },
                  },
                  label: {
                    show: false, //开启显示
                    position: "top", //在上方显示
                    textStyle: {
                      //数值样式
                      color: "#FFFFFF",
                      fontFamily: "SourceHanSansCN-Regular",
                      fontSize: 30,
                    },
                  },
                },
                {
                  name: "满意度",
                  type: "line",
                  yAxisIndex: 1,
                  // smooth: true, //平滑曲线显示
                  showAllSymbol: true, //显示所有图形。
                  symbol: "circle", //标记的图形为实心圆
                  symbolSize: 10, //标记的大小
                  itemStyle: {
                    //折线拐点标志的样式
                    color: "#ffc460",
                  },
                  lineStyle: {
                    color: "#ffc460",
                  },
                  areaStyle: {
                    color: "rgba(5,140,255, 0.2)",
                  },
                  data: tongData,
                },
              ],
            };
            myChart.setOption(option, true);
            myChart.getZr().on("mousemove", (param) => {
              myChart.getZr().setCursorStyle("default");
            });
          },
          //执法协同
          getChart02(id, echartsData) {
            echartsData = echartsData.map(item => {return {
              name: item.ywwd1,
              count: item.count
            }})
            let myChart = echarts.init(document.getElementById(id));
            let selectedIndex = "";
            let hoveredIndex = "";
            option = getPie3D(echartsData, 0.59);
            // 生成扇形的曲面参数方程
            function getParametricEquation(
              startRatio,
              endRatio,
              isSelected,
              isHovered,
              k,
              h
            ) {
              // 计算
              const midRatio = (startRatio + endRatio) / 2;

              const startRadian = startRatio * Math.PI * 2;
              const endRadian = endRatio * Math.PI * 2;
              const midRadian = midRatio * Math.PI * 2;

              // 如果只有一个扇形，则不实现选中效果。
              if (startRatio === 0 && endRatio === 1) {
                // eslint-disable-next-line no-param-reassign
                isSelected = false;
              }

              // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
              // eslint-disable-next-line no-param-reassign
              k = typeof k !== "undefined" ? k : 1 / 3;

              // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
              const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
              const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

              // 计算高亮效果的放大比例（未高亮，则比例为 1）
              const hoverRate = isHovered ? 1.05 : 1;

              // 返回曲面参数方程
              return {
                u: {
                  min: -Math.PI,
                  max: Math.PI * 3,
                  step: Math.PI / 32,
                },

                v: {
                  min: 0,
                  max: Math.PI * 2,
                  step: Math.PI / 20,
                },

                x(u, v) {
                  if (u < startRadian) {
                    return (
                      offsetX +
                      Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
                    );
                  }
                  if (u > endRadian) {
                    return (
                      offsetX +
                      Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
                    );
                  }
                  return (
                    offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
                  );
                },

                y(u, v) {
                  if (u < startRadian) {
                    return (
                      offsetY +
                      Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
                    );
                  }
                  if (u > endRadian) {
                    return (
                      offsetY +
                      Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
                    );
                  }
                  return (
                    offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
                  );
                },

                z(u, v) {
                  if (u < -Math.PI * 0.5) {
                    return Math.sin(u);
                  }
                  if (u > Math.PI * 2.5) {
                    return Math.sin(u) * h * 0.1;
                  }
                  // 当前图形的高度是Z根据h（每个value的值决定的）
                  return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
                },
              };
            }
            // 生成模拟 3D 饼图的配置项
            function getPie3D(pieData, internalDiameterRatio) {
              const series = [];
              // 总和
              let sumValue = 0;
              let startValue = 0;
              let endValue = 0;
              const legendData = [];
              let legend = [];
              const k =
                typeof internalDiameterRatio !== "undefined"
                  ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
                  : 1 / 3;

              // 为每一个饼图数据，生成一个 series-surface 配置
              for (let i = 0; i < pieData.length; i += 1) {
                sumValue += pieData[i].count;

                const seriesItem = {
                  name:
                    typeof pieData[i].name === "undefined"
                      ? `series${i}`
                      : pieData[i].name,
                  type: "surface",
                  parametric: true,
                  wireframe: {
                    show: false,
                  },
                  pieData: pieData[i],
                  pieStatus: {
                    selected: false,
                    hovered: false,
                    k,
                  },
                };

                if (typeof pieData[i].itemStyle !== "undefined") {
                  const { itemStyle } = pieData[i];

                  // eslint-disable-next-line no-unused-expressions
                  typeof pieData[i].itemStyle.color !== "undefined"
                    ? (itemStyle.color = pieData[i].itemStyle.color)
                    : null;
                  // eslint-disable-next-line no-unused-expressions
                  typeof pieData[i].itemStyle.opacity !== "undefined"
                    ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
                    : null;

                  seriesItem.itemStyle = itemStyle;
                }
                series.push(seriesItem);
              }
              // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
              // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
              for (let i = 0; i < series.length; i += 1) {
                endValue = startValue + series[i].pieData.count;

                series[i].pieData.startRatio = startValue / sumValue;
                series[i].pieData.endRatio = endValue / sumValue;
                series[i].parametricEquation = getParametricEquation(
                  series[i].pieData.startRatio,
                  series[i].pieData.endRatio,
                  false,
                  false,
                  k,
                  // 我这里做了一个处理，使除了第一个之外的值都是10
                  series[i].pieData.count === series[0].pieData.count ? 35 : 10
                );

                startValue = endValue;

                legendData.push(series[i].name);
              }

              // 准备待返回的配置项，把准备好的 legendData、series 传入。
              const option = {
                // animation: false,
                tooltip: {
                  formatter: (params) => {
                    if (params.seriesName !== "mouseoutSeries") {
                      return `${
                        params.seriesName
                      }<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                        params.color
                      };"></span>${
                        option.series[params.seriesIndex].pieData.count
                      } `;
                    }
                    return "";
                  },
                  textStyle: {
                    color: "#ffff",
                    fontSize: 24,
                  },
                  borderWidth: 0,
                  backgroundColor: "rgba(51, 51, 51, 0.7)",
                },
                legend: {
                  right: "10%",
                  top: "11%",
                  orient: "vertical",
                  textStyle: {
                    rich: {
                      name: {
                        fontSize: 30,
                        color: "#ffffff",
                        padding: [0, 0, 0, 15],
                      },
                      value: {
                        fontSize: 30,
                        color: "#e9d0ab",
                        padding: [10, 5, 0, 15],
                      },
                      value1: {
                        fontSize: 30,
                        color: "#e9d0ab",
                        padding: [10, 5, 0, 15],
                      },
                    },
                  },
                  formatter: function (name) {
                    var data = option.series; //获取series中的data
                    var total = 0;
                    var tarValue;
                    for (var i = 0, l = data.length; i < l; i++) {
                      total += data[i].pieData.count;
                      if (data[i].pieData.name == name) {
                        tarValue = data[i].pieData.count;
                      }
                    }
                    var p = ((tarValue / total) * 100).toFixed(2);
                    // return '{name|' + name + '}\n{value|' +p+"%  "+ tarValue + '件'+'}'
                    return (
                      "{name|" +
                      name +
                      "}{value1|" +
                      tarValue +
                      "}{value|" +
                      p +
                      "%}"
                    );
                  },
                  // padding: [0, 600, 0, 200],
                },
                xAxis3D: {
                  min: -1,
                  max: 1,
                },
                yAxis3D: {
                  min: -1,
                  max: 1,
                },
                zAxis3D: {
                  min: -1,
                  max: 1,
                },
                grid3D: {
                  show: false,
                  z: 1,
                  boxHeight: 10,
                  top: "-16%",
                  left: "-24%",
                  viewControl: {
                    // 3d效果可以放大、旋转等，请自己去查看官方配置
                    alpha: 25,
                    // beta: 30,
                    rotateSensitivity: 1,
                    zoomSensitivity: 0,
                    panSensitivity: 0,
                    autoRotate: true,
                    distance: 150,
                  },
                  // 后处理特效可以为画面添加高光、景深、环境光遮蔽（SSAO）、调色等效果。可以让整个画面更富有质感。
                  postEffect: {
                    // 配置这项会出现锯齿，请自己去查看官方配置有办法解决
                    enable: false,
                    bloom: {
                      enable: true,
                      bloomIntensity: 0.1,
                    },
                    SSAO: {
                      enable: true,
                      quality: "medium",
                      radius: 2,
                    },
                    // temporalSuperSampling: {
                    //   enable: true,
                    // },
                  },
                },
                series,
              };
              return option;
            }
            //  修正取消高亮失败的 bug
            // 监听 mouseover，近似实现高亮（放大）效果

            // 修正取消高亮失败的 bug

            myChart.setOption(option);
          },
          //执法协同
          getChart03(id, echartData) {
            let myChart = echarts.init(document.getElementById(id));
            let xdata = echartData.map((a) => a.name);
            let ydata = [
              echartData.map((a) => a.value1),
              echartData.map((a) => a.value2),
              echartData.map((a) => a.zb),
            ];
            let legend = ["投诉举报事项", "建议转执法案件数"];
            let color = ["172,171,52", "76,152,251"];
            let option = {
              tooltip: {
                trigger: "axis",
                backgroundColor: "rgba(51, 51, 51, 0.7)",
                borderWidth: 0,
                axisPointer: {
                  type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                },
                textStyle: {
                  color: "white",
                  fontSize: "24",
                },
                formatter: function (params) {
                  let str = params[0].name + "<br/>";
                  for (let item of params) {
                    str += item.seriesName + ": " + item.value + "<br/>";
                  }
                  return str + "占比：" + ydata[2][params[0].dataIndex];
                },
              },
              grid: {
                left: "5%",
                right: "5%",
                top: "15%",
                bottom: "12%",
                containLabel: true,
              },
              legend: {
                top: 10,
                left: "center",
                itemWidth: 16,
                itemHeight: 16,
                itemGap: 50,
                textStyle: {
                  fontSize: 24,
                  color: "#fff",
                  padding: [3, 0, 0, 0],
                },
              },
              dataZoom: [
                {
                  // 设置滚动条的隐藏与显示
                  show: true,
                  // 设置滚动条类型
                  type: "slider",
                  // 设置背景颜色
                  backgroundColor: "rgb(19, 63, 100)",
                  // 设置选中范围的填充颜色
                  fillerColor: "rgb(16, 171, 198)",
                  // 设置边框颜色
                  borderColor: "rgb(19, 63, 100)",
                  // 是否显示detail，即拖拽时候显示详细数值信息
                  showDetail: false,
                  // 数据窗口范围的起始数值
                  startValue: 10,
                  // 数据窗口范围的结束数值（一页显示多少条数据）
                  endValue: 5,
                  // empty：当前数据窗口外的数据，被设置为空。
                  // 即不会影响其他轴的数据范围
                  filterMode: "empty",
                  // 设置滚动条宽度，相对于盒子宽度
                  width: "50%",
                  // 设置滚动条高度
                  height: 18,
                  // 设置滚动条显示位置
                  left: "center",
                  // 是否锁定选择区域（或叫做数据窗口）的大小
                  zoomLoxk: true,
                  // 控制手柄的尺寸
                  handleSize: 0,
                  // dataZoom-slider组件离容器下侧的距离
                  bottom: 3,
                },
                {
                  // 没有下面这块的话，只能拖动滚动条，
                  // 鼠标滚轮在区域内不能控制外部滚动条
                  type: "inside",
                  // 滚轮是否触发缩放
                  zoomOnMouseWheel: false,
                  // 鼠标滚轮触发滚动
                  moveOnMouseMove: true,
                  moveOnMouseWheel: true,
                },
              ],
              xAxis: [
                {
                  type: "category",
                  data: xdata,
                  axisLine: {
                    lineStyle: {
                      color: "rgb(119,179,241,.4)", // 颜色
                      width: 1, // 粗细
                    },
                  },
                  axisTick: {
                    show: false,
                  },
                  axisLabel: {
                    interval: 0,
                    // rotate: 30,
                    textStyle: {
                      color: "#D6E7F9",
                      fontSize: 20,
                    },
                  },
                },
                {
                  type: "category",
                  axisLine: {
                    show: false,
                  },
                  axisTick: {
                    show: false,
                  },
                  axisLabel: {
                    show: false,
                  },
                  splitArea: {
                    show: false,
                  },
                  splitLine: {
                    show: false,
                  },
                  data: xdata,
                },
              ],
              yAxis: [
                {
                  name: "",
                  type: "value",
                  nameTextStyle: {
                    fontSize: 24,
                    color: "#D6E7F9",
                    padding: 5,
                  },
                  splitLine: {
                    lineStyle: {
                      color: "rgb(119,179,241,.4)",
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 24,
                      color: "#D6E7F9",
                    },
                  },
                },
              ],
              // dataZoom: [
              //   {
              //     show: true,
              //     height: 15,
              //     // xAxisIndex: [0],
              //     bottom: 30,
              //     start: 0,
              //     end: 80,
              //     textStyle: false,
              //   },
              // ],
              series: [
                {
                  name: legend[0],
                  type: "bar",
                  xAxisIndex: 1,
                  // stack: "总量",
                  barWidth: "20%",
                  itemStyle: {
                    normal: {
                      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: "rgba(1, 114, 162,0.8)",
                        },
                        {
                          offset: 1,
                          color: "rgba(76,152,251,0.2)",
                        },
                      ]),
                      barBorderRadius: 4,
                    },
                  },
                  data: ydata[0],
                },
                {
                  name: legend[1],
                  type: "bar",
                  // stack: "总量",
                  barWidth: "20%",
                  itemStyle: {
                    normal: {
                      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: "rgba(172,171,52,0.99)",
                        },
                        {
                          offset: 1,
                          color: "rgba(172,171,52,0)",
                        },
                      ]),
                      barBorderRadius: 4,
                    },
                  },
                  data: ydata[1],
                },
              ],
            };
            myChart.setOption(option);
            myChart.getZr().on("mousemove", (param) => {
              myChart.getZr().setCursorStyle("default");
            });
          },
        },
      });
    </script>
  </body>
</html>
