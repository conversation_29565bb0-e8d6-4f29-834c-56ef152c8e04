<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="initial-scale=1,maximum-scale=1,user-scalable=no"
    />
    <title>省域yt类型添加</title>

    <link
      rel="stylesheet"
      href="https://csdnwlgz.dsjj.jinhua.gov.cn/jsapi/4.25/esri/themes/light/main.css"
    />
    <script src="./index.js" type="module"></script>

    <style>
      html,
      body,
      #viewDiv {
        padding: 0;
        margin: 0;
        height: 100%;
        width: 100%;
      }

      .tools {
        position: absolute;
        top: 20px;
        left: 50%;
        width: 50%;
        height: 200px;
        display: flex;
      }

      .tools span {
        cursor: pointer;
        background-color: blue;
        width: 150px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        color: white;
      }
    </style>
  </head>

  <body>
    <div id="viewDiv">
      <div class="tools">
        <span onclick="add()">添加</span>
      </div>
    </div>
  </body>
  <script>
    function add() {
      ArcGisUtils.loadArcgisLayer(view, {
        title: "城镇开发边界（过渡期）-面",
        type: "map-image",
        url: "https://yt.zjasm.net/apigw2/services/arcgisdynamicmapservicelayer/63c506255a48498d95ed160a756ac3d4/latest/sy-6786f525-a9bd-477f-ab4a-f26d29524077",
      });
    }
  </script>
</html>
