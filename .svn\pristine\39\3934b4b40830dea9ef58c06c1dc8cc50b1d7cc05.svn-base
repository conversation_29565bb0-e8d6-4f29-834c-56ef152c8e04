<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  <title>人员信息弹窗</title>
  <link rel="shortcut icon" href="#" />
  <script src="/Vue/vue.js"></script>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <script src="/jquery/jquery-3.6.1.min.js"></script>
  <link rel="stylesheet" href="/elementui/css/index.css" />
  <script src="/elementui/js/index.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/static/js/jslib/s.min.vue.js"></script>

  <style type="text/css" scoped>
    [v-cloak] {
      display: none;
    }

    body {
      margin: 0;
      padding: 0;
    }

    #ryxx {
      width: 1200px;
      height: 680px;
      position: relative;
      background-image: url("/static/images/zhdd/bg_panel.png");
      background-size: 100% 100%;
    }

    .title {
      font-size: 36px;
      padding: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .close {
      width: 46px;
      height: 78px;
      font-size: 60px;
      color: #fff;
      font-weight: 600;
      cursor: pointer;
      position: absolute;
      right: 40px;
    }

    .content {
      width: 100%;
      padding: 0px 60px;
      box-sizing: border-box;
    }

    /* 表格 */
    .table {
        width: 100%;
        /* height: 500px; */
        padding: 10px 30px;
        box-sizing: border-box;
      }

      .table .th {
        width: 100%;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        font-weight: 700;
        font-size: 28px;
        line-height: 60px;
        color: #ffffff;
      }

      .table .th_td {
        letter-spacing: 0px;
        text-align: center;
      }

      .table .tbody {
        width: 100%;
        height: calc(100% - 60px);
        /* overflow-y: auto; */
        overflow: hidden;
      }

      .table .tbody:hover {
        overflow-y: auto;
      }

      .table .tbody::-webkit-scrollbar {
        width: 4px;
        /*滚动条整体样式*/
        height: 4px;
        /*高宽分别对应横竖滚动条的尺寸*/
      }

      .table .tbody::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: #20aeff;
        height: 8px;
      }

      .table .tr {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        height: 70px;
        line-height: 70px;
        font-size: 28px;
        color: #ffffff;
        cursor: pointer;
        border-top: 1px solid #959aa1;
        border-image: linear-gradient(to right, #e9f5ff3b, #f5ffffd4, #e9f5ff3b) 1;
        padding: 0 15px;
        box-sizing: border-box;
      }

      .table .tr:nth-child(2n) {
        background: rgba(50, 134, 248, 0.2);
      }

      .table .tr:nth-child(2n + 1) {
        background: rgba(50, 134, 248, 0.12);
      }

      .table .tr:hover {
        background-color: #0074da75;
      }

      .table .tr_td {
        letter-spacing: 0px;
        text-align: center;
        box-sizing: border-box;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      /* 分页 */
      .el-pagination{
        text-align: center;
        font-weight: normal;
        margin-top: 20px;
      }
      .el-pager li{
        background: none;
        color: #fff;
        font-size: 30px;
        padding: 0 20px;
      }
      .el-pagination button:disabled{
        background: none;
      }
      .el-pagination .btn-next, .el-pagination .btn-prev{
        color: #b5b8bf;
        background: none;
      }
      .el-pagination .btn-next .el-icon, .el-pagination .btn-prev .el-icon{
        font-size: 30px;
      }
      .el-pager li.btn-quicknext, .el-pager li.btn-quickprev{
        color: #b5b8bf;
      }
  </style>
</head>

<body>
  <div id="ryxx" v-cloak>
    <div class="title">
      <span class="s-c-yellow-gradient s-w7 s-font-40">人员信息</span>
      <div class="close" @click="close()">×</div>
    </div>
    <div class="content">
      <div class="table" style="height: 431px">
        <div class="th">
          <div class="th_td" style="flex: 0.3">执法队名称</div>
          <div class="th_td" style="flex: 0.2">执法队队员</div>
          <div class="th_td" style="flex: 0.25">联系电话</div>
          <div class="th_td" style="flex: 0.25">职务</div>
        </div>
        <div class="tbody">
          <div class="tr" v-for="(item,index) in tableData" :key="index">
            <div class="tr_td" style="flex: 0.3" :title="item.station">{{item.station}}</div>
            <div class="tr_td" style="flex: 0.2">{{item.name}}</div>
            <div class="tr_td" style="flex: 0.25">{{item.phone}}</div>
            <div class="tr_td" style="flex: 0.25" :title="item.duties">{{item.duties}}</div>
          </div>
        </div>
      </div>
      <el-pagination
        :page-size="pageSize"
        :pager-count="5"
        :current-page="currentPage"
        layout="prev, pager, next"
        :total="total"
        @current-change="changePage">
      </el-pagination>
    </div>
    </div>
  </div>
</body>
<script>
  var vm = new Vue({
    el: " #ryxx",
    data() {
      return {
        pageSize:5,
        total:0,
        currentPage:1,
        station: null,
        tableData:[
          {
            station: "婺城区长山乡综合行政执法队",
            name: "陈尝尝",
            phone: "13660234234",
            duties: "长山乡综合行政执法队队长",
          },
          {
            station: "婺城区长山乡综合行政执法队",
            name: "陈尝尝",
            phone: "13660234234",
            duties: "长山乡综合行政执法队队长",
          },
          {
            station: "婺城区长山乡综合行政执法队",
            name: "陈尝尝",
            phone: "13660234234",
            duties: "长山乡综合行政执法队队长",
          },
          {
            station: "婺城区长山乡综合行政执法队",
            name: "陈尝尝",
            phone: "13660234234",
            duties: "长山乡综合行政执法队队长",
          },
          {
            station: "婺城区长山乡综合行政执法队",
            name: "陈尝尝",
            phone: "13660234234",
            duties: "长山乡综合行政执法队队长",
          }
        ],
      };
    },
    mounted() {
      let this_ = this;
      window.addEventListener("message", (e) => {
        if (e.data && e.data.station) {
          this_.station=e.data.station
          this_.init()
        }
      });
    },
    methods: {
      init(){
        let params = {
          station:this.station,
          pagenum:this.currentPage,
          limit:this.pageSize,
        }
        $api('/csdn_yjyp15',params).then((res)=>{
          this.tableData = res[0].result
          this.currentPage = res[0].pagenum
          this.total = res[0].total
        })

      },
      changePage(val){
        console.log(val);
        this.currentPage = val
        this.init()
      },
      close(){
        window.parent.lay.closeIframeByNames(["ryxxDetail"]);
      },
    },
  });
</script>

</html>
