<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>导航页</title>
  <style>
    html,
    body {
      padding: 0;
      margin: 0;
      height: 100%;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .guide {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-column-gap: 20px;
      grid-row-gap: 10px;
    }

    span {
      /* width: 200px; */
      padding: 10px 20px;

      /* color: white; */
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
      cursor: pointer;
      border: 1px solid #ccc;
    }

    span:hover {
      background-color: aqua;
      color: white;
    }
  </style>
</head>

<body>
  <div class="guide">
    <span onclick="window.location.href='./1.tools.html'">1. 工具类：放大、缩小</span>
    <span onclick="window.location.href='./2.Draw.html'">2.绘图工具</span>
    <span onclick="window.location.href='./3.weather.html'">3.气象</span>
    <span onclick="window.location.href='./4.effect.html'">4.特效：三维热力图</span>
    <span onclick="window.location.href='./4.effect.html'">5.添加热网</span>
    <span onclick="window.location.href='./6.opacity.html'">6.设置图层透明度</span>
    <span onclick="window.location.href='./7.waterlayer.html'">7.添加水波纹效果图层</span>
    <span onclick="window.location.href='./8.flood.html'">8.淹没分析</span>

    <span onclick="window.location.href='./4.effect.html'">9.特效：主干道路流光线效果</span>
    <!-- <span onclick="window.location.href='./opacity.html'"
        >设置图层透明度</span
      > -->
      <span onclick="window.location.href='./10.integratedMesh.html'"
        >10.添加倾斜摄影</span
      >
      <span onclick="window.location.href='./11.weatherEffect.html'"
        >11.天气特效</span
      >
      <span onclick="window.location.href='./12.AreaMeasurement.html'"
        >12.测量工具</span
      >
      <span onclick="window.location.href='./13.whitePureModal.html'"
        >13.添加白膜、精模</span
      >
      <span onclick="window.location.href='./14.createSiceWidget.html'"
        >14.剖切工具</span
      >
      <span onclick="window.location.href='./15.MapPopupWidget.html'"
        >15.自定义Popu使用</span
      >
      <span onclick="window.location.href='./16.mapTransform.html'">16.底图切换</span>
      <span onclick="window.location.href='./17.geojson.html'">17.添加geojson</span>
      <span onclick="window.location.href='./18.initializationView.html'"
        >18.定位到点</span
      >
      <span onclick="window.location.href='./19.bimModel.html'">19.添加BIM模型</span>
      <span onclick="window.location.href='./20.coordinate.html'">20.坐标拾取</span>
      <span onclick="window.location.href='./21.3DText.html'">21.3D文本</span>
      <span onclick="window.location.href='./22.gif.html'">22.gif上图</span>
      <span onclick="window.location.href='./23.createLineOfSightWidget.html'"
        >23.通视分析</span
      >
      <span onclick="window.location.href='./24.groundOpacity.html'"
        >24.地表透明度</span
      >
      <span onclick="window.location.href='./25.pointAlarmEffect.html'"
        >25.点报警效果</span
      >
      <span onclick="window.location.href='./26.pointEffect.html'">26.粒子特效(火焰、烟雾、喷泉)</span>
    </div>
  </body>
</html>

