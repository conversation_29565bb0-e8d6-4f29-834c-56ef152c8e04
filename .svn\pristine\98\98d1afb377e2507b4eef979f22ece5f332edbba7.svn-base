var xzzfzx = new Vue({
  el: "#app",
  data: {
    page_menu: "home",
    active_cityName: "",
    header_title: "金华市行政执法指挥中心",
    cityList: [
      { name: "金华市", code: 330700 },
      { name: "婺城区", code: 330702 },
      { name: "金东区", code: 330703 },
      { name: "兰溪市", code: 330781 },
      { name: "东阳市", code: 330783 },
      { name: "义乌市", code: 330782 },
      { name: "永康市", code: 330784 },
      { name: "浦江县", code: 330726 },
      { name: "武义县", code: 330723 },
      { name: "磐安县", code: 330727 },
      { name: "金华开发区", code: 330751 },
    ],
    city: localStorage.getItem('city'),
    currentYear: "2025",
    yearList: [
      {
        name: "2025年度",
        value: "2025"
      },
      {
        name: "2024年度",
        value: "2024"
      },
      {
        name: "2023年度",
        value: "2023"
      }
    ],
    currentPage2:""
  },
  computed: {
    adminCity() {
      return localStorage.getItem('adminCity')
    }
  },

  mounted() {
    this.initScreen()
    this.currentPage2 = localStorage.getItem("currentPage2");
    localStorage.setItem("year",this.currentYear)
    const that = this;
    //token过期回到登录页
    window.addEventListener("message", function (event) {
      if (JSON.parse(event.data).type == "toLogin") {
        that.$confirm('当前页面Token已过期, 是否回到登录页?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          location.href = "https://" + location.href.split("/")[2]
        }).catch(() => {
          return false
        });
      }
      if (JSON.parse(event.data).type == "uav") {
        that.StartUAVClick(JSON.parse(event.data).data)
      }
    });
    let this_ = this;
    this.getWeather();
    this.getCityList();
    setInterval(function () {
      this_.getNewTime();
    }, 1000);

    // this_.openDefaultPage();
    this_.openCurrentPage();
    this_.cityFun(this.adminCity);
  },
  methods: {
    //如果屏幕宽度小于3840 则设置浏览器缩放为百分之50 其它结果统一设置缩放100%
    initScreen() {
      const app = document.getElementById('app');
      const screenWidth = window.screen.width;

      // if (screenWidth < 3840) {
      //   app.style.transform = 'scale(0.5)';
      // } else {
      //   app.style.transform = 'scale(1)';
      // }
    },
    getCityList() {
      const cityList = [
        { name: "金华市", code: 330700 },
        { name: "婺城区", code: 330702 },
        { name: "金东区", code: 330703 },
        { name: "兰溪市", code: 330781 },
        { name: "东阳市", code: 330783 },
        { name: "义乌市", code: 330782 },
        { name: "永康市", code: 330784 },
        { name: "浦江县", code: 330726 },
        { name: "武义县", code: 330723 },
        { name: "磐安县", code: 330727 },
        { name: "金华开发区", code: 330751 },
      ]
      this.adminCity == '金华市'?this.cityList = cityList:this.adminCity == '开发区'?this.cityList = [{ name: "金华开发区", code: 330751 }]:this.cityList = [cityList.find(item => item.name == this.adminCity)]
    },
    /**加载模块 */
    openDefaultPage() {
      lay.openIframe({
        type: "openIframe",
        name: "bottom",
        id: "bottom",
        src: baseURL.url + "/static/citybrain/home/<USER>/home_bottom.html",
        width: "1760px",
        height: "525px",
        left: "calc(50% - 860px)",
        top: '73.3%',
        zIndex: "666",
      });
      lay.openIframe({
        type: "openIframe",
        name: "left",
        id: "left",
        src: baseURL.url + "/static/citybrain/home/<USER>/home_left.html",
        width: "1030px",
        height: "1900px",
        left: "20px",
        top: "calc(50% - 870px)",
        zIndex: "666",
      });
      lay.openIframe({
        type: "openIframe",
        name: "right",
        id: "right",
        src: baseURL.url + "/static/citybrain/home/<USER>/home_right.html",
        width: "1030px",
        height: "1900px",
        right: "20px",
        top: "calc(50% - 870px)",
        zIndex: "666",
      });
      lay.openIframe({
        type: "openIframe",
        name: "top",
        id: "top",
        src: baseURL.url + "/static/citybrain/home/<USER>/home_top.html",
        width: "828px",
        height: "200px",
        left: "calc(50% - 414px)",
        top: "200px",
        zIndex: "666",
      });
    },
    setHomeTitle() {
      this.header_title = this.adminCity + "行政执法指挥中心"
    },
    openCurrentPage() {
      let currentPage = JSON.parse(localStorage.getItem('currentPage'))
      console.log(currentPage,"page");
      this.page_menu = currentPage.key
      //设置标题
      this.header_title = currentPage.name == "驾驶舱" ? this.adminCity + "行政执法指挥中心" : currentPage.name
      document.title = "金华市行政执法指挥中心";
      currentPage.components.forEach((item,i) => {
        lay.openIframe({
          type: "openIframe",
          name: item.name,
          id: item.name,
          src: baseURL.url + item.url,
          width: item.width,
          height: item.height,
          left: item.left,
          right: item.right,
          top: item.top,
          zIndex: "666",
        });
      })
    },
    getNewTime() {
      let date = new Date();
      $("#nowTime").html(`
      ${date.getHours().toString().padStart(2, "0")}:${date
        .getMinutes()
        .toString()
        .padStart(2, "0")}`);
    },
    getWeather() {
      $api("/cstz_tqjk").then((weaker) => {
        let date = new Date();
        for (var i = 0; i < weaker.length; i++) {
          if (
            date.getDate().toString().padStart(2, "0") ===
            weaker[i].days.padStart(2, "0")
          ) {
            let imgUrl = weaker[i].weather_path.slice(2);
            let wd =
              weaker[i].low.split(" ")[1] + "~" + weaker[i].high.split(" ")[1];
            $("#header_weather").html(`
              <img style="width:45px;margin-right:20px;" src="${imgUrl}"/> <span>${wd}</span>`);
            $("#header_week").html(`
              <span class="nowDate c-blue"><i class="iconfont icon-rili"></i>${
                weaker[i].ymd
              }</span>&nbsp;&nbsp;
              <span>${weaker[i].WEEK}</span>&nbsp;
              <span id="nowTime" class="c-blue">${date
                .getHours()
                .toString()
                .padStart(2, "0")}:
              ${date.getMinutes().toString().padStart(2, "0")}</span>`);
          }
        }
      });
    },
    /**头部县市区选择 */
    cityFun(item) {
      localStorage.setItem('city',item)
      eventbus.emit("cityChange", item);
      this.active_cityName = item;
      this.city = item
    },
    //姓名脱敏方法
    handleName(name) {
      let arr = Array.from(name)
      let result = ''
      if (arr.length === 2) {
        result = arr[0] + '*'
      } else if (arr.length > 2) {
        for (let i = 1; i < arr.length - 1; i++) {
          arr[i] = '*'
        }
        result = arr.join("")
      }else {
        return name
      }
      return result
    },
    //电话脱敏方法
    handlePhone (phone) {
      return phone.replace(/^(.{3})(?:\d+)(.{4})$/, "$1****$2")
    },
    formatIdentity(number) {
      number += '';
      return number.replace(/(\d{3})\d*(\d{4})/g, '$1***********$2')
    },

    StartUAVClick(obj) {
      this.$confirm('是否调度无人机前往现场?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        lrFrameSHClick('xzzf')
        this.openUAVvideoPage(obj);
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    },
    openUAVvideoPage(obj) {
      window.parent.lay.openIframe({
        type: "openIframe",
        name: "uavVideo",
        id: "uavVideo",
        src: baseURL.url + "/static/citybrain/commonts/zhdd/UAV.html",
        left: "1168px",
        top: "360px",
        width: "1509px",
        height: "1009px",
        zIndex: "130",
        argument: obj,
      });
    },
  },
  watch:{
    header_title:{
      handler(newVal,oldVal){
         localStorage.setItem("currentPage2",newVal)
         this.currentPage2 = newVal
      },
      immediate:true
    },
    currentYear(value) {
      localStorage.setItem("year",value)
      eventbus.emit("yearChange", value);
    }
  }
});
